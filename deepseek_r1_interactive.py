#!/usr/bin/env python3
"""
🎯 INTERFACE INTERACTIVE DEEPSEEK R1 8B + MÉMOIRE THERMIQUE
Interface en temps réel pour interaction avec DeepSeek R1 8B authentique
Créé par Jean-<PERSON> PASSAVE
"""

import sys
import os
from deepseek_r1_authentic_thermal import DeepSeekR1ThermalMemory
import time

class DeepSeekR1Interactive:
    """Interface interactive pour DeepSeek R1 8B avec mémoire thermique"""
    
    def __init__(self):
        self.deepseek_thermal = DeepSeekR1ThermalMemory()
        self.session_active = True
        self.conversation_count = 0
        
    def display_header(self):
        """Affiche l'en-tête du système"""
        print("🚀 INITIALISATION DEEPSEEK R1 8B + MÉMOIRE THERMIQUE")
        print("=" * 60)
        
        # Statut du système
        status = self.deepseek_thermal.get_thermal_status()
        
        print(f"✅ Système initialisé")
        print(f"🧠 Mémoire thermique: Active")
        print(f"🤖 DeepSeek R1 8B: {status['connection_status'].title()}")
        print()
        
        print("📊 STATUT SYSTÈME:")
        print(f"  🧠 Entrées mémoire: {status['memory_entries']}")
        print(f"  💬 Conversations: {status['conversations_stored']}")
        print(f"  💉 Injections: {status['thermal_injections_performed']}")
        print(f"  🤖 Modèle: {status['deepseek_model']}")
        print(f"  🔥 Mémoire active: {status['thermal_memory_active']}")
        print()
        
        print("🎯 INTERFACE INTERACTIVE DEEPSEEK R1 + MÉMOIRE THERMIQUE")
        print("=" * 60)
        print("Tapez /help pour voir les commandes disponibles")
        print()
    
    def display_help(self):
        """Affiche l'aide des commandes"""
        print("📋 COMMANDES DISPONIBLES:")
        print("  /help          - Afficher cette aide")
        print("  /status        - Statut du système")
        print("  /memory        - Afficher la mémoire thermique")
        print("  /inject <key> <value> - Injecter en mémoire thermique")
        print("  /reconnect     - Reconnecter à DeepSeek R1 8B")
        print("  /clear         - Effacer l'écran")
        print("  /quit          - Quitter l'interface")
        print()
    
    def display_status(self):
        """Affiche le statut détaillé"""
        status = self.deepseek_thermal.get_thermal_status()
        
        print("📊 STATUT DÉTAILLÉ DU SYSTÈME:")
        print(f"  🧠 Mémoire thermique: {'✅ Active' if status['thermal_memory_active'] else '❌ Inactive'}")
        print(f"  🤖 Modèle DeepSeek: {status['deepseek_model']}")
        print(f"  🔗 Connexion: {status['connection_status']}")
        print(f"  💾 Entrées mémoire: {status['memory_entries']}")
        print(f"  💬 Conversations: {status['conversations_stored']}")
        print(f"  💉 Injections réussies: {status['successful_injections']}")
        print(f"  ⏱️ Dernier temps réponse: {status['last_response_time_ms']:.2f}ms")
        print(f"  📁 Base de données: {status['database_path']}")
        print()
    
    def display_memory(self):
        """Affiche les entrées de mémoire thermique"""
        import sqlite3
        
        conn = sqlite3.connect(self.deepseek_thermal.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT key, type, importance, access_count 
            FROM thermal_memory 
            ORDER BY importance DESC, access_count DESC 
            LIMIT 10
        ''')
        
        entries = cursor.fetchall()
        conn.close()
        
        print("🧠 MÉMOIRE THERMIQUE (Top 10):")
        for key, mem_type, importance, access_count in entries:
            print(f"  • {key} (Type: {mem_type}, Importance: {importance}, Accès: {access_count})")
        print()
    
    def inject_memory(self, key, value):
        """Injecte une nouvelle entrée en mémoire thermique"""
        try:
            self.deepseek_thermal.store_thermal_memory(key, value, "user_injection", 7)
            print(f"✅ Injecté en mémoire thermique: {key} = {value}")
        except Exception as e:
            print(f"❌ Erreur injection: {e}")
        print()
    
    def reconnect_deepseek(self):
        """Tente de reconnecter à DeepSeek R1 8B"""
        print("🔄 Reconnexion à DeepSeek R1 8B...")
        success = self.deepseek_thermal.check_deepseek_connection()
        if success:
            print("✅ Reconnexion réussie !")
        else:
            print("⚠️ Reconnexion échouée, mode simulation actif")
        print()
    
    def process_command(self, user_input):
        """Traite les commandes spéciales"""
        if user_input.startswith('/'):
            command_parts = user_input[1:].split(' ', 2)
            command = command_parts[0].lower()
            
            if command == 'help':
                self.display_help()
                return True
            elif command == 'status':
                self.display_status()
                return True
            elif command == 'memory':
                self.display_memory()
                return True
            elif command == 'inject' and len(command_parts) >= 3:
                key = command_parts[1]
                value = ' '.join(command_parts[2:])
                self.inject_memory(key, value)
                return True
            elif command == 'reconnect':
                self.reconnect_deepseek()
                return True
            elif command == 'clear':
                os.system('clear' if os.name == 'posix' else 'cls')
                self.display_header()
                return True
            elif command == 'quit':
                print("👋 Au revoir Jean-Luc !")
                self.session_active = False
                return True
            else:
                print("❌ Commande inconnue. Tapez /help pour l'aide.")
                return True
        
        return False
    
    def format_response(self, result):
        """Formate la réponse de DeepSeek R1"""
        response = result['response']
        
        # Indicateurs visuels selon le statut
        if result['connection_status'] == 'authentic':
            status_icon = "🤖"
            status_text = "DeepSeek R1 8B"
        elif result['connection_status'] == 'simulation':
            status_icon = "🔄"
            status_text = "Mode Simulation"
        else:
            status_icon = "⚠️"
            status_text = "Erreur"
        
        print(f"{status_icon} {status_text}: {response}")
        print()
        print(f"⏱️ Temps: {result['response_time_ms']:.2f}ms")
        print(f"🧠 Injections: {result['thermal_injections']}")
        print(f"🔥 Mémoire utilisée: {result['thermal_context_used']}")
        print()
    
    def run(self):
        """Lance l'interface interactive"""
        self.display_header()
        
        try:
            while self.session_active:
                # Prompt utilisateur
                user_input = input("💬 > ").strip()
                
                if not user_input:
                    continue
                
                # Traiter les commandes
                if self.process_command(user_input):
                    continue
                
                # Afficher l'indicateur de traitement
                print(f"🤔 Vous: {user_input}")
                print("🤖 DeepSeek R1 réfléchit...")
                
                # Interroger DeepSeek R1 avec mémoire thermique
                start_time = time.time()
                result = self.deepseek_thermal.query_deepseek_r1_authentic(user_input)
                
                # Afficher la réponse formatée
                self.format_response(result)
                
                self.conversation_count += 1
                
        except KeyboardInterrupt:
            print("\n\n👋 Session interrompue par l'utilisateur")
        except Exception as e:
            print(f"\n❌ Erreur système: {e}")
        finally:
            print(f"\n📊 Session terminée - {self.conversation_count} conversations")

def main():
    """Point d'entrée principal"""
    try:
        interface = DeepSeekR1Interactive()
        interface.run()
    except Exception as e:
        print(f"❌ Erreur fatale: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
