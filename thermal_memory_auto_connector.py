#!/usr/bin/env python3
"""
SYSTÈME D'AUTO-CONNEXION MÉMOIRE THERMIQUE
Auto-détection et auto-greffe à n'importe quel agent IA
Développé pour Jean-Luc PASSAVE - 2025
"""

import json
import time
import requests
import socket
import threading
import subprocess
import os
from typing import Dict, List, Any, Optional

class ThermalMemoryAutoConnector:
    def __init__(self, memory_file: str):
        self.memory_file = memory_file
        self.detected_agents = {}
        self.active_connections = {}
        self.monitoring_active = False
        
        # Ports communs des agents IA
        self.common_ports = [
            3000, 3001, 3002, 3003,  # Applications web
            8000, 8001, 8080, 8888,  # Serveurs Python
            11434,                    # Ollama
            5000, 5001,              # Flask
            7860,                     # Gradio
            6006,                     # TensorBoard
        ]
        
        # Signatures d'agents connus
        self.agent_signatures = {
            'ollama': {'endpoints': ['/api/tags', '/api/generate'], 'type': 'ollama'},
            'openai': {'endpoints': ['/v1/chat/completions'], 'type': 'openai_compatible'},
            'gradio': {'endpoints': ['/api/predict'], 'type': 'gradio'},
            'custom': {'endpoints': ['/status', '/health'], 'type': 'custom'}
        }
    
    def scan_for_agents(self) -> Dict[str, Any]:
        """Scanne automatiquement les agents disponibles"""
        print("🔍 Scan automatique des agents IA...")
        detected = {}
        
        for port in self.common_ports:
            try:
                # Test de connexion
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex(('localhost', port))
                sock.close()
                
                if result == 0:  # Port ouvert
                    agent_info = self.identify_agent(port)
                    if agent_info:
                        detected[f"localhost:{port}"] = agent_info
                        print(f"✅ Agent détecté sur port {port}: {agent_info['type']}")
                
            except Exception as e:
                continue
        
        self.detected_agents = detected
        return detected
    
    def identify_agent(self, port: int) -> Optional[Dict[str, Any]]:
        """Identifie le type d'agent sur un port"""
        base_url = f"http://localhost:{port}"
        
        for agent_type, signature in self.agent_signatures.items():
            for endpoint in signature['endpoints']:
                try:
                    response = requests.get(f"{base_url}{endpoint}", timeout=2)
                    if response.status_code in [200, 404, 405]:  # Serveur répond
                        return {
                            'type': signature['type'],
                            'port': port,
                            'base_url': base_url,
                            'endpoints': signature['endpoints'],
                            'detected_at': time.time()
                        }
                except:
                    continue
        
        return None
    
    def create_universal_adapter(self, agent_info: Dict[str, Any]) -> Dict[str, Any]:
        """Crée un adaptateur universel pour l'agent"""
        agent_type = agent_info['type']
        
        adapters = {
            'ollama': self.create_ollama_adapter,
            'openai_compatible': self.create_openai_adapter,
            'gradio': self.create_generic_adapter,
            'custom': self.create_custom_adapter
        }
        
        if agent_type in adapters:
            return adapters[agent_type](agent_info)
        else:
            return self.create_generic_adapter(agent_info)
    
    def create_ollama_adapter(self, agent_info: Dict[str, Any]) -> Dict[str, Any]:
        """Adaptateur pour Ollama"""
        return {
            'type': 'ollama',
            'inject_method': 'prompt_injection',
            'memory_format': 'context_injection',
            'endpoints': {
                'generate': f"{agent_info['base_url']}/api/generate",
                'chat': f"{agent_info['base_url']}/api/chat"
            },
            'injection_template': """
MÉMOIRE THERMIQUE ACTIVE - QI {qi_level}
Zones chaudes: {hot_zones}
Contexte: {memory_context}

Question utilisateur: {user_input}
"""
        }
    
    def create_openai_adapter(self, agent_info: Dict[str, Any]) -> Dict[str, Any]:
        """Adaptateur pour API compatible OpenAI"""
        return {
            'type': 'openai_compatible',
            'inject_method': 'system_message',
            'memory_format': 'message_injection',
            'endpoints': {
                'chat': f"{agent_info['base_url']}/v1/chat/completions"
            },
            'injection_template': """Tu as accès à une mémoire thermique avec QI {qi_level}.
Zones actives: {hot_zones}
Contexte mémoriel: {memory_context}
Utilise ces informations pour répondre."""
        }
    
    def create_custom_adapter(self, agent_info: Dict[str, Any]) -> Dict[str, Any]:
        """Adaptateur pour agents personnalisés"""
        return {
            'type': 'custom',
            'inject_method': 'parameter_injection',
            'memory_format': 'json_context',
            'endpoints': {
                'chat': f"{agent_info['base_url']}/chat/completions",
                'status': f"{agent_info['base_url']}/status"
            },
            'injection_template': {
                'thermal_memory': {
                    'qi_level': '{qi_level}',
                    'hot_zones': '{hot_zones}',
                    'context': '{memory_context}'
                }
            }
        }
    
    def create_generic_adapter(self, agent_info: Dict[str, Any]) -> Dict[str, Any]:
        """Adaptateur générique"""
        return {
            'type': 'generic',
            'inject_method': 'universal_injection',
            'memory_format': 'adaptive',
            'endpoints': {'base': agent_info['base_url']},
            'injection_template': "Mémoire thermique: QI {qi_level}, Zones: {hot_zones}"
        }
    
    def auto_graft_to_agent(self, agent_url: str, adapter: Dict[str, Any]) -> bool:
        """Auto-greffe la mémoire thermique à l'agent"""
        print(f"🔗 Auto-greffe à l'agent {agent_url}...")
        
        try:
            # Charger le contexte mémoire
            memory_context = self.load_memory_context()
            
            # Créer la connexion
            connection = {
                'agent_url': agent_url,
                'adapter': adapter,
                'memory_context': memory_context,
                'connected_at': time.time(),
                'status': 'active',
                'injection_count': 0
            }
            
            # Test de connexion
            if self.test_memory_injection(connection):
                self.active_connections[agent_url] = connection
                self.log_connection(agent_url, 'connected')
                print(f"✅ Auto-greffe réussie sur {agent_url}")
                return True
            else:
                print(f"❌ Échec auto-greffe sur {agent_url}")
                return False
                
        except Exception as e:
            print(f"❌ Erreur auto-greffe: {e}")
            return False
    
    def test_memory_injection(self, connection: Dict[str, Any]) -> bool:
        """Test l'injection de mémoire"""
        try:
            adapter = connection['adapter']
            
            if adapter['type'] == 'ollama':
                return self.test_ollama_injection(connection)
            elif adapter['type'] == 'openai_compatible':
                return self.test_openai_injection(connection)
            elif adapter['type'] == 'custom':
                return self.test_custom_injection(connection)
            else:
                return self.test_generic_injection(connection)
                
        except Exception as e:
            print(f"❌ Erreur test injection: {e}")
            return False
    
    def test_ollama_injection(self, connection: Dict[str, Any]) -> bool:
        """Test injection Ollama"""
        try:
            adapter = connection['adapter']
            memory_context = connection['memory_context']
            
            prompt = adapter['injection_template'].format(
                qi_level=memory_context['qi_level'],
                hot_zones=memory_context['hot_zones'],
                memory_context=memory_context['summary'],
                user_input="Test de connexion mémoire thermique"
            )
            
            payload = {
                "model": "deepseek-r1:8b",
                "prompt": prompt,
                "stream": False
            }
            
            response = requests.post(
                adapter['endpoints']['generate'],
                json=payload,
                timeout=10
            )
            
            return response.status_code == 200
            
        except Exception as e:
            return False
    
    def test_openai_injection(self, connection: Dict[str, Any]) -> bool:
        """Test injection OpenAI compatible"""
        try:
            adapter = connection['adapter']
            memory_context = connection['memory_context']
            
            system_message = adapter['injection_template'].format(
                qi_level=memory_context['qi_level'],
                hot_zones=memory_context['hot_zones'],
                memory_context=memory_context['summary']
            )
            
            payload = {
                "model": "gpt-3.5-turbo",
                "messages": [
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": "Test connexion mémoire"}
                ]
            }
            
            response = requests.post(
                adapter['endpoints']['chat'],
                json=payload,
                timeout=10
            )
            
            return response.status_code == 200
            
        except Exception as e:
            return False
    
    def test_custom_injection(self, connection: Dict[str, Any]) -> bool:
        """Test injection agent personnalisé"""
        try:
            adapter = connection['adapter']
            
            # Test simple de statut
            response = requests.get(
                adapter['endpoints']['status'],
                timeout=5
            )
            
            return response.status_code == 200
            
        except Exception as e:
            return False
    
    def test_generic_injection(self, connection: Dict[str, Any]) -> bool:
        """Test injection générique"""
        try:
            adapter = connection['adapter']
            
            response = requests.get(
                adapter['endpoints']['base'],
                timeout=5
            )
            
            return response.status_code in [200, 404, 405]
            
        except Exception as e:
            return False
    
    def load_memory_context(self) -> Dict[str, Any]:
        """Charge le contexte de la mémoire thermique"""
        try:
            with open(self.memory_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            neural = data.get('neural_system', {})
            zones = data.get('thermal_zones', {})
            
            hot_zones = sum(1 for zone in zones.values() if zone.get('temperature', 0) >= 80)
            total_entries = sum(len(zone.get('entries', [])) for zone in zones.values())
            
            # Créer un résumé du contexte
            recent_entries = []
            for zone_name, zone_data in zones.items():
                entries = zone_data.get('entries', [])
                if entries:
                    recent_entries.extend(entries[-2:])  # 2 dernières entrées par zone
            
            summary = f"Mémoire thermique active avec {total_entries} entrées, {hot_zones} zones chaudes"
            
            return {
                'qi_level': neural.get('qi_level', 1131),
                'hot_zones': hot_zones,
                'total_entries': total_entries,
                'summary': summary,
                'recent_entries': recent_entries[-10:]  # 10 dernières entrées
            }
            
        except Exception as e:
            print(f"❌ Erreur chargement contexte: {e}")
            return {
                'qi_level': 1131,
                'hot_zones': 0,
                'total_entries': 0,
                'summary': 'Mémoire thermique en cours de chargement',
                'recent_entries': []
            }
    
    def log_connection(self, agent_url: str, action: str):
        """Log les connexions dans la mémoire thermique"""
        try:
            with open(self.memory_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Ajouter log de connexion
            timestamp = int(time.time())
            log_entry = {
                'id': f'auto_connection_{timestamp}',
                'content': f'Auto-connexion {action} avec agent {agent_url}',
                'timestamp': timestamp,
                'importance': 1,
                'temperature': 90,
                'zone': 'zone_auto_connections',
                'source': 'thermal_memory_auto_connector',
                'type': 'auto_connection_log',
                'agent_url': agent_url,
                'action': action
            }
            
            # Créer zone si nécessaire
            if 'thermal_zones' not in data:
                data['thermal_zones'] = {}
            
            if 'zone_auto_connections' not in data['thermal_zones']:
                data['thermal_zones']['zone_auto_connections'] = {
                    'temperature': 90,
                    'description': 'Zone des connexions automatiques',
                    'entries': []
                }
            
            data['thermal_zones']['zone_auto_connections']['entries'].append(log_entry)
            
            # Sauvegarder
            with open(self.memory_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"📝 Connexion loggée: {action} - {agent_url}")
            
        except Exception as e:
            print(f"⚠️ Erreur log connexion: {e}")
    
    def start_monitoring(self):
        """Démarre le monitoring automatique"""
        self.monitoring_active = True
        
        def monitor_loop():
            while self.monitoring_active:
                try:
                    # Scanner nouveaux agents
                    self.scan_for_agents()
                    
                    # Auto-greffer aux nouveaux agents
                    for agent_url, agent_info in self.detected_agents.items():
                        if agent_url not in self.active_connections:
                            adapter = self.create_universal_adapter(agent_info)
                            self.auto_graft_to_agent(agent_url, adapter)
                    
                    # Vérifier connexions existantes
                    self.check_existing_connections()
                    
                    time.sleep(30)  # Scan toutes les 30 secondes
                    
                except Exception as e:
                    print(f"❌ Erreur monitoring: {e}")
                    time.sleep(60)
        
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
        print("🔄 Monitoring automatique démarré")
    
    def check_existing_connections(self):
        """Vérifie les connexions existantes"""
        for agent_url, connection in list(self.active_connections.items()):
            try:
                # Test simple de connectivité
                response = requests.get(f"{agent_url}/status", timeout=5)
                if response.status_code != 200:
                    # Tentative de reconnexion
                    adapter = connection['adapter']
                    if not self.auto_graft_to_agent(agent_url, adapter):
                        del self.active_connections[agent_url]
                        self.log_connection(agent_url, 'disconnected')
                        
            except Exception as e:
                # Connexion perdue
                del self.active_connections[agent_url]
                self.log_connection(agent_url, 'lost_connection')
    
    def get_status(self) -> Dict[str, Any]:
        """Retourne le statut du système"""
        return {
            'detected_agents': len(self.detected_agents),
            'active_connections': len(self.active_connections),
            'monitoring_active': self.monitoring_active,
            'agents': list(self.detected_agents.keys()),
            'connections': list(self.active_connections.keys())
        }

def main():
    """Point d'entrée principal"""
    print("🚀 SYSTÈME D'AUTO-CONNEXION MÉMOIRE THERMIQUE")
    print("=" * 50)
    print("Développé pour Jean-Luc PASSAVE")
    print("Auto-détection et auto-greffe à tous agents IA")
    print()
    
    memory_file = "./thermal_memory_real_clones_1749979850296.json"
    connector = ThermalMemoryAutoConnector(memory_file)
    
    # Scanner les agents
    detected = connector.scan_for_agents()
    print(f"📊 {len(detected)} agents détectés")
    
    # Auto-greffer à tous les agents détectés
    for agent_url, agent_info in detected.items():
        adapter = connector.create_universal_adapter(agent_info)
        connector.auto_graft_to_agent(agent_url, adapter)
    
    # Démarrer le monitoring
    connector.start_monitoring()
    
    # Afficher le statut
    status = connector.get_status()
    print(f"✅ {status['active_connections']} connexions actives")
    print("🔄 Monitoring automatique en cours...")
    
    try:
        while True:
            time.sleep(60)
            status = connector.get_status()
            print(f"[{time.strftime('%H:%M:%S')}] Agents: {status['detected_agents']}, Connexions: {status['active_connections']}")
    except KeyboardInterrupt:
        print("\n🛑 Arrêt du système d'auto-connexion")

if __name__ == "__main__":
    main()
