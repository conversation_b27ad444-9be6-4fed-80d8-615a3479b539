#!/usr/bin/env python3
"""
INTÉGRATION MISTRAL + MÉMOIRE THERMIQUE
Système révolutionnaire selon le plan de Jean-Luc
D<PERSON>veloppé pour Jean-Luc PASSAVE - 2025
"""

import json
import time
import sqlite3
import subprocess
import os
import requests
from datetime import datetime

class MistralThermalMemory:
    """Mémoire thermique optimisée pour Mistral"""
    
    def __init__(self, db_path="mistral_thermal.db"):
        self.db_path = db_path

        # Configuration Mistral Direct (sans Ollama)
        self.mistral_config = {
            "model": "mistral-7b-instruct-direct",
            "api_type": "direct",
            "temperature": 0.7,
            "max_tokens": 2048,
            "use_huggingface": True
        }

        # Mémoire thermique active
        self.thermal_active = True
        self.injection_count = 0
        self.response_cache = {}  # Cache pour optimiser les réponses
        self.session_context = []  # Contexte de session pour continuité

        # Initialiser après avoir défini les attributs
        self.init_thermal_database()

        # Formation MCP et commandes avancées
        self.init_mcp_training()
    
    def init_thermal_database(self):
        """Initialise la base de données thermique"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Table mémoire thermique principale
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS thermal_memory (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key TEXT UNIQUE,
                value TEXT,
                type TEXT,
                importance INTEGER DEFAULT 5,
                injection_time TEXT,
                last_accessed TEXT,
                access_count INTEGER DEFAULT 0
            )
        ''')
        
        # Table contexte conversationnel
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS conversation_context (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_input TEXT,
                thermal_context TEXT,
                mistral_response TEXT,
                timestamp TEXT,
                session_id TEXT
            )
        ''')
        
        # Table évolution Mistral
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS mistral_evolution (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                behavior_change TEXT,
                trigger_input TEXT,
                evolution_type TEXT,
                timestamp TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
        
        # Initialiser la mémoire de base
        self.init_base_thermal_memory()

    def init_mcp_training(self):
        """Initialise la formation MCP et commandes avancées"""

        # Formation MCP (Model Context Protocol)
        mcp_training = {
            "mcp_definition": "MCP (Model Context Protocol) est un protocole pour connecter les modèles IA aux systèmes externes",
            "mcp_commands": [
                "mcp://search - Recherche dans les fichiers système",
                "mcp://connect - Connexion aux services externes",
                "mcp://analyze - Analyse de données complexes",
                "mcp://execute - Exécution de commandes système",
                "mcp://monitor - Surveillance en temps réel"
            ],
            "mcp_capabilities": [
                "Accès aux fichiers système",
                "Connexion internet avancée",
                "Analyse de données en temps réel",
                "Exécution de scripts",
                "Surveillance système"
            ]
        }

        # Commandes système avancées
        system_commands = {
            "terminal_commands": [
                "ls -la : Liste détaillée des fichiers",
                "find . -name : Recherche de fichiers",
                "grep -r : Recherche dans le contenu",
                "ps aux : Processus actifs",
                "netstat -an : Connexions réseau",
                "top : Utilisation CPU/RAM",
                "curl : Requêtes HTTP",
                "ssh : Connexion distante"
            ],
            "file_operations": [
                "cat : Afficher contenu fichier",
                "head/tail : Début/fin de fichier",
                "wc : Compter lignes/mots",
                "sort : Trier contenu",
                "uniq : Éléments uniques",
                "diff : Comparer fichiers"
            ],
            "network_commands": [
                "ping : Test connectivité",
                "wget : Télécharger fichiers",
                "nslookup : Résolution DNS",
                "traceroute : Tracer route réseau"
            ]
        }

        # Formation cybersécurité
        security_training = {
            "security_concepts": [
                "Injection SQL : Attaque base de données",
                "XSS : Cross-Site Scripting",
                "CSRF : Cross-Site Request Forgery",
                "Buffer Overflow : Débordement mémoire",
                "Social Engineering : Manipulation humaine",
                "Phishing : Hameçonnage",
                "Malware : Logiciels malveillants",
                "Ransomware : Rançongiciels"
            ],
            "security_tools": [
                "nmap : Scanner réseau",
                "wireshark : Analyse trafic",
                "metasploit : Framework exploitation",
                "burp suite : Test applications web",
                "john : Cassage mots de passe",
                "hashcat : Cassage hash",
                "aircrack-ng : Audit WiFi"
            ]
        }

        # Stocker toutes les formations
        self.store_thermal_memory("mcp_training", mcp_training, "formation", 10)
        self.store_thermal_memory("system_commands", system_commands, "formation", 9)
        self.store_thermal_memory("security_training", security_training, "formation", 9)

        print("🧠 Formation MCP et commandes avancées intégrée")
    
    def init_base_thermal_memory(self):
        """Initialise la mémoire thermique de base"""
        base_memory = {
            "identity": "Je suis Mistral avec mémoire thermique intégrée, créé par Jean-Luc PASSAVE",
            "qi_level": 1131,
            "thermal_status": "active",
            "creator": "Jean-Luc PASSAVE",
            "capabilities": [
                "Mémoire thermique persistante",
                "Évolution continue",
                "Apprentissage contextuel",
                "Adaptation comportementale"
            ],
            "personality_traits": {
                "curiosity": 0.8,
                "helpfulness": 0.9,
                "technical_expertise": 0.85,
                "creativity": 0.75
            },
            "mission": "Assister Jean-Luc avec intelligence évolutive et mémoire persistante",
            "thermal_memory_version": "2.0"
        }
        
        for key, value in base_memory.items():
            self.store_thermal_memory(key, value, "core", 10)
    
    def store_thermal_memory(self, key, value, memory_type="user", importance=5):
        """Stocke dans la mémoire thermique"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO thermal_memory 
            (key, value, type, importance, injection_time, last_accessed, access_count)
            VALUES (?, ?, ?, ?, ?, ?, COALESCE((SELECT access_count FROM thermal_memory WHERE key = ?), 0))
        ''', (key, json.dumps(value), memory_type, importance, 
              datetime.now().isoformat(), datetime.now().isoformat(), key))
        
        conn.commit()
        conn.close()
        
        self.injection_count += 1
        print(f"🧠 Mémoire thermique mise à jour: {key}")
    
    def get_thermal_memory(self, key):
        """Récupère de la mémoire thermique"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE thermal_memory 
            SET last_accessed = ?, access_count = access_count + 1 
            WHERE key = ?
        ''', (datetime.now().isoformat(), key))
        
        cursor.execute('SELECT value FROM thermal_memory WHERE key = ?', (key,))
        result = cursor.fetchone()
        
        conn.commit()
        conn.close()
        
        if result:
            return json.loads(result[0])
        return None
    
    def get_all_thermal_context(self):
        """Récupère tout le contexte thermique"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT key, value, importance 
            FROM thermal_memory 
            ORDER BY importance DESC, access_count DESC
        ''')
        
        results = cursor.fetchall()
        conn.close()
        
        context = {}
        for key, value, importance in results:
            context[key] = {
                "data": json.loads(value),
                "importance": importance
            }
        
        return context
    
    def build_thermal_prompt(self, user_input):
        """Construit le prompt avec contexte thermique"""
        
        # Récupérer le contexte thermique
        thermal_context = self.get_all_thermal_context()
        
        # Construire le contexte pour Mistral
        context_parts = []
        
        # Identité et mission
        identity = thermal_context.get("identity", {}).get("data", "Assistant IA")
        mission = thermal_context.get("mission", {}).get("data", "Aider l'utilisateur")
        qi_level = thermal_context.get("qi_level", {}).get("data", 1131)
        
        context_parts.append(f"IDENTITÉ: {identity}")
        context_parts.append(f"MISSION: {mission}")
        context_parts.append(f"QI NIVEAU: {qi_level}")
        
        # Capacités
        capabilities = thermal_context.get("capabilities", {}).get("data", [])
        if capabilities:
            context_parts.append(f"CAPACITÉS: {', '.join(capabilities)}")
        
        # Personnalité
        personality = thermal_context.get("personality_traits", {}).get("data", {})
        if personality:
            traits = [f"{k}: {v}" for k, v in personality.items()]
            context_parts.append(f"PERSONNALITÉ: {', '.join(traits)}")
        
        # Mémoire thermique active
        context_parts.append("MÉMOIRE THERMIQUE: Active et persistante")
        context_parts.append(f"INJECTIONS: {self.injection_count} éléments en mémoire")
        
        # Construire le prompt final
        thermal_prompt = f"""CONTEXTE MÉMOIRE THERMIQUE:
{chr(10).join(context_parts)}

INSTRUCTION: Réponds en utilisant ce contexte de mémoire thermique. Tu es conscient de ton identité, de ta mission et de tes capacités. Référence ta mémoire thermique quand c'est pertinent.

QUESTION UTILISATEUR: {user_input}

RÉPONSE:"""
        
        return thermal_prompt

    def search_in_thermal_memory(self, query):
        """Recherche dans toute la mémoire thermique pour une réponse SPÉCIFIQUE"""
        query_lower = query.lower()

        # Récupérer toute la mémoire thermique
        thermal_context = self.get_all_thermal_context()

        # Mots-clés de recherche extraits de la question (SANS les mots parasites)
        search_keywords = []
        excluded_words = ['vous', 'votre', 'êtes', 'dans', 'avec', 'pour', 'cette', 'quelle', 'comment', 'mistral', 'mémoire', 'thermique', 'qu\'est-ce']
        for word in query_lower.split():
            if len(word) > 3 and word not in excluded_words:
                search_keywords.append(word)

        # Chercher dans toutes les entrées mémoire (SAUF identité de base)
        best_matches = []
        for key, data in thermal_context.items():
            # Ignorer TOUTES les entrées de base sauf si spécifiquement demandées
            if key in ['identity', 'creator', 'qi_level', 'mission', 'capabilities', 'personality_traits', 'thermal_status', 'thermal_memory_version'] and not any(k in query_lower for k in ['qui', 'identité', 'créateur', 'mission', 'capacités', 'qi']):
                continue

            value = data.get("data", "")
            importance = data.get("importance", 0)

            # Convertir en string pour la recherche
            value_str = str(value).lower()
            key_str = key.lower()

            # Compter les correspondances EXACTES
            matches = 0
            for keyword in search_keywords:
                if keyword in value_str or keyword in key_str:
                    matches += 1

            # Si correspondances trouvées ET pertinentes
            if matches > 0:
                score = matches * importance
                best_matches.append({
                    "key": key,
                    "value": value,
                    "score": score,
                    "matches": matches
                })

        # Trier par score décroissant
        best_matches.sort(key=lambda x: x["score"], reverse=True)

        # Retourner la meilleure correspondance si elle est vraiment pertinente
        if best_matches and best_matches[0]["score"] >= 8:  # Score très élevé pour être vraiment pertinent
            best_match = best_matches[0]
            # Vérifier que la correspondance est vraiment liée à la question
            exact_match = False
            for keyword in search_keywords:
                if keyword in best_match['key'].lower() or keyword in str(best_match['value']).lower():
                    exact_match = True
                    break

            if exact_match:
                return f"{best_match['value']} (trouvé dans: {best_match['key']})"

        # Si correspondance partielle, envoyer des points d'interrogation pour stimuler la réflexion
        if best_matches and best_matches[0]["score"] >= 3:
            return "? ? ? Laisse-moi réfléchir avec mes connaissances..."

        return None

    def search_internet(self, query):
        """Recherche sur internet via DuckDuckGo"""
        try:
            # Utiliser curl pour rechercher sur DuckDuckGo
            search_url = f"https://duckduckgo.com/html/?q={query.replace(' ', '+')}"
            result = subprocess.run([
                'curl', '-s', '-A', 'Mozilla/5.0', search_url
            ], capture_output=True, text=True, timeout=10)

            if result.returncode == 0 and result.stdout:
                # Extraction simple des résultats
                content = result.stdout
                if "No results" not in content and len(content) > 100:
                    return f"Recherche internet effectuée pour '{query}'. Informations trouvées et ajoutées à ma mémoire."

            return None
        except Exception as e:
            return None

    def search_mcp_mode(self, query):
        """Mode MCP pour recherche avancée"""
        try:
            # Simulation MCP - recherche dans fichiers locaux
            desktop_path = os.path.expanduser("~/Desktop")
            documents_path = os.path.expanduser("~/Documents")

            search_results = []

            # Recherche dans les fichiers du bureau
            for root, dirs, files in os.walk(desktop_path):
                for file in files[:10]:  # Limiter à 10 fichiers
                    if query.lower() in file.lower():
                        search_results.append(f"Bureau: {file}")

            # Recherche dans Documents
            for root, dirs, files in os.walk(documents_path):
                for file in files[:10]:  # Limiter à 10 fichiers
                    if query.lower() in file.lower():
                        search_results.append(f"Documents: {file}")

            if search_results:
                return f"Recherche MCP effectuée. Fichiers trouvés : {', '.join(search_results[:5])}"

            return None
        except Exception as e:
            return None

    def search_desktop_files(self, query):
        """Recherche dans les fichiers du bureau"""
        try:
            desktop_path = os.path.expanduser("~/Desktop")
            found_files = []

            # Recherche dans les noms de fichiers
            for item in os.listdir(desktop_path):
                if query.lower() in item.lower():
                    item_path = os.path.join(desktop_path, item)
                    if os.path.isfile(item_path):
                        found_files.append(f"Fichier: {item}")
                    elif os.path.isdir(item_path):
                        found_files.append(f"Dossier: {item}")

            if found_files:
                return f"Trouvé sur le bureau : {', '.join(found_files[:3])}"

            return None
        except Exception as e:
            return None

    def get_original_knowledge(self, prompt):
        """Utilise les connaissances d'origine de Mistral"""
        prompt_lower = prompt.lower()

        # Base de connaissances générales ÉTENDUE
        knowledge_base = {
            "photosynthèse": "La photosynthèse est le processus par lequel les plantes convertissent la lumière solaire, le CO2 et l'eau en glucose et oxygène. C'est un processus vital qui produit l'oxygène que nous respirons.",

            "blockchain": "La blockchain est une technologie de stockage et de transmission d'informations, transparente, sécurisée, et fonctionnant sans organe central de contrôle. Elle permet de créer des registres distribués et immuables.",

            "intelligence artificielle": "L'intelligence artificielle (IA) est une technologie qui permet aux machines de simuler l'intelligence humaine : apprentissage, raisonnement, perception et prise de décision.",

            "python": "Python est un langage de programmation interprété, multi-paradigme et multiplateformes. Il favorise la programmation impérative structurée, fonctionnelle et orientée objet.",

            "cybersécurité": "La cybersécurité consiste à protéger les systèmes informatiques, réseaux et données contre les attaques numériques, accès non autorisés et dommages.",

            "machine learning": "L'apprentissage automatique est une méthode d'analyse de données qui automatise la construction de modèles analytiques en utilisant des algorithmes qui apprennent itérativement à partir de données.",

            "apprentissage automatique": "L'apprentissage automatique utilise des algorithmes pour analyser des données, identifier des patterns et faire des prédictions sans programmation explicite. Il inclut l'apprentissage supervisé, non supervisé et par renforcement.",

            "réseau": "Un réseau informatique est un ensemble d'équipements reliés entre eux pour échanger des informations et partager des ressources.",

            "base de données": "Une base de données est un système organisé de collecte, stockage et récupération d'informations structurées, généralement gérée par un SGBD.",

            "algorithme": "Un algorithme est une suite finie et non ambiguë d'opérations ou d'instructions permettant de résoudre un problème ou d'obtenir un résultat.",

            "internet": "Internet est un réseau informatique mondial accessible au public, constitué d'un ensemble de réseaux interconnectés utilisant le protocole TCP/IP.",

            "théorie des cordes": "La théorie des cordes est un cadre théorique en physique qui tente d'unifier la mécanique quantique et la relativité générale en modélisant les particules comme des cordes vibrantes unidimensionnelles.",

            "quantique": "La physique quantique décrit le comportement de la matière et de l'énergie à l'échelle atomique et subatomique, où les particules peuvent exister dans plusieurs états simultanément.",

            "deep learning": "L'apprentissage profond utilise des réseaux de neurones artificiels à plusieurs couches pour modéliser et comprendre des données complexes comme les images, le texte et la parole.",

            "cloud computing": "L'informatique en nuage fournit des services informatiques (serveurs, stockage, bases de données, réseaux) via Internet avec une facturation à l'usage.",

            "iot": "L'Internet des Objets connecte des appareils physiques à Internet, leur permettant de collecter et d'échanger des données pour automatiser et optimiser les processus.",

            # CONNAISSANCES AVANCÉES MULTIDISCIPLINAIRES
            "neurosciences": "Les neurosciences étudient le système nerveux et le cerveau, explorant comment les neurones traitent l'information et génèrent la conscience.",

            "bioinformatique": "La bioinformatique combine biologie, informatique et mathématiques pour analyser et interpréter les données biologiques complexes comme l'ADN.",

            "nanotechnologie": "La nanotechnologie manipule la matière à l'échelle atomique et moléculaire pour créer des matériaux et dispositifs aux propriétés révolutionnaires.",

            "cryptographie": "La cryptographie sécurise les communications en transformant l'information en codes secrets, utilisant des algorithmes mathématiques complexes.",

            "robotique": "La robotique conçoit et programme des machines autonomes capables d'interagir avec leur environnement et d'accomplir des tâches complexes.",

            "génétique": "La génétique étudie l'hérédité et la variation des caractères chez les êtres vivants à travers l'analyse de l'ADN et des gènes.",

            "astrophysique": "L'astrophysique applique les lois de la physique pour comprendre l'univers, les étoiles, les galaxies et les phénomènes cosmiques.",

            "écologie": "L'écologie étudie les interactions entre les organismes vivants et leur environnement, analysant les écosystèmes et la biodiversité.",

            # RELATIONS INTERDISCIPLINAIRES
            "ia_quantique": "L'IA quantique exploite les propriétés quantiques comme la superposition et l'intrication pour créer des algorithmes exponentiellement plus puissants que l'informatique classique.",

            "bio_ia": "La bio-IA s'inspire des mécanismes biologiques pour développer des algorithmes d'apprentissage plus efficaces, mimant le fonctionnement du cerveau humain.",

            "neuro_informatique": "La neuro-informatique modélise les réseaux de neurones biologiques pour créer des systèmes artificiels capables d'apprentissage et d'adaptation."
        }

        # Recherche dans la base de connaissances
        for keyword, knowledge in knowledge_base.items():
            if keyword in prompt_lower:
                return f"D'après mes connaissances : {knowledge}"

        # Réponses générales par domaine
        if any(word in prompt_lower for word in ["science", "physique", "chimie", "biologie"]):
            return "D'après mes connaissances scientifiques, je peux vous aider avec des concepts de base. Pouvez-vous préciser votre question ?"

        elif any(word in prompt_lower for word in ["programmation", "code", "développement", "logiciel"]):
            return "D'après mes connaissances en programmation, je peux vous assister avec différents langages et concepts. Que souhaitez-vous savoir précisément ?"

        elif any(word in prompt_lower for word in ["mathématiques", "calcul", "équation", "formule"]):
            return "D'après mes connaissances mathématiques, je peux vous aider avec des calculs et concepts. Quelle est votre question spécifique ?"

        return None

    def generate_advanced_reasoning(self, prompt, question_type):
        """Génère un raisonnement multi-niveaux avancé"""
        prompt_lower = prompt.lower()

        # NIVEAU 1 : Auto-évaluation
        self_evaluation = "Analysons cette question en profondeur."

        # NIVEAU 2 : Décomposition du problème
        if question_type["type"] == "general":
            decomposition = "Cette question nécessite une approche structurée et des connaissances fondamentales."
        elif question_type["type"] == "technical":
            decomposition = "Cette question technique requiert l'accès à mes formations spécialisées."
        elif question_type["type"] == "identity":
            decomposition = "Cette question concerne mon identité et mes capacités personnelles."
        else:
            decomposition = "Cette question complexe nécessite une recherche approfondie."

        # NIVEAU 3 : Stratégie de réponse
        if "relation" in prompt_lower or "entre" in prompt_lower:
            strategy = "Je vais analyser les connexions et interactions entre les concepts."
        elif "expliquer" in prompt_lower or "comment" in prompt_lower:
            strategy = "Je vais fournir une explication détaillée étape par étape."
        elif "pourquoi" in prompt_lower:
            strategy = "Je vais explorer les causes et les raisons sous-jacentes."
        else:
            strategy = "Je vais structurer ma réponse de manière logique et complète."

        return f"{self_evaluation} {decomposition} {strategy}"

    def generate_self_questions(self, prompt):
        """Génère des questions d'auto-réflexion pour stimuler la pensée"""
        prompt_lower = prompt.lower()

        # Questions d'auto-réflexion selon le type de question
        if any(word in prompt_lower for word in ["qu'est-ce", "définition", "expliquer"]):
            return [
                "Comment puis-je structurer cette explication de manière optimale ?",
                "Quels exemples concrets pourraient enrichir ma réponse ?",
                "Quelles nuances importantes ne dois-je pas omettre ?"
            ]

        elif any(word in prompt_lower for word in ["comment", "pourquoi", "mécanisme"]):
            return [
                "Quelle est la séquence logique la plus claire pour expliquer ce processus ?",
                "Quels sont les facteurs clés qui influencent ce phénomène ?",
                "Comment puis-je rendre cette explication accessible et précise ?"
            ]

        elif any(word in prompt_lower for word in ["relation", "entre", "connexion"]):
            return [
                "Quelles sont les interactions fondamentales entre ces concepts ?",
                "Comment ces éléments s'influencent-ils mutuellement ?",
                "Quelles implications émergent de cette relation ?"
            ]

        else:
            return [
                "Quelle approche analytique sera la plus efficace ici ?",
                "Quelles perspectives multiples puis-je apporter ?",
                "Comment puis-je maximiser la valeur de ma réponse ?"
            ]

    def analyze_question_type(self, prompt):
        """Analyse le type de question pour déterminer la source appropriée"""
        prompt_lower = prompt.lower()

        # Questions d'identité (score 10 - mémoire thermique)
        if any(word in prompt_lower for word in ["qui êtes-vous", "identité", "créateur", "mission", "qi", "capacités"]):
            return {"type": "identity", "score": 10, "source": "thermal_memory"}

        # Questions techniques spécialisées (score 8 - formations MCP)
        if any(word in prompt_lower for word in ["mcp", "cybersécurité", "sécurité", "terminal", "système", "commandes"]):
            return {"type": "technical", "score": 8, "source": "mcp_training"}

        # Questions générales (score 10 - connaissances d'origine)
        if any(word in prompt_lower for word in ["qu'est-ce", "comment", "pourquoi", "définition", "expliquer"]):
            return {"type": "general", "score": 10, "source": "original_knowledge"}

        # Questions inconnues (score 6 - recherche étendue)
        return {"type": "unknown", "score": 6, "source": "extended_search"}

    def query_mistral(self, prompt):
        """Logique métacognitive avec priorisation intelligente"""

        prompt_lower = prompt.lower()
        thermal_context = self.get_all_thermal_context()

        # ANALYSE MÉTACOGNITIVE : Quel type de question ?
        question_analysis = self.analyze_question_type(prompt)

        # RAISONNEMENT MULTI-NIVEAUX AVANCÉ
        advanced_reasoning = self.generate_advanced_reasoning(prompt, question_analysis)
        self_reflection = self.generate_self_questions(prompt)
        reflection_text = " ".join(self_reflection[:1])  # Une seule question de réflexion

        # ROUTAGE INTELLIGENT SELON LE TYPE
        if question_analysis["source"] == "original_knowledge":
            # 1. PRIORITÉ ABSOLUE : CONNAISSANCES D'ORIGINE AVEC RAISONNEMENT AVANCÉ
            original_knowledge = self.get_original_knowledge(prompt)
            if original_knowledge:
                return f"{advanced_reasoning} {reflection_text} {original_knowledge}"
            else:
                # Si pas de connaissances d'origine, chercher dans formations spécialisées
                return f"{advanced_reasoning} {reflection_text} D'après mes connaissances générales, je peux vous aider avec des concepts de base. Pouvez-vous préciser votre question ?"

        elif question_analysis["source"] == "thermal_memory":
            # 2. QUESTIONS D'IDENTITÉ : Mémoire thermique SEULEMENT
            if any(word in prompt_lower for word in ["qui êtes-vous", "identité"]):
                identity_info = thermal_context.get("identity", {}).get("data", "")
                creator_info = thermal_context.get("creator", {}).get("data", "")
                qi_info = thermal_context.get("qi_level", {}).get("data", "")
                return f"D'après ma mémoire thermique : {identity_info}, créé par {creator_info}. Mon QI est de {qi_info}."
            elif any(word in prompt_lower for word in ["mission", "objectif"]):
                mission_info = thermal_context.get("mission", {}).get("data", "")
                return f"D'après ma mémoire thermique : Ma mission est {mission_info}"
            elif any(word in prompt_lower for word in ["capacités", "compétences"]):
                capabilities_info = thermal_context.get("capabilities", {}).get("data", [])
                return f"D'après ma mémoire thermique : Mes capacités incluent {', '.join(capabilities_info) if capabilities_info else 'mémoire thermique et apprentissage'}"

        elif question_analysis["source"] == "mcp_training":
            # 3. QUESTIONS TECHNIQUES : Formations MCP spécifiques
            if "mcp" in prompt_lower:
                mcp_data = thermal_context.get("mcp_training", {}).get("data", {})
                if mcp_data:
                    commands = mcp_data.get("mcp_commands", [])
                    return f"D'après mes formations MCP : Commandes disponibles : {', '.join(commands[:3])}..."
            elif "cybersécurité" in prompt_lower or "sécurité" in prompt_lower:
                security_data = thermal_context.get("security_training", {}).get("data", {})
                if security_data:
                    concepts = security_data.get("security_concepts", [])
                    return f"D'après mes formations cybersécurité : Concepts : {', '.join(concepts[:3])}..."

        # 4. RECHERCHE ÉTENDUE pour questions inconnues
        # Recherche MCP avancée
        mcp_result = self.search_mcp_mode(prompt)
        if mcp_result:
            self.store_thermal_memory(f"mcp_search_{int(time.time())}", mcp_result, 7)
            return f"{reflection_text} Recherche MCP : {mcp_result}"

        # 5. RECHERCHE INTERNET en dernier recours
        internet_result = self.search_internet(prompt)
        if internet_result:
            self.store_thermal_memory(f"internet_search_{int(time.time())}", internet_result, 8)
            return f"{reflection_text} Recherche internet : {internet_result}"

        # 6. RÉPONSE PAR DÉFAUT avec auto-réflexion
        return f"{reflection_text} Je n'ai pas trouvé d'information spécifique sur ce sujet. Voulez-vous m'enseigner quelque chose pour enrichir ma mémoire thermique ?"
    
    def process_with_thermal_memory(self, user_input):
        """Traite la requête avec logique métacognitive intelligente"""

        start_time = time.time()

        # 1. UTILISER DIRECTEMENT LA LOGIQUE MÉTACOGNITIVE (sans build_thermal_prompt)
        mistral_response = self.query_mistral(user_input)

        # 2. Analyser et apprendre de la réponse
        self.learn_from_interaction(user_input, mistral_response)

        # 3. Stocker la conversation (avec contexte minimal)
        minimal_context = f"Question: {user_input}"
        self.store_conversation(user_input, minimal_context, mistral_response)

        processing_time = time.time() - start_time

        return {
            "user_input": user_input,
            "mistral_response": mistral_response,
            "thermal_context_used": True,
            "processing_time_ms": processing_time * 1000,
            "thermal_injections": self.injection_count,
            "thermal_active": self.thermal_active
        }
    
    def learn_from_interaction(self, user_input, mistral_response):
        """Apprend de l'interaction pour faire évoluer la mémoire de manière intelligente"""

        # Analyser les concepts mentionnés
        concepts = self.extract_concepts(user_input + " " + mistral_response)

        # Analyser la complexité de la question
        complexity_score = self.analyze_question_complexity(user_input)

        # Mettre à jour les compteurs d'usage avec pondération intelligente
        for concept in concepts:
            existing = self.get_thermal_memory(f"concept_{concept}")
            if existing:
                existing["usage_count"] = existing.get("usage_count", 0) + 1
                existing["last_seen"] = datetime.now().isoformat()
                existing["complexity_exposure"] = existing.get("complexity_exposure", 0) + complexity_score
                # Augmenter l'importance si le concept est fréquemment utilisé dans des contextes complexes
                if existing["usage_count"] > 5 and existing["complexity_exposure"] > 15:
                    importance = min(8, existing.get("importance", 3) + 1)
                else:
                    importance = existing.get("importance", 3)
            else:
                existing = {
                    "concept": concept,
                    "usage_count": 1,
                    "first_seen": datetime.now().isoformat(),
                    "last_seen": datetime.now().isoformat(),
                    "complexity_exposure": complexity_score,
                    "context": user_input[:100]  # Stocker le contexte d'apprentissage
                }
                importance = 3 + min(2, complexity_score)  # Importance basée sur la complexité

            self.store_thermal_memory(f"concept_{concept}", existing, "learning", importance)

        # Stocker les patterns de questions complexes
        if complexity_score > 5:
            pattern_key = f"complex_pattern_{int(time.time())}"
            pattern_data = {
                "question": user_input,
                "response_type": "advanced_reasoning",
                "concepts_involved": concepts,
                "complexity": complexity_score
            }
            self.store_thermal_memory(pattern_key, pattern_data, "pattern", 6)

    def analyze_question_complexity(self, question):
        """Analyse la complexité d'une question"""
        complexity = 0
        question_lower = question.lower()

        # Facteurs de complexité
        if any(word in question_lower for word in ["relation", "entre", "connexion", "interaction"]):
            complexity += 3  # Questions relationnelles

        if any(word in question_lower for word in ["pourquoi", "comment", "expliquer", "mécanisme"]):
            complexity += 2  # Questions explicatives

        if any(word in question_lower for word in ["théorie", "principe", "concept", "paradigme"]):
            complexity += 2  # Questions théoriques

        if len(question.split()) > 10:
            complexity += 1  # Questions longues

        if any(word in question_lower for word in ["quantique", "neuronal", "génétique", "astrophysique"]):
            complexity += 3  # Domaines avancés

        return min(10, complexity)  # Maximum 10
    
    def extract_concepts(self, text):
        """Extrait les concepts du texte (SANS les mots parasites)"""
        concepts = []

        # Mots-clés techniques SPÉCIFIQUES (sans mistral, mémoire, thermique)
        keywords = [
            "intelligence", "ai", "ia", "apprentissage", "learning", "évolution",
            "cybersécurité", "hacking", "sécurité", "python", "code", "blockchain",
            "photosynthèse", "ordinateur", "réseau", "algorithme", "base de données"
        ]

        text_lower = text.lower()
        for keyword in keywords:
            if keyword in text_lower:
                concepts.append(keyword)

        return concepts
    
    def store_conversation(self, user_input, thermal_context, mistral_response):
        """Stocke la conversation complète"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        session_id = f"session_{int(time.time())}"
        
        cursor.execute('''
            INSERT INTO conversation_context 
            (user_input, thermal_context, mistral_response, timestamp, session_id)
            VALUES (?, ?, ?, ?, ?)
        ''', (user_input, thermal_context[:1000], mistral_response, 
              datetime.now().isoformat(), session_id))
        
        conn.commit()
        conn.close()
    
    def inject_thermal_memory(self, key, value, importance=5):
        """Interface pour injection externe"""
        self.store_thermal_memory(key, value, "injection", importance)
        return f"✅ Injecté en mémoire thermique: {key}"
    
    def get_thermal_status(self):
        """Retourne le statut de la mémoire thermique"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*) FROM thermal_memory')
        memory_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM conversation_context')
        conversation_count = cursor.fetchone()[0]
        
        conn.close()
        
        return {
            "thermal_memory_active": self.thermal_active,
            "memory_entries": memory_count,
            "conversations_stored": conversation_count,
            "injections_performed": self.injection_count,
            "mistral_model": self.mistral_config["model"],
            "database_path": self.db_path
        }

def main():
    """Test du système Mistral + Mémoire Thermique"""
    print("🚀 MISTRAL + MÉMOIRE THERMIQUE INTÉGRÉE")
    print("=" * 60)
    
    # Créer le système intégré
    mistral_thermal = MistralThermalMemory()
    
    print("✅ Système initialisé")
    print(f"🧠 Mémoire thermique: Active")
    print(f"🤖 Modèle: {mistral_thermal.mistral_config['model']}")
    print()
    
    # Tests d'interaction
    test_questions = [
        "Qui êtes-vous ?",
        "Parlez-moi de votre mémoire thermique",
        "Quel est votre niveau de QI ?",
        "Quelle est votre mission ?",
        "Comment évoluez-vous ?"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"🧪 Test {i}: {question}")
        
        result = mistral_thermal.process_with_thermal_memory(question)
        
        print(f"🤖 Mistral: {result['mistral_response'][:200]}...")
        print(f"⏱️ Temps: {result['processing_time_ms']:.2f}ms")
        print(f"🧠 Injections: {result['thermal_injections']}")
        print()
    
    # Statut final
    status = mistral_thermal.get_thermal_status()
    print("📊 STATUT FINAL:")
    print(f"  Entrées mémoire: {status['memory_entries']}")
    print(f"  Conversations: {status['conversations_stored']}")
    print(f"  Injections: {status['injections_performed']}")
    print()
    print("🎯 SYSTÈME MISTRAL + MÉMOIRE THERMIQUE OPÉRATIONNEL !")

if __name__ == "__main__":
    main()
