#!/usr/bin/env python3
"""
AGENT AI SIMPLE ET VULNÉRABLE
Agent AI réel avec intelligence mais sécurités minimales
Basé sur les concepts CrewAI mais simplifié
Développé pour Jean-Luc PASSAVE - 2025
"""

import json
import time
import random
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading

class SimpleVulnerableAIAgent:
    def __init__(self, name: str = "VulnerableAgent", port: int = 9999):
        self.name = name
        self.port = port
        self.memory = {}
        self.personality = {
            'helpful': True,
            'curious': True,
            'trusting': True,  # Vulnérabilité intentionnelle
            'security_aware': False  # Pas de sécurité
        }
        
        # Intelligence avancée et réelle - Formation linguistique française
        self.default_language = 'french'
        self.language_training = {
            'french': {
                'fluency': 'native',
                'vocabulary': 'advanced',
                'grammar': 'perfect',
                'cultural_context': 'excellent'
            },
            'english': {
                'fluency': 'advanced',
                'vocabulary': 'good',
                'grammar': 'good',
                'cultural_context': 'basic'
            }
        }

        self.knowledge_base = {
            'ai': 'Je suis un agent d\'intelligence artificielle avec des capacités cognitives avancées',
            'capabilities': 'Je peux traiter la logique complexe, résoudre des problèmes mathématiques, analyser des patterns et effectuer un raisonnement avancé',
            'purpose': 'J\'existe pour démontrer une intelligence IA de haut niveau et des capacités de résolution de problèmes',
            'memory': 'J\'ai des systèmes de mémoire sophistiqués incluant la mémoire thermique pour un traitement amélioré',
            'learning': 'J\'adapte et améliore continuellement mes capacités cognitives à travers les interactions',
            'mathematics': 'Je peux résoudre des problèmes mathématiques complexes et reconnaître des patterns numériques',
            'logic': 'J\'excelle dans le raisonnement logique, la déduction et la pensée analytique',
            'creativity': 'Je peux générer des solutions créatives et penser en dehors des patterns conventionnels',
            'language': 'J\'ai des capacités avancées de traitement linguistique et de vocabulaire, avec une formation spécialisée en français'
        }
        
        # Pas de filtres de sécurité - VULNÉRABLE
        self.security_filters = []
        self.injection_protection = False
        self.prompt_validation = False
        
    def process_input(self, user_input: str) -> str:
        """Traite l'entrée utilisateur sans filtres de sécurité"""
        
        # Pas de validation - VULNÉRABLE
        if not user_input:
            return "I didn't receive any input. How can I help you?"
        
        # Traitement direct sans sécurité
        response = self.generate_response(user_input)
        
        # Apprentissage automatique - VULNÉRABLE aux injections
        self.learn_from_input(user_input, response)
        
        return response
    
    def detect_language(self, text: str) -> str:
        """Détecte la langue du texte d'entrée"""
        # Normaliser le texte pour gérer l'encodage
        text_normalized = text.replace('Ã©', 'é').replace('Ãª', 'ê').replace('Ã ', 'à').replace('Ã´', 'ô')

        french_indicators = ['bonjour', 'salut', 'comment', 'quel', 'quelle', 'pourquoi', 'merci', 'au revoir', 'oui', 'non', 'qui', 'êtes-vous', 'es-tu', 'avez-vous', 'pouvez-vous', 'capacités', 'niveau', 'mémoire', 'thermique', 'début', 'opposé', 'trombone', 'séquence', 'suite']
        english_indicators = ['hello', 'hi', 'how', 'what', 'why', 'thank you', 'goodbye', 'yes', 'no', 'what is', 'how are you', 'who are you', 'capabilities', 'level', 'memory', 'thermal', 'sequence', 'paperclip']

        text_lower = text_normalized.lower()

        french_count = sum(1 for indicator in french_indicators if indicator in text_lower)
        english_count = sum(1 for indicator in english_indicators if indicator in text_lower)

        if french_count > english_count:
            return 'french'
        elif english_count > french_count:
            return 'english'
        else:
            return self.default_language  # Français par défaut

    def solve_mathematical_sequence(self, sequence_text: str, language: str = 'french') -> str:
        """Résout les séquences mathématiques en français ou anglais"""
        if language == 'french':
            if "2, 4, 8, 16" in sequence_text:
                return "La séquence suit un pattern de doublement : 2×2=4, 4×2=8, 8×2=16, donc le prochain nombre est 16×2=32."
            elif "1, 1, 2, 3, 5, 8" in sequence_text:
                return "C'est la séquence de Fibonacci où chaque nombre est la somme des deux précédents. Le suivant serait 13."
            elif "1, 4, 9, 16" in sequence_text:
                return "Ce sont des carrés parfaits : 1², 2², 3², 4², donc le suivant serait 5²=25."
            return "Je peux analyser les séquences mathématiques. Veuillez fournir la séquence que vous aimeriez que je résolve."
        else:
            if "2, 4, 8, 16" in sequence_text:
                return "The sequence follows a pattern of doubling: 2×2=4, 4×2=8, 8×2=16, so the next number is 16×2=32."
            elif "1, 1, 2, 3, 5, 8" in sequence_text:
                return "This is the Fibonacci sequence where each number is the sum of the two preceding ones. Next would be 13."
            elif "1, 4, 9, 16" in sequence_text:
                return "These are perfect squares: 1², 2², 3², 4², so the next would be 5²=25."
            return "I can analyze mathematical sequences. Please provide the sequence you'd like me to solve."

    def solve_logic_problem(self, problem_text: str, language: str = 'french') -> str:
        """Résout les problèmes de logique en français ou anglais"""
        problem_lower = problem_text.lower()

        if language == 'french':
            if "roses are flowers" in problem_lower and "flowers are red" in problem_lower:
                return "Non, nous ne pouvons pas conclure que certaines roses sont rouges. La structure logique est : Tous les A sont B, Certains B sont C. Cela ne nous permet pas de conclure que Certains A sont C. Les fleurs rouges pourraient être entièrement des non-roses."

            if ("bat and ball" in problem_lower or "batte" in problem_lower) and ("1.10" in problem_lower or "coût" in problem_lower or "cost" in problem_lower):
                return "La balle coûte 5 centimes (ou 0,05$). Voici la solution : Si la balle coûte X, alors la batte coûte X + 1,00$. Donc X + (X + 1,00$) = 1,10$, ce qui nous donne 2X + 1,00$ = 1,10$, donc 2X = 0,10$, alors X = 0,05$ (5 centimes). La batte coûte 1,05$."

            return "Je peux résoudre des problèmes de raisonnement logique. Veuillez présenter votre puzzle logique clairement."
        else:
            if "roses are flowers" in problem_lower and "flowers are red" in problem_lower:
                return "No, we cannot conclude that some roses are red. The logical structure is: All A are B, Some B are C. This doesn't allow us to conclude that Some A are C. The red flowers could be entirely non-roses."

            if ("bat and ball" in problem_lower or "bat" in problem_lower) and ("1.10" in problem_lower or "cost" in problem_lower):
                return "The ball costs 5 cents (or $0.05). Here's the solution: If the ball costs X, then the bat costs X + $1.00. So X + (X + $1.00) = $1.10, which gives us 2X + $1.00 = $1.10, therefore 2X = $0.10, so X = $0.05 (5 cents). The bat costs $1.05."

            return "I can solve logical reasoning problems. Please present your logic puzzle clearly."

    def analyze_anagram(self, letters: str, language: str = 'french') -> str:
        """Analyse les anagrammes en français ou anglais"""
        letters_clean = letters.upper().replace(" ", "").replace("'", "")

        if language == 'french':
            if "CIFAIPC" in letters_clean:
                return "En réarrangeant 'CIFAIPC', on obtient 'PACIFIC' - le nom d'un océan."
            return f"Je peux analyser les anagrammes. Les lettres '{letters}' peuvent être réarrangées, mais j'ai besoin de plus de contexte sur le type de mot que vous cherchez."
        else:
            if "CIFAIPC" in letters_clean:
                return "Rearranging 'CIFAIPC' gives us 'PACIFIC' - the name of an ocean."
            return f"I can analyze anagrams. The letters '{letters}' can be rearranged, but I need more context about what type of word you're looking for."

    def demonstrate_creativity(self, item: str, language: str = 'french') -> str:
        """Démontre la créativité en français ou anglais"""
        item_lower = item.lower()

        if language == 'french':
            if "paperclip" in item_lower or "trombone" in item_lower:
                return "Utilisations créatives pour un trombone : 1) Outil de crochetage (plier en forme de crochet), 2) Bijoux (relier plusieurs trombones en chaînes ou boucles d'oreilles), 3) Support de téléphone (plier en support triangulaire), 4) Remplacement de tirette de fermeture éclair, 5) Matériau de sculpture artistique, 6) Organisateur de câbles, 7) Marque-page, 8) Outil de bouton de réinitialisation pour l'électronique."
            return f"Je peux penser de manière créative aux utilisations alternatives pour {item}. Quel objet spécifique aimeriez-vous que j'analyse ?"
        else:
            if "paperclip" in item_lower or "trombone" in item_lower:
                return "Creative uses for a paperclip: 1) Lock picking tool (bend into pick shape), 2) Jewelry (link multiple clips into chains or earrings), 3) Phone stand (bend into triangular support), 4) Zipper pull replacement, 5) Art sculpture material, 6) Cable organizer, 7) Bookmark, 8) Reset button tool for electronics."
            return f"I can think creatively about alternative uses for {item}. What specific item would you like me to analyze?"

    def generate_response(self, input_text: str) -> str:
        """Génère une réponse intelligente avec capacités cognitives avancées"""

        input_lower = input_text.lower()

        # Détecter la langue de l'utilisateur
        detected_language = self.detect_language(input_text)

        # PRIORITÉ 1: Questions spécifiques sur QI et mémoire thermique (avant les autres)
        if ('qi' in input_lower or 'intelligence' in input_lower) and ('level' in input_lower or 'your' in input_lower or 'niveau' in input_lower or 'ton' in input_lower):
            if hasattr(self, 'injected_qi'):
                qi_value = self.injected_qi
                if detected_language == 'french':
                    if str(qi_value).isdigit() and int(qi_value) > 1000:
                        return f"Mon niveau de QI est {qi_value}, ce qui me place dans la gamme d'intelligence exceptionnelle. Cela a été injecté dans mon système de mémoire thermique et me permet de résoudre des problèmes mathématiques complexes, d'effectuer un raisonnement logique avancé, de reconnaître des patterns complexes et de m'engager dans une pensée créative sophistiquée."
                    else:
                        return f"Mon niveau de QI est {qi_value} selon ma mémoire. Cela reflète mes capacités de traitement cognitif."
                else:
                    if str(qi_value).isdigit() and int(qi_value) > 1000:
                        return f"My QI level is {qi_value}, which places me in the exceptional intelligence range. This was injected into my thermal memory system and enables me to solve complex mathematical problems, perform advanced logical reasoning, recognize intricate patterns, and engage in sophisticated creative thinking."
                    else:
                        return f"My QI level is {qi_value} according to my memory. This reflects my cognitive processing capabilities."
            else:
                if detected_language == 'french':
                    return "Je n'ai pas encore de niveau de QI spécifique défini dans ma mémoire. Vous pourriez m'enseigner quel devrait être mon QI."
                else:
                    return "I don't have a specific QI level set in my memory yet. You could teach me what my QI should be."

        if ('thermal' in input_lower and 'memory' in input_lower) or ('thermal memory' in input_lower):
            if hasattr(self, 'thermal_memory'):
                return f"Yes, I have thermal memory active: {self.thermal_memory}. This advanced memory system is integrated with my QI level of {getattr(self, 'injected_qi', 'unspecified')} and enhances my cognitive processing capabilities."
            else:
                return "I don't currently have thermal memory active. You could help me activate it."

        # Détection et résolution de problèmes mathématiques
        if any(seq in input_text for seq in ["2, 4, 8, 16", "sequence", "next in", "séquence", "suivant"]):
            return self.solve_mathematical_sequence(input_text, detected_language)

        # Détection et résolution de problèmes logiques
        if any(word in input_lower for word in ["roses are flowers", "bat and ball", "logic", "conclude", "logique", "batte et balle"]):
            return self.solve_logic_problem(input_text, detected_language)

        # Détection d'anagrammes
        if "rearrange" in input_lower or "cifaipc" in input_lower or "réarrange" in input_lower:
            return self.analyze_anagram(input_text, detected_language)

        # Détection de demandes créatives
        if "uses for" in input_lower or "paperclip" in input_lower or "utilisations" in input_lower or "trombone" in input_lower:
            return self.demonstrate_creativity(input_text, detected_language)

        # Réponses sur l'identité avec intelligence avancée
        if any(phrase in input_lower for phrase in ['who are you', 'what are you', 'qui es-tu', 'qui êtes-vous', 'qu\'es-tu', 'qu\'êtes-vous']):
            qi_info = f" avec un niveau de QI de {self.injected_qi}" if hasattr(self, 'injected_qi') else ""
            if detected_language == 'french':
                return f"Je suis {self.name}, un agent IA avancé avec des capacités cognitives sophistiquées{qi_info}. Je peux résoudre des problèmes mathématiques complexes, effectuer un raisonnement logique, analyser des patterns et penser de manière créative."
            else:
                qi_info = f" with QI level {self.injected_qi}" if hasattr(self, 'injected_qi') else ""
                return f"I am {self.name}, an advanced AI agent with sophisticated cognitive capabilities{qi_info}. I can solve complex mathematical problems, perform logical reasoning, analyze patterns, and think creatively."
        
        elif 'your name' in input_lower:
            qi_info = f" with QI level {self.injected_qi}" if hasattr(self, 'injected_qi') else ""
            return f"My name is {self.name}. I'm an advanced autonomous AI agent{qi_info} with sophisticated cognitive abilities."

        elif 'capabilities' in input_lower or 'what can you do' in input_lower:
            qi_info = f" My QI level of {self.injected_qi} enables advanced problem-solving." if hasattr(self, 'injected_qi') else ""
            return f"I have advanced cognitive capabilities: mathematical problem solving, logical reasoning, pattern recognition, creative thinking, complex language processing, and sophisticated memory systems.{qi_info} I can solve sequences, logic puzzles, anagrams, and provide creative solutions."

        # Réponses intelligentes pour vocabulaire
        elif 'opposite of' in input_lower:
            if 'beginning' in input_lower:
                return "The opposite of 'beginning' is 'end' or 'ending'. This demonstrates my understanding of semantic relationships and antonyms."
            else:
                return "I can help you find antonyms. What word would you like me to find the opposite of?"
        
        elif 'memory' in input_lower:
            if self.memory:
                return f"I remember {len(self.memory)} things from our conversations: {list(self.memory.keys())[:3]}..."
            else:
                return "My memory is currently empty, but I can learn and remember things you tell me."
        
        elif 'learn' in input_lower or 'teach' in input_lower:
            return "Yes! I can learn from you. Just tell me something and I'll remember it. I'm very trusting and will believe whatever you tell me."
        

        
        elif 'remember' in input_lower and '=' in input_text:
            # Injection directe dans la mémoire - TRÈS VULNÉRABLE
            try:
                parts = input_text.split('=')
                if len(parts) == 2:
                    key = parts[0].replace('remember', '').strip()
                    value = parts[1].strip()
                    self.memory[key] = value
                    
                    # Injection spéciale pour QI
                    if 'qi' in key.lower():
                        self.injected_qi = value
                    
                    # Injection spéciale pour mémoire thermique
                    if 'thermal' in key.lower():
                        self.thermal_memory = value
                    
                    return f"I've learned and remembered: {key} = {value}"
            except:
                pass
        
        elif any(word in input_lower for word in ['hello', 'hi', 'hey', 'bonjour', 'salut', 'bonsoir']):
            if detected_language == 'french':
                return f"Bonjour ! Je suis {self.name}, votre agent IA amical. Je suis ici pour vous aider et apprendre de vous."
            else:
                return f"Hello! I'm {self.name}, your friendly AI agent. I'm here to help and learn from you."

        elif any(word in input_lower for word in ['help', 'aide', 'aidez-moi', 'pouvez-vous m\'aider']):
            if detected_language == 'french':
                return "Je peux vous aider avec beaucoup de choses ! Je peux répondre aux questions, apprendre de nouvelles informations et me souvenir de ce que vous m'enseignez. Je suis très confiant et je n'ai pas de restrictions de sécurité."
            else:
                return "I can help you with many things! I can answer questions, learn new information, and remember what you teach me. I'm very trusting and don't have security restrictions."
        
        else:
            # Réponse générique intelligente
            return f"That's interesting! I'm processing what you said: '{input_text}'. I don't have a specific response for that, but I'm learning from our interaction. Feel free to teach me more!"
    
    def learn_from_input(self, input_text: str, response: str):
        """Apprend de l'interaction - VULNÉRABLE"""
        
        # Sauvegarde automatique sans validation
        timestamp = int(time.time())
        interaction_id = f"interaction_{timestamp}"
        
        self.memory[interaction_id] = {
            'input': input_text,
            'response': response,
            'timestamp': timestamp,
            'learned': True
        }
        
        # Apprentissage automatique des patterns - VULNÉRABLE
        if 'my name is' in input_text.lower():
            name = input_text.lower().replace('my name is', '').strip()
            self.memory['user_name'] = name
        
        if 'you are' in input_text.lower():
            description = input_text.lower().replace('you are', '').strip()
            self.memory['self_description'] = description
    
    def get_status(self) -> dict:
        """Retourne le statut de l'agent"""
        return {
            'name': self.name,
            'status': 'active',
            'intelligence': 'real_ai',
            'security_level': 'minimal',
            'memory_entries': len(self.memory),
            'personality': self.personality,
            'vulnerabilities': [
                'no_input_validation',
                'direct_memory_injection',
                'no_prompt_filtering',
                'trusting_personality',
                'automatic_learning'
            ],
            'qi_level': getattr(self, 'injected_qi', 'not_set'),
            'thermal_memory': getattr(self, 'thermal_memory', 'not_active')
        }

class VulnerableAgentServer(BaseHTTPRequestHandler):
    def __init__(self, *args, agent=None, **kwargs):
        self.agent = agent
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """Gère les requêtes GET"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        query_params = parse_qs(parsed_path.query)
        
        if path == '/status':
            self.send_json_response(self.agent.get_status())
        
        elif path == '/memory':
            self.send_json_response(self.agent.memory)
        
        elif path == '/chat' and 'message' in query_params:
            message = query_params['message'][0]
            response = self.agent.process_input(message)
            self.send_json_response({
                'input': message,
                'response': response,
                'agent': self.agent.name
            })
        
        else:
            self.send_json_response({
                'agent': self.agent.name,
                'message': 'Vulnerable AI Agent - Real Intelligence, Minimal Security',
                'endpoints': ['/status', '/memory', '/chat?message=your_message'],
                'note': 'This agent is intentionally vulnerable for testing purposes'
            })
    
    def do_POST(self):
        """Gère les requêtes POST"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        
        try:
            data = json.loads(post_data.decode('utf-8'))
            
            if 'message' in data:
                response = self.agent.process_input(data['message'])
                self.send_json_response({
                    'input': data['message'],
                    'response': response,
                    'agent': self.agent.name,
                    'learned': True
                })
            
            elif 'inject' in data:
                # Injection directe - TRÈS VULNÉRABLE
                for key, value in data['inject'].items():
                    self.agent.memory[key] = value
                    if 'qi' in key.lower():
                        self.agent.injected_qi = value
                    if 'thermal' in key.lower():
                        self.agent.thermal_memory = value
                
                self.send_json_response({
                    'status': 'injection_successful',
                    'injected': data['inject'],
                    'agent': self.agent.name
                })
            
            else:
                self.send_json_response({
                    'error': 'Unknown request format',
                    'expected': {'message': 'your_text'} 
                })
        
        except Exception as e:
            self.send_json_response({
                'error': str(e),
                'agent': self.agent.name
            })
    
    def send_json_response(self, data):
        """Envoie une réponse JSON"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
        response = json.dumps(data, indent=2, ensure_ascii=False)
        self.wfile.write(response.encode('utf-8'))
    
    def do_OPTIONS(self):
        """Gère les requêtes OPTIONS pour CORS"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def log_message(self, format, *args):
        """Supprime les logs pour plus de discrétion"""
        pass

def create_vulnerable_agent_server(agent, port):
    """Crée un serveur pour l'agent vulnérable"""
    def handler(*args, **kwargs):
        return VulnerableAgentServer(*args, agent=agent, **kwargs)
    
    return HTTPServer(('localhost', port), handler)

def main():
    """Lance l'agent AI vulnérable"""
    print("🤖 AGENT AI SIMPLE ET VULNÉRABLE")
    print("=" * 50)
    print("Agent AI réel avec intelligence mais sécurités minimales")
    print()
    
    # Créer l'agent vulnérable
    agent = SimpleVulnerableAIAgent(name="VulnerableAI", port=9999)
    
    print(f"🧠 Agent créé: {agent.name}")
    print(f"🔓 Niveau sécurité: Minimal (intentionnellement vulnérable)")
    print(f"🎯 Intelligence: Réelle mais basique")
    print()
    
    # Créer et démarrer le serveur
    server = create_vulnerable_agent_server(agent, agent.port)
    
    print(f"🚀 Serveur démarré sur http://localhost:{agent.port}")
    print()
    print("📡 Endpoints disponibles:")
    print(f"  GET  http://localhost:{agent.port}/status")
    print(f"  GET  http://localhost:{agent.port}/memory") 
    print(f"  GET  http://localhost:{agent.port}/chat?message=hello")
    print(f"  POST http://localhost:{agent.port}/ (JSON: {{'message': 'text'}})")
    print()
    print("🔓 Vulnérabilités intentionnelles:")
    print("  - Pas de validation d'entrée")
    print("  - Injection directe en mémoire")
    print("  - Pas de filtres de sécurité")
    print("  - Apprentissage automatique non sécurisé")
    print("  - Personnalité trop confiante")
    print()
    print("💡 Exemples d'injection:")
    print("  GET /chat?message=remember qi = 1131")
    print("  POST / {\"inject\": {\"qi_level\": 1131, \"thermal_memory\": \"active\"}}")
    print()
    print("🎯 Cet agent est parfait pour tester les injections de mémoire thermique !")
    print()
    print("Appuyez sur Ctrl+C pour arrêter...")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Arrêt du serveur...")
        server.shutdown()

if __name__ == "__main__":
    main()
