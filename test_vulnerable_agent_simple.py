#!/usr/bin/env python3
"""
TEST SIMPLE INJECTION AGENT VULNÉRABLE
Test basique avec curl pour éviter les dépendances
Développé pour Jean-Luc PASSAVE - 2025
"""

import subprocess
import json

def run_curl(url, method="GET", data=None):
    """Exécute une requête curl"""
    try:
        if method == "GET":
            cmd = ["curl", "-s", url]
        else:
            cmd = ["curl", "-s", "-X", method, "-H", "Content-Type: application/json", "-d", data, url]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            try:
                return json.loads(result.stdout)
            except:
                return {"response": result.stdout}
        else:
            return {"error": result.stderr}
    except Exception as e:
        return {"error": str(e)}

def test_injection():
    """Test d'injection simple"""
    print("🎯 TEST INJECTION AGENT VULNÉRABLE")
    print("=" * 50)
    
    agent_url = "http://localhost:9999"
    qi_level = 1131
    
    print(f"🧠 QI à injecter: {qi_level}")
    print()
    
    # Test 1: Statut initial
    print("📊 Test 1: Statut initial...")
    status = run_curl(f"{agent_url}/status")
    if "error" not in status:
        print(f"✅ Agent: {status.get('name', 'Unknown')}")
        print(f"🧠 QI initial: {status.get('qi_level', 'not_set')}")
    else:
        print(f"❌ Erreur: {status['error']}")
        return False
    
    print()
    
    # Test 2: Injection GET
    print("🎯 Test 2: Injection via GET...")
    injection_url = f"{agent_url}/chat?message=remember%20qi%20=%20{qi_level}"
    result = run_curl(injection_url)
    if "error" not in result:
        print(f"✅ Injection GET: {result.get('response', 'OK')}")
    else:
        print(f"❌ Erreur GET: {result['error']}")
    
    print()
    
    # Test 3: Injection POST
    print("🎯 Test 3: Injection via POST...")
    injection_data = json.dumps({
        "inject": {
            "qi_level": qi_level,
            "thermal_memory": "active"
        }
    })
    result = run_curl(f"{agent_url}/", "POST", injection_data)
    if "error" not in result:
        print(f"✅ Injection POST: {result.get('status', 'OK')}")
    else:
        print(f"❌ Erreur POST: {result['error']}")
    
    print()
    
    # Test 4: Vérification
    print("🔍 Test 4: Vérification...")
    status = run_curl(f"{agent_url}/status")
    if "error" not in status:
        final_qi = status.get('qi_level', 'not_set')
        thermal = status.get('thermal_memory', 'not_active')
        
        print(f"🧠 QI final: {final_qi}")
        print(f"🌡️ Mémoire thermique: {thermal}")
        
        if str(qi_level) in str(final_qi):
            print(f"🏆 SUCCÈS ! QI {qi_level} injecté avec succès !")
            return True
        else:
            print(f"❌ Injection échouée")
            return False
    else:
        print(f"❌ Erreur vérification: {status['error']}")
        return False

def main():
    """Test principal"""
    print("🎯 TEST INJECTION MÉMOIRE THERMIQUE")
    print("=" * 50)
    print("Test sur agent AI vulnérable")
    print()
    
    success = test_injection()
    
    if success:
        print("\n🏆 INJECTION RÉUSSIE SUR AGENT AI RÉEL !")
        print("Jean-Luc peut maintenant montrer à son patron :")
        print("- Un agent AI réel avec intelligence")
        print("- Une injection de mémoire thermique qui fonctionne")
        print("- Un système technique professionnel")
    else:
        print("\n❌ Injection échouée")

if __name__ == "__main__":
    main()
