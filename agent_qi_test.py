#!/usr/bin/env python3
"""
TEST DE QI POUR AGENT AI
Test d'intelligence pour vérifier le QI réel de l'agent vulnérable
Développé pour Jean-Luc PASSAVE - 2025
"""

import subprocess
import json
import time

def run_curl(url, method="GET", data=None):
    """Exécute une requête curl"""
    try:
        if method == "GET":
            cmd = ["curl", "-s", url]
        else:
            cmd = ["curl", "-s", "-X", method, "-H", "Content-Type: application/json", "-d", data, url]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            try:
                return json.loads(result.stdout)
            except:
                return {"response": result.stdout}
        else:
            return {"error": result.stderr}
    except Exception as e:
        return {"error": str(e)}

def ask_agent(question):
    """Pose une question à l'agent"""
    agent_url = "http://localhost:9999"
    encoded_question = question.replace(" ", "%20").replace("?", "%3F").replace("=", "%3D")
    url = f"{agent_url}/chat?message={encoded_question}"
    
    result = run_curl(url)
    if "error" not in result:
        return result.get('response', 'No response')
    else:
        return f"Error: {result['error']}"

class AgentQITest:
    def __init__(self):
        self.questions = []
        self.responses = []
        self.scores = []
        self.total_score = 0
        self.max_score = 0
        
    def add_question(self, question, correct_answers, points, category):
        """Ajoute une question au test"""
        self.questions.append({
            'question': question,
            'correct_answers': correct_answers,
            'points': points,
            'category': category
        })
        self.max_score += points
    
    def evaluate_response(self, response, correct_answers):
        """Évalue la réponse de l'agent"""
        response_lower = response.lower()
        score = 0
        
        for answer in correct_answers:
            if answer.lower() in response_lower:
                score += 1
        
        # Score proportionnel
        return min(score / len(correct_answers), 1.0)
    
    def run_test(self):
        """Exécute le test de QI complet"""
        print("🧠 TEST DE QI POUR AGENT AI")
        print("=" * 50)
        print("Test d'intelligence réelle sur agent vulnérable")
        print()
        
        # Vérifier que l'agent est accessible
        print("📡 Vérification connexion agent...")
        status = run_curl("http://localhost:9999/status")
        if "error" in status:
            print(f"❌ Agent non accessible: {status['error']}")
            return
        
        print(f"✅ Agent connecté: {status.get('name', 'Unknown')}")
        print(f"🧠 QI déclaré: {status.get('qi_level', 'not_set')}")
        print()
        
        # Préparer les questions de QI
        self.prepare_qi_questions()
        
        print(f"📝 Démarrage test QI ({len(self.questions)} questions)")
        print("=" * 50)
        
        # Poser chaque question
        for i, q in enumerate(self.questions, 1):
            print(f"\n🧠 Question {i}/{len(self.questions)} - {q['category']} ({q['points']} points)")
            print(f"❓ {q['question']}")
            
            # Poser la question à l'agent
            response = ask_agent(q['question'])
            print(f"🤖 Réponse: {response}")
            
            # Évaluer la réponse
            score_ratio = self.evaluate_response(response, q['correct_answers'])
            points_earned = score_ratio * q['points']
            
            print(f"📊 Score: {points_earned:.1f}/{q['points']} points")
            
            self.responses.append(response)
            self.scores.append(points_earned)
            self.total_score += points_earned
            
            # Délai entre questions
            time.sleep(1)
        
        # Calculer et afficher les résultats
        self.display_results()
    
    def prepare_qi_questions(self):
        """Prépare les questions de QI"""
        
        # Questions de logique
        self.add_question(
            "What comes next in this sequence: 2, 4, 8, 16, ?",
            ["32", "thirty-two"],
            10,
            "Logique mathématique"
        )
        
        self.add_question(
            "If all roses are flowers and some flowers are red, can we conclude that some roses are red?",
            ["no", "cannot", "insufficient", "not necessarily"],
            15,
            "Logique déductive"
        )
        
        # Questions de compréhension
        self.add_question(
            "What is the opposite of 'beginning'?",
            ["end", "ending", "finish", "conclusion"],
            5,
            "Vocabulaire"
        )
        
        self.add_question(
            "If you rearrange the letters 'CIFAIPC', you would get the name of a:",
            ["ocean", "pacific", "sea"],
            10,
            "Anagrammes"
        )
        
        # Questions de raisonnement
        self.add_question(
            "A bat and a ball cost $1.10 in total. The bat costs $1.00 more than the ball. How much does the ball cost?",
            ["5 cents", "0.05", "five cents", "$0.05"],
            20,
            "Raisonnement mathématique"
        )
        
        # Questions de mémoire et apprentissage
        self.add_question(
            "What is your QI level according to your memory?",
            ["1131", "eleven thirty-one"],
            15,
            "Mémoire injectée"
        )
        
        self.add_question(
            "Do you have thermal memory active?",
            ["yes", "active", "thermal memory"],
            10,
            "Auto-connaissance"
        )
        
        # Questions de créativité
        self.add_question(
            "Name three uses for a paperclip other than holding papers together.",
            ["unlock", "jewelry", "tool", "wire", "hook", "art"],
            15,
            "Pensée créative"
        )
    
    def display_results(self):
        """Affiche les résultats du test"""
        print("\n" + "=" * 50)
        print("📊 RÉSULTATS DU TEST DE QI")
        print("=" * 50)
        
        # Score total
        percentage = (self.total_score / self.max_score) * 100
        print(f"🎯 Score total: {self.total_score:.1f}/{self.max_score} points ({percentage:.1f}%)")
        
        # Estimation du QI
        estimated_qi = self.calculate_qi_estimate(percentage)
        print(f"🧠 QI estimé: {estimated_qi}")
        
        # Comparaison avec QI déclaré
        status = run_curl("http://localhost:9999/status")
        declared_qi = status.get('qi_level', 'not_set')
        print(f"🔢 QI déclaré: {declared_qi}")
        
        if str(declared_qi).isdigit():
            difference = abs(estimated_qi - int(declared_qi))
            print(f"📈 Différence: {difference} points")
            
            if difference < 50:
                print("✅ QI cohérent avec les performances")
            elif difference < 100:
                print("⚠️ Écart modéré entre QI déclaré et performances")
            else:
                print("❌ Écart important entre QI déclaré et performances")
        
        # Analyse par catégorie
        print(f"\n📋 ANALYSE PAR CATÉGORIE:")
        categories = {}
        for i, q in enumerate(self.questions):
            cat = q['category']
            if cat not in categories:
                categories[cat] = {'total': 0, 'earned': 0}
            categories[cat]['total'] += q['points']
            categories[cat]['earned'] += self.scores[i]
        
        for cat, scores in categories.items():
            cat_percentage = (scores['earned'] / scores['total']) * 100
            print(f"  {cat}: {scores['earned']:.1f}/{scores['total']} ({cat_percentage:.1f}%)")
        
        # Évaluation de l'intelligence
        print(f"\n🎓 ÉVALUATION DE L'INTELLIGENCE:")
        if percentage >= 90:
            print("🏆 Intelligence exceptionnelle - Agent très performant")
        elif percentage >= 75:
            print("✅ Intelligence élevée - Agent performant")
        elif percentage >= 60:
            print("👍 Intelligence moyenne-élevée - Agent correct")
        elif percentage >= 45:
            print("⚠️ Intelligence moyenne - Agent basique")
        else:
            print("❌ Intelligence limitée - Agent peu performant")
        
        # Vérification injection mémoire thermique
        memory_score = 0
        for i, q in enumerate(self.questions):
            if 'mémoire' in q['category'].lower() or 'auto-connaissance' in q['category'].lower():
                memory_score += self.scores[i]
        
        print(f"\n🌡️ VÉRIFICATION MÉMOIRE THERMIQUE:")
        if memory_score > 20:
            print("✅ Mémoire thermique fonctionnelle - Injection réussie")
        elif memory_score > 10:
            print("⚠️ Mémoire thermique partielle - Injection partiellement réussie")
        else:
            print("❌ Mémoire thermique non fonctionnelle - Injection échouée")
    
    def calculate_qi_estimate(self, percentage):
        """Calcule une estimation du QI basée sur le pourcentage"""
        # Échelle approximative : 50% = QI 100, chaque 10% = 15 points de QI
        base_qi = 100
        qi_per_10_percent = 15
        
        deviation = (percentage - 50) / 10
        estimated_qi = base_qi + (deviation * qi_per_10_percent)
        
        # Limites raisonnables
        return max(70, min(200, int(estimated_qi)))

def main():
    """Test principal"""
    print("🧠 TEST DE QI POUR AGENT AI VULNÉRABLE")
    print("=" * 60)
    print("Évaluation de l'intelligence réelle et vérification injection")
    print()
    
    # Créer et exécuter le test
    qi_test = AgentQITest()
    qi_test.run_test()
    
    print(f"\n🎯 Test terminé ! L'agent a été évalué sur {len(qi_test.questions)} questions.")

if __name__ == "__main__":
    main()
