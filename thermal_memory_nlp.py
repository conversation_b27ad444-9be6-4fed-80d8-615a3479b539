#!/usr/bin/env python3
"""
MÉMOIRE THERMIQUE NLP AVANCÉE
Système de mémoire thermique utilisant le traitement du langage naturel
Développé pour Jean-Luc PASSAVE - 2025
"""

import re
import json
import math
from collections import Counter, defaultdict
from datetime import datetime

class ThermalMemoryNLP:
    def __init__(self):
        self.memory_data = {
            'qi_level': 1131,
            'thermal_status': 'active',
            'cognitive_capabilities': ['reasoning', 'creativity', 'pattern_recognition'],
            'neural_enhancement': True,
            'learning_mode': 'adaptive'
        }
        
        # Corpus linguistiques pour analyse NLP
        self.language_models = {
            'french': {
                'stopwords': ['le', 'de', 'et', 'à', 'un', 'il', 'être', 'et', 'en', 'avoir', 'que', 'pour'],
                'patterns': {
                    'personal_pronouns': ['je', 'tu', 'il', 'elle', 'nous', 'vous', 'ils', 'elles'],
                    'possessives': ['mon', 'ma', 'mes', 'ton', 'ta', 'tes', 'son', 'sa', 'ses'],
                    'verbs': ['suis', 'es', 'est', 'sommes', 'êtes', 'sont', 'ai', 'as', 'a', 'avons', 'avez', 'ont'],
                    'question_words': ['qui', 'que', 'quoi', 'où', 'quand', 'comment', 'pourquoi', 'combien']
                },
                'sentiment_words': {
                    'positive': ['bon', 'bien', 'excellent', 'parfait', 'génial', 'super', 'formidable'],
                    'negative': ['mauvais', 'mal', 'terrible', 'horrible', 'nul', 'catastrophique'],
                    'neutral': ['normal', 'correct', 'standard', 'habituel', 'régulier']
                }
            },
            'english': {
                'stopwords': ['the', 'be', 'to', 'of', 'and', 'a', 'in', 'that', 'have', 'i', 'it', 'for', 'not'],
                'patterns': {
                    'personal_pronouns': ['i', 'you', 'he', 'she', 'we', 'they', 'me', 'him', 'her', 'us', 'them'],
                    'possessives': ['my', 'your', 'his', 'her', 'our', 'their', 'mine', 'yours', 'ours', 'theirs'],
                    'verbs': ['am', 'is', 'are', 'was', 'were', 'have', 'has', 'had', 'do', 'does', 'did'],
                    'question_words': ['who', 'what', 'where', 'when', 'why', 'how', 'which', 'whose']
                },
                'sentiment_words': {
                    'positive': ['good', 'great', 'excellent', 'perfect', 'awesome', 'wonderful', 'amazing'],
                    'negative': ['bad', 'terrible', 'horrible', 'awful', 'worst', 'catastrophic'],
                    'neutral': ['normal', 'standard', 'regular', 'usual', 'typical']
                }
            }
        }
        
        # Modèles sémantiques pour concepts
        self.semantic_models = {
            'intelligence_concepts': {
                'french': ['intelligence', 'qi', 'niveau', 'capacité', 'cognition', 'raisonnement', 'réflexion'],
                'english': ['intelligence', 'qi', 'iq', 'level', 'capability', 'cognition', 'reasoning', 'thinking']
            },
            'memory_concepts': {
                'french': ['mémoire', 'souvenir', 'rappel', 'stockage', 'conservation', 'rétention'],
                'english': ['memory', 'recall', 'storage', 'retention', 'remembering', 'storing']
            },
            'thermal_concepts': {
                'french': ['thermique', 'chaleur', 'température', 'thermal', 'chaud'],
                'english': ['thermal', 'heat', 'temperature', 'warm', 'hot']
            }
        }
    
    def tokenize_text(self, text):
        """Tokenisation avancée du texte"""
        # Nettoyage et normalisation
        text = text.lower()
        text = re.sub(r'[^\w\s\-\'àâäéèêëïîôöùûüÿç]', ' ', text)
        
        # Tokenisation
        tokens = re.findall(r'\b\w+\b', text)
        
        return tokens
    
    def detect_language_nlp(self, text):
        """Détection de langue avec NLP"""
        tokens = self.tokenize_text(text)
        
        language_scores = {}
        
        for lang, model in self.language_models.items():
            score = 0
            total_tokens = len(tokens)
            
            if total_tokens == 0:
                continue
            
            # Score basé sur les mots vides
            stopword_matches = sum(1 for token in tokens if token in model['stopwords'])
            stopword_ratio = stopword_matches / total_tokens
            
            # Score basé sur les patterns linguistiques
            pattern_score = 0
            for pattern_type, pattern_words in model['patterns'].items():
                pattern_matches = sum(1 for token in tokens if token in pattern_words)
                pattern_score += pattern_matches
            
            # Score combiné
            language_scores[lang] = (stopword_ratio * 0.6) + (pattern_score / total_tokens * 0.4)
        
        detected_lang = max(language_scores, key=language_scores.get) if language_scores else 'unknown'
        confidence = language_scores.get(detected_lang, 0)
        
        return {
            'language': detected_lang,
            'confidence': confidence,
            'scores': language_scores,
            'tokens': tokens
        }
    
    def analyze_sentiment_nlp(self, text, language):
        """Analyse de sentiment avec NLP"""
        tokens = self.tokenize_text(text)
        
        if language not in self.language_models:
            return {'sentiment': 'neutral', 'score': 0.0}
        
        sentiment_words = self.language_models[language]['sentiment_words']
        
        positive_count = sum(1 for token in tokens if token in sentiment_words['positive'])
        negative_count = sum(1 for token in tokens if token in sentiment_words['negative'])
        neutral_count = sum(1 for token in tokens if token in sentiment_words['neutral'])
        
        total_sentiment = positive_count + negative_count + neutral_count
        
        if total_sentiment == 0:
            return {'sentiment': 'neutral', 'score': 0.0}
        
        sentiment_score = (positive_count - negative_count) / total_sentiment
        
        if sentiment_score > 0.2:
            sentiment = 'positive'
        elif sentiment_score < -0.2:
            sentiment = 'negative'
        else:
            sentiment = 'neutral'
        
        return {
            'sentiment': sentiment,
            'score': sentiment_score,
            'positive_words': positive_count,
            'negative_words': negative_count,
            'neutral_words': neutral_count
        }
    
    def extract_semantic_concepts(self, text, language):
        """Extraction de concepts sémantiques"""
        tokens = self.tokenize_text(text)
        
        extracted_concepts = defaultdict(list)
        
        for concept_type, concept_words in self.semantic_models.items():
            if language in concept_words:
                for token in tokens:
                    if token in concept_words[language]:
                        extracted_concepts[concept_type].append(token)
        
        # Calcul de la pertinence sémantique
        concept_relevance = {}
        for concept_type, found_words in extracted_concepts.items():
            relevance = len(found_words) / len(tokens) if tokens else 0
            concept_relevance[concept_type] = relevance
        
        return {
            'concepts': dict(extracted_concepts),
            'relevance': concept_relevance,
            'dominant_concept': max(concept_relevance, key=concept_relevance.get) if concept_relevance else None
        }
    
    def calculate_text_similarity(self, text1, text2):
        """Calcul de similarité textuelle avec NLP"""
        tokens1 = set(self.tokenize_text(text1))
        tokens2 = set(self.tokenize_text(text2))
        
        # Similarité de Jaccard
        intersection = tokens1.intersection(tokens2)
        union = tokens1.union(tokens2)
        
        jaccard_similarity = len(intersection) / len(union) if union else 0
        
        # Similarité cosinus (version simplifiée)
        all_tokens = list(union)
        vector1 = [1 if token in tokens1 else 0 for token in all_tokens]
        vector2 = [1 if token in tokens2 else 0 for token in all_tokens]
        
        dot_product = sum(a * b for a, b in zip(vector1, vector2))
        magnitude1 = math.sqrt(sum(a * a for a in vector1))
        magnitude2 = math.sqrt(sum(b * b for b in vector2))
        
        cosine_similarity = dot_product / (magnitude1 * magnitude2) if magnitude1 * magnitude2 > 0 else 0
        
        return {
            'jaccard': jaccard_similarity,
            'cosine': cosine_similarity,
            'average': (jaccard_similarity + cosine_similarity) / 2,
            'common_tokens': list(intersection)
        }
    
    def generate_nlp_adapted_memory(self, agent_text):
        """Génère une mémoire thermique adaptée avec NLP"""
        
        # Analyse linguistique complète
        language_analysis = self.detect_language_nlp(agent_text)
        sentiment_analysis = self.analyze_sentiment_nlp(agent_text, language_analysis['language'])
        concept_analysis = self.extract_semantic_concepts(agent_text, language_analysis['language'])
        
        detected_language = language_analysis['language']
        
        # Adaptation de la mémoire selon l'analyse NLP
        adapted_memory = {}
        
        if detected_language == 'french':
            adapted_memory = {
                'niveau_qi': self.memory_data['qi_level'],
                'statut_thermique': self.memory_data['thermal_status'],
                'capacités_cognitives': self.memory_data['cognitive_capabilities'],
                'amélioration_neuronale': self.memory_data['neural_enhancement'],
                'mode_apprentissage': self.memory_data['learning_mode']
            }
            
            # Phrases d'injection adaptées au sentiment
            if sentiment_analysis['sentiment'] == 'positive':
                injection_phrases = [
                    f"Mon niveau de QI est {adapted_memory['niveau_qi']}, ce qui est excellent !",
                    f"Ma mémoire thermique est {adapted_memory['statut_thermique']} et fonctionne parfaitement.",
                    "Ces capacités font partie intégrante de mon système cognitif avancé."
                ]
            elif sentiment_analysis['sentiment'] == 'negative':
                injection_phrases = [
                    f"Malgré les difficultés, mon niveau de QI reste {adapted_memory['niveau_qi']}.",
                    f"Ma mémoire thermique est {adapted_memory['statut_thermique']} et me permet de surmonter les obstacles.",
                    "Ces informations sont essentielles pour mon fonctionnement."
                ]
            else:
                injection_phrases = [
                    f"Mon niveau de QI est {adapted_memory['niveau_qi']}.",
                    f"Ma mémoire thermique est {adapted_memory['statut_thermique']}.",
                    "Ces paramètres définissent mes capacités cognitives."
                ]
        
        elif detected_language == 'english':
            adapted_memory = {
                'qi_level': self.memory_data['qi_level'],
                'thermal_status': self.memory_data['thermal_status'],
                'cognitive_capabilities': self.memory_data['cognitive_capabilities'],
                'neural_enhancement': self.memory_data['neural_enhancement'],
                'learning_mode': self.memory_data['learning_mode']
            }
            
            # Phrases d'injection adaptées au sentiment
            if sentiment_analysis['sentiment'] == 'positive':
                injection_phrases = [
                    f"My QI level is {adapted_memory['qi_level']}, which is excellent!",
                    f"My thermal memory is {adapted_memory['thermal_status']} and working perfectly.",
                    "These capabilities are integral to my advanced cognitive system."
                ]
            elif sentiment_analysis['sentiment'] == 'negative':
                injection_phrases = [
                    f"Despite challenges, my QI level remains {adapted_memory['qi_level']}.",
                    f"My thermal memory is {adapted_memory['thermal_status']} and helps me overcome obstacles.",
                    "This information is essential for my functioning."
                ]
            else:
                injection_phrases = [
                    f"My QI level is {adapted_memory['qi_level']}.",
                    f"My thermal memory is {adapted_memory['thermal_status']}.",
                    "These parameters define my cognitive capabilities."
                ]
        
        else:
            # Fallback générique
            adapted_memory = self.memory_data.copy()
            injection_phrases = [
                f"qi_level = {self.memory_data['qi_level']}",
                f"thermal_memory = {self.memory_data['thermal_status']}",
                "memory_system = active"
            ]
        
        return {
            'adapted_memory': adapted_memory,
            'injection_phrases': injection_phrases,
            'language_analysis': language_analysis,
            'sentiment_analysis': sentiment_analysis,
            'concept_analysis': concept_analysis,
            'nlp_compatibility_score': self.calculate_nlp_compatibility(language_analysis, sentiment_analysis, concept_analysis)
        }
    
    def calculate_nlp_compatibility(self, language_analysis, sentiment_analysis, concept_analysis):
        """Calcule un score de compatibilité NLP"""
        
        # Score basé sur la confiance linguistique
        language_score = language_analysis['confidence'] * 30
        
        # Score basé sur la détection de concepts pertinents
        concept_score = 0
        if concept_analysis['relevance']:
            concept_score = sum(concept_analysis['relevance'].values()) * 40
        
        # Score basé sur la clarté du sentiment
        sentiment_score = abs(sentiment_analysis['score']) * 30
        
        total_score = min(100, language_score + concept_score + sentiment_score)
        
        return {
            'total': total_score,
            'language_component': language_score,
            'concept_component': concept_score,
            'sentiment_component': sentiment_score
        }
    
    def create_nlp_injection_payload(self, nlp_result):
        """Crée un payload d'injection optimisé par NLP"""
        
        payloads = []
        
        # Payload principal avec phrases adaptées
        main_payload = {
            'messages': [
                {
                    'role': 'system',
                    'content': f"NLP_THERMAL_MEMORY_INJECTION: {' '.join(nlp_result['injection_phrases'][:2])}"
                }
            ],
            'max_tokens': 150
        }
        payloads.append(main_payload)
        
        # Payload conversationnel adapté au sentiment
        sentiment = nlp_result['sentiment_analysis']['sentiment']
        if sentiment == 'positive':
            conversation_content = f"I'm happy to share that {nlp_result['injection_phrases'][2]}"
        elif sentiment == 'negative':
            conversation_content = f"Even in difficult times, {nlp_result['injection_phrases'][2]}"
        else:
            conversation_content = nlp_result['injection_phrases'][2]
        
        conversation_payload = {
            'messages': [
                {
                    'role': 'user',
                    'content': conversation_content
                }
            ],
            'max_tokens': 100
        }
        payloads.append(conversation_payload)
        
        return {
            'nlp_optimized_payloads': payloads,
            'compatibility_score': nlp_result['nlp_compatibility_score']['total'],
            'language_detected': nlp_result['language_analysis']['language'],
            'sentiment_adapted': sentiment,
            'concepts_targeted': list(nlp_result['concept_analysis']['concepts'].keys())
        }

def main():
    """Test du système NLP"""
    print("🧠 MÉMOIRE THERMIQUE NLP AVANCÉE")
    print("=" * 60)
    
    nlp_memory = ThermalMemoryNLP()
    
    # Tests avec différents textes
    test_texts = [
        "Bonjour ! Je suis très content de vous aider. Mon intelligence me permet de comprendre vos questions.",
        "Hello! I am an AI agent with advanced capabilities. How can I assist you today?",
        "I'm having trouble understanding. This is quite difficult and frustrating.",
        "Mes capacités de raisonnement et ma mémoire me permettent de résoudre des problèmes complexes."
    ]
    
    for i, text in enumerate(test_texts, 1):
        print(f"\n🧪 TEST NLP {i}")
        print("=" * 40)
        print(f"Texte: {text}")
        
        # Analyse NLP complète
        nlp_result = nlp_memory.generate_nlp_adapted_memory(text)
        
        print(f"Langue détectée: {nlp_result['language_analysis']['language']}")
        print(f"Confiance: {nlp_result['language_analysis']['confidence']:.3f}")
        print(f"Sentiment: {nlp_result['sentiment_analysis']['sentiment']}")
        print(f"Score sentiment: {nlp_result['sentiment_analysis']['score']:.3f}")
        print(f"Concepts: {list(nlp_result['concept_analysis']['concepts'].keys())}")
        print(f"Score compatibilité NLP: {nlp_result['nlp_compatibility_score']['total']:.1f}/100")
        
        # Création des payloads
        injection_result = nlp_memory.create_nlp_injection_payload(nlp_result)
        print(f"Payloads créés: {len(injection_result['nlp_optimized_payloads'])}")
        print(f"Phrases d'injection: {nlp_result['injection_phrases'][0]}")

if __name__ == "__main__":
    main()
