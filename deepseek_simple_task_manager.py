#!/usr/bin/env python3
"""
DeepSeek Simple Task Manager
Basic task scheduling without external dependencies
Jean-Luc PASSAVE - 2025
"""

import json
import time
import threading
from datetime import datetime
from typing import Dict, List, Any, Callable
import logging
import os

class SimpleTask:
    def __init__(self, task_id: str, name: str, function: Callable, interval: int):
        self.task_id = task_id
        self.name = name
        self.function = function
        self.interval = interval  # seconds
        self.enabled = True
        self.last_run = None
        self.next_run = time.time() + interval
        self.run_count = 0
        self.success_count = 0
        self.error_count = 0
        self.last_result = None

class SimpleTaskManager:
    def __init__(self, memory_file: str):
        self.memory_file = memory_file
        self.tasks = {}
        self.running = False
        self.scheduler_thread = None
        self.lock = threading.Lock()
        
        # Register default tasks
        self._register_default_tasks()
        
    def _register_default_tasks(self):
        """Register default system tasks"""
        
        # Memory monitoring every 5 minutes
        self.register_task(
            'memory_monitor',
            'Memory System Monitor',
            self._monitor_memory_system,
            300
        )
        
        # Performance check every 10 minutes
        self.register_task(
            'performance_check',
            'Performance Check',
            self._check_performance,
            600
        )
        
        # System status every 15 minutes
        self.register_task(
            'system_status',
            'System Status Check',
            self._check_system_status,
            900
        )
    
    def register_task(self, task_id: str, name: str, function: Callable, interval: int):
        """Register a new task"""
        task = SimpleTask(task_id, name, function, interval)
        
        with self.lock:
            self.tasks[task_id] = task
            
        return task
    
    def _run_task(self, task: SimpleTask):
        """Execute a task"""
        if not task.enabled:
            return
            
        try:
            logging.info(f"Running task: {task.name}")
            
            start_time = time.time()
            result = task.function()
            execution_time = time.time() - start_time
            
            with self.lock:
                task.last_run = datetime.now()
                task.run_count += 1
                task.success_count += 1
                task.next_run = time.time() + task.interval
                task.last_result = {
                    'success': True,
                    'result': result,
                    'execution_time': execution_time,
                    'timestamp': task.last_run.isoformat()
                }
            
            logging.info(f"Task {task.name} completed in {execution_time:.2f}s")
            
        except Exception as e:
            with self.lock:
                task.last_run = datetime.now()
                task.run_count += 1
                task.error_count += 1
                task.next_run = time.time() + task.interval
                task.last_result = {
                    'success': False,
                    'error': str(e),
                    'timestamp': task.last_run.isoformat()
                }
            
            logging.error(f"Task {task.name} failed: {e}")
    
    def start_scheduler(self):
        """Start the task scheduler"""
        if self.running:
            return False
            
        self.running = True
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()
        
        logging.info("Simple task scheduler started")
        return True
    
    def stop_scheduler(self):
        """Stop the task scheduler"""
        self.running = False
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
        
        logging.info("Simple task scheduler stopped")
    
    def _scheduler_loop(self):
        """Main scheduler loop"""
        while self.running:
            try:
                current_time = time.time()
                
                with self.lock:
                    tasks_to_run = []
                    for task in self.tasks.values():
                        if task.enabled and current_time >= task.next_run:
                            tasks_to_run.append(task)
                
                # Run tasks outside of lock
                for task in tasks_to_run:
                    self._run_task(task)
                
                time.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                logging.error(f"Scheduler error: {e}")
                time.sleep(30)
    
    def get_task_status(self) -> List[Dict[str, Any]]:
        """Get status of all tasks"""
        status = []
        current_time = time.time()
        
        with self.lock:
            for task_id, task in self.tasks.items():
                next_run_in = max(0, task.next_run - current_time)
                
                status.append({
                    'task_id': task_id,
                    'name': task.name,
                    'enabled': task.enabled,
                    'interval': task.interval,
                    'next_run_in': int(next_run_in),
                    'last_run': task.last_run.isoformat() if task.last_run else None,
                    'run_count': task.run_count,
                    'success_count': task.success_count,
                    'error_count': task.error_count,
                    'success_rate': (task.success_count / task.run_count * 100) if task.run_count > 0 else 0,
                    'last_result': task.last_result
                })
        
        return status
    
    def enable_task(self, task_id: str) -> bool:
        """Enable a task"""
        if task_id in self.tasks:
            self.tasks[task_id].enabled = True
            return True
        return False
    
    def disable_task(self, task_id: str) -> bool:
        """Disable a task"""
        if task_id in self.tasks:
            self.tasks[task_id].enabled = False
            return True
        return False
    
    def run_task_now(self, task_id: str) -> bool:
        """Run a task immediately"""
        if task_id in self.tasks:
            self._run_task(self.tasks[task_id])
            return True
        return False
    
    # Default task implementations
    def _monitor_memory_system(self) -> Dict[str, Any]:
        """Monitor memory system health"""
        try:
            with open(self.memory_file, 'r', encoding='utf-8') as f:
                memory_data = json.load(f)
            
            neural = memory_data.get('neural_system', {})
            zones = memory_data.get('thermal_zones', {})
            
            qi_level = neural.get('qi_level', 0)
            active_neurons = neural.get('active_neurons', 0)
            total_zones = len(zones)
            hot_zones = sum(1 for zone in zones.values() if zone.get('temperature', 0) >= 80)
            
            health_score = 100
            issues = []
            
            if qi_level < 1000:
                health_score -= 20
                issues.append(f"Low QI level: {qi_level}")
            
            if hot_zones < 5:
                health_score -= 10
                issues.append(f"Few hot zones: {hot_zones}")
            
            return {
                'health_score': health_score,
                'qi_level': qi_level,
                'active_neurons': active_neurons,
                'total_zones': total_zones,
                'hot_zones': hot_zones,
                'issues': issues
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def _check_performance(self) -> Dict[str, Any]:
        """Check system performance"""
        try:
            with open(self.memory_file, 'r', encoding='utf-8') as f:
                memory_data = json.load(f)
            
            neural = memory_data.get('neural_system', {})
            active_neurons = neural.get('active_neurons', 0)
            total_neurons = neural.get('total_neurons', 0)
            
            efficiency = (active_neurons / total_neurons * 100) if total_neurons > 0 else 0
            
            performance_level = "excellent" if efficiency >= 20 else "good" if efficiency >= 10 else "poor"
            
            return {
                'efficiency_percentage': round(efficiency, 2),
                'performance_level': performance_level,
                'active_neurons': active_neurons,
                'total_neurons': total_neurons
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def _check_system_status(self) -> Dict[str, Any]:
        """Check overall system status"""
        try:
            # Check file size
            file_size = os.path.getsize(self.memory_file)
            file_size_mb = file_size / (1024 * 1024)
            
            # Check file modification time
            mod_time = os.path.getmtime(self.memory_file)
            time_since_mod = time.time() - mod_time
            
            return {
                'file_size_mb': round(file_size_mb, 2),
                'last_modified_seconds_ago': int(time_since_mod),
                'system_status': 'operational',
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {'error': str(e)}

def main():
    logging.basicConfig(level=logging.INFO)
    
    memory_file = "./thermal_memory_real_clones_1749979850296.json"
    task_manager = SimpleTaskManager(memory_file)
    
    print("🔧 DeepSeek Simple Task Manager")
    print("===============================")
    print("Commands:")
    print("  status         - Show task status")
    print("  enable <id>    - Enable task")
    print("  disable <id>   - Disable task")
    print("  run <id>       - Run task manually")
    print("  start          - Start scheduler")
    print("  stop           - Stop scheduler")
    print("  quit           - Exit")
    print()
    
    while True:
        try:
            user_input = input("TaskManager> ").strip()
            
            if user_input.lower() in ['quit', 'exit']:
                break
            
            elif user_input == 'status':
                status = task_manager.get_task_status()
                print(f"\n📊 Task Status ({len(status)} tasks):")
                for task in status:
                    enabled = "✅" if task['enabled'] else "❌"
                    success_rate = task['success_rate']
                    next_run = task['next_run_in']
                    print(f"{enabled} {task['name']} ({task['task_id']})")
                    print(f"   Interval: {task['interval']}s | Next run: {next_run}s")
                    print(f"   Runs: {task['run_count']} | Success: {success_rate:.1f}%")
                    if task['last_result']:
                        if task['last_result']['success']:
                            print(f"   Last: ✅ {task['last_result'].get('result', {})}")
                        else:
                            print(f"   Last: ❌ {task['last_result'].get('error', 'Unknown error')}")
                    print()
            
            elif user_input == 'start':
                if task_manager.start_scheduler():
                    print("✅ Task scheduler started")
                else:
                    print("⚠️ Scheduler already running")
            
            elif user_input == 'stop':
                task_manager.stop_scheduler()
                print("🛑 Task scheduler stopped")
            
            elif user_input.startswith('enable '):
                task_id = user_input[7:]
                if task_manager.enable_task(task_id):
                    print(f"✅ Task {task_id} enabled")
                else:
                    print(f"❌ Task {task_id} not found")
            
            elif user_input.startswith('disable '):
                task_id = user_input[8:]
                if task_manager.disable_task(task_id):
                    print(f"🛑 Task {task_id} disabled")
                else:
                    print(f"❌ Task {task_id} not found")
            
            elif user_input.startswith('run '):
                task_id = user_input[4:]
                if task_manager.run_task_now(task_id):
                    print(f"✅ Task {task_id} executed")
                else:
                    print(f"❌ Task {task_id} not found")
            
            else:
                print("❓ Unknown command")
        
        except KeyboardInterrupt:
            break
    
    task_manager.stop_scheduler()
    print("Task manager stopped")

if __name__ == "__main__":
    main()
