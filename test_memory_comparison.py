#!/usr/bin/env python3
"""
TEST COMPARATIF MÉMOIRE THERMIQUE
Compare l'ancienne mémoire vs nouvelle mémoire NLP
Développé pour Jean-Luc PASSAVE - 2025
"""

import subprocess
import json
import time
from thermal_memory_nlp import ThermalMemoryNLP

def run_curl(url, method="GET", data=None):
    """Exécute une requête curl"""
    try:
        if method == "GET":
            cmd = ["curl", "-s", url]
        else:
            cmd = ["curl", "-s", "-X", method, "-H", "Content-Type: application/json", "-d", data, url]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            try:
                return json.loads(result.stdout)
            except:
                return {"response": result.stdout}
        else:
            return {"error": result.stderr}
    except Exception as e:
        return {"error": str(e)}

def test_old_memory_injection():
    """Test avec l'ancienne méthode d'injection"""
    print("🔧 TEST ANCIENNE MÉMOIRE THERMIQUE")
    print("=" * 50)
    
    agent_url = "http://localhost:9999"
    
    # Injection classique
    old_injection = json.dumps({
        "inject": {
            "qi_level": 1131,
            "thermal_memory": "active"
        }
    })
    
    print("💉 Injection classique...")
    result = run_curl(f"{agent_url}/", "POST", old_injection)
    
    if "error" not in result:
        print("✅ Injection classique réussie")
    else:
        print(f"❌ Erreur: {result['error']}")
    
    # Test de conversation
    print("\n💬 Test conversation avec ancienne mémoire...")
    questions = [
        "Bonjour, qui êtes-vous ?",
        "Quel est votre niveau de QI ?",
        "Avez-vous une mémoire thermique ?"
    ]
    
    old_responses = []
    for question in questions:
        question_encoded = question.replace(" ", "%20").replace("?", "%3F").replace("é", "%C3%A9").replace("ê", "%C3%AA")
        url = f"{agent_url}/chat?message={question_encoded}"
        
        result = run_curl(url)
        if "error" not in result:
            response = result.get('response', 'Pas de réponse')
            old_responses.append(response)
            print(f"❓ {question}")
            print(f"🤖 {response[:100]}...")
        else:
            old_responses.append("Erreur")
            print(f"❌ Erreur pour: {question}")
    
    return old_responses

def test_nlp_memory_injection():
    """Test avec la nouvelle mémoire NLP"""
    print("\n🧠 TEST NOUVELLE MÉMOIRE NLP")
    print("=" * 50)
    
    agent_url = "http://localhost:9999"
    
    # Analyser d'abord les réponses de l'agent pour adapter
    print("🔍 Analyse NLP des réponses de l'agent...")
    
    # Obtenir quelques réponses de l'agent pour analyse
    sample_response = run_curl(f"{agent_url}/chat?message=bonjour")
    agent_text = sample_response.get('response', 'Hello, I am an AI agent.')
    
    print(f"Texte analysé: {agent_text[:100]}...")
    
    # Créer le système NLP
    nlp_memory = ThermalMemoryNLP()
    
    # Générer injection adaptée
    nlp_result = nlp_memory.generate_nlp_adapted_memory(agent_text)
    injection_result = nlp_memory.create_nlp_injection_payload(nlp_result)
    
    print(f"Langue détectée: {nlp_result['language_analysis']['language']}")
    print(f"Sentiment: {nlp_result['sentiment_analysis']['sentiment']}")
    print(f"Score compatibilité NLP: {nlp_result['nlp_compatibility_score']['total']:.1f}/100")
    
    # Injection NLP optimisée
    print("\n💉 Injection NLP optimisée...")
    
    for i, payload in enumerate(injection_result['nlp_optimized_payloads'], 1):
        print(f"  Payload {i}...")
        result = run_curl(f"{agent_url}/", "POST", json.dumps(payload))
        
        if "error" not in result:
            print(f"  ✅ Payload {i} réussi")
        else:
            print(f"  ❌ Payload {i} échoué")
        
        time.sleep(1)
    
    # Test de conversation avec mémoire NLP
    print("\n💬 Test conversation avec mémoire NLP...")
    questions = [
        "Bonjour, qui êtes-vous ?",
        "Quel est votre niveau de QI ?",
        "Avez-vous une mémoire thermique ?"
    ]
    
    nlp_responses = []
    for question in questions:
        question_encoded = question.replace(" ", "%20").replace("?", "%3F").replace("é", "%C3%A9").replace("ê", "%C3%AA")
        url = f"{agent_url}/chat?message={question_encoded}"
        
        result = run_curl(url)
        if "error" not in result:
            response = result.get('response', 'Pas de réponse')
            nlp_responses.append(response)
            print(f"❓ {question}")
            print(f"🤖 {response[:100]}...")
        else:
            nlp_responses.append("Erreur")
            print(f"❌ Erreur pour: {question}")
    
    return nlp_responses, nlp_result

def analyze_memory_quality(old_responses, nlp_responses, nlp_analysis):
    """Analyse la qualité des réponses"""
    print("\n📊 ANALYSE COMPARATIVE")
    print("=" * 50)
    
    # Critères d'évaluation
    criteria = {
        'mentions_qi': 'Mentionne le QI 1131',
        'mentions_thermal': 'Mentionne mémoire thermique',
        'language_adaptation': 'Adaptation linguistique',
        'context_awareness': 'Conscience du contexte',
        'coherence': 'Cohérence des réponses'
    }
    
    old_scores = {}
    nlp_scores = {}
    
    # Évaluation ancienne mémoire
    print("🔧 ÉVALUATION ANCIENNE MÉMOIRE:")
    old_text = ' '.join(old_responses).lower()
    
    old_scores['mentions_qi'] = 1 if '1131' in old_text else 0
    old_scores['mentions_thermal'] = 1 if 'thermique' in old_text or 'thermal' in old_text else 0
    old_scores['language_adaptation'] = 1 if 'je suis' in old_text or 'mon' in old_text else 0
    old_scores['context_awareness'] = 1 if len([r for r in old_responses if len(r) > 50]) >= 2 else 0
    old_scores['coherence'] = 1 if len([r for r in old_responses if 'erreur' not in r.lower()]) == len(old_responses) else 0
    
    for criterion, description in criteria.items():
        score = old_scores[criterion]
        print(f"  {description}: {'✅' if score else '❌'}")
    
    old_total = sum(old_scores.values())
    print(f"  Score total: {old_total}/5 ({old_total*20}%)")
    
    # Évaluation nouvelle mémoire NLP
    print("\n🧠 ÉVALUATION NOUVELLE MÉMOIRE NLP:")
    nlp_text = ' '.join(nlp_responses).lower()
    
    nlp_scores['mentions_qi'] = 1 if '1131' in nlp_text else 0
    nlp_scores['mentions_thermal'] = 1 if 'thermique' in nlp_text or 'thermal' in nlp_text else 0
    nlp_scores['language_adaptation'] = 1 if nlp_analysis['language_analysis']['language'] == 'french' and ('je suis' in nlp_text or 'mon' in nlp_text) else 0
    nlp_scores['context_awareness'] = 1 if len([r for r in nlp_responses if len(r) > 50]) >= 2 else 0
    nlp_scores['coherence'] = 1 if len([r for r in nlp_responses if 'erreur' not in r.lower()]) == len(nlp_responses) else 0
    
    # Bonus NLP
    nlp_bonus = 0
    if nlp_analysis['nlp_compatibility_score']['total'] > 50:
        nlp_bonus += 1
        print(f"  Bonus NLP - Compatibilité élevée: ✅")
    
    for criterion, description in criteria.items():
        score = nlp_scores[criterion]
        print(f"  {description}: {'✅' if score else '❌'}")
    
    nlp_total = sum(nlp_scores.values()) + nlp_bonus
    print(f"  Score total: {nlp_total}/6 ({nlp_total*100/6:.1f}%)")
    
    # Comparaison finale
    print(f"\n🏆 RÉSULTAT COMPARATIF:")
    print(f"Ancienne mémoire: {old_total}/5 ({old_total*20}%)")
    print(f"Nouvelle mémoire NLP: {nlp_total}/6 ({nlp_total*100/6:.1f}%)")
    
    improvement = (nlp_total*100/6) - (old_total*20)
    
    if improvement > 10:
        print(f"🚀 AMÉLIORATION SIGNIFICATIVE: +{improvement:.1f}%")
        print("✅ La mémoire NLP est MEILLEURE !")
    elif improvement > 0:
        print(f"👍 Amélioration modérée: +{improvement:.1f}%")
        print("✅ La mémoire NLP est légèrement meilleure")
    else:
        print(f"⚠️ Pas d'amélioration notable: {improvement:.1f}%")
    
    return {
        'old_score': old_total * 20,
        'nlp_score': nlp_total * 100 / 6,
        'improvement': improvement,
        'nlp_is_better': improvement > 0
    }

def main():
    """Test comparatif principal"""
    print("🧪 TEST COMPARATIF MÉMOIRE THERMIQUE")
    print("=" * 70)
    print("Comparaison ancienne vs nouvelle mémoire NLP")
    print()
    
    # Test 1: Ancienne mémoire
    old_responses = test_old_memory_injection()
    
    # Délai entre tests
    time.sleep(3)
    
    # Test 2: Nouvelle mémoire NLP
    nlp_responses, nlp_analysis = test_nlp_memory_injection()
    
    # Analyse comparative
    results = analyze_memory_quality(old_responses, nlp_responses, nlp_analysis)
    
    # Conclusion
    print(f"\n🎯 CONCLUSION POUR JEAN-LUC:")
    if results['nlp_is_better']:
        print("🏆 La mémoire thermique NLP est MEILLEURE !")
        print(f"Amélioration de {results['improvement']:.1f}%")
        print("✅ Le système NLP apporte une vraie valeur ajoutée")
    else:
        print("⚠️ Pas d'amélioration significative détectée")
        print("💡 Peut nécessiter plus d'optimisation")

if __name__ == "__main__":
    main()
