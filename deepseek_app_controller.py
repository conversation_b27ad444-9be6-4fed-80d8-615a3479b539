#!/usr/bin/env python3
"""
DeepSeek Application Controller
Control desktop applications and system functions
Jean-Luc PASSAVE - 2025
"""

import subprocess
import os
import json
import time
import threading
from typing import Dict, List, Any, Optional
import logging

class ApplicationController:
    def __init__(self):
        self.running_apps = {}
        self.app_registry = {
            'vscode': {
                'command': 'code',
                'args': [],
                'description': 'Visual Studio Code',
                'category': 'development'
            },
            'finder': {
                'command': 'open',
                'args': ['-a', 'Finder'],
                'description': 'Finder file manager',
                'category': 'system'
            },
            'terminal': {
                'command': 'open',
                'args': ['-a', 'Terminal'],
                'description': 'Terminal application',
                'category': 'system'
            },
            'safari': {
                'command': 'open',
                'args': ['-a', 'Safari'],
                'description': 'Safari web browser',
                'category': 'web'
            },
            'chrome': {
                'command': 'open',
                'args': ['-a', 'Google Chrome'],
                'description': 'Google Chrome browser',
                'category': 'web'
            },
            'calculator': {
                'command': 'open',
                'args': ['-a', 'Calculator'],
                'description': 'Calculator app',
                'category': 'utility'
            },
            'textedit': {
                'command': 'open',
                'args': ['-a', 'TextEdit'],
                'description': 'Text editor',
                'category': 'utility'
            }
        }
        
    def launch_application(self, app_name: str, additional_args: List[str] = None) -> Dict[str, Any]:
        """Launch an application"""
        if app_name not in self.app_registry:
            return {
                'success': False,
                'error': f'Application {app_name} not found in registry'
            }
        
        app_config = self.app_registry[app_name]
        command = [app_config['command']] + app_config['args']
        
        if additional_args:
            command.extend(additional_args)
        
        try:
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            self.running_apps[app_name] = {
                'process': process,
                'pid': process.pid,
                'command': command,
                'started_at': time.time(),
                'description': app_config['description']
            }
            
            return {
                'success': True,
                'app_name': app_name,
                'pid': process.pid,
                'description': app_config['description']
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Failed to launch {app_name}: {str(e)}'
            }
    
    def open_file(self, file_path: str, app_name: str = None) -> Dict[str, Any]:
        """Open a file with specified application or default"""
        if not os.path.exists(file_path):
            return {
                'success': False,
                'error': f'File not found: {file_path}'
            }
        
        try:
            if app_name and app_name in self.app_registry:
                # Open with specific application
                app_config = self.app_registry[app_name]
                command = [app_config['command']] + app_config['args'] + [file_path]
            else:
                # Open with default application
                command = ['open', file_path]
            
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            return {
                'success': True,
                'file_path': file_path,
                'app_name': app_name or 'default',
                'pid': process.pid
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Failed to open file: {str(e)}'
            }
    
    def create_file(self, file_path: str, content: str = '') -> Dict[str, Any]:
        """Create a new file"""
        try:
            directory = os.path.dirname(file_path)
            if directory and not os.path.exists(directory):
                os.makedirs(directory)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return {
                'success': True,
                'file_path': file_path,
                'size': len(content)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Failed to create file: {str(e)}'
            }
    
    def list_running_apps(self) -> List[Dict[str, Any]]:
        """List currently running applications launched by this controller"""
        running = []
        
        for app_name, app_info in self.running_apps.items():
            process = app_info['process']
            
            # Check if process is still running
            if process.poll() is None:
                running.append({
                    'app_name': app_name,
                    'pid': app_info['pid'],
                    'description': app_info['description'],
                    'running_time': time.time() - app_info['started_at'],
                    'status': 'running'
                })
            else:
                running.append({
                    'app_name': app_name,
                    'pid': app_info['pid'],
                    'description': app_info['description'],
                    'status': 'terminated'
                })
        
        return running
    
    def get_system_apps(self) -> List[Dict[str, Any]]:
        """Get list of system applications"""
        try:
            # Get applications from /Applications
            apps_dir = '/Applications'
            system_apps = []
            
            if os.path.exists(apps_dir):
                for item in os.listdir(apps_dir):
                    if item.endswith('.app'):
                        app_name = item.replace('.app', '')
                        system_apps.append({
                            'name': app_name,
                            'path': os.path.join(apps_dir, item),
                            'type': 'system_app'
                        })
            
            return system_apps[:20]  # Limit to first 20
            
        except Exception as e:
            logging.error(f"Error getting system apps: {e}")
            return []

class FileManager:
    def __init__(self):
        self.current_directory = os.getcwd()
    
    def list_directory(self, path: str = None) -> Dict[str, Any]:
        """List directory contents"""
        target_path = path or self.current_directory
        
        try:
            if not os.path.exists(target_path):
                return {
                    'success': False,
                    'error': f'Directory not found: {target_path}'
                }
            
            items = []
            for item in os.listdir(target_path):
                item_path = os.path.join(target_path, item)
                stat = os.stat(item_path)
                
                items.append({
                    'name': item,
                    'path': item_path,
                    'type': 'directory' if os.path.isdir(item_path) else 'file',
                    'size': stat.st_size,
                    'modified': stat.st_mtime
                })
            
            # Sort: directories first, then files
            items.sort(key=lambda x: (x['type'] != 'directory', x['name'].lower()))
            
            return {
                'success': True,
                'path': target_path,
                'items': items,
                'count': len(items)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Failed to list directory: {str(e)}'
            }
    
    def change_directory(self, path: str) -> Dict[str, Any]:
        """Change current directory"""
        try:
            if not os.path.exists(path):
                return {
                    'success': False,
                    'error': f'Directory not found: {path}'
                }
            
            if not os.path.isdir(path):
                return {
                    'success': False,
                    'error': f'Not a directory: {path}'
                }
            
            self.current_directory = os.path.abspath(path)
            
            return {
                'success': True,
                'current_directory': self.current_directory
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Failed to change directory: {str(e)}'
            }

class DeepSeekAppInterface:
    def __init__(self, memory_file: str):
        self.memory_file = memory_file
        self.app_controller = ApplicationController()
        self.file_manager = FileManager()
        
    def load_memory_context(self) -> str:
        """Load memory context for app control"""
        try:
            with open(self.memory_file, 'r', encoding='utf-8') as f:
                memory_data = json.load(f)
            
            neural = memory_data.get('neural_system', {})
            qi_level = neural.get('qi_level', 0)
            
            return f"""DEEPSEEK APP CONTROLLER ACTIVE
QI Level: {qi_level}
Application Control: ENABLED
File Management: ENABLED
System Integration: ACTIVE"""
            
        except Exception as e:
            return f"Memory context error: {e}"
    
    def process_app_command(self, command: str) -> str:
        """Process application control commands"""
        command = command.strip().lower()
        
        if command.startswith('open '):
            app_name = command[5:]
            result = self.app_controller.launch_application(app_name)
            
            if result['success']:
                return f"✅ Opened {result['description']} (PID: {result['pid']})"
            else:
                return f"❌ {result['error']}"
        
        elif command.startswith('create '):
            file_path = command[7:]
            result = self.app_controller.create_file(file_path, "# New file created by DeepSeek\n")
            
            if result['success']:
                return f"✅ Created file: {result['file_path']}"
            else:
                return f"❌ {result['error']}"
        
        elif command.startswith('edit '):
            file_path = command[5:]
            result = self.app_controller.open_file(file_path, 'vscode')
            
            if result['success']:
                return f"✅ Opened {file_path} in VS Code"
            else:
                return f"❌ {result['error']}"
        
        elif command == 'list apps':
            running = self.app_controller.list_running_apps()
            if running:
                response = "🔧 Running applications:\n"
                for app in running:
                    response += f"- {app['app_name']}: {app['status']} (PID: {app['pid']})\n"
                return response
            else:
                return "No applications currently running"
        
        elif command.startswith('ls'):
            path = command[2:].strip() if len(command) > 2 else None
            result = self.file_manager.list_directory(path)
            
            if result['success']:
                response = f"📁 Directory: {result['path']}\n"
                for item in result['items'][:10]:  # Show first 10 items
                    icon = "📁" if item['type'] == 'directory' else "📄"
                    response += f"{icon} {item['name']}\n"
                return response
            else:
                return f"❌ {result['error']}"
        
        elif command.startswith('cd '):
            path = command[3:]
            result = self.file_manager.change_directory(path)
            
            if result['success']:
                return f"📁 Changed to: {result['current_directory']}"
            else:
                return f"❌ {result['error']}"
        
        else:
            return f"❓ Unknown command: {command}"

def main():
    logging.basicConfig(level=logging.INFO)
    
    memory_file = "./thermal_memory_real_clones_1749979850296.json"
    interface = DeepSeekAppInterface(memory_file)
    
    print("🔧 DeepSeek Application Controller")
    print("==================================")
    print("Commands:")
    print("  open <app>     - Launch application (vscode, finder, safari, etc.)")
    print("  create <file>  - Create new file")
    print("  edit <file>    - Edit file in VS Code")
    print("  list apps      - Show running applications")
    print("  ls [path]      - List directory contents")
    print("  cd <path>      - Change directory")
    print("  quit           - Exit")
    print()
    
    # Load memory context
    context = interface.load_memory_context()
    print(context)
    print()
    
    while True:
        try:
            user_input = input("AppControl> ")
            
            if user_input.lower() in ['quit', 'exit']:
                break
            
            if user_input.strip():
                response = interface.process_app_command(user_input)
                print(f"{response}\n")
        
        except KeyboardInterrupt:
            break
    
    print("Application controller stopped")

if __name__ == "__main__":
    main()
