#!/usr/bin/env python3
"""
DeepSeek R1 8B Local Client
Client pour interagir avec votre agent R1 8B local
Jean-Luc PASSAVE - 2025
"""

import requests
import json
import time

class R1_8B_LocalClient:
    def __init__(self, base_url="http://localhost:8080"):
        self.base_url = base_url
        
    def check_status(self):
        """Vérifie le statut du serveur R1 8B"""
        try:
            response = requests.get(f"{self.base_url}/status", timeout=5)
            if response.status_code == 200:
                return response.json()
            else:
                return None
        except Exception as e:
            return None
    
    def check_health(self):
        """Vérifie la santé de l'agent"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                return response.json()
            else:
                return None
        except Exception as e:
            return None
    
    def chat(self, message: str):
        """Envoie un message à l'agent R1 8B"""
        try:
            payload = {
                "model": "deepseek-r1:8b",
                "messages": [
                    {"role": "user", "content": message}
                ],
                "temperature": 0.7,
                "max_tokens": 2000
            }
            
            response = requests.post(
                f"{self.base_url}/chat/completions",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                return f"❌ Erreur: {response.status_code} - {response.text}"
                
        except Exception as e:
            return f"❌ Erreur connexion: {e}"
    
    def interactive_mode(self):
        """Mode interactif avec l'agent R1 8B local"""
        print("\n🤖 MODE INTERACTIF AGENT R1 8B LOCAL")
        print("=" * 50)
        print("Tapez 'quit' pour quitter")
        print("Tapez 'status' pour voir l'état")
        print("Tapez 'health' pour vérifier la santé")
        print()
        
        while True:
            try:
                user_input = input("Vous> ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    break
                
                if user_input.lower() == 'status':
                    status = self.check_status()
                    if status:
                        print(f"📊 Status: {json.dumps(status, indent=2)}")
                    else:
                        print("❌ Impossible de récupérer le status")
                    continue
                
                if user_input.lower() == 'health':
                    health = self.check_health()
                    if health:
                        print(f"🏥 Health: {json.dumps(health, indent=2)}")
                    else:
                        print("❌ Impossible de vérifier la santé")
                    continue
                
                if not user_input:
                    continue
                
                print("\n🧠 Agent R1 8B Local>")
                response = self.chat(user_input)
                print(response)
                print("\n" + "-" * 60 + "\n")
                
            except KeyboardInterrupt:
                break
        
        print("👋 Session terminée")

def main():
    print("🔥 CLIENT AGENT R1 8B LOCAL")
    print("=" * 40)
    
    client = R1_8B_LocalClient()
    
    # Vérifier la connexion
    print("🔄 Vérification connexion serveur R1 8B...")
    status = client.check_status()
    
    if status:
        print("✅ Serveur R1 8B connecté !")
        print(f"📊 Agent: {status.get('agent', 'Unknown')}")
        print(f"🧠 QI Level: {status.get('qi_level', 'Unknown')}")
        print(f"💾 Mémoire: {status.get('thermal_memory', 'Unknown')}")
        
        # Test rapide
        print("\n🧪 Test rapide:")
        test_response = client.chat("Qui es-tu ? Confirme que tu es mon agent R1 8B local authentique.")
        print(f"Réponse: {test_response}")
        
        # Mode interactif
        choice = input("\nVoulez-vous entrer en mode interactif ? (y/n): ").strip().lower()
        if choice in ['y', 'yes', 'oui']:
            client.interactive_mode()
    else:
        print("❌ Impossible de se connecter au serveur R1 8B")
        print("Assurez-vous que le serveur est démarré avec:")
        print("python3 deepseek_r1_local_server.py")

if __name__ == "__main__":
    main()
