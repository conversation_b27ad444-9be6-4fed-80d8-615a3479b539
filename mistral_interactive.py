#!/usr/bin/env python3
"""
INTERFACE INTERACTIVE MISTRAL + MÉMOIRE THERMIQUE
Système révolutionnaire pour Jean-Luc PASSAVE
"""

import sys
import time
from mistral_thermal_integration import MistralThermalMemory

class MistralInteractiveInterface:
    """Interface interactive pour Mistral + Mémoire Thermique"""
    
    def __init__(self):
        print("🚀 INITIALISATION MISTRAL + MÉMOIRE THERMIQUE")
        print("=" * 60)
        
        self.mistral_thermal = MistralThermalMemory()
        
        print("✅ Système initialisé")
        print("🧠 Mémoire thermique: Active")
        print("🤖 Mistral 7B: Prêt")
        print()
        
        # Afficher le statut
        self.show_status()
    
    def show_status(self):
        """Affiche le statut du système"""
        status = self.mistral_thermal.get_thermal_status()
        
        print("📊 STATUT SYSTÈME:")
        print(f"  🧠 Entrées mémoire: {status['memory_entries']}")
        print(f"  💬 Conversations: {status['conversations_stored']}")
        print(f"  💉 Injections: {status['injections_performed']}")
        print(f"  🤖 Modèle: {status['mistral_model']}")
        print(f"  🔥 Mémoire active: {status['thermal_memory_active']}")
        print()
    
    def show_help(self):
        """Affiche l'aide"""
        print("🆘 COMMANDES DISPONIBLES:")
        print("  /help     - Afficher cette aide")
        print("  /status   - Statut du système")
        print("  /inject   - Injecter en mémoire thermique")
        print("  /memory   - Voir la mémoire thermique")
        print("  /clear    - Effacer l'écran")
        print("  /quit     - Quitter")
        print()
        print("💬 Tapez votre question pour interagir avec Mistral")
        print()
    
    def inject_memory(self):
        """Interface pour injection mémoire"""
        print("💉 INJECTION MÉMOIRE THERMIQUE")
        print("-" * 40)
        
        key = input("🔑 Clé: ").strip()
        if not key:
            print("❌ Clé requise")
            return
        
        value = input("💾 Valeur: ").strip()
        if not value:
            print("❌ Valeur requise")
            return
        
        importance = input("⭐ Importance (1-10, défaut 5): ").strip()
        try:
            importance = int(importance) if importance else 5
            importance = max(1, min(10, importance))
        except ValueError:
            importance = 5
        
        result = self.mistral_thermal.inject_thermal_memory(key, value, importance)
        print(result)
        print()
    
    def show_memory(self):
        """Affiche la mémoire thermique"""
        print("🧠 MÉMOIRE THERMIQUE ACTUELLE")
        print("-" * 50)
        
        context = self.mistral_thermal.get_all_thermal_context()
        
        if not context:
            print("❌ Aucune mémoire stockée")
            return
        
        for key, data in context.items():
            importance = data.get("importance", 0)
            value = data.get("data", "")
            
            # Limiter l'affichage
            if isinstance(value, str):
                display_value = value[:100] + "..." if len(value) > 100 else value
            else:
                display_value = str(value)[:100] + "..." if len(str(value)) > 100 else str(value)
            
            print(f"🔑 {key}")
            print(f"   ⭐ Importance: {importance}")
            print(f"   💾 Valeur: {display_value}")
            print()
    
    def process_question(self, question):
        """Traite une question avec Mistral"""
        print(f"🤔 Vous: {question}")
        print("🤖 Mistral réfléchit...")
        
        start_time = time.time()
        
        # Traitement avec mémoire thermique
        result = self.mistral_thermal.process_with_thermal_memory(question)
        
        processing_time = time.time() - start_time
        
        print(f"🤖 Mistral: {result['mistral_response']}")
        print()
        print(f"⏱️ Temps: {processing_time:.2f}s")
        print(f"🧠 Injections: {result['thermal_injections']}")
        print(f"🔥 Mémoire utilisée: {result['thermal_context_used']}")
        print()
    
    def run(self):
        """Lance l'interface interactive"""
        print("🎯 INTERFACE INTERACTIVE MISTRAL + MÉMOIRE THERMIQUE")
        print("=" * 60)
        print("Tapez /help pour voir les commandes disponibles")
        print()
        
        while True:
            try:
                user_input = input("💬 > ").strip()
                
                if not user_input:
                    continue
                
                # Commandes système
                if user_input.lower() == "/quit":
                    print("👋 Au revoir Jean-Luc !")
                    break
                
                elif user_input.lower() == "/help":
                    self.show_help()
                
                elif user_input.lower() == "/status":
                    self.show_status()
                
                elif user_input.lower() == "/inject":
                    self.inject_memory()
                
                elif user_input.lower() == "/memory":
                    self.show_memory()
                
                elif user_input.lower() == "/clear":
                    import os
                    os.system('clear' if os.name == 'posix' else 'cls')
                    print("🚀 MISTRAL + MÉMOIRE THERMIQUE - Interface Interactive")
                    print("=" * 60)
                
                # Question normale
                else:
                    self.process_question(user_input)
            
            except KeyboardInterrupt:
                print("\n👋 Au revoir Jean-Luc !")
                break
            
            except Exception as e:
                print(f"❌ Erreur: {e}")
                print()

def main():
    """Point d'entrée principal"""
    try:
        interface = MistralInteractiveInterface()
        interface.run()
    except KeyboardInterrupt:
        print("\n👋 Au revoir Jean-Luc !")
    except Exception as e:
        print(f"❌ Erreur fatale: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
