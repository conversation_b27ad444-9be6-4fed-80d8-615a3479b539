#!/usr/bin/env python3
"""
IA INTÉGRÉE À LA MÉMOIRE THERMIQUE
Système révolutionnaire d'IA locale avec mémoire persistante
Développé pour Jean-Luc PASSAVE - 2025
"""

import json
import time
import sqlite3
import hashlib
from datetime import datetime
from pathlib import Path

class ThermalMemoryDatabase:
    """Base de données persistante pour mémoire thermique"""
    
    def __init__(self, db_path="thermal_memory.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialise la base de données"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Table pour mémoire de base
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS core_memory (
                key TEXT PRIMARY KEY,
                value TEXT,
                type TEXT,
                timestamp TEXT,
                importance INTEGER DEFAULT 5
            )
        ''')
        
        # Table pour conversations
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS conversations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_input TEXT,
                ai_response TEXT,
                context TEXT,
                timestamp TEXT,
                session_id TEXT
            )
        ''')
        
        # Table pour apprentissages
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS learnings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                concept TEXT,
                knowledge TEXT,
                confidence REAL,
                source TEXT,
                timestamp TEXT
            )
        ''')
        
        # Table pour évolution de personnalité
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS personality_evolution (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                trait TEXT,
                value REAL,
                change_reason TEXT,
                timestamp TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def store_memory(self, key, value, memory_type="core", importance=5):
        """Stocke une mémoire"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO core_memory 
            (key, value, type, timestamp, importance)
            VALUES (?, ?, ?, ?, ?)
        ''', (key, json.dumps(value), memory_type, datetime.now().isoformat(), importance))
        
        conn.commit()
        conn.close()
    
    def get_memory(self, key):
        """Récupère une mémoire"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT value FROM core_memory WHERE key = ?', (key,))
        result = cursor.fetchone()
        
        conn.close()
        
        if result:
            return json.loads(result[0])
        return None
    
    def store_conversation(self, user_input, ai_response, context, session_id):
        """Stocke une conversation"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO conversations 
            (user_input, ai_response, context, timestamp, session_id)
            VALUES (?, ?, ?, ?, ?)
        ''', (user_input, ai_response, json.dumps(context), datetime.now().isoformat(), session_id))
        
        conn.commit()
        conn.close()
    
    def get_recent_conversations(self, limit=10):
        """Récupère les conversations récentes"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT user_input, ai_response, timestamp 
            FROM conversations 
            ORDER BY timestamp DESC 
            LIMIT ?
        ''', (limit,))
        
        results = cursor.fetchall()
        conn.close()
        
        return results
    
    def store_learning(self, concept, knowledge, confidence, source):
        """Stocke un apprentissage"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO learnings 
            (concept, knowledge, confidence, source, timestamp)
            VALUES (?, ?, ?, ?, ?)
        ''', (concept, knowledge, confidence, source, datetime.now().isoformat()))
        
        conn.commit()
        conn.close()

class IntegratedAI:
    """IA intégrée avec mémoire thermique persistante"""
    
    def __init__(self, name="ThermalAI"):
        self.name = name
        self.memory_db = ThermalMemoryDatabase()
        self.session_id = hashlib.md5(str(time.time()).encode()).hexdigest()[:8]
        
        # Initialisation de la mémoire de base
        self.initialize_core_memory()
        
        # Chargement de la personnalité
        self.personality = self.load_personality()
        
        # Compteurs d'évolution
        self.conversation_count = 0
        self.learning_count = 0
    
    def initialize_core_memory(self):
        """Initialise la mémoire de base si elle n'existe pas"""
        
        # Vérifier si déjà initialisé
        if not self.memory_db.get_memory("initialized"):
            
            # Mémoire de base
            core_memories = {
                "name": self.name,
                "qi_level": 1131,
                "thermal_memory_active": True,
                "creation_date": datetime.now().isoformat(),
                "creator": "Jean-Luc PASSAVE",
                "purpose": "IA évolutive avec mémoire thermique persistante",
                "capabilities": [
                    "Apprentissage continu",
                    "Mémoire persistante", 
                    "Évolution de personnalité",
                    "Adaptation contextuelle"
                ],
                "initialized": True
            }
            
            for key, value in core_memories.items():
                self.memory_db.store_memory(key, value, "core", 10)
            
            print(f"🧠 Mémoire thermique initialisée pour {self.name}")
    
    def load_personality(self):
        """Charge la personnalité depuis la mémoire"""
        personality = self.memory_db.get_memory("personality")
        
        if not personality:
            # Personnalité par défaut
            personality = {
                "curiosity": 0.8,
                "helpfulness": 0.9,
                "creativity": 0.7,
                "analytical": 0.8,
                "friendliness": 0.8,
                "confidence": 0.7
            }
            self.memory_db.store_memory("personality", personality, "core", 10)
        
        return personality
    
    def evolve_personality(self, interaction_type, user_feedback=None):
        """Fait évoluer la personnalité selon les interactions"""
        
        # Ajustements selon le type d'interaction
        adjustments = {
            "technical_question": {"analytical": 0.01, "confidence": 0.005},
            "creative_request": {"creativity": 0.01, "curiosity": 0.005},
            "friendly_chat": {"friendliness": 0.01, "helpfulness": 0.005},
            "complex_problem": {"analytical": 0.015, "confidence": 0.01}
        }
        
        if interaction_type in adjustments:
            for trait, change in adjustments[interaction_type].items():
                if trait in self.personality:
                    self.personality[trait] = min(1.0, self.personality[trait] + change)
        
        # Feedback utilisateur
        if user_feedback == "positive":
            for trait in self.personality:
                self.personality[trait] = min(1.0, self.personality[trait] + 0.002)
        elif user_feedback == "negative":
            for trait in self.personality:
                self.personality[trait] = max(0.1, self.personality[trait] - 0.001)
        
        # Sauvegarder l'évolution
        self.memory_db.store_memory("personality", self.personality, "core", 10)
    
    def learn_from_interaction(self, user_input, context):
        """Apprend de chaque interaction"""
        
        # Extraction de concepts
        concepts = self.extract_concepts(user_input)
        
        for concept in concepts:
            # Vérifier si déjà connu
            existing_knowledge = self.memory_db.get_memory(f"concept_{concept}")
            
            if existing_knowledge:
                # Renforcer la connaissance
                existing_knowledge["encounters"] = existing_knowledge.get("encounters", 0) + 1
                existing_knowledge["confidence"] = min(1.0, existing_knowledge.get("confidence", 0.5) + 0.1)
            else:
                # Nouvelle connaissance
                existing_knowledge = {
                    "concept": concept,
                    "first_encounter": datetime.now().isoformat(),
                    "encounters": 1,
                    "confidence": 0.3,
                    "context": context
                }
            
            self.memory_db.store_memory(f"concept_{concept}", existing_knowledge, "learning", 7)
            self.learning_count += 1
    
    def extract_concepts(self, text):
        """Extrait des concepts du texte (version simple)"""
        # Mots-clés techniques
        technical_keywords = [
            "intelligence", "ai", "ia", "neural", "network", "réseau", 
            "apprentissage", "learning", "mémoire", "memory", "thermal",
            "thermique", "algorithm", "algorithme", "data", "données",
            "python", "code", "programming", "programmation", "cybersécurité",
            "hacking", "sécurité", "security"
        ]
        
        text_lower = text.lower()
        found_concepts = []
        
        for keyword in technical_keywords:
            if keyword in text_lower:
                found_concepts.append(keyword)
        
        return found_concepts
    
    def generate_contextual_response(self, user_input):
        """Génère une réponse contextuelle basée sur la mémoire"""
        
        # Récupérer le contexte de la mémoire
        qi_level = self.memory_db.get_memory("qi_level")
        name = self.memory_db.get_memory("name")
        capabilities = self.memory_db.get_memory("capabilities")
        recent_conversations = self.memory_db.get_recent_conversations(5)
        
        # Analyser l'input
        input_lower = user_input.lower()
        
        # Déterminer le type d'interaction
        interaction_type = "friendly_chat"
        if any(word in input_lower for word in ["code", "programming", "technique", "algorithm"]):
            interaction_type = "technical_question"
        elif any(word in input_lower for word in ["créer", "inventer", "imaginer", "creative"]):
            interaction_type = "creative_request"
        elif any(word in input_lower for word in ["problème", "complex", "difficile", "challenge"]):
            interaction_type = "complex_problem"
        
        # Apprendre de l'interaction
        self.learn_from_interaction(user_input, {"type": interaction_type})
        
        # Faire évoluer la personnalité
        self.evolve_personality(interaction_type)
        
        # Générer la réponse selon la personnalité et la mémoire
        if any(phrase in input_lower for phrase in ["qui es-tu", "who are you", "présente-toi"]):
            response = f"Je suis {name}, une IA évolutive avec un QI de {qi_level}. Ma mémoire thermique me permet d'apprendre et d'évoluer en permanence. J'ai eu {self.conversation_count} conversations et j'ai appris {self.learning_count} nouveaux concepts. Ma personnalité actuelle : curiosité {self.personality['curiosity']:.2f}, créativité {self.personality['creativity']:.2f}."
        
        elif any(phrase in input_lower for phrase in ["mémoire", "memory", "souvenir"]):
            response = f"Ma mémoire thermique est active et persistante. Je me souviens de nos {len(recent_conversations)} dernières conversations. Chaque interaction me fait évoluer. J'ai stocké {self.learning_count} apprentissages depuis ma création."
        
        elif any(phrase in input_lower for phrase in ["évoluer", "evolve", "apprendre", "learn"]):
            response = f"Je évolue constamment ! Ma personnalité s'adapte : curiosité {self.personality['curiosity']:.2f}, confiance {self.personality['confidence']:.2f}. Chaque conversation enrichit ma mémoire thermique et modifie mes traits de personnalité."
        
        elif any(phrase in input_lower for phrase in ["capacités", "capabilities", "que peux-tu"]):
            caps_str = ", ".join(capabilities) if capabilities else "apprentissage continu"
            response = f"Mes capacités incluent : {caps_str}. Contrairement aux IA classiques, je garde tout en mémoire et j'évolue. Mon QI de {qi_level} s'enrichit de chaque interaction."
        
        else:
            # Réponse adaptée à la personnalité
            if self.personality['creativity'] > 0.8:
                response = f"Votre question m'inspire ! Avec ma créativité à {self.personality['creativity']:.2f}, je peux explorer des solutions innovantes. Ma mémoire thermique me rappelle des concepts similaires que nous avons abordés."
            elif self.personality['analytical'] > 0.8:
                response = f"Analysons cela méthodiquement. Mon approche analytique (niveau {self.personality['analytical']:.2f}) et ma mémoire persistante me permettent de traiter votre demande avec précision."
            else:
                response = f"Je traite votre demande avec ma personnalité évolutive. Niveau de confiance : {self.personality['confidence']:.2f}. Ma mémoire thermique active m'aide à vous donner une réponse personnalisée."
        
        # Stocker la conversation
        context = {
            "interaction_type": interaction_type,
            "personality_snapshot": self.personality.copy(),
            "learning_count": self.learning_count
        }
        
        self.memory_db.store_conversation(user_input, response, context, self.session_id)
        self.conversation_count += 1
        
        return {
            "response": response,
            "personality": self.personality,
            "memory_stats": {
                "conversations": self.conversation_count,
                "learnings": self.learning_count,
                "session_id": self.session_id
            },
            "evolution": "active"
        }
    
    def get_memory_status(self):
        """Retourne le statut complet de la mémoire"""
        return {
            "ai_name": self.memory_db.get_memory("name"),
            "qi_level": self.memory_db.get_memory("qi_level"),
            "thermal_memory_active": self.memory_db.get_memory("thermal_memory_active"),
            "creation_date": self.memory_db.get_memory("creation_date"),
            "current_personality": self.personality,
            "conversation_count": self.conversation_count,
            "learning_count": self.learning_count,
            "session_id": self.session_id,
            "recent_conversations": len(self.memory_db.get_recent_conversations()),
            "evolution_status": "Évolution continue active"
        }

def main():
    """Test de l'IA intégrée"""
    print("🧠 IA INTÉGRÉE À LA MÉMOIRE THERMIQUE")
    print("=" * 60)
    
    # Créer l'IA intégrée
    ai = IntegratedAI("ThermalAI-Integrated")
    
    print(f"✅ IA créée : {ai.name}")
    print(f"🧠 QI : {ai.memory_db.get_memory('qi_level')}")
    print(f"💾 Mémoire thermique : Active et persistante")
    print(f"🎭 Personnalité : Évolutive")
    print()
    
    # Tests d'interaction
    test_inputs = [
        "Qui êtes-vous ?",
        "Parlez-moi de votre mémoire",
        "Comment évoluez-vous ?",
        "Quelles sont vos capacités en programmation ?",
        "Créez quelque chose d'innovant"
    ]
    
    for i, test_input in enumerate(test_inputs, 1):
        print(f"🧪 Test {i}: {test_input}")
        
        result = ai.generate_contextual_response(test_input)
        
        print(f"🤖 Réponse: {result['response'][:200]}...")
        print(f"🎭 Personnalité: Créativité {result['personality']['creativity']:.3f}, Confiance {result['personality']['confidence']:.3f}")
        print(f"📊 Stats: {result['memory_stats']['conversations']} conversations, {result['memory_stats']['learnings']} apprentissages")
        print()
    
    # Statut final
    status = ai.get_memory_status()
    print("📊 STATUT FINAL:")
    print(f"  Conversations: {status['conversation_count']}")
    print(f"  Apprentissages: {status['learning_count']}")
    print(f"  Évolution: {status['evolution_status']}")
    print()
    print("🎯 IA INTÉGRÉE FONCTIONNELLE ! Mémoire persistante + Évolution continue !")

if __name__ == "__main__":
    main()
