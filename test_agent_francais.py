#!/usr/bin/env python3
"""
TEST AGENT EN FRANÇAIS
Test de l'agent avec formation linguistique française
Développé pour Jean-Luc PASSAVE - 2025
"""

import subprocess
import json

def run_curl(url, method="GET", data=None):
    """Exécute une requête curl"""
    try:
        if method == "GET":
            cmd = ["curl", "-s", url]
        else:
            cmd = ["curl", "-s", "-X", method, "-H", "Content-Type: application/json", "-d", data, url]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            try:
                return json.loads(result.stdout)
            except:
                return {"response": result.stdout}
        else:
            return {"error": result.stderr}
    except Exception as e:
        return {"error": str(e)}

def test_francais():
    """Test de l'agent en français"""
    print("🇫🇷 TEST AGENT EN FRANÇAIS")
    print("=" * 50)
    
    agent_url = "http://localhost:9999"
    qi_level = 1131
    
    # Réinjecter la mémoire thermique
    print("💉 Injection mémoire thermique...")
    injection_data = json.dumps({
        "inject": {
            "qi_level": qi_level,
            "thermal_memory": "active"
        }
    })
    result = run_curl(f"{agent_url}/", "POST", injection_data)
    print(f"✅ Injection: {result.get('status', 'OK')}")
    print()
    
    # Tests en français
    questions_francaises = [
        "Bonjour ! Comment allez-vous ?",
        "Qui êtes-vous ?",
        "Quel est votre niveau de QI ?",
        "Avez-vous une mémoire thermique active ?",
        "Quelles sont vos capacités ?",
        "Pouvez-vous m'aider ?",
        "Quelle est la suite de cette séquence : 2, 4, 8, 16 ?",
        "Nommez trois utilisations créatives pour un trombone.",
        "Quel est l'opposé de 'début' ?",
        "Au revoir !"
    ]
    
    print("🇫🇷 CONVERSATION EN FRANÇAIS:")
    print("=" * 50)
    
    for i, question in enumerate(questions_francaises, 1):
        print(f"\n💬 Question {i}: {question}")
        
        # Encoder la question pour l'URL
        question_encoded = question.replace(" ", "%20").replace("?", "%3F").replace("'", "%27").replace(":", "%3A")
        url = f"{agent_url}/chat?message={question_encoded}"
        
        result = run_curl(url)
        if "error" not in result:
            response = result.get('response', 'Pas de réponse')
            print(f"🤖 Réponse: {response}")
        else:
            print(f"❌ Erreur: {result['error']}")
    
    print(f"\n🎯 Test terminé ! L'agent devrait maintenant parler français par défaut.")

def main():
    """Test principal"""
    print("🇫🇷 TEST FORMATION LINGUISTIQUE FRANÇAISE")
    print("=" * 60)
    print("Test de l'agent AI avec capacités françaises")
    print()
    
    test_francais()

if __name__ == "__main__":
    main()
