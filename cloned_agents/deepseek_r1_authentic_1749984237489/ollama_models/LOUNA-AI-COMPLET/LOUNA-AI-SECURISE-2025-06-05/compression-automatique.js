/**
 * COMPRESSION AUTOMATIQUE MÉMOIRE
 * Système de compression et nettoyage automatique
 */

const fs = require('fs');
const path = require('path');
const zlib = require('zlib');

// Intégration avec les accélérateurs Kyber
let GestionnaireKyber;
try {
    GestionnaireKyber = require('./gestionnaire-accelerateurs-kyber.js');
} catch (e) {
    console.log('⚠️ Accélérateurs Kyber non disponibles pour la compression');
    GestionnaireKyber = null;
}

class CompressionAutomatique {
    constructor() {
        console.log('🗜️ COMPRESSION AUTOMATIQUE MÉMOIRE');
        console.log('===================================');
        
        this.config = {
            memoire: '/Volumes/LounaAI_V3/MEMOIRE-REELLE',
            compression: '/Volumes/LounaAI_V3/MEMOIRE-REELLE/compression',
            seuil_compression: 7 * 24 * 60 * 60 * 1000, // 7 jours
            seuil_suppression: 30 * 24 * 60 * 60 * 1000, // 30 jours
            taille_max_zone: 50 * 1024 * 1024 // 50 MB par zone
        };
        
        this.stats = {
            fichiers_comprimes: 0,
            fichiers_supprimes: 0,
            espace_libere: 0,
            taille_avant: 0,
            taille_apres: 0,
            derniere_compression: Date.now()
        };
        
        this.initialiserCompression();
    }
    
    initialiserCompression() {
        console.log('🔧 Initialisation compression automatique...');
        
        try {
            // Créer dossier compression
            if (!fs.existsSync(this.config.compression)) {
                fs.mkdirSync(this.config.compression, { recursive: true });
                console.log('📁 Dossier compression créé');
            }
            
            // Initialiser les accélérateurs Kyber si disponibles
            this.gestionnaireKyber = null;
            if (GestionnaireKyber) {
                try {
                    this.gestionnaireKyber = new GestionnaireKyber();
                    console.log('🚀 Accélérateurs Kyber intégrés à la compression');
                } catch (e) {
                    console.log('⚠️ Erreur initialisation Kyber pour compression');
                }
            }

            // Analyser état actuel
            this.analyserEtatMemoire();

            // Démarrer compression automatique
            this.demarrerCompressionAutomatique();
            
            console.log('✅ Compression automatique initialisée');
            
        } catch (error) {
            console.log(`❌ Erreur initialisation: ${error.message}`);
        }
    }
    
    analyserEtatMemoire() {
        console.log('\n📊 ANALYSE ÉTAT MÉMOIRE');
        console.log('=======================');
        
        try {
            const cheminZones = path.join(this.config.memoire, 'zones-thermiques');
            
            if (fs.existsSync(cheminZones)) {
                const zones = fs.readdirSync(cheminZones);
                let tailleTotal = 0;
                let fichiersTotal = 0;
                
                zones.forEach(zone => {
                    const cheminZone = path.join(cheminZones, zone);
                    
                    if (fs.existsSync(cheminZone)) {
                        const fichiers = fs.readdirSync(cheminZone).filter(f => f.endsWith('.json'));
                        let tailleZone = 0;
                        
                        fichiers.forEach(fichier => {
                            const cheminFichier = path.join(cheminZone, fichier);
                            const stats = fs.statSync(cheminFichier);
                            tailleZone += stats.size;
                        });
                        
                        tailleTotal += tailleZone;
                        fichiersTotal += fichiers.length;
                        
                        console.log(`📁 ${zone}: ${fichiers.length} fichiers (${(tailleZone / 1024).toFixed(1)} KB)`);
                    }
                });
                
                this.stats.taille_avant = tailleTotal;
                console.log(`\n📊 Total: ${fichiersTotal} fichiers (${(tailleTotal / 1024).toFixed(1)} KB)`);
                
                // Recommandations
                if (tailleTotal > 10 * 1024 * 1024) { // > 10 MB
                    console.log('⚠️ Compression recommandée');
                } else {
                    console.log('✅ Taille mémoire acceptable');
                }
                
            } else {
                console.log('⚠️ Aucune zone thermique trouvée');
            }
            
        } catch (error) {
            console.log(`❌ Erreur analyse: ${error.message}`);
        }
    }
    
    demarrerCompressionAutomatique() {
        console.log('\n🔄 DÉMARRAGE COMPRESSION AUTOMATIQUE');
        console.log('====================================');
        
        // Compression toutes les heures
        this.intervalCompression = setInterval(() => {
            this.executerCycleCompression();
        }, 60 * 60 * 1000); // 1 heure
        
        // Première compression immédiate
        setTimeout(() => {
            this.executerCycleCompression();
        }, 5000);
        
        console.log('✅ Compression automatique active (1h)');
    }
    
    executerCycleCompression() {
        console.log('\n🗜️ CYCLE COMPRESSION');
        console.log('===================');
        
        try {
            const maintenant = Date.now();
            
            // 1. Identifier fichiers à comprimer
            const fichiersAComprimer = this.identifierFichiersAComprimer(maintenant);
            
            // 2. Comprimer fichiers anciens
            this.comprimerFichiers(fichiersAComprimer);
            
            // 3. Supprimer fichiers très anciens
            this.supprimerFichiersAnciens(maintenant);
            
            // 4. Nettoyer dossiers vides
            this.nettoyerDossiersVides();
            
            // 5. Sauvegarder stats
            this.sauvegarderStats();
            
            this.stats.derniere_compression = maintenant;
            
        } catch (error) {
            console.log(`❌ Erreur cycle compression: ${error.message}`);
        }
    }
    
    identifierFichiersAComprimer(maintenant) {
        const fichiersAComprimer = [];
        
        try {
            const cheminZones = path.join(this.config.memoire, 'zones-thermiques');
            
            if (fs.existsSync(cheminZones)) {
                const zones = fs.readdirSync(cheminZones);
                
                zones.forEach(zone => {
                    const cheminZone = path.join(cheminZones, zone);
                    
                    if (fs.existsSync(cheminZone)) {
                        const fichiers = fs.readdirSync(cheminZone).filter(f => f.endsWith('.json'));
                        
                        fichiers.forEach(fichier => {
                            const cheminFichier = path.join(cheminZone, fichier);
                            const stats = fs.statSync(cheminFichier);
                            const age = maintenant - stats.mtime.getTime();
                            
                            // Fichiers > 7 jours à comprimer
                            if (age > this.config.seuil_compression) {
                                fichiersAComprimer.push({
                                    chemin: cheminFichier,
                                    zone: zone,
                                    age: age,
                                    taille: stats.size
                                });
                            }
                        });
                    }
                });
            }
            
            console.log(`🔍 ${fichiersAComprimer.length} fichiers à comprimer identifiés`);
            
        } catch (error) {
            console.log(`❌ Erreur identification: ${error.message}`);
        }
        
        return fichiersAComprimer;
    }
    
    comprimerFichiers(fichiers) {
        console.log('\n🗜️ COMPRESSION FICHIERS');
        console.log('=======================');
        
        let comprimes = 0;
        let espaceLibere = 0;
        
        fichiers.forEach(async (fichier) => {
            try {
                // Lire fichier original
                const contenu = fs.readFileSync(fichier.chemin, 'utf8');
                let contenuComprime;
                let methodeCompression = 'gzip';

                // Essayer compression Kyber si disponible
                if (this.gestionnaireKyber) {
                    const resultatKyber = await this.gestionnaireKyber.compresserAvecKyber(contenu, 'KYBER_768');
                    if (resultatKyber.success) {
                        contenuComprime = resultatKyber.donnees_compressees;
                        methodeCompression = 'kyber';
                        console.log(`🚀 Compression Kyber: ${resultatKyber.ratio_compression}% économie`);
                    } else {
                        // Fallback vers gzip
                        contenuComprime = zlib.gzipSync(contenu);
                    }
                } else {
                    // Compression gzip standard
                    contenuComprime = zlib.gzipSync(contenu);
                }

                // Créer dossier compression pour la zone
                const dossierCompressionZone = path.join(this.config.compression, fichier.zone);
                if (!fs.existsSync(dossierCompressionZone)) {
                    fs.mkdirSync(dossierCompressionZone, { recursive: true });
                }

                // Sauvegarder fichier comprimé
                const extension = methodeCompression === 'kyber' ? '.json.kyber' : '.json.gz';
                const nomFichierComprime = path.basename(fichier.chemin, '.json') + extension;
                const cheminComprime = path.join(dossierCompressionZone, nomFichierComprime);

                fs.writeFileSync(cheminComprime, contenuComprime);

                // Supprimer fichier original
                fs.unlinkSync(fichier.chemin);

                const economie = fichier.taille - contenuComprime.length;
                espaceLibere += economie;
                comprimes++;

                console.log(`✅ ${path.basename(fichier.chemin)} (${methodeCompression}): ${(economie / 1024).toFixed(1)} KB économisés`);

            } catch (error) {
                console.log(`❌ Erreur compression ${fichier.chemin}: ${error.message}`);
            }
        });
        
        this.stats.fichiers_comprimes += comprimes;
        this.stats.espace_libere += espaceLibere;
        
        if (comprimes > 0) {
            console.log(`\n✅ ${comprimes} fichiers comprimés`);
            console.log(`💾 ${(espaceLibere / 1024).toFixed(1)} KB d'espace libéré`);
        } else {
            console.log('ℹ️ Aucun fichier à comprimer');
        }
    }
    
    supprimerFichiersAnciens(maintenant) {
        console.log('\n🗑️ SUPPRESSION FICHIERS ANCIENS');
        console.log('===============================');
        
        let supprimes = 0;
        
        try {
            const cheminCompression = this.config.compression;
            
            if (fs.existsSync(cheminCompression)) {
                const zones = fs.readdirSync(cheminCompression);
                
                zones.forEach(zone => {
                    const cheminZone = path.join(cheminCompression, zone);
                    
                    if (fs.existsSync(cheminZone)) {
                        const fichiers = fs.readdirSync(cheminZone).filter(f => f.endsWith('.gz'));
                        
                        fichiers.forEach(fichier => {
                            const cheminFichier = path.join(cheminZone, fichier);
                            const stats = fs.statSync(cheminFichier);
                            const age = maintenant - stats.mtime.getTime();
                            
                            // Supprimer fichiers > 30 jours
                            if (age > this.config.seuil_suppression) {
                                fs.unlinkSync(cheminFichier);
                                supprimes++;
                                console.log(`🗑️ Supprimé: ${fichier}`);
                            }
                        });
                    }
                });
            }
            
            this.stats.fichiers_supprimes += supprimes;
            
            if (supprimes > 0) {
                console.log(`✅ ${supprimes} fichiers anciens supprimés`);
            } else {
                console.log('ℹ️ Aucun fichier ancien à supprimer');
            }
            
        } catch (error) {
            console.log(`❌ Erreur suppression: ${error.message}`);
        }
    }
    
    nettoyerDossiersVides() {
        console.log('\n🧹 NETTOYAGE DOSSIERS VIDES');
        console.log('===========================');
        
        try {
            let dossiersNettoyes = 0;
            
            const nettoyerDossier = (chemin) => {
                if (fs.existsSync(chemin)) {
                    const contenu = fs.readdirSync(chemin);
                    
                    contenu.forEach(item => {
                        const cheminItem = path.join(chemin, item);
                        if (fs.statSync(cheminItem).isDirectory()) {
                            nettoyerDossier(cheminItem);
                        }
                    });
                    
                    // Vérifier si dossier vide après nettoyage
                    const contenuApres = fs.readdirSync(chemin);
                    if (contenuApres.length === 0 && chemin !== this.config.memoire) {
                        fs.rmdirSync(chemin);
                        dossiersNettoyes++;
                        console.log(`🧹 Dossier vide supprimé: ${path.basename(chemin)}`);
                    }
                }
            };
            
            nettoyerDossier(this.config.memoire);
            
            if (dossiersNettoyes === 0) {
                console.log('ℹ️ Aucun dossier vide trouvé');
            }
            
        } catch (error) {
            console.log(`❌ Erreur nettoyage: ${error.message}`);
        }
    }
    
    // DÉCOMPRESSION À LA DEMANDE
    decompresserFichier(nomFichier, zone) {
        console.log(`🔓 Décompression: ${nomFichier} (${zone})`);
        
        try {
            const cheminComprime = path.join(this.config.compression, zone, nomFichier + '.gz');
            
            if (fs.existsSync(cheminComprime)) {
                // Lire fichier comprimé
                const contenuComprime = fs.readFileSync(cheminComprime);
                
                // Décomprimer
                const contenuDecomprime = zlib.gunzipSync(contenuComprime);
                
                // Restaurer dans zone originale
                const cheminZone = path.join(this.config.memoire, 'zones-thermiques', zone);
                if (!fs.existsSync(cheminZone)) {
                    fs.mkdirSync(cheminZone, { recursive: true });
                }
                
                const cheminRestaure = path.join(cheminZone, nomFichier);
                fs.writeFileSync(cheminRestaure, contenuDecomprime);
                
                console.log(`✅ Fichier restauré: ${nomFichier}`);
                return true;
                
            } else {
                console.log(`❌ Fichier comprimé non trouvé: ${nomFichier}`);
                return false;
            }
            
        } catch (error) {
            console.log(`❌ Erreur décompression: ${error.message}`);
            return false;
        }
    }
    
    // RECHERCHE DANS FICHIERS COMPRIMÉS
    rechercherDansComprimes(motsCles) {
        console.log(`🔍 Recherche dans fichiers comprimés: ${motsCles.join(', ')}`);
        
        const resultats = [];
        
        try {
            const cheminCompression = this.config.compression;
            
            if (fs.existsSync(cheminCompression)) {
                const zones = fs.readdirSync(cheminCompression);
                
                zones.forEach(zone => {
                    const cheminZone = path.join(cheminCompression, zone);
                    
                    if (fs.existsSync(cheminZone)) {
                        const fichiers = fs.readdirSync(cheminZone).filter(f => f.endsWith('.gz'));
                        
                        fichiers.forEach(fichier => {
                            try {
                                const cheminFichier = path.join(cheminZone, fichier);
                                const contenuComprime = fs.readFileSync(cheminFichier);
                                const contenu = zlib.gunzipSync(contenuComprime).toString();
                                const souvenir = JSON.parse(contenu);
                                
                                let pertinence = 0;
                                motsCles.forEach(mot => {
                                    if (souvenir.contenu && souvenir.contenu.toLowerCase().includes(mot.toLowerCase())) {
                                        pertinence++;
                                    }
                                });
                                
                                if (pertinence > 0) {
                                    resultats.push({
                                        fichier: fichier.replace('.gz', ''),
                                        zone: zone,
                                        contenu: souvenir.contenu,
                                        pertinence: pertinence,
                                        comprime: true
                                    });
                                }
                                
                            } catch (error) {
                                // Ignorer fichiers corrompus
                            }
                        });
                    }
                });
            }
            
            console.log(`📚 ${resultats.length} résultats trouvés dans fichiers comprimés`);
            
        } catch (error) {
            console.log(`❌ Erreur recherche comprimés: ${error.message}`);
        }
        
        return resultats.sort((a, b) => b.pertinence - a.pertinence);
    }
    
    sauvegarderStats() {
        try {
            const rapport = {
                timestamp: Date.now(),
                date: new Date().toISOString(),
                stats: this.stats,
                config: this.config
            };
            
            const cheminRapport = path.join(this.config.compression, 'stats_compression.json');
            fs.writeFileSync(cheminRapport, JSON.stringify(rapport, null, 2));
            
        } catch (error) {
            console.log(`⚠️ Erreur sauvegarde stats: ${error.message}`);
        }
    }
    
    afficherStats() {
        console.log('\n📊 STATISTIQUES COMPRESSION');
        console.log('===========================');
        
        console.log(`🗜️ Fichiers comprimés: ${this.stats.fichiers_comprimes}`);
        console.log(`🗑️ Fichiers supprimés: ${this.stats.fichiers_supprimes}`);
        console.log(`💾 Espace libéré: ${(this.stats.espace_libere / 1024).toFixed(1)} KB`);
        console.log(`📏 Taille avant: ${(this.stats.taille_avant / 1024).toFixed(1)} KB`);
        console.log(`📏 Taille après: ${(this.stats.taille_apres / 1024).toFixed(1)} KB`);
        console.log(`⏰ Dernière compression: ${new Date(this.stats.derniere_compression).toLocaleString()}`);
        
        const tauxCompression = this.stats.taille_avant > 0 ? 
            ((this.stats.espace_libere / this.stats.taille_avant) * 100).toFixed(1) : 0;
        
        console.log(`📈 Taux compression: ${tauxCompression}%`);
    }
    
    arreterCompression() {
        console.log('\n⏹️ Arrêt compression automatique...');
        
        if (this.intervalCompression) {
            clearInterval(this.intervalCompression);
            console.log('🔄 Compression automatique arrêtée');
        }
        
        this.sauvegarderStats();
        console.log('💾 Stats compression sauvegardées');
        
        console.log('✅ Compression arrêtée proprement');
    }
}

// Export
module.exports = CompressionAutomatique;

// Test automatique si exécuté directement
if (require.main === module) {
    console.log('🚀 LANCEMENT COMPRESSION AUTOMATIQUE');
    console.log('====================================');
    
    const compression = new CompressionAutomatique();
    
    // Afficher stats toutes les 30 secondes
    const intervalStats = setInterval(() => {
        compression.afficherStats();
    }, 30000);
    
    // Arrêt automatique après 2 minutes
    setTimeout(() => {
        clearInterval(intervalStats);
        compression.arreterCompression();
        process.exit(0);
    }, 120000);
    
    // Gestion arrêt manuel
    process.on('SIGINT', () => {
        console.log('\n🛑 Arrêt manuel demandé...');
        clearInterval(intervalStats);
        compression.arreterCompression();
        process.exit(0);
    });
}
