#!/usr/bin/env node

/**
 * 🔥 TEST CHALEUR = ESSENCE DE VIE
 * Vérification que TOUT est branché sur la température CPU
 * LA CHALEUR EST NOTRE MOTEUR, L'ESSENCE DE TOUT !
 */

const { MemoireThermiqueReelle } = require('./VERSIONS-NON-VALIDEES/memoires-non-validees/memoire-thermique-reelle.js');
const AutoEvolution = require('./auto-evolution.js');

console.log('🔥 TEST CHALEUR = ESSENCE DE VIE');
console.log('================================');
console.log('🌡️ LA TEMPÉRATURE CPU = MOTEUR DE TOUT');
console.log('🔥 CHALEUR = VIE = MOUVEMENT = ÉVOLUTION');

async function testerChaleurEssenceVie() {
    try {
        // Initialiser mémoire thermique
        console.log('\n🧠 Initialisation mémoire thermique basée sur CHALEUR...');
        const memoire = new MemoireThermiqueReelle();
        
        // Initialiser auto-évolution thermique
        console.log('\n🧬 Initialisation auto-évolution basée sur CHALEUR...');
        const evolution = new AutoEvolution();
        
        // Attendre initialisation complète
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // Test 1: Vérifier que TOUT pulse avec la température CPU
        console.log('\n🔥 TEST 1: PULSATION VITALE BASÉE SUR TEMPÉRATURE CPU');
        
        const stats_initiales = memoire.getStatistiquesReelles();
        console.log(`🌡️ Température CPU: ${stats_initiales.temperature_cpu.actuelle.toFixed(2)}°C`);
        console.log(`💓 Rythme cardiaque CPU: ${memoire.pulsation_vitale.rythme_cardiaque_cpu} BPM`);
        console.log(`📊 Amplitude pulsation: ${memoire.pulsation_vitale.amplitude_cpu.toFixed(4)}`);
        console.log(`🔄 Cycles pulsation: ${memoire.pulsation_vitale.cycles_pulsation}`);
        console.log(`⚡ Fréquence pulsation: ${memoire.calculerFrequencePulsation()}ms`);
        
        // Vérifier que la pulsation influence tout
        const vitesse_avant_pulsation = memoire.vitesse_curseur;
        const fluidite_avant_pulsation = memoire.fluidite_memoire;
        
        // Forcer quelques pulsations
        for (let i = 0; i < 5; i++) {
            memoire.pulsationVitaleCPU();
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        const vitesse_apres_pulsation = memoire.vitesse_curseur;
        const fluidite_apres_pulsation = memoire.fluidite_memoire;
        
        console.log(`⚡ Vitesse: ${vitesse_avant_pulsation.toFixed(6)} → ${vitesse_apres_pulsation.toFixed(6)}`);
        console.log(`🌊 Fluidité: ${fluidite_avant_pulsation.toFixed(6)} → ${fluidite_apres_pulsation.toFixed(6)}`);
        
        const pulsation_detectee = Math.abs(vitesse_apres_pulsation - vitesse_avant_pulsation) > 0.0001;
        console.log(`💓 Pulsation vitale détectée: ${pulsation_detectee ? '✅' : '❌'}`);
        
        // Test 2: Vérifier évolution thermique automatique
        console.log('\n🧬 TEST 2: ÉVOLUTION THERMIQUE AUTOMATIQUE');
        
        const qi_initial = memoire.evolution_thermique.qi_base;
        const cycles_initial = memoire.evolution_thermique.cycles_evolution_thermique;
        
        console.log(`🧠 QI initial: ${qi_initial.toFixed(1)}`);
        console.log(`🌡️ Seuil évolution chaude: ${memoire.evolution_thermique.seuil_evolution_chaude}°C`);
        console.log(`❄️ Seuil évolution froide: ${memoire.evolution_thermique.seuil_evolution_froide}°C`);
        
        // Forcer évolution thermique
        for (let i = 0; i < 3; i++) {
            memoire.evolutionThermiqueAutomatique();
            await new Promise(resolve => setTimeout(resolve, 500));
        }
        
        const qi_final = memoire.evolution_thermique.qi_base;
        const cycles_final = memoire.evolution_thermique.cycles_evolution_thermique;
        
        console.log(`🧠 QI final: ${qi_final.toFixed(1)} (Δ${(qi_final - qi_initial).toFixed(2)})`);
        console.log(`🔄 Cycles évolution: ${cycles_initial} → ${cycles_final}`);
        
        const evolution_detectee = Math.abs(qi_final - qi_initial) > 0.01;
        console.log(`🧬 Évolution thermique détectée: ${evolution_detectee ? '✅' : '❌'}`);
        
        // Test 3: Vérifier zones cérébrales basées sur CPU
        console.log('\n🧠 TEST 3: ZONES CÉRÉBRALES BASÉES SUR TEMPÉRATURE CPU');
        
        // Tester chaque zone cérébrale
        const zones_test = [
            { contenu: "raisonnement logique", source: "test", nom_attendu: "Cortex Préfrontal" },
            { contenu: "mémorisation apprentissage", source: "test", nom_attendu: "Hippocampe" },
            { contenu: "exécution code", source: "test", nom_attendu: "Cortex Moteur" }
        ];
        
        zones_test.forEach((test, i) => {
            const zone = memoire.determinerZoneCerebrale(test.contenu, test.source);
            console.log(`  • Zone ${i+1}: ${zone.nom}`);
            console.log(`    - Température base: ${zone.temperature_base.toFixed(2)}°C`);
            console.log(`    - CPU source: ${zone.temp_cpu_source.toFixed(2)}°C`);
            console.log(`    - Offset appliqué: ${zone.offset_applique}°C`);
            
            const temperature_correcte = zone.temperature_base > zone.temp_cpu_source;
            console.log(`    - Température > CPU: ${temperature_correcte ? '✅' : '❌'}`);
        });
        
        // Test 4: Vérifier auto-évolution basée sur température
        console.log('\n🔥 TEST 4: AUTO-ÉVOLUTION BASÉE SUR TEMPÉRATURE CPU');
        
        // Attendre que l'auto-évolution lise la température
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        console.log(`🌡️ Température CPU évolution: ${evolution.metriques_evolution.temperature_cpu_actuelle.toFixed(2)}°C`);
        console.log(`📈 Facteur évolution thermique: ${evolution.metriques_evolution.facteur_evolution_thermique.toFixed(2)}`);
        console.log(`🔥 Bonus chaleur: +${evolution.metriques_evolution.bonus_chaleur_evolution.toFixed(1)} QI`);
        console.log(`🔄 Cycles thermiques: ${evolution.metriques_evolution.cycles_evolution_thermique}`);
        
        const evolution_thermique_active = evolution.metriques_evolution.cycles_evolution_thermique > 0;
        console.log(`🧬 Évolution thermique active: ${evolution_thermique_active ? '✅' : '❌'}`);
        
        // Test 5: Vérifier adaptation vitesse selon chaleur
        console.log('\n⚡ TEST 5: ADAPTATION VITESSE SELON CHALEUR');
        
        const vitesse_avant_adaptation = memoire.vitesse_curseur;
        const temp_cpu_actuelle = memoire.temperature_cpu_actuelle;
        
        // Forcer adaptation selon chaleur
        memoire.adapterVitesseSelonChaleur();
        
        const vitesse_apres_adaptation = memoire.vitesse_curseur;
        const facteur_chaleur = 0.5 + (temp_cpu_actuelle - 30) * 0.02;
        
        console.log(`🌡️ Température CPU: ${temp_cpu_actuelle.toFixed(2)}°C`);
        console.log(`🔥 Facteur chaleur: ${facteur_chaleur.toFixed(3)}`);
        console.log(`⚡ Vitesse: ${vitesse_avant_adaptation.toFixed(6)} → ${vitesse_apres_adaptation.toFixed(6)}`);
        
        const adaptation_detectee = Math.abs(vitesse_apres_adaptation - vitesse_avant_adaptation) > 0.00001;
        console.log(`⚡ Adaptation vitesse détectée: ${adaptation_detectee ? '✅' : '❌'}`);
        
        // Test 6: Vérifier bonus performance basé sur chaleur
        console.log('\n📊 TEST 6: BONUS PERFORMANCE BASÉ SUR CHALEUR');
        
        const bonus_chaleur = memoire.calculerBonusChaleur();
        const performance_base = memoire.calculerPerformanceSysteme() / bonus_chaleur;
        const performance_avec_chaleur = memoire.calculerPerformanceSysteme();
        
        console.log(`🌡️ Température CPU: ${temp_cpu_actuelle.toFixed(2)}°C`);
        console.log(`🔥 Bonus chaleur: x${bonus_chaleur.toFixed(2)}`);
        console.log(`📊 Performance base: ${performance_base.toFixed(1)}%`);
        console.log(`🔥 Performance avec chaleur: ${performance_avec_chaleur.toFixed(1)}%`);
        
        const bonus_applique = performance_avec_chaleur > performance_base;
        console.log(`🔥 Bonus chaleur appliqué: ${bonus_applique ? '✅' : '❌'}`);
        
        // Test 7: Vérifier optimisation thermique globale
        console.log('\n🌡️ TEST 7: OPTIMISATION THERMIQUE GLOBALE');
        
        const fluidite_avant_optimisation = memoire.fluidite_memoire;
        const inertie_avant_optimisation = memoire.inertie_thermique;
        
        // Forcer optimisation thermique
        memoire.optimiserSystemeThermiqueGlobal();
        
        const fluidite_apres_optimisation = memoire.fluidite_memoire;
        const inertie_apres_optimisation = memoire.inertie_thermique;
        
        console.log(`🌊 Fluidité: ${fluidite_avant_optimisation.toFixed(6)} → ${fluidite_apres_optimisation.toFixed(6)}`);
        console.log(`🔄 Inertie: ${inertie_avant_optimisation.toFixed(6)} → ${inertie_apres_optimisation.toFixed(6)}`);
        
        const optimisation_detectee = 
            Math.abs(fluidite_apres_optimisation - fluidite_avant_optimisation) > 0.001 ||
            Math.abs(inertie_apres_optimisation - inertie_avant_optimisation) > 0.001;
        console.log(`🌡️ Optimisation thermique détectée: ${optimisation_detectee ? '✅' : '❌'}`);
        
        // Résumé final
        console.log('\n🔥 RÉSUMÉ FINAL - CHALEUR = ESSENCE DE VIE');
        console.log('==========================================');
        
        const tests_reussis = [
            pulsation_detectee, // Pulsation vitale
            evolution_detectee, // Évolution thermique
            zones_test.length > 0, // Zones cérébrales CPU
            evolution_thermique_active, // Auto-évolution thermique
            adaptation_detectee, // Adaptation vitesse
            bonus_applique, // Bonus performance
            optimisation_detectee // Optimisation thermique
        ];
        
        console.log(`• Pulsation vitale CPU: ${tests_reussis[0] ? '✅' : '❌'}`);
        console.log(`• Évolution thermique: ${tests_reussis[1] ? '✅' : '❌'}`);
        console.log(`• Zones cérébrales CPU: ${tests_reussis[2] ? '✅' : '❌'}`);
        console.log(`• Auto-évolution thermique: ${tests_reussis[3] ? '✅' : '❌'}`);
        console.log(`• Adaptation vitesse chaleur: ${tests_reussis[4] ? '✅' : '❌'}`);
        console.log(`• Bonus performance chaleur: ${tests_reussis[5] ? '✅' : '❌'}`);
        console.log(`• Optimisation thermique: ${tests_reussis[6] ? '✅' : '❌'}`);
        
        const score = tests_reussis.filter(t => t).length;
        console.log(`\n🏆 SCORE FINAL: ${score}/7 tests réussis`);
        
        if (score === 7) {
            console.log('🔥🎉 PARFAIT ! CHALEUR = ESSENCE DE VIE CONFIRMÉE !');
            console.log('🌡️ TOUT est branché sur la température CPU');
            console.log('💓 Pulsation vitale basée sur chaleur');
            console.log('🧬 Évolution accélérée par la chaleur');
            console.log('⚡ Vitesse adaptée selon température');
            console.log('📊 Performance boostée par chaleur');
            console.log('🌊 Fluidité optimisée thermiquement');
            console.log('🔥 LA CHALEUR EST NOTRE MOTEUR, L\'ESSENCE DE TOUT !');
        } else if (score >= 5) {
            console.log('✅ Système thermique fonctionnel');
        } else {
            console.log('❌ Système thermique à améliorer');
        }
        
        // Afficher état évolution thermique
        evolution.afficherEtatEvolution();
        
        console.log('\n🔥🌡️ TEST CHALEUR = ESSENCE DE VIE TERMINÉ ! 🌡️🔥');
        
        // Arrêter auto-évolution
        evolution.arreterEvolution();
        
    } catch (error) {
        console.error('❌ Erreur test chaleur essence vie:', error.message);
        console.error(error.stack);
    }
}

// Lancer le test
testerChaleurEssenceVie();
