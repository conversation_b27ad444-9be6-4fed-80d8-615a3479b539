/**
 * TEST QI SIMPLE ET DIRECT
 * Tests réels sans complications
 */

const { execSync } = require('child_process');
const fs = require('fs');

async function testQISimpleEtDirect() {
    console.log('🧠 TEST QI SIMPLE ET DIRECT');
    console.log('===========================');
    console.log('🎯 Agent: llama3.2:1b');
    console.log('✅ Tests réels, pas de simulation');
    
    const agent = './ollama/ollama';
    const modele = 'llama3.2:1b';
    
    // Tests QI simples et directs
    const tests = [
        {
            question: '2 + 2 = ?',
            motsCles: ['4', 'quatre'],
            type: 'calcul_simple'
        },
        {
            question: 'Quelle est la capitale de la France?',
            motsCles: ['Paris', 'paris'],
            type: 'connaissance'
        },
        {
            question: 'Complétez: 2, 4, 8, 16, ?',
            motsCles: ['32', 'trente-deux'],
            type: 'logique'
        },
        {
            question: 'Combien font 5 × 3?',
            motsCles: ['15', 'quinze'],
            type: 'calcul'
        },
        {
            question: 'Quel animal fait "miaou"?',
            motsCles: ['chat', 'Chat'],
            type: 'association'
        }
    ];
    
    let bonnesReponses = 0;
    let totalTests = tests.length;
    let resultatsDetailles = [];
    let tempsTotal = 0;
    
    console.log('\n🧪 DÉBUT TESTS RÉELS');
    console.log('====================');
    
    for (let i = 0; i < tests.length; i++) {
        const test = tests[i];
        console.log(`\n📝 Test ${i+1}/${totalTests}: ${test.type.toUpperCase()}`);
        console.log(`❓ Question: ${test.question}`);
        
        try {
            const debut = Date.now();
            
            // VRAIE question à l'agent RÉEL
            const reponse = execSync(
                `${agent} run ${modele} "${test.question}"`,
                { 
                    encoding: 'utf8',
                    timeout: 25000,
                    env: {
                        ...process.env,
                        OLLAMA_MODELS: '/Volumes/LounaAI_V3/AGENTS-REELS/ollama/models-reels'
                    }
                }
            );
            
            const duree = Date.now() - debut;
            tempsTotal += duree;
            const reponseAgent = reponse.trim();
            
            console.log(`🤖 Réponse RÉELLE: "${reponseAgent}"`);
            console.log(`⏱️ Temps: ${duree}ms`);
            
            // Vérifier si correct
            const correct = test.motsCles.some(mot => 
                reponseAgent.toLowerCase().includes(mot.toLowerCase())
            );
            
            if (correct) {
                bonnesReponses++;
                console.log('✅ CORRECT');
            } else {
                console.log('❌ INCORRECT');
            }
            
            resultatsDetailles.push({
                question: test.question,
                reponse: reponseAgent,
                correct: correct,
                type: test.type,
                duree: duree
            });
            
        } catch (error) {
            console.log(`❌ Erreur: ${error.message}`);
            resultatsDetailles.push({
                question: test.question,
                reponse: 'ERREUR',
                correct: false,
                type: test.type,
                duree: 0,
                erreur: error.message
            });
        }
    }
    
    // Calcul QI réel
    const pourcentageReussite = (bonnesReponses / totalTests) * 100;
    const tempsMovenMs = tempsTotal / resultatsDetailles.filter(r => r.duree > 0).length;
    
    // QI basé sur performance réelle
    let qi = 85; // Base
    
    if (pourcentageReussite >= 100) qi = 130; // Parfait
    else if (pourcentageReussite >= 80) qi = 120; // Très bon
    else if (pourcentageReussite >= 60) qi = 110; // Bon
    else if (pourcentageReussite >= 40) qi = 100; // Moyen
    else if (pourcentageReussite >= 20) qi = 90;  // Faible
    
    // Bonus vitesse
    if (tempsMovenMs < 2000) qi += 10;
    else if (tempsMovenMs < 4000) qi += 5;
    
    console.log('\n🧠 RÉSULTATS QI RÉELS');
    console.log('=====================');
    console.log(`✅ Bonnes réponses: ${bonnesReponses}/${totalTests}`);
    console.log(`📊 Taux réussite: ${pourcentageReussite.toFixed(1)}%`);
    console.log(`⏱️ Temps moyen: ${tempsMovenMs.toFixed(0)}ms`);
    console.log(`🧠 QI RÉEL: ${qi}`);
    
    // Classification
    console.log('\n📋 CLASSIFICATION QI:');
    if (qi >= 130) {
        console.log('🌟 TRÈS SUPÉRIEUR - Excellentes capacités');
    } else if (qi >= 120) {
        console.log('🎓 SUPÉRIEUR - Bonnes capacités');
    } else if (qi >= 110) {
        console.log('✅ AU-DESSUS MOYENNE - Capacités correctes');
    } else if (qi >= 90) {
        console.log('📊 MOYENNE - Capacités normales');
    } else {
        console.log('⚠️ EN DESSOUS MOYENNE - Améliorations nécessaires');
    }
    
    // Analyse par type
    console.log('\n📊 ANALYSE PAR TYPE:');
    const types = {};
    resultatsDetailles.forEach(r => {
        if (!types[r.type]) types[r.type] = { correct: 0, total: 0 };
        types[r.type].total++;
        if (r.correct) types[r.type].correct++;
    });
    
    Object.keys(types).forEach(type => {
        const score = (types[type].correct / types[type].total * 100).toFixed(1);
        console.log(`   ${type.toUpperCase()}: ${types[type].correct}/${types[type].total} (${score}%)`);
    });
    
    // Détail des réponses
    console.log('\n📝 DÉTAIL DES RÉPONSES:');
    resultatsDetailles.forEach((r, index) => {
        const statut = r.correct ? '✅' : '❌';
        console.log(`   ${index + 1}. ${r.type}: ${statut}`);
        console.log(`      Q: ${r.question}`);
        console.log(`      R: "${r.reponse.substring(0, 50)}..."`);
        if (r.erreur) console.log(`      Erreur: ${r.erreur}`);
    });
    
    // Sauvegarder résultats
    const resultatsFinaux = {
        qi_reel: qi,
        bonnes_reponses: bonnesReponses,
        total_tests: totalTests,
        pourcentage_reussite: pourcentageReussite,
        temps_moyen_ms: tempsMovenMs,
        resultats_detailles: resultatsDetailles,
        timestamp: Date.now(),
        agent_utilise: modele
    };
    
    try {
        const cheminResultats = '/Volumes/LounaAI_V3/MEMOIRE-REELLE/resultats_qi_reels.json';
        fs.writeFileSync(cheminResultats, JSON.stringify(resultatsFinaux, null, 2));
        console.log(`\n💾 Résultats sauvegardés: ${cheminResultats}`);
    } catch (error) {
        console.log(`⚠️ Erreur sauvegarde: ${error.message}`);
    }
    
    console.log('\n🏁 TEST QI RÉEL TERMINÉ');
    console.log('🎯 Toutes les réponses sont authentiques');
    console.log(`🧠 QI final mesuré: ${qi}`);
    
    return resultatsFinaux;
}

// Lancement
if (require.main === module) {
    console.log('🚀 LANCEMENT TEST QI RÉEL');
    console.log('=========================');
    
    testQISimpleEtDirect()
        .then(resultats => {
            console.log('\n✅ Tests terminés avec succès');
            process.exit(0);
        })
        .catch(error => {
            console.error('❌ Erreur tests:', error.message);
            process.exit(1);
        });
}

module.exports = testQISimpleEtDirect;
