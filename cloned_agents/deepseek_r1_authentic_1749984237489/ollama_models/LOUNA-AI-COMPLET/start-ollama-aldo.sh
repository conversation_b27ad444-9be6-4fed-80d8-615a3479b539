#!/bin/bash

# Configuration Ollama pour utiliser le disque ALDO
export OLLAMA_MODELS="/Volumes/ALDO et MIM/ollama-models"

echo "🚀 Démarrage d'Ollama avec le disque ALDO..."
echo "📁 Répertoire modèles: $OLLAMA_MODELS"

# Vérifier que le disque ALDO est monté
if [ ! -d "$OLLAMA_MODELS" ]; then
    echo "❌ Erreur: Le disque ALDO n'est pas monté ou le répertoire n'existe pas"
    exit 1
fi

echo "✅ Disque ALDO détecté"

# Arrêter Ollama s'il tourne déjà
pkill ollama 2>/dev/null
sleep 2

# Démarrer Ollama avec la configuration ALDO
echo "🔄 Démarrage d'Ollama..."
ollama serve &

# Attendre qu'Ollama soit prêt
sleep 5

echo "✅ Ollama démarré avec le disque ALDO"
echo "📊 Modèles disponibles:"
ollama list
