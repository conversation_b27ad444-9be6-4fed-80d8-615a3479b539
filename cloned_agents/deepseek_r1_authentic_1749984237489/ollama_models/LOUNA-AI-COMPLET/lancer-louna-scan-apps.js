#!/usr/bin/env node

/**
 * LANCEUR LOUNA-AI AVEC SCAN D'APPLICATIONS
 * Version complète avec contrôle d'applications de bureau
 */

const SystemeScanIntelligent = require('./systeme-scan-intelligent.js');
const GestionnaireApplicationsIntelligent = require('./gestionnaire-applications-intelligent.js');

class LounaAIScanApps {
    constructor() {
        console.log('🤖 LOUNA-AI - VERSION SCAN D\'APPLICATIONS');
        console.log('==========================================');
        
        this.scanneur = new SystemeScanIntelligent();
        this.gestionnaire = new GestionnaireApplicationsIntelligent();
        this.initialise = false;
    }

    async initialiser() {
        console.log('🔄 Initialisation de LOUNA-AI...');
        
        try {
            // Scanner toutes les applications au démarrage
            console.log('📱 Scan des applications installées...');
            const resultatScan = await this.scanneur.scannerApplications();
            
            if (resultatScan.success) {
                console.log(`✅ ${resultatScan.total} applications détectées`);
                
                // Intégrer les applications trouvées dans le gestionnaire
                resultatScan.applications.forEach(app => {
                    this.gestionnaire.applicationsConnues.set(
                        app.nom.toLowerCase().replace(/\s+/g, ''),
                        {
                            nom: app.nom_complet,
                            commande: app.commande,
                            type: app.categorie,
                            description: app.description,
                            chemin: app.chemin,
                            version: app.version
                        }
                    );
                });
                
                console.log('🔗 Applications intégrées dans le gestionnaire');
            }
            
            // Scanner le système
            console.log('🖥️ Scan du système...');
            const resultatSysteme = await this.scanneur.scannerSystemeComplet();
            
            if (resultatSysteme.success) {
                console.log('✅ Informations système collectées');
            }
            
            this.initialise = true;
            console.log('🎉 LOUNA-AI prêt avec contrôle d\'applications !');
            
            return {
                success: true,
                applications_detectees: resultatScan.total || 0,
                message: 'LOUNA-AI initialisé avec succès'
            };
            
        } catch (error) {
            console.error(`❌ Erreur initialisation: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
    }

    async traiterCommande(commande) {
        if (!this.initialise) {
            return {
                success: false,
                message: 'LOUNA-AI n\'est pas encore initialisé. Veuillez patienter...'
            };
        }

        const commandeLower = commande.toLowerCase();

        try {
            // Commandes de scan
            if (commandeLower.includes('scan') && commandeLower.includes('app')) {
                return await this.scanneur.scannerApplications();
            }

            if (commandeLower.includes('scan') && commandeLower.includes('système')) {
                return await this.scanneur.scannerSystemeComplet();
            }

            // Commandes d'ouverture d'applications
            if (commandeLower.includes('ouvre') || commandeLower.includes('lance') || commandeLower.includes('démarre')) {
                return await this.gestionnaire.traiterDemande(commande);
            }

            // Lister les applications
            if (commandeLower.includes('liste') && commandeLower.includes('app')) {
                return await this.gestionnaire.listerApplications();
            }

            // Recherche d'informations
            if (commandeLower.includes('info') || commandeLower.includes('recherche')) {
                return await this.gestionnaire.traiterDemande(commande);
            }

            // Créer une fiche technique
            if (commandeLower.includes('fiche')) {
                return await this.gestionnaire.traiterDemande(commande);
            }

            // Détecter nouvelles applications
            if (commandeLower.includes('nouvelles') && commandeLower.includes('app')) {
                return await this.scanneur.detecterNouvellesApplications();
            }

            // Statistiques
            if (commandeLower.includes('stats') || commandeLower.includes('statistiques')) {
                return this.obtenirStatistiques();
            }

            // Par défaut, essayer de traiter comme une demande d'application
            return await this.gestionnaire.traiterDemande(commande);

        } catch (error) {
            return {
                success: false,
                error: error.message,
                message: `Erreur lors du traitement: ${error.message}`
            };
        }
    }

    obtenirStatistiques() {
        const statsGestionnaire = this.gestionnaire.obtenirStatistiques();
        const statsSysteme = {
            applications_scannees: this.scanneur.applicationsDetectees.size,
            derniere_scan: this.scanneur.dernierScan,
            historique_scans: this.scanneur.historiqueScan.length
        };

        return {
            success: true,
            gestionnaire: statsGestionnaire,
            systeme: statsSysteme,
            message: 'Statistiques LOUNA-AI'
        };
    }

    async demarrerModeInteractif() {
        console.log('\n🎮 MODE INTERACTIF LOUNA-AI');
        console.log('============================');
        console.log('Commandes disponibles:');
        console.log('• "scan applications" - Scanner toutes les apps');
        console.log('• "ouvre [nom app]" - Ouvrir une application');
        console.log('• "liste applications" - Voir toutes les apps');
        console.log('• "info [nom app]" - Infos sur une app');
        console.log('• "fiche [nom app]" - Créer fiche technique');
        console.log('• "stats" - Voir les statistiques');
        console.log('• "quit" - Quitter');
        console.log('============================\n');

        const readline = require('readline');
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        const poserQuestion = () => {
            rl.question('LOUNA-AI> ', async (input) => {
                if (input.toLowerCase() === 'quit') {
                    console.log('👋 Au revoir !');
                    rl.close();
                    return;
                }

                const resultat = await this.traiterCommande(input);
                
                if (resultat.success) {
                    console.log(`✅ ${resultat.message}`);
                    if (resultat.applications) {
                        console.log(`📱 ${resultat.applications.length} applications trouvées`);
                    }
                } else {
                    console.log(`❌ ${resultat.message}`);
                }

                console.log(''); // Ligne vide
                poserQuestion();
            });
        };

        poserQuestion();
    }
}

// Démarrage automatique si exécuté directement
if (require.main === module) {
    const louna = new LounaAIScanApps();
    
    louna.initialiser().then(async (resultat) => {
        if (resultat.success) {
            console.log('\n🚀 LOUNA-AI prêt !');
            await louna.demarrerModeInteractif();
        } else {
            console.error('❌ Échec de l\'initialisation');
        }
    });
}

module.exports = LounaAIScanApps;
