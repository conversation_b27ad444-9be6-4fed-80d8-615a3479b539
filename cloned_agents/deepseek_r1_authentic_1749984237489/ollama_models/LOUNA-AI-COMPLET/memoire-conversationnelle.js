/**
 * MÉMOIRE CONVERSATIONNELLE ULTRA-STABLE
 * Correction de tous les bugs de type
 */

const fs = require('fs');
const path = require('path');

class MemoireConversationnelle {
    constructor() {
        this.fichierConversations = path.join(__dirname, 'conversations-data.json');
        this.conversationActuelle = null;
        this.conversations = new Map();
        this.maxMessagesParConversation = 100;
        this.maxConversations = 50;
        
        this.chargerConversations();
        this.demarrerNouvelleConversation();
    }

    // CHARGER CONVERSATIONS EXISTANTES
    chargerConversations() {
        try {
            if (fs.existsSync(this.fichierConversations)) {
                const data = JSON.parse(fs.readFileSync(this.fichierConversations, 'utf8'));
                this.conversations = new Map(data.conversations || []);
                console.log(`📥 ${this.conversations.size} conversations chargées`);
            }
        } catch (error) {
            console.error(`❌ Erreur chargement conversations:`, error.message);
            this.conversations = new Map();
        }
    }

    // SAUVEGARDER CONVERSATIONS
    sauvegarderConversations() {
        try {
            const data = {
                conversations: Array.from(this.conversations.entries()),
                timestamp: Date.now()
            };
            fs.writeFileSync(this.fichierConversations, JSON.stringify(data, null, 2));
            console.log(`💾 Conversations sauvegardées: ${this.conversations.size}`);
            return true;
        } catch (error) {
            console.error(`❌ Erreur sauvegarde conversations:`, error.message);
            return false;
        }
    }

    // DÉMARRER NOUVELLE CONVERSATION
    demarrerNouvelleConversation() {
        const id = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        this.conversationActuelle = {
            id: id,
            debut: Date.now(),
            messages: [],
            contexte: {
                sujet_principal: null,
                variables_code: new Map(),
                classes_definies: new Map(),
                fonctions_definies: new Map(),
                etapes_projet: [],
                derniere_action: null
            },
            resume: null
        };
        
        this.conversations.set(id, this.conversationActuelle);
        console.log(`🆕 Nouvelle conversation: ${id}`);
        
        // Nettoyer anciennes conversations si trop nombreuses
        if (this.conversations.size > this.maxConversations) {
            this.nettoyerAnciennesConversations();
        }
        
        return id;
    }

    // AJOUTER MESSAGE À LA CONVERSATION (SÉCURISÉ)
    ajouterMessage(question, reponse, source, metadata = {}) {
        try {
            if (!this.conversationActuelle) {
                this.demarrerNouvelleConversation();
            }

            const message = {
                timestamp: Date.now(),
                question: String(question || ''),
                reponse: String(reponse || ''),
                source: String(source || ''),
                metadata: metadata,
                numero: this.conversationActuelle.messages.length + 1
            };

            this.conversationActuelle.messages.push(message);

            // Analyser et extraire le contexte (SÉCURISÉ)
            this.analyserContexte(message.question, message.reponse);

            // Limiter le nombre de messages
            if (this.conversationActuelle.messages.length > this.maxMessagesParConversation) {
                this.resumerConversation();
            }

            this.sauvegarderConversations();
            return message;
        } catch (error) {
            console.error('❌ Erreur ajout message:', error.message);
            return null;
        }
    }

    // ANALYSER CONTEXTE (ULTRA-SÉCURISÉ)
    analyserContexte(question, reponse) {
        try {
            const contexte = this.conversationActuelle.contexte;
            
            // Convertir en strings pour éviter les erreurs
            const questionStr = String(question || '');
            const reponseStr = String(reponse || '');
            
            // Détecter sujet principal
            if (!contexte.sujet_principal) {
                if (questionStr.toLowerCase().includes('code') || 
                    questionStr.toLowerCase().includes('classe') || 
                    questionStr.toLowerCase().includes('fonction')) {
                    contexte.sujet_principal = 'programmation';
                } else if (questionStr.toLowerCase().includes('calcul') || 
                          questionStr.toLowerCase().includes('mathématique') ||
                          /\d+\s*[+\-*/×÷]\s*\d+/.test(questionStr)) {
                    contexte.sujet_principal = 'mathematiques';
                }
            }

            // Extraire définitions de classes (SÉCURISÉ)
            try {
                const matchClasse = reponseStr.match(/class\s+(\w+)/gi);
                if (matchClasse) {
                    matchClasse.forEach(match => {
                        const parts = match.split(' ');
                        if (parts.length >= 2) {
                            const className = parts[1];
                            contexte.classes_definies.set(className, {
                                definition: reponseStr,
                                timestamp: Date.now(),
                                message_numero: this.conversationActuelle.messages.length
                            });
                        }
                    });
                }
            } catch (error) {
                console.error('❌ Erreur extraction classes:', error.message);
            }

            // Extraire définitions de fonctions (SÉCURISÉ)
            try {
                const matchFonction = reponseStr.match(/def\s+(\w+)|function\s+(\w+)/gi);
                if (matchFonction) {
                    matchFonction.forEach(match => {
                        const parts = match.split(' ');
                        if (parts.length >= 2) {
                            const funcName = parts[1];
                            contexte.fonctions_definies.set(funcName, {
                                definition: reponseStr,
                                timestamp: Date.now(),
                                message_numero: this.conversationActuelle.messages.length
                            });
                        }
                    });
                }
            } catch (error) {
                console.error('❌ Erreur extraction fonctions:', error.message);
            }

            // Détecter étapes de projet (SÉCURISÉ)
            try {
                const matchEtape = questionStr.match(/étape\s+(\d+)/i);
                if (matchEtape) {
                    const numeroEtape = parseInt(matchEtape[1]);
                    if (!isNaN(numeroEtape)) {
                        contexte.etapes_projet.push({
                            numero: numeroEtape,
                            description: questionStr,
                            reponse: reponseStr,
                            timestamp: Date.now()
                        });
                    }
                }
            } catch (error) {
                console.error('❌ Erreur extraction étapes:', error.message);
            }

            // Mémoriser dernière action (SÉCURISÉ)
            try {
                contexte.derniere_action = {
                    type: this.detecterTypeAction(questionStr),
                    description: questionStr,
                    timestamp: Date.now()
                };
            } catch (error) {
                console.error('❌ Erreur dernière action:', error.message);
            }

        } catch (error) {
            console.error('❌ Erreur analyse contexte:', error.message);
        }
    }

    // DÉTECTER TYPE D'ACTION (SÉCURISÉ)
    detecterTypeAction(question) {
        try {
            const questionLower = String(question || '').toLowerCase();
            
            if (questionLower.includes('crée') || questionLower.includes('créer')) {
                return 'creation';
            } else if (questionLower.includes('ajoute') || questionLower.includes('ajouter')) {
                return 'ajout';
            } else if (questionLower.includes('modifie') || questionLower.includes('modifier')) {
                return 'modification';
            } else if (questionLower.includes('calcule') || questionLower.includes('calculer')) {
                return 'calcul';
            } else if (questionLower.includes('explique') || questionLower.includes('expliquer')) {
                return 'explication';
            }
            return 'autre';
        } catch (error) {
            console.error('❌ Erreur détection action:', error.message);
            return 'autre';
        }
    }

    // OBTENIR CONTEXTE POUR RÉPONSE (SÉCURISÉ)
    obtenirContexte(question) {
        try {
            if (!this.conversationActuelle) {
                return null;
            }

            const contexte = this.conversationActuelle.contexte;
            const messagesRecents = this.conversationActuelle.messages.slice(-5); // 5 derniers messages

            return {
                sujet_principal: contexte.sujet_principal,
                messages_recents: messagesRecents,
                classes_definies: Array.from(contexte.classes_definies.entries()),
                fonctions_definies: Array.from(contexte.fonctions_definies.entries()),
                etapes_projet: contexte.etapes_projet,
                derniere_action: contexte.derniere_action,
                conversation_id: this.conversationActuelle.id,
                nombre_messages: this.conversationActuelle.messages.length
            };
        } catch (error) {
            console.error('❌ Erreur obtention contexte:', error.message);
            return null;
        }
    }

    // RECHERCHER DANS L'HISTORIQUE (ULTRA-SÉCURISÉ)
    rechercherDansHistorique(requete, limite = 5) {
        const resultats = [];
        
        try {
            if (!this.conversationActuelle) {
                return resultats;
            }

            const requeteLower = String(requete || '').toLowerCase();
            
            // Rechercher dans les messages de la conversation actuelle
            for (const message of this.conversationActuelle.messages) {
                try {
                    let pertinence = 0;
                    
                    const questionStr = String(message.question || '').toLowerCase();
                    const reponseStr = String(message.reponse || '').toLowerCase();
                    
                    if (questionStr.includes(requeteLower)) {
                        pertinence += 1.0;
                    }
                    if (reponseStr.includes(requeteLower)) {
                        pertinence += 0.8;
                    }
                    
                    // Recherche par mots-clés
                    const mots = requeteLower.split(' ');
                    for (const mot of mots) {
                        if (mot.length > 2) {
                            if (questionStr.includes(mot)) {
                                pertinence += 0.3;
                            }
                            if (reponseStr.includes(mot)) {
                                pertinence += 0.2;
                            }
                        }
                    }
                    
                    if (pertinence > 0.3) {
                        resultats.push({
                            message: message,
                            pertinence: pertinence,
                            age_messages: this.conversationActuelle.messages.length - message.numero
                        });
                    }
                } catch (error) {
                    console.error('❌ Erreur traitement message historique:', error.message);
                    continue;
                }
            }
            
            // Trier par pertinence et récence
            resultats.sort((a, b) => {
                const scoreA = a.pertinence - (a.age_messages * 0.1);
                const scoreB = b.pertinence - (b.age_messages * 0.1);
                return scoreB - scoreA;
            });
            
            return resultats.slice(0, limite);
            
        } catch (error) {
            console.error('❌ Erreur recherche historique:', error.message);
            return resultats;
        }
    }

    // RÉSUMER CONVERSATION LONGUE
    resumerConversation() {
        try {
            const messages = this.conversationActuelle.messages;
            const anciens = messages.slice(0, -20); // Garder les 20 derniers
            
            // Créer un résumé des anciens messages
            const resume = {
                periode: `Messages 1-${anciens.length}`,
                sujet_principal: this.conversationActuelle.contexte.sujet_principal,
                classes_creees: Array.from(this.conversationActuelle.contexte.classes_definies.keys()),
                fonctions_creees: Array.from(this.conversationActuelle.contexte.fonctions_definies.keys()),
                etapes_completees: this.conversationActuelle.contexte.etapes_projet.length,
                timestamp: Date.now()
            };
            
            this.conversationActuelle.resume = resume;
            this.conversationActuelle.messages = messages.slice(-20); // Garder seulement les 20 derniers
            
            console.log(`📝 Conversation résumée: ${anciens.length} messages archivés`);
        } catch (error) {
            console.error('❌ Erreur résumé conversation:', error.message);
        }
    }

    // NETTOYER ANCIENNES CONVERSATIONS
    nettoyerAnciennesConversations() {
        try {
            const conversations = Array.from(this.conversations.entries());
            conversations.sort((a, b) => b[1].debut - a[1].debut); // Trier par date
            
            // Garder seulement les plus récentes
            const aGarder = conversations.slice(0, this.maxConversations);
            this.conversations = new Map(aGarder);
            
            console.log(`🧹 Nettoyage: ${conversations.length - aGarder.length} anciennes conversations supprimées`);
        } catch (error) {
            console.error('❌ Erreur nettoyage conversations:', error.message);
        }
    }

    // STATISTIQUES (SÉCURISÉ)
    getStats() {
        try {
            const conv = this.conversationActuelle;
            return {
                conversation_actuelle: conv ? conv.id : null,
                nombre_messages: conv ? conv.messages.length : 0,
                sujet_principal: conv ? conv.contexte.sujet_principal : null,
                classes_definies: conv ? conv.contexte.classes_definies.size : 0,
                fonctions_definies: conv ? conv.contexte.fonctions_definies.size : 0,
                etapes_projet: conv ? conv.contexte.etapes_projet.length : 0,
                total_conversations: this.conversations.size
            };
        } catch (error) {
            console.error('❌ Erreur stats conversation:', error.message);
            return {
                conversation_actuelle: null,
                nombre_messages: 0,
                sujet_principal: null,
                classes_definies: 0,
                fonctions_definies: 0,
                etapes_projet: 0,
                total_conversations: 0
            };
        }
    }
}

module.exports = { MemoireConversationnelle };
