/**
 * SYSTÈME D'ATTENTION ET MÉTACOGNITION POUR LOUNA-AI
 * Gestion de l'attention, mémoire de travail, métacognition
 */

class SystemeAttentionMetacognition {
    constructor() {
        // SYSTÈME D'ATTENTION
        this.attention = {
            focus_actuel: null,
            intensite: 0.5,
            duree_focus: 0,
            distractions: [],
            historique_focus: []
        };

        // MÉMOIRE DE TRAVAIL
        this.memoireTravail = {
            elements_actifs: [],
            capacite_max: 7, // Limite de <PERSON>
            charge_cognitive: 0,
            buffer_temporaire: []
        };

        // MÉTACOGNITION
        this.metacognition = {
            conscience_processus: {},
            strategies_apprentissage: [],
            auto_evaluationuation: {},
            regulation_cognitive: {},
            monitoring_performance: []
        };

        this.historique = [];
    }

    // GESTION DE L'ATTENTION
    dirigerAttention(stimulus, priorite = 0.5) {
        const timestamp = Date.now();
        
        // Calculer l'intensité d'attention basée sur la priorité
        const intensite = this.calculerIntensiteAttention(stimulus, priorite);
        
        // Mettre à jour le focus
        if (this.attention.focus_actuel !== stimulus) {
            this.changerFocus(stimulus, intensite);
        } else {
            this.renforcerFocus(intensite);
        }

        console.log(`🎯 Attention dirigée: ${stimulus} (intensité: ${intensite.toFixed(2)})`);
        
        return {
            focus: stimulus,
            intensite: intensite,
            charge_cognitive: this.memoireTravail.charge_cognitive
        };
    }

    // CALCUL INTENSITÉ D'ATTENTION
    calculerIntensiteAttention(stimulus, priorite) {
        let intensite = priorite;

        // Facteurs d'augmentation
        if (stimulus.includes('urgent') || stimulus.includes('important')) {
            intensite += 0.3;
        }
        if (stimulus.includes('calcul') || stimulus.includes('problème')) {
            intensite += 0.2;
        }
        if (stimulus.includes('médical') || stimulus.includes('diagnostic')) {
            intensite += 0.25;
        }

        // Facteurs de diminution
        if (this.memoireTravail.charge_cognitive > 0.8) {
            intensite -= 0.2; // Surcharge cognitive
        }

        return Math.max(0.1, Math.min(1.0, intensite));
    }

    // CHANGEMENT DE FOCUS
    changerFocus(nouveau_stimulus, intensite) {
        this.attention.focus_actuel = nouveau_stimulus;
        this.attention.intensite = intensite;
        this.attention.duree_focus = 0;

        console.log(`🔄 Changement de focus: ${nouveau_stimulus}`);
    }

    // RENFORCEMENT DU FOCUS
    renforcerFocus(nouvelle_intensite) {
        this.attention.intensite = Math.max(this.attention.intensite, nouvelle_intensite);
        this.attention.duree_focus += 1;
        
        console.log(`⚡ Focus renforcé: ${this.attention.focus_actuel} (${this.attention.duree_focus})`);
    }

    // GESTION MÉMOIRE DE TRAVAIL
    ajouterMemoireTravail(element, importance = 0.5) {
        // Vérifier la capacité
        if (this.memoireTravail.elements_actifs.length >= this.memoireTravail.capacite_max) {
            this.libererMemoireTravail();
        }

        // Ajouter l'élément
        const nouvel_element = {
            contenu: element,
            importance: importance,
            timestamp: Date.now(),
            activations: 1
        };

        this.memoireTravail.elements_actifs.push(nouvel_element);
        this.mettreAJourChargeCognitive();

        console.log(`💭 Ajout mémoire travail: ${element} (charge: ${this.memoireTravail.charge_cognitive.toFixed(2)})`);
        
        return nouvel_element;
    }

    // LIBÉRATION MÉMOIRE DE TRAVAIL
    libererMemoireTravail() {
        if (this.memoireTravail.elements_actifs.length > 0) {
            const element_supprime = this.memoireTravail.elements_actifs.shift();
            console.log(`🗑️ Libération mémoire travail: ${element_supprime?.contenu}`);
            this.mettreAJourChargeCognitive();
        }
    }

    // MISE À JOUR CHARGE COGNITIVE
    mettreAJourChargeCognitive() {
        const nb_elements = this.memoireTravail.elements_actifs.length;
        const charge_base = nb_elements / this.memoireTravail.capacite_max;
        
        this.memoireTravail.charge_cognitive = Math.min(1.0, charge_base);
    }

    // MÉTACOGNITION - MONITORING
    monitorerPerformance(tache, resultat, temps_execution) {
        const evaluationuation = {
            tache: tache,
            resultat: resultat,
            temps: temps_execution,
            qualite: this.evaluationuerQualiteReponse(resultat),
            efficacite: this.calculerEfficacite(temps_execution, resultat),
            timestamp: Date.now()
        };

        this.metacognition.monitoring_performance.push(evaluationuation);
        
        // Garder seulement les 50 dernières évaluations
        if (this.metacognition.monitoring_performance.length > 50) {
            this.metacognition.monitoring_performance.shift();
        }

        console.log(`🧠 Monitoring: ${tache} (qualité: ${evaluationuation.qualite.toFixed(2)}, efficacité: ${evaluationuation.efficacite.toFixed(2)})`);
        
        return evaluationuation;
    }

    // ÉVALUATION QUALITÉ RÉPONSE
    evaluationuerQualiteReponse(resultat) {
        let qualite = 0.5;

        if (typeof resultat === 'number') {
            qualite = 0.9; // Les calculs sont généralement précis
        } else if (typeof resultat === 'string') {
            if (resultat.length > 50 && resultat.includes('.')) {
                qualite = 0.8; // Réponse détaillée
            } else if (resultat.includes('Je ne')) {
                qualite = 0.7; // Honnêteté sur les limites
            } else {
                qualite = 0.6; // Réponse basique
            }
        }

        return qualite;
    }

    // CALCUL EFFICACITÉ
    calculerEfficacite(temps_execution, resultat) {
        const temps_optimal = 1000; // 1 seconde comme référence
        const facteur_temps = Math.max(0.1, temps_optimal / Math.max(temps_execution, 100));
        
        const qualite = this.evaluationuerQualiteReponse(resultat);
        
        return Math.min(1.0, facteur_temps * qualite);
    }

    // OBTENIR PERFORMANCE RÉCENTE
    obtenirPerformanceRecente() {
        const evaluationuations_recentes = this.metacognition.monitoring_performance.slice(-10);
        if (evaluationuations_recentes.length === 0) return 0.5;

        const moyenne_qualite = evaluationuations_recentes.reduce((sum, evaluation) => sum + evaluation.qualite, 0) / evaluationuations_recentes.length;
        const moyenne_efficacite = evaluationuations_recentes.reduce((sum, evaluation) => sum + evaluation.efficacite, 0) / evaluationuations_recentes.length;
        
        return (moyenne_qualite + moyenne_efficacite) / 2;
    }

    // STATISTIQUES SYSTÈME
    obtenirStatistiques() {
        const performance_globale = this.obtenirPerformanceRecente();
        
        return {
            attention: {
                focus_actuel: this.attention.focus_actuel,
                intensite: this.attention.intensite,
                duree_focus: this.attention.duree_focus
            },
            memoire_travail: {
                elements_actifs: this.memoireTravail.elements_actifs.length,
                capacite_max: this.memoireTravail.capacite_max,
                charge_cognitive: this.memoireTravail.charge_cognitive,
                utilisation: (this.memoireTravail.elements_actifs.length / this.memoireTravail.capacite_max * 100).toFixed(1) + '%'
            },
            metacognition: {
                evaluationuations_totales: this.metacognition.monitoring_performance.length,
                performance_globale: performance_globale
            }
        };
    }

    // TRAITEMENT COMPLET D'UNE TÂCHE
    traiterTache(tache, donnees) {
        const debut = Date.now();
        
        // 1. Diriger l'attention
        this.dirigerAttention(tache, 0.8);
        
        // 2. Charger en mémoire de travail
        this.ajouterMemoireTravail(donnees, 0.7);
        
        // 3. Simuler le traitement
        const resultat = `Tâche ${tache} traitée avec attention ${this.attention.intensite.toFixed(2)}`;
        
        // 4. Monitoring métacognitif
        const temps_execution = Date.now() - debut;
        const evaluationuation = this.monitorerPerformance(tache, resultat, temps_execution);
        
        return {
            resultat: resultat,
            evaluationuation: evaluationuation,
            charge_cognitive: this.memoireTravail.charge_cognitive
        };
    }
}

module.exports = SystemeAttentionMetacognition;
