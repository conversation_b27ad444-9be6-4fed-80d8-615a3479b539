/**
 * Gestionnaire pour la formation IA
 * Permet de gérer la formation et l'évolution neuronale
 */

const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');
const fetch = require('node-fetch');

// Variables globales pour la formation
let thermalMemory = null;
let io = null;
let mcpSystem = null;
let trainingActive = false;
let trainingPaused = false;
let trainingInterval = null;
let trainingData = {
  stats: {
    iq: 100,
    neurons: 1000,
    learningSpeed: 25,
    evolutionTime: 0
  },
  modules: [
    {
      id: 'basic-knowledge',
      name: 'Connaissances de base',
      description: 'Acquisition des connaissances fondamentales et du raisonnement logique',
      active: false,
      pending: false,
      progress: 0,
      cycles: 0,
      time: '0h',
      priority: 'Haute'
    },
    {
      id: 'language-understanding',
      name: 'Compréhension du langage',
      description: 'Amélioration de la compréhension et de la génération du langage naturel',
      active: false,
      pending: false,
      progress: 0,
      cycles: 0,
      time: '0h',
      priority: 'Haute'
    },
    {
      id: 'problem-solving',
      name: 'Résolution de problèmes',
      description: 'Développement des capacités de résolution de problèmes complexes',
      active: false,
      pending: false,
      progress: 0,
      cycles: 0,
      time: '0h',
      priority: 'Moyenne'
    },
    {
      id: 'creative-thinking',
      name: 'Pensée créative',
      description: 'Stimulation de la créativité et de l\'innovation',
      active: false,
      pending: false,
      progress: 0,
      cycles: 0,
      time: '0h',
      priority: 'Moyenne'
    },
    {
      id: 'memory-optimization',
      name: 'Optimisation de la mémoire',
      description: 'Amélioration de la gestion et de l\'organisation de la mémoire',
      active: false,
      pending: false,
      progress: 0,
      cycles: 0,
      time: '0h',
      priority: 'Basse'
    },
    {
      id: 'self-awareness',
      name: 'Conscience de soi',
      description: 'Développement de la conscience de soi et de l\'introspection',
      active: false,
      pending: false,
      progress: 0,
      cycles: 0,
      time: '0h',
      priority: 'Basse'
    }
  ],
  details: {
    status: 'inactive',
    baseModel: 'deepseek-r1:7b',
    trainingModel: 'deepseek-r1:7b',
    completedCycles: 0,
    totalTime: 0,
    lastUpdate: null
  },
  chartData: {
    learningProgress: {
      labels: [],
      iq: [],
      learningSpeed: []
    },
    neuralEvolution: {
      labels: [],
      neurons: [],
      connections: []
    }
  }
};

// Fonction d'initialisation du module
function initThermalMemory(memory, socketIo, mcp) {
  thermalMemory = memory;
  io = socketIo;
  mcpSystem = mcp;
  
  // Charger les données de formation si elles existent
  loadTrainingData();
  
  // Initialiser les gestionnaires de socket
  initSocketHandlers();
  
  return router;
}

// Initialiser les gestionnaires de socket
function initSocketHandlers() {
  if (!io) return;
  
  io.on('connection', (socket) => {
    // Obtenir les données de formation
    socket.on('get training data', () => {
      socket.emit('training data', {
        success: true,
        stats: trainingData.stats,
        modules: trainingData.modules,
        details: trainingData.details,
        chartData: trainingData.chartData
      });
    });
    
    // Démarrer la formation
    socket.on('start training', async () => {
      try {
        if (trainingActive) {
          socket.emit('training started', {
            success: false,
            error: 'La formation est déjà en cours'
          });
          return;
        }
        
        // Démarrer la formation
        trainingActive = true;
        trainingPaused = false;
        trainingData.details.status = 'active';
        trainingData.details.lastUpdate = new Date().toISOString();
        
        // Activer les modules de formation
        activateTrainingModules();
        
        // Démarrer l'intervalle de formation
        startTrainingInterval();
        
        // Sauvegarder les données de formation
        saveTrainingData();
        
        socket.emit('training started', {
          success: true,
          message: 'Formation démarrée avec succès',
          details: trainingData.details
        });
        
        // Émettre à tous les clients
        io.emit('training progress', {
          log: 'Formation démarrée',
          logType: 'success',
          details: trainingData.details
        });
      } catch (error) {
        console.error('Erreur lors du démarrage de la formation:', error);
        socket.emit('training started', {
          success: false,
          error: 'Erreur lors du démarrage de la formation: ' + error.message
        });
      }
    });
    
    // Mettre en pause la formation
    socket.on('pause training', () => {
      try {
        if (!trainingActive) {
          socket.emit('training paused', {
            success: false,
            error: 'La formation n\'est pas en cours'
          });
          return;
        }
        
        if (trainingPaused) {
          socket.emit('training paused', {
            success: false,
            error: 'La formation est déjà en pause'
          });
          return;
        }
        
        // Mettre en pause la formation
        trainingPaused = true;
        trainingData.details.status = 'paused';
        trainingData.details.lastUpdate = new Date().toISOString();
        
        // Sauvegarder les données de formation
        saveTrainingData();
        
        socket.emit('training paused', {
          success: true,
          message: 'Formation mise en pause',
          details: trainingData.details
        });
        
        // Émettre à tous les clients
        io.emit('training progress', {
          log: 'Formation mise en pause',
          logType: 'info',
          details: trainingData.details
        });
      } catch (error) {
        console.error('Erreur lors de la mise en pause de la formation:', error);
        socket.emit('training paused', {
          success: false,
          error: 'Erreur lors de la mise en pause de la formation: ' + error.message
        });
      }
    });
    
    // Reprendre la formation
    socket.on('resume training', () => {
      try {
        if (!trainingActive) {
          socket.emit('training resumed', {
            success: false,
            error: 'La formation n\'est pas en cours'
          });
          return;
        }
        
        if (!trainingPaused) {
          socket.emit('training resumed', {
            success: false,
            error: 'La formation n\'est pas en pause'
          });
          return;
        }
        
        // Reprendre la formation
        trainingPaused = false;
        trainingData.details.status = 'active';
        trainingData.details.lastUpdate = new Date().toISOString();
        
        // Sauvegarder les données de formation
        saveTrainingData();
        
        socket.emit('training resumed', {
          success: true,
          message: 'Formation reprise',
          details: trainingData.details
        });
        
        // Émettre à tous les clients
        io.emit('training progress', {
          log: 'Formation reprise',
          logType: 'info',
          details: trainingData.details
        });
      } catch (error) {
        console.error('Erreur lors de la reprise de la formation:', error);
        socket.emit('training resumed', {
          success: false,
          error: 'Erreur lors de la reprise de la formation: ' + error.message
        });
      }
    });
    
    // Arrêter la formation
    socket.on('stop training', () => {
      try {
        if (!trainingActive) {
          socket.emit('training stopped', {
            success: false,
            error: 'La formation n\'est pas en cours'
          });
          return;
        }
        
        // Arrêter la formation
        stopTraining();
        
        socket.emit('training stopped', {
          success: true,
          message: 'Formation arrêtée',
          details: trainingData.details
        });
        
        // Émettre à tous les clients
        io.emit('training progress', {
          log: 'Formation arrêtée',
          logType: 'info',
          details: trainingData.details
        });
      } catch (error) {
        console.error('Erreur lors de l\'arrêt de la formation:', error);
        socket.emit('training stopped', {
          success: false,
          error: 'Erreur lors de l\'arrêt de la formation: ' + error.message
        });
      }
    });
    
    // Réinitialiser la formation
    socket.on('reset training', () => {
      try {
        // Arrêter la formation si elle est en cours
        if (trainingActive) {
          stopTraining();
        }
        
        // Réinitialiser les données de formation
        resetTrainingData();
        
        socket.emit('training reset', {
          success: true,
          message: 'Formation réinitialisée'
        });
        
        // Émettre à tous les clients
        io.emit('training progress', {
          log: 'Formation réinitialisée',
          logType: 'warning'
        });
      } catch (error) {
        console.error('Erreur lors de la réinitialisation de la formation:', error);
        socket.emit('training reset', {
          success: false,
          error: 'Erreur lors de la réinitialisation de la formation: ' + error.message
        });
      }
    });
  });
}

// Démarrer l'intervalle de formation
function startTrainingInterval() {
  // Arrêter l'intervalle existant s'il y en a un
  if (trainingInterval) {
    clearInterval(trainingInterval);
  }
  
  // Démarrer un nouvel intervalle
  trainingInterval = setInterval(() => {
    if (trainingActive && !trainingPaused) {
      updateTraining();
    }
  }, 5000); // Mise à jour toutes les 5 secondes
}

// Mettre à jour la formation
function updateTraining() {
  try {
    // Incrémenter le temps total
    trainingData.details.totalTime += 5;
    
    // Mettre à jour les modules actifs
    trainingData.modules.forEach(module => {
      if (module.active) {
        // Incrémenter la progression
        const progressIncrement = Math.random() * 0.5; // 0-0.5% par mise à jour
        module.progress = Math.min(100, module.progress + progressIncrement);
        
        // Incrémenter les cycles si la progression atteint un multiple de 10%
        if (Math.floor(module.progress / 10) > Math.floor((module.progress - progressIncrement) / 10)) {
          module.cycles++;
          trainingData.details.completedCycles++;
          
          // Émettre un événement de progression
          io.emit('training progress', {
            log: `Module "${module.name}" a complété un cycle de formation`,
            logType: 'success'
          });
        }
        
        // Mettre à jour le temps
        const hours = Math.floor(trainingData.details.totalTime / 3600);
        module.time = `${hours}h`;
        
        // Si le module est terminé, le désactiver et activer le suivant
        if (module.progress >= 100) {
          module.active = false;
          module.progress = 100;
          
          // Trouver le prochain module en attente
          const nextModule = trainingData.modules.find(m => m.pending);
          if (nextModule) {
            nextModule.active = true;
            nextModule.pending = false;
            
            // Émettre un événement de progression
            io.emit('training progress', {
              log: `Module "${module.name}" terminé. Démarrage du module "${nextModule.name}"`,
              logType: 'success'
            });
          } else {
            // Émettre un événement de progression
            io.emit('training progress', {
              log: `Module "${module.name}" terminé. Tous les modules sont terminés.`,
              logType: 'success'
            });
          }
        }
      }
    });
    
    // Mettre à jour les statistiques
    updateTrainingStats();
    
    // Mettre à jour les données du graphique
    updateChartData();
    
    // Mettre à jour la date de dernière mise à jour
    trainingData.details.lastUpdate = new Date().toISOString();
    
    // Sauvegarder les données de formation
    saveTrainingData();
    
    // Émettre un événement de progression
    io.emit('training progress', {
      stats: trainingData.stats,
      details: trainingData.details,
      chartData: trainingData.chartData
    });
  } catch (error) {
    console.error('Erreur lors de la mise à jour de la formation:', error);
  }
}

// Mettre à jour les statistiques de formation
function updateTrainingStats() {
  // Calculer le QI en fonction des cycles complétés
  const baseIQ = 100;
  const iqGain = Math.log(trainingData.details.completedCycles + 1) * 10;
  trainingData.stats.iq = Math.floor(baseIQ + iqGain);
  
  // Calculer le nombre de neurones en fonction des cycles complétés
  const baseNeurons = 1000;
  const neuronGain = trainingData.details.completedCycles * 100;
  trainingData.stats.neurons = Math.floor(baseNeurons + neuronGain);
  
  // Calculer la vitesse d'apprentissage en fonction des modules actifs
  const activeModules = trainingData.modules.filter(module => module.active);
  const baseSpeed = 25;
  const speedBonus = activeModules.length * 5;
  trainingData.stats.learningSpeed = Math.min(100, baseSpeed + speedBonus);
  
  // Calculer le temps d'évolution en heures
  trainingData.stats.evolutionTime = Math.floor(trainingData.details.totalTime / 3600);
}

// Mettre à jour les données du graphique
function updateChartData() {
  const now = new Date().toLocaleTimeString();
  
  // Ajouter les données au graphique de progression de l'apprentissage
  trainingData.chartData.learningProgress.labels.push(now);
  trainingData.chartData.learningProgress.iq.push(trainingData.stats.iq);
  trainingData.chartData.learningProgress.learningSpeed.push(trainingData.stats.learningSpeed);
  
  // Limiter le nombre de points à 20
  if (trainingData.chartData.learningProgress.labels.length > 20) {
    trainingData.chartData.learningProgress.labels.shift();
    trainingData.chartData.learningProgress.iq.shift();
    trainingData.chartData.learningProgress.learningSpeed.shift();
  }
  
  // Ajouter les données au graphique d'évolution neuronale
  trainingData.chartData.neuralEvolution.labels.push(now);
  trainingData.chartData.neuralEvolution.neurons.push(trainingData.stats.neurons);
  trainingData.chartData.neuralEvolution.connections.push(Math.floor(trainingData.stats.neurons * 1.5));
  
  // Limiter le nombre de points à 20
  if (trainingData.chartData.neuralEvolution.labels.length > 20) {
    trainingData.chartData.neuralEvolution.labels.shift();
    trainingData.chartData.neuralEvolution.neurons.shift();
    trainingData.chartData.neuralEvolution.connections.shift();
  }
}

// Activer les modules de formation
function activateTrainingModules() {
  // Activer le premier module et mettre les autres en attente
  let firstModuleActivated = false;
  
  trainingData.modules.forEach((module, index) => {
    if (index === 0 && !firstModuleActivated) {
      module.active = true;
      module.pending = false;
      firstModuleActivated = true;
    } else {
      module.active = false;
      module.pending = true;
    }
  });
}

// Arrêter la formation
function stopTraining() {
  // Arrêter l'intervalle de formation
  if (trainingInterval) {
    clearInterval(trainingInterval);
    trainingInterval = null;
  }
  
  // Mettre à jour les variables d'état
  trainingActive = false;
  trainingPaused = false;
  trainingData.details.status = 'inactive';
  trainingData.details.lastUpdate = new Date().toISOString();
  
  // Désactiver tous les modules
  trainingData.modules.forEach(module => {
    module.active = false;
    module.pending = false;
  });
  
  // Sauvegarder les données de formation
  saveTrainingData();
}

// Réinitialiser les données de formation
function resetTrainingData() {
  trainingData = {
    stats: {
      iq: 100,
      neurons: 1000,
      learningSpeed: 25,
      evolutionTime: 0
    },
    modules: [
      {
        id: 'basic-knowledge',
        name: 'Connaissances de base',
        description: 'Acquisition des connaissances fondamentales et du raisonnement logique',
        active: false,
        pending: false,
        progress: 0,
        cycles: 0,
        time: '0h',
        priority: 'Haute'
      },
      {
        id: 'language-understanding',
        name: 'Compréhension du langage',
        description: 'Amélioration de la compréhension et de la génération du langage naturel',
        active: false,
        pending: false,
        progress: 0,
        cycles: 0,
        time: '0h',
        priority: 'Haute'
      },
      {
        id: 'problem-solving',
        name: 'Résolution de problèmes',
        description: 'Développement des capacités de résolution de problèmes complexes',
        active: false,
        pending: false,
        progress: 0,
        cycles: 0,
        time: '0h',
        priority: 'Moyenne'
      },
      {
        id: 'creative-thinking',
        name: 'Pensée créative',
        description: 'Stimulation de la créativité et de l\'innovation',
        active: false,
        pending: false,
        progress: 0,
        cycles: 0,
        time: '0h',
        priority: 'Moyenne'
      },
      {
        id: 'memory-optimization',
        name: 'Optimisation de la mémoire',
        description: 'Amélioration de la gestion et de l\'organisation de la mémoire',
        active: false,
        pending: false,
        progress: 0,
        cycles: 0,
        time: '0h',
        priority: 'Basse'
      },
      {
        id: 'self-awareness',
        name: 'Conscience de soi',
        description: 'Développement de la conscience de soi et de l\'introspection',
        active: false,
        pending: false,
        progress: 0,
        cycles: 0,
        time: '0h',
        priority: 'Basse'
      }
    ],
    details: {
      status: 'inactive',
      baseModel: 'deepseek-r1:7b',
      trainingModel: 'deepseek-r1:7b',
      completedCycles: 0,
      totalTime: 0,
      lastUpdate: new Date().toISOString()
    },
    chartData: {
      learningProgress: {
        labels: [],
        iq: [],
        learningSpeed: []
      },
      neuralEvolution: {
        labels: [],
        neurons: [],
        connections: []
      }
    }
  };
  
  // Sauvegarder les données de formation
  saveTrainingData();
}

// Charger les données de formation
function loadTrainingData() {
  try {
    const dataPath = path.join(__dirname, '../data/training/training_data.json');
    
    // Créer le dossier s'il n'existe pas
    const dir = path.dirname(dataPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    // Vérifier si le fichier existe
    if (fs.existsSync(dataPath)) {
      const data = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
      trainingData = data;
      
      // Vérifier si la formation était active
      if (trainingData.details.status === 'active') {
        trainingActive = true;
        trainingPaused = false;
        startTrainingInterval();
      } else if (trainingData.details.status === 'paused') {
        trainingActive = true;
        trainingPaused = true;
      }
    } else {
      // Créer le fichier avec les données par défaut
      saveTrainingData();
    }
  } catch (error) {
    console.error('Erreur lors du chargement des données de formation:', error);
  }
}

// Sauvegarder les données de formation
function saveTrainingData() {
  try {
    const dataPath = path.join(__dirname, '../data/training/training_data.json');
    
    // Créer le dossier s'il n'existe pas
    const dir = path.dirname(dataPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    // Sauvegarder les données
    fs.writeFileSync(dataPath, JSON.stringify(trainingData, null, 2));
  } catch (error) {
    console.error('Erreur lors de la sauvegarde des données de formation:', error);
  }
}

// Routes API
router.get('/training/status', (req, res) => {
  res.json({
    success: true,
    status: trainingData.details.status,
    stats: trainingData.stats
  });
});

router.post('/training/start', (req, res) => {
  try {
    if (trainingActive) {
      return res.json({
        success: false,
        error: 'La formation est déjà en cours'
      });
    }
    
    // Démarrer la formation
    trainingActive = true;
    trainingPaused = false;
    trainingData.details.status = 'active';
    trainingData.details.lastUpdate = new Date().toISOString();
    
    // Activer les modules de formation
    activateTrainingModules();
    
    // Démarrer l'intervalle de formation
    startTrainingInterval();
    
    // Sauvegarder les données de formation
    saveTrainingData();
    
    // Émettre à tous les clients
    io.emit('training progress', {
      log: 'Formation démarrée via API',
      logType: 'success',
      details: trainingData.details
    });
    
    res.json({
      success: true,
      message: 'Formation démarrée avec succès'
    });
  } catch (error) {
    console.error('Erreur lors du démarrage de la formation via API:', error);
    res.json({
      success: false,
      error: 'Erreur lors du démarrage de la formation: ' + error.message
    });
  }
});

router.post('/training/stop', (req, res) => {
  try {
    if (!trainingActive) {
      return res.json({
        success: false,
        error: 'La formation n\'est pas en cours'
      });
    }
    
    // Arrêter la formation
    stopTraining();
    
    // Émettre à tous les clients
    io.emit('training progress', {
      log: 'Formation arrêtée via API',
      logType: 'info',
      details: trainingData.details
    });
    
    res.json({
      success: true,
      message: 'Formation arrêtée avec succès'
    });
  } catch (error) {
    console.error('Erreur lors de l\'arrêt de la formation via API:', error);
    res.json({
      success: false,
      error: 'Erreur lors de l\'arrêt de la formation: ' + error.message
    });
  }
});

module.exports = {
  router,
  initThermalMemory
};
