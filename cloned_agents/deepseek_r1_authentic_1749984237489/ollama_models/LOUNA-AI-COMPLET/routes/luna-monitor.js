/**
 * Gestionnaire pour la surveillance du système Luna
 * Surveille les performances du système et détecte les anomalies
 */

const express = require('express');
const router = express.Router();
const SystemMonitor = require('../modules/system-monitor');

// Variables globales
let systemMonitor = null;
let io = null;
let mcpSystem = null;

// Fonction d'initialisation du module
function initMonitorRouter(options = {}) {
  const { socketIo, mcp } = options;
  
  io = socketIo;
  mcpSystem = mcp;
  
  // Initialiser le gestionnaire de surveillance
  systemMonitor = new SystemMonitor({
    monitoringEnabled: true,
    monitoringInterval: 5000, // 5 secondes
    alertThresholds: {
      cpu: 80,
      memory: 80,
      disk: 90,
      temperature: 80
    },
    debug: true
  });
  
  // Écouter les événements du gestionnaire de surveillance
  setupMonitorEvents();
  
  // Initialiser les gestionnaires de socket
  if (io) {
    initSocketHandlers();
  }
  
  return router;
}

// Configurer les événements du gestionnaire de surveillance
function setupMonitorEvents() {
  if (!systemMonitor) return;
  
  // Événement d'initialisation
  systemMonitor.on('monitor:initialized', (data) => {
    if (io) {
      io.emit('monitor log', {
        message: 'Système de surveillance initialisé avec succès',
        level: 'success'
      });
    }
  });
  
  // Événement d'erreur
  systemMonitor.on('monitor:error', (data) => {
    if (io) {
      io.emit('monitor log', {
        message: `Erreur de surveillance: ${data.error}`,
        level: 'error'
      });
    }
  });
  
  // Événement de démarrage de la surveillance
  systemMonitor.on('monitor:started', () => {
    if (io) {
      io.emit('monitor started');
      
      io.emit('monitor log', {
        message: 'Surveillance démarrée',
        level: 'success'
      });
    }
  });
  
  // Événement d'arrêt de la surveillance
  systemMonitor.on('monitor:stopped', () => {
    if (io) {
      io.emit('monitor stopped');
      
      io.emit('monitor log', {
        message: 'Surveillance arrêtée',
        level: 'info'
      });
    }
  });
  
  // Événement de mise à jour des métriques
  systemMonitor.on('monitor:updated', (data) => {
    if (io) {
      io.emit('monitor updated', data);
    }
  });
  
  // Événement d'alertes
  systemMonitor.on('monitor:alerts', (data) => {
    if (io) {
      io.emit('monitor alerts', data);
      
      // Émettre un log pour chaque alerte
      data.alerts.forEach(alert => {
        io.emit('monitor log', {
          message: alert.message,
          level: alert.level
        });
      });
    }
  });
  
  // Événement de mise à jour des options
  systemMonitor.on('monitor:options-updated', (data) => {
    if (io) {
      io.emit('monitor options updated', {
        success: true,
        options: data.options
      });
      
      io.emit('monitor log', {
        message: 'Options de surveillance mises à jour',
        level: 'success'
      });
    }
  });
}

// Initialiser les gestionnaires de socket
function initSocketHandlers() {
  io.on('connection', (socket) => {
    // Obtenir l'état de surveillance
    socket.on('get monitor state', () => {
      if (!systemMonitor) {
        socket.emit('monitor state', {
          success: false,
          error: 'Gestionnaire de surveillance non initialisé'
        });
        return;
      }
      
      socket.emit('monitor state', {
        success: true,
        state: systemMonitor.getMonitorState()
      });
    });
    
    // Démarrer la surveillance
    socket.on('start monitoring', () => {
      if (!systemMonitor) {
        socket.emit('monitor started', {
          success: false,
          error: 'Gestionnaire de surveillance non initialisé'
        });
        return;
      }
      
      const success = systemMonitor.startMonitoring();
      
      if (!success) {
        socket.emit('monitor log', {
          message: 'La surveillance est déjà en cours',
          level: 'warning'
        });
      }
    });
    
    // Arrêter la surveillance
    socket.on('stop monitoring', () => {
      if (!systemMonitor) {
        socket.emit('monitor stopped', {
          success: false,
          error: 'Gestionnaire de surveillance non initialisé'
        });
        return;
      }
      
      const success = systemMonitor.stopMonitoring();
      
      if (!success) {
        socket.emit('monitor log', {
          message: 'La surveillance n\'est pas en cours',
          level: 'warning'
        });
      }
    });
    
    // Définir les options de surveillance
    socket.on('set monitor options', (options) => {
      if (!systemMonitor) {
        socket.emit('monitor options updated', {
          success: false,
          error: 'Gestionnaire de surveillance non initialisé'
        });
        return;
      }
      
      try {
        const success = systemMonitor.setMonitorOptions(options);
        
        if (!success) {
          socket.emit('monitor options updated', {
            success: false,
            error: 'Erreur lors de la mise à jour des options'
          });
        }
      } catch (error) {
        socket.emit('monitor options updated', {
          success: false,
          error: error.message
        });
      }
    });
    
    // Effacer les alertes
    socket.on('clear alerts', () => {
      if (!systemMonitor) {
        socket.emit('monitor alerts cleared', {
          success: false,
          error: 'Gestionnaire de surveillance non initialisé'
        });
        return;
      }
      
      const success = systemMonitor.clearAlerts();
      
      if (success) {
        socket.emit('monitor alerts cleared');
      } else {
        socket.emit('monitor log', {
          message: 'Erreur lors de l\'effacement des alertes',
          level: 'error'
        });
      }
    });
  });
}

// Routes API
router.get('/monitor/status', (req, res) => {
  if (!systemMonitor) {
    return res.json({
      success: false,
      error: 'Gestionnaire de surveillance non initialisé'
    });
  }
  
  res.json({
    success: true,
    state: systemMonitor.getMonitorState()
  });
});

router.post('/monitor/start', (req, res) => {
  if (!systemMonitor) {
    return res.json({
      success: false,
      error: 'Gestionnaire de surveillance non initialisé'
    });
  }
  
  const success = systemMonitor.startMonitoring();
  
  res.json({
    success: success,
    error: success ? null : 'La surveillance est déjà en cours'
  });
});

router.post('/monitor/stop', (req, res) => {
  if (!systemMonitor) {
    return res.json({
      success: false,
      error: 'Gestionnaire de surveillance non initialisé'
    });
  }
  
  const success = systemMonitor.stopMonitoring();
  
  res.json({
    success: success,
    error: success ? null : 'La surveillance n\'est pas en cours'
  });
});

router.post('/monitor/options', (req, res) => {
  if (!systemMonitor) {
    return res.json({
      success: false,
      error: 'Gestionnaire de surveillance non initialisé'
    });
  }
  
  try {
    const success = systemMonitor.setMonitorOptions(req.body);
    
    res.json({
      success: success,
      options: systemMonitor.options
    });
  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

router.post('/monitor/alerts/clear', (req, res) => {
  if (!systemMonitor) {
    return res.json({
      success: false,
      error: 'Gestionnaire de surveillance non initialisé'
    });
  }
  
  const success = systemMonitor.clearAlerts();
  
  res.json({
    success: success
  });
});

module.exports = {
  router,
  initMonitorRouter
};
