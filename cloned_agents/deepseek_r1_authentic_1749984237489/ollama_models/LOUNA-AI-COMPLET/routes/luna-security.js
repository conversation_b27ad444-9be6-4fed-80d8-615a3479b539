/**
 * Gestionnaire pour la sécurité Luna
 * Intègre la protection antivirus et la connexion VPN
 */

const express = require('express');
const router = express.Router();
const SecurityManager = require('../modules/security-manager');

// Variables globales
let securityManager = null;
let io = null;
let mcpSystem = null;
let chartData = {
  securityActivity: {
    labels: [],
    vpnActivity: [],
    firewallActivity: []
  },
  threats: {
    labels: [],
    detected: [],
    blocked: []
  }
};

// Fonction d'initialisation du module
function initSecurityRouter(options = {}) {
  const { socketIo, mcp } = options;

  io = socketIo;
  mcpSystem = mcp;

  // Initialiser le gestionnaire de sécurité
  securityManager = new SecurityManager({
    vpnEnabled: false, // Désactivé par défaut
    antivirusEnabled: true,
    firewallEnabled: true,
    vpnProvider: 'auto',
    vpnCountry: 'auto',
    scanInterval: 3600000, // 1 heure
    debug: true
  });

  // Écouter les événements du gestionnaire de sécurité
  setupSecurityEvents();

  // Initialiser les données du graphique
  initChartData();

  // Initialiser les gestionnaires de socket
  if (io) {
    initSocketHandlers();
  }

  return router;
}

// Configurer les événements du gestionnaire de sécurité
function setupSecurityEvents() {
  if (!securityManager) return;

  // Événement d'initialisation
  securityManager.on('security:initialized', (data) => {
    if (io) {
      io.emit('security log', {
        message: 'Système de sécurité initialisé avec succès',
        level: 'success'
      });
    }
  });

  // Événement d'erreur
  securityManager.on('security:error', (data) => {
    if (io) {
      io.emit('security log', {
        message: `Erreur de sécurité: ${data.error}`,
        level: 'error'
      });
    }
  });

  // Événement de connexion VPN
  securityManager.on('vpn:connected', (data) => {
    if (io) {
      io.emit('vpn connected', {
        success: true,
        ip: data.ip,
        country: data.country
      });

      io.emit('security log', {
        message: `VPN connecté: IP ${data.ip}, Pays ${data.country}`,
        level: 'success'
      });
    }

    // Mettre à jour les données du graphique
    updateActivityChart('vpn', 1);
  });

  // Événement de déconnexion VPN
  securityManager.on('vpn:disconnected', () => {
    if (io) {
      io.emit('vpn disconnected', {
        success: true
      });

      io.emit('security log', {
        message: 'VPN déconnecté',
        level: 'info'
      });
    }

    // Mettre à jour les données du graphique
    updateActivityChart('vpn', 0);
  });

  // Événement d'erreur VPN
  securityManager.on('vpn:error', (data) => {
    if (io) {
      io.emit('vpn connected', {
        success: false,
        error: data.error
      });

      io.emit('security log', {
        message: `Erreur VPN: ${data.error}`,
        level: 'error'
      });
    }
  });

  // Événement d'activation du pare-feu
  securityManager.on('firewall:enabled', () => {
    if (io) {
      io.emit('firewall toggled', {
        success: true,
        enabled: true
      });

      io.emit('security log', {
        message: 'Pare-feu activé',
        level: 'success'
      });
    }

    // Mettre à jour les données du graphique
    updateActivityChart('firewall', 1);
  });

  // Événement de désactivation du pare-feu
  securityManager.on('firewall:disabled', () => {
    if (io) {
      io.emit('firewall toggled', {
        success: true,
        enabled: false
      });

      io.emit('security log', {
        message: 'Pare-feu désactivé',
        level: 'warning'
      });
    }

    // Mettre à jour les données du graphique
    updateActivityChart('firewall', 0);
  });

  // Événement d'erreur du pare-feu
  securityManager.on('firewall:error', (data) => {
    if (io) {
      io.emit('firewall toggled', {
        success: false,
        error: data.error
      });

      io.emit('security log', {
        message: `Erreur pare-feu: ${data.error}`,
        level: 'error'
      });
    }
  });

  // Événement de début d'analyse antivirus
  securityManager.on('antivirus:scanning', (data) => {
    if (io) {
      io.emit('security scan started', {
        started: data.started
      });

      io.emit('security log', {
        message: 'Analyse antivirus démarrée',
        level: 'info'
      });
    }
  });

  // Événement de fin d'analyse antivirus
  securityManager.on('antivirus:completed', (results) => {
    if (io) {
      io.emit('security scan completed', {
        success: true,
        results: results
      });

      io.emit('security log', {
        message: `Analyse terminée: ${results.scannedFiles} fichiers analysés, ${results.threats} menaces détectées`,
        level: results.threats > 0 ? 'warning' : 'success'
      });
    }

    // Mettre à jour les données du graphique
    updateThreatsChart(results.threats, results.quarantined);
  });

  // Événement d'erreur d'analyse antivirus
  securityManager.on('antivirus:error', (data) => {
    if (io) {
      io.emit('security scan completed', {
        success: false,
        error: data.error
      });

      io.emit('security log', {
        message: `Erreur d'analyse antivirus: ${data.error}`,
        level: 'error'
      });
    }
  });

  // Événement de changement de niveau de sécurité
  securityManager.on('security:level-changed', (data) => {
    if (io) {
      io.emit('security level changed', {
        success: true,
        level: data.level
      });

      io.emit('security log', {
        message: `Niveau de sécurité défini sur: ${data.level}`,
        level: 'success'
      });
    }
  });
}

// Initialiser les données du graphique
function initChartData() {
  // Générer des données initiales pour les graphiques
  const now = new Date();
  const timeLabels = [];

  // Générer des labels pour les 24 dernières heures
  for (let i = 23; i >= 0; i--) {
    const time = new Date(now);
    time.setHours(now.getHours() - i);
    timeLabels.push(time.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }));
  }

  // Initialiser les données d'activité
  chartData.securityActivity.labels = timeLabels;
  chartData.securityActivity.vpnActivity = Array(24).fill(0);
  chartData.securityActivity.firewallActivity = Array(24).fill(0);

  // Initialiser les données de menaces
  chartData.threats.labels = timeLabels;
  chartData.threats.detected = Array(24).fill(0);
  chartData.threats.blocked = Array(24).fill(0);
}

// Mettre à jour le graphique d'activité
function updateActivityChart(type, value) {
  // Ajouter un nouveau point de données
  const now = new Date();
  const timeLabel = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

  // Si le dernier label est différent, ajouter un nouveau label
  if (chartData.securityActivity.labels[chartData.securityActivity.labels.length - 1] !== timeLabel) {
    chartData.securityActivity.labels.push(timeLabel);
    chartData.securityActivity.vpnActivity.push(type === 'vpn' ? value : chartData.securityActivity.vpnActivity[chartData.securityActivity.vpnActivity.length - 1]);
    chartData.securityActivity.firewallActivity.push(type === 'firewall' ? value : chartData.securityActivity.firewallActivity[chartData.securityActivity.firewallActivity.length - 1]);

    // Limiter à 24 points de données
    if (chartData.securityActivity.labels.length > 24) {
      chartData.securityActivity.labels.shift();
      chartData.securityActivity.vpnActivity.shift();
      chartData.securityActivity.firewallActivity.shift();
    }
  } else {
    // Mettre à jour le dernier point de données
    if (type === 'vpn') {
      chartData.securityActivity.vpnActivity[chartData.securityActivity.vpnActivity.length - 1] = value;
    } else if (type === 'firewall') {
      chartData.securityActivity.firewallActivity[chartData.securityActivity.firewallActivity.length - 1] = value;
    }
  }
}

// Mettre à jour le graphique des menaces
function updateThreatsChart(detected, blocked) {
  // Ajouter un nouveau point de données
  const now = new Date();
  const timeLabel = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

  // Si le dernier label est différent, ajouter un nouveau label
  if (chartData.threats.labels[chartData.threats.labels.length - 1] !== timeLabel) {
    chartData.threats.labels.push(timeLabel);
    chartData.threats.detected.push(detected);
    chartData.threats.blocked.push(blocked);

    // Limiter à 24 points de données
    if (chartData.threats.labels.length > 24) {
      chartData.threats.labels.shift();
      chartData.threats.detected.shift();
      chartData.threats.blocked.shift();
    }
  } else {
    // Mettre à jour le dernier point de données
    chartData.threats.detected[chartData.threats.detected.length - 1] = detected;
    chartData.threats.blocked[chartData.threats.blocked.length - 1] = blocked;
  }
}

// Initialiser les gestionnaires de socket
function initSocketHandlers() {
  io.on('connection', (socket) => {
    // Obtenir l'état de sécurité
    socket.on('get security state', () => {
      if (!securityManager) {
        socket.emit('security state', {
          success: false,
          error: 'Gestionnaire de sécurité non initialisé'
        });
        return;
      }

      socket.emit('security state', {
        success: true,
        state: securityManager.getSecurityState(),
        chartData: chartData
      });
    });

    // Démarrer une analyse de sécurité
    socket.on('start security scan', async () => {
      if (!securityManager) {
        socket.emit('security scan completed', {
          success: false,
          error: 'Gestionnaire de sécurité non initialisé'
        });
        return;
      }

      socket.emit('security scan started');

      try {
        const results = await securityManager.scanForThreats();

        socket.emit('security scan completed', {
          success: true,
          results: results,
          chartData: chartData
        });
      } catch (error) {
        socket.emit('security scan completed', {
          success: false,
          error: error.message
        });
      }
    });

    // Connecter au VPN
    socket.on('connect vpn', async () => {
      if (!securityManager) {
        socket.emit('vpn connected', {
          success: false,
          error: 'Gestionnaire de sécurité non initialisé'
        });
        return;
      }

      try {
        await securityManager.connectVPN();
      } catch (error) {
        socket.emit('vpn connected', {
          success: false,
          error: error.message
        });
      }
    });

    // Déconnecter du VPN
    socket.on('disconnect vpn', async () => {
      if (!securityManager) {
        socket.emit('vpn disconnected', {
          success: false,
          error: 'Gestionnaire de sécurité non initialisé'
        });
        return;
      }

      try {
        await securityManager.disconnectVPN();
      } catch (error) {
        socket.emit('vpn disconnected', {
          success: false,
          error: error.message
        });
      }
    });

    // Activer/désactiver l'antivirus
    socket.on('toggle antivirus', ({ enabled }) => {
      if (!securityManager) {
        socket.emit('antivirus toggled', {
          success: false,
          error: 'Gestionnaire de sécurité non initialisé'
        });
        return;
      }

      securityManager.options.antivirusEnabled = enabled;

      socket.emit('antivirus toggled', {
        success: true,
        enabled: enabled
      });

      socket.emit('security log', {
        message: `Antivirus ${enabled ? 'activé' : 'désactivé'}`,
        level: enabled ? 'success' : 'warning'
      });
    });

    // Activer/désactiver le pare-feu
    socket.on('toggle firewall', async ({ enabled }) => {
      if (!securityManager) {
        socket.emit('firewall toggled', {
          success: false,
          error: 'Gestionnaire de sécurité non initialisé'
        });
        return;
      }

      try {
        if (enabled) {
          await securityManager.enableFirewall();
        } else {
          await securityManager.disableFirewall();
        }
      } catch (error) {
        socket.emit('firewall toggled', {
          success: false,
          error: error.message
        });
      }
    });

    // Définir le niveau de sécurité
    socket.on('set security level', ({ level }) => {
      if (!securityManager) {
        socket.emit('security level changed', {
          success: false,
          error: 'Gestionnaire de sécurité non initialisé'
        });
        return;
      }

      const success = securityManager.setSecurityLevel(level);

      socket.emit('security level changed', {
        success: success,
        level: level,
        error: success ? null : 'Niveau de sécurité invalide'
      });
    });

    // Activer la protection par mot de passe
    socket.on('enable password protection', ({ password }) => {
      if (!securityManager) {
        socket.emit('security password protection', {
          success: false,
          error: 'Gestionnaire de sécurité non initialisé'
        });
        return;
      }

      try {
        const success = securityManager.enablePasswordProtection(password);

        socket.emit('security password protection', {
          success,
          enabled: true,
          error: success ? null : 'Erreur lors de l\'activation de la protection par mot de passe'
        });

        if (success) {
          socket.emit('security log', {
            message: 'Protection par mot de passe activée avec succès',
            level: 'success'
          });
        } else {
          socket.emit('security log', {
            message: 'Erreur lors de l\'activation de la protection par mot de passe',
            level: 'error'
          });
        }
      } catch (error) {
        socket.emit('security password protection', {
          success: false,
          error: error.message
        });
      }
    });

    // Vérifier un mot de passe
    socket.on('verify password', ({ password }) => {
      if (!securityManager) {
        socket.emit('security password verified', {
          success: false,
          error: 'Gestionnaire de sécurité non initialisé'
        });
        return;
      }

      try {
        const isValid = securityManager.verifyPassword(password);

        socket.emit('security password verified', {
          success: isValid,
          error: isValid ? null : 'Mot de passe incorrect'
        });
      } catch (error) {
        socket.emit('security password verified', {
          success: false,
          error: error.message
        });
      }
    });

    // Activer le chiffrement des données
    socket.on('enable data encryption', async () => {
      if (!securityManager) {
        socket.emit('security data encryption', {
          success: false,
          error: 'Gestionnaire de sécurité non initialisé'
        });
        return;
      }

      try {
        const success = await securityManager.enableDataEncryption();

        socket.emit('security data encryption', {
          success,
          enabled: true,
          error: success ? null : 'Erreur lors de l\'activation du chiffrement des données'
        });

        if (success) {
          socket.emit('security log', {
            message: 'Chiffrement des données activé avec succès',
            level: 'success'
          });
        }
      } catch (error) {
        socket.emit('security data encryption', {
          success: false,
          error: error.message
        });
      }
    });

    // Désactiver le chiffrement des données
    socket.on('disable data encryption', async () => {
      if (!securityManager) {
        socket.emit('security data encryption', {
          success: false,
          error: 'Gestionnaire de sécurité non initialisé'
        });
        return;
      }

      try {
        const success = await securityManager.disableDataEncryption();

        socket.emit('security data encryption', {
          success,
          enabled: false,
          error: success ? null : 'Erreur lors de la désactivation du chiffrement des données'
        });

        if (success) {
          socket.emit('security log', {
            message: 'Chiffrement des données désactivé',
            level: 'warning'
          });
        }
      } catch (error) {
        socket.emit('security data encryption', {
          success: false,
          error: error.message
        });
      }
    });

    // Activer l'analyse comportementale
    socket.on('enable behavioral analysis', async () => {
      if (!securityManager) {
        socket.emit('security behavioral analysis', {
          success: false,
          error: 'Gestionnaire de sécurité non initialisé'
        });
        return;
      }

      try {
        const success = await securityManager.enableBehavioralAnalysis();

        socket.emit('security behavioral analysis', {
          success,
          enabled: true,
          error: success ? null : 'Erreur lors de l\'activation de l\'analyse comportementale'
        });

        if (success) {
          socket.emit('security log', {
            message: 'Analyse comportementale activée avec succès',
            level: 'success'
          });
        }
      } catch (error) {
        socket.emit('security behavioral analysis', {
          success: false,
          error: error.message
        });
      }
    });

    // Désactiver l'analyse comportementale
    socket.on('disable behavioral analysis', async () => {
      if (!securityManager) {
        socket.emit('security behavioral analysis', {
          success: false,
          error: 'Gestionnaire de sécurité non initialisé'
        });
        return;
      }

      try {
        const success = await securityManager.disableBehavioralAnalysis();

        socket.emit('security behavioral analysis', {
          success,
          enabled: false,
          error: success ? null : 'Erreur lors de la désactivation de l\'analyse comportementale'
        });

        if (success) {
          socket.emit('security log', {
            message: 'Analyse comportementale désactivée',
            level: 'warning'
          });
        }
      } catch (error) {
        socket.emit('security behavioral analysis', {
          success: false,
          error: error.message
        });
      }
    });

    // Vérifier les mises à jour de sécurité
    socket.on('check security updates', async () => {
      if (!securityManager) {
        socket.emit('security updates', {
          success: false,
          error: 'Gestionnaire de sécurité non initialisé'
        });
        return;
      }

      try {
        socket.emit('security log', {
          message: 'Vérification des mises à jour de sécurité...',
          level: 'info'
        });

        const results = await securityManager.checkSecurityUpdates();

        socket.emit('security updates', {
          success: true,
          results,
          error: null
        });

        socket.emit('security log', {
          message: results.available ?
            `Mises à jour disponibles: version ${results.version}` :
            'Aucune mise à jour disponible',
          level: results.available ? (results.critical ? 'error' : 'warning') : 'success'
        });
      } catch (error) {
        socket.emit('security updates', {
          success: false,
          error: error.message
        });
      }
    });
  });
}

// Routes API
router.get('/security/status', (req, res) => {
  if (!securityManager) {
    return res.json({
      success: false,
      error: 'Gestionnaire de sécurité non initialisé'
    });
  }

  res.json({
    success: true,
    state: securityManager.getSecurityState()
  });
});

router.post('/security/scan', async (req, res) => {
  if (!securityManager) {
    return res.json({
      success: false,
      error: 'Gestionnaire de sécurité non initialisé'
    });
  }

  try {
    const results = await securityManager.scanForThreats();

    res.json({
      success: true,
      results: results
    });
  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

router.post('/security/vpn', async (req, res) => {
  if (!securityManager) {
    return res.json({
      success: false,
      error: 'Gestionnaire de sécurité non initialisé'
    });
  }

  const { action } = req.body;

  try {
    let result;

    if (action === 'connect') {
      result = await securityManager.connectVPN();
    } else if (action === 'disconnect') {
      result = await securityManager.disconnectVPN();
    } else {
      return res.json({
        success: false,
        error: 'Action invalide'
      });
    }

    res.json({
      success: true,
      result: result
    });
  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

router.post('/security/level', (req, res) => {
  if (!securityManager) {
    return res.json({
      success: false,
      error: 'Gestionnaire de sécurité non initialisé'
    });
  }

  const { level } = req.body;

  const success = securityManager.setSecurityLevel(level);

  res.json({
    success: success,
    level: level,
    error: success ? null : 'Niveau de sécurité invalide'
  });
});

router.post('/security/password-protection', (req, res) => {
  if (!securityManager) {
    return res.json({
      success: false,
      error: 'Gestionnaire de sécurité non initialisé'
    });
  }

  const { password } = req.body;

  if (!password) {
    return res.json({
      success: false,
      error: 'Mot de passe requis'
    });
  }

  try {
    const success = securityManager.enablePasswordProtection(password);

    res.json({
      success,
      enabled: true,
      error: success ? null : 'Erreur lors de l\'activation de la protection par mot de passe'
    });
  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

router.post('/security/verify-password', (req, res) => {
  if (!securityManager) {
    return res.json({
      success: false,
      error: 'Gestionnaire de sécurité non initialisé'
    });
  }

  const { password } = req.body;

  if (!password) {
    return res.json({
      success: false,
      error: 'Mot de passe requis'
    });
  }

  try {
    const isValid = securityManager.verifyPassword(password);

    res.json({
      success: isValid,
      error: isValid ? null : 'Mot de passe incorrect'
    });
  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

router.post('/security/data-encryption', (req, res) => {
  if (!securityManager) {
    return res.json({
      success: false,
      error: 'Gestionnaire de sécurité non initialisé'
    });
  }

  const { action } = req.body;

  if (!action || !['enable', 'disable'].includes(action)) {
    return res.json({
      success: false,
      error: 'Action invalide'
    });
  }

  try {
    let success;

    if (action === 'enable') {
      success = securityManager.enableDataEncryption();
    } else {
      success = securityManager.disableDataEncryption();
    }

    res.json({
      success,
      enabled: action === 'enable',
      error: success ? null : `Erreur lors de l'${action === 'enable' ? 'activation' : 'désactivation'} du chiffrement des données`
    });
  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

router.post('/security/behavioral-analysis', (req, res) => {
  if (!securityManager) {
    return res.json({
      success: false,
      error: 'Gestionnaire de sécurité non initialisé'
    });
  }

  const { action } = req.body;

  if (!action || !['enable', 'disable'].includes(action)) {
    return res.json({
      success: false,
      error: 'Action invalide'
    });
  }

  try {
    let success;

    if (action === 'enable') {
      success = securityManager.enableBehavioralAnalysis();
    } else {
      success = securityManager.disableBehavioralAnalysis();
    }

    res.json({
      success,
      enabled: action === 'enable',
      error: success ? null : `Erreur lors de l'${action === 'enable' ? 'activation' : 'désactivation'} de l'analyse comportementale`
    });
  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

router.get('/security/updates', async (req, res) => {
  if (!securityManager) {
    return res.json({
      success: false,
      error: 'Gestionnaire de sécurité non initialisé'
    });
  }

  try {
    const results = await securityManager.checkSecurityUpdates();

    res.json({
      success: true,
      results,
      error: null
    });
  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

module.exports = {
  router,
  initSecurityRouter
};
