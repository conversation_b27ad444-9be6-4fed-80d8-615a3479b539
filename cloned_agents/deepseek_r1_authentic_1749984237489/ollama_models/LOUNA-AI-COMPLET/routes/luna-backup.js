/**
 * Gestionnaire pour la sauvegarde Luna
 * Intègre des sauvegardes automatiques et chiffrées
 */

const express = require('express');
const router = express.Router();
const BackupManager = require('../modules/backup-manager');

// Variables globales
let backupManager = null;
let io = null;
let mcpSystem = null;

// Fonction d'initialisation du module
function initBackupRouter(options = {}) {
  const { socketIo, mcp } = options;
  
  io = socketIo;
  mcpSystem = mcp;
  
  // Initialiser le gestionnaire de sauvegarde
  backupManager = new BackupManager({
    backupEnabled: true,
    backupInterval: 3600000, // 1 heure
    maxBackups: 10,
    encryptBackups: true,
    compressionLevel: 'medium',
    externalBackupPath: '/Volumes/seagate/Jarvis_Working/backups',
    debug: true
  });
  
  // Écouter les événements du gestionnaire de sauvegarde
  setupBackupEvents();
  
  // Initialiser les gestionnaires de socket
  if (io) {
    initSocketHandlers();
  }
  
  return router;
}

// Configurer les événements du gestionnaire de sauvegarde
function setupBackupEvents() {
  if (!backupManager) return;
  
  // Événement d'initialisation
  backupManager.on('backup:initialized', (data) => {
    if (io) {
      io.emit('backup log', {
        message: 'Système de sauvegarde initialisé avec succès',
        level: 'success'
      });
    }
  });
  
  // Événement d'erreur
  backupManager.on('backup:error', (data) => {
    if (io) {
      io.emit('backup log', {
        message: `Erreur de sauvegarde: ${data.error}`,
        level: 'error'
      });
    }
  });
  
  // Événement de début de sauvegarde
  backupManager.on('backup:started', (data) => {
    if (io) {
      io.emit('backup started', {
        description: data.description,
        startTime: data.startTime
      });
      
      io.emit('backup log', {
        message: `Sauvegarde "${data.description}" démarrée`,
        level: 'info'
      });
    }
  });
  
  // Événement de fin de sauvegarde
  backupManager.on('backup:completed', (data) => {
    if (io) {
      io.emit('backup completed', {
        success: true,
        backup: data
      });
      
      io.emit('backup log', {
        message: `Sauvegarde terminée: ${data.fileName}`,
        level: 'success'
      });
    }
  });
  
  // Événement de mise à jour des options
  backupManager.on('backup:options-updated', (data) => {
    if (io) {
      io.emit('backup options updated', {
        success: true,
        options: data.options
      });
      
      io.emit('backup log', {
        message: 'Options de sauvegarde mises à jour',
        level: 'success'
      });
    }
  });
}

// Initialiser les gestionnaires de socket
function initSocketHandlers() {
  io.on('connection', (socket) => {
    // Obtenir l'état de sauvegarde
    socket.on('get backup state', () => {
      if (!backupManager) {
        socket.emit('backup state', {
          success: false,
          error: 'Gestionnaire de sauvegarde non initialisé'
        });
        return;
      }
      
      socket.emit('backup state', {
        success: true,
        state: backupManager.getBackupState()
      });
    });
    
    // Créer une sauvegarde
    socket.on('create backup', async (data) => {
      if (!backupManager) {
        socket.emit('backup completed', {
          success: false,
          error: 'Gestionnaire de sauvegarde non initialisé'
        });
        return;
      }
      
      try {
        // Simuler la progression
        let progress = 0;
        const progressInterval = setInterval(() => {
          progress += Math.floor(Math.random() * 5) + 1;
          if (progress > 95) {
            clearInterval(progressInterval);
            return;
          }
          
          socket.emit('backup progress', {
            progress,
            status: getProgressStatus(progress),
            details: getProgressDetails(progress)
          });
        }, 1000);
        
        // Créer la sauvegarde
        const backup = await backupManager.createBackup(data.description);
        
        // Arrêter la simulation de progression
        clearInterval(progressInterval);
        
        if (backup) {
          socket.emit('backup completed', {
            success: true,
            backup
          });
        } else {
          socket.emit('backup completed', {
            success: false,
            error: 'Erreur lors de la création de la sauvegarde'
          });
        }
      } catch (error) {
        socket.emit('backup completed', {
          success: false,
          error: error.message
        });
      }
    });
    
    // Annuler une sauvegarde
    socket.on('cancel backup', () => {
      if (!backupManager || !backupManager.state.backupInProgress) {
        return;
      }
      
      // Simuler l'annulation (dans un vrai système, il faudrait implémenter cette fonctionnalité)
      backupManager.state.backupInProgress = false;
      
      socket.emit('backup cancelled');
      
      socket.emit('backup log', {
        message: 'Sauvegarde annulée',
        level: 'warning'
      });
    });
    
    // Définir les options de sauvegarde
    socket.on('set backup options', (options) => {
      if (!backupManager) {
        socket.emit('backup options updated', {
          success: false,
          error: 'Gestionnaire de sauvegarde non initialisé'
        });
        return;
      }
      
      try {
        const success = backupManager.setBackupOptions(options);
        
        socket.emit('backup options updated', {
          success,
          options: backupManager.options
        });
      } catch (error) {
        socket.emit('backup options updated', {
          success: false,
          error: error.message
        });
      }
    });
    
    // Restaurer une sauvegarde
    socket.on('restore backup', async (data) => {
      if (!backupManager) {
        socket.emit('backup restored', {
          success: false,
          error: 'Gestionnaire de sauvegarde non initialisé'
        });
        return;
      }
      
      try {
        const success = await backupManager.restoreBackup(data.backupId);
        
        if (success) {
          const backup = backupManager.state.backupHistory.find(b => b.id === data.backupId);
          
          socket.emit('backup restored', {
            success: true,
            backup
          });
        } else {
          socket.emit('backup restored', {
            success: false,
            error: 'Erreur lors de la restauration de la sauvegarde'
          });
        }
      } catch (error) {
        socket.emit('backup restored', {
          success: false,
          error: error.message
        });
      }
    });
    
    // Supprimer une sauvegarde
    socket.on('delete backup', async (data) => {
      if (!backupManager) {
        socket.emit('backup deleted', {
          success: false,
          error: 'Gestionnaire de sauvegarde non initialisé'
        });
        return;
      }
      
      try {
        // Simuler la suppression (dans un vrai système, il faudrait implémenter cette fonctionnalité)
        const backupIndex = backupManager.state.backupHistory.findIndex(b => b.id === data.backupId);
        
        if (backupIndex === -1) {
          socket.emit('backup deleted', {
            success: false,
            error: 'Sauvegarde non trouvée'
          });
          return;
        }
        
        backupManager.state.backupHistory.splice(backupIndex, 1);
        backupManager.state.totalBackups = backupManager.state.backupHistory.length;
        
        socket.emit('backup deleted', {
          success: true,
          backupId: data.backupId
        });
      } catch (error) {
        socket.emit('backup deleted', {
          success: false,
          error: error.message
        });
      }
    });
  });
}

// Fonction pour obtenir le statut en fonction de la progression
function getProgressStatus(progress) {
  if (progress < 10) return 'Préparation...';
  if (progress < 30) return 'Compression...';
  if (progress < 60) return 'Sauvegarde...';
  if (progress < 80) return 'Chiffrement...';
  if (progress < 95) return 'Finalisation...';
  return 'Terminé';
}

// Fonction pour obtenir les détails en fonction de la progression
function getProgressDetails(progress) {
  if (progress < 10) return 'Préparation des fichiers pour la sauvegarde...';
  if (progress < 30) return 'Compression des fichiers...';
  if (progress < 60) return 'Sauvegarde des fichiers...';
  if (progress < 80) return 'Chiffrement de la sauvegarde...';
  if (progress < 95) return 'Finalisation de la sauvegarde...';
  return 'Sauvegarde terminée avec succès.';
}

// Routes API
router.get('/backup/status', (req, res) => {
  if (!backupManager) {
    return res.json({
      success: false,
      error: 'Gestionnaire de sauvegarde non initialisé'
    });
  }
  
  res.json({
    success: true,
    state: backupManager.getBackupState()
  });
});

router.post('/backup/create', async (req, res) => {
  if (!backupManager) {
    return res.json({
      success: false,
      error: 'Gestionnaire de sauvegarde non initialisé'
    });
  }
  
  try {
    const { description } = req.body;
    const backup = await backupManager.createBackup(description || 'Sauvegarde API');
    
    if (backup) {
      res.json({
        success: true,
        backup
      });
    } else {
      res.json({
        success: false,
        error: 'Erreur lors de la création de la sauvegarde'
      });
    }
  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

router.post('/backup/restore', async (req, res) => {
  if (!backupManager) {
    return res.json({
      success: false,
      error: 'Gestionnaire de sauvegarde non initialisé'
    });
  }
  
  try {
    const { backupId } = req.body;
    const success = await backupManager.restoreBackup(backupId);
    
    if (success) {
      const backup = backupManager.state.backupHistory.find(b => b.id === backupId);
      
      res.json({
        success: true,
        backup
      });
    } else {
      res.json({
        success: false,
        error: 'Erreur lors de la restauration de la sauvegarde'
      });
    }
  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

router.post('/backup/options', (req, res) => {
  if (!backupManager) {
    return res.json({
      success: false,
      error: 'Gestionnaire de sauvegarde non initialisé'
    });
  }
  
  try {
    const success = backupManager.setBackupOptions(req.body);
    
    res.json({
      success,
      options: backupManager.options
    });
  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

module.exports = {
  router,
  initBackupRouter
};
