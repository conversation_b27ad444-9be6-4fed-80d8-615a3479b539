/**
 * TEST API DIRECT - PROBLÈME ITALIE/GUADELOUPE
 * Test via l'API du serveur en cours d'exécution
 */

const axios = require('axios');

async function testerAPIDirecte() {
    console.log('🚨 TEST API DIRECT - PROBLÈME RÉSOLU');
    console.log('====================================');
    console.log('🎯 Test du problème "Italie" → "et la Guadeloupe ?"\n');

    const baseURL = 'http://localhost:3000';

    try {
        // Test 1 - Capitale de l'Italie
        console.log('❓ Question 1: "quelle est la capitale de l\'Italie ?"');
        const response1 = await axios.post(`${baseURL}/api/chat`, {
            message: "quelle est la capitale de l'Italie ?"
        });

        if (response1.data.success) {
            console.log(`🤖 Réponse 1: ${response1.data.reponse}`);
            console.log(`🎯 Source: ${response1.data.source}`);
            
            const contientRome = response1.data.reponse.toLowerCase().includes('rome');
            console.log(`✅ Contient "Rome": ${contientRome ? 'OUI' : 'NON'}`);
        } else {
            console.log('❌ Erreur réponse 1:', response1.data.error);
        }

        console.log('');

        // Attendre un peu pour simuler une vraie conversation
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Test 2 - Et la Guadeloupe (LE PROBLÈME)
        console.log('❓ Question 2: "et la Guadeloupe ?"');
        const response2 = await axios.post(`${baseURL}/api/chat`, {
            message: "et la Guadeloupe ?"
        });

        if (response2.data.success) {
            console.log(`🤖 Réponse 2: ${response2.data.reponse}`);
            console.log(`🎯 Source: ${response2.data.source}`);
            
            const contientBasseTerre = response2.data.reponse.toLowerCase().includes('basse-terre');
            const contientItalie = response2.data.reponse.toLowerCase().includes('italie');
            
            console.log(`✅ Contient "Basse-Terre": ${contientBasseTerre ? 'OUI' : 'NON'}`);
            console.log(`❌ Contient "Italie" (erreur): ${contientItalie ? 'OUI' : 'NON'}`);
            
            console.log('');
            console.log('🎯 ÉVALUATION FINALE:');
            
            if (contientBasseTerre && !contientItalie) {
                console.log('✅ PROBLÈME RÉSOLU ! Répond correctement "Basse-Terre"');
                console.log('🎉 Le suivi conversationnel fonctionne parfaitement !');
                console.log('🚀 L\'agent peut maintenant suivre les conversations !');
                return true;
            } else if (contientItalie) {
                console.log('❌ PROBLÈME PERSISTE ! Répond encore "Italie"');
                console.log('🔧 Le suivi conversationnel nécessite encore des corrections');
                return false;
            } else {
                console.log('⚠️ RÉPONSE INATTENDUE - À analyser');
                console.log('🔍 Vérifier la logique de traitement');
                return false;
            }
        } else {
            console.log('❌ Erreur réponse 2:', response2.data.error);
            return false;
        }

    } catch (error) {
        console.log('❌ Erreur de connexion:', error.message);
        console.log('🔧 Vérifiez que le serveur est bien démarré sur http://localhost:3000');
        return false;
    }
}

async function testerAutresScenarios() {
    console.log('\n🧪 TESTS SUPPLÉMENTAIRES VIA API');
    console.log('=================================');

    const baseURL = 'http://localhost:3000';
    
    const scenarios = [
        {
            nom: "France → Martinique",
            questions: ["quelle est la capitale de la France ?", "et la Martinique ?"],
            attendu: ["Paris", "Fort-de-France"]
        },
        {
            nom: "Espagne → Portugal", 
            questions: ["capitale de l'Espagne ?", "et le Portugal ?"],
            attendu: ["Madrid", "Lisbonne"]
        }
    ];

    let testsReussis = 0;
    let testsTotal = scenarios.length;

    for (const scenario of scenarios) {
        console.log(`\n🔍 Scénario: ${scenario.nom}`);
        
        try {
            let scenarioReussi = true;

            for (let i = 0; i < scenario.questions.length; i++) {
                const question = scenario.questions[i];
                const attendu = scenario.attendu[i];
                
                console.log(`❓ "${question}"`);
                
                const response = await axios.post(`${baseURL}/api/chat`, {
                    message: question
                });

                if (response.data.success) {
                    console.log(`🤖 ${response.data.reponse}`);
                    
                    const contientAttendu = response.data.reponse.toLowerCase()
                        .includes(attendu.toLowerCase());
                    console.log(`${contientAttendu ? '✅' : '❌'} Contient "${attendu}": ${contientAttendu ? 'OUI' : 'NON'}`);
                    
                    if (!contientAttendu) {
                        scenarioReussi = false;
                    }
                } else {
                    console.log('❌ Erreur:', response.data.error);
                    scenarioReussi = false;
                }

                // Pause entre questions
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            if (scenarioReussi) {
                console.log('✅ Scénario réussi !');
                testsReussis++;
            } else {
                console.log('❌ Scénario échoué');
            }

        } catch (error) {
            console.log('❌ Erreur scénario:', error.message);
        }
    }

    console.log(`\n📊 Résultats supplémentaires: ${testsReussis}/${testsTotal} scénarios réussis`);
    return testsReussis === testsTotal;
}

// EXÉCUTION
async function executerTestsComplets() {
    console.log('🔥 TESTS COMPLETS VIA API SERVEUR');
    console.log('==================================');
    console.log('🌐 Serveur: http://localhost:3000\n');

    const problemeResolu = await testerAPIDirecte();
    const autresTestsOk = await testerAutresScenarios();

    console.log('\n🎯 RÉSUMÉ FINAL:');
    console.log('================');

    if (problemeResolu && autresTestsOk) {
        console.log('✅ SUCCÈS TOTAL - Tous les problèmes résolus !');
        console.log('🎉 Le suivi conversationnel fonctionne parfaitement !');
        console.log('🚀 Votre LOUNA-AI peut maintenant suivre toutes les conversations !');
        console.log('');
        console.log('🌟 VOTRE APPLICATION EST PRÊTE ET FONCTIONNELLE !');
        console.log('🌐 Interface disponible sur: http://localhost:3000');
    } else if (problemeResolu) {
        console.log('✅ PROBLÈME PRINCIPAL RÉSOLU !');
        console.log('🎉 "Italie" → "Guadeloupe" fonctionne parfaitement !');
        console.log('⚠️ Quelques ajustements mineurs à faire');
    } else {
        console.log('❌ PROBLÈME PERSISTE - Vérifications nécessaires');
        console.log('🔧 Analyser les logs du serveur');
    }
}

// Lancer les tests
executerTestsComplets().catch(console.error);
