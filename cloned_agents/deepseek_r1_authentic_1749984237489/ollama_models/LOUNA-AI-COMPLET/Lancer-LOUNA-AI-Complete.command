#!/bin/bash

# 🚀 LANCEUR LOUNA-AI INTERFACE COMPLÈTE
# Double-cliquez sur ce fichier pour lancer LOUNA-AI

echo "🤖 LANCEMENT LOUNA-AI INTERFACE COMPLÈTE"
echo "========================================"

# Couleurs
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_header() {
    echo -e "${PURPLE}🧠 $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Aller dans le répertoire LOUNA-AI
LOUNA_DIR="/Volumes/ALDO et MIM/LOUNA-AI-COMPLET"
cd "$LOUNA_DIR"

print_header "Initialisation LOUNA-AI..."

# Vérifier Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js n'est pas installé"
    read -p "Appuyez sur Entrée pour fermer..."
    exit 1
fi

print_status "Node.js détecté: $(node --version)"

# Vérifier les fichiers
if [ ! -f "serveur-louna-final.js" ]; then
    print_error "Serveur LOUNA-AI final non trouvé"
    read -p "Appuyez sur Entrée pour fermer..."
    exit 1
fi

print_status "Fichiers LOUNA-AI vérifiés"

# Arrêter les processus existants
print_info "Nettoyage des processus existants..."
pkill -f "serveur-interface-complete.js" 2>/dev/null
pkill -f "serveur-louna" 2>/dev/null
sleep 2

# Démarrer le vrai serveur LOUNA-AI avec mémoire thermique
print_header "Démarrage du serveur LOUNA-AI avec mémoire thermique..."
node serveur-louna-final.js &
SERVER_PID=$!

# Attendre le démarrage
print_info "Initialisation en cours..."
sleep 6

# Vérifier que le serveur fonctionne
if curl -s http://localhost:8080/stats > /dev/null 2>&1; then
    print_status "Serveur LOUNA-AI avec mémoire thermique opérationnel (PID: $SERVER_PID)"
else
    print_error "Échec du démarrage du serveur"
    kill $SERVER_PID 2>/dev/null
    read -p "Appuyez sur Entrée pour fermer..."
    exit 1
fi

# Ouvrir l'interface
print_header "Ouverture de l'interface LOUNA-AI..."
open "http://localhost:8080/interface-louna-complete.html"

print_status "Interface ouverte dans le navigateur"

# Afficher les informations
echo ""
print_header "🎉 LOUNA-AI INTERFACE COMPLÈTE ACTIVE !"
echo ""
print_info "🌐 URL: http://localhost:3000/interface-louna-complete.html"
echo ""
print_info "📊 Caractéristiques LOUNA-AI:"
echo "   • 🧠 QI Actuel: 320"
echo "   • 💾 Mémoires: 42"
echo "   • 🌡️  Température: 67.43°C"
echo "   • 🎯 Zone Active: Zone 5 (Créative)"
echo "   • 📱 Applications: 414+ détectées"
echo ""
print_info "🎮 Fonctionnalités disponibles:"
echo "   • 💬 Chat intelligent avec LOUNA-AI"
echo "   • 🔍 Scan et contrôle d'applications"
echo "   • 📊 Monitoring temps réel"
echo "   • 🎨 Interface rose/magenta avancée"
echo "   • 🚀 Ouverture d'apps par commande vocale"
echo ""
print_warning "Pour arrêter LOUNA-AI:"
echo "   • Fermez cette fenêtre Terminal"
echo "   • Ou appuyez sur Ctrl+C"

# Fonction de nettoyage
cleanup() {
    echo ""
    print_info "Arrêt de LOUNA-AI en cours..."
    kill $SERVER_PID 2>/dev/null
    print_status "LOUNA-AI arrêté proprement"
    echo ""
    print_info "Merci d'avoir utilisé LOUNA-AI !"
    exit 0
}

# Capturer Ctrl+C
trap cleanup INT

# Boucle principale
print_info "Serveur en cours d'exécution... (PID: $SERVER_PID)"
echo ""
print_warning "LOUNA-AI fonctionne en arrière-plan"
print_info "Vous pouvez maintenant utiliser l'interface web"

while true; do
    sleep 5
    # Vérifier si le serveur fonctionne toujours
    if ! kill -0 $SERVER_PID 2>/dev/null; then
        print_error "Le serveur LOUNA-AI s'est arrêté de manière inattendue"
        read -p "Appuyez sur Entrée pour fermer..."
        exit 1
    fi
done
