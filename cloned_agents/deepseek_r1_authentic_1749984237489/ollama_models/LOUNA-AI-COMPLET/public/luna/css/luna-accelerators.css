/* Styles pour la page des accélérateurs Kyber */

.accelerators-dashboard {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  width: 100%;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.dashboard-header h2 {
  margin: 0;
  color: #2c3e50;
}

.dashboard-controls {
  display: flex;
  gap: 10px;
}

.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease-in-out;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.stat-card h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  color: #7f8c8d;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #2c3e50;
}

.progress-bar {
  height: 8px;
  background-color: #ecf0f1;
  border-radius: 4px;
  overflow: hidden;
}

.progress {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2980b9);
  border-radius: 4px;
  transition: width 0.5s ease-in-out;
}

.accelerator-types {
  margin-bottom: 20px;
}

.type-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
}

.type-button {
  padding: 8px 16px;
  background-color: #ecf0f1;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

.type-button:hover {
  background-color: #d5dbdb;
}

.type-button.active {
  background-color: #3498db;
  color: white;
}

.accelerators-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.accelerator-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.accelerator-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.accelerator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.accelerator-header h4 {
  margin: 0;
  font-size: 16px;
  color: #2c3e50;
}

.accelerator-status {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
}

.accelerator-status.active {
  background-color: #2ecc71;
  color: white;
}

.accelerator-status.inactive {
  background-color: #e74c3c;
  color: white;
}

.accelerator-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-bottom: 15px;
}

.stat {
  display: flex;
  flex-direction: column;
}

.stat-label {
  font-size: 12px;
  color: #7f8c8d;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #2c3e50;
}

.stat-value.high {
  color: #2ecc71;
}

.stat-value.normal {
  color: #3498db;
}

.stat-value.low {
  color: #e74c3c;
}

.stat-value.hot {
  color: #e74c3c;
}

.stat-value.warm {
  color: #f39c12;
}

.stat-value.cold {
  color: #3498db;
}

.accelerator-progress {
  margin-top: 10px;
}

.accelerator-details {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.accelerator-details h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #2c3e50;
}

.details-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #ecf0f1;
}

.detail-label {
  font-weight: 500;
  color: #7f8c8d;
}

.detail-value {
  font-weight: 500;
  color: #2c3e50;
}

.detail-value.active {
  color: #2ecc71;
}

.detail-value.inactive {
  color: #e74c3c;
}

.detail-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.accelerator-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.chart-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.chart-container h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #2c3e50;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  grid-column: 1 / -1;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3498db;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.no-data {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px;
  color: #7f8c8d;
  font-style: italic;
}

.error-message, .success-message {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 20px;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  opacity: 0;
  transform: translateY(-20px);
  transition: all 0.3s ease-in-out;
}

.error-message {
  background-color: #e74c3c;
  color: white;
}

.success-message {
  background-color: #2ecc71;
  color: white;
}

.error-message.show, .success-message.show {
  opacity: 1;
  transform: translateY(0);
}

/* Styles pour les boutons Luna */
.luna-button {
  padding: 8px 16px;
  background-color: #ecf0f1;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

.luna-button:hover {
  background-color: #d5dbdb;
}

.luna-button.primary {
  background-color: #3498db;
  color: white;
}

.luna-button.primary:hover {
  background-color: #2980b9;
}

.luna-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive design */
@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .dashboard-controls {
    width: 100%;
  }
  
  .accelerator-charts {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .dashboard-stats {
    grid-template-columns: 1fr;
  }
  
  .accelerators-grid {
    grid-template-columns: 1fr;
  }
  
  .type-selector {
    flex-direction: column;
  }
  
  .type-button {
    width: 100%;
  }
}
