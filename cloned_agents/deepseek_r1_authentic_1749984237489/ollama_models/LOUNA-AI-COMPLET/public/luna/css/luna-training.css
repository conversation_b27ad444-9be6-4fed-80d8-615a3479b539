/* Styles pour la page de formation Luna */

.training-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  width: 100%;
}

.training-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.training-header h2 {
  margin: 0;
  color: #2c3e50;
}

.training-controls {
  display: flex;
  gap: 10px;
}

.training-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease-in-out;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.stat-card h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  color: #7f8c8d;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #2c3e50;
}

.progress-bar {
  height: 8px;
  background-color: #ecf0f1;
  border-radius: 4px;
  overflow: hidden;
}

.progress {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2980b9);
  border-radius: 4px;
  transition: width 0.5s ease-in-out;
}

.training-modules {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.module-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.module-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.module-header h4 {
  margin: 0;
  font-size: 16px;
  color: #2c3e50;
}

.module-status {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
}

.module-status.active {
  background-color: #2ecc71;
  color: white;
}

.module-status.inactive {
  background-color: #e74c3c;
  color: white;
}

.module-status.pending {
  background-color: #f39c12;
  color: white;
}

.module-description {
  color: #7f8c8d;
  margin-bottom: 15px;
  font-size: 14px;
}

.module-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-bottom: 15px;
}

.module-stat {
  display: flex;
  flex-direction: column;
}

.module-stat-label {
  font-size: 12px;
  color: #7f8c8d;
  margin-bottom: 5px;
}

.module-stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #2c3e50;
}

.module-progress {
  margin-top: 10px;
}

.training-details {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.training-details h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #2c3e50;
}

.details-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #ecf0f1;
}

.detail-label {
  font-weight: 500;
  color: #7f8c8d;
}

.detail-value {
  font-weight: 500;
  color: #2c3e50;
}

.detail-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.training-log {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.training-log h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #2c3e50;
}

.log-container {
  background-color: #2c3e50;
  color: #ecf0f1;
  padding: 15px;
  border-radius: 4px;
  font-family: monospace;
  height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
}

.log-entry {
  margin-bottom: 5px;
  line-height: 1.5;
}

.log-entry.info {
  color: #3498db;
}

.log-entry.success {
  color: #2ecc71;
}

.log-entry.warning {
  color: #f39c12;
}

.log-entry.error {
  color: #e74c3c;
}

.training-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.chart-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.chart-container h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #2c3e50;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  grid-column: 1 / -1;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3498db;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.no-data {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px;
  color: #7f8c8d;
  font-style: italic;
}

.error-message, .success-message {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 20px;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  opacity: 0;
  transform: translateY(-20px);
  transition: all 0.3s ease-in-out;
}

.error-message {
  background-color: #e74c3c;
  color: white;
}

.success-message {
  background-color: #2ecc71;
  color: white;
}

.error-message.show, .success-message.show {
  opacity: 1;
  transform: translateY(0);
}

/* Styles pour les boutons Luna */
.luna-button {
  padding: 8px 16px;
  background-color: #ecf0f1;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

.luna-button:hover {
  background-color: #d5dbdb;
}

.luna-button.primary {
  background-color: #3498db;
  color: white;
}

.luna-button.primary:hover {
  background-color: #2980b9;
}

.luna-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive design */
@media (max-width: 768px) {
  .training-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .training-controls {
    width: 100%;
  }
  
  .training-charts {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .training-stats {
    grid-template-columns: 1fr;
  }
  
  .training-modules {
    grid-template-columns: 1fr;
  }
}
