/* Styles principaux pour l'interface Luna */

/* Réinitialisation et styles de base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f5f7fa;
  color: #2c3e50;
  line-height: 1.6;
}

.luna-body {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.luna-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

/* En-tête */
.luna-header {
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
}

.luna-logo {
  display: flex;
  align-items: center;
  gap: 15px;
}

.luna-logo img {
  height: 40px;
  width: auto;
}

.luna-logo h1 {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.luna-nav ul {
  display: flex;
  list-style: none;
  gap: 20px;
}

.luna-nav .nav-link {
  text-decoration: none;
  color: #7f8c8d;
  font-weight: 500;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.2s ease-in-out;
}

.luna-nav .nav-link:hover {
  color: #3498db;
  background-color: #f8f9fa;
}

.luna-nav .nav-link.active {
  color: #3498db;
  background-color: #edf2f7;
}

/* Contenu principal */
.luna-main {
  flex: 1;
  padding: 20px;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
}

/* Pied de page */
.luna-footer {
  background-color: #fff;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.luna-footer p {
  color: #7f8c8d;
  font-size: 14px;
}

.system-status {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #e74c3c;
}

.status-indicator.active {
  background-color: #2ecc71;
}

.status-text {
  font-size: 14px;
  color: #7f8c8d;
}

.version {
  font-size: 12px;
  color: #95a5a6;
}

/* Cartes et conteneurs */
.luna-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
  transition: transform 0.2s ease-in-out;
}

.luna-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.luna-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.luna-card-header h2 {
  margin: 0;
  font-size: 18px;
  color: #2c3e50;
}

.luna-card-content {
  color: #34495e;
}

/* Boutons */
.luna-button {
  padding: 8px 16px;
  background-color: #ecf0f1;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

.luna-button:hover {
  background-color: #d5dbdb;
}

.luna-button.primary {
  background-color: #3498db;
  color: white;
}

.luna-button.primary:hover {
  background-color: #2980b9;
}

.luna-button.success {
  background-color: #2ecc71;
  color: white;
}

.luna-button.success:hover {
  background-color: #27ae60;
}

.luna-button.danger {
  background-color: #e74c3c;
  color: white;
}

.luna-button.danger:hover {
  background-color: #c0392b;
}

.luna-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Formulaires */
.luna-form-group {
  margin-bottom: 15px;
}

.luna-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #2c3e50;
}

.luna-input,
.luna-textarea,
.luna-select {
  width: 100%;
  padding: 10px;
  border: 1px solid #dfe6e9;
  border-radius: 4px;
  font-family: inherit;
  font-size: 14px;
  transition: border-color 0.2s ease-in-out;
}

.luna-input:focus,
.luna-textarea:focus,
.luna-select:focus {
  outline: none;
  border-color: #3498db;
}

/* Alertes et notifications */
.luna-alert {
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.luna-alert.info {
  background-color: #d1ecf1;
  color: #0c5460;
}

.luna-alert.success {
  background-color: #d4edda;
  color: #155724;
}

.luna-alert.warning {
  background-color: #fff3cd;
  color: #856404;
}

.luna-alert.danger {
  background-color: #f8d7da;
  color: #721c24;
}

/* Badges */
.luna-badge {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.luna-badge.primary {
  background-color: #3498db;
  color: white;
}

.luna-badge.success {
  background-color: #2ecc71;
  color: white;
}

.luna-badge.warning {
  background-color: #f39c12;
  color: white;
}

.luna-badge.danger {
  background-color: #e74c3c;
  color: white;
}

/* Barres de progression */
.luna-progress {
  height: 8px;
  background-color: #ecf0f1;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
}

.luna-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2980b9);
  border-radius: 4px;
  transition: width 0.5s ease-in-out;
}

.luna-progress-label {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #7f8c8d;
}

/* Tableaux */
.luna-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.luna-table th,
.luna-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #dfe6e9;
}

.luna-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #2c3e50;
}

.luna-table tr:hover {
  background-color: #f8f9fa;
}

/* Responsive design */
@media (max-width: 768px) {
  .luna-header {
    flex-direction: column;
    padding: 10px;
  }
  
  .luna-logo {
    margin-bottom: 10px;
  }
  
  .luna-nav ul {
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
  }
  
  .luna-footer {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .luna-main {
    padding: 10px;
  }
  
  .luna-card {
    padding: 15px;
  }
  
  .luna-nav .nav-link {
    padding: 6px 8px;
    font-size: 14px;
  }
}
