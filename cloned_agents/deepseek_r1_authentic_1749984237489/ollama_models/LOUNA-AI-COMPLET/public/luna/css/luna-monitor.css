/* Styles pour la page de surveillance du système Luna */

.monitor-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  width: 100%;
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.monitor-header h2 {
  margin: 0;
  color: #2c3e50;
}

.monitor-controls {
  display: flex;
  gap: 10px;
}

.monitor-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.metric-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.metric-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.metric-card h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  color: #7f8c8d;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #2c3e50;
}

.metric-details {
  font-size: 14px;
  color: #7f8c8d;
}

.progress-bar {
  height: 8px;
  background-color: #ecf0f1;
  border-radius: 4px;
  overflow: hidden;
  margin-top: 10px;
}

.progress {
  height: 100%;
  border-radius: 4px;
  transition: width 0.5s ease-in-out;
}

.progress.normal {
  background: linear-gradient(90deg, #2ecc71, #27ae60);
}

.progress.warning {
  background: linear-gradient(90deg, #f39c12, #e67e22);
}

.progress.danger {
  background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.monitor-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.chart-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.chart-container h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #2c3e50;
}

.monitor-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.details-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.details-card h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #2c3e50;
}

.system-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #ecf0f1;
}

.info-label {
  font-weight: 500;
  color: #7f8c8d;
}

.info-value {
  font-weight: 500;
  color: #2c3e50;
}

.process-table {
  width: 100%;
  border-collapse: collapse;
}

.process-table th,
.process-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #ecf0f1;
}

.process-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #2c3e50;
}

.process-table tr:hover {
  background-color: #f8f9fa;
}

.alerts-list {
  max-height: 300px;
  overflow-y: auto;
}

.alert-item {
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.alert-item.warning {
  background-color: rgba(243, 156, 18, 0.1);
  border-left: 4px solid #f39c12;
}

.alert-item.error {
  background-color: rgba(231, 76, 60, 0.1);
  border-left: 4px solid #e74c3c;
}

.alert-item.info {
  background-color: rgba(52, 152, 219, 0.1);
  border-left: 4px solid #3498db;
}

.alert-content {
  flex: 1;
}

.alert-message {
  margin: 0 0 5px 0;
  font-weight: 500;
  color: #2c3e50;
}

.alert-timestamp {
  font-size: 12px;
  color: #7f8c8d;
}

.alert-actions {
  margin-left: 10px;
}

.monitor-settings {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.monitor-settings h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #2c3e50;
}

.settings-form {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #2c3e50;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 10px;
  border: 1px solid #dfe6e9;
  border-radius: 4px;
  font-family: inherit;
  font-size: 14px;
}

.form-group input[type="checkbox"] {
  width: auto;
  margin-right: 10px;
}

.form-actions {
  grid-column: 1 / -1;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.monitor-log {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.monitor-log h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #2c3e50;
}

.log-container {
  background-color: #2c3e50;
  color: #ecf0f1;
  padding: 15px;
  border-radius: 4px;
  font-family: monospace;
  height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
}

.log-entry {
  margin-bottom: 5px;
  line-height: 1.5;
}

.log-entry.info {
  color: #3498db;
}

.log-entry.success {
  color: #2ecc71;
}

.log-entry.warning {
  color: #f39c12;
}

.log-entry.error {
  color: #e74c3c;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  grid-column: 1 / -1;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3498db;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.no-data {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px;
  color: #7f8c8d;
  font-style: italic;
}

.error-message, .success-message {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 20px;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  opacity: 0;
  transform: translateY(-20px);
  transition: all 0.3s ease-in-out;
}

.error-message {
  background-color: #e74c3c;
  color: white;
}

.success-message {
  background-color: #2ecc71;
  color: white;
}

.error-message.show, .success-message.show {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive design */
@media (max-width: 768px) {
  .monitor-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .monitor-controls {
    width: 100%;
  }
  
  .monitor-charts {
    grid-template-columns: 1fr;
  }
  
  .monitor-details {
    grid-template-columns: 1fr;
  }
  
  .settings-form {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .monitor-overview {
    grid-template-columns: 1fr;
  }
  
  .system-info {
    grid-template-columns: 1fr;
  }
}
