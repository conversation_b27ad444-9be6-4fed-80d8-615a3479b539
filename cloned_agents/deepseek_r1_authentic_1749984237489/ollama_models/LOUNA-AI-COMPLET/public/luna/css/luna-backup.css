/* Styles pour la page de sauvegarde Luna */

.backup-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  width: 100%;
}

.backup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.backup-header h2 {
  margin: 0;
  color: #2c3e50;
}

.backup-controls {
  display: flex;
  gap: 10px;
}

.backup-status {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.status-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease-in-out;
}

.status-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.status-card h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  color: #7f8c8d;
}

.status-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #2c3e50;
}

.progress-bar {
  height: 8px;
  background-color: #ecf0f1;
  border-radius: 4px;
  overflow: hidden;
}

.progress {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2980b9);
  border-radius: 4px;
  transition: width 0.5s ease-in-out;
}

.backup-history {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.backup-history h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #2c3e50;
}

.history-table {
  width: 100%;
  border-collapse: collapse;
}

.history-table th,
.history-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #ecf0f1;
}

.history-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #2c3e50;
}

.history-table tr:hover {
  background-color: #f8f9fa;
}

.history-table .actions {
  display: flex;
  gap: 10px;
}

.backup-settings {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.backup-settings h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #2c3e50;
}

.settings-form {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #2c3e50;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 10px;
  border: 1px solid #dfe6e9;
  border-radius: 4px;
  font-family: inherit;
  font-size: 14px;
}

.form-group input[type="checkbox"] {
  width: auto;
  margin-right: 10px;
}

.form-actions {
  grid-column: 1 / -1;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.backup-log {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.backup-log h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #2c3e50;
}

.log-container {
  background-color: #2c3e50;
  color: #ecf0f1;
  padding: 15px;
  border-radius: 4px;
  font-family: monospace;
  height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
}

.log-entry {
  margin-bottom: 5px;
  line-height: 1.5;
}

.log-entry.info {
  color: #3498db;
}

.log-entry.success {
  color: #2ecc71;
}

.log-entry.warning {
  color: #f39c12;
}

.log-entry.error {
  color: #e74c3c;
}

.backup-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.chart-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.chart-container h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #2c3e50;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  grid-column: 1 / -1;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3498db;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.no-data {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px;
  color: #7f8c8d;
  font-style: italic;
}

.error-message, .success-message {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 20px;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  opacity: 0;
  transform: translateY(-20px);
  transition: all 0.3s ease-in-out;
}

.error-message {
  background-color: #e74c3c;
  color: white;
}

.success-message {
  background-color: #2ecc71;
  color: white;
}

.error-message.show, .success-message.show {
  opacity: 1;
  transform: translateY(0);
}

.backup-progress-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.backup-progress-card {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  width: 400px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.backup-progress-card h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #2c3e50;
}

.backup-progress-bar {
  height: 10px;
  background-color: #ecf0f1;
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 15px;
}

.backup-progress {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2980b9);
  border-radius: 5px;
  transition: width 0.3s ease-in-out;
}

.backup-progress-status {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.backup-progress-status .status {
  font-weight: bold;
}

.backup-progress-status .percentage {
  color: #3498db;
}

.backup-progress-details {
  font-size: 14px;
  color: #7f8c8d;
  margin-bottom: 20px;
}

/* Responsive design */
@media (max-width: 768px) {
  .backup-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .backup-controls {
    width: 100%;
  }
  
  .settings-form {
    grid-template-columns: 1fr;
  }
  
  .backup-charts {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .backup-status {
    grid-template-columns: 1fr;
  }
  
  .history-table {
    font-size: 12px;
  }
  
  .history-table th,
  .history-table td {
    padding: 8px;
  }
}
