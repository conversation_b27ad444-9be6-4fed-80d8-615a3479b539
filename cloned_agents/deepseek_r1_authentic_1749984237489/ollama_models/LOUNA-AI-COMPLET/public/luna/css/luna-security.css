/* Styles pour la page de sécurité <PERSON> */

.security-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  width: 100%;
}

.security-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.security-header h2 {
  margin: 0;
  color: #2c3e50;
}

.security-controls {
  display: flex;
  gap: 10px;
}

.security-status {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.status-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease-in-out;
  display: flex;
  flex-direction: column;
}

.status-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.status-icon {
  font-size: 24px;
  margin-bottom: 10px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ecf0f1;
  color: #7f8c8d;
}

.status-icon.active {
  background-color: #2ecc71;
  color: white;
}

.status-icon.inactive {
  background-color: #e74c3c;
  color: white;
}

.status-icon.warning {
  background-color: #f39c12;
  color: white;
}

.status-icon.success {
  background-color: #2ecc71;
  color: white;
}

.status-icon.danger {
  background-color: #e74c3c;
  color: white;
}

.status-info {
  flex: 1;
}

.status-info h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  color: #2c3e50;
}

.status-active {
  color: #2ecc71;
  font-weight: bold;
}

.status-inactive {
  color: #e74c3c;
  font-weight: bold;
}

.status-warning {
  color: #f39c12;
  font-weight: bold;
}

.status-success {
  color: #2ecc71;
  font-weight: bold;
}

.status-danger {
  color: #e74c3c;
  font-weight: bold;
}

.status-actions {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

.security-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.details-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.details-card h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #2c3e50;
}

.scan-result-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #ecf0f1;
}

.result-label {
  font-weight: 500;
  color: #7f8c8d;
}

.result-value {
  font-weight: 500;
  color: #2c3e50;
}

.quarantine-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.quarantine-item {
  padding: 10px;
  border-bottom: 1px solid #ecf0f1;
  color: #e74c3c;
}

.quarantine-item:last-child {
  border-bottom: none;
}

/* Styles pour le score de sécurité */
.score-display {
  font-size: 2.5rem;
  font-weight: bold;
  color: #2c3e50;
  text-align: center;
  margin-bottom: 10px;
}

.update-actions {
  margin-top: 15px;
  display: flex;
  justify-content: center;
}

.security-log {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.security-log h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #2c3e50;
}

.log-container {
  background-color: #2c3e50;
  color: #ecf0f1;
  padding: 15px;
  border-radius: 4px;
  font-family: monospace;
  height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
}

.log-entry {
  margin-bottom: 5px;
  line-height: 1.5;
}

.log-entry.info {
  color: #3498db;
}

.log-entry.success {
  color: #2ecc71;
}

.log-entry.warning {
  color: #f39c12;
}

.log-entry.error {
  color: #e74c3c;
}

.security-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.chart-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.chart-container h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #2c3e50;
}

/* Styles pour les statistiques de sécurité */
.security-stats {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.security-stats h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #2c3e50;
}

.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.stat-card {
  display: flex;
  align-items: flex-start;
  padding: 15px;
  border-radius: 8px;
  background-color: #f8f9fa;
  transition: transform 0.2s ease-in-out;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #3498db;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 18px;
}

.stat-info {
  flex: 1;
}

.stat-info h4 {
  margin-top: 0;
  margin-bottom: 8px;
  color: #2c3e50;
  font-size: 14px;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.stat-details {
  font-size: 12px;
  color: #7f8c8d;
}

.stat-progress {
  margin-top: 8px;
}

/* Styles pour les paramètres avancés */
.security-advanced {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.security-advanced h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #2c3e50;
}

.advanced-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.advanced-section {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.advanced-section h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #2c3e50;
  font-size: 16px;
  border-bottom: 1px solid #ecf0f1;
  padding-bottom: 10px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  color: #7f8c8d;
  font-size: 14px;
}

.luna-select, .luna-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
  color: #2c3e50;
  font-size: 14px;
}

.luna-select:focus, .luna-input:focus {
  border-color: #3498db;
  outline: none;
}

.checkbox-group {
  display: flex;
  align-items: center;
}

.luna-checkbox {
  margin-right: 10px;
}

.checkbox-group label {
  margin-bottom: 0;
  cursor: pointer;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  grid-column: 1 / -1;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3498db;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.no-data {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px;
  color: #7f8c8d;
  font-style: italic;
}

.error-message, .success-message {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 20px;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  opacity: 0;
  transform: translateY(-20px);
  transition: all 0.3s ease-in-out;
}

.error-message {
  background-color: #e74c3c;
  color: white;
}

.success-message {
  background-color: #2ecc71;
  color: white;
}

.error-message.show, .success-message.show {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive design */
@media (max-width: 768px) {
  .security-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .security-controls {
    width: 100%;
  }

  .security-details {
    grid-template-columns: 1fr;
  }

  .security-charts {
    grid-template-columns: 1fr;
  }
}

/* Styles pour la barre de progression */
.progress-bar {
  width: 100%;
  height: 10px;
  background-color: #e0e0e0;
}

/* Styles pour le pied de page fixe */
.footer-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 40px;
  background-color: #1a1a2e;
  border-top: 1px solid rgba(184, 190, 221, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  color: #edf2fb;
  border-radius: 5px;
  overflow: hidden;
  margin-top: 10px;
}

.progress {
  height: 100%;
  border-radius: 5px;
  transition: width 0.5s ease-in-out;
}

.progress.high {
  background-color: #2ecc71;
}

.progress.medium {
  background-color: #3498db;
}

.progress.low {
  background-color: #f39c12;
}

.progress.critical {
  background-color: #e74c3c;
}

/* Styles pour les badges */
.security-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: #3498db;
  color: white;
  font-size: 0.8rem;
  margin-left: 10px;
}

.security-badge.critical {
  background-color: #e74c3c;
}

/* Styles pour la fenêtre modale */
.security-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.security-modal-content {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  width: 80%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
}

.security-modal-close {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.security-modal-close:hover {
  color: #000;
}

@media (max-width: 480px) {
  .security-status {
    grid-template-columns: 1fr;
  }

  .security-modal-content {
    width: 95%;
    padding: 15px;
  }
}
