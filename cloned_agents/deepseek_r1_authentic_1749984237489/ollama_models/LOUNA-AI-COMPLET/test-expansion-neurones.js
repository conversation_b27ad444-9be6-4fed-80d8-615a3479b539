#!/usr/bin/env node

/**
 * TEST EXPANSION AUTOMATIQUE NEURONES INFINIS
 * Déclenche l'expansion automatique pour prouver que ça fonctionne
 */

console.log('🧪 TEST EXPANSION AUTOMATIQUE NEURONES INFINIS');
console.log('===============================================');

const { ThermalMemoryUltraAutomatique } = require('./thermal-memory-ultra-automatique-vrai');

async function testerExpansionAutomatique() {
    try {
        console.log('\n🔥 Initialisation mémoire thermique...');
        const memoire = new ThermalMemoryUltraAutomatique({
            kyber_auto_install: true,
            neurones_auto_install: true,
            neurones_max_limit: false,
            neurones_demand_threshold: 0.5 // Seuil plus bas pour test
        });

        await new Promise(resolve => setTimeout(resolve, 3000));

        console.log('\n📊 État initial:');
        let stats = memoire.getStats();
        console.log(`🧠 Neurones totaux: ${(stats.neurones_system.total_installed / 1000000).toFixed(1)}M`);
        console.log(`🧠 Neurones actifs: ${(stats.neurones_system.active_count / 1000000).toFixed(1)}M`);
        console.log(`⚡ Accélérateurs: ${stats.kyber_accelerators.installed}`);

        console.log('\n🚀 DÉCLENCHEMENT EXPANSION AUTOMATIQUE...');
        console.log('Ajout de données pour forcer l\'expansion...');

        // Ajouter beaucoup de données pour déclencher l'expansion automatique
        for (let i = 0; i < 20; i++) {
            memoire.add(`test_expansion_${i}`, `Données test ${i} pour déclencher expansion automatique neurones`, 0.9, 'test_expansion');
            
            // Vérifier expansion après chaque ajout
            if (i % 5 === 0) {
                const currentStats = memoire.getStats();
                console.log(`📈 Après ${i+1} ajouts: ${(currentStats.neurones_system.total_installed / 1000000).toFixed(1)}M neurones`);
            }
        }

        console.log('\n⏳ Attente expansion automatique...');
        await new Promise(resolve => setTimeout(resolve, 2000));

        console.log('\n📊 État final:');
        stats = memoire.getStats();
        console.log(`🧠 Neurones totaux: ${(stats.neurones_system.total_installed / 1000000).toFixed(1)}M`);
        console.log(`🧠 Neurones actifs: ${(stats.neurones_system.active_count / 1000000).toFixed(1)}M`);
        console.log(`🔢 Auto-installations neurones: ${stats.neurones_system.auto_installations}`);
        console.log(`⚡ Accélérateurs: ${stats.kyber_accelerators.installed}`);
        console.log(`🔢 Auto-installations KYBER: ${stats.kyber_accelerators.auto_installs}`);

        if (stats.neurones_system.auto_installations > 0) {
            console.log('\n✅ EXPANSION AUTOMATIQUE RÉUSSIE !');
            console.log('🧠 Les neurones s\'ajoutent automatiquement à l\'infini !');
            console.log('⚡ Les accélérateurs s\'ajoutent automatiquement !');
        } else {
            console.log('\n⚠️ Expansion pas encore déclenchée, continuons...');
            
            // Forcer plus d'activité
            for (let i = 0; i < 50; i++) {
                memoire.add(`force_${i}`, `Force expansion ${i}`, 0.95, 'urgent');
            }
            
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            const finalStats = memoire.getStats();
            console.log(`🧠 Neurones finaux: ${(finalStats.neurones_system.total_installed / 1000000).toFixed(1)}M`);
            console.log(`🔢 Auto-installations: ${finalStats.neurones_system.auto_installations}`);
        }

        return true;

    } catch (error) {
        console.log(`❌ ERREUR: ${error.message}`);
        return false;
    }
}

testerExpansionAutomatique().then(success => {
    console.log(success ? '\n🎉 TEST EXPANSION AUTOMATIQUE VALIDÉ !' : '\n💥 PROBLÈME DÉTECTÉ !');
    process.exit(success ? 0 : 1);
});
