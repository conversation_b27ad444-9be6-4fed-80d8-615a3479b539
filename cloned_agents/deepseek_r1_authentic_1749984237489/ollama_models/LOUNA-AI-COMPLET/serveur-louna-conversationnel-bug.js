/**
 * SERVEUR LOUNA-AI AVEC MÉMOIRE CONVERSATIONNELLE
 * Capable de suivre les conversations longues et le code complexe
 */

const express = require('express');
const path = require('path');
const { MoteurRaisonnementReel } = require('./moteur-raisonnement-reel');
const { MemoireThermiqueReelle } = require('./memoire-thermique-reelle');
const { AutoEvaluation } = require('./auto-evaluation');
const { MiseAJourTempsReel } = require('./mise-a-jour-temps-reel');
const { MemoireConversationnelle } = require('./memoire-conversationnelle');

const app = express();
const PORT = 8080;

// INITIALISATION DES SYSTÈMES COMPLETS
let moteurRaisonnement = null;
let memoireThermique = null;
let autoEvaluation = null;
let miseAJourTempsReel = null;
let memoireConversationnelle = null;

// Middleware
app.use(express.json());
app.use(express.static(__dirname));

console.log('🚀 INITIALISATION LOUNA-AI CONVERSATIONNEL COMPLET');
console.log('==================================================');

// Initialisation du moteur de raisonnement
try {
    moteurRaisonnement = new MoteurRaisonnementReel();
    console.log('🧠 Moteur de raisonnement conversationnel initialisé');
} catch (error) {
    console.error('❌ Erreur moteur raisonnement:', error.message);
}

// Initialisation de la mémoire thermique
try {
    memoireThermique = new MemoireThermiqueReelle();
    console.log('💾 Mémoire thermique conversationnelle initialisée');
} catch (error) {
    console.error('❌ Erreur mémoire thermique:', error.message);
}

// Initialisation auto-évaluation
try {
    autoEvaluation = new AutoEvaluation();
    console.log('🔍 Système d\'auto-évaluation initialisé');
} catch (error) {
    console.error('❌ Erreur auto-évaluation:', error.message);
}

// Initialisation mise à jour temps réel
try {
    miseAJourTempsReel = new MiseAJourTempsReel(memoireThermique);
    console.log('⚡ Système de mise à jour temps réel initialisé');
} catch (error) {
    console.error('❌ Erreur mise à jour temps réel:', error.message);
}

// Initialisation mémoire conversationnelle
try {
    memoireConversationnelle = new MemoireConversationnelle();
    console.log('💬 Mémoire conversationnelle initialisée');
} catch (error) {
    console.error('❌ Erreur mémoire conversationnelle:', error.message);
}

// ROUTE INTERFACE PRINCIPALE
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'interface-louna-grande.html'));
});

// ROUTE CHAT CONVERSATIONNEL COMPLET
app.post('/chat', async (req, res) => {
    try {
        const { message } = req.body;
        
        if (!message) {
            return res.json({
                success: false,
                error: 'Message requis'
            });
        }

        console.log(`💬 Question reçue: "${message}"`);

        // ÉTAPE 1: OBTENIR CONTEXTE CONVERSATIONNEL
        let contexteConversation = null;
        if (memoireConversationnelle) {
            contexteConversation = memoireConversationnelle.obtenirContexte(message);
            console.log(`📋 Contexte conversation:`, {
                sujet: contexteConversation?.sujet_principal,
                messages: contexteConversation?.nombre_messages,
                classes: contexteConversation?.classes_definies?.length || 0,
                etapes: contexteConversation?.etapes_projet?.length || 0
            });
        }

        // ÉTAPE 2: RECHERCHER DANS L'HISTORIQUE CONVERSATIONNEL
        let resultatsHistorique = [];
        if (memoireConversationnelle) {
            resultatsHistorique = memoireConversationnelle.rechercherDansHistorique(message, 3);
            console.log(`🔍 Historique conversation: ${resultatsHistorique.length} résultats`);
        }

        // ÉTAPE 3: RAISONNEMENT INTERNE AVEC CONTEXTE
        let resultatRaisonnement = null;
        if (moteurRaisonnement) {
            // Enrichir la question avec le contexte si disponible
            let questionEnrichie = message;
            if (contexteConversation && contexteConversation.derniere_action) {
                // questionEnrichie = `Contexte: ${contexteConversation.derniere_action.description}. Question actuelle: ${message}`;
            }
            
            resultatRaisonnement = moteurRaisonnement.penser(message);
            console.log(`🧠 Processus de pensée:`, resultatRaisonnement.processus);
        }

        // ÉTAPE 4: RECHERCHE MÉMOIRE THERMIQUE
        let resultatsMemoire = [];
        if (memoireThermique) {
            resultatsMemoire = memoireThermique.rechercher(message, 3);
            console.log(`💾 Résultats mémoire thermique: ${resultatsMemoire.length}`);
        }

        // ÉTAPE 5: DÉCISION DE RÉPONSE INTELLIGENTE
        let reponseFinale = null;
        let source = null;
        let sourceComplete = null;

        // Priorité 1: Raisonnement interne
        if (resultatRaisonnement && resultatRaisonnement.reponse) {
            reponseFinale = resultatRaisonnement.reponse;
            source = 'Raisonnement interne';
            sourceComplete = 'raisonnement_interne';
            console.log(`✅ Réponse par raisonnement interne`);
        }
        // Priorité 2: Historique conversationnel pertinent
        else if (resultatsHistorique.length > 0 && resultatsHistorique[0].pertinence > 0.7) {
            const messageHistorique = resultatsHistorique[0].message;
            reponseFinale = `Basé sur notre conversation précédente: ${messageHistorique.reponse}`;
            source = `Historique conversation (message ${messageHistorique.numero})`;
            sourceComplete = 'historique_conversation';
            console.log(`✅ Réponse par historique conversationnel`);
        }
        // Priorité 3: Mémoire thermique
        else if (resultatsMemoire.length > 0 && resultatsMemoire[0].pertinence > 0.3) {
            reponseFinale = resultatsMemoire[0].contenu;
            source = `Mémoire thermique (${resultatsMemoire[0].source})`;
            sourceComplete = 'memoire_thermique';
            console.log(`✅ Réponse par mémoire thermique`);
        }
        // Priorité 4: Réponse contextuelle
        else if (contexteConversation && contexteConversation.sujet_principal === 'programmation') {
            reponseFinale = "Je comprends que nous travaillons sur un projet de programmation. Pouvez-vous me donner plus de détails sur ce que vous souhaitez que je code ou modifie ?";
            source = 'Réponse contextuelle programmation';
            sourceComplete = 'contexte_programmation';
            console.log(`✅ Réponse contextuelle programmation`);
        }
        // Défaut
        else {
            reponseFinale = "Je ne trouve pas d'information pertinente dans ma mémoire, notre conversation ou mes connaissances internes pour répondre à cette question.";
            source = 'Réponse par défaut';
            sourceComplete = 'defaut';
            console.log(`❌ Aucune réponse trouvée`);
        }

        // CALCUL QI CONVERSATIONNEL
        let qiConversationnel = 127; // Base
        
        if (moteurRaisonnement) {
            const stats = moteurRaisonnement.getStatistiquesReelles();
            qiConversationnel += Math.min(stats.connaissances_base * 2, 40);
        }
        
        if (memoireThermique) {
            const statsMemoire = memoireThermique.getStatistiquesReelles();
            qiConversationnel += Math.min(statsMemoire.totalEntries, 30);
        }

        // Bonus pour contexte conversationnel
        if (contexteConversation) {
            qiConversationnel += Math.min(contexteConversation.nombre_messages * 0.5, 20);
            qiConversationnel += contexteConversation.classes_definies.length * 2;
            qiConversationnel += contexteConversation.etapes_projet.length * 3;
        }

        // Bonus pour type de réponse
        if (sourceComplete === 'raisonnement_interne') {
            qiConversationnel += 15;
        } else if (sourceComplete === 'historique_conversation') {
            qiConversationnel += 12;
        } else if (sourceComplete === 'memoire_thermique') {
            qiConversationnel += 8;
        } else if (sourceComplete === 'contexte_programmation') {
            qiConversationnel += 10;
        }

        console.log(`🧠 QI conversationnel calculé: ${qiConversationnel}`);

        // MISE À JOUR TEMPS RÉEL
        if (miseAJourTempsReel && sourceComplete !== 'defaut') {
            miseAJourTempsReel.renforcerMemoire(message, reponseFinale, source);
        }

        // ENREGISTRER DANS MÉMOIRE CONVERSATIONNELLE
        if (memoireConversationnelle) {
            memoireConversationnelle.ajouterMessage(message, reponseFinale, source, {
                qi: qiConversationnel,
                source_complete: sourceComplete,
                contexte_utilise: contexteConversation !== null
            });
        }

        // AUTO-ÉVALUATION PÉRIODIQUE
        let evaluation = null;
        if (autoEvaluation) {
            evaluation = autoEvaluation.enregistrerInteraction(message, reponseFinale, source, qiConversationnel);
            if (evaluation) {
                console.log(`📊 Auto-évaluation déclenchée:`, evaluation);
            }
        }

        // RÉPONSE FINALE CONVERSATIONNELLE
        const response = {
            success: true,
            response: reponseFinale,
            source: source,
            qi_actuel: qiConversationnel,
            memory_used: resultatsMemoire.length > 0,
            reasoning_used: resultatRaisonnement && resultatRaisonnement.reponse !== null,
            conversation_context: contexteConversation !== null,
            history_used: resultatsHistorique.length > 0,
            timestamp: Date.now()
        };

        // Ajouter évaluation si disponible
        if (evaluation) {
            response.auto_evaluation = evaluation;
        }

        res.json(response);

    } catch (error) {
        console.error('❌ Erreur traitement chat conversationnel:', error);
        res.json({
            success: false,
            error: 'Erreur interne du serveur conversationnel',
            details: error.message
        });
    }
});

// ROUTE STATISTIQUES CONVERSATIONNELLES
app.get('/stats', (req, res) => {
    try {
        let statsConversationnelles = {
            success: true,
            stats: {}
        };

        // Stats moteur raisonnement
        if (moteurRaisonnement) {
            statsConversationnelles.stats.moteur_raisonnement = moteurRaisonnement.getStatistiquesReelles();
        }

        // Stats mémoire thermique
        if (memoireThermique) {
            statsConversationnelles.stats.memoire_thermique = memoireThermique.getStatistiquesReelles();
        }

        // Stats auto-évaluation
        if (autoEvaluation) {
            statsConversationnelles.stats.auto_evaluation = autoEvaluation.getStats();
        }

        // Stats mise à jour temps réel
        if (miseAJourTempsReel) {
            statsConversationnelles.stats.mise_a_jour = miseAJourTempsReel.getStats();
        }

        // Stats mémoire conversationnelle
        if (memoireConversationnelle) {
            statsConversationnelles.stats.memoire_conversationnelle = memoireConversationnelle.getStats();
        }

        // Calcul QI conversationnel
        let qiConversationnel = 127;
        if (moteurRaisonnement) {
            qiConversationnel += Math.min(moteurRaisonnement.getStatistiquesReelles().connaissances_base * 2, 40);
        }
        if (memoireThermique) {
            qiConversationnel += Math.min(memoireThermique.getStatistiquesReelles().totalEntries, 30);
        }
        if (memoireConversationnelle) {
            const statsConv = memoireConversationnelle.getStats();
            qiConversationnel += Math.min(statsConv.nombre_messages * 0.5, 20);
            qiConversationnel += statsConv.classes_definies * 2;
            qiConversationnel += statsConv.etapes_projet * 3;
        }

        statsConversationnelles.coefficient_intellectuel = qiConversationnel;
        statsConversationnelles.timestamp = Date.now();

        res.json(statsConversationnelles);

    } catch (error) {
        console.error('❌ Erreur stats conversationnelles:', error);
        res.json({
            success: false,
            error: 'Erreur récupération statistiques conversationnelles'
        });
    }
});

// ROUTE NOUVELLE CONVERSATION
app.post('/nouvelle-conversation', (req, res) => {
    try {
        if (memoireConversationnelle) {
            const nouvelleId = memoireConversationnelle.demarrerNouvelleConversation();
            res.json({
                success: true,
                conversation_id: nouvelleId,
                message: 'Nouvelle conversation démarrée'
            });
        } else {
            res.json({
                success: false,
                error: 'Mémoire conversationnelle non disponible'
            });
        }
    } catch (error) {
        console.error('❌ Erreur nouvelle conversation:', error);
        res.json({
            success: false,
            error: 'Erreur création nouvelle conversation'
        });
    }
});

// ROUTE FORMATION CONVERSATIONNELLE
app.post('/formation', (req, res) => {
    try {
        const { sujet, contenu } = req.body;
        
        if (!sujet || !contenu) {
            return res.json({
                success: false,
                error: 'Sujet et contenu requis'
            });
        }

        console.log(`🎓 Formation conversationnelle: ${sujet}`);

        // Stocker en mémoire thermique avec importance maximale
        let memoireId = null;
        if (memoireThermique) {
            memoireId = memoireThermique.stocker(contenu, `Formation: ${sujet}`, 0.95);
        }

        // Apprendre dans le moteur de raisonnement
        let connaissanceId = null;
        if (moteurRaisonnement) {
            connaissanceId = moteurRaisonnement.apprendreNouvelleFait(contenu, `Formation: ${sujet}`);
        }

        res.json({
            success: true,
            message: `Formation conversationnelle "${sujet}" intégrée`,
            memoire_id: memoireId,
            connaissance_id: connaissanceId
        });

    } catch (error) {
        console.error('❌ Erreur formation conversationnelle:', error);
        res.json({
            success: false,
            error: 'Erreur pendant la formation conversationnelle'
        });
    }
});

// MAINTENANCE AUTOMATIQUE CONVERSATIONNELLE
setInterval(() => {
    if (memoireThermique) {
        memoireThermique.maintenance();
    }
    if (miseAJourTempsReel) {
        miseAJourTempsReel.optimiserOrganisation();
    }
}, 10 * 60 * 1000); // Toutes les 10 minutes

// DÉMARRAGE DU SERVEUR CONVERSATIONNEL
app.listen(PORT, () => {
    console.log('');
    console.log('✅ LOUNA-AI CONVERSATIONNEL COMPLET OPÉRATIONNEL');
    console.log('===============================================');
    console.log(`🔗 Interface: http://localhost:${PORT}`);
    console.log(`🧠 Moteur raisonnement: ${moteurRaisonnement ? 'CONVERSATIONNEL' : 'INACTIF'}`);
    console.log(`💾 Mémoire thermique: ${memoireThermique ? 'CONVERSATIONNELLE' : 'INACTIVE'}`);
    console.log(`🔍 Auto-évaluation: ${autoEvaluation ? 'ACTIVE' : 'INACTIVE'}`);
    console.log(`⚡ Mise à jour temps réel: ${miseAJourTempsReel ? 'ACTIVE' : 'INACTIVE'}`);
    console.log(`💬 Mémoire conversationnelle: ${memoireConversationnelle ? 'ACTIVE' : 'INACTIVE'}`);
    console.log('');
});
