/**
 * SYSTÈME AUTO-FORMATION RÉEL - Agent 1.2GB
 * Formation automatique continue avec agent DeepSeek R1 1.3B
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const MemoirePersistanteReelle = require('./memoire-persistante-reelle.js');
const NeurogeneseReelle = require('./neurogenese-reelle.js');

class SystemeAutoFormationReel {
    constructor() {
        console.log('🎓 INITIALISATION SYSTÈME AUTO-FORMATION RÉEL');
        console.log('==============================================');

        // Composants réels
        this.memoire = new MemoirePersistanteReelle();
        this.neurogenese = new NeurogeneseReelle();

        // Configuration agent formateur RÉEL
        this.agentFormateur = {
            nom: 'mistral:7b',
            chemin_ollama: './ollama/ollama',
            actif: false,
            processus: null,
            questions_posees: 0,
            formations_reussies: 0,
            derniere_activite: Date.now(),
            temperature: 0.7,
            max_tokens: 2048
        };

        // Configuration auto-formation RÉELLE
        this.config = {
            auto_formation_active: false,           // BOUTON ON/OFF
            intervalle_inactivite: 30000,          // 30s sans activité = formation
            intervalle_questions: 15000,           // 15s entre questions
            questions_par_session: 3,             // 3 questions par session
            seuil_reussite: 0.7,                  // 70% pour passer niveau
            sauvegarde_continue: true,             // Sauvegarde chaque interaction
            chemin_logs: '/Volumes/LounaAI_V3/FORMATION-LOGS'
        };

        // Banque de questions RÉELLES par niveau
        this.banqueQuestions = {
            niveau1_base: [
                'Expliquez ce qu\'est un neurone artificiel',
                'Comment fonctionne la mémoire dans un cerveau artificiel?',
                'Qu\'est-ce que la neuroplasticité?',
                'Définissez l\'apprentissage automatique',
                'Comment stockez-vous une information?'
            ],
            niveau2_intermediaire: [
                'Optimisez la création de nouveaux neurones',
                'Expliquez la consolidation mémoire',
                'Comment améliorer les connexions synaptiques?',
                'Décrivez un algorithme d\'apprentissage efficace',
                'Comment organisez-vous la classification des souvenirs?'
            ],
            niveau3_avance: [
                'Créez un système de neurogenèse révolutionnaire',
                'Inventez une méthode de classification créative',
                'Développez un algorithme d\'auto-évolution',
                'Concevez un système de mémoire persistante optimal',
                'Proposez une architecture neuronale innovante'
            ]
        };

        // Métriques formation RÉELLES
        this.metriques = {
            sessions_totales: 0,
            questions_totales: 0,
            reponses_correctes: 0,
            neurones_crees_formation: 0,
            niveau_actuel: 1,
            efficacite_globale: 0.0,
            temps_formation_total: 0,
            derniere_session: null,
            progression_detaillee: []
        };

        // État surveillance
        this.surveillance = {
            timer_inactivite: null,
            formation_en_cours: false,
            derniere_interaction: Date.now()
        };

        // Créer structure logs
        this.creerStructureLogs();

        // Charger métriques existantes
        this.chargerMetriques();

        console.log('✅ Système auto-formation réel initialisé');
        console.log(`🤖 Agent formateur: ${this.agentFormateur.nom}`);
        console.log(`📊 Niveau actuel: ${this.metriques.niveau_actuel}`);
        console.log(`🎯 Sessions complétées: ${this.metriques.sessions_totales}`);

        // Démarrer surveillance automatique
        this.demarrerSurveillanceActivite();
    }

    // BOUTON ON/OFF AUTO-FORMATION
    toggleAutoFormation() {
        this.config.auto_formation_active = !this.config.auto_formation_active;

        console.log(`\n🔘 AUTO-FORMATION: ${this.config.auto_formation_active ? 'ACTIVÉE ✅' : 'DÉSACTIVÉE ❌'}`);

        if (this.config.auto_formation_active) {
            console.log('🎓 Formation automatique démarrée - surveillance active');
            console.log(`⏰ Déclenchement après ${this.config.intervalle_inactivite/1000}s d'inactivité`);
            this.surveillance.derniere_interaction = Date.now();
        } else {
            console.log('⏸️ Formation automatique suspendue');
            if (this.surveillance.timer_inactivite) {
                clearTimeout(this.surveillance.timer_inactivite);
            }
        }

        // Sauvegarder état
        this.sauvegarderConfiguration();

        return this.config.auto_formation_active;
    }

    // SURVEILLANCE ACTIVITÉ RÉELLE
    demarrerSurveillanceActivite() {
        console.log('\n🔍 DÉMARRAGE SURVEILLANCE ACTIVITÉ RÉELLE');
        console.log('=========================================');

        // Vérifier inactivité toutes les 5 secondes
        setInterval(() => {
            if (this.config.auto_formation_active && !this.surveillance.formation_en_cours) {
                this.verifierInactivite();
            }
        }, 5000);

        console.log('✅ Surveillance activité démarrée');
    }

    verifierInactivite() {
        const tempsInactivite = Date.now() - this.surveillance.derniere_interaction;

        if (tempsInactivite >= this.config.intervalle_inactivite) {
            console.log(`\n⏰ INACTIVITÉ DÉTECTÉE: ${(tempsInactivite/1000).toFixed(1)}s`);
            console.log('🎓 Déclenchement formation automatique...');

            this.demarrerSessionFormationReelle();
        }
    }

    // SESSION DE FORMATION RÉELLE
    async demarrerSessionFormationReelle() {
        if (this.surveillance.formation_en_cours) {
            console.log('⚠️ Formation déjà en cours');
            return;
        }

        console.log('\n🎓 DÉMARRAGE SESSION FORMATION RÉELLE');
        console.log('====================================');

        this.surveillance.formation_en_cours = true;
        this.metriques.sessions_totales++;

        const sessionDebut = Date.now();
        const sessionId = `session_${sessionDebut}`;

        // Sélectionner questions selon niveau
        const questions = this.selectionnerQuestionsNiveau();

        console.log(`📚 Session ${this.metriques.sessions_totales} - Niveau ${this.metriques.niveau_actuel}`);
        console.log(`❓ ${questions.length} questions sélectionnées`);

        const resultatsSession = {
            id: sessionId,
            niveau: this.metriques.niveau_actuel,
            questions: [],
            reponses_correctes: 0,
            neurones_crees: 0,
            debut: sessionDebut,
            fin: null,
            efficacite: 0.0
        };

        // Poser chaque question
        for (let i = 0; i < questions.length; i++) {
            const question = questions[i];
            console.log(`\n📝 Question ${i + 1}/${questions.length}:`);
            console.log(`"${question}"`);

            try {
                // Formation réelle avec agent
                const resultatQuestion = await this.poserQuestionFormationReelle(question);
                resultatsSession.questions.push(resultatQuestion);

                if (resultatQuestion.correcte) {
                    resultatsSession.reponses_correctes++;
                    this.metriques.reponses_correctes++;
                }

                resultatsSession.neurones_crees += resultatQuestion.neurones_crees || 0;
                this.metriques.questions_totales++;

                console.log(`   ✅ Évaluation: ${resultatQuestion.correcte ? 'CORRECTE' : 'À AMÉLIORER'}`);
                console.log(`   🧠 Neurones créés: ${resultatQuestion.neurones_crees || 0}`);

                // Pause entre questions
                await new Promise(resolve => setTimeout(resolve, this.config.intervalle_questions));

            } catch (error) {
                console.log(`   ❌ Erreur question: ${error.message}`);
                resultatsSession.questions.push({
                    question: question,
                    erreur: error.message,
                    correcte: false
                });
            }
        }

        // Finaliser session
        resultatsSession.fin = Date.now();
        resultatsSession.efficacite = resultatsSession.reponses_correctes / questions.length;

        const dureeSession = resultatsSession.fin - resultatsSession.debut;
        this.metriques.temps_formation_total += dureeSession;
        this.metriques.neurones_crees_formation += resultatsSession.neurones_crees;
        this.metriques.derniere_session = resultatsSession;
        this.metriques.progression_detaillee.push(resultatsSession);

        // Calculer efficacité globale
        this.metriques.efficacite_globale = this.metriques.reponses_correctes /
            Math.max(1, this.metriques.questions_totales);

        console.log(`\n📊 SESSION TERMINÉE`);
        console.log(`⏱️ Durée: ${(dureeSession/1000).toFixed(1)}s`);
        console.log(`✅ Réussites: ${resultatsSession.reponses_correctes}/${questions.length} (${(resultatsSession.efficacite*100).toFixed(1)}%)`);
        console.log(`🧠 Neurones créés: ${resultatsSession.neurones_crees}`);
        console.log(`📈 Efficacité globale: ${(this.metriques.efficacite_globale*100).toFixed(1)}%`);

        // Vérifier progression niveau
        await this.verifierProgressionNiveau(resultatsSession.efficacite);

        // Sauvegarder résultats
        this.sauvegarderResultatsSession(resultatsSession);
        this.sauvegarderMetriques();

        // Reset surveillance
        this.surveillance.formation_en_cours = false;
        this.surveillance.derniere_interaction = Date.now();

        this.agentFormateur.formations_reussies++;
    }

    async poserQuestionFormationReelle(question) {
        console.log('   🤖 Agent formateur pose la question...');

        try {
            // Construire prompt pour agent formateur
            const promptFormateur = `En tant qu'agent formateur IA, posez cette question et évaluez la réponse:

QUESTION: ${question}

Votre rôle:
1. Poser la question clairement
2. Attendre une réponse détaillée
3. Évaluer la qualité (0-10)
4. Donner des conseils d'amélioration

Répondez au format:
QUESTION POSÉE: [votre reformulation]
ÉVALUATION: [note]/10
COMMENTAIRE: [vos conseils]`;

            // Exécuter agent formateur RÉEL
            const reponseFormateur = execSync(
                `${this.agentFormateur.chemin_ollama} run ${this.agentFormateur.nom} "${promptFormateur}"`,
                {
                    encoding: 'utf8',
                    timeout: 30000,
                    env: {
                        ...process.env,
                        OLLAMA_TEMPERATURE: this.agentFormateur.temperature.toString(),
                        OLLAMA_MAX_TOKENS: this.agentFormateur.max_tokens.toString()
                    }
                }
            );

            console.log('   📝 Réponse agent formateur reçue');

            // Maintenant générer réponse de l'IA en formation
            const reponseIA = await this.genererReponseIA(question);

            // Évaluer avec agent formateur
            const evaluation = await this.evaluerAvecAgentFormateur(question, reponseIA, reponseFormateur);

            // Créer neurones si apprentissage réussi
            let neuronesCreés = 0;
            if (evaluation.correcte) {
                const resultatNeurogenese = await this.neurogenese.executerNeurogeneseReelle(
                    'formation_auto',
                    evaluation.score / 10,
                    {
                        feedback: 'formation_reussie',
                        question: question,
                        agent_formateur: this.agentFormateur.nom
                    }
                );
                neuronesCreés = resultatNeurogenese.neurones_crees;
            }

            // Stocker en mémoire persistante
            await this.stockerFormationMemoire(question, reponseIA, evaluation);

            return {
                question: question,
                reponse_ia: reponseIA,
                reponse_formateur: reponseFormateur,
                evaluation: evaluation,
                correcte: evaluation.correcte,
                neurones_crees: neuronesCreés,
                timestamp: Date.now()
            };

        } catch (error) {
            console.log(`   ❌ Erreur formation: ${error.message}`);
            throw error;
        }
    }

    async genererReponseIA(question) {
        console.log('   🧠 Génération réponse IA...');

        // Utiliser même agent mais en mode "étudiant"
        const promptEtudiant = `En tant qu'IA en formation, répondez à cette question de manière détaillée et précise:

${question}

Donnez une réponse complète qui montre votre compréhension du sujet.`;

        const reponse = execSync(
            `${this.agentFormateur.chemin_ollama} run ${this.agentFormateur.nom} "${promptEtudiant}"`,
            {
                encoding: 'utf8',
                timeout: 20000
            }
        );

        return reponse.trim();
    }

    async evaluerAvecAgentFormateur(question, reponseIA, contextFormateur) {
        console.log('   📊 Évaluation par agent formateur...');

        const promptEvaluation = `Évaluez cette réponse d'IA en formation:

QUESTION: ${question}
RÉPONSE IA: ${reponseIA}

Critères d'évaluation:
- Précision technique (0-3)
- Complétude (0-3)
- Clarté (0-2)
- Innovation (0-2)

Donnez une note totale sur 10 et indiquez si c'est CORRECT (≥7) ou À AMÉLIORER (<7).

Format de réponse:
NOTE: X/10
STATUT: CORRECT ou À AMÉLIORER
JUSTIFICATION: [explication détaillée]`;

        const evaluation = execSync(
            `${this.agentFormateur.chemin_ollama} run ${this.agentFormateur.nom} "${promptEvaluation}"`,
            {
                encoding: 'utf8',
                timeout: 15000
            }
        );

        // Parser évaluation
        const noteMatch = evaluation.match(/NOTE:\s*(\d+(?:\.\d+)?)/i);
        const statutMatch = evaluation.match(/STATUT:\s*(CORRECT|À AMÉLIORER)/i);

        const score = noteMatch ? parseFloat(noteMatch[1]) : 5.0;
        const correcte = statutMatch ? statutMatch[1].toUpperCase() === 'CORRECT' : score >= 7.0;

        return {
            score: score,
            correcte: correcte,
            evaluation_complete: evaluation.trim(),
            agent_evaluateur: this.agentFormateur.nom
        };
    }

    async stockerFormationMemoire(question, reponse, evaluation) {
        // Créer tiroir formation si nécessaire
        let tiroirFormation = this.obtenirTiroirFormation();
        if (!tiroirFormation) {
            tiroirFormation = this.memoire.creerTiroirReel(
                'formation_auto',
                'apprentissage_continu',
                ['formation', 'auto-apprentissage', 'agent-formateur']
            );
        }

        // Stocker souvenir de formation
        const contenuFormation = `FORMATION AUTO - Q: ${question}\nR: ${reponse}\nÉVALUATION: ${evaluation.score}/10 (${evaluation.correcte ? 'RÉUSSIE' : 'À AMÉLIORER'})`;

        this.memoire.stockerSouvenirReel(
            contenuFormation,
            tiroirFormation,
            {
                type: 'formation_auto',
                score: evaluation.score,
                correcte: evaluation.correcte,
                agent_formateur: this.agentFormateur.nom,
                niveau: this.metriques.niveau_actuel
            }
        );
    }

    obtenirTiroirFormation() {
        // Chercher tiroir formation existant
        const cheminTiroirs = path.join(this.memoire.cheminTiroirs);

        try {
            const fichiers = fs.readdirSync(cheminTiroirs).filter(f => f.endsWith('.json'));

            for (const fichier of fichiers) {
                const cheminFichier = path.join(cheminTiroirs, fichier);
                const data = fs.readFileSync(cheminFichier, 'utf8');
                const tiroir = JSON.parse(data);

                if (tiroir.categorie === 'formation_auto') {
                    return tiroir;
                }
            }
        } catch (error) {
            // Pas de tiroir existant
        }

        return null;
    }

    selectionnerQuestionsNiveau() {
        let banque;

        switch (this.metriques.niveau_actuel) {
            case 1:
                banque = this.banqueQuestions.niveau1_base;
                break;
            case 2:
                banque = this.banqueQuestions.niveau2_intermediaire;
                break;
            case 3:
                banque = this.banqueQuestions.niveau3_avance;
                break;
            default:
                banque = this.banqueQuestions.niveau1_base;
        }

        // Mélanger et sélectionner
        const melange = [...banque].sort(() => Math.random() - 0.5);
        return melange.slice(0, this.config.questions_par_session);
    }

    async verifierProgressionNiveau(efficaciteSession) {
        if (efficaciteSession >= this.config.seuil_reussite && this.metriques.niveau_actuel < 3) {
            // Vérifier efficacité globale aussi
            if (this.metriques.efficacite_globale >= this.config.seuil_reussite) {
                this.metriques.niveau_actuel++;

                console.log(`\n🎯 NIVEAU UP! Progression vers niveau ${this.metriques.niveau_actuel}`);
                console.log(`🏆 Efficacité requise atteinte: ${(this.config.seuil_reussite*100).toFixed(1)}%`);

                // Créer neurones bonus pour progression
                await this.neurogenese.executerNeurogeneseReelle(
                    'progression_niveau',
                    0.9,
                    {
                        feedback: 'niveau_up',
                        nouveau_niveau: this.metriques.niveau_actuel,
                        efficacite: this.metriques.efficacite_globale
                    }
                );
            }
        }
    }

    // FORMATION MANUELLE
    async demarrerFormationManuelle() {
        console.log('\n🎓 FORMATION MANUELLE DÉMARRÉE');
        console.log('==============================');

        this.surveillance.derniere_interaction = Date.now();
        await this.demarrerSessionFormationReelle();
    }

    // GESTION FICHIERS ET LOGS
    creerStructureLogs() {
        if (!fs.existsSync(this.config.chemin_logs)) {
            fs.mkdirSync(this.config.chemin_logs, { recursive: true });
            console.log(`📁 Créé répertoire logs: ${this.config.chemin_logs}`);
        }

        // Sous-répertoires
        const sousRep = ['sessions', 'metriques', 'evaluations', 'config'];
        sousRep.forEach(rep => {
            const chemin = path.join(this.config.chemin_logs, rep);
            if (!fs.existsSync(chemin)) {
                fs.mkdirSync(chemin, { recursive: true });
            }
        });
    }

    chargerMetriques() {
        const fichierMetriques = path.join(this.config.chemin_logs, 'metriques', 'metriques_globales.json');

        if (fs.existsSync(fichierMetriques)) {
            try {
                const data = fs.readFileSync(fichierMetriques, 'utf8');
                const metriques = JSON.parse(data);

                // Fusionner avec métriques actuelles
                Object.assign(this.metriques, metriques);

                console.log(`📊 Métriques chargées: ${this.metriques.sessions_totales} sessions`);
            } catch (error) {
                console.log(`⚠️ Erreur chargement métriques: ${error.message}`);
            }
        }
    }

    sauvegarderMetriques() {
        const fichierMetriques = path.join(this.config.chemin_logs, 'metriques', 'metriques_globales.json');

        try {
            fs.writeFileSync(fichierMetriques, JSON.stringify(this.metriques, null, 2));
        } catch (error) {
            console.log(`⚠️ Erreur sauvegarde métriques: ${error.message}`);
        }
    }

    sauvegarderConfiguration() {
        const fichierConfig = path.join(this.config.chemin_logs, 'config', 'configuration.json');

        try {
            const configSauvegarde = {
                auto_formation_active: this.config.auto_formation_active,
                niveau_actuel: this.metriques.niveau_actuel,
                derniere_modification: Date.now()
            };

            fs.writeFileSync(fichierConfig, JSON.stringify(configSauvegarde, null, 2));
        } catch (error) {
            console.log(`⚠️ Erreur sauvegarde config: ${error.message}`);
        }
    }

    sauvegarderResultatsSession(resultats) {
        const fichierSession = path.join(
            this.config.chemin_logs,
            'sessions',
            `session_${resultats.id}.json`
        );

        try {
            fs.writeFileSync(fichierSession, JSON.stringify(resultats, null, 2));
        } catch (error) {
            console.log(`⚠️ Erreur sauvegarde session: ${error.message}`);
        }
    }

    // STATISTIQUES RÉELLES
    obtenirStatistiquesCompletes() {
        const statsMemoire = this.memoire.obtenirStatistiquesReelles();
        const statsNeurogenese = this.neurogenese.obtenirStatistiquesNeurogenese();

        return {
            formation: this.metriques,
            agent_formateur: this.agentFormateur,
            configuration: this.config,
            surveillance: this.surveillance,
            memoire: statsMemoire,
            neurogenese: statsNeurogenese,
            performance_globale: this.calculerPerformanceGlobale()
        };
    }

    calculerPerformanceGlobale() {
        const facteurs = [
            this.metriques.efficacite_globale,                    // Efficacité formation
            this.metriques.niveau_actuel / 3,                     // Progression niveau
            Math.min(1, this.metriques.sessions_totales / 10),    // Expérience
            Math.min(1, this.metriques.neurones_crees_formation / 100000) // Croissance neuronale
        ];

        return facteurs.reduce((sum, f) => sum + f, 0) / facteurs.length;
    }

    afficherStatistiquesCompletes() {
        console.log('\n📊 STATISTIQUES SYSTÈME AUTO-FORMATION RÉEL');
        console.log('===========================================');

        const stats = this.obtenirStatistiquesCompletes();

        console.log(`🎓 FORMATION:`);
        console.log(`   Auto-formation: ${this.config.auto_formation_active ? 'ACTIVÉE ✅' : 'DÉSACTIVÉE ❌'}`);
        console.log(`   Niveau actuel: ${stats.formation.niveau_actuel}/3`);
        console.log(`   Sessions totales: ${stats.formation.sessions_totales}`);
        console.log(`   Questions totales: ${stats.formation.questions_totales}`);
        console.log(`   Réponses correctes: ${stats.formation.reponses_correctes}`);
        console.log(`   Efficacité globale: ${(stats.formation.efficacite_globale * 100).toFixed(1)}%`);

        console.log(`\n🤖 AGENT FORMATEUR:`);
        console.log(`   Modèle: ${stats.agent_formateur.nom}`);
        console.log(`   Formations réussies: ${stats.agent_formateur.formations_reussies}`);
        console.log(`   Questions posées: ${stats.agent_formateur.questions_posees}`);
        console.log(`   Statut: ${stats.surveillance.formation_en_cours ? 'EN FORMATION' : 'EN ATTENTE'}`);

        console.log(`\n🧠 CROISSANCE NEURONALE:`);
        console.log(`   Neurones créés formation: ${stats.formation.neurones_crees_formation.toLocaleString()}`);
        console.log(`   Neurones totaux: ${stats.memoire.compteurs.neurones_total.toLocaleString()}`);

        console.log(`\n📈 PERFORMANCE GLOBALE: ${(stats.performance_globale * 100).toFixed(1)}%`);

        const tempsInactivite = Date.now() - this.surveillance.derniere_interaction;
        console.log(`\n⏰ SURVEILLANCE:`);
        console.log(`   Dernière interaction: ${(tempsInactivite/1000).toFixed(1)}s`);
        console.log(`   Seuil inactivité: ${this.config.intervalle_inactivite/1000}s`);
        console.log(`   Formation en cours: ${this.surveillance.formation_en_cours ? 'OUI' : 'NON'}`);
    }

    // INTERFACE CONTRÔLE
    afficherInterfaceControle() {
        console.log('\n🎛️ INTERFACE CONTRÔLE AUTO-FORMATION');
        console.log('====================================');
        console.log('1. 🔘 Activer/Désactiver auto-formation');
        console.log('2. 🎓 Démarrer formation manuelle');
        console.log('3. 📊 Afficher statistiques complètes');
        console.log('4. 📈 Changer niveau difficulté');
        console.log('5. ⏰ Modifier intervalle inactivité');
        console.log('6. 🧠 Forcer création neurones');
        console.log('7. 💾 Sauvegarder état complet');
        console.log('8. 📁 Voir logs formation');
    }

    // COMMANDES INTERFACE
    async executerCommande(numero) {
        switch (numero) {
            case 1:
                return this.toggleAutoFormation();

            case 2:
                await this.demarrerFormationManuelle();
                break;

            case 3:
                this.afficherStatistiquesCompletes();
                break;

            case 4:
                console.log('📈 Changement niveau - Fonctionnalité à implémenter');
                break;

            case 5:
                console.log('⏰ Modification intervalle - Fonctionnalité à implémenter');
                break;

            case 6:
                await this.forcerCreationNeurones();
                break;

            case 7:
                this.sauvegarderEtatComplet();
                break;

            case 8:
                this.afficherLogsFormation();
                break;

            default:
                console.log('❌ Commande non reconnue');
        }
    }

    async forcerCreationNeurones() {
        console.log('\n🧠 CRÉATION FORCÉE DE NEURONES');
        console.log('==============================');

        const resultat = await this.neurogenese.executerNeurogeneseReelle(
            'creation_forcee',
            0.8,
            { feedback: 'creation_manuelle', source: 'interface_controle' }
        );

        console.log(`✅ ${resultat.neurones_crees} neurones créés`);
        console.log(`🔗 ${resultat.synapses_creees} synapses créées`);
    }

    sauvegarderEtatComplet() {
        console.log('\n💾 SAUVEGARDE ÉTAT COMPLET');
        console.log('==========================');

        this.sauvegarderMetriques();
        this.sauvegarderConfiguration();

        const etatComplet = this.obtenirStatistiquesCompletes();
        const fichierEtat = path.join(this.config.chemin_logs, `etat_complet_${Date.now()}.json`);

        try {
            fs.writeFileSync(fichierEtat, JSON.stringify(etatComplet, null, 2));
            console.log(`✅ État sauvegardé: ${fichierEtat}`);
        } catch (error) {
            console.log(`❌ Erreur sauvegarde: ${error.message}`);
        }
    }

    afficherLogsFormation() {
        console.log('\n📁 LOGS FORMATION');
        console.log('=================');

        try {
            const cheminSessions = path.join(this.config.chemin_logs, 'sessions');
            const fichiers = fs.readdirSync(cheminSessions).filter(f => f.endsWith('.json'));

            console.log(`📄 ${fichiers.length} sessions enregistrées`);

            // Afficher les 3 dernières sessions
            const dernieresSessions = fichiers.slice(-3);
            dernieresSessions.forEach(fichier => {
                const chemin = path.join(cheminSessions, fichier);
                const data = JSON.parse(fs.readFileSync(chemin, 'utf8'));

                console.log(`\n📝 ${fichier}:`);
                console.log(`   Niveau: ${data.niveau}`);
                console.log(`   Réussites: ${data.reponses_correctes}/${data.questions.length}`);
                console.log(`   Efficacité: ${(data.efficacite * 100).toFixed(1)}%`);
                console.log(`   Neurones: ${data.neurones_crees}`);
            });

        } catch (error) {
            console.log(`❌ Erreur lecture logs: ${error.message}`);
        }
    }
}

// Export
module.exports = SystemeAutoFormationReel;

// Test automatique si exécuté directement
if (require.main === module) {
    console.log('🚀 SYSTÈME AUTO-FORMATION RÉEL - AGENT 1.2GB');
    console.log('==============================================');

    const formation = new SystemeAutoFormationReel();

    // Afficher interface
    formation.afficherInterfaceControle();

    // Activer auto-formation pour test
    console.log('\n🔘 Activation auto-formation pour test...');
    formation.toggleAutoFormation();

    // Afficher statistiques
    setTimeout(() => {
        formation.afficherStatistiquesCompletes();
    }, 2000);

    // Test formation manuelle après 5 secondes
    setTimeout(async () => {
        console.log('\n🎓 Test formation manuelle...');
        await formation.demarrerFormationManuelle();
    }, 5000);
}
