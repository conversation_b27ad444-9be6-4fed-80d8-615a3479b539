#!/bin/bash

# =============================================================================
# LANCEUR UNIFIÉ LOUNA-AI COMPLET - VERSION WORKSPACE
# =============================================================================
# Ce script lance tous les composants LOUNA-AI de manière unifiée
# - Serveur principal avec mémoire thermique
# - Agent Ollama intelligent
# - Interface complète
# - Ouverture d'applications
# Adapté pour le workspace /Volumes/seagate/ollama-models/LOUNA-AI-COMPLET
# =============================================================================

echo "🚀 LANCEMENT UNIFIÉ LOUNA-AI COMPLET"
echo "===================================="

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_header() {
    echo -e "${PURPLE}🎯 $1${NC}"
}

# Vérification du répertoire
if [ ! -f "serveur-interface-complete.js" ]; then
    print_error "Fichier serveur-interface-complete.js non trouvé"
    print_info "Assurez-vous d'être dans le répertoire LOUNA-AI-COMPLET"
    exit 1
fi

# Vérification des dépendances Node.js
print_header "Vérification des dépendances..."
if ! command -v node &> /dev/null; then
    print_error "Node.js n'est pas installé"
    exit 1
fi
print_status "Node.js disponible"

if ! command -v npm &> /dev/null; then
    print_error "npm n'est pas installé"
    exit 1
fi
print_status "npm disponible"

# Installation des dépendances si nécessaire
if [ ! -d "node_modules" ]; then
    print_header "Installation des dépendances Node.js..."
    npm install
    print_status "Dépendances installées"
fi

# Vérification d'Ollama
print_header "Vérification d'Ollama..."
if curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
    print_status "Ollama est démarré et accessible"
    
    # Vérification des modèles (MODÈLE 18GB PRIORITAIRE)
    MODELS=$(curl -s http://localhost:11434/api/tags | jq -r '.models[].name' 2>/dev/null)
    if echo "$MODELS" | grep -q "llama2:13b"; then
        print_status "✅ Modèle 18GB (llama2:13b) disponible - UTILISÉ PAR DÉFAUT"
    elif echo "$MODELS" | grep -q "mistral:7b"; then
        print_status "Modèle mistral:7b disponible (fallback)"
    else
        print_warning "⚠️ Aucun modèle trouvé - Vérifiez votre installation Ollama"
        print_warning "Modèles attendus: llama2:13b (18GB) ou mistral:7b"
        echo "Modèles actuellement installés:"
        ollama list
    fi
else
    print_warning "Ollama n'est pas démarré, tentative de démarrage..."
    # Démarrer Ollama en arrière-plan
    ollama serve &
    OLLAMA_PID=$!
    sleep 5
    
    if curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
        print_status "Ollama démarré avec succès"
    else
        print_error "Impossible de démarrer Ollama"
        print_info "Veuillez démarrer Ollama manuellement avec: ollama serve"
    fi
fi

# Arrêt des processus existants sur le port 3000
print_header "Nettoyage des processus existants..."
if lsof -ti:3000 > /dev/null 2>&1; then
    print_info "Arrêt du serveur existant sur le port 3000..."
    kill -9 $(lsof -ti:3000) 2>/dev/null || true
    sleep 2
fi

# Sauvegarde de la configuration actuelle
print_header "Sauvegarde de la configuration..."
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
mkdir -p "sauvegardes/$TIMESTAMP"

# Sauvegarder les fichiers critiques
cp serveur-interface-complete.js "sauvegardes/$TIMESTAMP/"
cp interface-louna-complete.html "sauvegardes/$TIMESTAMP/"
cp gestionnaire-applications-intelligent.js "sauvegardes/$TIMESTAMP/" 2>/dev/null || true
cp systeme-scan-intelligent.js "sauvegardes/$TIMESTAMP/" 2>/dev/null || true

print_status "Configuration sauvegardée dans sauvegardes/$TIMESTAMP/"

# Démarrage du serveur LOUNA-AI unifié
print_header "Démarrage du serveur LOUNA-AI unifié..."
echo ""
echo "🧠 Composants intégrés:"
echo "   ✅ Mémoire thermique réelle"
echo "   ✅ Agent Ollama intelligent"
echo "   ✅ Gestionnaire d'applications"
echo "   ✅ Scan système complet"
echo "   ✅ Interface complète"
echo ""

# Démarrer le serveur en arrière-plan
node serveur-interface-complete.js &
SERVER_PID=$!

# Attendre que le serveur démarre
print_info "Attente du démarrage du serveur..."
sleep 8

# Vérifier que le serveur fonctionne
if curl -s http://localhost:3000/api/status > /dev/null 2>&1; then
    print_status "Serveur LOUNA-AI unifié opérationnel (PID: $SERVER_PID)"
    
    # Récupérer les statistiques
    STATS=$(curl -s http://localhost:3000/api/status | jq -r '.etat')
    QI=$(echo "$STATS" | jq -r '.qi_actuel')
    MEMOIRES=$(echo "$STATS" | jq -r '.memoires')
    TEMP=$(echo "$STATS" | jq -r '.temperature')
    APPS=$(echo "$STATS" | jq -r '.applications_detectees')
    
    echo ""
    print_header "📊 ÉTAT DU SYSTÈME LOUNA-AI:"
    echo "   🧠 QI: $QI"
    echo "   💾 Mémoires: $MEMOIRES"
    echo "   🌡️  Température: ${TEMP}°C"
    echo "   📱 Applications: $APPS détectées"
    echo ""
    
else
    print_error "Échec du démarrage du serveur"
    kill $SERVER_PID 2>/dev/null
    exit 1
fi

# Ouverture de l'interface
print_header "Ouverture de l'interface LOUNA-AI..."
sleep 2
open "http://localhost:3000/interface-louna-complete.html"
print_status "Interface ouverte dans le navigateur"

# Test de fonctionnement
print_header "Test de fonctionnement..."
TEST_RESPONSE=$(curl -s -X POST http://localhost:3000/api/chat \
    -H "Content-Type: application/json" \
    -d '{"message":"Test de fonctionnement LOUNA-AI"}' | jq -r '.success')

if [ "$TEST_RESPONSE" = "true" ]; then
    print_status "Test de communication réussi"
else
    print_warning "Test de communication échoué"
fi

# Affichage des informations finales
echo ""
echo "🎉 LOUNA-AI UNIFIÉ OPÉRATIONNEL !"
echo "================================="
echo ""
echo "🌐 Interface: http://localhost:3000/interface-louna-complete.html"
echo "📊 API Status: http://localhost:3000/api/status"
echo "💬 API Chat: http://localhost:3000/api/chat"
echo ""
echo "🔧 Processus:"
echo "   - Serveur LOUNA-AI: PID $SERVER_PID"
if [ ! -z "$OLLAMA_PID" ]; then
    echo "   - Ollama: PID $OLLAMA_PID"
fi
echo ""
echo "📁 Logs en temps réel: tail -f logs/louna-$(date +%Y%m%d).log"
echo ""
echo "🛑 Pour arrêter: kill $SERVER_PID"
echo ""

# Créer un fichier de statut
cat > "LOUNA-AI-STATUS.txt" << EOF
LOUNA-AI UNIFIÉ - STATUT
========================
Démarré le: $(date)
PID Serveur: $SERVER_PID
PID Ollama: ${OLLAMA_PID:-"Déjà en cours"}
Interface: http://localhost:3000/interface-louna-complete.html
QI: $QI
Mémoires: $MEMOIRES
Applications: $APPS
EOF

print_status "Fichier de statut créé: LOUNA-AI-STATUS.txt"

# Garder le script actif pour surveiller
print_info "Surveillance active... (Ctrl+C pour arrêter)"
trap 'echo ""; print_info "Arrêt de LOUNA-AI..."; kill $SERVER_PID 2>/dev/null; [ ! -z "$OLLAMA_PID" ] && kill $OLLAMA_PID 2>/dev/null; print_status "LOUNA-AI arrêté proprement"; exit 0' INT

# Boucle de surveillance
while true; do
    sleep 30
    if ! kill -0 $SERVER_PID 2>/dev/null; then
        print_error "Le serveur LOUNA-AI s'est arrêté de manière inattendue"
        break
    fi
done
