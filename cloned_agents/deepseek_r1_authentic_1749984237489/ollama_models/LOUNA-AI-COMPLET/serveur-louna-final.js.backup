/**
 * Connexions sécurisées - Aucune déconnexion
 */

const express = require('express');
const AccesSystemeSecurise = require("./acces-systeme-securise");const path = require('path');
const GestionnaireApplicationsIntelligent = require("./gestionnaire-applications-intelligent");const { MoteurRaisonnementReel } = require('./moteur-raisonnement-reel');
const SystemeScanIntelligent = require("./systeme-scan-intelligent");const { MemoireThermiqueGlissante } = require('./memoire-thermique-glissante');
const AnalyseurTachesIntelligent = require("./analyseur-taches-intelligent");const { AutoEvaluation } = require('./auto-evaluation');
const GenerateurProgrammesAutonome = require("./generateur-programmes-autonome");const { MiseAJourTempsReelGlissante } = require('./mise-a-jour-temps-reel-glissante');
const GenerateurCodeExpert = require("./generateur-code-expert");const { MemoireConversationnelle } = require('./memoire-conversationnelle');
const GenerateurCodeReflexif = require("./generateur-code-reflexif");
const app = express();
const PORT = 8080;

// INITIALISATION SÉCURISÉE
let moteurRaisonnement = null;
let memoireThermique = null;
let autoEvaluation = null;
let miseAJourTempsReel = null;
let memoireConversationnelle = null;
let accesSysteme = null;
let gestionnaireApps = null;
let systemeScan = null;
let analyseurTaches = null;
let generateurProgrammes = null;
let generateurCodeExpert = null;// Middleware sécurisé
let generateurCodeReflexif = null;app.use(express.json({ limit: '10mb' }));
app.use(express.static(__dirname));

// Gestion d'erreurs ultra-sécurisée
process.on('uncaughtException', (error) => {
    console.error('❌ Erreur non capturée (maintien connexion):', error.message);
    // NE PAS ARRÊTER LE SERVEUR
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Promesse rejetée (maintien connexion):', reason);
    // NE PAS ARRÊTER LE SERVEUR
});

console.log('🚀 INITIALISATION LOUNA-AI FINAL SÉCURISÉ');
console.log('=========================================');

// Initialisation ultra-sécurisée
function initialiserComposantSecurise(nom, ClasseComposant, ...args) {
    try {
        const instance = new ClasseComposant(...args);
        console.log(`✅ ${nom} initialisé et sécurisé`);
        return instance;
    } catch (error) {
        console.error(`❌ Erreur ${nom} (maintien connexion):`, error.message);
        return null;
    }
}

// INITIALISATION DANS L'ORDRE CORRECT
moteurRaisonnement = initialiserComposantSecurise('Moteur de raisonnement', MoteurRaisonnementReel);
memoireThermique = initialiserComposantSecurise('Mémoire thermique glissante', MemoireThermiqueGlissante);
autoEvaluation = initialiserComposantSecurise('Auto-évaluation', AutoEvaluation);
miseAJourTempsReel = initialiserComposantSecurise('Mise à jour temps réel glissante', MiseAJourTempsReelGlissante, memoireThermique);
accesSysteme = initialiserComposantSecurise("Accès système sécurisé", AccesSystemeSecurise);memoireConversationnelle = initialiserComposantSecurise('Mémoire conversationnelle', MemoireConversationnelle);
gestionnaireApps = initialiserComposantSecurise("Gestionnaire applications intelligent", GestionnaireApplicationsIntelligent);
systemeScan = initialiserComposantSecurise("Système scan intelligent", SystemeScanIntelligent);// ROUTE INTERFACE PRINCIPALE
analyseurTaches = initialiserComposantSecurise("Analyseur tâches intelligent", AnalyseurTachesIntelligent);
generateurProgrammes = initialiserComposantSecurise("Générateur programmes autonome", GenerateurProgrammesAutonome);
generateurCodeExpert = initialiserComposantSecurise("Générateur code expert", GenerateurCodeExpert);
generateurCodeReflexif = initialiserComposantSecurise("Générateur code réflexif", GenerateurCodeReflexif);// ROUTE INTERFACE PRINCIPALE
app.get('/',(req, res) => {

// ROUTE VISUALISATION 3D DU CERVEAU
app.get("/cerveau", (req, res) => {
    res.sendFile(path.join(__dirname, "cerveau-3d-louna.html"));
});

// ROUTE INTERFACE PRINCIPALE
app.get('/',(req, res) => {
    try {        res.sendFile(path.join(__dirname, 'interface-louna-complete.html'));
    } catch (error) {
        console.error('❌ Erreur interface (maintien connexion):', error.message);
        res.status(500).send('Erreur chargement interface - Connexion maintenue');
    }
});

// ROUTE CHAT FINAL ULTRA-SÉCURISÉE
app.post('/chat', async (req, res) => {
    try {
        const { message } = req.body;
        
        if (!message) {
            return res.json({
                success: false,
                error: 'Message requis',
                connexion: 'maintenue'
            });
        }

        console.log(`�� Question reçue: "${message}"`);
        
        // DÉTECTION PROJETS DE CODAGE AVANCÉS
        const demandesCodeAvance = ["jeu du morpion", "morpion", "tic tac toe", "projet complet", "avec tests", "vs code", "projet professionnel"];
        const estDemandeCodeAvance = demandesCodeAvance.some(mot => message.toLowerCase().includes(mot));
        
        if (estDemandeCodeAvance && generateurCodeExpert) {
            console.log(`💻 Demande projet de codage avancé détectée`);
            try {
                const specifications = {
                    nom: "morpion-louna",
                    description: "Jeu du morpion interactif en console",
                    type: "jeu",
                    features: ["tests", "vscode", "git"],
                    complexite: "moyenne"
                };
                
                const resultatProjet = await generateurCodeExpert.creerProjetComplet(specifications);
                
                if (resultatProjet.success) {
                    let reponse = "🎉 **Projet de codage expert créé !**\n\n";
                    reponse += resultatProjet.message + "\n\n";
                    reponse += `📁 **Projet**: ${resultatProjet.projet.nom}\n`;
                    reponse += `📂 **Chemin**: ${resultatProjet.projet.chemin}\n`;
                    reponse += `📋 **Fichiers**: ${resultatProjet.projet.fichiers.length} fichiers créés\n`;
                    reponse += `🧪 **Tests**: ${resultatProjet.tests.success ? "✅ Passés" : "❌ Échoués"}\n\n`;
                    reponse += "🚀 **Pour ouvrir dans VS Code**:\n";
                    reponse += "`code \"" + resultatProjet.projet.chemin + "\"`\n\n";
                    reponse += "⚡ **Commandes disponibles**:\n";
                    reponse += "• `npm start` - Lancer le jeu\n";
                    reponse += "• `npm test` - Exécuter les tests\n";
                    reponse += "• `npm run dev` - Mode développement";
                    
                    // Ouvrir automatiquement dans VS Code
                    await generateurCodeExpert.ouvrirDansVSCode(resultatProjet.projet.chemin);
                    
                    return res.json({
                        success: true,
                        response: reponse,
                        source: "Générateur code expert",
                        qi_actuel: Math.min(550, 350 + (generateurCodeExpert ? 60 : 0) + (generateurCodeReflexif ? 90 : 0)),
                        coefficient_intellectuel: 350,
                        projet_expert_cree: true,
                        projet_info: resultatProjet.projet,
                        tests_info: resultatProjet.tests,
                        connexion_securisee: true,
                        timestamp: Date.now()
                    });
                } else {
                    return res.json({
                        success: true,
                        response: `❌ Erreur création projet: ${resultatProjet.message}`,
                        source: "Générateur code expert",
                        qi_actuel: 300,
                        coefficient_intellectuel: 300,
                        projet_expert_cree: false,
                        connexion_securisee: true,
                        timestamp: Date.now()
                    });
                }
            } catch (error) {
                console.log(`❌ Erreur générateur code expert: ${error.message}`);
            }
        }        // DÉTECTION CONFIRMATION CRÉATION PROGRAMME
        const confirmationsCreation = ["oui, crée le programme", "crée le programme", "oui crée", "créer le programme", "génère le programme"];
        const estConfirmationCreation = confirmationsCreation.some(conf => message.toLowerCase().includes(conf));
        
        if (estConfirmationCreation && generateurProgrammes) {
            console.log(`🚀 Confirmation création programme détectée`);
            try {
                // Récupérer les spécifications du dernier programme proposé (simulation)
                const specifications = {
                    nom_suggere: `programme_personnalise_${Date.now()}`,
                    description: "Programme créé selon votre demande",
                    complexite: "moyenne",
                    temps_estime: 60,
                    technologies: ["JavaScript", "Node.js", "fs"]
                };
                
                const resultatCreation = await generateurProgrammes.creerProgramme(specifications);
                
                if (resultatCreation.success) {
                    let reponse = "🎉 **Programme créé avec succès !**\n\n";
                    reponse += resultatCreation.message + "\n\n";
                    reponse += `📁 **Fichiers créés** (${resultatCreation.programme.fichiers.length}):\n`;
                    resultatCreation.programme.fichiers.forEach(fichier => {
                        reponse += `  • ${fichier.nom} (${fichier.type})\n`;
                    });
                    reponse += "\n📋 **Documentation**: README.md créé\n";
                    reponse += "⚡ **Utilisation**: `node programmes-crees/" + specifications.nom_suggere + ".js`\n";
                    reponse += "🧪 **Tests**: `node programmes-crees/" + specifications.nom_suggere + "_tests.js`";
                    
                    return res.json({
                        success: true,
                        response: reponse,
                        source: "Générateur programmes autonome",
                        qi_actuel: Math.min(500, 320 + (generateurCodeExpert ? 50 : 0) + (generateurCodeReflexif ? 80 : 0)),
                        coefficient_intellectuel: 320,
                        programme_cree: true,
                        programme_info: resultatCreation.programme,
                        connexion_securisee: true,
                        timestamp: Date.now()
                    });
                } else {
                    return res.json({
                        success: true,
                        response: `❌ Erreur lors de la création: ${resultatCreation.message}`,
                        source: "Générateur programmes autonome",
                        qi_actuel: Math.min(450, 280 + (generateurCodeExpert ? 50 : 0) + (generateurCodeReflexif ? 70 : 0)),
                        coefficient_intellectuel: 280,
                        programme_cree: false,
                        connexion_securisee: true,
                        timestamp: Date.now()
                    });
                }
            } catch (error) {
                console.log(`❌ Erreur générateur programmes: ${error.message}`);
            }
        }        // ANALYSE INTELLIGENTE DE TÂCHE (toujours en dernier recours)
        if (analyseurTaches) {
            console.log(`🧠 Analyse intelligente de la tâche`);
            try {
                const resultatAnalyse = await analyseurTaches.analyserTache(message);
                if (resultatAnalyse.success && resultatAnalyse.analyse.recommandation.action_recommandee === "creer_programme") {
                    let reponse = "🤖 **Analyse de votre demande**\n\n";
                    reponse += resultatAnalyse.message;
                    reponse += "\n\n❓ **Voulez-vous que je crée ce programme pour vous ?**\n";
                    reponse += "Répondez \"Oui, crée le programme\" pour que je commence la création.";
                    
                    return res.json({
                        success: true,
                        response: reponse,
                        source: "Analyseur tâches intelligent",
                        qi_actuel: 290,
                        coefficient_intellectuel: 290,
                        analyseur_taches: true,
                        creation_programme_proposee: true,
                        programme_suggere: resultatAnalyse.analyse.recommandation.creation_programme,
                        connexion_securisee: true,
                        timestamp: Date.now()
                    });
                } else if (resultatAnalyse.success && resultatAnalyse.analyse.recommandation.solution_preferee) {
                    let reponse = "🔍 **Solution trouvée !**\n\n";
                    reponse += resultatAnalyse.message;
                    
                    return res.json({
                        success: true,
                        response: reponse,
                        source: "Analyseur tâches intelligent",
                        qi_actuel: Math.min(450, 280 + (generateurCodeExpert ? 50 : 0) + (generateurCodeReflexif ? 70 : 0)),
                        coefficient_intellectuel: 280,
                        analyseur_taches: true,
                        solution_existante: true,
                        connexion_securisee: true,
                        timestamp: Date.now()
                    });
                }
            } catch (error) {
                console.log(`❌ Erreur analyseur tâches: ${error.message}`);
            }
        }        // DÉTECTION DEMANDES SCAN SYSTÈME
        const demandesScan = ["scan", "scanne", "analyse", "inventaire", "système", "machine", "applications installées", "nouvelles applications"];
        const estDemandeScan = demandesScan.some(mot => message.toLowerCase().includes(mot));
        
        if (estDemandeScan && systemeScan && !message.toLowerCase().includes("bureau") && !message.toLowerCase().includes("ouvre") && !message.toLowerCase().includes("lance")) {
            console.log(`🔍 Demande scan système détectée`);
            try {
                const resultatScan = await systemeScan.traiterDemande(message);
                if (resultatScan.success) {
                    let reponse = resultatScan.message;
                    
                    if (resultatScan.applications && resultatScan.applications.length > 0) {
                        reponse += `\n\n📱 Applications détectées (${resultatScan.total || resultatScan.applications.length}):`;
                        
                        // Grouper par catégorie
                        const parCategorie = {};
                        resultatScan.applications.forEach(app => {
                            if (!parCategorie[app.categorie]) parCategorie[app.categorie] = [];
                            parCategorie[app.categorie].push(app);
                        });
                        
                        Object.entries(parCategorie).forEach(([categorie, apps]) => {
                            reponse += `\n\n🏷️ ${categorie.toUpperCase()} (${apps.length}):`;
                            apps.slice(0, 5).forEach(app => {
                                reponse += `\n  • ${app.nom_complet || app.nom}`;
                                if (app.version && app.version !== "Inconnue") {
                                    reponse += ` (v${app.version})`;
                                }
                            });
                            if (apps.length > 5) {
                                reponse += `\n  ... et ${apps.length - 5} autres`;
                            }
                        });
                    }
                    
                    if (resultatScan.systeme) {
                        const sys = resultatScan.systeme;
                        reponse += "\n\n🖥️ Informations système:";
                        if (sys.systeme) {
                            reponse += `\n  • Plateforme: ${sys.systeme.plateforme} ${sys.systeme.architecture}`;
                            reponse += `\n  • Utilisateur: ${sys.systeme.utilisateur.username}`;
                            reponse += `\n  • Uptime: ${Math.round(sys.systeme.uptime / 3600)}h`;
                        }
                        if (sys.memoire) {
                            reponse += `\n  • Mémoire: ${(sys.memoire.utilisee / 1024 / 1024 / 1024).toFixed(1)}GB / ${(sys.memoire.totale / 1024 / 1024 / 1024).toFixed(1)}GB (${sys.memoire.pourcentage_utilise}%)`;
                        }
                        if (sys.cpu) {
                            reponse += `\n  • CPU: ${sys.cpu.modele} (${sys.cpu.nombre_coeurs} cœurs)`;
                        }
                    }
                    
                    if (resultatScan.nouvelles_applications && resultatScan.nouvelles_applications.length > 0) {
                        reponse += `\n\n🆕 Nouvelles applications (${resultatScan.total_nouvelles}):`;
                        resultatScan.nouvelles_applications.slice(0, 10).forEach(app => {
                            reponse += `\n  • ${app.nom_complet || app.nom} (${app.categorie})`;
                        });
                    }
                    
                    return res.json({
                        success: true,
                        response: reponse,
                        source: "Système scan intelligent",
                        qi_actuel: 275,
                        coefficient_intellectuel: 275,
                        systeme_scan: true,
                        connexion_securisee: true,
                        timestamp: Date.now()
                    });
                }
            } catch (error) {
                console.log(`❌ Erreur système scan: ${error.message}`);
            }
        }        // DÉTECTION DEMANDES APPLICATIONS
        const demandesApps = ["ouvre", "lance", "démarre", "open", "start", "vscode", "code", "xcode", "terminal", "chrome", "safari", "photoshop", "figma", "application"];
        const estDemandeApp = demandesApps.some(mot => message.toLowerCase().includes(mot));
        
        if (estDemandeApp && gestionnaireApps && !message.toLowerCase().includes("bureau") && !message.toLowerCase().includes("fichiers")) {
            console.log(`🚀 Demande application détectée`);
            try {
                const resultatApp = await gestionnaireApps.traiterDemande(message);
                if (resultatApp.success) {
                    let reponse = resultatApp.message;
                    
                    if (resultatApp.application) {
                        reponse += `\n\n📱 Application: ${resultatApp.application}`;
                        reponse += `\n🏷️ Type: ${resultatApp.type}`;
                        reponse += `\n📝 Description: ${resultatApp.description}`;
                    }
                    
                    if (resultatApp.suggestions && resultatApp.suggestions.length > 0) {
                        reponse += "\n\n💡 Suggestions:";
                        resultatApp.suggestions.forEach(suggestion => {
                            reponse += `\n- ${suggestion.nom} (${suggestion.type})`;
                        });
                    }
                    
                    if (resultatApp.fiche) {
                        reponse += `\n\n📋 Fiche technique créée`;
                        reponse += `\n📅 Date: ${new Date(resultatApp.fiche.dateCreation).toLocaleString()}`;
                    }
                    
                    if (resultatApp.informations) {
                        reponse += `\n\n🔍 Informations trouvées`;
                        if (resultatApp.informations.liens) {
                            reponse += "\n🔗 Liens utiles:";
                            resultatApp.informations.liens.forEach(lien => {
                                reponse += `\n- ${lien}`;
                            });
                        }
                    }
                    
                    return res.json({
                        success: true,
                        response: reponse,
                        source: "Gestionnaire applications intelligent",
                        qi_actuel: 250,
                        coefficient_intellectuel: 250,
                        gestionnaire_apps: true,
                        connexion_securisee: true,
                        timestamp: Date.now()
                    });
                } else {
                    let reponseErreur = resultatApp.message;
                    if (resultatApp.suggestions && resultatApp.suggestions.length > 0) {
                        reponseErreur += "\n\n💡 Applications similaires disponibles:";
                        resultatApp.suggestions.forEach(suggestion => {
                            reponseErreur += `\n- ${suggestion.nom}: ${suggestion.description}`;
                        });
                    }
                    if (resultatApp.exemple) {
                        reponseErreur += `\n\n${resultatApp.exemple}`;
                    }
                    return res.json({
                        success: true,
                        response: reponseErreur,
                        source: "Gestionnaire applications intelligent",
                        qi_actuel: 240,
                        coefficient_intellectuel: 240,
                        gestionnaire_apps: true,
                        connexion_securisee: true,
                        timestamp: Date.now()
                    });
                }
            } catch (error) {
                console.log(`❌ Erreur gestionnaire apps: ${error.message}`);
            }
        }        // DÉTECTION ACCÈS BUREAU
        if ((message.toLowerCase().includes("bureau") || message.toLowerCase().includes("fichiers")) && accesSysteme) {
            console.log(`🖥️ Demande accès bureau détectée`);
            try {
                const resultatBureau = await accesSysteme.accederBureau();
                if (resultatBureau.success) {
                    let reponse = `✅ Accès au bureau réussi !\n\nChemin: ${resultatBureau.chemin}\n\nFichiers trouvés (${resultatBureau.fichiers.length}):`;
                    resultatBureau.fichiers.slice(0, 15).forEach(fichier => {
                        reponse += `\n📄 ${fichier.nom} (${fichier.type})`;
                    });
                    if (resultatBureau.fichiers.length > 15) {
                        reponse += `\n... et ${resultatBureau.fichiers.length - 15} autres éléments`;
                    }
                    return res.json({
                        success: true,
                        response: reponse,
                        source: "Accès système sécurisé",
                        qi_actuel: 225,
                        coefficient_intellectuel: 225,
                        acces_systeme: true,
                        connexion_securisee: true,
                        timestamp: Date.now()
                    });
                }
            } catch (error) {
                console.log(`❌ Erreur accès bureau: ${error.message}`);
            }
        }
        // ÉTAPE 1: CONTEXTE CONVERSATIONNEL (ULTRA-SÉCURISÉ)
        let contexteConversation = null;
        try {
            if (memoireConversationnelle) {
                contexteConversation = memoireConversationnelle.obtenirContexte(message);
                console.log(`📋 Contexte:`, {
                    sujet: contexteConversation?.sujet_principal || 'aucun',
                    messages: contexteConversation?.nombre_messages || 0
                });
            }
        } catch (error) {
            console.error('❌ Erreur contexte (maintien connexion):', error.message);
            contexteConversation = null;
        }

        // ÉTAPE 2: HISTORIQUE (ULTRA-SÉCURISÉ)
        let resultatsHistorique = [];
        try {
            if (memoireConversationnelle) {
                resultatsHistorique = memoireConversationnelle.rechercherDansHistorique(message, 3);
                console.log(`🔍 Historique: ${resultatsHistorique.length} résultats`);
            }
        } catch (error) {
            console.error('❌ Erreur historique (maintien connexion):', error.message);
            resultatsHistorique = [];
        }

        // ÉTAPE 3: RAISONNEMENT (ULTRA-SÉCURISÉ)
        let resultatRaisonnement = null;
        try {
            if (moteurRaisonnement) {
                resultatRaisonnement = moteurRaisonnement.penser(message);
                console.log(`🧠 Raisonnement:`, resultatRaisonnement?.source || 'aucun');
            }
        } catch (error) {
            console.error('❌ Erreur raisonnement (maintien connexion):', error.message);
            resultatRaisonnement = null;
        }

        // ÉTAPE 4: MÉMOIRE THERMIQUE GLISSANTE (ULTRA-SÉCURISÉ)
        let resultatsMemoire = [];
        try {
            if (memoireThermique) {
                resultatsMemoire = memoireThermique.rechercher(message, 3);
                console.log(`💾 Mémoire glissante: ${resultatsMemoire.length} résultats`);
            }
        } catch (error) {
            console.error('❌ Erreur mémoire glissante (maintien connexion):', error.message);
            resultatsMemoire = [];
        }

        // ÉTAPE 5: DÉCISION DE RÉPONSE INTELLIGENTE
        let reponseFinale = null;
        let source = null;
        let sourceComplete = null;

        // Priorité 1: Raisonnement interne
        if (resultatRaisonnement && resultatRaisonnement.reponse !== null && resultatRaisonnement.reponse !== undefined) {
            reponseFinale = resultatRaisonnement.reponse;
            source = 'Raisonnement interne';
            sourceComplete = 'raisonnement_interne';
            console.log(`✅ Réponse par raisonnement interne`);
        }
        // Priorité 2: Mémoire thermique glissante
        else if (resultatsMemoire.length > 0 && resultatsMemoire[0].pertinence > 0.3) {
            reponseFinale = resultatsMemoire[0].contenu;
            source = `Mémoire thermique glissante (${resultatsMemoire[0].source})`;
            sourceComplete = 'memoire_thermique_glissante';
            console.log(`✅ Réponse par mémoire thermique glissante`);
        }
        // Priorité 3: Historique conversationnel
        else if (resultatsHistorique.length > 0 && resultatsHistorique[0].pertinence > 0.7) {
            const messageHistorique = resultatsHistorique[0].message;
            reponseFinale = `Basé sur notre conversation: ${String(messageHistorique.reponse)}`;
            source = `Historique conversation (message ${messageHistorique.numero})`;
            sourceComplete = 'historique_conversation';
            console.log(`✅ Réponse par historique conversationnel`);
        }
        // Priorité 4: Réponse contextuelle
        else if (contexteConversation && contexteConversation.sujet_principal === 'programmation') {
            reponseFinale = "Je comprends que nous travaillons sur un projet de programmation. Pouvez-vous me donner plus de détails ?";
            source = 'Réponse contextuelle programmation';
            sourceComplete = 'contexte_programmation';
            console.log(`✅ Réponse contextuelle programmation`);
        }
        // Défaut
        else {
            reponseFinale = "Je ne trouve pas d'information pertinente pour répondre à cette question.";
            source = 'Réponse par défaut';
            sourceComplete = 'defaut';
            console.log(`❌ Aucune réponse trouvée`);
        }

        // CALCUL QI AVEC MÉMOIRE GLISSANTE
        let qiCalcule = 127; // Base
        
        try {
            if (moteurRaisonnement) {
                const stats = moteurRaisonnement.getStatistiquesReelles();
                qiCalcule += Math.min(stats.connaissances_base * 2, 40);
            }
            
            if (memoireThermique) {
                const statsMemoire = memoireThermique.getStatistiquesGlissantes();
                qiCalcule += Math.min(statsMemoire.totalEntries * 2, 30);
                
                // Bonus pour température élevée (zone créative)
                if (statsMemoire.averageTemperature > 70) {
                    qiCalcule += 15; // Bonus créativité
                }
                
                // Bonus pour curseur actif
                if (statsMemoire.curseurThermique > 55) {
                    qiCalcule += 10; // Bonus activité
                }
            }

            // Bonus pour contexte
            if (contexteConversation) {
                qiCalcule += Math.min(contexteConversation.nombre_messages * 0.5, 20);
                qiCalcule += (contexteConversation.classes_definies?.length || 0) * 2;
                qiCalcule += (contexteConversation.etapes_projet?.length || 0) * 3;
            }

            // Bonus pour type de réponse
            switch (sourceComplete) {
                case 'raisonnement_interne': qiCalcule += 15; break;
                case 'memoire_thermique_glissante': qiCalcule += 20; break; // Bonus spécial
                case 'historique_conversation': qiCalcule += 12; break;
                case 'contexte_programmation': qiCalcule += 10; break;
            }
        } catch (error) {
            console.error('❌ Erreur calcul QI (maintien connexion):', error.message);
        }

        console.log(`🧠 QI calculé: ${qiCalcule}`);

        // MISE À JOUR TEMPS RÉEL GLISSANTE (ULTRA-SÉCURISÉ)
        try {
            if (miseAJourTempsReel && sourceComplete !== 'defaut') {
                miseAJourTempsReel.renforcerMemoire(message, reponseFinale, source);
            }
        } catch (error) {
            console.error('❌ Erreur mise à jour glissante (maintien connexion):', error.message);
        }

        // ENREGISTREMENT CONVERSATIONNEL (ULTRA-SÉCURISÉ)
        try {
            if (memoireConversationnelle) {
                memoireConversationnelle.ajouterMessage(message, reponseFinale, source, {
                    qi: qiCalcule,
                    source_complete: sourceComplete,
                    contexte_utilise: contexteConversation !== null,
                    memoire_glissante: memoireThermique !== null
                });
            }
        } catch (error) {
            console.error('❌ Erreur enregistrement conversation (maintien connexion):', error.message);
        }

        // AUTO-ÉVALUATION (ULTRA-SÉCURISÉ)
        let evaluation = null;
        try {
            if (autoEvaluation) {
                evaluation = autoEvaluation.enregistrerInteraction(message, reponseFinale, source, qiCalcule);
                if (evaluation) {
                    console.log(`📊 Auto-évaluation déclenchée`);
                }
            }
        } catch (error) {
            console.error('❌ Erreur auto-évaluation (maintien connexion):', error.message);
        }

        // RÉPONSE FINALE SÉCURISÉE
        const response = {
            success: true,
            response: reponseFinale,
            source: source,
            qi_actuel: qiCalcule,
            coefficient_intellectuel: qiCalcule, // Pour l'interface
            memory_used: resultatsMemoire.length > 0,
            reasoning_used: resultatRaisonnement && resultatRaisonnement.reponse !== null,
            conversation_context: contexteConversation !== null,
            history_used: resultatsHistorique.length > 0,
            memoire_glissante: memoireThermique !== null,
            connexion_securisee: true,
            timestamp: Date.now()
        };

        if (evaluation) {
            response.auto_evaluation = evaluation;
        }

        res.json(response);

    } catch (error) {
        console.error('❌ Erreur critique chat (maintien connexion):', error.message);
        res.json({
            success: false,
            error: 'Erreur interne - Connexion maintenue',
            details: error.message,
            connexion_securisee: true,
            timestamp: Date.now()
        });
    }
});

// ROUTE STATISTIQUES GLISSANTES SÉCURISÉE
app.get('/stats', (req, res) => {
    try {
        let stats = {
            success: true,
            stats: {},
            connexion_securisee: true,
            timestamp: Date.now()
        };

        if (moteurRaisonnement) {
            try {
                stats.stats.moteur_raisonnement = moteurRaisonnement.getStatistiquesReelles();
            } catch (error) {
                console.error('❌ Erreur stats moteur (maintien connexion):', error.message);
                stats.stats.moteur_raisonnement = { erreur: 'Erreur stats moteur' };
            }
        }

        if (memoireThermique) {
            try {
                stats.stats.memoire_thermique_glissante = memoireThermique.getStatistiquesGlissantes();
            } catch (error) {
                console.error('❌ Erreur stats mémoire glissante (maintien connexion):', error.message);
                stats.stats.memoire_thermique_glissante = { erreur: 'Erreur stats mémoire' };
            }
        }

        if (autoEvaluation) {
            try {
                stats.stats.auto_evaluation = autoEvaluation.getStats();
            } catch (error) {
                console.error('❌ Erreur stats évaluation (maintien connexion):', error.message);
                stats.stats.auto_evaluation = { erreur: 'Erreur stats évaluation' };
            }
        }

        if (memoireConversationnelle) {
            try {
                stats.stats.memoire_conversationnelle = memoireConversationnelle.getStats();
            } catch (error) {
                console.error('❌ Erreur stats conversation (maintien connexion):', error.message);
                stats.stats.memoire_conversationnelle = { erreur: 'Erreur stats conversation' };
            }
        }

        // Calcul QI sécurisé
        let qi = 127;
        try {
            if (moteurRaisonnement && stats.stats.moteur_raisonnement.connaissances_base) {
                qi += Math.min(stats.stats.moteur_raisonnement.connaissances_base * 2, 40);
            }
            if (memoireThermique && stats.stats.memoire_thermique_glissante.totalEntries) {
                qi += Math.min(stats.stats.memoire_thermique_glissante.totalEntries * 2, 30);
                
                // Bonus température
                if (stats.stats.memoire_thermique_glissante.averageTemperature > 70) {
                    qi += 15;
                }
            }
            if (memoireConversationnelle && stats.stats.memoire_conversationnelle.nombre_messages) {
                qi += Math.min(stats.stats.memoire_conversationnelle.nombre_messages * 0.5, 20);
            }
        } catch (error) {
            console.error('❌ Erreur calcul QI stats (maintien connexion):', error.message);
        }

        // Ajouter statistiques pour interface
        stats.stats.neurones_system = {
            total_installed: (qi * 1000000), // Neurones basés sur QI
            auto_expansion: true,
            zones_actives: 7
        };
        stats.stats.kyber_accelerators = {
            active_count: Math.min(15, Math.floor(qi / 20)), // Accélérateurs basés sur QI
            auto_scaling: true,
            performance: "optimal"
        };        stats.coefficient_intellectuel = qi;
        res.json(stats);

    } catch (error) {
        console.error('❌ Erreur stats globales (maintien connexion):', error.message);
        res.json({
            success: false,
            error: 'Erreur récupération statistiques - Connexion maintenue',
            details: error.message,
            connexion_securisee: true,
            timestamp: Date.now()
        });
    }
});

// ROUTE FORMATION SÉCURISÉE
app.post('/formation', (req, res) => {
    try {
        const { sujet, contenu } = req.body;
        
        if (!sujet || !contenu) {
            return res.json({
                success: false,
                error: 'Sujet et contenu requis',
                connexion_securisee: true
            });
        }

        console.log(`🎓 Formation sécurisée: ${sujet}`);

        let memoireId = null;
        let connaissanceId = null;

        try {
            if (memoireThermique) {
                memoireId = memoireThermique.stocker(contenu, `Formation: ${sujet}`, 0.95);
            }
        } catch (error) {
            console.error('❌ Erreur stockage mémoire formation (maintien connexion):', error.message);
        }

        try {
            if (moteurRaisonnement) {
                connaissanceId = moteurRaisonnement.apprendreNouvelleFait(contenu, `Formation: ${sujet}`);
            }
        } catch (error) {
            console.error('❌ Erreur apprentissage formation (maintien connexion):', error.message);
        }

        res.json({
            success: true,
            message: `Formation sécurisée "${sujet}" intégrée`,
            memoire_id: memoireId,
            connaissance_id: connaissanceId,
            connexion_securisee: true
        });

    } catch (error) {
        console.error('❌ Erreur formation (maintien connexion):', error.message);
        res.json({
            success: false,
            error: 'Erreur pendant la formation - Connexion maintenue',
            details: error.message,
            connexion_securisee: true
        });
    }
});

// MAINTENANCE AUTOMATIQUE ULTRA-SÉCURISÉE
setInterval(() => {
    try {
        if (memoireThermique) {
            memoireThermique.maintenance();
        }
    } catch (error) {
        console.error('❌ Erreur maintenance mémoire (maintien connexion):', error.message);
    }

    try {
        if (miseAJourTempsReel) {
            miseAJourTempsReel.optimiserOrganisation();
        }
    } catch (error) {
        console.error('❌ Erreur maintenance mise à jour (maintien connexion):', error.message);
    }
}, 10 * 60 * 1000); // Toutes les 10 minutes

// DÉMARRAGE SERVEUR FINAL SÉCURISÉ
const server = app.listen(PORT, () => {
    console.log('');
    console.log('✅ LOUNA-AI FINAL SÉCURISÉ OPÉRATIONNEL');
    console.log('======================================');
    console.log(`🔗 Interface: http://localhost:${PORT}`);
    console.log(`🧠 Moteur raisonnement: ${moteurRaisonnement ? 'SÉCURISÉ' : 'INACTIF'}`);
    console.log(`💾 Mémoire thermique glissante: ${memoireThermique ? 'SÉCURISÉE' : 'INACTIVE'}`);
    console.log(`🔍 Auto-évaluation: ${autoEvaluation ? 'SÉCURISÉE' : 'INACTIVE'}`);
    console.log(`⚡ Mise à jour temps réel glissante: ${miseAJourTempsReel ? 'SÉCURISÉE' : 'INACTIVE'}`);
    console.log(`💬 Mémoire conversationnelle: ${memoireConversationnelle ? 'SÉCURISÉE' : 'INACTIVE'}`);
    console.log(`🔒 CONNEXION ULTRA-SÉCURISÉE - AUCUNE DÉCONNEXION`);
    console.log('');
});

// Gestion ultra-sécurisée de l'arrêt
process.on('SIGTERM', () => {
    console.log('🔄 Arrêt sécurisé du serveur...');
    server.close(() => {
        console.log('✅ Serveur arrêté en sécurité');
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log('🔄 Arrêt sécurisé du serveur...');
    server.close(() => {
        console.log('✅ Serveur arrêté en sécurité');
        process.exit(0);
    });
});
