<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎓 FORMATIONS IA THERMIQUES - LOUNA-AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: rgba(255, 105, 180, 0.1);
            border-radius: 20px;
            border: 2px solid rgba(255, 105, 180, 0.3);
        }

        .header h1 {
            font-size: 3em;
            background: linear-gradient(45deg, #ff69b4, #00ffff, #ffff00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 0 0 20px rgba(255, 105, 180, 0.5); }
            to { text-shadow: 0 0 30px rgba(255, 105, 180, 0.8), 0 0 40px rgba(0, 255, 255, 0.5); }
        }

        .stats-bar {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .stat-item {
            background: rgba(0, 255, 255, 0.1);
            padding: 15px 25px;
            border-radius: 15px;
            border: 2px solid rgba(0, 255, 255, 0.3);
            text-align: center;
            min-width: 150px;
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #00ffff;
            display: block;
        }

        .formations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .formation-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 30px;
            border: 2px solid rgba(255, 105, 180, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .formation-card:hover {
            transform: translateY(-10px);
            border-color: rgba(255, 105, 180, 0.6);
            box-shadow: 0 20px 40px rgba(255, 105, 180, 0.2);
        }

        .formation-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #ff69b4, #00ffff, #ffff00);
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        .formation-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .formation-icon {
            font-size: 3em;
            margin-right: 20px;
        }

        .formation-title {
            font-size: 1.5em;
            font-weight: bold;
            color: #ff69b4;
        }

        .formation-status {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            margin-left: auto;
        }

        .status-active {
            background: rgba(0, 255, 0, 0.2);
            color: #00ff00;
            border: 1px solid #00ff00;
        }

        .status-available {
            background: rgba(255, 255, 0, 0.2);
            color: #ffff00;
            border: 1px solid #ffff00;
        }

        .status-locked {
            background: rgba(255, 0, 0, 0.2);
            color: #ff0000;
            border: 1px solid #ff0000;
        }

        .formation-description {
            margin-bottom: 20px;
            line-height: 1.6;
            color: #cccccc;
        }

        .formation-progress {
            margin-bottom: 20px;
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff69b4, #00ffff);
            transition: width 0.3s ease;
        }

        .formation-actions {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(45deg, #ff69b4, #ff1493);
            color: white;
        }

        .btn-primary:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 20px rgba(255, 105, 180, 0.3);
        }

        .btn-secondary {
            background: rgba(0, 255, 255, 0.2);
            color: #00ffff;
            border: 2px solid #00ffff;
        }

        .btn-secondary:hover {
            background: rgba(0, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .thermal-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 15px;
            border: 2px solid rgba(255, 105, 180, 0.5);
            z-index: 1000;
        }

        .thermal-temp {
            font-size: 2em;
            color: #ff69b4;
            font-weight: bold;
            text-align: center;
        }

        .thermal-status {
            text-align: center;
            margin-top: 10px;
            font-size: 0.9em;
        }

        .learning-stream {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 15px;
            padding: 20px;
            margin-top: 30px;
            border: 2px solid rgba(0, 255, 255, 0.3);
            max-height: 300px;
            overflow-y: auto;
        }

        .stream-title {
            color: #00ffff;
            font-weight: bold;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .stream-item {
            padding: 10px;
            margin-bottom: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 4px solid #ff69b4;
        }

        .stream-time {
            color: #888;
            font-size: 0.8em;
        }

        .stream-event {
            color: #fff;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- HEADER -->
        <div class="header">
            <h1>🎓 FORMATIONS IA THERMIQUES</h1>
            <p>Système d'apprentissage adaptatif basé sur la température CPU</p>
            
            <div class="stats-bar">
                <div class="stat-item">
                    <span class="stat-value" id="qi-display">383</span>
                    <span>QI Évolutif</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value" id="formations-actives">3</span>
                    <span>Formations Actives</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value" id="niveau-apprentissage">Expert</span>
                    <span>Niveau</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value" id="efficacite">94%</span>
                    <span>Efficacité</span>
                </div>
            </div>
        </div>

        <!-- INDICATEUR THERMIQUE -->
        <div class="thermal-indicator">
            <div class="thermal-temp" id="temp-indicator">67.4°C</div>
            <div class="thermal-status" id="thermal-status">🔥 ACTIF</div>
        </div>

        <!-- GRILLE DES FORMATIONS -->
        <div class="formations-grid">
            <!-- Formation Générale Avancée -->
            <div class="formation-card">
                <div class="formation-header">
                    <div class="formation-icon">🧠</div>
                    <div>
                        <div class="formation-title">Formation Générale Avancée</div>
                        <div class="formation-status status-active">✅ ACTIVE</div>
                    </div>
                </div>
                <div class="formation-description">
                    Système d'apprentissage continu qui s'adapte à votre utilisation. Plus vous interagissez, plus l'IA devient intelligente et personnalisée.
                </div>
                <div class="formation-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 85%"></div>
                    </div>
                    <small>Progression: 85% - Niveau Expert</small>
                </div>
                <div class="formation-actions">
                    <button class="btn btn-primary" onclick="lancerFormation('generale')">📊 Voir Détails</button>
                    <button class="btn btn-secondary" onclick="configurerFormation('generale')">⚙️ Configurer</button>
                </div>
            </div>

            <!-- Formation Interfaces Modernes -->
            <div class="formation-card">
                <div class="formation-header">
                    <div class="formation-icon">🎨</div>
                    <div>
                        <div class="formation-title">Interfaces Modernes</div>
                        <div class="formation-status status-active">✅ ACTIVE</div>
                    </div>
                </div>
                <div class="formation-description">
                    Apprentissage des dernières tendances en design d'interface, UX/UI moderne, et création d'expériences utilisateur innovantes.
                </div>
                <div class="formation-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 92%"></div>
                    </div>
                    <small>Progression: 92% - Niveau Maître</small>
                </div>
                <div class="formation-actions">
                    <button class="btn btn-primary" onclick="lancerFormation('interfaces')">🎨 Créer Interface</button>
                    <button class="btn btn-secondary" onclick="voirExemples('interfaces')">👁️ Exemples</button>
                </div>
            </div>

            <!-- Formation Correction Automatique -->
            <div class="formation-card">
                <div class="formation-header">
                    <div class="formation-icon">🔧</div>
                    <div>
                        <div class="formation-title">Correction Automatique</div>
                        <div class="formation-status status-active">✅ ACTIVE</div>
                    </div>
                </div>
                <div class="formation-description">
                    Système intelligent de détection et correction automatique des erreurs de code, optimisation des performances et suggestions d'amélioration.
                </div>
                <div class="formation-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 78%"></div>
                    </div>
                    <small>Progression: 78% - Niveau Avancé</small>
                </div>
                <div class="formation-actions">
                    <button class="btn btn-primary" onclick="lancerFormation('correction')">🔍 Analyser Code</button>
                    <button class="btn btn-secondary" onclick="voirCorrections()">📋 Historique</button>
                </div>
            </div>

            <!-- Formation Mémoire Thermique -->
            <div class="formation-card">
                <div class="formation-header">
                    <div class="formation-icon">🌡️</div>
                    <div>
                        <div class="formation-title">Mémoire Thermique</div>
                        <div class="formation-status status-available">🔥 DISPONIBLE</div>
                    </div>
                </div>
                <div class="formation-description">
                    Apprentissage avancé du système de mémoire thermique : comment la température CPU influence l'intelligence et l'évolution du système.
                </div>
                <div class="formation-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 45%"></div>
                    </div>
                    <small>Progression: 45% - En cours d'apprentissage</small>
                </div>
                <div class="formation-actions">
                    <button class="btn btn-primary" onclick="lancerFormation('thermique')">🚀 Commencer</button>
                    <button class="btn btn-secondary" onclick="voirTheorie('thermique')">📚 Théorie</button>
                </div>
            </div>

            <!-- Formation Cerveau 3D -->
            <div class="formation-card">
                <div class="formation-header">
                    <div class="formation-icon">🧬</div>
                    <div>
                        <div class="formation-title">Cerveau 3D Vivant</div>
                        <div class="formation-status status-available">⚡ NOUVEAU</div>
                    </div>
                </div>
                <div class="formation-description">
                    Maîtrise de la visualisation 3D du cerveau artificiel, compréhension des connexions neuronales et de l'activité synaptique en temps réel.
                </div>
                <div class="formation-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 15%"></div>
                    </div>
                    <small>Progression: 15% - Débutant</small>
                </div>
                <div class="formation-actions">
                    <button class="btn btn-primary" onclick="ouvrirCerveau3D()">🧠 Voir Cerveau</button>
                    <button class="btn btn-secondary" onclick="tutoriel3D()">🎓 Tutoriel</button>
                </div>
            </div>

            <!-- Formation Ollama Intégré -->
            <div class="formation-card">
                <div class="formation-header">
                    <div class="formation-icon">🤖</div>
                    <div>
                        <div class="formation-title">Ollama Intégré</div>
                        <div class="formation-status status-available">🆕 BETA</div>
                    </div>
                </div>
                <div class="formation-description">
                    Formation sur l'utilisation d'Ollama intégré directement dans l'application, gestion des modèles IA et optimisation des performances.
                </div>
                <div class="formation-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 60%"></div>
                    </div>
                    <small>Progression: 60% - Intermédiaire</small>
                </div>
                <div class="formation-actions">
                    <button class="btn btn-primary" onclick="testerOllama()">🧪 Tester</button>
                    <button class="btn btn-secondary" onclick="configOllama()">⚙️ Configuration</button>
                </div>
            </div>
        </div>

        <!-- FLUX D'APPRENTISSAGE -->
        <div class="learning-stream">
            <div class="stream-title">📡 Flux d'Apprentissage Temps Réel</div>
            <div id="learning-events">
                <div class="stream-item">
                    <span class="stream-time">14:32:15</span>
                    <span class="stream-event">🧠 Formation Générale: Nouveau pattern détecté</span>
                </div>
                <div class="stream-item">
                    <span class="stream-time">14:31:42</span>
                    <span class="stream-event">🌡️ Température CPU: 67.4°C - Apprentissage accéléré</span>
                </div>
                <div class="stream-item">
                    <span class="stream-time">14:30:18</span>
                    <span class="stream-event">🎨 Interface: Nouveau composant appris</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Mise à jour temps réel des statistiques
        function mettreAJourStats() {
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // QI
                        if (data.coefficient_intellectuel) {
                            document.getElementById('qi-display').textContent = data.coefficient_intellectuel;
                        }
                        
                        // Température
                        if (data.stats && data.stats.memoire_thermique && data.stats.memoire_thermique.currentTemperature) {
                            const temp = data.stats.memoire_thermique.currentTemperature;
                            document.getElementById('temp-indicator').textContent = temp.toFixed(1) + '°C';
                            
                            // Statut thermique
                            let status = '🔥 ACTIF';
                            if (temp > 70) status = '🚀 TURBO';
                            else if (temp > 60) status = '⚡ RAPIDE';
                            else if (temp < 50) status = '❄️ FROID';
                            
                            document.getElementById('thermal-status').textContent = status;
                        }
                    }
                })
                .catch(error => console.log('Erreur stats:', error));
        }

        // Fonctions des formations
        function lancerFormation(type) {
            alert(`🎓 Lancement formation: ${type}`);
        }

        function configurerFormation(type) {
            alert(`⚙️ Configuration formation: ${type}`);
        }

        function ouvrirCerveau3D() {
            window.open('/3d', '_blank');
        }

        function testerOllama() {
            window.open('/api/ollama/status', '_blank');
        }

        // Ajouter événements d'apprentissage
        function ajouterEvenementApprentissage(message) {
            const events = document.getElementById('learning-events');
            const time = new Date().toLocaleTimeString();
            
            const item = document.createElement('div');
            item.className = 'stream-item';
            item.innerHTML = `
                <span class="stream-time">${time}</span>
                <span class="stream-event">${message}</span>
            `;
            
            events.insertBefore(item, events.firstChild);
            
            // Limiter à 10 événements
            while (events.children.length > 10) {
                events.removeChild(events.lastChild);
            }
        }

        // Simulation d'événements d'apprentissage
        setInterval(() => {
            const events = [
                '🧠 Nouvelle connexion neuronale créée',
                '🌡️ Adaptation thermique en cours',
                '🎨 Optimisation interface détectée',
                '🔧 Correction automatique appliquée',
                '🤖 Modèle Ollama mis à jour',
                '📊 Performance améliorée',
                '🧬 Évolution QI détectée'
            ];
            
            const event = events[Math.floor(Math.random() * events.length)];
            ajouterEvenementApprentissage(event);
        }, 5000);

        // Initialisation
        document.addEventListener('DOMContentLoaded', () => {
            mettreAJourStats();
            setInterval(mettreAJourStats, 5000);
            
            ajouterEvenementApprentissage('🚀 Interface Formations initialisée');
        });
    </script>
</body>
</html>
