#!/usr/bin/env node

/**
 * GESTIONNAIRE DE DÉPENDANCES UNIFIÉ LOUNA-AI
 * ==========================================
 * Ce module gère toutes les dépendances et l'intégrité du système LOUNA-AI
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

class GestionnaireDependancesUnifie {
    constructor() {
        this.config = this.chargerConfiguration();
        this.fichiersEssentiels = [
            'serveur-interface-complete.js',
            'interface-louna-complete.html',
            'gestionnaire-applications-intelligent.js',
            'systeme-scan-intelligent.js',
            'VERSIONS-NON-VALIDEES/memoires-non-validees/memoire-thermique-reelle.js',
            'config-louna-unifie.json'
        ];
        this.dependancesNode = [
            'express',
            'axios',
            'cors',
            'fs-extra'
        ];
    }

    chargerConfiguration() {
        try {
            const configPath = path.join(__dirname, 'config-louna-unifie.json');
            if (fs.existsSync(configPath)) {
                return JSON.parse(fs.readFileSync(configPath, 'utf8'));
            }
        } catch (error) {
            console.log('⚠️ Configuration par défaut utilisée');
        }
        return { louna_ai_config: {} };
    }

    async verifierIntegrite() {
        console.log('🔍 VÉRIFICATION DE L\'INTÉGRITÉ DU SYSTÈME');
        console.log('=========================================');

        const resultats = {
            fichiers_essentiels: true,
            dependances_node: true,
            services_externes: true,
            configuration: true,
            erreurs: []
        };

        // 1. Vérification des fichiers essentiels
        console.log('\n📁 Vérification des fichiers essentiels...');
        for (const fichier of this.fichiersEssentiels) {
            if (fs.existsSync(fichier)) {
                console.log(`✅ ${fichier}`);
            } else {
                console.log(`❌ ${fichier} - MANQUANT`);
                resultats.fichiers_essentiels = false;
                resultats.erreurs.push(`Fichier manquant: ${fichier}`);
            }
        }

        // 2. Vérification des dépendances Node.js
        console.log('\n📦 Vérification des dépendances Node.js...');
        try {
            const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
            for (const dep of this.dependancesNode) {
                if (packageJson.dependencies && packageJson.dependencies[dep]) {
                    console.log(`✅ ${dep} - ${packageJson.dependencies[dep]}`);
                } else {
                    console.log(`❌ ${dep} - NON INSTALLÉ`);
                    resultats.dependances_node = false;
                    resultats.erreurs.push(`Dépendance manquante: ${dep}`);
                }
            }
        } catch (error) {
            console.log('❌ Erreur lecture package.json');
            resultats.dependances_node = false;
            resultats.erreurs.push('package.json inaccessible');
        }

        // 3. Vérification des services externes
        console.log('\n🔗 Vérification des services externes...');
        const ollamaStatus = await this.verifierOllama();
        if (ollamaStatus) {
            console.log('✅ Ollama - Accessible');
        } else {
            console.log('❌ Ollama - Non accessible');
            resultats.services_externes = false;
            resultats.erreurs.push('Ollama non accessible');
        }

        return resultats;
    }

    async verifierOllama() {
        return new Promise((resolve) => {
            exec('curl -s http://localhost:11434/api/tags', (error, stdout, stderr) => {
                if (error) {
                    resolve(false);
                } else {
                    try {
                        const data = JSON.parse(stdout);
                        resolve(data.models && Array.isArray(data.models));
                    } catch (e) {
                        resolve(false);
                    }
                }
            });
        });
    }

    async installerDependancesManquantes() {
        console.log('\n🔧 INSTALLATION DES DÉPENDANCES MANQUANTES');
        console.log('==========================================');

        // Vérifier si package.json existe
        if (!fs.existsSync('package.json')) {
            console.log('📦 Création de package.json...');
            await this.creerPackageJson();
        }

        // Installer les dépendances Node.js
        console.log('📦 Installation des dépendances Node.js...');
        return new Promise((resolve, reject) => {
            exec('npm install', (error, stdout, stderr) => {
                if (error) {
                    console.log('❌ Erreur installation npm:', error.message);
                    reject(error);
                } else {
                    console.log('✅ Dépendances Node.js installées');
                    resolve(true);
                }
            });
        });
    }

    async creerPackageJson() {
        const packageJson = {
            "name": "louna-ai-unifie",
            "version": "1.0.0",
            "description": "LOUNA-AI Système Unifié Complet",
            "main": "serveur-interface-complete.js",
            "scripts": {
                "start": "node serveur-interface-complete.js",
                "dev": "node serveur-interface-complete.js",
                "test": "node test-systeme-complet.js",
                "install-deps": "npm install",
                "check-health": "node gestionnaire-dependances-unifie.js --check",
                "backup": "node gestionnaire-dependances-unifie.js --backup"
            },
            "dependencies": {
                "express": "^4.18.2",
                "axios": "^1.6.0",
                "cors": "^2.8.5",
                "fs-extra": "^11.1.1"
            },
            "keywords": [
                "louna-ai",
                "intelligence-artificielle",
                "memoire-thermique",
                "ollama",
                "guadeloupe"
            ],
            "author": "Jean-Luc Passave <<EMAIL>>",
            "license": "PROPRIETARY",
            "repository": {
                "type": "local",
                "url": "/Volumes/ALDO et MIM/LOUNA-AI-COMPLET"
            },
            "engines": {
                "node": ">=16.0.0",
                "npm": ">=8.0.0"
            }
        };

        fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2));
        console.log('✅ package.json créé');
    }

    async creerSauvegarde() {
        console.log('\n💾 CRÉATION DE SAUVEGARDE');
        console.log('=========================');

        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const dossierSauvegarde = path.join('sauvegardes', `backup-${timestamp}`);

        // Créer le dossier de sauvegarde
        if (!fs.existsSync('sauvegardes')) {
            fs.mkdirSync('sauvegardes');
        }
        fs.mkdirSync(dossierSauvegarde, { recursive: true });

        // Sauvegarder les fichiers essentiels
        for (const fichier of this.fichiersEssentiels) {
            if (fs.existsSync(fichier)) {
                const destination = path.join(dossierSauvegarde, path.basename(fichier));
                fs.copyFileSync(fichier, destination);
                console.log(`✅ Sauvegardé: ${fichier}`);
            }
        }

        // Créer un fichier de métadonnées
        const metadata = {
            timestamp: new Date().toISOString(),
            version: this.config.louna_ai_config.version || '1.0.0',
            fichiers_sauvegardes: this.fichiersEssentiels.filter(f => fs.existsSync(f)),
            taille_totale: this.calculerTailleDossier(dossierSauvegarde)
        };

        fs.writeFileSync(
            path.join(dossierSauvegarde, 'metadata.json'),
            JSON.stringify(metadata, null, 2)
        );

        console.log(`✅ Sauvegarde créée: ${dossierSauvegarde}`);
        return dossierSauvegarde;
    }

    calculerTailleDossier(dossier) {
        let taille = 0;
        const fichiers = fs.readdirSync(dossier);
        for (const fichier of fichiers) {
            const cheminFichier = path.join(dossier, fichier);
            const stats = fs.statSync(cheminFichier);
            taille += stats.size;
        }
        return taille;
    }

    async genererRapportSante() {
        console.log('\n📊 RAPPORT DE SANTÉ DU SYSTÈME');
        console.log('==============================');

        const integrite = await this.verifierIntegrite();
        const rapport = {
            timestamp: new Date().toISOString(),
            statut_global: integrite.fichiers_essentiels && integrite.dependances_node && integrite.services_externes ? 'SAIN' : 'PROBLÈMES DÉTECTÉS',
            details: integrite,
            recommandations: []
        };

        if (!integrite.fichiers_essentiels) {
            rapport.recommandations.push('Restaurer les fichiers manquants depuis une sauvegarde');
        }
        if (!integrite.dependances_node) {
            rapport.recommandations.push('Exécuter: npm install');
        }
        if (!integrite.services_externes) {
            rapport.recommandations.push('Démarrer Ollama: ollama serve');
        }

        // Sauvegarder le rapport
        const fichierRapport = `rapport-sante-${new Date().toISOString().split('T')[0]}.json`;
        fs.writeFileSync(fichierRapport, JSON.stringify(rapport, null, 2));

        console.log(`📄 Rapport sauvegardé: ${fichierRapport}`);
        return rapport;
    }

    async executerMaintenance() {
        console.log('\n🔧 MAINTENANCE AUTOMATIQUE');
        console.log('==========================');

        // 1. Vérification de l'intégrité
        const integrite = await this.verifierIntegrite();

        // 2. Création de sauvegarde si système sain
        if (integrite.fichiers_essentiels) {
            await this.creerSauvegarde();
        }

        // 3. Nettoyage des anciens logs
        this.nettoyerAnciensFichiers('logs', 30);

        // 4. Nettoyage des anciennes sauvegardes
        this.nettoyerAnciensFichiers('sauvegardes', 7);

        // 5. Génération du rapport
        await this.genererRapportSante();

        console.log('✅ Maintenance terminée');
    }

    nettoyerAnciensFichiers(dossier, joursRetention) {
        if (!fs.existsSync(dossier)) return;

        const maintenant = Date.now();
        const seuilSuppression = maintenant - (joursRetention * 24 * 60 * 60 * 1000);

        const fichiers = fs.readdirSync(dossier);
        let supprimés = 0;

        for (const fichier of fichiers) {
            const cheminFichier = path.join(dossier, fichier);
            const stats = fs.statSync(cheminFichier);

            if (stats.mtime.getTime() < seuilSuppression) {
                if (stats.isDirectory()) {
                    fs.rmSync(cheminFichier, { recursive: true });
                } else {
                    fs.unlinkSync(cheminFichier);
                }
                supprimés++;
            }
        }

        if (supprimés > 0) {
            console.log(`🗑️ ${supprimés} ancien(s) fichier(s) supprimé(s) dans ${dossier}`);
        }
    }
}

// Exécution en ligne de commande
if (require.main === module) {
    const gestionnaire = new GestionnaireDependancesUnifie();
    const args = process.argv.slice(2);

    if (args.includes('--check')) {
        gestionnaire.verifierIntegrite().then(console.log);
    } else if (args.includes('--install')) {
        gestionnaire.installerDependancesManquantes();
    } else if (args.includes('--backup')) {
        gestionnaire.creerSauvegarde();
    } else if (args.includes('--maintenance')) {
        gestionnaire.executerMaintenance();
    } else if (args.includes('--health')) {
        gestionnaire.genererRapportSante();
    } else {
        console.log('Usage: node gestionnaire-dependances-unifie.js [--check|--install|--backup|--maintenance|--health]');
    }
}

module.exports = GestionnaireDependancesUnifie;
