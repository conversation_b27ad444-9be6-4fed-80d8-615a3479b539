/**
 * TEST MONITORING NEURONES ET QI
 * Analyse complète des capacités intellectuelles de l'agent
 */

const SystemeCompletIntegreAccelere = require('./systeme-complet-integre-accelere.js');
const MonitoringNeuronesQI = require('./monitoring-neurones-qi.js');

async function testerMonitoringQI() {
    console.log('🧠 TEST MONITORING NEURONES ET QI');
    console.log('==================================');
    
    try {
        // Démarrer le système
        console.log('🚀 Démarrage système...');
        const systeme = new SystemeCompletIntegreAccelere();
        await systeme.demarrerSystemeComplet();
        
        // Initialiser le monitoring
        const monitoring = new MonitoringNeuronesQI(systeme);
        
        // Attendre un peu pour que le système se stabilise
        console.log('⏳ Stabilisation du système...');
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Analyser les capacités intellectuelles
        const analyse = await monitoring.analyserCapacitesIntellectuelles();
        
        // Afficher résumé
        console.log('\n' + '='.repeat(60));
        console.log('📋 RÉSUMÉ EXÉCUTIF:');
        console.log('='.repeat(60));
        console.log(monitoring.afficherResumeCourt());
        
        // Attendre et refaire une analyse pour voir l'évolution
        console.log('\n⏳ Attente pour mesurer l\'évolution...');
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        console.log('\n🔄 DEUXIÈME ANALYSE POUR ÉVOLUTION:');
        console.log('===================================');
        await monitoring.analyserCapacitesIntellectuelles();
        
        console.log('\n✅ MONITORING TERMINÉ');
        
    } catch (error) {
        console.log(`❌ Erreur: ${error.message}`);
    }
}

if (require.main === module) {
    testerMonitoringQI();
}

module.exports = { testerMonitoringQI };
