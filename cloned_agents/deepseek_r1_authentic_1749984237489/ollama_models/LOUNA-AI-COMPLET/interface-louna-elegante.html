<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LOUNA-AI - Mémoire Thermique Vivante</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, 
                #000000 0%, 
                #1a0a1a 25%, 
                #2d1b2d 50%, 
                #4a2c4a 75%, 
                #663d66 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(45deg, 
                rgba(255, 182, 193, 0.1) 0%, 
                rgba(255, 105, 180, 0.15) 50%, 
                rgba(199, 21, 133, 0.2) 100%);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 182, 193, 0.3);
            box-shadow: 0 8px 32px rgba(255, 105, 180, 0.2);
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #ff69b4, #ffb6c1, #ff1493);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(255, 105, 180, 0.5);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { filter: drop-shadow(0 0 20px rgba(255, 105, 180, 0.5)); }
            to { filter: drop-shadow(0 0 30px rgba(255, 105, 180, 0.8)); }
        }

        .subtitle {
            font-size: 1.2em;
            color: #ffb6c1;
            margin-bottom: 10px;
        }

        .status-bar {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .status-item {
            background: linear-gradient(135deg, 
                rgba(0, 0, 0, 0.8) 0%, 
                rgba(255, 182, 193, 0.1) 100%);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 182, 193, 0.2);
            backdrop-filter: blur(5px);
            transition: all 0.3s ease;
        }

        .status-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(255, 105, 180, 0.3);
            border-color: rgba(255, 105, 180, 0.5);
        }

        .status-title {
            font-size: 0.9em;
            color: #ffb6c1;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .status-value {
            font-size: 1.4em;
            font-weight: bold;
            color: #ffffff;
            transition: all 0.3s ease;
        }

        .status-value.updating {
            transform: scale(1.1);
            color: #ff69b4;
            text-shadow: 0 0 10px rgba(255, 105, 180, 0.8);
        }

        .status-detail {
            font-size: 0.8em;
            color: #ffb6c1;
            margin-top: 5px;
        }

        .chat-container {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .chat-section {
            background: linear-gradient(135deg, 
                rgba(0, 0, 0, 0.9) 0%, 
                rgba(255, 182, 193, 0.05) 100%);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 182, 193, 0.2);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
        }

        .chat-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(255, 182, 193, 0.2);
        }

        .chat-title {
            font-size: 1.5em;
            color: #ff69b4;
            margin-left: 10px;
        }

        .chat-messages {
            max-height: 500px;
            overflow-y: auto;
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            border: 1px solid rgba(255, 182, 193, 0.1);
            margin-bottom: 25px;
        }

        .input-container {
            display: flex;
            gap: 15px;
        }

        .chat-input {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid rgba(255, 182, 193, 0.3);
            border-radius: 25px;
            background: rgba(0, 0, 0, 0.7);
            color: #ffffff;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;
        }

        .chat-input:focus {
            border-color: #ff69b4;
            box-shadow: 0 0 20px rgba(255, 105, 180, 0.3);
        }

        .chat-input::placeholder {
            color: #ffb6c1;
        }

        .send-btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(45deg, #ff1493, #ff69b4);
            color: white;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 20, 147, 0.3);
        }

        .send-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 20, 147, 0.5);
        }

        .send-btn:disabled {
            background: rgba(255, 182, 193, 0.3);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .message {
            margin-bottom: 20px;
            padding: 15px 20px;
            border-radius: 15px;
            max-width: 80%;
            word-wrap: break-word;
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message.user {
            background: linear-gradient(135deg, #ff1493, #ff69b4);
            margin-left: auto;
            color: white;
        }

        .message.assistant {
            background: linear-gradient(135deg, 
                rgba(255, 182, 193, 0.2), 
                rgba(255, 105, 180, 0.1));
            border: 1px solid rgba(255, 182, 193, 0.3);
            color: #ffffff;
        }

        .message.system {
            background: linear-gradient(135deg, 
                rgba(255, 215, 0, 0.2), 
                rgba(255, 165, 0, 0.1));
            border: 1px solid rgba(255, 215, 0, 0.3);
            color: #ffd700;
            font-style: italic;
            text-align: center;
            margin: 10px auto;
            max-width: 60%;
            animation: pulse 0.5s ease-in;
        }

        @keyframes pulse {
            0% { transform: scale(0.95); opacity: 0.8; }
            50% { transform: scale(1.02); opacity: 1; }
            100% { transform: scale(1); opacity: 1; }
        }

        .memory-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .memory-card {
            background: linear-gradient(135deg, 
                rgba(0, 0, 0, 0.8) 0%, 
                rgba(255, 182, 193, 0.1) 100%);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 182, 193, 0.2);
            backdrop-filter: blur(5px);
        }

        .memory-card h3 {
            color: #ff69b4;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .memory-stat {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            color: #ffb6c1;
        }

        .memory-stat .value {
            color: #ffffff;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 182, 193, 0.3);
            border-radius: 50%;
            border-top-color: #ff69b4;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .update-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(45deg, #ff1493, #ff69b4);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 14px;
            box-shadow: 0 4px 15px rgba(255, 20, 147, 0.3);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            z-index: 1000;
        }

        .update-indicator.show {
            transform: translateX(0);
        }

        .neurones-animation {

        .qi-evolution-animation {
            animation: qiEvolution 2s ease-in-out;
        }

        @keyframes qiEvolution {
            0% { 
                color: #ffffff; 
                transform: scale(1); 
            }
            50% { 
                color: #ff1493; 
                transform: scale(1.2); 
                text-shadow: 0 0 20px #ff1493; 
            }
            100% { 
                color: #ff69b4; 
                transform: scale(1); 
                text-shadow: 0 0 10px #ff69b4; 
            }
        }            animation: neuronesGlow 1s ease-in-out 3;
        }

        @keyframes neuronesGlow {
            0%, 100% { 
                color: #ffffff; 
                text-shadow: none; 
            }
            50% { 
                color: #ff69b4; 
                text-shadow: 0 0 15px rgba(255, 105, 180, 0.8);
                transform: scale(1.05);
            }
        }

        .real-time-indicator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: #ff69b4;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 12px;
            border: 1px solid rgba(255, 105, 180, 0.3);
            animation: blink 2s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.5; }
        }

        /* Scrollbar personnalisée */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(45deg, #ff1493, #ff69b4);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(45deg, #ff69b4, #ffb6c1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 LOUNA-AI</h1>
            <div class="subtitle">Mémoire Thermique Vivante & Intelligence Adaptative</div>
            <p>Neurones infinis • Accélérateurs KYBER • Mise à jour automatique</p>
        </div>

        <div class="status-bar">
            <div class="status-item">
                <div class="status-title">🧠 Neurones</div>
                <div class="status-value" id="neuronesCount">Chargement...</div>
                <div class="status-detail">Actifs: <span id="neuronesActifs">0</span></div>
            </div>
            <div class="status-item">
                <div class="status-title">⚡ Accélérateurs</div>
                <div class="status-value" id="accelerateursCount">Chargement...</div>
                <div class="status-detail">Auto-installés: <span id="accelerateursAuto">0</span></div>
            </div>
            <div class="status-item">
                <div class="status-title">💾 Mémoire</div>
                <div class="status-value" id="memoireEntries">Chargement...</div>
                <div class="status-detail">Entrées totales</div>
            </div>
            <div class="status-item">
                <div class="status-title">🔄 Cycles</div>
                <div class="status-value" id="cyclesCount">Chargement...</div>
                <div class="status-detail">Auto-cycles</div>
            </div>
            <div class="status-item">
                <div class="status-title">🌡️ Température</div>
                <div class="status-value" id="tempMoyenne">Chargement...</div>
                <div class="status-detail">Moyenne</div>
            </div>
            <div class="status-item">
            <div class="status-item">
                <div class="status-title">🧠 QI Évolutif</div>
                <div class="status-value" id="coefficientQI">127</div>
                <div class="status-detail">Évolution: <span id="evolutionQI">+0</span></div>
            </div>                <div class="status-title">🔄 Dernière MAJ</div>
                <div class="status-value" id="derniereMaj">Jamais</div>
                <div class="status-detail">Mémoire vivante</div>
            </div>
        </div>

        <div class="chat-container">
            <div class="chat-section">
                <div class="chat-header">
                    <span style="font-size: 2em;">🤖</span>
                    <div class="chat-title">Conversation avec LOUNA</div>
                </div>
                
                <div class="chat-messages" id="chatMessages">
                    <div class="message system">
                        🧠 LOUNA-AI initialisé avec mémoire thermique vivante
                    </div>
                </div>
                
                <div class="input-container">
                    <input type="text" id="chatInput" class="chat-input" 
                           placeholder="Posez votre question à LOUNA... (mémoire thermique + Internet si nécessaire)" />
                    <button id="sendBtn" class="send-btn">Envoyer</button>
                </div>
            </div>
        </div>

        <div class="memory-info">
            <div class="memory-card">
                <h3>🔥 Mémoire Instantanée</h3>
                <div class="memory-stat">
                    <span>Entrées:</span>
                    <span class="value" id="instantEntries">0</span>
                </div>
                <div class="memory-stat">
                    <span>Température:</span>
                    <span class="value">≥ 0.8</span>
                </div>
            </div>
            
            <div class="memory-card">
                <h3>⚡ Mémoire Court Terme</h3>
                <div class="memory-stat">
                    <span>Entrées:</span>
                    <span class="value" id="shortTermEntries">0</span>
                </div>
                <div class="memory-stat">
                    <span>Température:</span>
                    <span class="value">≥ 0.6</span>
                </div>
            </div>
            
            <div class="memory-card">
                <h3>💼 Mémoire de Travail</h3>
                <div class="memory-stat">
                    <span>Entrées:</span>
                    <span class="value" id="workingEntries">0</span>
                </div>
                <div class="memory-stat">
                    <span>Température:</span>
                    <span class="value">≥ 0.4</span>
                </div>
            </div>
            
            <div class="memory-card">
                <h3>📚 Mémoire Long Terme</h3>
                <div class="memory-stat">
                    <span>Entrées:</span>
                    <span class="value" id="longTermEntries">0</span>
                </div>
                <div class="memory-stat">
                    <span>Température:</span>
                    <span class="value">< 0.2</span>
                </div>
            </div>
        </div>
    </div>

    <div class="update-indicator" id="updateIndicator">
        🔄 Mise à jour mémoire en cours...
    </div>

    <div class="real-time-indicator">
        🔴 TEMPS RÉEL
    </div>

    <script>
        let isProcessing = false;
        let lastUpdateTime = null;
        let lastStats = {};

        // Fonction pour ajouter un message
        function addMessage(content, type = 'assistant') {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = content;
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Fonction pour animer les changements
        function updateDisplayWithAnimation(elementId, newValue) {
            const element = document.getElementById(elementId);
            if (element && element.textContent !== newValue.toString()) {
                // Animation de changement
                element.classList.add('updating');
                element.textContent = newValue;
                
                setTimeout(() => {
                    element.classList.remove('updating');
                }, 500);
            }
        }

        // Fonction pour afficher augmentation neurones
        function showNeuronesIncrease(oldCount, newCount) {
            const increase = newCount - oldCount;
            addMessage(`🧠 NEURONES AUTO-INSTALLÉS: +${(increase / 1000000).toFixed(1)}M neurones ! Total: ${(newCount / 1000000).toFixed(1)}M`, 'system');
            
            // Animation spéciale pour les neurones
            const neuronesElement = document.getElementById('neuronesCount');
            neuronesElement.classList.add('neurones-animation');
            setTimeout(() => {
                neuronesElement.classList.remove('neurones-animation');
            }, 3000);
        }

        // Fonction pour afficher augmentation accélérateurs
        function showAccelerateursIncrease(oldCount, newCount) {
            const increase = newCount - oldCount;
            addMessage(`⚡ ACCÉLÉRATEURS KYBER AUTO-INSTALLÉS: +${increase} ! Total: ${newCount}`, 'system');
            
            // Animation spéciale pour les accélérateurs
            const accelElement = document.getElementById('accelerateursCount');
            accelElement.classList.add('neurones-animation');
            setTimeout(() => {
                accelElement.classList.remove('neurones-animation');
            }, 3000);
        }

        // Fonction pour mettre à jour les statistiques
        // Fonction pour mettre à jour les stats de sécurité
        async function updateSecurityStats() {
            try {
                const response = await fetch('/stats-securite');
                const data = await response.json();
                
                if (data.success) {
                    const stats = data.stats;
                    const tauxSecurite = stats.tauxSecurite || 0;
                    updateDisplayWithAnimation('securiteGoogle', `${tauxSecurite}%`);
                }
            } catch (error) {
                console.error('Erreur stats sécurité:', error);
            }
        }

        async function updateStats() {
            try {
                const response = await fetch('/stats');
                const data = await response.json();
                
                if (data.success) {
                    const stats = data.stats;
                    
                    // Vérifier changements neurones
                    const currentNeurones = stats.neurones_system.total_installed;
                    if (lastStats.neurones && currentNeurones > lastStats.neurones) {
                        showNeuronesIncrease(lastStats.neurones, currentNeurones);
                    }
                    
                    // Vérifier changements accélérateurs
                    const currentAccel = stats.kyber_accelerators.active;
                    if (lastStats.accelerateurs && currentAccel > lastStats.accelerateurs) {
                        showAccelerateursIncrease(lastStats.accelerateurs, currentAccel);
                    }

                    // Vérifier changements mémoire
                    const currentMemoire = stats.totalEntries;
                    if (lastStats.memoire && currentMemoire > lastStats.memoire) {
                        const increase = currentMemoire - lastStats.memoire;
                        addMessage(`💾 NOUVELLES ENTRÉES MÉMOIRE: +${increase} ! Total: ${currentMemoire}`, 'system');
                    }
                    
                    // Mettre à jour affichage avec animations
                    updateDisplayWithAnimation('neuronesCount', `${(currentNeurones / 1000000).toFixed(1)}M`);
                    updateDisplayWithAnimation('neuronesActifs', `${(stats.neurones_system.active_count / 1000000).toFixed(1)}M`);
                    updateDisplayWithAnimation('accelerateursCount', `${currentAccel}`);
                    updateDisplayWithAnimation('accelerateursAuto', `${stats.kyber_accelerators.auto_installs || 0}`);
                    updateDisplayWithAnimation('memoireEntries', `${currentMemoire}`);
                    updateDisplayWithAnimation('cyclesCount', `${stats.automation.auto_cycles}`);
                    updateDisplayWithAnimation('tempMoyenne', `${(stats.averageTemperature * 100).toFixed(1)}°C`);
                    
                    // Mettre à jour les détails mémoire
                    updateDisplayWithAnimation('instantEntries', stats.instantEntries);
                    updateDisplayWithAnimation('shortTermEntries', stats.shortTermEntries);
                    updateDisplayWithAnimation('workingEntries', stats.workingMemoryEntries);
                    updateDisplayWithAnimation('longTermEntries', stats.longTermEntries);
                    
                    // Mettre à jour mémoire vivante si disponible
                    if (stats.living_memory) {
                        updateDisplayWithAnimation('derniereMaj', 

                    // Mettre à jour QI évolutif
                    if (data.qi_actuel) {
                        updateDisplayWithAnimation("coefficientQI", data.qi_actuel);
                        if (data.evolution_qi && data.evolution_qi > 0) {
                            updateDisplayWithAnimation("evolutionQI", `+${data.evolution_qi}`);
                            addMessage(`🧠 ÉVOLUTION QI: ${data.qi_actuel - data.evolution_qi} → ${data.qi_actuel} (+${data.evolution_qi})`, "system");
                        }
                    }                    
                    // Mettre à jour stats sécurité Google
                    updateSecurityStats();
                            new Date(stats.living_memory.last_update).toLocaleTimeString());
                    }
                    

                    // Mettre à jour QI évolutif depuis les stats
                    if (data.coefficient_intellectuel) {
                        const currentQI = data.coefficient_intellectuel;
                        const evolutionQI = data.evolution_qi || 0;
                        updateDisplayWithAnimation("coefficientQI", currentQI);
                        if (evolutionQI > 0) {
                            updateDisplayWithAnimation("evolutionQI", `+${evolutionQI}`);
                        }
                    }                    // Sauvegarder stats pour comparaison
                    lastStats = {
                        neurones: currentNeurones,
                        accelerateurs: currentAccel,
                        memoire: currentMemoire
                    };
                }
            } catch (error) {
                console.error('Erreur mise à jour stats:', error);
            }
        }

        // Fonction pour envoyer un message
        async function sendMessage() {
            if (isProcessing) return;
            
            const input = document.getElementById('chatInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            isProcessing = true;
            const sendBtn = document.getElementById('sendBtn');
            sendBtn.disabled = true;
            sendBtn.innerHTML = '<span class="loading"></span> Traitement...';
            
            // Ajouter message utilisateur
            addMessage(message, 'user');
            input.value = '';
            
            try {
                // Envoyer à LOUNA avec mémoire thermique
                const response = await fetch('/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ message })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // Ajouter réponse de LOUNA
                    addMessage(data.response, 'assistant');
                    
                    // Afficher source de l'information
                    if (data.source) {
                        addMessage(`📍 Source: ${data.source}`, 'system');
                    }
                    
                    // Mettre à jour les stats immédiatement après une question
                    setTimeout(updateStats, 1000);
                } else {
                    addMessage(`❌ Erreur: ${data.error}`, 'system');
                }
                
            } catch (error) {
                addMessage(`❌ Erreur de connexion: ${error.message}`, 'system');
            } finally {
                isProcessing = false;
                sendBtn.disabled = false;
                sendBtn.textContent = 'Envoyer';
            }
        }

        // Fonction de mise à jour automatique de la mémoire
        async function autoUpdateMemory() {
            const indicator = document.getElementById('updateIndicator');
            indicator.classList.add('show');
            
            try {
                const response = await fetch('/auto-update-memory', {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    lastUpdateTime = new Date();
                    document.getElementById('derniereMaj').textContent = 
                        lastUpdateTime.toLocaleTimeString();
                    
                    addMessage('🔄 Mémoire mise à jour automatiquement', 'system');
                    await updateStats();
                }
            } catch (error) {
                console.error('Erreur mise à jour auto:', error);
            } finally {
                setTimeout(() => {
                    indicator.classList.remove('show');
                }, 3000);
            }
        }

        // Event listeners
        document.getElementById('sendBtn').addEventListener('click', sendMessage);
        document.getElementById('chatInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            updateStats();
            
            // Mise à jour automatique des stats toutes les 2 secondes (encore plus fréquent)
            setInterval(updateStats, 2000);
            
            // Mise à jour automatique de la mémoire toutes les 5 minutes
            setInterval(autoUpdateMemory, 300000);
            
            addMessage('🧠 Bonjour ! Je suis LOUNA avec mémoire thermique vivante. Je consulte d\'abord ma mémoire, puis Internet si nécessaire, et je mets à jour mes connaissances automatiquement. Vous verrez les neurones s\'ajouter en temps réel ! L\'input est maintenant en bas pour plus de confort.', 'assistant');
        });
    </script>
</body>
</html>

<script>
// CORRECTION QI ÉVOLUTIF - MISE À JOUR AUTOMATIQUE
(function() {
    // Fonction pour mettre à jour QI depuis serveur
    async function updateQIFromServer() {
        try {
            const response = await fetch("/stats");
            const data = await response.json();
            
            if (data.success && data.coefficient_intellectuel) {
                const currentQI = data.coefficient_intellectuel;
                const qiElement = document.getElementById("coefficientQI");
                if (qiElement) {
                    qiElement.textContent = currentQI;
                    console.log(`🧠 QI mis à jour: ${currentQI}`);
                }
            }
        } catch (error) {
            console.error("Erreur mise à jour QI:", error);
        }
    }

    // Attendre que la page soit chargée
    document.addEventListener('DOMContentLoaded', function() {
        // Mettre à jour QI immédiatement
        updateQIFromServer();
        
        // Mettre à jour QI toutes les 5 secondes
        setInterval(updateQIFromServer, 5000);
    });
})();
</script>
