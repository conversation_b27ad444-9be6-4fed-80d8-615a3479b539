/**
 * Utilitaires pour programme_personnalise_1749006756214
 */

class Utilitaires {
    static formaterDate(date = new Date()) {
        return date.toISOString().split('T')[0];
    }
    
    static validerEntree(valeur, type = 'string') {
        if (type === 'string') return typeof valeur === 'string' && valeur.length > 0;
        if (type === 'number') return typeof valeur === 'number' && !isNaN(valeur);
        if (type === 'array') return Array.isArray(valeur);
        return false;
    }
    
    static afficherProgres(actuel, total) {
        const pourcentage = Math.round((actuel / total) * 100);
        const barre = '█'.repeat(Math.floor(pourcentage / 5)) + '░'.repeat(20 - Math.floor(pourcentage / 5));
        process.stdout.write(`\r[${barre}] ${pourcentage}%`);
    }
}

module.exports = Utilitaires;
