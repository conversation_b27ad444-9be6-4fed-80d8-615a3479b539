/**
 * Script pour corriger le fichier server-luna.js
 * Ce script corrige les erreurs suivantes :
 * - Identifier 'thermalMemory' has already been declared
 */

const fs = require('fs');
const path = require('path');

// Chemin vers le fichier server-luna.js
const SERVER_LUNA_FILE = path.join(__dirname, 'server-luna.js');

// Fonction pour corriger le fichier server-luna.js
function fixServerLuna() {
  try {
    if (fs.existsSync(SERVER_LUNA_FILE)) {
      // Lire le contenu du fichier
      let content = fs.readFileSync(SERVER_LUNA_FILE, 'utf8');
      
      // Créer une sauvegarde du fichier original
      fs.writeFileSync(`${SERVER_LUNA_FILE}.bak`, content, 'utf8');
      console.log('Sauvegarde du fichier server-luna.js créée');
      
      // Remplacer les imports des services
      content = content.replace(
        /const ThermalMemory = require\(['"]\.\/services\/thermal-memory['"]\);[\r\n]+const BrainPresence = require\(['"]\.\/services\/brain-presence['"]\);/g,
        ''
      );
      
      // Remplacer l'initialisation des services
      content = content.replace(
        /\/\/ Initialiser la mémoire thermique[\r\n]+const thermalMemory = new ThermalMemory\(path\.join\(__dirname, ['"]data\/memory\/thermal_memory\.json['"]\)\);[\r\n]+[\r\n]+\/\/ Initialiser le service de présence cérébrale[\r\n]+const brainPresence = new BrainPresence\(thermalMemory\);/g,
        ''
      );
      
      // Remplacer la déclaration de thermalMemory
      content = content.replace(
        /\/\/ Charger ou initialiser le module de mémoire thermique[\r\n]+let thermalMemory;/g,
        '// Charger ou initialiser le module de mémoire thermique\nlet thermalMemory;\nlet brainPresence;'
      );
      
      // Remplacer la déclaration de brainPresence
      content = content.replace(
        /let thermalMemory;[\r\n]+let temperatureRegulation;[\r\n]+let slidingThermalZones;[\r\n]+let brainPresence;/g,
        'let thermalMemory;\nlet temperatureRegulation;\nlet slidingThermalZones;'
      );
      
      // Ajouter l'import du service de mémoire thermique
      content = content.replace(
        /try {[\r\n]+  const ThermalMemory = require\(['"]\.\/thermal-memory\/thermal-memory['"]\);/g,
        'try {\n  // Essayer d\'abord d\'utiliser le service de mémoire thermique personnalisé\n  try {\n    const ThermalMemory = require(\'./services/thermal-memory\');\n    thermalMemory = new ThermalMemory(path.join(__dirname, \'data/memory/thermal_memory.json\'));\n    console.log(\'Service de mémoire thermique personnalisé utilisé\');\n  } catch (serviceError) {\n    console.log(\'Service de mémoire thermique personnalisé non disponible, utilisation du module par défaut\');\n    const ThermalMemory = require(\'./thermal-memory/thermal-memory\');'
      );
      
      // Ajouter l'import du service de présence cérébrale
      content = content.replace(
        /\/\/ Initialiser le module de présence du cerveau[\r\n]+  try {[\r\n]+    const BrainPresence = require\(['"]\.\/lib\/brain-presence['"]\);/g,
        '// Initialiser le module de présence du cerveau\n  try {\n    // Essayer d\'abord d\'utiliser le service de présence cérébrale personnalisé\n    try {\n      const BrainPresence = require(\'./services/brain-presence\');\n      brainPresence = new BrainPresence(thermalMemory);\n      console.log(\'Service de présence cérébrale personnalisé utilisé\');\n    } catch (serviceError) {\n      console.log(\'Service de présence cérébrale personnalisé non disponible, utilisation du module par défaut\');\n      const BrainPresence = require(\'./lib/brain-presence\');'
      );
      
      // Ajouter la fermeture du bloc try pour le service de mémoire thermique
      content = content.replace(
        /  thermalMemory = new ThermalMemory\(\);[\r\n]+  console\.log\('Mémoire thermique initialisée'\);/g,
        '  thermalMemory = new ThermalMemory();\n  }\n  console.log(\'Mémoire thermique initialisée\');'
      );
      
      // Ajouter la fermeture du bloc try pour le service de présence cérébrale
      content = content.replace(
        /    brainPresence = new BrainPresence\({[\r\n]+      backgroundActivityInterval: 3000,[\r\n]+      presenceUpdateInterval: 1000,[\r\n]+      thoughtGenerationInterval: 10000,[\r\n]+      autoActivate: true,[\r\n]+      debug: true[\r\n]+    }, thermalMemory\);/g,
        '    brainPresence = new BrainPresence({\n      backgroundActivityInterval: 3000,\n      presenceUpdateInterval: 1000,\n      thoughtGenerationInterval: 10000,\n      autoActivate: true,\n      debug: true\n    }, thermalMemory);\n    }'
      );
      
      // Ajouter l'initialisation du service de présence cérébrale
      content = content.replace(
        /\/\/ Configurer les événements de présence du cerveau[\r\n]+if \(brainPresence\) {/g,
        '// Initialiser le service de présence cérébrale\nif (brainPresence && typeof brainPresence.initialize === \'function\') {\n  brainPresence.initialize();\n}\n\n// Configurer les événements de présence du cerveau\nif (brainPresence) {'
      );
      
      // Écrire le contenu modifié dans le fichier
      fs.writeFileSync(SERVER_LUNA_FILE, content, 'utf8');
      console.log('Fichier server-luna.js corrigé');
      
      return true;
    } else {
      console.error('Fichier server-luna.js non trouvé');
      return false;
    }
  } catch (error) {
    console.error(`Erreur lors de la correction du fichier server-luna.js: ${error.message}`);
    return false;
  }
}

// Fonction principale
function main() {
  console.log('=== CORRECTION DU FICHIER SERVER-LUNA.JS ===');
  
  // Corriger le fichier server-luna.js
  const success = fixServerLuna();
  
  if (success) {
    console.log('=== CORRECTION TERMINÉE ===');
    console.log('Veuillez redémarrer le serveur Luna pour appliquer les corrections');
  } else {
    console.log('=== ÉCHEC DE LA CORRECTION ===');
    console.log('Veuillez vérifier le fichier server-luna.js manuellement');
  }
}

// Exécuter la fonction principale
main();
