#!/usr/bin/env node

/**
 * 🧪 TEST API OLLAMA INTÉGRÉ
 * 
 * Test des API REST pour Ollama intégré
 */

const axios = require('axios').default;

async function testerAPIServeur() {
    console.log('🧪 TEST API OLLAMA SERVEUR');
    console.log('==========================');
    
    try {
        // Test API Status
        console.log('\n📊 TEST API STATUS');
        const response_status = await axios.get('http://localhost:3000/api/ollama/status', { timeout: 5000 });
        console.log('✅ API Status:', JSON.stringify(response_status.data, null, 2));
        
        // Test API Test complet
        console.log('\n🧪 TEST API COMPLET');
        const response_test = await axios.post('http://localhost:3000/api/ollama/test', {}, { timeout: 10000 });
        console.log('✅ API Test:', JSON.stringify(response_test.data, null, 2));
        
        console.log('\n🎉 TOUS LES TESTS API RÉUSSIS !');
        
    } catch (error) {
        console.error('❌ Erreur API:', error.message);
        if (error.response) {
            console.error('📊 Status:', error.response.status);
            console.error('📋 Data:', error.response.data);
        }
    }
}

// Lancer test
testerAPIServeur();
