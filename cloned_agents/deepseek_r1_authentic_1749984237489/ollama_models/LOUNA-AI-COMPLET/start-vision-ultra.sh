#!/bin/bash

# Script de démarrage pour le serveur Vision Ultra
# Créé par <PERSON>, Sainte-Anne, Guadeloupe (97180)

# Définir les couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Afficher le logo
echo -e "${PURPLE}"
echo "██╗   ██╗██╗███████╗██╗ ██████╗ ███╗   ██╗    ██╗   ██╗██╗  ████████╗██████╗  █████╗ "
echo "██║   ██║██║██╔════╝██║██╔═══██╗████╗  ██║    ██║   ██║██║  ╚══██╔══╝██╔══██╗██╔══██╗"
echo "██║   ██║██║███████╗██║██║   ██║██╔██╗ ██║    ██║   ██║██║     ██║   ██████╔╝███████║"
echo "╚██╗ ██╔╝██║╚════██║██║██║   ██║██║╚██╗██║    ██║   ██║██║     ██║   ██╔══██╗██╔══██║"
echo " ╚████╔╝ ██║███████║██║╚██████╔╝██║ ╚████║    ╚██████╔╝███████╗██║   ██║  ██║██║  ██║"
echo "  ╚═══╝  ╚═╝╚══════╝╚═╝ ╚═════╝ ╚═╝  ╚═══╝     ╚═════╝ ╚══════╝╚═╝   ╚═╝  ╚═╝╚═╝  ╚═╝"
echo -e "${NC}"

echo -e "${CYAN}Créé par <PERSON>, Sainte-Anne, Guadeloupe (97180)${NC}"
echo ""

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
    echo -e "${RED}Node.js n'est pas installé. Veuillez installer Node.js pour exécuter ce serveur.${NC}"
    exit 1
fi

# Vérifier si le fichier server-vision-ultra.js existe
if [ ! -f "server-vision-ultra.js" ]; then
    echo -e "${RED}Le fichier server-vision-ultra.js n'existe pas dans le répertoire courant.${NC}"
    exit 1
fi

# Vérifier si les modules Node.js sont installés
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}Installation des dépendances Node.js...${NC}"
    npm install express socket.io http path fs os express-fileupload uuid ejs
fi

# Arrêter les serveurs existants
echo -e "${YELLOW}Arrêt des serveurs existants...${NC}"
pkill -f "node server-" || true

# Démarrer le serveur Vision Ultra
echo -e "${GREEN}Démarrage du serveur Vision Ultra...${NC}"
node server-vision-ultra.js

# Ce code ne sera jamais atteint tant que le serveur est en cours d'exécution
echo -e "${RED}Le serveur Vision Ultra s'est arrêté.${NC}"
