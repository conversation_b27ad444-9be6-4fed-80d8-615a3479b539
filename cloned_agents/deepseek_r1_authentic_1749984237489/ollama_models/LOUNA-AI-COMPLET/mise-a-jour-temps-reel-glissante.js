/**
 * MISE À JOUR TEMPS RÉEL POUR MÉMOIRE GLISSANTE
 * Compatible avec la mémoire thermique glissante
 */

class MiseAJourTempsReelGlissante {
    constructor(memoireThermique) {
        this.memoireThermique = memoireThermique;
        this.bonnesReponses = new Map();
        this.corrections = new Map();
    }

    // RENFORCER MÉMOIRE APRÈS BONNE RÉPONSE (COMPATIBLE GLISSANTE)
    renforcerMemoire(question, reponse, source) {
        if (!this.memoireThermique) return;

        // Rechercher mémoires liées à cette réponse
        const memoiresLiees = this.memoireThermique.rechercher(question, 5);
        
        for (const memoire of memoiresLiees) {
            if (memoire.pertinence > 0.5) {
                // Obtenir la mémoire complète
                const memoireData = this.memoireThermique.memoires.get(memoire.id);
                if (memoireData) {
                    // Augmenter utilisation
                    memoireData.utilisation++;
                    memoireData.dernierAcces = Date.now();
                    
                    // La température sera recalculée automatiquement par le glissement
                    console.log(`🔥 Mémoire renforcée: ${memoire.id.substr(-9)} (util: ${memoireData.utilisation})`);
                }
            }
        }

        // Sauvegarder les changements
        this.memoireThermique.sauvegarderMemoire();
    }

    // CORRIGER ERREUR IMMÉDIATEMENT
    corrigerErreur(question, mauvaiseReponse, bonneReponse) {
        console.log(`🔧 Correction erreur: "${mauvaiseReponse}" → "${bonneReponse}"`);
        
        // Stocker la correction
        this.corrections.set(question, {
            erreur: mauvaiseReponse,
            correction: bonneReponse,
            timestamp: Date.now()
        });

        // Ajouter la bonne réponse en mémoire avec importance élevée
        if (this.memoireThermique) {
            this.memoireThermique.stocker(
                `CORRECTION: ${question} = ${bonneReponse} (PAS ${mauvaiseReponse})`,
                'Correction automatique',
                0.95 // Importance très élevée
            );
        }
    }

    // INTÉGRER FEEDBACK UTILISATEUR
    integrerFeedback(question, reponse, feedback) {
        if (feedback === 'correct' || feedback === 'bon') {
            this.renforcerMemoire(question, reponse, 'feedback_positif');
            console.log(`✅ Feedback positif intégré pour: "${question}"`);
        } else if (feedback === 'incorrect' || feedback === 'faux') {
            // Rechercher et diminuer température des mémoires liées
            const memoiresLiees = this.memoireThermique?.rechercher(question, 3) || [];
            for (const memoire of memoiresLiees) {
                const memoireData = this.memoireThermique.memoires.get(memoire.id);
                if (memoireData) {
                    // Diminuer importance au lieu de température directement
                    memoireData.importance = Math.max(0.1, memoireData.importance - 0.2);
                    console.log(`❌ Importance réduite: ${memoire.id.substr(-9)} (${memoireData.importance})`);
                }
            }
            console.log(`❌ Feedback négatif intégré pour: "${question}"`);
        }
    }

    // OPTIMISER ORGANISATION MÉMOIRE
    optimiserOrganisation() {
        if (!this.memoireThermique) return;

        console.log(`🔧 Optimisation organisation mémoire glissante...`);
        
        // Identifier mémoires très utilisées
        const memoiresTresUtilisees = [];
        for (const [id, memoire] of this.memoireThermique.memoires) {
            if (memoire.utilisation > 5) {
                memoiresTresUtilisees.push({ id, memoire });
            }
        }

        // Augmenter leur importance (la température suivra automatiquement)
        for (const { id, memoire } of memoiresTresUtilisees) {
            memoire.importance = Math.min(1.0, memoire.importance + 0.05);
        }

        console.log(`🔥 ${memoiresTresUtilisees.length} mémoires optimisées`);
        this.memoireThermique.sauvegarderMemoire();
    }

    // STATISTIQUES
    getStats() {
        return {
            bonnes_reponses: this.bonnesReponses.size,
            corrections: this.corrections.size,
            derniere_optimisation: this.derniereOptimisation || 'Jamais'
        };
    }
}

module.exports = { MiseAJourTempsReelGlissante };
