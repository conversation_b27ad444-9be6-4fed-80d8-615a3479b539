#!/bin/bash

# Script pour démarrer le serveur MCP et tester la connexion
# Créé par <PERSON>, Sainte-Anne, Guadeloupe (97180)

# Définir les couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Afficher le logo
echo -e "${PURPLE}"
echo "██╗   ██╗██╗███████╗██╗ ██████╗ ███╗   ██╗    ██╗   ██╗██╗  ████████╗██████╗  █████╗ "
echo "██║   ██║██║██╔════╝██║██╔═══██╗████╗  ██║    ██║   ██║██║  ╚══██╔══╝██╔══██╗██╔══██╗"
echo "██║   ██║██║███████╗██║██║   ██║██╔██╗ ██║    ██║   ██║██║     ██║   ██████╔╝███████║"
echo "╚██╗ ██╔╝██║╚════██║██║██║   ██║██║╚██╗██║    ██║   ██║██║     ██║   ██╔══██╗██╔══██║"
echo " ╚████╔╝ ██║███████║██║╚██████╔╝██║ ╚████║    ╚██████╔╝███████╗██║   ██║  ██║██║  ██║"
echo "  ╚═══╝  ╚═╝╚══════╝╚═╝ ╚═════╝ ╚═╝  ╚═══╝     ╚═════╝ ╚══════╝╚═╝   ╚═╝  ╚═╝╚═╝  ╚═╝"
echo -e "${NC}"

echo -e "${CYAN}Créé par <PERSON>, Sainte-Anne, Guadeloupe (97180)${NC}"
echo ""

# Chemin vers le répertoire de travail
WORK_DIR="/Volumes/seagate/Jarvis_Working/code/deepseek-node-ui"

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
    echo -e "${RED}Node.js n'est pas installé. Veuillez installer Node.js pour exécuter ce serveur.${NC}"
    exit 1
fi

# Vérifier si le fichier server-luna.js existe
if [ ! -f "$WORK_DIR/server-luna.js" ]; then
    echo -e "${RED}Le fichier server-luna.js n'existe pas dans le répertoire de travail.${NC}"
    exit 1
fi

# Créer le dossier de configuration s'il n'existe pas
if [ ! -d "$WORK_DIR/data/config" ]; then
    echo -e "${YELLOW}Création du dossier de configuration...${NC}"
    mkdir -p "$WORK_DIR/data/config"
fi

# S'assurer que la configuration MCP est correcte
echo -e "${YELLOW}Configuration du MCP avec accès au bureau...${NC}"
cat > "$WORK_DIR/data/config/mcp-config.json" << EOF
{
  "port": 3002,
  "allowInternet": true,
  "allowDesktop": true,
  "allowSystemCommands": true,
  "debug": true
}
EOF
echo -e "${GREEN}Configuration MCP mise à jour avec accès Internet et bureau activés${NC}"

# Démarrer le serveur MCP uniquement
echo -e "${GREEN}Démarrage du serveur MCP pour test...${NC}"
cd "$WORK_DIR" && node mcp/standalone-mcp.js &
MCP_PID=$!

# Attendre que le serveur démarre
echo -e "${YELLOW}Attente du démarrage du serveur MCP...${NC}"
sleep 3

# Vérifier si le serveur est toujours en cours d'exécution
if kill -0 $MCP_PID 2>/dev/null; then
    echo -e "${GREEN}Serveur MCP démarré avec succès (PID: $MCP_PID)${NC}"
    
    # Exécuter le script de test
    echo -e "${YELLOW}Exécution du test de connexion MCP...${NC}"
    cd "$WORK_DIR" && node test-mcp-connection.js
    
    # Attendre que l'utilisateur appuie sur Ctrl+C
    echo -e "${YELLOW}Appuyez sur Ctrl+C pour arrêter le serveur MCP${NC}"
    wait $MCP_PID
else
    echo -e "${RED}Erreur lors du démarrage du serveur MCP${NC}"
    exit 1
fi
