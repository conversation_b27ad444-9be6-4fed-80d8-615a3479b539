#!/usr/bin/env node

/**
 * 🚀 LOUNA-AI APPLICATION COMPLÈTE
 * ================================
 * Application desktop complète avec interface graphique
 * Basée sur la configuration finale verrouillée
 */

const { app, BrowserWindow, Menu, Tray, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const { spawn, exec } = require('child_process');
const fs = require('fs');

// Import du serveur LOUNA-AI validé
const ServeurInterfaceComplete = require('./serveur-interface-complete.js');

class LounaAIApp {
    constructor() {
        this.mainWindow = null;
        this.tray = null;
        this.serveurLouna = null;
        this.serveurProcess = null;
        this.isQuitting = false;
        
        console.log('🚀 LOUNA-AI APPLICATION COMPLÈTE');
        console.log('================================');
        
        this.initialiserApp();
    }

    async initialiserApp() {
        // Attendre que l'app soit prête
        await app.whenReady();
        
        // Créer l'interface principale
        this.creerFenetrePrincipale();
        
        // Créer le menu système
        this.creerMenu();
        
        // Créer l'icône système
        this.creerTray();
        
        // Démarrer le serveur LOUNA-AI
        await this.demarrerServeurLouna();
        
        // Configurer les événements
        this.configurerEvenements();
        
        console.log('✅ LOUNA-AI Application initialisée');
    }

    creerFenetrePrincipale() {
        this.mainWindow = new BrowserWindow({
            width: 1400,
            height: 900,
            minWidth: 1200,
            minHeight: 800,
            title: 'LOUNA-AI - Intelligence Artificielle Complète',
            icon: this.obtenirIcone(),
            webPreferences: {
                nodeIntegration: true,
                contextIsolation: false,
                enableRemoteModule: true
            },
            show: false, // Ne pas afficher immédiatement
            titleBarStyle: 'default',
            backgroundColor: '#1a1a2e',
            darkTheme: true
        });

        // Charger l'interface LOUNA-AI
        this.mainWindow.loadFile('interface-louna-complete.html');

        // Afficher quand prêt
        this.mainWindow.once('ready-to-show', () => {
            this.mainWindow.show();
            this.mainWindow.focus();
            
            // Message de bienvenue
            this.afficherNotification('LOUNA-AI Démarrée', 'Intelligence artificielle complète activée');
        });

        // Gestion fermeture
        this.mainWindow.on('close', (event) => {
            if (!this.isQuitting) {
                event.preventDefault();
                this.mainWindow.hide();
                this.afficherNotification('LOUNA-AI', 'Application minimisée dans la barre système');
            }
        });

        // Ouvrir DevTools en développement
        if (process.env.NODE_ENV === 'development') {
            this.mainWindow.webContents.openDevTools();
        }
    }

    creerMenu() {
        const template = [
            {
                label: 'LOUNA-AI',
                submenu: [
                    {
                        label: 'À propos de LOUNA-AI',
                        click: () => this.afficherAPropos()
                    },
                    { type: 'separator' },
                    {
                        label: 'Préférences...',
                        accelerator: 'CmdOrCtrl+,',
                        click: () => this.ouvrirPreferences()
                    },
                    { type: 'separator' },
                    {
                        label: 'Quitter LOUNA-AI',
                        accelerator: 'CmdOrCtrl+Q',
                        click: () => this.quitterApp()
                    }
                ]
            },
            {
                label: 'IA',
                submenu: [
                    {
                        label: 'Nouvelle Conversation',
                        accelerator: 'CmdOrCtrl+N',
                        click: () => this.nouvelleConversation()
                    },
                    {
                        label: 'Statistiques IA',
                        accelerator: 'CmdOrCtrl+I',
                        click: () => this.afficherStatistiques()
                    },
                    { type: 'separator' },
                    {
                        label: 'Test QI Avancé',
                        click: () => this.lancerTestQI()
                    },
                    {
                        label: 'Formation Continue',
                        click: () => this.activerFormation()
                    }
                ]
            },
            {
                label: 'Système',
                submenu: [
                    {
                        label: 'Scanner Applications',
                        click: () => this.scannerApplications()
                    },
                    {
                        label: 'Mémoire Thermique',
                        click: () => this.afficherMemoireThermique()
                    },
                    { type: 'separator' },
                    {
                        label: 'Redémarrer Serveur',
                        click: () => this.redemarrerServeur()
                    }
                ]
            },
            {
                label: 'Aide',
                submenu: [
                    {
                        label: 'Guide Utilisateur',
                        click: () => this.ouvrirGuide()
                    },
                    {
                        label: 'Raccourcis Clavier',
                        click: () => this.afficherRaccourcis()
                    },
                    { type: 'separator' },
                    {
                        label: 'Signaler un Problème',
                        click: () => this.signalerProbleme()
                    }
                ]
            }
        ];

        const menu = Menu.buildFromTemplate(template);
        Menu.setApplicationMenu(menu);
    }

    creerTray() {
        this.tray = new Tray(this.obtenirIcone());
        
        const contextMenu = Menu.buildFromTemplate([
            {
                label: 'Ouvrir LOUNA-AI',
                click: () => this.afficherFenetre()
            },
            { type: 'separator' },
            {
                label: 'Statistiques Rapides',
                click: () => this.afficherStatistiquesRapides()
            },
            {
                label: 'Nouvelle Question',
                click: () => this.nouvelleQuestionRapide()
            },
            { type: 'separator' },
            {
                label: 'Quitter',
                click: () => this.quitterApp()
            }
        ]);

        this.tray.setContextMenu(contextMenu);
        this.tray.setToolTip('LOUNA-AI - Intelligence Artificielle');
        
        this.tray.on('click', () => {
            this.afficherFenetre();
        });
    }

    async demarrerServeurLouna() {
        try {
            console.log('🔄 Démarrage serveur LOUNA-AI...');
            
            // Créer une nouvelle instance du serveur
            this.serveurLouna = new ServeurInterfaceComplete();
            
            // Attendre que le serveur soit prêt
            await new Promise((resolve) => {
                setTimeout(resolve, 3000); // Laisser le temps au serveur de démarrer
            });
            
            console.log('✅ Serveur LOUNA-AI démarré sur http://localhost:3000');
            
        } catch (error) {
            console.error('❌ Erreur démarrage serveur:', error);
            this.afficherErreur('Erreur Serveur', 'Impossible de démarrer le serveur LOUNA-AI');
        }
    }

    configurerEvenements() {
        // Événements de l'application
        app.on('window-all-closed', () => {
            // Sur macOS, garder l'app active même si toutes les fenêtres sont fermées
            if (process.platform !== 'darwin') {
                this.quitterApp();
            }
        });

        app.on('activate', () => {
            // Sur macOS, recréer la fenêtre si l'app est activée et qu'il n'y a pas de fenêtre
            if (BrowserWindow.getAllWindows().length === 0) {
                this.creerFenetrePrincipale();
            } else {
                this.afficherFenetre();
            }
        });

        app.on('before-quit', () => {
            this.isQuitting = true;
        });

        // IPC pour communication avec l'interface
        ipcMain.handle('get-app-info', () => {
            return {
                version: app.getVersion(),
                name: app.getName(),
                serveur_url: 'http://localhost:3000'
            };
        });

        ipcMain.handle('open-external', (event, url) => {
            shell.openExternal(url);
        });
    }

    // Méthodes utilitaires
    obtenirIcone() {
        // Retourner le chemin vers l'icône (à créer)
        return path.join(__dirname, 'assets', 'louna-icon.png');
    }

    afficherFenetre() {
        if (this.mainWindow) {
            if (this.mainWindow.isMinimized()) {
                this.mainWindow.restore();
            }
            this.mainWindow.show();
            this.mainWindow.focus();
        }
    }

    afficherNotification(title, body) {
        if (this.tray) {
            this.tray.displayBalloon({
                title: title,
                content: body,
                icon: this.obtenirIcone()
            });
        }
    }

    afficherErreur(title, message) {
        dialog.showErrorBox(title, message);
    }

    quitterApp() {
        this.isQuitting = true;
        
        // Arrêter le serveur
        if (this.serveurProcess) {
            this.serveurProcess.kill();
        }
        
        app.quit();
    }

    // Actions du menu
    afficherAPropos() {
        dialog.showMessageBox(this.mainWindow, {
            type: 'info',
            title: 'À propos de LOUNA-AI',
            message: 'LOUNA-AI - Intelligence Artificielle Complète',
            detail: `Version: ${app.getVersion()}\nCréée par: Jean-Luc Passave\nLieu: Sainte-Anne, Guadeloupe\n\nIA complète avec mémoire thermique, raisonnement avancé et auto-évolution.`,
            buttons: ['OK']
        });
    }

    nouvelleConversation() {
        this.mainWindow.webContents.send('nouvelle-conversation');
    }

    afficherStatistiques() {
        this.mainWindow.webContents.send('afficher-statistiques');
    }

    lancerTestQI() {
        this.mainWindow.webContents.send('lancer-test-qi');
    }
}

// Créer l'application si ce fichier est exécuté directement
if (require.main === module) {
    new LounaAIApp();
}

module.exports = LounaAIApp;
