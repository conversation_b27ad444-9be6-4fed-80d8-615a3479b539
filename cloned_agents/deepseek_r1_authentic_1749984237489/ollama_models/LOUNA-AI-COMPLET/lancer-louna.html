<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Lanceur LOUNA-AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a0a1a 50%, #2a0a2a 100%);
            color: #ffffff;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        .launcher {
            text-align: center;
            padding: 50px;
            background: linear-gradient(135deg, #1a1a1a, #2a1a2a);
            border-radius: 20px;
            border: 2px solid #ff69b4;
            box-shadow: 0 0 50px rgba(255, 105, 180, 0.3);
            max-width: 600px;
            width: 90%;
        }

        .logo {
            font-size: 72px;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #ff69b4, #ff1493, #00ff88);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 30px rgba(255, 105, 180, 0.5);
            animation: glow 3s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { filter: brightness(1); }
            to { filter: brightness(1.3); }
        }

        .title {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #ff69b4;
        }

        .subtitle {
            font-size: 18px;
            color: #cccccc;
            margin-bottom: 40px;
        }

        .status {
            margin-bottom: 30px;
            padding: 15px;
            background: rgba(0, 255, 136, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(0, 255, 136, 0.3);
        }

        .status-text {
            font-size: 16px;
            color: #00ff88;
            font-weight: bold;
        }

        .buttons {
            display: flex;
            flex-direction: column;
            gap: 20px;
            align-items: center;
        }

        .launch-btn {
            padding: 20px 40px;
            font-size: 24px;
            font-weight: bold;
            background: linear-gradient(135deg, #ff69b4, #ff1493);
            border: none;
            border-radius: 15px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            min-width: 300px;
            box-shadow: 0 5px 20px rgba(255, 105, 180, 0.4);
        }

        .launch-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(255, 105, 180, 0.6);
            background: linear-gradient(135deg, #ff1493, #ff69b4);
        }

        .launch-btn:active {
            transform: translateY(-2px);
        }

        .secondary-btn {
            padding: 15px 30px;
            font-size: 16px;
            font-weight: bold;
            background: linear-gradient(135deg, #00ff88, #00cc66);
            border: none;
            border-radius: 10px;
            color: #000000;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            min-width: 250px;
            box-shadow: 0 3px 15px rgba(0, 255, 136, 0.4);
        }

        .secondary-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 255, 136, 0.6);
        }

        .info {
            margin-top: 30px;
            padding: 20px;
            background: rgba(255, 105, 180, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(255, 105, 180, 0.3);
        }

        .info-title {
            font-size: 18px;
            font-weight: bold;
            color: #ff69b4;
            margin-bottom: 15px;
        }

        .info-list {
            text-align: left;
            color: #cccccc;
            line-height: 1.6;
        }

        .info-list li {
            margin-bottom: 8px;
            list-style: none;
            position: relative;
            padding-left: 25px;
        }

        .info-list li:before {
            content: "✅";
            position: absolute;
            left: 0;
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .loading {
            display: none;
            margin-top: 20px;
        }

        .spinner {
            border: 4px solid rgba(255, 105, 180, 0.3);
            border-top: 4px solid #ff69b4;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="launcher">
        <div class="logo">🧠</div>
        <div class="title">LOUNA-AI</div>
        <div class="subtitle">Intelligence Artificielle Complète</div>
        
        <div class="status">
            <div class="status-text" id="status">🔄 Vérification du système...</div>
        </div>

        <div class="buttons">
            <a href="http://localhost:8080" class="launch-btn pulse" id="launch-main">
                🚀 LANCER LOUNA-AI
            </a>
            
            <a href="http://localhost:8080/cerveau" class="secondary-btn" id="launch-brain">
                🧠 Visualisation Cerveau 3D
            </a>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Démarrage en cours...</p>
        </div>

        <div class="info">
            <div class="info-title">🌟 Fonctionnalités Disponibles</div>
            <ul class="info-list">
                <li>Interface de chat complète avec micro et haut-parleur</li>
                <li>Mémoire thermique avec 6 zones de température</li>
                <li>8 accélérateurs KYBER pour performances optimales</li>
                <li>QI dynamique qui évolue avec les capacités</li>
                <li>Transfert de fichiers (WiFi, Bluetooth, AirDrop)</li>
                <li>Visualisation 3D du cerveau et des neurones</li>
                <li>42 formations expertes intégrées</li>
                <li>Capacités de codage avancées</li>
            </ul>
        </div>
    </div>

    <script>
        // Vérification du statut du serveur
        function checkServerStatus() {
            fetch('http://localhost:8080/stats')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('status').innerHTML = '✅ LOUNA-AI Opérationnelle - QI: ' + data.qi_actuel;
                    document.getElementById('status').style.color = '#00ff88';
                })
                .catch(error => {
                    document.getElementById('status').innerHTML = '❌ Serveur non démarré - Cliquez pour lancer';
                    document.getElementById('status').style.color = '#ff4444';
                });
        }

        // Lancement avec animation
        document.getElementById('launch-main').addEventListener('click', function(e) {
            document.getElementById('loading').style.display = 'block';
            setTimeout(() => {
                window.open('http://localhost:8080', '_blank');
                document.getElementById('loading').style.display = 'none';
            }, 1000);
        });

        document.getElementById('launch-brain').addEventListener('click', function(e) {
            document.getElementById('loading').style.display = 'block';
            setTimeout(() => {
                window.open('http://localhost:8080/cerveau', '_blank');
                document.getElementById('loading').style.display = 'none';
            }, 1000);
        });

        // Vérification initiale et périodique
        checkServerStatus();
        setInterval(checkServerStatus, 5000);
    </script>
</body>
</html>
