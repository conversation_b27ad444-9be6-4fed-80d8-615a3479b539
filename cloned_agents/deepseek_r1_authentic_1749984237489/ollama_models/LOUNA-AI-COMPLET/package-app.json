{"name": "louna-ai-app", "version": "2.0.0", "description": "LOUNA-AI - Intelligence Artificielle Complète avec Interface Desktop", "main": "LOUNA-AI-APP.js", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "homepage": "https://louna-ai.com", "repository": {"type": "git", "url": "https://github.com/louna-ai/louna-ai-complete"}, "keywords": ["intelligence-artificielle", "ia", "louna-ai", "electron", "desktop-app", "raisonnement", "memoire-thermique", "auto-evolution"], "scripts": {"start": "electron .", "dev": "NODE_ENV=development electron .", "build": "electron-builder", "build-mac": "electron-builder --mac", "build-win": "electron-builder --win", "build-linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never", "postinstall": "electron-builder install-app-deps", "test": "node test-app.js", "clean": "rm -rf dist/ build/", "setup": "npm install && npm run create-assets"}, "dependencies": {"electron": "^22.0.0", "axios": "^1.6.0", "ws": "^8.14.0", "express": "^4.18.0", "socket.io": "^4.7.0", "node-fetch": "^3.3.0", "cheerio": "^1.0.0-rc.12", "fs-extra": "^11.1.0", "path": "^0.12.7"}, "devDependencies": {"electron-builder": "^24.6.0", "electron-devtools-installer": "^3.2.0", "concurrently": "^8.2.0"}, "build": {"appId": "com.louna-ai.app", "productName": "LOUNA-AI", "directories": {"output": "dist", "buildResources": "build"}, "files": ["**/*", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}", "!VERSIONS-NON-VALIDEES/**/*", "!SAUVEGARDE_*/**/*", "!test/**/*", "!docs/**/*"], "mac": {"category": "public.app-category.productivity", "icon": "assets/icon.icns", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "darkModeSupport": true, "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist"}, "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}], "icon": "assets/icon.ico", "publisherName": "LOUNA-AI"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "Office"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "LOUNA-AI"}, "dmg": {"title": "LOUNA-AI ${version}", "icon": "assets/icon.icns", "background": "assets/dmg-background.png", "contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}]}, "publish": {"provider": "github", "owner": "louna-ai", "repo": "louna-ai-complete"}}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}