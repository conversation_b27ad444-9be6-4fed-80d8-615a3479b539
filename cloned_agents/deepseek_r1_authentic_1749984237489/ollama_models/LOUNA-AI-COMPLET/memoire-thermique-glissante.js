/**
 * MÉMOIRE THERMIQUE GLISSANTE POUR LOUNA-AI
 * Curseur mobile avec 7 zones et vraie température CPU
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

class MemoireThermiqueGlissante {
    constructor() {
        this.fichierMemoire = path.join(__dirname, 'memoire-thermique-glissante.json');
        this.memoires = new Map();
        this.curseurThermique = 50; // Position du curseur (20-70°C)
        this.temperatureCPU = 50;
        this.vitesseGlissement = 0.1; // Souplesse du glissement
        
        // 7 ZONES THERMIQUES AVEC SOMMEIL/CRÉATIVITÉ
        this.zones = {
            0: { min: 15, max: 25, nom: 'Hibernation profonde', couleur: '🧊', fonction: 'Stockage long terme' },
            1: { min: 25, max: 35, nom: 'Sommeil léger', couleur: '😴', fonction: 'Consolidation mémoire' },
            2: { min: 35, max: 45, nom: 'Éveil calme', couleur: '🌙', fonction: 'Réflexion lente' },
            3: { min: 45, max: 55, nom: 'Activité normale', couleur: '🌡️', fonction: 'Traitement standard' },
            4: { min: 55, max: 65, nom: 'Haute activité', couleur: '🔥', fonction: 'Accès rapide' },
            5: { min: 65, max: 75, nom: 'Zone brûlante', couleur: '🌋', fonction: 'Ultra-prioritaire' },
            6: { min: 75, max: 85, nom: 'Créativité intense', couleur: '⚡', fonction: 'Innovation et liens' }
        };
        
        this.chargerMemoire();
        this.demarrerCurseurGlissant();
    }

    // OBTENIR VRAIE TEMPÉRATURE CPU AVEC LISSAGE
    obtenirTemperatureCPUReelle() {
        try {
            // Méthode 1: Charge CPU réelle
            const cpus = os.cpus();
            let totalUsage = 0;
            
            cpus.forEach(cpu => {
                let total = 0;
                let idle = cpu.times.idle;
                
                for (let type in cpu.times) {
                    total += cpu.times[type];
                }
                
                const usage = 100 - ~~(100 * idle / total);
                totalUsage += usage;
            });
            
            const avgUsage = totalUsage / cpus.length;
            
            // Convertir en température (30-70°C base)
            let tempCalculee = 30 + (avgUsage * 0.6);
            
            // Méthode 2: Charge mémoire
            const memInfo = process.memoryUsage();
            const memUsage = (memInfo.heapUsed / memInfo.heapTotal) * 100;
            tempCalculee += memUsage * 0.1;
            
            // Méthode 3: Activité réseau (simulation)
            const networkActivity = Math.random() * 10; // 0-10
            tempCalculee += networkActivity * 0.5;
            
            // Lissage pour éviter les variations brutales
            const ancienneTemp = this.temperatureCPU;
            this.temperatureCPU = ancienneTemp + (tempCalculee - ancienneTemp) * this.vitesseGlissement;
            
            // Limiter entre 20-80°C
            this.temperatureCPU = Math.max(20, Math.min(80, this.temperatureCPU));
            
            return this.temperatureCPU;
            
        } catch (error) {
            // Fallback avec variation naturelle
            const variation = (Math.random() - 0.5) * 4; // ±2°C
            this.temperatureCPU += variation * this.vitesseGlissement;
            this.temperatureCPU = Math.max(20, Math.min(80, this.temperatureCPU));
            return this.temperatureCPU;
        }
    }

    // CURSEUR THERMIQUE GLISSANT
    calculerPositionCurseur() {
        const tempCPU = this.obtenirTemperatureCPUReelle();
        
        // Le curseur suit la température CPU avec inertie
        const ecart = tempCPU - this.curseurThermique;
        this.curseurThermique += ecart * this.vitesseGlissement * 2; // Plus réactif que la temp
        
        // Influence de l'activité mémoire
        const activiteMemoire = this.calculerActiviteMemoire();
        this.curseurThermique += activiteMemoire * 0.5;
        
        // Limiter le curseur
        this.curseurThermique = Math.max(20, Math.min(80, this.curseurThermique));
        
        return this.curseurThermique;
    }

    // CALCULER ACTIVITÉ MÉMOIRE
    calculerActiviteMemoire() {
        const maintenant = Date.now();
        let activiteRecente = 0;
        
        for (const memoire of this.memoires.values()) {
            const tempsDepuisAcces = (maintenant - memoire.dernierAcces) / (1000 * 60); // minutes
            if (tempsDepuisAcces < 5) { // Activité dans les 5 dernières minutes
                activiteRecente += (5 - tempsDepuisAcces) / 5; // Score 0-1
            }
        }
        
        return Math.min(10, activiteRecente); // Max +10°C
    }

    // DÉTERMINER ZONE AVEC CURSEUR GLISSANT
    determinerZoneGlissante(temperature) {
        // La zone dépend de la position relative au curseur
        const positionCurseur = this.calculerPositionCurseur();
        const ecartCurseur = temperature - positionCurseur;
        
        // Zones relatives au curseur
        if (ecartCurseur < -25) return 0; // Très en dessous
        if (ecartCurseur < -15) return 1; // En dessous
        if (ecartCurseur < -5) return 2;  // Légèrement en dessous
        if (ecartCurseur < 5) return 3;   // Autour du curseur
        if (ecartCurseur < 15) return 4;  // Légèrement au dessus
        if (ecartCurseur < 25) return 5;  // Au dessus
        return 6; // Très au dessus (créativité)
    }

    // CALCULER TEMPÉRATURE GLISSANTE
    calculerTemperatureGlissante(importance, recence, utilisation, pertinence = 0) {
        const positionCurseur = this.calculerPositionCurseur();
        
        // Base : position du curseur
        let temperature = positionCurseur;
        
        // Facteur importance : écart par rapport au curseur
        const ecartImportance = (importance - 0.5) * 30; // ±15°C
        temperature += ecartImportance;
        
        // Facteur récence : boost si récent
        temperature += recence * 15; // +0 à +15°C
        
        // Facteur utilisation : boost progressif
        const utilisationNorm = Math.min(1, utilisation / 10);
        temperature += utilisationNorm * 10; // +0 à +10°C
        
        // Facteur pertinence : boost temporaire
        temperature += pertinence * 8; // +0 à +8°C
        
        // Variation naturelle pour fluidité
        const variation = (Math.random() - 0.5) * 2; // ±1°C
        temperature += variation;
        
        // Limiter entre 15-85°C
        return Math.max(15, Math.min(85, temperature));
    }

    // GLISSEMENT FLUIDE DES MÉMOIRES
    appliquerGlissementFluide() {
        const positionCurseur = this.calculerPositionCurseur();
        let migrations = 0;
        
        console.log(`🌊 Glissement fluide (Curseur: ${positionCurseur.toFixed(1)}°C, CPU: ${this.temperatureCPU.toFixed(1)}°C)`);
        
        for (const [id, memoire] of this.memoires) {
            const ancienneTemp = memoire.temperature;
            const ancienneZone = memoire.zone;
            
            // Recalculer température avec glissement
            const recence = this.calculerRecence(memoire.timestamp);
            let nouvelleTemp = this.calculerTemperatureGlissante(
                memoire.importance,
                recence,
                memoire.utilisation
            );
            
            // Glissement fluide vers la nouvelle température
            const ecart = nouvelleTemp - ancienneTemp;
            memoire.temperature = ancienneTemp + (ecart * this.vitesseGlissement * 3);
            
            // Nouvelle zone avec curseur
            const nouvelleZone = this.determinerZoneGlissante(memoire.temperature);
            
            // Enregistrer migration si changement
            if (nouvelleZone !== ancienneZone) {
                migrations++;
                memoire.zone = nouvelleZone;
                
                // Enregistrer l'évolution
                if (!memoire.evolution) memoire.evolution = [];
                memoire.evolution.push({
                    timestamp: Date.now(),
                    temperature: memoire.temperature,
                    zone: nouvelleZone,
                    curseur: positionCurseur,
                    action: 'glissement_fluide'
                });
                
                console.log(`🌊 Migration fluide: ${id.substr(-9)} zone ${ancienneZone} → ${nouvelleZone} (${memoire.temperature.toFixed(1)}°C) ${this.zones[nouvelleZone].couleur}`);
            }
        }
        
        if (migrations > 0) {
            console.log(`🌊 ${migrations} migrations fluides effectuées`);
            this.sauvegarderMemoire();
        }
    }

    // STOCKER AVEC GLISSEMENT
    stocker(contenu, source, importance = 0.5) {
        const id = `mem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const timestamp = Date.now();
        const recence = 1.0;
        const utilisation = 1;
        
        const temperature = this.calculerTemperatureGlissante(importance, recence, utilisation);
        const zone = this.determinerZoneGlissante(temperature);
        
        const memoire = {
            id: id,
            contenu: contenu,
            source: source,
            timestamp: timestamp,
            importance: importance,
            utilisation: utilisation,
            temperature: temperature,
            zone: zone,
            dernierAcces: timestamp,
            liens: [],
            evolution: [{
                timestamp,
                temperature,
                zone,
                curseur: this.curseurThermique,
                action: 'creation'
            }]
        };
        
        this.memoires.set(id, memoire);
        console.log(`🧠 Mémoire stockée: ${id.substr(-9)} (${temperature.toFixed(1)}°C, zone ${zone} ${this.zones[zone].couleur})`);
        
        this.sauvegarderMemoire();
        return id;
    }

    // RECHERCHE AVEC GLISSEMENT
    rechercher(requete, limite = 5) {
        const resultats = [];
        const requeteLower = requete.toLowerCase();
        
        for (const [id, memoire] of this.memoires) {
            let pertinence = 0;
            
            // Calcul pertinence
            const contenuLower = memoire.contenu.toLowerCase();
            if (contenuLower.includes(requeteLower)) {
                pertinence += 1.0;
            }
            
            
            // Correspondance sémantique améliorée
            const correspondancesDomaines = {
                "calcul": ["mathématiques", "équation", "formule", "nombre", "intégrale"],
                "médecine": ["diagnostic", "symptôme", "traitement", "maladie", "patient"],
                "physique": ["énergie", "force", "vitesse", "température", "cinétique"],
                "chimie": ["réaction", "équilibre", "molécule", "atome", "équilibrage"],
                "photosynthèse": ["sciences naturelles", "biologie", "plantes", "CO2", "H2O"]
            };
            
            for (const [motCle, domaines] of Object.entries(correspondancesDomaines)) {
                if (requeteLower.includes(motCle)) {
                    for (const domaine of domaines) {
                        if (contenuLower.includes(domaine)) {
                            pertinence += 0.8; // Bonus sémantique
                        }
                    }
                }
            }
                        const mots = requeteLower.split(' ');
            for (const mot of mots) {
                if (mot.length > 2 && contenuLower.includes(mot)) {
                    pertinence += 0.3;
                }
            }
            
            
            // Bonus pour mémoires importantes
            if (memoire.utilisation > 5) {
                pertinence += 0.2;
            }
            if (memoire.temperature > 70) {
                pertinence += 0.3;
            }
                        if (pertinence > 0.01) { // Seuil très bas pour être plus permissif
                // Accès déclenche glissement
                memoire.utilisation++;
                memoire.dernierAcces = Date.now();
                
                // Recalcul avec glissement
                const nouvelleRecence = this.calculerRecence(memoire.timestamp);
                const nouvelleTemp = this.calculerTemperatureGlissante(
                    memoire.importance,
                    nouvelleRecence,
                    memoire.utilisation,
                    pertinence
                );
                
                // Glissement fluide
                const ecart = nouvelleTemp - memoire.temperature;
                memoire.temperature += ecart * this.vitesseGlissement * 5; // Plus rapide pour accès
                
                const ancienneZone = memoire.zone;
                memoire.zone = this.determinerZoneGlissante(memoire.temperature);
                
                // Enregistrer évolution
                memoire.evolution.push({
                    timestamp: Date.now(),
                    temperature: memoire.temperature,
                    zone: memoire.zone,
                    curseur: this.curseurThermique,
                    action: 'acces_recherche',
                    pertinence: pertinence
                });
                
                if (memoire.zone !== ancienneZone) {
                    console.log(`🔍 Migration recherche: ${id.substr(-9)} zone ${ancienneZone} → ${memoire.zone} (${memoire.temperature.toFixed(1)}°C) ${this.zones[memoire.zone].couleur}`);
                }
                
                resultats.push({
                    id: id,
                    contenu: memoire.contenu,
                    source: memoire.source,
                    pertinence: pertinence,
                    temperature: memoire.temperature,
                    zone: memoire.zone,
                    utilisation: memoire.utilisation
                });
            }
        }
        
        // Tri par pertinence ET proximité du curseur
        resultats.sort((a, b) => {
            const proximiteA = 1 / (1 + Math.abs(a.temperature - this.curseurThermique));
            const proximiteB = 1 / (1 + Math.abs(b.temperature - this.curseurThermique));
            const scoreA = a.pertinence + proximiteA;
            const scoreB = b.pertinence + proximiteB;
            return scoreB - scoreA;
        });
        
        this.sauvegarderMemoire();
        return resultats.slice(0, limite);
    }

    // DÉMARRER CURSEUR GLISSANT
    demarrerCurseurGlissant() {
        // Glissement rapide toutes les 5 secondes
        setInterval(() => {
            this.appliquerGlissementFluide();
        }, 5000);
        
        // Évolution lente toutes les 30 secondes
        setInterval(() => {
            this.evolutionLente();
        }, 30000);
    }

    // ÉVOLUTION LENTE (SOMMEIL/CRÉATIVITÉ)
    evolutionLente() {
        console.log(`😴 Évolution lente - Phase de sommeil/créativité`);
        
        for (const [id, memoire] of this.memoires) {
            const maintenant = Date.now();
            const tempsDepuisAcces = (maintenant - memoire.dernierAcces) / (1000 * 60); // minutes
            
            // Refroidissement naturel très lent
            if (tempsDepuisAcces > 30) { // Plus de 30 minutes
                memoire.temperature *= 0.995; // Refroidissement très lent
                
                // Recalculer zone
                const nouvelleZone = this.determinerZoneGlissante(memoire.temperature);
                if (nouvelleZone !== memoire.zone) {
                    console.log(`😴 Migration sommeil: ${id.substr(-9)} zone ${memoire.zone} → ${nouvelleZone} (${memoire.temperature.toFixed(1)}°C)`);
                    memoire.zone = nouvelleZone;
                    
                    memoire.evolution.push({
                        timestamp: maintenant,
                        temperature: memoire.temperature,
                        zone: nouvelleZone,
                        curseur: this.curseurThermique,
                        action: 'evolution_sommeil'
                    });
                }
            }
            
            // Boost créativité pour mémoires très utilisées
            if (memoire.utilisation > 10 && Math.random() < 0.1) {
                memoire.temperature += 5; // Boost créatif
                memoire.zone = this.determinerZoneGlissante(memoire.temperature);
                console.log(`⚡ Boost créatif: ${id.substr(-9)} → ${memoire.temperature.toFixed(1)}°C zone ${memoire.zone}`);
            }
        }
        
        this.sauvegarderMemoire();
    }

    // CALCULER RÉCENCE
    calculerRecence(timestamp) {
        const maintenant = Date.now();
        const ageHeures = (maintenant - timestamp) / (1000 * 60 * 60);
        return Math.exp(-ageHeures / 48); // Décroissance sur 48h
    }

    // CHARGER MÉMOIRE
    chargerMemoire() {
        try {
            if (fs.existsSync(this.fichierMemoire)) {
                const data = JSON.parse(fs.readFileSync(this.fichierMemoire, 'utf8'));
                this.memoires = new Map(data.memoires || []);
                this.curseurThermique = data.curseurThermique || 50;
                this.temperatureCPU = data.temperatureCPU || 50;
                console.log(`📥 Mémoire glissante chargée: ${this.memoires.size} entrées`);
            }
        } catch (error) {
            console.error(`❌ Erreur chargement mémoire glissante:`, error.message);
            this.memoires = new Map();
        }
    }

    // SAUVEGARDER MÉMOIRE
    sauvegarderMemoire() {
        try {
            const data = {
                memoires: Array.from(this.memoires.entries()),
                curseurThermique: this.curseurThermique,
                temperatureCPU: this.temperatureCPU,
                vitesseGlissement: this.vitesseGlissement,
                timestamp: Date.now()
            };
            fs.writeFileSync(this.fichierMemoire, JSON.stringify(data, null, 2));
            return true;
        } catch (error) {
            console.error(`❌ Erreur sauvegarde mémoire glissante:`, error.message);
            return false;
        }
    }

    // STATISTIQUES GLISSANTES
    getStatistiquesGlissantes() {
        const stats = {
            totalEntries: this.memoires.size,
            temperatureCPU: this.temperatureCPU.toFixed(1),
            curseurThermique: this.curseurThermique.toFixed(1),
            vitesseGlissement: this.vitesseGlissement,
            zonesDistribution: [0, 0, 0, 0, 0, 0, 0],
            zonesDetails: {},
            activiteMemoire: this.calculerActiviteMemoire().toFixed(1)
        };
        
        let totalTemp = 0;
        
        // Distribution et détails par zones
        for (const memoire of this.memoires.values()) {
            stats.zonesDistribution[memoire.zone]++;
            totalTemp += memoire.temperature;
            
            if (!stats.zonesDetails[memoire.zone]) {
                stats.zonesDetails[memoire.zone] = {
                    nom: this.zones[memoire.zone].nom,
                    couleur: this.zones[memoire.zone].couleur,
                    fonction: this.zones[memoire.zone].fonction,
                    count: 0,
                    tempMoyenne: 0
                };
            }
            stats.zonesDetails[memoire.zone].count++;
            stats.zonesDetails[memoire.zone].tempMoyenne += memoire.temperature;
        }
        
        // Calculer moyennes
        for (const zone in stats.zonesDetails) {
            stats.zonesDetails[zone].tempMoyenne = 
                (stats.zonesDetails[zone].tempMoyenne / stats.zonesDetails[zone].count).toFixed(1);
        }
        
        stats.averageTemperature = this.memoires.size > 0 ? (totalTemp / this.memoires.size).toFixed(1) : 0;
        
        return stats;
    }

    // MAINTENANCE
    maintenance() {
        console.log(`🔧 Maintenance mémoire glissante...`);
        this.appliquerGlissementFluide();
        this.evolutionLente();
    }
}

module.exports = { MemoireThermiqueGlissante };
