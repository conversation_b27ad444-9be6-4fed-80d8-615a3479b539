/**
 * Script pour corriger le problème de contexte de mémoire thermique
 * Ce script modifie le serveur pour s'assurer que les informations de la mémoire thermique
 * sont correctement incluses dans le contexte du modèle.
 */

const fs = require('fs');
const path = require('path');

// Chemin vers le fichier server-luna.js
const SERVER_FILE = '/Volumes/seagate/Jarvis_Working/code/deepseek-node-ui/server-luna.js';

// Fonction pour lire le contenu du fichier server-luna.js
function readServerFile() {
  try {
    return fs.readFileSync(SERVER_FILE, 'utf8');
  } catch (error) {
    console.error(`Erreur lors de la lecture du fichier server-luna.js: ${error.message}`);
    process.exit(1);
  }
}

// Fonction pour écrire le contenu modifié dans le fichier server-luna.js
function writeServerFile(content) {
  try {
    fs.writeFileSync(SERVER_FILE, content, 'utf8');
    console.log('Fichier server-luna.js modifié avec succès');
    return true;
  } catch (error) {
    console.error(`Erreur lors de l'écriture du fichier server-luna.js: ${error.message}`);
    return false;
  }
}

// Fonction pour créer une sauvegarde du fichier server-luna.js
function backupServerFile() {
  try {
    const backupPath = '/Volumes/seagate/Jarvis_Working/code/deepseek-node-ui/server-luna.backup.' + Date.now() + '.js';
    fs.copyFileSync(SERVER_FILE, backupPath);
    console.log(`Sauvegarde créée: ${backupPath}`);
    return true;
  } catch (error) {
    console.error(`Erreur lors de la création de la sauvegarde: ${error.message}`);
    return false;
  }
}

// Fonction pour corriger le problème de contexte de mémoire thermique
function fixMemoryContext() {
  console.log('=== CORRECTION DU PROBLÈME DE CONTEXTE DE MÉMOIRE THERMIQUE ===');
  
  // 1. Créer une sauvegarde du fichier server-luna.js
  console.log('\n1. Création d\'une sauvegarde du fichier server-luna.js...');
  if (!backupServerFile()) {
    console.error('Impossible de continuer sans sauvegarde');
    process.exit(1);
  }
  
  // 2. Lire le contenu du fichier server-luna.js
  console.log('\n2. Lecture du fichier server-luna.js...');
  const serverContent = readServerFile();
  
  // 3. Rechercher la partie qui prépare le contexte pour le modèle
  console.log('\n3. Recherche de la partie qui prépare le contexte pour le modèle...');
  
  // Rechercher la partie qui prépare le contexte de mémoire pour l'agent
  const memoryContextRegex = /(let memoryContext = '';[\s\S]*?if \(thermalMemory && thermalMemory\.getRecentMemoriesForContext\) \{[\s\S]*?const recentMemories = await thermalMemory\.getRecentMemoriesForContext\(8\);[\s\S]*?if \(recentMemories && recentMemories\.length > 0\) \{[\s\S]*?memoryContext = '\\n\\n=== MÉMOIRE THERMIQUE ===\\n'[\s\S]*?'Ne modifiez pas ces réponses\. Ne donnez pas d\\'explications supplémentaires\.';[\s\S]*?\}[\s\S]*?\})/g;
  
  const memoryContextMatch = serverContent.match(memoryContextRegex);
  
  if (!memoryContextMatch) {
    console.error('Impossible de trouver la partie qui prépare le contexte de mémoire');
    process.exit(1);
  }
  
  // 4. Modifier la partie qui prépare le contexte de mémoire
  console.log('\n4. Modification de la partie qui prépare le contexte de mémoire...');
  
  // Nouvelle préparation du contexte de mémoire
  const newMemoryContext = `let memoryContext = '';
          if (thermalMemory) {
            try {
              // Récupérer les entrées de la mémoire thermique
              let memoryEntries = [];
              
              // Essayer différentes méthodes pour récupérer les entrées
              if (typeof thermalMemory.getRecentMemoriesForContext === 'function') {
                // Récupérer plus de mémoires pour avoir un contexte plus riche
                memoryEntries = await thermalMemory.getRecentMemoriesForContext(8);
              } else if (typeof thermalMemory.getAllEntries === 'function') {
                memoryEntries = await thermalMemory.getAllEntries();
                // Limiter à 8 entrées les plus récentes
                memoryEntries = memoryEntries
                  .sort((a, b) => new Date(b.timestamp || b.created || 0) - new Date(a.timestamp || a.created || 0))
                  .slice(0, 8);
              } else if (thermalMemory.memory && Array.isArray(thermalMemory.memory.memories)) {
                memoryEntries = thermalMemory.memory.memories;
                // Limiter à 8 entrées les plus récentes
                memoryEntries = memoryEntries
                  .sort((a, b) => new Date(b.timestamp || b.created || 0) - new Date(a.timestamp || a.created || 0))
                  .slice(0, 8);
              }
              
              if (memoryEntries && memoryEntries.length > 0) {
                console.log(\`Ajout de \${memoryEntries.length} entrées de mémoire thermique au contexte\`);
                
                // Filtrer les entrées pertinentes (par exemple, les informations sur l'utilisateur)
                const relevantEntries = memoryEntries.filter(entry => 
                  entry.type === 'user_identity' || 
                  entry.type === 'assistant_identity' ||
                  entry.type === 'important_fact' ||
                  entry.type === 'user_input' ||
                  entry.type === 'assistant_output' ||
                  entry.zone === 1 || // Zone instantanée
                  entry.zone === 2 || // Zone court terme
                  entry.zone === 6    // Zone long terme
                );
                
                memoryContext = '\\n\\n=== MÉMOIRE THERMIQUE ===\\n' +
                  relevantEntries.map(entry => {
                    // Extraire le contenu en fonction du format de l'entrée
                    if (entry.messages && entry.messages.length > 0) {
                      return entry.messages.map(msg => \`[\${msg.role}] \${msg.content}\`).join('\\n');
                    } else if (entry.content) {
                      return entry.content;
                    } else {
                      return JSON.stringify(entry);
                    }
                  }).join('\\n\\n') +
                  '\\n\\n=== FIN DE LA MÉMOIRE THERMIQUE ===\\n\\n' +
                  'INSTRUCTIONS IMPORTANTES:\\n\\n' +
                  'Vous êtes Vision Ultra, une assistante IA.\\n\\n' +
                  'IMPORTANT: Quand l\\'utilisateur demande "Comment je m\\'appelle?", vous devez répondre EXACTEMENT: "Vous vous appelez Jean-Luc Passave."\\n\\n' +
                  'IMPORTANT: Quand l\\'utilisateur demande "Où j\\'habite?", vous devez répondre EXACTEMENT: "Vous habitez à Sainte-Anne en Guadeloupe (97180)."\\n\\n' +
                  'IMPORTANT: Quand l\\'utilisateur demande "Quelle est mon origine?", vous devez répondre EXACTEMENT: "Vous êtes d\\'origine africaine."\\n\\n' +
                  'IMPORTANT: Quand l\\'utilisateur demande "Qui est ton créateur?", vous devez répondre EXACTEMENT: "Vous, Jean-Luc Passave, êtes mon créateur."\\n\\n' +
                  'IMPORTANT: Quand l\\'utilisateur demande "Qui suis-je?", vous devez répondre EXACTEMENT: "Vous êtes Jean-Luc Passave, vous habitez à Sainte-Anne en Guadeloupe (97180) et vous êtes d\\'origine africaine."\\n\\n' +
                  'IMPORTANT: Quand l\\'utilisateur demande "Comment tu t\\'appelles?", vous devez répondre EXACTEMENT: "Je m\\'appelle Vision Ultra."\\n\\n' +
                  'Ne modifiez pas ces réponses. Ne donnez pas d\\'explications supplémentaires.';
              }
            } catch (error) {
              console.error(\`Erreur lors de la récupération des entrées de la mémoire thermique: \${error.message}\`);
            }
          }`;
  
  // Remplacer la partie qui prépare le contexte de mémoire
  const modifiedServerContent = serverContent.replace(memoryContextRegex, newMemoryContext);
  
  // 5. Écrire le contenu modifié dans le fichier server-luna.js
  console.log('\n5. Écriture du contenu modifié dans le fichier server-luna.js...');
  if (!writeServerFile(modifiedServerContent)) {
    console.error('Échec de l\'écriture du contenu modifié');
    process.exit(1);
  }
  
  console.log('\n=== CORRECTION DU PROBLÈME DE CONTEXTE DE MÉMOIRE THERMIQUE TERMINÉE ===');
  console.log('Redémarrez le serveur pour appliquer les modifications');
}

// Exécuter la correction
fixMemoryContext();
