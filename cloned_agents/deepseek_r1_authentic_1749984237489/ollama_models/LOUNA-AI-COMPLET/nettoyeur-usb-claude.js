/**
 * NETTOYEUR USB POUR MODÈLE CLAUDE
 * Libère espace et prépare installation Claude 4.5GB
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class NettoyeurUSBClaude {
    constructor() {
        console.log('🧹 NETTOYEUR USB POUR MODÈLE CLAUDE');
        console.log('===================================');
        
        this.config = {
            usb_path: '/Volumes/LounaAI_V3',
            espace_requis: 4.5 * 1024 * 1024 * 1024, // 4.5 GB en bytes
            sauvegarde: '/Volumes/LounaAI_V3/SAUVEGARDE-AVANT-CLAUDE'
        };
        
        this.analyse = {
            espace_total: 0,
            espace_libre: 0,
            espace_utilise: 0,
            fichiers_volumineux: [],
            dossiers_inutiles: [],
            espace_liberable: 0
        };
        
        this.elements_a_supprimer = [
            // Modèles anciens/inutiles
            'ollama/models-reels/llama3.2:1b',
            'ollama/models-reels/registry.ollama.ai',
            'ollama/models-reels/manifests',
            'ollama/models-reels/blobs',
            
            // Fichiers temporaires
            '.DS_Store',
            '.Spotlight-V100',
            '.Trashes',
            '.fseventsd',
            
            // Logs et caches
            'logs/',
            'cache/',
            'temp/',
            'tmp/',
            
            // Fichiers de développement
            'node_modules/',
            '.git/',
            '.vscode/',
            
            // Sauvegardes anciennes
            'backup/',
            'old/',
            'archive/'
        ];
        
        this.analyserEspaceUSB();
    }
    
    analyserEspaceUSB() {
        console.log('🔍 Analyse espace USB...');
        
        try {
            // Vérifier que l'USB est accessible
            if (!fs.existsSync(this.config.usb_path)) {
                throw new Error('USB LounaAI_V3 non trouvée');
            }
            
            // Analyser espace disque
            this.analyserEspaceDisque();
            
            // Scanner fichiers volumineux
            this.scannerFichiersVolumineux();
            
            // Identifier éléments supprimables
            this.identifierElementsSupprimer();
            
            // Calculer espace libérable
            this.calculerEspaceLiberable();
            
            // Afficher analyse
            this.afficherAnalyse();
            
            // Proposer nettoyage
            this.proposerNettoyage();
            
        } catch (error) {
            console.log(`❌ Erreur analyse: ${error.message}`);
        }
    }
    
    analyserEspaceDisque() {
        console.log('💾 Analyse espace disque...');
        
        try {
            // Utiliser df pour obtenir l'espace disque
            const dfOutput = execSync(`df -k "${this.config.usb_path}"`, { encoding: 'utf8' });
            const lines = dfOutput.trim().split('\n');
            
            if (lines.length >= 2) {
                const stats = lines[1].split(/\s+/);
                this.analyse.espace_total = parseInt(stats[1]) * 1024; // Convertir KB en bytes
                this.analyse.espace_utilise = parseInt(stats[2]) * 1024;
                this.analyse.espace_libre = parseInt(stats[3]) * 1024;
            }
            
            console.log(`📊 Espace total: ${this.formatTaille(this.analyse.espace_total)}`);
            console.log(`📊 Espace utilisé: ${this.formatTaille(this.analyse.espace_utilise)}`);
            console.log(`📊 Espace libre: ${this.formatTaille(this.analyse.espace_libre)}`);
            
            // Vérifier si assez d'espace pour Claude
            const espace_necessaire = this.config.espace_requis - this.analyse.espace_libre;
            if (espace_necessaire > 0) {
                console.log(`⚠️ Espace insuffisant: ${this.formatTaille(espace_necessaire)} manquants`);
            } else {
                console.log(`✅ Espace suffisant pour Claude 4.5GB`);
            }
            
        } catch (error) {
            console.log(`⚠️ Erreur analyse disque: ${error.message}`);
        }
    }
    
    scannerFichiersVolumineux() {
        console.log('🔍 Scan fichiers volumineux...');
        
        try {
            this.scannerDossierRecursif(this.config.usb_path, '');
            
            // Trier par taille décroissante
            this.analyse.fichiers_volumineux.sort((a, b) => b.taille - a.taille);
            
            console.log(`📁 ${this.analyse.fichiers_volumineux.length} fichiers volumineux trouvés`);
            
        } catch (error) {
            console.log(`⚠️ Erreur scan: ${error.message}`);
        }
    }
    
    scannerDossierRecursif(cheminComplet, cheminRelatif) {
        try {
            const elements = fs.readdirSync(cheminComplet);
            
            elements.forEach(element => {
                // Ignorer fichiers système
                if (element.startsWith('.') && element !== '.DS_Store') return;
                
                const cheminElementComplet = path.join(cheminComplet, element);
                const cheminElementRelatif = path.join(cheminRelatif, element);
                
                try {
                    const stats = fs.statSync(cheminElementComplet);
                    
                    if (stats.isFile()) {
                        // Fichier volumineux (> 10 MB)
                        if (stats.size > 10 * 1024 * 1024) {
                            this.analyse.fichiers_volumineux.push({
                                nom: element,
                                chemin: cheminElementRelatif,
                                taille: stats.size,
                                type: path.extname(element),
                                modifie: stats.mtime
                            });
                        }
                    } else if (stats.isDirectory()) {
                        // Scanner récursivement (limiter profondeur)
                        if (cheminRelatif.split(path.sep).length < 4) {
                            this.scannerDossierRecursif(cheminElementComplet, cheminElementRelatif);
                        }
                    }
                    
                } catch (error) {
                    // Ignorer erreurs d'accès
                }
            });
            
        } catch (error) {
            // Ignorer erreurs de lecture dossier
        }
    }
    
    identifierElementsSupprimer() {
        console.log('🎯 Identification éléments à supprimer...');
        
        this.elements_a_supprimer.forEach(element => {
            const cheminComplet = path.join(this.config.usb_path, element);
            
            if (fs.existsSync(cheminComplet)) {
                try {
                    const stats = fs.statSync(cheminComplet);
                    let taille = 0;
                    
                    if (stats.isFile()) {
                        taille = stats.size;
                    } else if (stats.isDirectory()) {
                        taille = this.calculerTailleDossier(cheminComplet);
                    }
                    
                    this.analyse.dossiers_inutiles.push({
                        nom: element,
                        chemin: cheminComplet,
                        taille: taille,
                        type: stats.isDirectory() ? 'dossier' : 'fichier'
                    });
                    
                } catch (error) {
                    // Ignorer erreurs
                }
            }
        });
        
        console.log(`🗑️ ${this.analyse.dossiers_inutiles.length} éléments identifiés pour suppression`);
    }
    
    calculerTailleDossier(cheminDossier) {
        let taille = 0;
        
        try {
            const elements = fs.readdirSync(cheminDossier);
            
            elements.forEach(element => {
                const cheminElement = path.join(cheminDossier, element);
                
                try {
                    const stats = fs.statSync(cheminElement);
                    
                    if (stats.isFile()) {
                        taille += stats.size;
                    } else if (stats.isDirectory()) {
                        taille += this.calculerTailleDossier(cheminElement);
                    }
                } catch (error) {
                    // Ignorer erreurs
                }
            });
            
        } catch (error) {
            // Ignorer erreurs
        }
        
        return taille;
    }
    
    calculerEspaceLiberable() {
        this.analyse.espace_liberable = this.analyse.dossiers_inutiles.reduce((total, element) => {
            return total + element.taille;
        }, 0);
        
        console.log(`💾 Espace libérable: ${this.formatTaille(this.analyse.espace_liberable)}`);
    }
    
    afficherAnalyse() {
        console.log('\n📊 ANALYSE COMPLÈTE USB');
        console.log('=======================');
        
        console.log('\n💾 ESPACE DISQUE:');
        console.log(`├── Total: ${this.formatTaille(this.analyse.espace_total)}`);
        console.log(`├── Utilisé: ${this.formatTaille(this.analyse.espace_utilise)}`);
        console.log(`├── Libre: ${this.formatTaille(this.analyse.espace_libre)}`);
        console.log(`└── Libérable: ${this.formatTaille(this.analyse.espace_liberable)}`);
        
        console.log('\n📁 FICHIERS VOLUMINEUX (Top 10):');
        this.analyse.fichiers_volumineux.slice(0, 10).forEach((fichier, index) => {
            console.log(`${index + 1}. ${fichier.nom}: ${this.formatTaille(fichier.taille)}`);
        });
        
        console.log('\n🗑️ ÉLÉMENTS À SUPPRIMER:');
        this.analyse.dossiers_inutiles.forEach(element => {
            console.log(`├── ${element.nom}: ${this.formatTaille(element.taille)} (${element.type})`);
        });
        
        // Vérifier si assez d'espace après nettoyage
        const espace_apres_nettoyage = this.analyse.espace_libre + this.analyse.espace_liberable;
        const suffisant = espace_apres_nettoyage >= this.config.espace_requis;
        
        console.log('\n🎯 POUR CLAUDE 4.5GB:');
        console.log(`├── Requis: ${this.formatTaille(this.config.espace_requis)}`);
        console.log(`├── Disponible après nettoyage: ${this.formatTaille(espace_apres_nettoyage)}`);
        console.log(`└── ${suffisant ? '✅ SUFFISANT' : '❌ INSUFFISANT'}`);
    }
    
    proposerNettoyage() {
        console.log('\n🧹 PROPOSITION NETTOYAGE');
        console.log('========================');
        
        const espace_apres = this.analyse.espace_libre + this.analyse.espace_liberable;
        
        if (espace_apres >= this.config.espace_requis) {
            console.log('✅ Le nettoyage libérera assez d\'espace pour Claude');
            console.log('🚀 Prêt à exécuter le nettoyage automatique');
            
            // Créer script de nettoyage
            this.creerScriptNettoyage();
            
        } else {
            console.log('⚠️ Nettoyage insuffisant pour Claude 4.5GB');
            console.log('💡 Solutions supplémentaires nécessaires');
            
            this.proposerSolutionsSupplementaires();
        }
    }
    
    creerScriptNettoyage() {
        console.log('📜 Création script nettoyage...');
        
        let script = '#!/bin/bash\n';
        script += '# SCRIPT NETTOYAGE USB POUR CLAUDE\n\n';
        script += 'echo "🧹 NETTOYAGE USB POUR MODÈLE CLAUDE"\n';
        script += 'echo "==================================="\n\n';
        
        // Créer sauvegarde
        script += 'echo "💾 Création sauvegarde..."\n';
        script += `mkdir -p "${this.config.sauvegarde}"\n\n`;
        
        // Supprimer éléments identifiés
        this.analyse.dossiers_inutiles.forEach(element => {
            script += `echo "🗑️ Suppression: ${element.nom}"\n`;
            script += `rm -rf "${element.chemin}"\n\n`;
        });
        
        script += 'echo "✅ Nettoyage terminé"\n';
        script += 'echo "📊 Espace libéré: ' + this.formatTaille(this.analyse.espace_liberable) + '"\n';
        script += 'df -h /Volumes/LounaAI_V3\n';
        
        const cheminScript = path.join(this.config.usb_path, 'nettoyage-claude.sh');
        fs.writeFileSync(cheminScript, script);
        
        // Rendre exécutable
        try {
            execSync(`chmod +x "${cheminScript}"`);
        } catch (error) {
            // Ignorer erreur chmod
        }
        
        console.log(`📜 Script créé: ${cheminScript}`);
        console.log('🚀 Exécuter: bash nettoyage-claude.sh');
    }
    
    proposerSolutionsSupplementaires() {
        console.log('\n💡 SOLUTIONS SUPPLÉMENTAIRES');
        console.log('============================');
        
        console.log('🎯 Options pour libérer plus d\'espace:');
        console.log('├── 1. Supprimer anciens modèles Ollama');
        console.log('├── 2. Compresser mémoire thermique');
        console.log('├── 3. Archiver logs et historiques');
        console.log('├── 4. Utiliser USB plus grande');
        console.log('└── 5. Installer Claude sur disque local');
        
        // Analyser modèles Ollama spécifiquement
        this.analyserModelesOllama();
    }
    
    analyserModelesOllama() {
        console.log('\n🤖 ANALYSE MODÈLES OLLAMA');
        console.log('=========================');
        
        const cheminModeles = path.join(this.config.usb_path, 'AGENTS-REELS/ollama/models-reels');
        
        if (fs.existsSync(cheminModeles)) {
            try {
                const taille = this.calculerTailleDossier(cheminModeles);
                console.log(`📊 Taille modèles Ollama: ${this.formatTaille(taille)}`);
                
                if (taille > 1024 * 1024 * 1024) { // > 1GB
                    console.log('💡 Modèles Ollama volumineux détectés');
                    console.log('🎯 Recommandation: Remplacer par Claude plus efficace');
                }
                
            } catch (error) {
                console.log(`⚠️ Erreur analyse modèles: ${error.message}`);
            }
        }
    }
    
    formatTaille(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    executerNettoyage() {
        console.log('\n🚀 EXÉCUTION NETTOYAGE');
        console.log('======================');
        
        let espaceLibere = 0;
        
        // Créer sauvegarde
        if (!fs.existsSync(this.config.sauvegarde)) {
            fs.mkdirSync(this.config.sauvegarde, { recursive: true });
            console.log('💾 Dossier sauvegarde créé');
        }
        
        // Supprimer éléments identifiés
        this.analyse.dossiers_inutiles.forEach(element => {
            try {
                console.log(`🗑️ Suppression: ${element.nom}`);
                
                if (fs.existsSync(element.chemin)) {
                    // Sauvegarder si important
                    if (element.nom.includes('model') || element.nom.includes('agent')) {
                        const cheminSauvegarde = path.join(this.config.sauvegarde, element.nom);
                        execSync(`cp -r "${element.chemin}" "${cheminSauvegarde}"`);
                        console.log(`💾 Sauvegardé: ${element.nom}`);
                    }
                    
                    // Supprimer
                    execSync(`rm -rf "${element.chemin}"`);
                    espaceLibere += element.taille;
                    console.log(`✅ Supprimé: ${this.formatTaille(element.taille)}`);
                }
                
            } catch (error) {
                console.log(`❌ Erreur suppression ${element.nom}: ${error.message}`);
            }
        });
        
        console.log(`\n✅ Nettoyage terminé`);
        console.log(`💾 Espace libéré: ${this.formatTaille(espaceLibere)}`);
        
        // Vérifier espace final
        this.analyserEspaceDisque();
    }
}

// Export
module.exports = NettoyeurUSBClaude;

// Test automatique si exécuté directement
if (require.main === module) {
    console.log('🚀 LANCEMENT NETTOYEUR USB CLAUDE');
    console.log('=================================');
    
    const nettoyeur = new NettoyeurUSBClaude();
    
    setTimeout(() => {
        console.log('\n🎯 Nettoyeur prêt à libérer espace pour Claude 4.5GB');
        console.log('💡 Exécuter: nettoyeur.executerNettoyage() pour nettoyer');
    }, 2000);
}
