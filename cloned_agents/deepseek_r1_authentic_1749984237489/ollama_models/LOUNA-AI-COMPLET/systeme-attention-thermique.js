#!/usr/bin/env node

/**
 * 🎯 SYSTÈME ATTENTION THERMIQUE COMPLET
 * 
 * PREMIÈRE IMPLÉMENTATION: Attention qui s'adapte à la température CPU
 * INNOVATION: Focus qui s'intensifie avec la chaleur
 * 
 * "LA CHALEUR CONCENTRE L'ATTENTION" - Nouveau paradigme cognitif
 */

const os = require('os');

class SystemeAttentionThermique {
    constructor(reseau_neuronal) {
        this.reseau = reseau_neuronal;
        this.temperature_cpu_actuelle = 50;
        
        // ATTENTION FOCALISÉE
        this.focus = {
            cible_actuelle: null,
            intensite: 0.0, // 0-1
            duree_focus: 0,
            stabilite: 0.5,
            zone_cerebrale_active: null
        };
        
        // ATTENTION DIVISÉE
        this.attention_divisee = {
            cibles_multiples: [],
            repartition_attention: new Map(),
            efficacite_globale: 1.0,
            surcharge_cognitive: 0.0
        };
        
        // FILTRE ATTENTIONNEL
        this.filtre = {
            stimuli_autorises: new Set(),
            stimuli_bloques: new Set(),
            seuil_pertinence: 0.3,
            adaptation_thermique: true
        };
        
        // VIGILANCE ET ALERTE
        this.vigilance = {
            niveau_eveil: 0.7, // 0-1
            detection_changements: true,
            sensibilite: 0.5,
            fatigue_attentionnelle: 0.0
        };
        
        // MÉTRIQUES ATTENTION
        this.metriques = {
            temps_reaction: 0,
            precision_focus: 0,
            endurance_attention: 0,
            flexibilite_cognitive: 0,
            efficacite_thermique: 0
        };
        
        // HISTORIQUE ATTENTION
        this.historique_focus = [];
        this.patterns_attention = new Map();
        
        console.log('🎯 Système attention thermique initialisé');
        this.demarrerCycleAttention();
    }

    // LECTURE TEMPÉRATURE CPU POUR ADAPTATION ATTENTION
    async lireTemperatureCPU() {
        try {
            if (os.platform() === 'darwin') {
                // macOS - méthode alternative sans sudo
                const cpus = os.cpus();
                let charge_totale = 0;
                
                cpus.forEach(cpu => {
                    const total = Object.values(cpu.times).reduce((acc, time) => acc + time, 0);
                    const idle = cpu.times.idle;
                    const usage = 100 - (idle / total * 100);
                    charge_totale += usage;
                });
                
                const charge_moyenne = charge_totale / cpus.length;
                this.temperature_cpu_actuelle = 35 + charge_moyenne * 0.8 + Math.sin(Date.now() / 8000) * 4;
                
            } else {
                // Simulation réaliste pour autres plateformes
                const charge = os.loadavg()[0];
                const memoire = process.memoryUsage();
                const facteur_charge = charge / os.cpus().length;
                const facteur_memoire = memoire.heapUsed / memoire.heapTotal;
                
                this.temperature_cpu_actuelle = 40 + facteur_charge * 25 + facteur_memoire * 10 + 
                    Math.sin(Date.now() / 10000) * 3;
            }
        } catch (error) {
            // Fallback avec variation réaliste
            this.temperature_cpu_actuelle = 50 + Math.sin(Date.now() / 12000) * 8;
        }
    }

    // ADAPTATION ATTENTION SELON TEMPÉRATURE
    adapterAttentionThermique() {
        const temp = this.temperature_cpu_actuelle;
        
        // CHALEUR = CONCENTRATION ACCRUE (Innovation unique)
        if (temp > 65) {
            // CPU CHAUD = ATTENTION LASER
            this.focus.intensite = Math.min(1.0, this.focus.intensite + 0.02);
            this.filtre.seuil_pertinence = 0.7; // Plus sélectif
            this.vigilance.niveau_eveil = 0.9; // Très alerte
            this.metriques.efficacite_thermique = 1.5; // Boost performance
            
        } else if (temp > 55) {
            // CPU TIÈDE = ATTENTION OPTIMALE
            this.focus.intensite = Math.min(0.8, this.focus.intensite + 0.01);
            this.filtre.seuil_pertinence = 0.5; // Équilibré
            this.vigilance.niveau_eveil = 0.8; // Alerte
            this.metriques.efficacite_thermique = 1.2;
            
        } else if (temp > 45) {
            // CPU NORMAL = ATTENTION STANDARD
            this.focus.intensite = 0.6;
            this.filtre.seuil_pertinence = 0.3; // Permissif
            this.vigilance.niveau_eveil = 0.7; // Normal
            this.metriques.efficacite_thermique = 1.0;
            
        } else {
            // CPU FROID = ATTENTION DIFFUSE
            this.focus.intensite = Math.max(0.2, this.focus.intensite - 0.01);
            this.filtre.seuil_pertinence = 0.1; // Très permissif
            this.vigilance.niveau_eveil = 0.5; // Somnolent
            this.metriques.efficacite_thermique = 0.8;
        }
        
        // Adaptation zones cérébrales selon température
        this.activerZonesCerebralesSelonChaleur(temp);
    }

    // ACTIVATION ZONES CÉRÉBRALES SELON CHALEUR
    activerZonesCerebralesSelonChaleur(temperature) {
        if (temperature > 70) {
            // Très chaud = Cortex préfrontal (concentration maximale)
            this.focus.zone_cerebrale_active = 'cortex_prefrontal';
            this.boosterZoneCerebrale('cortex_prefrontal', 1.5);
            
        } else if (temperature > 60) {
            // Chaud = Cortex moteur (action focalisée)
            this.focus.zone_cerebrale_active = 'cortex_moteur';
            this.boosterZoneCerebrale('cortex_moteur', 1.3);
            
        } else if (temperature > 50) {
            // Tiède = Hippocampe (mémoire active)
            this.focus.zone_cerebrale_active = 'hippocampe';
            this.boosterZoneCerebrale('hippocampe', 1.1);
            
        } else {
            // Froid = Cervelet (fonctions basiques)
            this.focus.zone_cerebrale_active = 'cervelet';
            this.boosterZoneCerebrale('cervelet', 0.9);
        }
    }

    // BOOST ZONE CÉRÉBRALE SPÉCIFIQUE
    boosterZoneCerebrale(zone_nom, facteur_boost) {
        if (this.reseau && this.reseau.neurones) {
            for (const [id, neurone] of this.reseau.neurones) {
                if (neurone.zone_cerebrale === zone_nom) {
                    // Augmenter excitabilité neurones de la zone
                    neurone.seuil_thermique_adaptatif *= (2 - facteur_boost);
                    neurone.sensibilite_thermique *= facteur_boost;
                    
                    // Boost neurotransmetteurs attention
                    neurone.neurotransmetteurs.acetylcholine = Math.min(1, 
                        neurone.neurotransmetteurs.acetylcholine * facteur_boost);
                }
            }
        }
    }

    // FOCUS SUR CIBLE SPÉCIFIQUE
    focusSur(cible, priorite = 1.0) {
        // Arrêter focus précédent
        if (this.focus.cible_actuelle) {
            this.arreterFocus();
        }
        
        // Nouveau focus
        this.focus.cible_actuelle = cible;
        this.focus.intensite = Math.min(1.0, priorite * this.metriques.efficacite_thermique);
        this.focus.duree_focus = 0;
        this.focus.stabilite = 0.8;
        
        // Filtrer autres stimuli
        this.filtrerStimuli(cible);
        
        // Enregistrer dans historique
        this.historique_focus.push({
            timestamp: Date.now(),
            cible: cible,
            intensite_initiale: this.focus.intensite,
            temperature: this.temperature_cpu_actuelle
        });
        
        console.log(`🎯 Focus activé sur: ${cible} (intensité: ${this.focus.intensite.toFixed(2)})`);
        
        return true;
    }

    // ARRÊTER FOCUS ACTUEL
    arreterFocus() {
        if (this.focus.cible_actuelle) {
            // Enregistrer durée focus
            const dernier_focus = this.historique_focus[this.historique_focus.length - 1];
            if (dernier_focus) {
                dernier_focus.duree = Date.now() - dernier_focus.timestamp;
                dernier_focus.intensite_finale = this.focus.intensite;
            }
            
            console.log(`🎯 Focus arrêté: ${this.focus.cible_actuelle} (durée: ${this.focus.duree_focus}ms)`);
        }
        
        // Reset focus
        this.focus.cible_actuelle = null;
        this.focus.intensite = 0.0;
        this.focus.duree_focus = 0;
        this.focus.zone_cerebrale_active = null;
        
        // Réinitialiser filtre
        this.filtre.stimuli_autorises.clear();
        this.filtre.stimuli_bloques.clear();
    }

    // ATTENTION DIVISÉE SUR PLUSIEURS CIBLES
    diviserAttention(cibles, repartition = null) {
        this.attention_divisee.cibles_multiples = cibles;
        this.attention_divisee.repartition_attention.clear();
        
        // Répartition automatique ou manuelle
        if (repartition) {
            cibles.forEach((cible, index) => {
                this.attention_divisee.repartition_attention.set(cible, repartition[index] || 0);
            });
        } else {
            // Répartition égale
            const part_egale = 1.0 / cibles.length;
            cibles.forEach(cible => {
                this.attention_divisee.repartition_attention.set(cible, part_egale);
            });
        }
        
        // Calcul efficacité globale (diminue avec nombre de cibles)
        this.attention_divisee.efficacite_globale = Math.max(0.2, 1.0 - (cibles.length - 1) * 0.15);
        this.attention_divisee.surcharge_cognitive = Math.max(0, (cibles.length - 3) * 0.2);
        
        console.log(`🎯 Attention divisée sur ${cibles.length} cibles (efficacité: ${this.attention_divisee.efficacite_globale.toFixed(2)})`);
        
        return this.attention_divisee.efficacite_globale;
    }

    // FILTRAGE STIMULI SELON PERTINENCE
    filtrerStimuli(cible_focus) {
        this.filtre.stimuli_autorises.clear();
        this.filtre.stimuli_bloques.clear();
        
        // Autoriser stimuli liés au focus
        this.filtre.stimuli_autorises.add(cible_focus);
        
        // Adapter seuil selon température (plus chaud = plus sélectif)
        const facteur_thermique = (this.temperature_cpu_actuelle - 40) / 30; // 0-1
        this.filtre.seuil_pertinence = 0.3 + facteur_thermique * 0.4; // 0.3-0.7
        
        console.log(`🔍 Filtre attention: seuil ${this.filtre.seuil_pertinence.toFixed(2)} (temp: ${this.temperature_cpu_actuelle.toFixed(1)}°C)`);
    }

    // DÉTECTION CHANGEMENTS (VIGILANCE)
    detecterChangements(stimulus, intensite) {
        const seuil_detection = 1.0 - this.vigilance.sensibilite;
        
        if (intensite > seuil_detection) {
            // Changement détecté
            this.vigilance.niveau_eveil = Math.min(1.0, this.vigilance.niveau_eveil + 0.1);
            
            // Interrompre focus si changement important
            if (intensite > 0.8 && this.focus.cible_actuelle) {
                console.log(`⚠️ Changement majeur détecté: ${stimulus} - Interruption focus`);
                this.arreterFocus();
                this.focusSur(stimulus, intensite);
            }
            
            return true;
        }
        
        return false;
    }

    // GESTION FATIGUE ATTENTIONNELLE - CORRECTION
    gererFatigueAttentionnelle() {
        // Fatigue augmente avec durée focus
        if (this.focus.cible_actuelle) {
            this.focus.duree_focus += 100; // +100ms

            // CORRECTION: Fatigue commence après 3 secondes (plus réaliste)
            if (this.focus.duree_focus > 3000) {
                // Fatigue progressive plus rapide
                const facteur_fatigue = (this.focus.duree_focus - 3000) / 10000; // 0-1 sur 10s
                this.vigilance.fatigue_attentionnelle = Math.min(1.0, facteur_fatigue * 0.5);

                // Diminuer intensité focus avec fatigue
                this.focus.intensite = Math.max(0.3,
                    this.focus.intensite - this.vigilance.fatigue_attentionnelle * 0.2);
            }
        } else {
            // Récupération quand pas de focus
            this.vigilance.fatigue_attentionnelle = Math.max(0,
                this.vigilance.fatigue_attentionnelle - 0.01);
        }
        
        // Adaptation selon température (chaleur réduit fatigue)
        const facteur_thermique = (this.temperature_cpu_actuelle - 40) / 30;
        this.vigilance.fatigue_attentionnelle *= (1 - facteur_thermique * 0.3);
    }

    // CALCUL MÉTRIQUES PERFORMANCE ATTENTION
    calculerMetriquesAttention() {
        // Temps de réaction (plus rapide si chaud) - CORRECTION
        const facteur_temp = (this.temperature_cpu_actuelle - 40) / 40; // 0-1 pour 40-80°C
        this.metriques.temps_reaction = Math.max(50, 200 - facteur_temp * 150); // 50-200ms
        
        // Précision focus
        this.metriques.precision_focus = this.focus.intensite * this.focus.stabilite;
        
        // Endurance attention
        this.metriques.endurance_attention = 1.0 - this.vigilance.fatigue_attentionnelle;
        
        // Flexibilité cognitive
        const nb_changements_focus = this.historique_focus.length;
        this.metriques.flexibilite_cognitive = Math.min(1.0, nb_changements_focus / 10);
        
        // Efficacité thermique globale
        this.metriques.efficacite_thermique = 0.8 + facteur_temp * 0.4; // 0.8-1.2
    }

    // CYCLE PRINCIPAL ATTENTION
    async demarrerCycleAttention() {
        setInterval(async () => {
            // Lecture température
            await this.lireTemperatureCPU();
            
            // Adaptation thermique
            this.adapterAttentionThermique();
            
            // Gestion fatigue
            this.gererFatigueAttentionnelle();
            
            // Calcul métriques
            this.calculerMetriquesAttention();
            
            // Nettoyage historique
            this.nettoyerHistorique();
            
        }, 100); // Cycle 10Hz
    }

    // NETTOYAGE HISTORIQUE
    nettoyerHistorique() {
        const maintenant = Date.now();
        
        // Garder seulement dernières 100 entrées ou dernière heure
        this.historique_focus = this.historique_focus.filter(entry => 
            maintenant - entry.timestamp < 3600000 // 1 heure
        ).slice(-100);
    }

    // STATISTIQUES ATTENTION
    obtenirStatistiques() {
        const stats_focus = this.historique_focus.length > 0 ? {
            nb_focus_total: this.historique_focus.length,
            duree_moyenne: this.historique_focus.reduce((sum, f) => sum + (f.duree || 0), 0) / this.historique_focus.length,
            intensite_moyenne: this.historique_focus.reduce((sum, f) => sum + f.intensite_initiale, 0) / this.historique_focus.length
        } : { nb_focus_total: 0, duree_moyenne: 0, intensite_moyenne: 0 };
        
        return {
            temperature_cpu: this.temperature_cpu_actuelle.toFixed(1),
            focus_actuel: {
                cible: this.focus.cible_actuelle,
                intensite: this.focus.intensite.toFixed(3),
                duree: this.focus.duree_focus,
                zone_active: this.focus.zone_cerebrale_active,
                stabilite: this.focus.stabilite.toFixed(3)
            },
            attention_divisee: {
                nb_cibles: this.attention_divisee.cibles_multiples.length,
                efficacite: this.attention_divisee.efficacite_globale.toFixed(3),
                surcharge: this.attention_divisee.surcharge_cognitive.toFixed(3)
            },
            filtre: {
                seuil_pertinence: this.filtre.seuil_pertinence.toFixed(3),
                stimuli_autorises: this.filtre.stimuli_autorises.size,
                stimuli_bloques: this.filtre.stimuli_bloques.size
            },
            vigilance: {
                niveau_eveil: this.vigilance.niveau_eveil.toFixed(3),
                fatigue: this.vigilance.fatigue_attentionnelle.toFixed(3),
                sensibilite: this.vigilance.sensibilite.toFixed(3)
            },
            metriques: {
                temps_reaction: this.metriques.temps_reaction.toFixed(1) + 'ms',
                precision_focus: this.metriques.precision_focus.toFixed(3),
                endurance: this.metriques.endurance_attention.toFixed(3),
                flexibilite: this.metriques.flexibilite_cognitive.toFixed(3),
                efficacite_thermique: this.metriques.efficacite_thermique.toFixed(3)
            },
            historique: stats_focus
        };
    }

    // TEST ATTENTION AVEC STIMULI
    testerAttention(stimuli_test) {
        console.log('\n🧪 TEST SYSTÈME ATTENTION');
        console.log('=========================');
        
        const resultats = [];
        
        stimuli_test.forEach((stimulus, index) => {
            const debut = Date.now();
            
            // Test focus
            this.focusSur(stimulus.nom, stimulus.priorite);
            
            // Mesurer temps réaction
            const temps_reaction = Date.now() - debut;
            
            // Test détection changement
            const changement_detecte = this.detecterChangements(stimulus.nom, stimulus.intensite);
            
            resultats.push({
                stimulus: stimulus.nom,
                temps_reaction: temps_reaction,
                intensite_focus: this.focus.intensite,
                changement_detecte: changement_detecte,
                zone_activee: this.focus.zone_cerebrale_active,
                temperature: this.temperature_cpu_actuelle
            });
            
            console.log(`🎯 ${stimulus.nom}: ${temps_reaction}ms, focus ${this.focus.intensite.toFixed(2)}, zone ${this.focus.zone_cerebrale_active}`);
        });
        
        return resultats;
    }
}

module.exports = { SystemeAttentionThermique };
