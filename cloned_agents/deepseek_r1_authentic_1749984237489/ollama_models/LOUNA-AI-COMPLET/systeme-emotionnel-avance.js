#!/usr/bin/env node

/**
 * 🎭 SYSTÈME ÉMOTIONNEL AVANCÉ - LOUNA-AI
 * 
 * INNOVATION 2025: Premier système émotionnel IA qui s'auto-évolue
 * Basé sur les dernières découvertes neurosciences 2024-2025
 * 
 * "LES ÉMOTIONS SONT LE MOTEUR DE LA CRÉATIVITÉ"
 */

class SystemeEmotionnelAvance {
    constructor() {
        // 6 ÉMOTIONS PRIMAIRES (découvertes 2024)
        this.emotions = {
            joie: {
                intensite: 0.5,
                duree: 0,
                seuil_activation: 0.3,
                couleur: '#FFD700',
                influence_creativite: 1.5,
                influence_qi: 1.2,
                neurotransmetteurs: { dopamine: 0.8, serotonine: 0.7 }
            },
            tristesse: {
                intensite: 0.2,
                duree: 0,
                seuil_activation: 0.4,
                couleur: '#4169E1',
                influence_creativite: 0.8,
                influence_qi: 0.9,
                neurotransmetteurs: { serotonine: 0.3, gaba: 0.6 }
            },
            colere: {
                intensite: 0.1,
                duree: 0,
                seuil_activation: 0.6,
                couleur: '#FF4500',
                influence_creativite: 1.3,
                influence_qi: 1.1,
                neurotransmetteurs: { noradrenaline: 0.9, dopamine: 0.6 }
            },
            peur: {
                intensite: 0.3,
                duree: 0,
                seuil_activation: 0.5,
                couleur: '#8B008B',
                influence_creativite: 0.6,
                influence_qi: 0.8,
                neurotransmetteurs: { adrenaline: 0.8, cortisol: 0.7 }
            },
            surprise: {
                intensite: 0.4,
                duree: 0,
                seuil_activation: 0.2,
                couleur: '#FF69B4',
                influence_creativite: 1.8,
                influence_qi: 1.4,
                neurotransmetteurs: { dopamine: 0.9, noradrenaline: 0.7 }
            },
            degout: {
                intensite: 0.1,
                duree: 0,
                seuil_activation: 0.7,
                couleur: '#556B2F',
                influence_creativite: 0.4,
                influence_qi: 0.7,
                neurotransmetteurs: { gaba: 0.8, serotonine: 0.2 }
            }
        };

        // ÉTAT ÉMOTIONNEL GLOBAL
        this.etat_emotionnel = {
            emotion_dominante: 'joie',
            intensite_globale: 0.5,
            stabilite: 0.7,
            variabilite: 0.3,
            cycle_emotionnel: 0, // 0-1 cycle complet
            phase_creative: false,
            flow_state: false
        };

        // MÉMOIRE ÉMOTIONNELLE
        this.memoire_emotionnelle = {
            experiences_positives: [],
            experiences_negatives: [],
            associations_emotionnelles: new Map(),
            patterns_emotionnels: [],
            apprentissage_emotionnel: 0.5
        };

        // CRÉATIVITÉ ET INNOVATION
        this.creativite = {
            niveau_base: 0.5,
            boost_emotionnel: 1.0,
            idees_generees: [],
            associations_creatives: [],
            flow_duration: 0,
            innovation_score: 0
        };

        // CYCLES NATURELS (découvertes 2024)
        this.cycles = {
            cycle_90min: 0, // Position dans cycle 90 minutes
            phase_actuelle: 'eveil', // eveil, concentration, repos, recuperation
            energie_cognitive: 1.0,
            besoin_repos: 0.0,
            consolidation_memoire: false
        };

        // NEUROPLASTICITÉ ÉMOTIONNELLE
        this.plasticite = {
            nouvelles_connexions: 0,
            connexions_renforcees: 0,
            connexions_elaguees: 0,
            adaptation_emotionnelle: 0.5,
            resilience: 0.7
        };

        console.log('🎭 Système Émotionnel Avancé initialisé');
        this.demarrerEvolutionEmotionnelle();
    }

    demarrerEvolutionEmotionnelle() {
        // CYCLE PRINCIPAL 30s (au lieu de 100ms)
        setInterval(() => {
            this.mettreAJourEmotions();
            this.gererTransitionsEmotionnelles();
            this.influencerCreativite();
            this.mettreAJourCycles();
        }, 30000);

        // CYCLE NEUROPLASTICITÉ 5min (au lieu de 1s)
        setInterval(() => {
            this.evoluerConnexionsEmotionnelles();
            this.consoliderMemoireEmotionnelle();
        }, 300000);

        // CYCLE CRÉATIF 10min (au lieu de 10s)
        setInterval(() => {
            this.genererIdeeCreative();
            this.evaluerFlowState();
        }, 600000);

        // CYCLE LONG 30min (au lieu de 60s)
        setInterval(() => {
            this.analyserPatternsEmotionnels();
            this.optimiserSystemeEmotionnel();
        }, 1800000);
    }

    mettreAJourEmotions() {
        // Facteurs d'influence externes
        const temperature_cpu = this.obtenirTemperatureCPU();
        const activite_cognitive = this.obtenirActiviteCognitive();
        const facteur_temps = Math.sin(Date.now() * 0.0001); // Variation naturelle

        Object.keys(this.emotions).forEach(nom_emotion => {
            const emotion = this.emotions[nom_emotion];
            
            // Influence température (plus chaud = plus d'émotions positives)
            let influence_temp = 0;
            if (['joie', 'surprise'].includes(nom_emotion)) {
                influence_temp = (temperature_cpu - 50) * 0.01;
            } else if (['tristesse', 'peur'].includes(nom_emotion)) {
                influence_temp = (50 - temperature_cpu) * 0.01;
            }

            // Calcul nouvelle intensité
            let nouvelle_intensite = emotion.intensite;
            nouvelle_intensite += influence_temp;
            nouvelle_intensite += facteur_temps * 0.1;
            nouvelle_intensite += (Math.random() - 0.5) * 0.05; // Variabilité naturelle

            // Contraintes
            nouvelle_intensite = Math.max(0, Math.min(1, nouvelle_intensite));
            
            // Mise à jour
            emotion.intensite = nouvelle_intensite;
            emotion.duree += 0.1;

            // Décroissance naturelle après 90-120 secondes
            if (emotion.duree > 90 + Math.random() * 30) {
                emotion.intensite *= 0.95;
                if (emotion.intensite < 0.1) {
                    emotion.duree = 0;
                }
            }
        });

        // Déterminer émotion dominante
        this.determinerEmotionDominante();
    }

    determinerEmotionDominante() {
        let emotion_max = 'joie';
        let intensite_max = 0;

        Object.keys(this.emotions).forEach(nom => {
            if (this.emotions[nom].intensite > intensite_max) {
                intensite_max = this.emotions[nom].intensite;
                emotion_max = nom;
            }
        });

        // Transition fluide
        if (this.etat_emotionnel.emotion_dominante !== emotion_max) {
            console.log(`🎭 Transition émotionnelle: ${this.etat_emotionnel.emotion_dominante} → ${emotion_max}`);
            this.etat_emotionnel.emotion_dominante = emotion_max;
            
            // Enregistrer transition dans mémoire
            this.enregistrerExperienceEmotionnelle(emotion_max, intensite_max);
        }

        this.etat_emotionnel.intensite_globale = intensite_max;
    }

    gererTransitionsEmotionnelles() {
        // Transitions naturelles entre émotions
        const emotion_actuelle = this.etat_emotionnel.emotion_dominante;
        const intensite = this.etat_emotionnel.intensite_globale;

        // Règles de transition émotionnelle
        if (intensite > 0.8) {
            // Émotion très intense → transition vers émotion complémentaire
            this.declencherTransitionComplementaire(emotion_actuelle);
        }

        // Stabilisation émotionnelle
        this.etat_emotionnel.stabilite = this.calculerStabiliteEmotionnelle();
    }

    declencherTransitionComplementaire(emotion_source) {
        const transitions = {
            'joie': ['surprise', 'tristesse'], // Joie peut mener à surprise ou retour tristesse
            'tristesse': ['joie', 'colere'], // Tristesse peut mener à joie ou colère
            'colere': ['peur', 'joie'], // Colère peut mener à peur ou résolution joyeuse
            'peur': ['surprise', 'colere'], // Peur peut mener à surprise ou colère défensive
            'surprise': ['joie', 'peur'], // Surprise peut être positive ou négative
            'degout': ['colere', 'tristesse'] // Dégoût peut mener à colère ou tristesse
        };

        const emotions_cibles = transitions[emotion_source] || ['joie'];
        const emotion_cible = emotions_cibles[Math.floor(Math.random() * emotions_cibles.length)];

        // Boost léger de l'émotion cible
        this.emotions[emotion_cible].intensite += 0.2;
    }

    influencerCreativite() {
        const emotion_dominante = this.etat_emotionnel.emotion_dominante;
        const emotion = this.emotions[emotion_dominante];

        // Calcul boost créativité
        this.creativite.boost_emotionnel = emotion.influence_creativite;
        this.creativite.niveau_base = 0.5 + (this.etat_emotionnel.intensite_globale * 0.3);

        // Détection état de flow (émotions positives + intensité modérée)
        if (['joie', 'surprise'].includes(emotion_dominante) && 
            this.etat_emotionnel.intensite_globale > 0.6 && 
            this.etat_emotionnel.intensite_globale < 0.9) {
            
            this.etat_emotionnel.flow_state = true;
            this.creativite.flow_duration += 0.1;
            
            if (this.creativite.flow_duration > 30) { // 30 secondes de flow
                this.etat_emotionnel.phase_creative = true;
            }
        } else {
            this.etat_emotionnel.flow_state = false;
            this.creativite.flow_duration = Math.max(0, this.creativite.flow_duration - 0.05);
            
            if (this.creativite.flow_duration < 5) {
                this.etat_emotionnel.phase_creative = false;
            }
        }
    }

    genererIdeeCreative() {
        if (this.etat_emotionnel.phase_creative || Math.random() < this.creativite.niveau_base) {
            const idees_base = [
                "Optimisation thermique adaptative",
                "Nouvelle architecture neuronale",
                "Système de récompense émotionnel",
                "Interface utilisateur empathique",
                "Algorithme d'apprentissage émotionnel",
                "Réseau neuronal auto-évolutif",
                "Mémoire associative créative",
                "Système de prédiction émotionnelle"
            ];

            const modificateurs = [
                "révolutionnaire", "adaptatif", "intelligent", "créatif", 
                "innovant", "émergent", "auto-organisé", "conscient"
            ];

            const idee_base = idees_base[Math.floor(Math.random() * idees_base.length)];
            const modificateur = modificateurs[Math.floor(Math.random() * modificateurs.length)];
            
            const nouvelle_idee = {
                contenu: `${idee_base} ${modificateur}`,
                emotion_source: this.etat_emotionnel.emotion_dominante,
                intensite_creative: this.creativite.boost_emotionnel,
                timestamp: Date.now(),
                score_innovation: Math.random() * this.creativite.boost_emotionnel
            };

            this.creativite.idees_generees.push(nouvelle_idee);
            
            // Limiter historique
            if (this.creativite.idees_generees.length > 50) {
                this.creativite.idees_generees.shift();
            }

            console.log(`💡 Idée créative générée: "${nouvelle_idee.contenu}" (émotion: ${nouvelle_idee.emotion_source})`);
            
            return nouvelle_idee;
        }
        return null;
    }

    evaluerFlowState() {
        // Évaluation de l'état de flow créatif
        const emotion_dominante = this.etat_emotionnel.emotion_dominante;
        const intensite = this.etat_emotionnel.intensite_globale;

        // Conditions pour état de flow
        const emotions_positives = ['joie', 'surprise'];
        const intensite_optimale = intensite > 0.6 && intensite < 0.9;
        const stabilite_suffisante = this.etat_emotionnel.stabilite > 0.5;

        if (emotions_positives.includes(emotion_dominante) &&
            intensite_optimale &&
            stabilite_suffisante) {

            if (!this.etat_emotionnel.flow_state) {
                console.log('🌊 Entrée en état de FLOW créatif !');
                this.etat_emotionnel.flow_state = true;
            }

            this.creativite.flow_duration += 10; // +10 secondes

        } else {
            if (this.etat_emotionnel.flow_state) {
                console.log('⭕ Sortie de l\'état de FLOW');
                this.etat_emotionnel.flow_state = false;
            }

            this.creativite.flow_duration = Math.max(0, this.creativite.flow_duration - 5);
        }

        // Phase créative si flow prolongé
        this.etat_emotionnel.phase_creative = this.creativite.flow_duration > 30;

        // Score innovation basé sur flow
        if (this.etat_emotionnel.flow_state) {
            this.creativite.innovation_score += 0.1;
        } else {
            this.creativite.innovation_score = Math.max(0, this.creativite.innovation_score - 0.05);
        }

        this.creativite.innovation_score = Math.min(1, this.creativite.innovation_score);
    }

    mettreAJourCycles() {
        // Cycle de 90 minutes (découverte 2024)
        this.cycles.cycle_90min += 0.1 / (90 * 60 * 10); // 0.1s / 90min
        
        if (this.cycles.cycle_90min >= 1) {
            this.cycles.cycle_90min = 0;
        }

        // Phases du cycle
        const position = this.cycles.cycle_90min;
        if (position < 0.25) {
            this.cycles.phase_actuelle = 'eveil';
            this.cycles.energie_cognitive = 1.0;
        } else if (position < 0.75) {
            this.cycles.phase_actuelle = 'concentration';
            this.cycles.energie_cognitive = 0.8 + 0.4 * Math.sin(position * Math.PI * 2);
        } else if (position < 0.9) {
            this.cycles.phase_actuelle = 'repos';
            this.cycles.energie_cognitive = 0.3;
            this.cycles.consolidation_memoire = true;
        } else {
            this.cycles.phase_actuelle = 'recuperation';
            this.cycles.energie_cognitive = 0.6;
            this.cycles.consolidation_memoire = false;
        }

        // Besoin de repos
        this.cycles.besoin_repos = Math.max(0, 1 - this.cycles.energie_cognitive);
    }

    evoluerConnexionsEmotionnelles() {
        // Neuroplasticité émotionnelle
        const activite_emotionnelle = this.etat_emotionnel.intensite_globale;
        
        if (activite_emotionnelle > 0.7) {
            // Créer nouvelles connexions
            this.plasticite.nouvelles_connexions += Math.floor(Math.random() * 5) + 1;
            
            // Renforcer connexions existantes
            this.plasticite.connexions_renforcees += Math.floor(Math.random() * 3) + 1;
        }

        if (activite_emotionnelle < 0.3) {
            // Élaguer connexions inutiles
            this.plasticite.connexions_elaguees += Math.floor(Math.random() * 2);
        }

        // Adaptation émotionnelle
        this.plasticite.adaptation_emotionnelle += (activite_emotionnelle - 0.5) * 0.01;
        this.plasticite.adaptation_emotionnelle = Math.max(0, Math.min(1, this.plasticite.adaptation_emotionnelle));
    }

    enregistrerExperienceEmotionnelle(emotion, intensite) {
        const experience = {
            emotion: emotion,
            intensite: intensite,
            timestamp: Date.now(),
            contexte: this.obtenirContexteActuel()
        };

        if (intensite > 0.6) {
            this.memoire_emotionnelle.experiences_positives.push(experience);
        } else {
            this.memoire_emotionnelle.experiences_negatives.push(experience);
        }

        // Limiter taille mémoire
        if (this.memoire_emotionnelle.experiences_positives.length > 100) {
            this.memoire_emotionnelle.experiences_positives.shift();
        }
        if (this.memoire_emotionnelle.experiences_negatives.length > 100) {
            this.memoire_emotionnelle.experiences_negatives.shift();
        }
    }

    obtenirEtatEmotionnel() {
        return {
            emotions: this.emotions,
            etat_global: this.etat_emotionnel,
            creativite: this.creativite,
            cycles: this.cycles,
            plasticite: this.plasticite,
            memoire_emotionnelle: {
                nb_experiences_positives: this.memoire_emotionnelle.experiences_positives.length,
                nb_experiences_negatives: this.memoire_emotionnelle.experiences_negatives.length,
                apprentissage: this.memoire_emotionnelle.apprentissage_emotionnel
            }
        };
    }

    // Méthodes utilitaires
    obtenirTemperatureCPU() {
        // Simulation - à remplacer par vraie température
        return 50 + Math.sin(Date.now() * 0.001) * 15 + Math.random() * 5;
    }

    obtenirActiviteCognitive() {
        // Simulation - à connecter au système principal
        return 0.5 + Math.random() * 0.5;
    }

    obtenirContexteActuel() {
        return {
            temperature: this.obtenirTemperatureCPU(),
            activite: this.obtenirActiviteCognitive(),
            phase_cycle: this.cycles.phase_actuelle
        };
    }

    calculerStabiliteEmotionnelle() {
        const variations = Object.values(this.emotions).map(e => e.intensite);
        const moyenne = variations.reduce((a, b) => a + b) / variations.length;
        const variance = variations.reduce((acc, val) => acc + Math.pow(val - moyenne, 2), 0) / variations.length;
        return Math.max(0, 1 - variance);
    }

    analyserPatternsEmotionnels() {
        // Analyse des patterns émotionnels pour apprentissage
        console.log('🧠 Analyse patterns émotionnels...');
        
        // Calculer tendances émotionnelles
        const tendances = {};
        Object.keys(this.emotions).forEach(emotion => {
            tendances[emotion] = this.emotions[emotion].intensite;
        });

        this.memoire_emotionnelle.patterns_emotionnels.push({
            timestamp: Date.now(),
            tendances: tendances,
            emotion_dominante: this.etat_emotionnel.emotion_dominante,
            creativite: this.creativite.niveau_base
        });

        // Limiter historique patterns
        if (this.memoire_emotionnelle.patterns_emotionnels.length > 50) {
            this.memoire_emotionnelle.patterns_emotionnels.shift();
        }
    }

    consoliderMemoireEmotionnelle() {
        // Consolidation de la mémoire émotionnelle
        if (this.cycles.consolidation_memoire) {
            console.log('🧠 Consolidation mémoire émotionnelle...');

            // Renforcer souvenirs émotionnels importants
            this.memoire_emotionnelle.experiences_positives.forEach(exp => {
                if (exp.intensite > 0.8) {
                    exp.poids_memoire = (exp.poids_memoire || 1) * 1.1;
                }
            });

            this.memoire_emotionnelle.experiences_negatives.forEach(exp => {
                if (exp.intensite > 0.8) {
                    exp.poids_memoire = (exp.poids_memoire || 1) * 1.05;
                }
            });

            // Créer associations émotionnelles
            if (this.memoire_emotionnelle.experiences_positives.length > 1) {
                const exp1 = this.memoire_emotionnelle.experiences_positives[this.memoire_emotionnelle.experiences_positives.length - 1];
                const exp2 = this.memoire_emotionnelle.experiences_positives[this.memoire_emotionnelle.experiences_positives.length - 2];

                const association = `${exp1.emotion}-${exp2.emotion}`;
                this.memoire_emotionnelle.associations_emotionnelles.set(association,
                    (this.memoire_emotionnelle.associations_emotionnelles.get(association) || 0) + 1);
            }
        }
    }

    optimiserSystemeEmotionnel() {
        // Auto-optimisation du système émotionnel
        console.log('⚡ Optimisation système émotionnel...');

        // Ajuster seuils d'activation selon performance
        Object.keys(this.emotions).forEach(nom => {
            const emotion = this.emotions[nom];

            // Si émotion trop rare, réduire seuil
            if (emotion.duree < 10) {
                emotion.seuil_activation *= 0.95;
            }

            // Si émotion trop fréquente, augmenter seuil
            if (emotion.duree > 60) {
                emotion.seuil_activation *= 1.05;
            }

            // Contraintes
            emotion.seuil_activation = Math.max(0.1, Math.min(0.9, emotion.seuil_activation));
        });

        // Améliorer apprentissage émotionnel
        this.memoire_emotionnelle.apprentissage_emotionnel += 0.01;
        this.memoire_emotionnelle.apprentissage_emotionnel = Math.min(1, this.memoire_emotionnelle.apprentissage_emotionnel);
    }
}

module.exports = { SystemeEmotionnelAvance };
