import axios from 'axios';

const OLLAMA_API_URL = 'http://localhost:11434/api';

// Configuration d'axios avec des en-têtes CORS
axios.defaults.headers.common['Access-Control-Allow-Origin'] = '*';
axios.defaults.headers.common['Content-Type'] = 'application/json';

/**
 * Liste tous les modèles disponibles
 */
export const listModels = async () => {
  try {
    const response = await axios.get(`${OLLAMA_API_URL}/tags`);
    return response.data.models || [];
  } catch (error) {
    console.error('Erreur lors de la récupération des modèles:', error);
    throw error;
  }
};

/**
 * Télécharge un modèle s'il n'est pas déjà disponible
 */
export const pullModel = async (modelName) => {
  try {
    const response = await axios.post(`${OLLAMA_API_URL}/pull`, {
      name: modelName,
      stream: false
    });
    return response.data;
  } catch (error) {
    console.error(`Erreur lors du téléchargement du modèle ${modelName}:`, error);
    throw error;
  }
};

/**
 * Envoie un message au modèle et récupère la réponse
 */
export const chatWithModel = async (modelName, messages, options = {}) => {
  try {
    const response = await axios.post(`${OLLAMA_API_URL}/chat`, {
      model: modelName,
      messages,
      stream: false,
      options
    });
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la communication avec le modèle:', error);
    throw error;
  }
};

/**
 * Vérifie si Ollama est en cours d'exécution
 */
export const checkOllamaStatus = async () => {
  try {
    const response = await axios.get(`${OLLAMA_API_URL}/version`);
    return { running: true, version: response.data.version };
  } catch (error) {
    return { running: false, error: error.message };
  }
};
