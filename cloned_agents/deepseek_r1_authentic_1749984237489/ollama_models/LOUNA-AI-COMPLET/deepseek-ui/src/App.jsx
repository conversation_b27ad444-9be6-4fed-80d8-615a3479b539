import { useState, useEffect } from 'react'
import {
  Box,
  Container,
  Flex,
  Heading,
  Text,
  VStack,
  useToast,
  Spinner,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Button,
} from '@chakra-ui/react'
import ChatInterface from './components/ChatInterface'
import ModelSelector from './components/ModelSelector'
import { checkOllamaStatus, listModels, pullModel } from './api/ollamaApi'

function App() {
  const [isOllamaRunning, setIsOllamaRunning] = useState(false)
  const [ollamaVersion, setOllamaVersion] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [availableModels, setAvailableModels] = useState([])
  const [selectedModel, setSelectedModel] = useState('')
  const [isModelLoading, setIsModelLoading] = useState(false)
  const toast = useToast()

  // État pour stocker les erreurs
  const [error, setError] = useState(null)

  // Vérifier si Ollama est en cours d'exécution
  useEffect(() => {
    const checkStatus = async () => {
      try {
        console.log("Vérification du statut d'Ollama...")
        const status = await checkOllamaStatus()
        console.log("Statut d'Ollama:", status)
        setIsOllamaRunning(status.running)
        if (status.running) {
          setOllamaVersion(status.version)
          fetchAvailableModels()
        } else {
          setError("Ollama n'est pas en cours d'exécution")
        }
      } catch (error) {
        console.error('Erreur lors de la vérification du statut d\'Ollama:', error)
        setError(`Erreur: ${error.message}`)
      } finally {
        setIsLoading(false)
      }
    }

    checkStatus()
  }, [])

  // Récupérer les modèles disponibles
  const fetchAvailableModels = async () => {
    try {
      const models = await listModels()
      setAvailableModels(models)

      // Sélectionner automatiquement un modèle deepseek-r1 s'il est disponible
      const deepseekModel = models.find(model => model.name.includes('deepseek-r1'))
      if (deepseekModel) {
        setSelectedModel(deepseekModel.name)
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des modèles:', error)
      toast({
        title: 'Erreur',
        description: 'Impossible de récupérer la liste des modèles',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    }
  }

  // Télécharger un modèle
  const handleModelDownload = async (modelName) => {
    setIsModelLoading(true)
    try {
      await pullModel(modelName)
      toast({
        title: 'Succès',
        description: `Le modèle ${modelName} a été téléchargé avec succès`,
        status: 'success',
        duration: 5000,
        isClosable: true,
      })
      await fetchAvailableModels()
      setSelectedModel(modelName)
    } catch (error) {
      toast({
        title: 'Erreur',
        description: `Impossible de télécharger le modèle ${modelName}`,
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    } finally {
      setIsModelLoading(false)
    }
  }

  if (isLoading) {
    return (
      <Flex height="100vh" alignItems="center" justifyContent="center">
        <Spinner size="xl" />
      </Flex>
    )
  }

  if (!isOllamaRunning) {
    return (
      <Container maxW="container.xl" py={10}>
        <Alert status="error" variant="solid" flexDirection="column" alignItems="center" justifyContent="center" textAlign="center" height="200px">
          <AlertIcon boxSize="40px" mr={0} />
          <AlertTitle mt={4} mb={1} fontSize="lg">
            Ollama n'est pas en cours d'exécution
          </AlertTitle>
          <AlertDescription maxWidth="sm">
            Veuillez démarrer Ollama pour utiliser cette application.
            <Button mt={4} colorScheme="red" onClick={() => window.location.reload()}>
              Réessayer
            </Button>
          </AlertDescription>
        </Alert>
      </Container>
    )
  }

  return (
    <Container maxW="container.xl" py={4}>
      <VStack spacing={6} align="stretch">
        <Box textAlign="center" mb={6}>
          <Heading as="h1" size="xl" mb={2}>
            DeepSeek r1 Interface
          </Heading>
          <Text fontSize="md" color="gray.400">
            Une interface moderne pour interagir avec l'agent DeepSeek r1 via Ollama v{ollamaVersion}
          </Text>
        </Box>

        <ModelSelector
          availableModels={availableModels}
          selectedModel={selectedModel}
          setSelectedModel={setSelectedModel}
          onModelDownload={handleModelDownload}
          isModelLoading={isModelLoading}
        />

        {selectedModel && (
          <ChatInterface modelName={selectedModel} />
        )}
      </VStack>
    </Container>
  )
}

export default App
