import { useState } from 'react'
import {
  Box,
  Flex,
  Text,
  IconButton,
  useToast,
  Tooltip,
  useColorModeValue,
} from '@chakra-ui/react'
import { FiCopy, FiUser, FiCpu } from 'react-icons/fi'
import ReactMarkdown from 'react-markdown'

const MessageItem = ({ message }) => {
  const [isHovered, setIsHovered] = useState(false)
  const toast = useToast()
  
  const isUser = message.role === 'user'
  
  const bgColor = isUser ? 'blue.700' : 'gray.700'
  const iconBg = isUser ? 'blue.500' : 'purple.500'
  
  const copyToClipboard = () => {
    navigator.clipboard.writeText(message.content)
    toast({
      title: 'Copié',
      description: 'Le message a été copié dans le presse-papiers',
      status: 'success',
      duration: 2000,
      isClosable: true,
    })
  }

  // Fonction pour formater le code dans le markdown
  const formatCodeBlocks = (content) => {
    // Remplacer les blocs de code avec des balises ```
    return content.replace(/```([\s\S]*?)```/g, (match, code) => {
      return `\`\`\`\n${code.trim()}\n\`\`\``
    })
  }

  return (
    <Box
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      position="relative"
      width="100%"
    >
      <Flex mb={2}>
        <Box
          bg={iconBg}
          color="white"
          borderRadius="full"
          p={2}
          mr={2}
          display="flex"
          alignItems="center"
          justifyContent="center"
        >
          {isUser ? <FiUser /> : <FiCpu />}
        </Box>
        <Text fontWeight="bold" alignSelf="center">
          {isUser ? 'Vous' : 'DeepSeek r1'}
        </Text>
      </Flex>
      
      <Box
        ml={10}
        p={4}
        borderRadius="md"
        bg={bgColor}
        position="relative"
      >
        <Box className="markdown-content" sx={{
          'pre': {
            bg: 'gray.800',
            p: 3,
            borderRadius: 'md',
            overflowX: 'auto',
          },
          'code': {
            bg: 'gray.800',
            p: 1,
            borderRadius: 'sm',
            fontFamily: 'monospace',
          },
          'p': {
            my: 2,
          },
          'ul, ol': {
            pl: 5,
            my: 2,
          },
          'li': {
            my: 1,
          },
          'h1, h2, h3, h4, h5, h6': {
            fontWeight: 'bold',
            my: 3,
          },
          'a': {
            color: 'blue.300',
            textDecoration: 'underline',
          },
          'blockquote': {
            borderLeftWidth: '4px',
            borderLeftColor: 'gray.500',
            pl: 3,
            py: 1,
            my: 2,
          },
          'table': {
            width: '100%',
            my: 3,
            borderCollapse: 'collapse',
          },
          'th, td': {
            borderWidth: '1px',
            borderColor: 'gray.600',
            p: 2,
          },
          'th': {
            bg: 'gray.700',
            fontWeight: 'bold',
          }
        }}>
          <ReactMarkdown>
            {formatCodeBlocks(message.content)}
          </ReactMarkdown>
        </Box>
        
        {isHovered && (
          <Tooltip label="Copier le message">
            <IconButton
              icon={<FiCopy />}
              size="sm"
              aria-label="Copier le message"
              position="absolute"
              top={2}
              right={2}
              onClick={copyToClipboard}
              variant="ghost"
            />
          </Tooltip>
        )}
      </Box>
    </Box>
  )
}

export default MessageItem
