import { useState, useRef, useEffect } from 'react'
import {
  Box,
  Button,
  Flex,
  Input,
  Text,
  VStack,
  HStack,
  IconButton,
  Textarea,
  useToast,
  Spinner,
  Divider,
  Badge,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Tooltip,
} from '@chakra-ui/react'
import { FiSend, FiTrash2, FiSettings, FiCopy, FiChevronDown, FiRefreshCw, FiZap } from 'react-icons/fi'
import { chatWithModel } from '../api/ollamaApi'
import MessageItem from './MessageItem'

const ChatInterface = ({ modelName }) => {
  const [messages, setMessages] = useState([])
  const [input, setInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [temperature, setTemperature] = useState(0.7)
  const endOfMessagesRef = useRef(null)
  const toast = useToast()

  // Options du modèle
  const modelOptions = {
    temperature: temperature,
    top_p: 0.9,
    top_k: 40,
  }

  // Faire défiler vers le bas lorsque de nouveaux messages sont ajoutés
  useEffect(() => {
    if (endOfMessagesRef.current) {
      endOfMessagesRef.current.scrollIntoView({ behavior: 'smooth' })
    }
  }, [messages])

  const handleSendMessage = async () => {
    if (!input.trim()) return

    const userMessage = { role: 'user', content: input }
    setMessages(prev => [...prev, userMessage])
    setInput('')
    setIsLoading(true)

    try {
      const formattedMessages = [...messages, userMessage]
      const response = await chatWithModel(modelName, formattedMessages, modelOptions)

      if (response && response.message) {
        setMessages(prev => [...prev, response.message])
      }
    } catch (error) {
      console.error('Erreur lors de l\'envoi du message:', error)
      toast({
        title: 'Erreur',
        description: 'Impossible d\'obtenir une réponse du modèle',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const clearChat = () => {
    setMessages([])
  }

  const copyConversation = () => {
    const conversationText = messages
      .map(msg => `${msg.role === 'user' ? 'Vous' : 'DeepSeek r1'}: ${msg.content}`)
      .join('\n\n')

    navigator.clipboard.writeText(conversationText)
    toast({
      title: 'Copié',
      description: 'La conversation a été copiée dans le presse-papiers',
      status: 'success',
      duration: 2000,
      isClosable: true,
    })
  }

  const refreshConversation = () => {
    // Sauvegarder les messages actuels
    const currentMessages = [...messages]

    // Afficher un toast pour indiquer que le rafraîchissement est en cours
    toast({
      title: 'Rafraîchissement',
      description: 'Rafraîchissement de l\'affichage de la conversation en cours...',
      status: 'info',
      duration: 2000,
      isClosable: true,
    })

    // Simuler un rafraîchissement en vidant puis en restaurant les messages
    // Cela ne modifie pas la mémoire thermique, seulement l'affichage
    setMessages([])
    setTimeout(() => {
      setMessages(currentMessages)
      toast({
        title: 'Affichage rafraîchi',
        description: 'L\'affichage de la conversation a été rafraîchi. La mémoire thermique reste intacte.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      })
    }, 500)
  }

  // Fonction pour lancer une session de formation aléatoire
  const launchTraining = async () => {
    // Générer un nombre aléatoire entre 1 et 5 (comme un dé)
    const diceRoll = Math.floor(Math.random() * 5) + 1

    toast({
      title: 'Formation lancée',
      description: `Lancement d'une session de formation avec ${diceRoll} question(s) aléatoire(s)...`,
      status: 'info',
      duration: 3000,
      isClosable: true,
    })

    try {
      // Appel à l'API pour lancer la formation
      const response = await fetch('/api/training', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          questionCount: diceRoll,
          modelName: modelName
        }),
      })

      if (response.ok) {
        const result = await response.json()

        // Ajouter les questions et réponses à la conversation
        const trainingMessages = []

        result.sessions.forEach(session => {
          trainingMessages.push({ role: 'system', content: `--- Session de formation ${session.id} ---` })
          trainingMessages.push({ role: 'user', content: session.question })
          trainingMessages.push({ role: 'assistant', content: session.response })
          if (session.feedback) {
            trainingMessages.push({ role: 'system', content: `Feedback: ${session.feedback}` })
          }
        })

        setMessages(prev => [...prev, ...trainingMessages])

        toast({
          title: 'Formation terminée',
          description: `${diceRoll} question(s) traitée(s) avec succès. Les connaissances ont été ajoutées à la mémoire thermique.`,
          status: 'success',
          duration: 5000,
          isClosable: true,
        })
      } else {
        throw new Error('Erreur lors de la formation')
      }
    } catch (error) {
      console.error('Erreur lors de la formation:', error)
      toast({
        title: 'Erreur',
        description: 'Impossible de terminer la session de formation',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    }
  }

  return (
    <Box p={5} borderWidth="1px" borderRadius="lg" bg="gray.800" height="70vh" display="flex" flexDirection="column">
      <Flex justifyContent="space-between" alignItems="center" mb={4}>
        <HStack>
          <Text fontWeight="bold">Conversation</Text>
          <Badge colorScheme="blue">{modelName}</Badge>
        </HStack>
        <HStack>
          <Tooltip label="Rafraîchir l'affichage de la conversation (ne modifie pas la mémoire)">
            <IconButton
              icon={<FiRefreshCw />}
              aria-label="Rafraîchir l'affichage de la conversation"
              size="sm"
              onClick={refreshConversation}
              isDisabled={messages.length === 0}
              colorScheme="green"
              mr={2}
            />
          </Tooltip>
          <Tooltip label="Lancer une session de formation aléatoire">
            <IconButton
              icon={<FiZap />}
              aria-label="Lancer formation"
              size="sm"
              onClick={launchTraining}
              colorScheme="purple"
            />
          </Tooltip>
          <Tooltip label="Copier la conversation">
            <IconButton
              icon={<FiCopy />}
              aria-label="Copier la conversation"
              size="sm"
              onClick={copyConversation}
              isDisabled={messages.length === 0}
            />
          </Tooltip>
          <Tooltip label="Effacer la conversation">
            <IconButton
              icon={<FiTrash2 />}
              aria-label="Effacer la conversation"
              size="sm"
              onClick={clearChat}
              isDisabled={messages.length === 0}
            />
          </Tooltip>
          <Menu>
            <Tooltip label="Paramètres du modèle">
              <MenuButton
                as={IconButton}
                icon={<FiSettings />}
                aria-label="Paramètres du modèle"
                size="sm"
              />
            </Tooltip>
            <MenuList bg="gray.700">
              <MenuItem bg="gray.700" _hover={{ bg: 'gray.600' }}>
                <VStack align="start" width="100%">
                  <Text fontSize="sm">Température: {temperature}</Text>
                  <Flex width="100%" alignItems="center">
                    <Text fontSize="xs" mr={2}>0</Text>
                    <Input
                      type="range"
                      min="0"
                      max="1"
                      step="0.1"
                      value={temperature}
                      onChange={(e) => setTemperature(parseFloat(e.target.value))}
                      flex="1"
                    />
                    <Text fontSize="xs" ml={2}>1</Text>
                  </Flex>
                </VStack>
              </MenuItem>
            </MenuList>
          </Menu>
        </HStack>
      </Flex>

      <Divider mb={4} />

      <VStack
        flex="1"
        overflowY="auto"
        spacing={4}
        align="stretch"
        css={{
          '&::-webkit-scrollbar': {
            width: '8px',
          },
          '&::-webkit-scrollbar-track': {
            background: '#2D3748',
          },
          '&::-webkit-scrollbar-thumb': {
            background: '#4A5568',
            borderRadius: '4px',
          },
        }}
      >
        {messages.length === 0 ? (
          <Flex
            direction="column"
            alignItems="center"
            justifyContent="center"
            height="100%"
            color="gray.500"
          >
            <Text mb={2}>Commencez une conversation avec DeepSeek r1</Text>
            <Text fontSize="sm">Posez une question ou demandez de l'aide pour une tâche</Text>
          </Flex>
        ) : (
          messages.map((message, index) => (
            <MessageItem key={index} message={message} />
          ))
        )}
        <div ref={endOfMessagesRef} />
      </VStack>

      <Divider mt={4} />

      <Box mt={4}>
        <Flex>
          <Textarea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Tapez votre message ici..."
            resize="none"
            rows={2}
            bg="gray.700"
            mr={2}
            disabled={isLoading}
          />
          <Button
            colorScheme="blue"
            onClick={handleSendMessage}
            isDisabled={!input.trim() || isLoading}
            alignSelf="flex-end"
            height="40px"
          >
            {isLoading ? <Spinner size="sm" /> : <FiSend />}
          </Button>
        </Flex>
      </Box>
    </Box>
  )
}

export default ChatInterface
