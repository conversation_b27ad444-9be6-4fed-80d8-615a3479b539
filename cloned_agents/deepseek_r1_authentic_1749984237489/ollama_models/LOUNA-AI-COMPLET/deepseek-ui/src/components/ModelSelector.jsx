import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  Select,
  Text,
  Badge,
  Spinner,
  HStack,
  Tooltip,
  IconButton,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  Modal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Input,
} from '@chakra-ui/react'
import { FiDownload, FiRefreshCw, FiInfo } from 'react-icons/fi'
import { useState } from 'react'

const DEEPSEEK_MODELS = [
  { name: 'deepseek-r1:1.5b', description: 'DeepSeek R1 Distill Qwen 1.5B (1.1 Go)' },
  { name: 'deepseek-r1:7b', description: 'DeepSeek R1 Distill Qwen 7B (4.7 Go)' },
  { name: 'deepseek-r1:8b', description: 'DeepSeek R1 Distill Llama 8B (4.9 Go)' },
  { name: 'deepseek-r1:14b', description: 'DeepSeek R1 Distill Qwen 14B (9.0 Go)' },
  { name: 'deepseek-r1:32b', description: 'DeepSeek R1 Distill Qwen 32B (20 Go)' },
  { name: 'deepseek-r1:70b', description: 'DeepSeek R1 Distill Llama 70B (43 Go)' },
  { name: 'deepseek-r1:671b', description: 'DeepSeek R1 Original 671B (404 Go)' },
]

const ModelSelector = ({ 
  availableModels, 
  selectedModel, 
  setSelectedModel, 
  onModelDownload,
  isModelLoading 
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure()
  const [customModelName, setCustomModelName] = useState('')

  // Filtrer les modèles disponibles pour trouver les modèles deepseek-r1
  const installedDeepseekModels = availableModels.filter(model => 
    model.name.includes('deepseek-r1')
  )

  // Trouver les modèles deepseek-r1 qui ne sont pas encore installés
  const notInstalledDeepseekModels = DEEPSEEK_MODELS.filter(model => 
    !installedDeepseekModels.some(installed => installed.name === model.name)
  )

  const handleModelChange = (e) => {
    setSelectedModel(e.target.value)
  }

  const handleDownloadModel = () => {
    if (customModelName) {
      onModelDownload(customModelName)
      onClose()
    }
  }

  return (
    <Box p={5} borderWidth="1px" borderRadius="lg" bg="gray.800">
      <FormControl>
        <FormLabel fontWeight="bold">Sélectionner un modèle</FormLabel>
        <Flex direction={{ base: 'column', md: 'row' }} gap={4}>
          <Select 
            value={selectedModel} 
            onChange={handleModelChange}
            placeholder="Sélectionner un modèle"
            flex="1"
            bg="gray.700"
          >
            {installedDeepseekModels.map((model) => (
              <option key={model.name} value={model.name}>
                {model.name}
              </option>
            ))}
          </Select>
          <HStack>
            <Tooltip label="Télécharger un modèle DeepSeek r1">
              <Button 
                leftIcon={<FiDownload />} 
                colorScheme="blue" 
                onClick={onOpen}
                isDisabled={isModelLoading}
              >
                Télécharger
              </Button>
            </Tooltip>
          </HStack>
        </Flex>
      </FormControl>

      {selectedModel && (
        <Flex mt={4} alignItems="center">
          <Text fontSize="sm" color="gray.400" mr={2}>
            Modèle sélectionné:
          </Text>
          <Badge colorScheme="green" fontSize="0.8em" p={1}>
            {selectedModel}
          </Badge>
          <Tooltip label="Les modèles plus grands offrent de meilleures performances mais nécessitent plus de ressources">
            <IconButton
              icon={<FiInfo />}
              size="sm"
              variant="ghost"
              ml={2}
              aria-label="Informations sur le modèle"
            />
          </Tooltip>
        </Flex>
      )}

      {/* Modal pour télécharger un modèle */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent bg="gray.800">
          <ModalHeader>Télécharger un modèle DeepSeek r1</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Text mb={4}>
              Sélectionnez un modèle à télécharger. Les modèles plus grands offrent de meilleures performances mais nécessitent plus de ressources.
            </Text>
            
            {notInstalledDeepseekModels.length > 0 ? (
              <>
                <Text fontWeight="bold" mb={2}>Modèles disponibles :</Text>
                {notInstalledDeepseekModels.map((model) => (
                  <Button
                    key={model.name}
                    onClick={() => onModelDownload(model.name)}
                    colorScheme="blue"
                    variant="outline"
                    mb={2}
                    mr={2}
                    isDisabled={isModelLoading}
                    width="100%"
                    justifyContent="space-between"
                  >
                    <Text>{model.name}</Text>
                    <Text fontSize="xs" color="gray.400">{model.description}</Text>
                  </Button>
                ))}
              </>
            ) : (
              <Text>Tous les modèles DeepSeek r1 sont déjà installés.</Text>
            )}
            
            <Text fontWeight="bold" mt={4} mb={2}>Ou spécifiez un nom de modèle personnalisé :</Text>
            <Input
              placeholder="Exemple: deepseek-r1:7b"
              value={customModelName}
              onChange={(e) => setCustomModelName(e.target.value)}
              bg="gray.700"
            />
          </ModalBody>

          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onClose}>
              Annuler
            </Button>
            <Button 
              colorScheme="blue" 
              onClick={handleDownloadModel}
              isDisabled={!customModelName || isModelLoading}
              leftIcon={isModelLoading ? <Spinner size="sm" /> : <FiDownload />}
            >
              {isModelLoading ? 'Téléchargement...' : 'Télécharger'}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  )
}

export default ModelSelector
