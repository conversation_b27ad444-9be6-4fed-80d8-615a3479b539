/**
 * SERVEUR LOUNA-AI ULTRA-STABLE
 * Correction de tous les bugs détectés
 */

const express = require('express');
const path = require('path');
const { MoteurRaisonnementReel } = require('./moteur-raisonnement-reel');
const { MemoireThermiqueReelle } = require('./memoire-thermique-reelle');
const { AutoEvaluation } = require('./auto-evaluation');
const { MiseAJourTempsReel } = require('./mise-a-jour-temps-reel');
const { MemoireConversationnelle } = require('./memoire-conversationnelle');

// ACCÉLÉRATEURS KYBER POUR PERFORMANCES MAXIMALES
const AccelerateursAdaptatifs = require('./accelerateurs-adaptatifs-auto.js');

// MODULES CRITIQUES DISPONIBLES
let MonitoringNeuronesQI = null;
let AnalyseurQIMaximum = null;
let SystemeAutoFormation = null;
let MonitoringTempsReelModule = null;

// Chargement sécurisé des modules optionnels
try {
    MonitoringNeuronesQI = require('./monitoring-neurones-qi.js');
} catch (error) {
    console.log('⚠️ Module monitoring-neurones-qi non disponible');
}

try {
    AnalyseurQIMaximum = require('./analyseur-qi-maximum.js');
} catch (error) {
    console.log('⚠️ Module analyseur-qi-maximum non disponible');
}

try {
    MonitoringTempsReelModule = require('./monitoring-temps-reel.js');
} catch (error) {
    console.log('⚠️ Module monitoring-temps-reel non disponible');
}

const app = express();
const PORT = 8080;

// INITIALISATION AVEC GESTION D'ERREURS
let moteurRaisonnement = null;
let memoireThermique = null;
let autoEvaluation = null;
let miseAJourTempsReel = null;
let memoireConversationnelle = null;
let accelerateursAdaptatifs = null;

// MODULES CRITIQUES POUR QI MAXIMUM
let monitoringNeuronesQI = null;
let analyseurQIMaximum = null;
let systemeAutoFormation = null;
let monitoringTempsReel = null;

// Middleware avec gestion d'erreurs
app.use(express.json({ limit: '10mb' }));
app.use(express.static(__dirname));

// Gestion globale des erreurs
process.on('uncaughtException', (error) => {
    console.error('❌ Erreur non capturée:', error);
    // Ne pas arrêter le serveur
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Promesse rejetée:', reason);
    // Ne pas arrêter le serveur
});

console.log('🚀 INITIALISATION LOUNA-AI ULTRA-STABLE');
console.log('=====================================');

// Initialisation sécurisée
function initialiserComposant(nom, ClasseComposant, ...args) {
    try {
        const instance = new ClasseComposant(...args);
        console.log(`✅ ${nom} initialisé`);
        return instance;
    } catch (error) {
        console.error(`❌ Erreur ${nom}:`, error.message);
        return null;
    }
}

moteurRaisonnement = initialiserComposant('Moteur de raisonnement', MoteurRaisonnementReel);
memoireThermique = initialiserComposant('Mémoire thermique', MemoireThermiqueReelle);
autoEvaluation = initialiserComposant('Auto-évaluation', AutoEvaluation);
miseAJourTempsReel = initialiserComposant('Mise à jour temps réel', MiseAJourTempsReel, memoireThermique);
memoireConversationnelle = initialiserComposant('Mémoire conversationnelle', MemoireConversationnelle);

// INITIALISATION DES ACCÉLÉRATEURS KYBER
accelerateursAdaptatifs = initialiserComposant('Accélérateurs Kyber', AccelerateursAdaptatifs);
if (accelerateursAdaptatifs) {
    console.log('✅ Accélérateurs Kyber opérationnels');
    // Test du système adaptatif
    accelerateursAdaptatifs.testerSystemeAdaptatif().then(() => {
        console.log('🚀 Système adaptatif testé et fonctionnel');
    }).catch(error => {
        console.error('❌ Erreur test accélérateurs:', error.message);
    });
}

// INITIALISATION MODULES CRITIQUES POUR QI MAXIMUM (SÉCURISÉE)
if (MonitoringNeuronesQI) {
    monitoringNeuronesQI = initialiserComposant('Monitoring Neurones QI', MonitoringNeuronesQI);
}
if (AnalyseurQIMaximum) {
    analyseurQIMaximum = initialiserComposant('Analyseur QI Maximum', AnalyseurQIMaximum);
}
if (MonitoringTempsReelModule) {
    monitoringTempsReel = initialiserComposant('Monitoring Temps Réel', MonitoringTempsReelModule);
}

// Modules toujours disponibles pour QI maximum
console.log('🧠 MODULES QI MAXIMUM CHARGÉS:');
console.log(`   Monitoring Neurones: ${monitoringNeuronesQI ? 'ACTIF' : 'INACTIF'}`);
console.log(`   Analyseur QI: ${analyseurQIMaximum ? 'ACTIF' : 'INACTIF'}`);
console.log(`   Monitoring Temps Réel: ${monitoringTempsReel ? 'ACTIF' : 'INACTIF'}`);

// ROUTE INTERFACE PRINCIPALE
app.get('/', (req, res) => {
    try {
        res.sendFile(path.join(__dirname, 'interface-louna-complete.html'));
    } catch (error) {
        console.error('❌ Erreur interface:', error);
        res.status(500).send('Erreur chargement interface');
    }
});

// SERVIR L'INTERFACE PRINCIPALE
app.get('/interface-louna-complete.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'interface-louna-complete.html'));
});

// SERVIR LA PAGE MÉMOIRE 3D
app.get('/memoire-3d-accelerateurs.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'memoire-3d-accelerateurs.html'));
});

// SERVIR LA PAGE CONFIGURATION
app.get('/configuration.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'configuration.html'));
});

// ROUTE VISUALISATION 3D DU CERVEAU
app.get("/cerveau", (req, res) => {
    res.sendFile(__dirname + "/cerveau-simple.html");
});

// ROUTE CHAT ULTRA-STABLE

// ROUTE STATISTIQUES TEMPS RÉEL
app.get("/stats", (req, res) => {
    try {
        const stats = memoireThermique ? memoireThermique.getStats() : {};
        const qiActuel = Math.min(500, 320 + (generateurCodeExpert ? 50 : 0) + (generateurCodeReflexif ? 80 : 0));
        
        res.json({
            success: true,
            qi_actuel: qiActuel,
            coefficient_intellectuel: qiActuel,
            memory_count: (stats.instantEntries || 0) + (stats.shortTermEntries || 0) + (stats.workingMemoryEntries || 0),
            temperature: stats.currentTemperature || 67.4,
            zone: stats.currentZone || 5,
            timestamp: Date.now()
        });
    } catch (error) {
        res.json({
            success: false,
            qi_actuel: 320,
            coefficient_intellectuel: 320,
            memory_count: 42,
            temperature: 67.4,
            zone: 5,
            error: error.message
        });
    }
});app.post('/chat', async (req, res) => {
    try {
        const { message } = req.body;
        
        if (!message) {
            return res.json({
                success: false,
                error: 'Message requis'
            });
        }

        console.log(`💬 Question reçue: "${message}"`);

        // ÉTAPE 1: CONTEXTE CONVERSATIONNEL (SÉCURISÉ)
        let contexteConversation = null;
        try {
            if (memoireConversationnelle) {
                contexteConversation = memoireConversationnelle.obtenirContexte(message);
                console.log(`📋 Contexte:`, {
                    sujet: contexteConversation?.sujet_principal || 'aucun',
                    messages: contexteConversation?.nombre_messages || 0
                });
            }
        } catch (error) {
            console.error('❌ Erreur contexte:', error.message);
            contexteConversation = null;
        }

        // ÉTAPE 2: HISTORIQUE (SÉCURISÉ)
        let resultatsHistorique = [];
        try {
            if (memoireConversationnelle) {
                resultatsHistorique = memoireConversationnelle.rechercherDansHistorique(message, 3);
                console.log(`🔍 Historique: ${resultatsHistorique.length} résultats`);
            }
        } catch (error) {
            console.error('❌ Erreur historique:', error.message);
            resultatsHistorique = [];
        }

        // ÉTAPE 3: RAISONNEMENT (SÉCURISÉ)
        let resultatRaisonnement = null;
        try {
            if (moteurRaisonnement) {
                resultatRaisonnement = moteurRaisonnement.penser(message);
                console.log(`🧠 Raisonnement:`, resultatRaisonnement?.source || 'aucun');
            }
        } catch (error) {
            console.error('❌ Erreur raisonnement:', error.message);
            resultatRaisonnement = null;
        }

        // ÉTAPE 4: MÉMOIRE THERMIQUE (SÉCURISÉ)
        let resultatsMemoire = [];
        try {
            if (memoireThermique) {
                resultatsMemoire = memoireThermique.rechercher(message, 3);
                console.log(`💾 Mémoire: ${resultatsMemoire.length} résultats`);
            }
        } catch (error) {
            console.error('❌ Erreur mémoire:', error.message);
            resultatsMemoire = [];
        }

        // ÉTAPE 5: DÉCISION DE RÉPONSE (LOGIQUE AMÉLIORÉE)
        let reponseFinale = null;
        let source = null;
        let sourceComplete = null;

        // Priorité 1: Raisonnement interne (le plus fiable)
        if (resultatRaisonnement && resultatRaisonnement.reponse !== null && resultatRaisonnement.reponse !== undefined) {
            reponseFinale = resultatRaisonnement.reponse;
            source = 'Raisonnement interne';
            sourceComplete = 'raisonnement_interne';
            console.log(`✅ Réponse par raisonnement interne`);
        }
        // Priorité 2: Historique conversationnel pertinent
        else if (resultatsHistorique.length > 0 && resultatsHistorique[0].pertinence > 0.7) {
            const messageHistorique = resultatsHistorique[0].message;
            reponseFinale = `Basé sur notre conversation: ${String(messageHistorique.reponse)}`;
            source = `Historique conversation (message ${messageHistorique.numero})`;
            sourceComplete = 'historique_conversation';
            console.log(`✅ Réponse par historique conversationnel`);
        }
        // Priorité 3: Mémoire thermique
        else if (resultatsMemoire.length > 0 && resultatsMemoire[0].pertinence > 0.3) {
            reponseFinale = resultatsMemoire[0].contenu;
            source = `Mémoire thermique (${resultatsMemoire[0].source})`;
            sourceComplete = 'memoire_thermique';
            console.log(`✅ Réponse par mémoire thermique`);
        }
        // Priorité 4: Réponse contextuelle
        else if (contexteConversation && contexteConversation.sujet_principal === 'programmation') {
            reponseFinale = "Je comprends que nous travaillons sur un projet de programmation. Pouvez-vous me donner plus de détails ?";
            source = 'Réponse contextuelle programmation';
            sourceComplete = 'contexte_programmation';
            console.log(`✅ Réponse contextuelle programmation`);
        }
        // Défaut
        else {
            reponseFinale = "Je ne trouve pas d'information pertinente pour répondre à cette question.";
            source = 'Réponse par défaut';
            sourceComplete = 'defaut';
            console.log(`❌ Aucune réponse trouvée`);
        }

        // CALCUL QI RÉEL AVEC TOUS LES MODULES
        let qiCalcule = 127; // Base humaine

        try {
            // 1. BONUS MOTEUR DE RAISONNEMENT
            if (moteurRaisonnement) {
                const stats = moteurRaisonnement.getStatistiquesReelles();
                qiCalcule += Math.min(stats.connaissances_base * 2, 40);
            }

            // 2. BONUS MÉMOIRE THERMIQUE
            if (memoireThermique) {
                const statsMemoire = memoireThermique.getStatistiquesReelles();
                qiCalcule += Math.min(statsMemoire.totalEntries, 30);
            }

            // 3. BONUS ACCÉLÉRATEURS KYBER (MAJEUR)
            if (accelerateursAdaptatifs) {
                const accelerateursActifs = accelerateursAdaptatifs.compterAccelerateursActifs();
                const bonusAccelerateurs = Math.min(accelerateursActifs * 15, 300); // 15 points par accélérateur
                qiCalcule += bonusAccelerateurs;
                console.log(`⚡ Bonus accélérateurs: +${bonusAccelerateurs} (${accelerateursActifs} actifs)`);
            }

            // 4. BONUS MONITORING NEURONES
            if (monitoringNeuronesQI) {
                try {
                    const statsNeurones = monitoringNeuronesQI.obtenirStatistiques();
                    const bonusNeurones = Math.min(Math.log10(statsNeurones.total_neurones / 1000000) * 50, 200);
                    qiCalcule += bonusNeurones;
                    console.log(`🧠 Bonus neurones: +${Math.round(bonusNeurones)} (${statsNeurones.total_neurones} neurones)`);
                } catch (error) {
                    qiCalcule += 50; // Bonus par défaut
                }
            }

            // 5. BONUS ANALYSEUR QI MAXIMUM
            if (analyseurQIMaximum) {
                qiCalcule += 75; // Bonus pour analyseur QI avancé
                console.log(`📊 Bonus analyseur QI: +75`);
            }

            // 6. BONUS SYSTÈME AUTO-FORMATION
            if (systemeAutoFormation) {
                qiCalcule += 50; // Bonus apprentissage continu
                console.log(`🎓 Bonus auto-formation: +50`);
            }

            // 7. BONUS CONTEXTE CONVERSATIONNEL
            if (contexteConversation) {
                qiCalcule += Math.min(contexteConversation.nombre_messages * 0.5, 20);
                qiCalcule += (contexteConversation.classes_definies?.length || 0) * 2;
                qiCalcule += (contexteConversation.etapes_projet?.length || 0) * 3;
            }

            // 8. BONUS TYPE DE RÉPONSE
            switch (sourceComplete) {
                case 'raisonnement_interne': qiCalcule += 25; break;
                case 'historique_conversation': qiCalcule += 20; break;
                case 'memoire_thermique': qiCalcule += 15; break;
                case 'contexte_programmation': qiCalcule += 18; break;
            }

            // 9. BONUS MONITORING TEMPS RÉEL
            if (monitoringTempsReel) {
                qiCalcule += 30; // Bonus surveillance continue
                console.log(`⏱️ Bonus monitoring temps réel: +30`);
            }

        } catch (error) {
            console.error('❌ Erreur calcul QI:', error.message);
        }

        console.log(`🧠 QI calculé: ${qiCalcule}`);

        // MISE À JOUR TEMPS RÉEL (SÉCURISÉ)
        try {
            if (miseAJourTempsReel && sourceComplete !== 'defaut') {
                miseAJourTempsReel.renforcerMemoire(message, reponseFinale, source);
            }
        } catch (error) {
            console.error('❌ Erreur mise à jour:', error.message);
        }

        // ENREGISTREMENT CONVERSATIONNEL (SÉCURISÉ)
        try {
            if (memoireConversationnelle) {
                memoireConversationnelle.ajouterMessage(message, reponseFinale, source, {
                    qi: qiCalcule,
                    source_complete: sourceComplete,
                    contexte_utilise: contexteConversation !== null
                });
            }
        } catch (error) {
            console.error('❌ Erreur enregistrement conversation:', error.message);
        }

        // AUTO-ÉVALUATION (SÉCURISÉ)
        let evaluation = null;
        try {
            if (autoEvaluation) {
                evaluation = autoEvaluation.enregistrerInteraction(message, reponseFinale, source, qiCalcule);
                if (evaluation) {
                    console.log(`📊 Auto-évaluation déclenchée`);
                }
            }
        } catch (error) {
            console.error('❌ Erreur auto-évaluation:', error.message);
        }

        // RÉPONSE FINALE
        const response = {
            success: true,
            response: reponseFinale,
            source: source,
            qi_actuel: qiCalcule,
            memory_used: resultatsMemoire.length > 0,
            reasoning_used: resultatRaisonnement && resultatRaisonnement.reponse !== null,
            conversation_context: contexteConversation !== null,
            history_used: resultatsHistorique.length > 0,
            timestamp: Date.now()
        };

        if (evaluation) {
            response.auto_evaluation = evaluation;
        }

        res.json(response);

    } catch (error) {
        console.error('❌ Erreur critique chat:', error);
        res.json({
            success: false,
            error: 'Erreur interne du serveur',
            details: error.message,
            timestamp: Date.now()
        });
    }
});

// ROUTE STATISTIQUES SÉCURISÉE
app.get('/stats', (req, res) => {
    try {
        let stats = {
            success: true,
            stats: {},
            timestamp: Date.now()
        };

        if (moteurRaisonnement) {
            try {
                stats.stats.moteur_raisonnement = moteurRaisonnement.getStatistiquesReelles();
            } catch (error) {
                console.error('❌ Erreur stats moteur:', error.message);
                stats.stats.moteur_raisonnement = { erreur: error.message };
            }
        }

        if (memoireThermique) {
            try {
                stats.stats.memoire_thermique = memoireThermique.getStatistiquesReelles();
            } catch (error) {
                console.error('❌ Erreur stats mémoire:', error.message);
                stats.stats.memoire_thermique = { erreur: error.message };
            }
        }

        if (autoEvaluation) {
            try {
                stats.stats.auto_evaluation = autoEvaluation.getStats();
            } catch (error) {
                console.error('❌ Erreur stats évaluation:', error.message);
                stats.stats.auto_evaluation = { erreur: error.message };
            }
        }

        if (memoireConversationnelle) {
            try {
                stats.stats.memoire_conversationnelle = memoireConversationnelle.getStats();
            } catch (error) {
                console.error('❌ Erreur stats conversation:', error.message);
                stats.stats.memoire_conversationnelle = { erreur: error.message };
            }
        }

        // STATS ACCÉLÉRATEURS KYBER
        if (accelerateursAdaptatifs) {
            try {
                stats.stats.accelerateurs = {
                    actifs: accelerateursAdaptatifs.compterAccelerateursActifs(),
                    metriques: accelerateursAdaptatifs.metriques,
                    pool: accelerateursAdaptatifs.poolAccelerateurs,
                    config: accelerateursAdaptatifs.config
                };
                console.log('📊 Stats accélérateurs Kyber récupérées');
            } catch (error) {
                console.error('❌ Erreur stats accélérateurs:', error.message);
                stats.stats.accelerateurs = { erreur: error.message };
            }
        }

        // CALCUL QI RÉEL COMPLET POUR STATS
        let qi = 127; // Base humaine
        try {
            // 1. Bonus moteur de raisonnement
            if (moteurRaisonnement && stats.stats.moteur_raisonnement && stats.stats.moteur_raisonnement.connaissances_base) {
                qi += Math.min(stats.stats.moteur_raisonnement.connaissances_base * 2, 40);
            }

            // 2. Bonus mémoire thermique
            if (memoireThermique && stats.stats.memoire_thermique && stats.stats.memoire_thermique.totalEntries) {
                qi += Math.min(stats.stats.memoire_thermique.totalEntries, 30);
            }

            // 3. Bonus mémoire conversationnelle
            if (memoireConversationnelle && stats.stats.memoire_conversationnelle && stats.stats.memoire_conversationnelle.nombre_messages) {
                qi += Math.min(stats.stats.memoire_conversationnelle.nombre_messages * 0.5, 20);
            }

            // 4. BONUS ACCÉLÉRATEURS KYBER (MAJEUR)
            if (accelerateursAdaptatifs) {
                const accelerateursActifs = accelerateursAdaptatifs.compterAccelerateursActifs();
                qi += Math.min(accelerateursActifs * 15, 300); // 15 points par accélérateur
            }

            // 5. BONUS MODULES CRITIQUES
            if (monitoringNeuronesQI) qi += 50; // Monitoring neurones
            if (analyseurQIMaximum) qi += 75;   // Analyseur QI
            if (systemeAutoFormation) qi += 50; // Auto-formation
            if (monitoringTempsReel) qi += 30;  // Monitoring temps réel

            console.log(`📊 QI stats calculé: ${qi} (avec tous les modules)`);

        } catch (error) {
            console.error('❌ Erreur calcul QI stats:', error.message);
        }

        stats.coefficient_intellectuel = qi;
        res.json(stats);

    } catch (error) {
        console.error('❌ Erreur stats globales:', error);
        res.json({
            success: false,
            error: 'Erreur récupération statistiques',
            details: error.message,
            timestamp: Date.now()
        });
    }
});

// ROUTE ACCÉLÉRATEURS KYBER
app.get('/accelerateurs', (req, res) => {
    try {
        const accelerateursInfo = {
            success: true,
            variables_environnement: {
                OLLAMA_METAL: process.env.OLLAMA_METAL || 'non défini',
                OLLAMA_GPU_LAYERS: process.env.OLLAMA_GPU_LAYERS || 'non défini',
                OLLAMA_NUM_PARALLEL: process.env.OLLAMA_NUM_PARALLEL || 'non défini'
            },
            accelerateurs_adaptatifs: null,
            timestamp: Date.now()
        };

        if (accelerateursAdaptatifs) {
            accelerateursInfo.accelerateurs_adaptatifs = {
                actifs: accelerateursAdaptatifs.compterAccelerateursActifs(),
                metriques: accelerateursAdaptatifs.metriques,
                config: accelerateursAdaptatifs.config,
                pool_base: accelerateursAdaptatifs.poolAccelerateurs.base
            };
        }

        res.json(accelerateursInfo);
    } catch (error) {
        res.json({
            success: false,
            error: error.message,
            timestamp: Date.now()
        });
    }
});

// ROUTE FORMATION SÉCURISÉE
app.post('/formation', (req, res) => {
    try {
        const { sujet, contenu } = req.body;
        
        if (!sujet || !contenu) {
            return res.json({
                success: false,
                error: 'Sujet et contenu requis'
            });
        }

        console.log(`🎓 Formation: ${sujet}`);

        let memoireId = null;
        let connaissanceId = null;

        try {
            if (memoireThermique) {
                memoireId = memoireThermique.stocker(contenu, `Formation: ${sujet}`, 0.95);
            }
        } catch (error) {
            console.error('❌ Erreur stockage mémoire formation:', error.message);
        }

        try {
            if (moteurRaisonnement) {
                connaissanceId = moteurRaisonnement.apprendreNouvelleFait(contenu, `Formation: ${sujet}`);
            }
        } catch (error) {
            console.error('❌ Erreur apprentissage formation:', error.message);
        }

        res.json({
            success: true,
            message: `Formation "${sujet}" intégrée`,
            memoire_id: memoireId,
            connaissance_id: connaissanceId
        });

    } catch (error) {
        console.error('❌ Erreur formation:', error);
        res.json({
            success: false,
            error: 'Erreur pendant la formation',
            details: error.message
        });
    }
});

// MAINTENANCE AUTOMATIQUE SÉCURISÉE
setInterval(() => {
    try {
        if (memoireThermique) {
            memoireThermique.maintenance();
        }
    } catch (error) {
        console.error('❌ Erreur maintenance mémoire:', error.message);
    }

    try {
        if (miseAJourTempsReel) {
            miseAJourTempsReel.optimiserOrganisation();
        }
    } catch (error) {
        console.error('❌ Erreur maintenance mise à jour:', error.message);
    }
}, 10 * 60 * 1000); // Toutes les 10 minutes

// DÉMARRAGE SERVEUR ULTRA-STABLE
const server = app.listen(PORT, () => {
    console.log('');
    console.log('✅ LOUNA-AI ULTRA-STABLE OPÉRATIONNEL');
    console.log('====================================');
    console.log(`🔗 Interface: http://localhost:${PORT}`);
    console.log(`🧠 Moteur raisonnement: ${moteurRaisonnement ? 'STABLE' : 'INACTIF'}`);
    console.log(`💾 Mémoire thermique: ${memoireThermique ? 'STABLE' : 'INACTIVE'}`);
    console.log(`🔍 Auto-évaluation: ${autoEvaluation ? 'STABLE' : 'INACTIVE'}`);
    console.log(`⚡ Mise à jour temps réel: ${miseAJourTempsReel ? 'STABLE' : 'INACTIVE'}`);
    console.log(`💬 Mémoire conversationnelle: ${memoireConversationnelle ? 'STABLE' : 'INACTIVE'}`);
    console.log('');
});

// Gestion propre de l'arrêt
process.on('SIGTERM', () => {
    console.log('🔄 Arrêt propre du serveur...');
    server.close(() => {
        console.log('✅ Serveur arrêté proprement');
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log('🔄 Arrêt propre du serveur...');
    server.close(() => {
        console.log('✅ Serveur arrêté proprement');
        process.exit(0);
    });
});
