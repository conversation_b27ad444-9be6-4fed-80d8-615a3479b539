/**
 * SERVEUR LOUNA-AI EXPERT COMPLET
 * Avec auto-évaluation, mise à jour temps réel, et toutes les capacités
 */

const express = require('express');
const path = require('path');
const { MoteurRaisonnementReel } = require('./moteur-raisonnement-reel');
const { MemoireThermiqueReelle } = require('./memoire-thermique-reelle');
const { AutoEvaluation } = require('./auto-evaluation');
const { MiseAJourTempsReel } = require('./mise-a-jour-temps-reel');

const app = express();
const PORT = 8080;

// INITIALISATION DES SYSTÈMES EXPERTS
let moteurRaisonnement = null;
let memoireThermique = null;
let autoEvaluation = null;
let miseAJourTempsReel = null;

// Middleware
app.use(express.json());
app.use(express.static(__dirname));

console.log('🚀 INITIALISATION LOUNA-AI EXPERT COMPLET');
console.log('==========================================');

// Initialisation du moteur de raisonnement
try {
    moteurRaisonnement = new MoteurRaisonnementReel();
    console.log('🧠 Moteur de raisonnement expert initialisé');
} catch (error) {
    console.error('❌ Erreur moteur raisonnement:', error.message);
}

// Initialisation de la mémoire thermique
try {
    memoireThermique = new MemoireThermiqueReelle();
    console.log('💾 Mémoire thermique experte initialisée');
} catch (error) {
    console.error('❌ Erreur mémoire thermique:', error.message);
}

// Initialisation auto-évaluation
try {
    autoEvaluation = new AutoEvaluation();
    console.log('🔍 Système d\'auto-évaluation initialisé');
} catch (error) {
    console.error('❌ Erreur auto-évaluation:', error.message);
}

// Initialisation mise à jour temps réel
try {
    miseAJourTempsReel = new MiseAJourTempsReel(memoireThermique);
    console.log('⚡ Système de mise à jour temps réel initialisé');
} catch (error) {
    console.error('❌ Erreur mise à jour temps réel:', error.message);
}

// ROUTE INTERFACE PRINCIPALE
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'interface-louna-grande.html'));
});

// ROUTE CHAT EXPERT AVEC TOUS LES SYSTÈMES
app.post('/chat', async (req, res) => {
    try {
        const { message } = req.body;
        
        if (!message) {
            return res.json({
                success: false,
                error: 'Message requis'
            });
        }

        console.log(`💬 Question reçue: "${message}"`);

        // ÉTAPE 1: RAISONNEMENT INTERNE
        let resultatRaisonnement = null;
        if (moteurRaisonnement) {
            resultatRaisonnement = moteurRaisonnement.penser(message);
            console.log(`🧠 Processus de pensée:`, resultatRaisonnement.processus);
        }

        // ÉTAPE 2: RECHERCHE MÉMOIRE THERMIQUE
        let resultatsMemoire = [];
        if (memoireThermique) {
            resultatsMemoire = memoireThermique.rechercher(message, 3);
            console.log(`💾 Résultats mémoire: ${resultatsMemoire.length}`);
        }

        // ÉTAPE 3: DÉCISION DE RÉPONSE
        let reponseFinale = null;
        let source = null;
        let sourceComplete = null;

        if (resultatRaisonnement && resultatRaisonnement.reponse) {
            reponseFinale = resultatRaisonnement.reponse;
            source = 'Raisonnement interne';
            sourceComplete = 'raisonnement_interne';
            console.log(`✅ Réponse par raisonnement interne`);
        }
        else if (resultatsMemoire.length > 0 && resultatsMemoire[0].pertinence > 0.3) {
            reponseFinale = resultatsMemoire[0].contenu;
            source = `Mémoire thermique (${resultatsMemoire[0].source})`;
            sourceComplete = 'memoire_thermique';
            console.log(`✅ Réponse par mémoire thermique`);
        }
        else {
            reponseFinale = "Je ne trouve pas d'information dans ma mémoire ou mes connaissances internes pour répondre à cette question.";
            source = 'Réponse par défaut';
            sourceComplete = 'defaut';
            console.log(`❌ Aucune réponse trouvée`);
        }

        // CALCUL QI EXPERT
        let qiExpert = 127; // Base
        
        if (moteurRaisonnement) {
            const stats = moteurRaisonnement.getStatistiquesReelles();
            qiExpert += Math.min(stats.connaissances_base * 2, 40);
        }
        
        if (memoireThermique) {
            const statsMemoire = memoireThermique.getStatistiquesReelles();
            qiExpert += Math.min(statsMemoire.totalEntries, 30);
        }

        // Bonus pour type de réponse
        if (sourceComplete === 'raisonnement_interne') {
            qiExpert += 15;
        } else if (sourceComplete === 'memoire_thermique') {
            qiExpert += 8;
        }

        console.log(`🧠 QI expert calculé: ${qiExpert}`);

        // MISE À JOUR TEMPS RÉEL
        if (miseAJourTempsReel && sourceComplete !== 'defaut') {
            miseAJourTempsReel.renforcerMemoire(message, reponseFinale, source);
        }

        // AUTO-ÉVALUATION PÉRIODIQUE
        let evaluation = null;
        if (autoEvaluation) {
            evaluation = autoEvaluation.enregistrerInteraction(message, reponseFinale, source, qiExpert);
            if (evaluation) {
                console.log(`📊 Auto-évaluation déclenchée:`, evaluation);
            }
        }

        // RÉPONSE FINALE EXPERTE
        const response = {
            success: true,
            response: reponseFinale,
            source: source,
            qi_actuel: qiExpert,
            memory_used: resultatsMemoire.length > 0,
            reasoning_used: resultatRaisonnement && resultatRaisonnement.reponse !== null,
            timestamp: Date.now()
        };

        // Ajouter évaluation si disponible
        if (evaluation) {
            response.auto_evaluation = evaluation;
        }

        res.json(response);

    } catch (error) {
        console.error('❌ Erreur traitement chat expert:', error);
        res.json({
            success: false,
            error: 'Erreur interne du serveur expert',
            details: error.message
        });
    }
});

// ROUTE STATISTIQUES EXPERTES
app.get('/stats', (req, res) => {
    try {
        let statsExpertes = {
            success: true,
            stats: {}
        };

        // Stats moteur raisonnement
        if (moteurRaisonnement) {
            statsExpertes.stats.moteur_raisonnement = moteurRaisonnement.getStatistiquesReelles();
        }

        // Stats mémoire thermique
        if (memoireThermique) {
            statsExpertes.stats.memoire_thermique = memoireThermique.getStatistiquesReelles();
        }

        // Stats auto-évaluation
        if (autoEvaluation) {
            statsExpertes.stats.auto_evaluation = autoEvaluation.getStats();
        }

        // Stats mise à jour temps réel
        if (miseAJourTempsReel) {
            statsExpertes.stats.mise_a_jour = miseAJourTempsReel.getStats();
        }

        // Calcul QI expert
        let qiExpert = 127;
        if (moteurRaisonnement) {
            qiExpert += Math.min(moteurRaisonnement.getStatistiquesReelles().connaissances_base * 2, 40);
        }
        if (memoireThermique) {
            qiExpert += Math.min(memoireThermique.getStatistiquesReelles().totalEntries, 30);
        }

        statsExpertes.coefficient_intellectuel = qiExpert;
        statsExpertes.timestamp = Date.now();

        res.json(statsExpertes);

    } catch (error) {
        console.error('❌ Erreur stats expertes:', error);
        res.json({
            success: false,
            error: 'Erreur récupération statistiques expertes'
        });
    }
});

// ROUTE FORMATION EXPERTE
app.post('/formation', (req, res) => {
    try {
        const { sujet, contenu } = req.body;
        
        if (!sujet || !contenu) {
            return res.json({
                success: false,
                error: 'Sujet et contenu requis'
            });
        }

        console.log(`🎓 Formation experte: ${sujet}`);

        // Stocker en mémoire thermique avec importance maximale
        let memoireId = null;
        if (memoireThermique) {
            memoireId = memoireThermique.stocker(contenu, `Formation: ${sujet}`, 0.95);
        }

        // Apprendre dans le moteur de raisonnement
        let connaissanceId = null;
        if (moteurRaisonnement) {
            connaissanceId = moteurRaisonnement.apprendreNouvelleFait(contenu, `Formation: ${sujet}`);
        }

        res.json({
            success: true,
            message: `Formation experte "${sujet}" intégrée`,
            memoire_id: memoireId,
            connaissance_id: connaissanceId
        });

    } catch (error) {
        console.error('❌ Erreur formation experte:', error);
        res.json({
            success: false,
            error: 'Erreur pendant la formation experte'
        });
    }
});

// ROUTE CORRECTION D'ERREUR
app.post('/correction', (req, res) => {
    try {
        const { question, erreur, correction } = req.body;
        
        if (!question || !erreur || !correction) {
            return res.json({
                success: false,
                error: 'Question, erreur et correction requis'
            });
        }

        console.log(`🔧 Correction reçue: ${question}`);

        if (miseAJourTempsReel) {
            miseAJourTempsReel.corrigerErreur(question, erreur, correction);
        }

        res.json({
            success: true,
            message: 'Correction intégrée avec succès'
        });

    } catch (error) {
        console.error('❌ Erreur correction:', error);
        res.json({
            success: false,
            error: 'Erreur pendant la correction'
        });
    }
});

// MAINTENANCE AUTOMATIQUE EXPERTE
setInterval(() => {
    if (memoireThermique) {
        memoireThermique.maintenance();
    }
    if (miseAJourTempsReel) {
        miseAJourTempsReel.optimiserOrganisation();
    }
}, 10 * 60 * 1000); // Toutes les 10 minutes

// DÉMARRAGE DU SERVEUR EXPERT
app.listen(PORT, () => {
    console.log('');
    console.log('✅ LOUNA-AI EXPERT COMPLET OPÉRATIONNEL');
    console.log('=======================================');
    console.log(`🔗 Interface: http://localhost:${PORT}`);
    console.log(`🧠 Moteur raisonnement: ${moteurRaisonnement ? 'EXPERT' : 'INACTIF'}`);
    console.log(`💾 Mémoire thermique: ${memoireThermique ? 'EXPERTE' : 'INACTIVE'}`);
    console.log(`🔍 Auto-évaluation: ${autoEvaluation ? 'ACTIVE' : 'INACTIVE'}`);
    console.log(`⚡ Mise à jour temps réel: ${miseAJourTempsReel ? 'ACTIVE' : 'INACTIVE'}`);
    console.log('');
});
