{"memoires": [["mem_1749002251263_ytsipovzx", {"id": "mem_1749002251263_ytsipovzx", "contenu": "SOMMEIL ET CRÉATIVITÉ: Zone 6 (75-85°C) pour innovation intense, liens créatifs, consolidation pendant le sommeil. Zone 1 (25-35°C) pour sommeil léger et consolidation mémoire.", "source": "Formation: Sommeil et créativité", "timestamp": 1749002251263, "importance": 1, "utilisation": 37, "temperature": 84.53172204832401, "zone": 6, "dernierAcces": 1749005705353, "liens": [], "evolution": [{"timestamp": 1749002251263, "temperature": 81.41524283452517, "zone": 6, "curseur": 51.77121211610015, "action": "creation"}, {"timestamp": 1749002256716, "temperature": 83.28926466366102, "zone": 6, "curseur": 54.82735453301853, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749002256718, "temperature": 84.14463233183051, "zone": 6, "curseur": 55.57129248671142, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749002269482, "temperature": 84.85330444490893, "zone": 6, "curseur": 57.087401432327006, "action": "acces_recherche", "pertinence": 1.3}, {"timestamp": 1749002269483, "temperature": 84.92665222245446, "zone": 6, "curseur": 57.29680353485433, "action": "acces_recherche", "pertinence": 1.3}, {"timestamp": 1749002272507, "temperature": 84.96332611122723, "zone": 6, "curseur": 57.47111472117806, "action": "acces_recherche", "pertinence": 1.3}, {"timestamp": 1749002272509, "temperature": 84.98166305561361, "zone": 6, "curseur": 57.7259240323019, "action": "acces_recherche", "pertinence": 1.3}, {"timestamp": 1749002275528, "temperature": 84.99358206946476, "zone": 6, "curseur": 58.19599148784751, "action": "acces_recherche", "pertinence": 1.3}, {"timestamp": 1749002275529, "temperature": 84.99679103473238, "zone": 6, "curseur": 58.34807446301779, "action": "acces_recherche", "pertinence": 1.3}, {"timestamp": 1749002580580, "temperature": 86.20049999999667, "zone": 5, "curseur": 61.29461954751521, "action": "glissement_fluide"}, {"timestamp": 1749002620588, "temperature": 85.0692064360048, "zone": 6, "curseur": 60.085285104857206, "action": "glissement_fluide"}, {"timestamp": 1749003134569, "temperature": 85.00136881189978, "zone": 5, "curseur": 60.766621369953256, "action": "glissement_fluide"}, {"timestamp": 1749003169574, "temperature": 85.00011272754584, "zone": 4, "curseur": 70.66693604935406, "action": "glissement_fluide"}, {"timestamp": 1749003504641, "temperature": 85.00067066277163, "zone": 5, "curseur": 68.89941025879634, "action": "glissement_fluide"}, {"timestamp": 1749003619664, "temperature": 85.0000001284864, "zone": 4, "curseur": 80, "action": "glissement_fluide"}, {"timestamp": 1749003819700, "temperature": 85.00022999326836, "zone": 5, "curseur": 69.23726253486574, "action": "glissement_fluide"}, {"timestamp": 1749003914724, "temperature": 85.00002705847804, "zone": 6, "curseur": 56.296976425261775, "action": "glissement_fluide"}, {"timestamp": 1749004346560, "temperature": 84.9191000639187, "zone": 5, "curseur": 60.455004538861694, "action": "glissement_fluide"}, {"timestamp": 1749004351556, "temperature": 84.49450456359911, "zone": 3, "curseur": 80, "action": "evolution_sommeil"}, {"timestamp": 1749004513481, "temperature": 84.76397473487464, "zone": 4, "curseur": 79.91956935672017, "action": "glissement_fluide"}, {"timestamp": 1749004603499, "temperature": 84.76413979180316, "zone": 5, "curseur": 69.44928957328297, "action": "glissement_fluide"}, {"timestamp": 1749004718470, "temperature": 84.51865317744515, "zone": 6, "curseur": 59.39292219038117, "action": "evolution_sommeil"}, {"timestamp": 1749004847382, "temperature": 84.96530777039038, "zone": 6, "curseur": 58.049572251249806, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004847385, "temperature": 84.98265388519519, "zone": 3, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004855150, "temperature": 84.99575020187282, "zone": 3, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004855153, "temperature": 84.99787510093641, "zone": 3, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862880, "temperature": 84.99925628532775, "zone": 3, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862884, "temperature": 84.99962814266388, "zone": 3, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871820, "temperature": 84.99990889495265, "zone": 3, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871823, "temperature": 84.99995444747633, "zone": 3, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887194, "temperature": 84.99999218774218, "zone": 3, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887196, "temperature": 84.99999609387109, "zone": 3, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004965419, "temperature": 84.99999999350939, "zone": 3, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004965422, "temperature": 84.9999999967547, "zone": 3, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005131072, "temperature": 85.00581576284964, "zone": 4, "curseur": 79.77558879769285, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005131131, "temperature": 85.00290788142482, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005270110, "temperature": 85.00002874915853, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005270114, "temperature": 85.00001437457927, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005278649, "temperature": 85.00000352177193, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005278652, "temperature": 85.00000176088597, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005430041, "temperature": 85.00000000070253, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005430044, "temperature": 85.00000000035126, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435922, "temperature": 85.00000000012295, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435926, "temperature": 85.00000000006148, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005678831, "temperature": 85.00000000000001, "zone": 5, "curseur": 69.7578373199219, "action": "glissement_fluide"}, {"timestamp": 1749005705349, "temperature": 85, "zone": 5, "curseur": 65.37076994118792, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005705354, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005956680, "temperature": 85, "zone": 5, "curseur": 69.27183292315289, "action": "glissement_fluide"}, {"timestamp": 1749005996685, "temperature": 85, "zone": 6, "curseur": 57.912543590595526, "action": "glissement_fluide"}, {"timestamp": 1749006852601, "temperature": 85.0009579061569, "zone": 5, "curseur": 59.797705110854736, "action": "glissement_fluide"}, {"timestamp": 1749006887607, "temperature": 85.00007888769102, "zone": 4, "curseur": 70.9556251399392, "action": "glissement_fluide"}, {"timestamp": 1749006942616, "temperature": 85.00000155986741, "zone": 5, "curseur": 69.3665924712448, "action": "glissement_fluide"}, {"timestamp": 1749006947617, "temperature": 85.0000010919072, "zone": 4, "curseur": 70.03291243736054, "action": "glissement_fluide"}, {"timestamp": 1749006952618, "temperature": 85.00000076433504, "zone": 5, "curseur": 69.85466676982655, "action": "glissement_fluide"}, {"timestamp": 1749007141142, "temperature": 85.00000000020918, "zone": 6, "curseur": 59.673840126668054, "action": "glissement_fluide"}, {"timestamp": 1749007671345, "temperature": 84.82708029825314, "zone": 5, "curseur": 60.519555719918614, "action": "glissement_fluide"}, {"timestamp": 1749007746357, "temperature": 85.53157828270008, "zone": 4, "curseur": 70.56395680080702, "action": "glissement_fluide"}, {"timestamp": 1749007886381, "temperature": 84.88445501502754, "zone": 5, "curseur": 69.37615092134531, "action": "glissement_fluide"}, {"timestamp": 1749008041636, "temperature": 84.6628255868211, "zone": 6, "curseur": 59.50770496281099, "action": "glissement_fluide"}]}], ["mem_1749002541411_z3pfg2v7e", {"id": "mem_1749002541411_z3pfg2v7e", "contenu": "SYLLOGISMES: Si tous les A sont des B, et tous les B sont des C, alors tous les A sont des C (transitivité). Si X est un A, alors X est aussi un C. Exemple: Tous les humains sont mortels, Socrate est humain, donc Socrate est mortel.", "source": "Formation: Syllogismes logiques", "timestamp": 1749002541411, "importance": 1, "utilisation": 37, "temperature": 84.51535358528483, "zone": 6, "dernierAcces": 1749005705354, "liens": [], "evolution": [{"timestamp": 1749002541411, "temperature": 85, "zone": 6, "curseur": 56.43645188670785, "action": "creation"}, {"timestamp": 1749002547143, "temperature": 85, "zone": 6, "curseur": 58.11070122883242, "action": "acces_recherche", "pertinence": 2.6999999999999997}, {"timestamp": 1749002547145, "temperature": 85, "zone": 6, "curseur": 58.29982201047634, "action": "acces_recherche", "pertinence": 2.6999999999999997}, {"timestamp": 1749002570579, "temperature": 85, "zone": 5, "curseur": 59.46914398343281, "action": "glissement_fluide"}, {"timestamp": 1749002625590, "temperature": 85, "zone": 6, "curseur": 60.01889662912039, "action": "glissement_fluide"}, {"timestamp": 1749002645593, "temperature": 85, "zone": 5, "curseur": 60.0064176871447, "action": "glissement_fluide"}, {"timestamp": 1749002650595, "temperature": 85, "zone": 6, "curseur": 59.99127277604923, "action": "glissement_fluide"}, {"timestamp": 1749003134570, "temperature": 85, "zone": 5, "curseur": 60.766621369953256, "action": "glissement_fluide"}, {"timestamp": 1749003169574, "temperature": 85, "zone": 4, "curseur": 70.66693604935406, "action": "glissement_fluide"}, {"timestamp": 1749003222493, "temperature": 85, "zone": 4, "curseur": 78.45042148134885, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749003222494, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749003239548, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003239550, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003499640, "temperature": 85, "zone": 5, "curseur": 70.5863144733919, "action": "glissement_fluide"}, {"timestamp": 1749003617857, "temperature": 85, "zone": 5, "curseur": 66.23011369614949, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003617859, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003626027, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003819700, "temperature": 85.0166164652848, "zone": 5, "curseur": 69.23726253486574, "action": "glissement_fluide"}, {"timestamp": 1749003909724, "temperature": 85.00279272932042, "zone": 6, "curseur": 61.630549190803826, "action": "glissement_fluide"}, {"timestamp": 1749004346560, "temperature": 85.00136848142951, "zone": 5, "curseur": 60.455004538861694, "action": "glissement_fluide"}, {"timestamp": 1749004351194, "temperature": 85.00068424071475, "zone": 5, "curseur": 61.85007139274231, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004351560, "temperature": 85.00047896850032, "zone": 4, "curseur": 80, "action": "glissement_fluide"}, {"timestamp": 1749004598498, "temperature": 85.00000000021346, "zone": 5, "curseur": 70.04106146767342, "action": "glissement_fluide"}, {"timestamp": 1749004708517, "temperature": 85.00000000000009, "zone": 6, "curseur": 60.015787323036896, "action": "glissement_fluide"}, {"timestamp": 1749004713518, "temperature": 85.00000000000006, "zone": 5, "curseur": 60.410075158984206, "action": "glissement_fluide"}, {"timestamp": 1749004718519, "temperature": 85.00000000000004, "zone": 6, "curseur": 59.325668431920526, "action": "glissement_fluide"}, {"timestamp": 1749004847382, "temperature": 85.420175, "zone": 6, "curseur": 59.46340295654841, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004847385, "temperature": 85.2100875, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004855151, "temperature": 85.0514714375, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004855153, "temperature": 85.02573571875, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862880, "temperature": 85.00900750156251, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862884, "temperature": 85.00450375078125, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871820, "temperature": 85.0011034189414, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871823, "temperature": 85.0005517094707, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887194, "temperature": 85.00009461817422, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887196, "temperature": 85.00004730908711, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004965419, "temperature": 85.00000007861098, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749004965422, "temperature": 85.00000003930549, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005131072, "temperature": 85.47029238725914, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005131131, "temperature": 85.23514619362956, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005270110, "temperature": 85.00232480428589, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005270114, "temperature": 85.00116240214294, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005278649, "temperature": 85.00028478852502, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005278652, "temperature": 85.00014239426251, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005430041, "temperature": 85.00000005680981, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005430044, "temperature": 85.00000002840491, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435922, "temperature": 85.00000000994172, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435926, "temperature": 85.00000000497086, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005663829, "temperature": 85.0000386549686, "zone": 5, "curseur": 70.20377937756368, "action": "glissement_fluide"}, {"timestamp": 1749005668830, "temperature": 85.00002705847803, "zone": 4, "curseur": 70.81972418047057, "action": "glissement_fluide"}, {"timestamp": 1749005673831, "temperature": 85.00001894093462, "zone": 5, "curseur": 70.29119658051496, "action": "glissement_fluide"}, {"timestamp": 1749005705349, "temperature": 85.00000111419101, "zone": 5, "curseur": 66.55285506516321, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005705354, "temperature": 85.0000005570955, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005956680, "temperature": 85.00000000000004, "zone": 5, "curseur": 69.27183292315289, "action": "glissement_fluide"}, {"timestamp": 1749005996685, "temperature": 85.00000000000001, "zone": 6, "curseur": 57.912543590595526, "action": "glissement_fluide"}, {"timestamp": 1749006852601, "temperature": 85.00000002159106, "zone": 5, "curseur": 59.797705110854736, "action": "glissement_fluide"}, {"timestamp": 1749006882605, "temperature": 85.00000000254016, "zone": 4, "curseur": 69.47022064004223, "action": "glissement_fluide"}, {"timestamp": 1749006942616, "temperature": 85.00000000003516, "zone": 5, "curseur": 69.3665924712448, "action": "glissement_fluide"}, {"timestamp": 1749006947617, "temperature": 85.00000000002461, "zone": 4, "curseur": 70.03291243736054, "action": "glissement_fluide"}, {"timestamp": 1749006952618, "temperature": 85.00000000001722, "zone": 5, "curseur": 69.85466676982655, "action": "glissement_fluide"}, {"timestamp": 1749007136141, "temperature": 85.00000000000001, "zone": 6, "curseur": 60.181333237425946, "action": "glissement_fluide"}, {"timestamp": 1749007671345, "temperature": 84.82662098696167, "zone": 5, "curseur": 60.519555719918614, "action": "glissement_fluide"}, {"timestamp": 1749007731355, "temperature": 84.83478461761821, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007886381, "temperature": 84.88442862618375, "zone": 5, "curseur": 69.37615092134531, "action": "glissement_fluide"}, {"timestamp": 1749008046637, "temperature": 84.76959229170855, "zone": 6, "curseur": 59.40523340662873, "action": "glissement_fluide"}]}], ["mem_1749002567358_cjoc0runt", {"id": "mem_1749002567358_cjoc0runt", "contenu": "RAISONNEMENT SÉQUENTIEL: Pour résoudre des problèmes d'ordre, créer une séquence. Exemple: A avant B, B avant C, D après C mais avant E = ordre: A, B, C, D, <PERSON><PERSON> est E.", "source": "Formation: Raisonnement séquentiel", "timestamp": 1749002567358, "importance": 1, "utilisation": 35, "temperature": 84.51557824573437, "zone": 6, "dernierAcces": 1749005705357, "liens": [], "evolution": [{"timestamp": 1749002567358, "temperature": 85, "zone": 6, "curseur": 58.969761998122344, "action": "creation"}, {"timestamp": 1749002570579, "temperature": 85, "zone": 5, "curseur": 59.46914398343281, "action": "glissement_fluide"}, {"timestamp": 1749002573607, "temperature": 85, "zone": 5, "curseur": 61.36432404176052, "action": "acces_recherche", "pertinence": 1.8}, {"timestamp": 1749002573608, "temperature": 85, "zone": 5, "curseur": 61.37995047098438, "action": "acces_recherche", "pertinence": 1.8}, {"timestamp": 1749002625590, "temperature": 85, "zone": 6, "curseur": 60.01889662912039, "action": "glissement_fluide"}, {"timestamp": 1749002640593, "temperature": 85, "zone": 5, "curseur": 59.51680052579548, "action": "glissement_fluide"}, {"timestamp": 1749002650595, "temperature": 85, "zone": 6, "curseur": 59.99127277604923, "action": "glissement_fluide"}, {"timestamp": 1749003129566, "temperature": 85, "zone": 5, "curseur": 58.908519258507845, "action": "glissement_fluide"}, {"timestamp": 1749003169574, "temperature": 85, "zone": 4, "curseur": 70.66693604935406, "action": "glissement_fluide"}, {"timestamp": 1749003222493, "temperature": 85, "zone": 4, "curseur": 79.61440678582936, "action": "acces_recherche", "pertinence": 1.2}, {"timestamp": 1749003222494, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.2}, {"timestamp": 1749003239548, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003239550, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003499640, "temperature": 85, "zone": 5, "curseur": 70.5863144733919, "action": "glissement_fluide"}, {"timestamp": 1749003619664, "temperature": 85, "zone": 4, "curseur": 80, "action": "glissement_fluide"}, {"timestamp": 1749003626027, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003819700, "temperature": 85, "zone": 5, "curseur": 69.23726253486574, "action": "glissement_fluide"}, {"timestamp": 1749003909724, "temperature": 85, "zone": 6, "curseur": 61.630549190803826, "action": "glissement_fluide"}, {"timestamp": 1749004346560, "temperature": 85, "zone": 5, "curseur": 60.455004538861694, "action": "glissement_fluide"}, {"timestamp": 1749004351560, "temperature": 85, "zone": 4, "curseur": 80, "action": "glissement_fluide"}, {"timestamp": 1749004598498, "temperature": 85, "zone": 5, "curseur": 70.04106146767342, "action": "glissement_fluide"}, {"timestamp": 1749004718519, "temperature": 85, "zone": 6, "curseur": 59.325668431920526, "action": "glissement_fluide"}, {"timestamp": 1749004847382, "temperature": 85, "zone": 5, "curseur": 61.17207608058343, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004847385, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004855151, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004855153, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862880, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862884, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871820, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871823, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887194, "temperature": 86.225, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887196, "temperature": 85.6125, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004965419, "temperature": 85.03562097650119, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004965422, "temperature": 85.0178104882506, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005131072, "temperature": 85.00000028674046, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005131131, "temperature": 85.00000014337023, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005270110, "temperature": 85.00000000141745, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005270114, "temperature": 85.00000000070872, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005278649, "temperature": 85.00000000017363, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005278652, "temperature": 85.00000000008681, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005430041, "temperature": 85.00000000000003, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005430044, "temperature": 85.00000000000001, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435923, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435926, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005673831, "temperature": 85, "zone": 5, "curseur": 70.29119658051496, "action": "glissement_fluide"}, {"timestamp": 1749005705349, "temperature": 85, "zone": 5, "curseur": 68.05064555747633, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005705354, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005956680, "temperature": 85.00003919000312, "zone": 5, "curseur": 69.27183292315289, "action": "glissement_fluide"}, {"timestamp": 1749005991685, "temperature": 85.00000322746527, "zone": 6, "curseur": 61.161255471637524, "action": "glissement_fluide"}, {"timestamp": 1749006852601, "temperature": 85.00569944759269, "zone": 5, "curseur": 59.797705110854736, "action": "glissement_fluide"}, {"timestamp": 1749006882605, "temperature": 85.00067053430983, "zone": 4, "curseur": 69.47022064004223, "action": "glissement_fluide"}, {"timestamp": 1749007136141, "temperature": 85.00067053608791, "zone": 6, "curseur": 60.181333237425946, "action": "glissement_fluide"}, {"timestamp": 1749007671345, "temperature": 84.82662033955975, "zone": 5, "curseur": 60.519555719918614, "action": "glissement_fluide"}, {"timestamp": 1749007731355, "temperature": 84.83478460874672, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007781362, "temperature": 88.16305405209485, "zone": 4, "curseur": 74.76347705689987, "action": "glissement_fluide"}, {"timestamp": 1749007886381, "temperature": 84.88635435942396, "zone": 5, "curseur": 69.37615092134531, "action": "glissement_fluide"}, {"timestamp": 1749008046637, "temperature": 84.76397803249596, "zone": 6, "curseur": 59.40523340662873, "action": "glissement_fluide"}]}], ["mem_1749003121057_5qg1mnfo9", {"id": "mem_1749003121057_5qg1mnfo9", "contenu": "MÉDECINE AVANCÉE: Anatomie humaine - 206 os, 600 muscles, 12 systèmes. Physiologie - circulation sanguine, respiration, digestion. Pathologie - diagnostic différentiel, symptômes, traitements. Pharmacologie - mécanismes d'action, interactions médicamenteuses. Urgences médicales - AVC, infarctus, choc anaphylactique.", "source": "Formation: Médecine avancée", "timestamp": 1749003121057, "importance": 1, "utilisation": 40, "temperature": 84.61514983529457, "zone": 6, "dernierAcces": 1749005705354, "liens": [], "evolution": [{"timestamp": 1749003121057, "temperature": 85, "zone": 6, "curseur": 56.57620168726838, "action": "creation"}, {"timestamp": 1749003129566, "temperature": 85, "zone": 5, "curseur": 58.908519258507845, "action": "glissement_fluide"}, {"timestamp": 1749003164573, "temperature": 85, "zone": 4, "curseur": 68.27692344162317, "action": "glissement_fluide"}, {"timestamp": 1749003222493, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003222495, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003239548, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003239550, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003465641, "temperature": 85, "zone": 4, "curseur": 70.96132872365116, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003465642, "temperature": 85, "zone": 4, "curseur": 72.74533149420115, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003499640, "temperature": 85, "zone": 5, "curseur": 70.5863144733919, "action": "glissement_fluide"}, {"timestamp": 1749003581030, "temperature": 85, "zone": 5, "curseur": 65.61064298349937, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003581033, "temperature": 85, "zone": 5, "curseur": 66.35521176093059, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003617857, "temperature": 85, "zone": 5, "curseur": 66.84000131267278, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003617859, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003814699, "temperature": 85, "zone": 5, "curseur": 70.7310714193889, "action": "glissement_fluide"}, {"timestamp": 1749003909724, "temperature": 85, "zone": 6, "curseur": 61.630549190803826, "action": "glissement_fluide"}, {"timestamp": 1749004346560, "temperature": 85.0000000051839, "zone": 5, "curseur": 60.455004538861694, "action": "glissement_fluide"}, {"timestamp": 1749004351194, "temperature": 85.00000000259195, "zone": 5, "curseur": 63.199498448548304, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004351560, "temperature": 85.00000000181436, "zone": 4, "curseur": 80, "action": "glissement_fluide"}, {"timestamp": 1749004598498, "temperature": 85.00000000000001, "zone": 5, "curseur": 70.04106146767342, "action": "glissement_fluide"}, {"timestamp": 1749004713518, "temperature": 85.00000000000001, "zone": 6, "curseur": 60.410075158984206, "action": "glissement_fluide"}, {"timestamp": 1749004847382, "temperature": 85, "zone": 5, "curseur": 63.14087301705209, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004847385, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004855151, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004855153, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862880, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862884, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871820, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749004871823, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749004887194, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887196, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004965419, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749004965422, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005131072, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005131131, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005270110, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005270114, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005278649, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005278652, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005430041, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005430044, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435923, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435926, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005668830, "temperature": 85, "zone": 5, "curseur": 70.81972418047057, "action": "glissement_fluide"}, {"timestamp": 1749005705350, "temperature": 85, "zone": 5, "curseur": 69.7484847932517, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005705354, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005941073, "temperature": 85.00398961331489, "zone": 5, "curseur": 70.88408937645887, "action": "glissement_fluide"}, {"timestamp": 1749005991685, "temperature": 85.00022999326828, "zone": 6, "curseur": 61.161255471637524, "action": "glissement_fluide"}, {"timestamp": 1749006852601, "temperature": 85.00000318340288, "zone": 5, "curseur": 59.797705110854736, "action": "glissement_fluide"}, {"timestamp": 1749006882605, "temperature": 85.00000037452418, "zone": 4, "curseur": 69.47022064004223, "action": "glissement_fluide"}, {"timestamp": 1749006942616, "temperature": 85.0000000051839, "zone": 5, "curseur": 69.3665924712448, "action": "glissement_fluide"}, {"timestamp": 1749006947617, "temperature": 85.00000000362874, "zone": 4, "curseur": 70.03291243736054, "action": "glissement_fluide"}, {"timestamp": 1749006952618, "temperature": 85.00000000254012, "zone": 5, "curseur": 69.85466676982655, "action": "glissement_fluide"}, {"timestamp": 1749007141143, "temperature": 85.0000000000007, "zone": 6, "curseur": 59.673840126668054, "action": "glissement_fluide"}, {"timestamp": 1749007671345, "temperature": 84.82662657869665, "zone": 5, "curseur": 60.519555719918614, "action": "glissement_fluide"}, {"timestamp": 1749007731355, "temperature": 84.83478469424298, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007846374, "temperature": 87.21414002659243, "zone": 4, "curseur": 72.70915837587019, "action": "glissement_fluide"}, {"timestamp": 1749007876379, "temperature": 85.05093890318862, "zone": 5, "curseur": 70.46787439314477, "action": "glissement_fluide"}, {"timestamp": 1749007881380, "temperature": 85.03565723223204, "zone": 4, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749007886381, "temperature": 85.02496006256243, "zone": 5, "curseur": 69.37615092134531, "action": "glissement_fluide"}, {"timestamp": 1749008046638, "temperature": 84.76398691516711, "zone": 6, "curseur": 59.40523340662873, "action": "glissement_fluide"}]}], ["mem_1749003128761_37umcv0jk", {"id": "mem_1749003128761_37umcv0jk", "contenu": "CODAGE EXPERT: Algorithmes - tri rapide O(n log n), recherche binaire O(log n), graphes BFS/DFS. Structures de données - arbres binaires, tables de hachage, listes chaînées. Paradigmes - programmation orientée objet, fonctionnelle, récursive. Optimisation - complexité temporelle et spatiale, profilage, refactoring. Sécurité - chiffrement, authentification, validation d'entrée.", "source": "Formation: Codage expert", "timestamp": 1749003128761, "importance": 0.95, "utilisation": 38, "temperature": 84.5153566589247, "zone": 6, "dernierAcces": 1749005705357, "liens": [], "evolution": [{"timestamp": 1749003128761, "temperature": 85, "zone": 6, "curseur": 58.39027521395307, "action": "creation"}, {"timestamp": 1749003129566, "temperature": 85, "zone": 5, "curseur": 58.908519258507845, "action": "glissement_fluide"}, {"timestamp": 1749003164573, "temperature": 85, "zone": 4, "curseur": 68.27692344162317, "action": "glissement_fluide"}, {"timestamp": 1749003247500, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003247501, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003263029, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003263031, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003499640, "temperature": 85, "zone": 5, "curseur": 70.5863144733919, "action": "glissement_fluide"}, {"timestamp": 1749003617857, "temperature": 85, "zone": 5, "curseur": 68.15101264131532, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003617859, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003814699, "temperature": 85, "zone": 5, "curseur": 70.7310714193889, "action": "glissement_fluide"}, {"timestamp": 1749003909724, "temperature": 85, "zone": 6, "curseur": 61.630549190803826, "action": "glissement_fluide"}, {"timestamp": 1749004344031, "temperature": 85, "zone": 6, "curseur": 57.089819277298574, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004344033, "temperature": 85, "zone": 6, "curseur": 59.49345134124808, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004346560, "temperature": 85, "zone": 5, "curseur": 60.455004538861694, "action": "glissement_fluide"}, {"timestamp": 1749004351194, "temperature": 85, "zone": 5, "curseur": 63.98057017861689, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004351560, "temperature": 85, "zone": 4, "curseur": 80, "action": "glissement_fluide"}, {"timestamp": 1749004598498, "temperature": 85, "zone": 5, "curseur": 70.04106146767342, "action": "glissement_fluide"}, {"timestamp": 1749004713519, "temperature": 85, "zone": 6, "curseur": 60.410075158984206, "action": "glissement_fluide"}, {"timestamp": 1749004847382, "temperature": 85, "zone": 5, "curseur": 65.18964980087682, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004847386, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004855151, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004855153, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862881, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862884, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871820, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871823, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887194, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887197, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004965419, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749004965422, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005131072, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005131131, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005270110, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005270114, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005278649, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005278652, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005430041, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005430044, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435923, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435926, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005668831, "temperature": 85, "zone": 5, "curseur": 70.81972418047057, "action": "glissement_fluide"}, {"timestamp": 1749005705350, "temperature": 85, "zone": 4, "curseur": 71.74395048604265, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005705354, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005941073, "temperature": 85, "zone": 5, "curseur": 70.88408937645887, "action": "glissement_fluide"}, {"timestamp": 1749005991685, "temperature": 85, "zone": 6, "curseur": 61.161255471637524, "action": "glissement_fluide"}, {"timestamp": 1749006852602, "temperature": 85.04940599087801, "zone": 5, "curseur": 59.797705110854736, "action": "glissement_fluide"}, {"timestamp": 1749006882605, "temperature": 85.00581256542081, "zone": 4, "curseur": 69.47022064004223, "action": "glissement_fluide"}, {"timestamp": 1749006942616, "temperature": 85.00008045338735, "zone": 5, "curseur": 69.3665924712448, "action": "glissement_fluide"}, {"timestamp": 1749006947617, "temperature": 85.00005631737115, "zone": 4, "curseur": 70.03291243736054, "action": "glissement_fluide"}, {"timestamp": 1749006952618, "temperature": 85.0000394221598, "zone": 5, "curseur": 69.85466676982655, "action": "glissement_fluide"}, {"timestamp": 1749007141143, "temperature": 85.00000001078935, "zone": 6, "curseur": 59.673840126668054, "action": "glissement_fluide"}, {"timestamp": 1749007671346, "temperature": 84.82662024154466, "zone": 5, "curseur": 60.519555719918614, "action": "glissement_fluide"}, {"timestamp": 1749007731355, "temperature": 84.83478460740359, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007886381, "temperature": 84.88442862618359, "zone": 5, "curseur": 69.37615092134531, "action": "glissement_fluide"}]}], ["mem_1749003138754_i5y46d5po", {"id": "mem_1749003138754_i5y46d5po", "contenu": "SCIENCES PHYSIQUES: Mécanique - F=ma, énergie cinétique E=½mv², conservation de l'énergie. Thermodynamique - lois de la thermodynamique, entropie, cycles de Carnot. Électromagnétisme - loi d'Ohm V=RI, équations de Maxwell, induction électromagnétique. Optique - réfraction, diffraction, interférences. Physique quantique - principe d'incertitude, dualité onde-particule.", "source": "Formation: Sciences physiques", "timestamp": 1749003138754, "importance": 1, "utilisation": 37, "temperature": 84.5153535852948, "zone": 6, "dernierAcces": 1749005705354, "liens": [], "evolution": [{"timestamp": 1749003138754, "temperature": 85, "zone": 5, "curseur": 61.14013926817445, "action": "creation"}, {"timestamp": 1749003164573, "temperature": 85, "zone": 4, "curseur": 68.27692344162317, "action": "glissement_fluide"}, {"timestamp": 1749003247500, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749003247501, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749003263029, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749003263031, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749003499640, "temperature": 85, "zone": 5, "curseur": 70.5863144733919, "action": "glissement_fluide"}, {"timestamp": 1749003617857, "temperature": 85, "zone": 5, "curseur": 69.96566821663573, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749003617859, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749003814699, "temperature": 85, "zone": 5, "curseur": 70.7310714193889, "action": "glissement_fluide"}, {"timestamp": 1749003909724, "temperature": 85, "zone": 6, "curseur": 61.630549190803826, "action": "glissement_fluide"}, {"timestamp": 1749004346560, "temperature": 85, "zone": 5, "curseur": 60.455004538861694, "action": "glissement_fluide"}, {"timestamp": 1749004351194, "temperature": 85, "zone": 5, "curseur": 65.45713705281216, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004598498, "temperature": 85.00000155986741, "zone": 5, "curseur": 70.04106146767342, "action": "glissement_fluide"}, {"timestamp": 1749004713519, "temperature": 85.00000000042692, "zone": 6, "curseur": 60.410075158984206, "action": "glissement_fluide"}, {"timestamp": 1749004847382, "temperature": 85.00000000000006, "zone": 5, "curseur": 67.35612074346254, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004847386, "temperature": 85.00000000000003, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004855151, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.3}, {"timestamp": 1749004855153, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.3}, {"timestamp": 1749004862881, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862884, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871820, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871823, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887194, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887197, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004965419, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749004965422, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005131072, "temperature": 85.00589626049359, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005131131, "temperature": 85.00294813024679, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005270110, "temperature": 85.0000291470839, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005270114, "temperature": 85.00001457354195, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005278649, "temperature": 85.00000357051778, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005278652, "temperature": 85.00000178525889, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005430041, "temperature": 85.00000000071225, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005430044, "temperature": 85.00000000035612, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435923, "temperature": 85.00000000012464, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435926, "temperature": 85.00000000006233, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005668831, "temperature": 85.001368437367, "zone": 5, "curseur": 70.81972418047057, "action": "glissement_fluide"}, {"timestamp": 1749005705350, "temperature": 85.00005634835074, "zone": 4, "curseur": 73.90185622853296, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005705354, "temperature": 85.00002817417537, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005941073, "temperature": 85.00000000000301, "zone": 5, "curseur": 70.88408937645887, "action": "glissement_fluide"}, {"timestamp": 1749005991686, "temperature": 85.00000000000018, "zone": 6, "curseur": 61.161255471637524, "action": "glissement_fluide"}, {"timestamp": 1749006852602, "temperature": 85.0484445052035, "zone": 5, "curseur": 59.797705110854736, "action": "glissement_fluide"}, {"timestamp": 1749006882605, "temperature": 85.00569944759269, "zone": 4, "curseur": 69.47022064004223, "action": "glissement_fluide"}, {"timestamp": 1749006942617, "temperature": 85.00007888769102, "zone": 5, "curseur": 69.3665924712448, "action": "glissement_fluide"}, {"timestamp": 1749007141143, "temperature": 85.00000001057938, "zone": 6, "curseur": 59.673840126668054, "action": "glissement_fluide"}, {"timestamp": 1749007671346, "temperature": 84.8270810436719, "zone": 5, "curseur": 60.519555719918614, "action": "glissement_fluide"}, {"timestamp": 1749007691347, "temperature": 88.16118974779269, "zone": 5, "curseur": 64.39831602012268, "action": "glissement_fluide"}, {"timestamp": 1749007731355, "temperature": 85.03555011670169, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007846374, "temperature": 87.21419388284862, "zone": 4, "curseur": 72.70915837587019, "action": "glissement_fluide"}, {"timestamp": 1749008041622, "temperature": 84.5184976540559, "zone": 6, "curseur": 59.37780058802187, "action": "evolution_sommeil"}, {"timestamp": 1749008051639, "temperature": 84.83484469534118, "zone": 5, "curseur": 59.74530863464909, "action": "glissement_fluide"}, {"timestamp": 1749008056642, "temperature": 84.88439128673882, "zone": 6, "curseur": 59.845259593127835, "action": "glissement_fluide"}]}], ["mem_1749003146520_6u4iq17v5", {"id": "mem_1749003146520_6u4iq17v5", "contenu": "SCIENCES NATURELLES: Biologie cellulaire - mitose, méiose, ADN, ARN, protéines. Écologie - chaînes alimentaires, cycles biogéochimiques, biodiversité. Évolution - sélection naturelle, mutations, spéciation. Génétique - lo<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, génie g<PERSON>. Botanique - photosynthèse 6CO2+6H2O→C6H12O6+6O2, classification des plantes.", "source": "Formation: Sciences naturelles", "timestamp": 1749003146520, "importance": 1, "utilisation": 47, "temperature": 84.51537984208673, "zone": 6, "dernierAcces": 1749005705357, "liens": [], "evolution": [{"timestamp": 1749003146520, "temperature": 85, "zone": 5, "curseur": 63.456802128319985, "action": "creation"}, {"timestamp": 1749003164573, "temperature": 85, "zone": 4, "curseur": 68.27692344162317, "action": "glissement_fluide"}, {"timestamp": 1749003214183, "temperature": 85, "zone": 4, "curseur": 76.77078849994143, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749003214184, "temperature": 85, "zone": 4, "curseur": 77.31380487643683, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749003222493, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749003222495, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749003239548, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003239550, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003270603, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749003270605, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749003292388, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749003292389, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749003499640, "temperature": 85, "zone": 5, "curseur": 70.5863144733919, "action": "glissement_fluide"}, {"timestamp": 1749003617857, "temperature": 85, "zone": 4, "curseur": 72.10805079517313, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003617859, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003626027, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003814699, "temperature": 85.02373780754971, "zone": 5, "curseur": 70.7310714193889, "action": "glissement_fluide"}, {"timestamp": 1749003909724, "temperature": 85.00279272932042, "zone": 6, "curseur": 61.630549190803826, "action": "glissement_fluide"}, {"timestamp": 1749004346560, "temperature": 85.000000005184, "zone": 5, "curseur": 60.455004538861694, "action": "glissement_fluide"}, {"timestamp": 1749004351194, "temperature": 85.000000002592, "zone": 5, "curseur": 67.38977763499041, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004351560, "temperature": 85.0000000018144, "zone": 4, "curseur": 80, "action": "glissement_fluide"}, {"timestamp": 1749004543991, "temperature": 85.00000000000001, "zone": 4, "curseur": 76.04696533043762, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004598498, "temperature": 85.00000000000001, "zone": 5, "curseur": 70.04106146767342, "action": "glissement_fluide"}, {"timestamp": 1749004713519, "temperature": 85.00000000000001, "zone": 6, "curseur": 60.410075158984206, "action": "glissement_fluide"}, {"timestamp": 1749004847382, "temperature": 85, "zone": 5, "curseur": 69.60251194314796, "action": "acces_recherche", "pertinence": 3.2}, {"timestamp": 1749004847386, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 3.2}, {"timestamp": 1749004855151, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004855153, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862881, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862884, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871820, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871823, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887194, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887197, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004965419, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749004965422, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005131072, "temperature": 85.47037288490239, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005131131, "temperature": 85.2351864424512, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005270110, "temperature": 85.00232520221125, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005270114, "temperature": 85.00116260110562, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005278649, "temperature": 85.00028483727087, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005278652, "temperature": 85.00014241863543, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005430041, "temperature": 85.00000005681953, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005430044, "temperature": 85.00000002840977, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435923, "temperature": 85.00000000994342, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435926, "temperature": 85.00000000497171, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005668831, "temperature": 85.00000000000001, "zone": 5, "curseur": 70.81972418047057, "action": "glissement_fluide"}, {"timestamp": 1749005705350, "temperature": 85, "zone": 4, "curseur": 75.96433138062687, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005705354, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005991686, "temperature": 85.14123762450001, "zone": 6, "curseur": 61.161255471637524, "action": "glissement_fluide"}, {"timestamp": 1749006852602, "temperature": 85.00569946921841, "zone": 5, "curseur": 59.797705110854736, "action": "glissement_fluide"}, {"timestamp": 1749006882605, "temperature": 85.00067053685407, "zone": 4, "curseur": 69.47022064004223, "action": "glissement_fluide"}, {"timestamp": 1749006942617, "temperature": 85.00000928109318, "zone": 5, "curseur": 69.3665924712448, "action": "glissement_fluide"}, {"timestamp": 1749007136142, "temperature": 85.00000000177809, "zone": 6, "curseur": 60.181333237425946, "action": "glissement_fluide"}, {"timestamp": 1749007676345, "temperature": 84.87863469087301, "zone": 5, "curseur": 60.12563136484058, "action": "glissement_fluide"}, {"timestamp": 1749007731355, "temperature": 84.83478461761821, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007876379, "temperature": 84.76414005343622, "zone": 5, "curseur": 70.46787439314477, "action": "glissement_fluide"}, {"timestamp": 1749007881380, "temperature": 84.83489803740535, "zone": 4, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749007886381, "temperature": 84.88442862618375, "zone": 5, "curseur": 69.37615092134531, "action": "glissement_fluide"}]}], ["mem_1749003154094_iu878cgz8", {"id": "mem_1749003154094_iu878cgz8", "contenu": "MATHÉMATIQUES AVANCÉES: Calcul différentiel - dérivées, intégrales, théorème fondamental. Algèbre linéaire - matrices, déterminants, espaces vectoriels. Statistiques - moyenne, médiane, écart-type, loi normale. Probabilités - théorème de Bayes P(A|B)=P(B|A)P(A)/P(B). Géométrie - théorème de Pythagore, trigonométrie, géométrie analytique.", "source": "Formation: Mathématiques avancées", "timestamp": 1749003154094, "importance": 0.95, "utilisation": 33, "temperature": 84.51535358586199, "zone": 6, "dernierAcces": 1749005705354, "liens": [], "evolution": [{"timestamp": 1749003154094, "temperature": 85, "zone": 5, "curseur": 65.69430708870156, "action": "creation"}, {"timestamp": 1749003164573, "temperature": 85, "zone": 4, "curseur": 68.27692344162317, "action": "glissement_fluide"}, {"timestamp": 1749003344724, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003344725, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003499640, "temperature": 85, "zone": 5, "curseur": 70.5863144733919, "action": "glissement_fluide"}, {"timestamp": 1749003617857, "temperature": 85, "zone": 4, "curseur": 74.32538137191004, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003617859, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003814699, "temperature": 85, "zone": 5, "curseur": 70.7310714193889, "action": "glissement_fluide"}, {"timestamp": 1749003909724, "temperature": 85, "zone": 6, "curseur": 61.630549190803826, "action": "glissement_fluide"}, {"timestamp": 1749004346560, "temperature": 85, "zone": 5, "curseur": 60.455004538861694, "action": "glissement_fluide"}, {"timestamp": 1749004351194, "temperature": 85, "zone": 5, "curseur": 69.50209828366602, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004351560, "temperature": 85, "zone": 4, "curseur": 80, "action": "glissement_fluide"}, {"timestamp": 1749004468813, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004468815, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004598498, "temperature": 85, "zone": 5, "curseur": 70.04106146767342, "action": "glissement_fluide"}, {"timestamp": 1749004713519, "temperature": 85, "zone": 6, "curseur": 60.410075158984206, "action": "glissement_fluide"}, {"timestamp": 1749004847382, "temperature": 85, "zone": 4, "curseur": 71.85271202603366, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004847386, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004855151, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 2.1}, {"timestamp": 1749004855153, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 2.1}, {"timestamp": 1749004862881, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862884, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871820, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871823, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887194, "temperature": 86.225, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887197, "temperature": 85.6125, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004965419, "temperature": 85.03562097650119, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749004965422, "temperature": 85.0178104882506, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005131072, "temperature": 85.00000028674046, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005131131, "temperature": 85.00000014337023, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005270110, "temperature": 85.00000000141745, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005270114, "temperature": 85.00000000070872, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005278649, "temperature": 85.00000000017363, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005278652, "temperature": 85.00000000008681, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005430041, "temperature": 85.00000000000003, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005430044, "temperature": 85.00000000000001, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435923, "temperature": 86.75, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435926, "temperature": 85.875, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005668831, "temperature": 85.00000079585072, "zone": 5, "curseur": 70.81972418047057, "action": "glissement_fluide"}, {"timestamp": 1749005705350, "temperature": 85.00000003277086, "zone": 4, "curseur": 78.03433762047996, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005705354, "temperature": 85.00000001638543, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005941073, "temperature": 85.00000000000001, "zone": 5, "curseur": 70.88408937645887, "action": "glissement_fluide"}, {"timestamp": 1749005991686, "temperature": 85.00000000000001, "zone": 6, "curseur": 61.161255471637524, "action": "glissement_fluide"}, {"timestamp": 1749006852602, "temperature": 85.00569944759269, "zone": 5, "curseur": 59.797705110854736, "action": "glissement_fluide"}, {"timestamp": 1749006882605, "temperature": 85.00067053430983, "zone": 4, "curseur": 69.47022064004223, "action": "glissement_fluide"}, {"timestamp": 1749006942617, "temperature": 85.00000928105797, "zone": 5, "curseur": 69.3665924712448, "action": "glissement_fluide"}, {"timestamp": 1749007131142, "temperature": 85.00000000254012, "zone": 6, "curseur": 62.99990429382722, "action": "glissement_fluide"}, {"timestamp": 1749007666344, "temperature": 84.7523146455174, "zone": 5, "curseur": 57.10190905701287, "action": "glissement_fluide"}, {"timestamp": 1749007671346, "temperature": 84.82662025186218, "zone": 6, "curseur": 60.519555719918614, "action": "glissement_fluide"}, {"timestamp": 1749007676345, "temperature": 84.87863417630352, "zone": 5, "curseur": 60.12563136484058, "action": "glissement_fluide"}, {"timestamp": 1749007731355, "temperature": 84.83478460754498, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007781362, "temperature": 88.16305405206123, "zone": 4, "curseur": 74.76347705689987, "action": "glissement_fluide"}, {"timestamp": 1749007876379, "temperature": 84.76807012127335, "zone": 5, "curseur": 70.46787439314477, "action": "glissement_fluide"}, {"timestamp": 1749007881380, "temperature": 84.83764908489135, "zone": 4, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749007886381, "temperature": 84.88635435942395, "zone": 5, "curseur": 69.37615092134531, "action": "glissement_fluide"}, {"timestamp": 1749008041622, "temperature": 84.51966378849978, "zone": 6, "curseur": 59.33973457840888, "action": "evolution_sommeil"}]}], ["mem_1749003161585_7rw42nbvf", {"id": "mem_1749003161585_7rw42nbvf", "contenu": "CHIMIE AVANCÉE: Chimie organique - hydrocarbures, groupes fonctionnels, réactions de substitution. Chimie inorganique - tableau périodique, liaisons ioniques et covalentes. Thermochimie - enthalpie, entropie, énergie libre de Gibbs. Cinétique - vitesse de réaction, catalyseurs, mécanismes réactionnels. Équilibres chimiques - constante d'équilibre, princi<PERSON> de Le Chatelier.", "source": "Formation: <PERSON><PERSON>", "timestamp": 1749003161585, "importance": 1, "utilisation": 42, "temperature": 84.51535358528368, "zone": 6, "dernierAcces": 1749005705354, "liens": [], "evolution": [{"timestamp": 1749003161585, "temperature": 85, "zone": 5, "curseur": 67.79193439186072, "action": "creation"}, {"timestamp": 1749003164573, "temperature": 85, "zone": 4, "curseur": 68.27692344162317, "action": "glissement_fluide"}, {"timestamp": 1749003214183, "temperature": 85, "zone": 4, "curseur": 77.054317106953, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003214184, "temperature": 85, "zone": 4, "curseur": 77.44060916742156, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003247500, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003247502, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003263029, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749003263031, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749003270603, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749003270605, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749003292388, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003292389, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003499640, "temperature": 85, "zone": 5, "curseur": 70.5863144733919, "action": "glissement_fluide"}, {"timestamp": 1749003542807, "temperature": 85.144120025, "zone": 5, "curseur": 64.79920457819307, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003542810, "temperature": 85.0720600125, "zone": 5, "curseur": 66.83394298484176, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003556433, "temperature": 85.01235829214374, "zone": 5, "curseur": 67.61585061997876, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003556434, "temperature": 85.00617914607187, "zone": 5, "curseur": 67.64209703594405, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003619664, "temperature": 85.04848641359786, "zone": 4, "curseur": 80, "action": "glissement_fluide"}, {"timestamp": 1749003814699, "temperature": 85.0000000441005, "zone": 5, "curseur": 70.7310714193889, "action": "glissement_fluide"}, {"timestamp": 1749003862491, "temperature": 88.50000001058854, "zone": 6, "curseur": 66.85819175368742, "action": "glissement_fluide"}, {"timestamp": 1749003867491, "temperature": 87.45000000741197, "zone": 5, "curseur": 62.230395978605515, "action": "glissement_fluide"}, {"timestamp": 1749003909724, "temperature": 86.71500000518839, "zone": 6, "curseur": 61.630549190803826, "action": "glissement_fluide"}, {"timestamp": 1749004346560, "temperature": 85.00000000007175, "zone": 5, "curseur": 60.455004538861694, "action": "glissement_fluide"}, {"timestamp": 1749004351560, "temperature": 85.00000000005022, "zone": 4, "curseur": 80, "action": "glissement_fluide"}, {"timestamp": 1749004598498, "temperature": 85.00000000000001, "zone": 5, "curseur": 70.04106146767342, "action": "glissement_fluide"}, {"timestamp": 1749004713519, "temperature": 85.00000000000001, "zone": 6, "curseur": 60.410075158984206, "action": "glissement_fluide"}, {"timestamp": 1749004847383, "temperature": 85, "zone": 4, "curseur": 74.31150043880612, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004847386, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004855151, "temperature": 86.225, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004855153, "temperature": 85.6125, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862881, "temperature": 85.21437499999999, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862884, "temperature": 85.1071875, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871820, "temperature": 85.0262609375, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871823, "temperature": 85.01313046874999, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887194, "temperature": 85.00225187539063, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.1}, {"timestamp": 1749004887197, "temperature": 85.00112593769532, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.1}, {"timestamp": 1749004965419, "temperature": 85.00000187091047, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004965422, "temperature": 85.00000093545523, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005131072, "temperature": 85.00000000001506, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005131131, "temperature": 85.00000000000753, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005270111, "temperature": 85.00000000000007, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005270114, "temperature": 85.00000000000003, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005278649, "temperature": 86.22500000000001, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005278652, "temperature": 85.61250000000001, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005430041, "temperature": 85.00024436381554, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005430044, "temperature": 85.00012218190777, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435923, "temperature": 85.00004276366772, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435926, "temperature": 85.00002138183386, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005668831, "temperature": 85.00136843738645, "zone": 5, "curseur": 70.81972418047057, "action": "glissement_fluide"}, {"timestamp": 1749005705350, "temperature": 85.00005634835154, "zone": 4, "curseur": 79.18984793641286, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005705354, "temperature": 85.00002817417577, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005941073, "temperature": 85.0039896133179, "zone": 5, "curseur": 70.88408937645887, "action": "glissement_fluide"}, {"timestamp": 1749005991686, "temperature": 85.00022999326843, "zone": 6, "curseur": 61.161255471637524, "action": "glissement_fluide"}, {"timestamp": 1749006471772, "temperature": 85.00000000001205, "zone": 5, "curseur": 57.63217591501163, "action": "glissement_fluide"}, {"timestamp": 1749006487893, "temperature": 85.00000000000844, "zone": 6, "curseur": 59.98890981180637, "action": "glissement_fluide"}, {"timestamp": 1749006852602, "temperature": 85.00011269670146, "zone": 5, "curseur": 59.797705110854736, "action": "glissement_fluide"}, {"timestamp": 1749006882605, "temperature": 85.00001325865423, "zone": 4, "curseur": 69.47022064004223, "action": "glissement_fluide"}, {"timestamp": 1749006942617, "temperature": 85.00000018351685, "zone": 5, "curseur": 69.3665924712448, "action": "glissement_fluide"}, {"timestamp": 1749007131143, "temperature": 85.00000000005022, "zone": 6, "curseur": 62.99990429382722, "action": "glissement_fluide"}, {"timestamp": 1749007666344, "temperature": 84.************, "zone": 5, "curseur": 57.10190905701287, "action": "glissement_fluide"}, {"timestamp": 1749007671346, "temperature": 84.8270802982529, "zone": 6, "curseur": 60.519555719918614, "action": "glissement_fluide"}, {"timestamp": 1749007676345, "temperature": 84.87895620877703, "zone": 5, "curseur": 60.12563136484058, "action": "glissement_fluide"}, {"timestamp": 1749007731355, "temperature": 84.83479091166205, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007876379, "temperature": 84.76414005363388, "zone": 5, "curseur": 70.46787439314477, "action": "glissement_fluide"}, {"timestamp": 1749007881380, "temperature": 84.83489803754372, "zone": 4, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749007886381, "temperature": 84.88442862628061, "zone": 5, "curseur": 69.37615092134531, "action": "glissement_fluide"}, {"timestamp": 1749008041622, "temperature": 84.51832226343596, "zone": 6, "curseur": 59.40850957692229, "action": "evolution_sommeil"}, {"timestamp": 1749008041637, "temperature": 84.66282558440517, "zone": 5, "curseur": 59.50770496281099, "action": "glissement_fluide"}, {"timestamp": 1749008046638, "temperature": 84.76397790908362, "zone": 6, "curseur": 59.40523340662873, "action": "glissement_fluide"}, {"timestamp": 1749008051639, "temperature": 84.83478453635854, "zone": 5, "curseur": 59.74530863464909, "action": "glissement_fluide"}, {"timestamp": 1749008056642, "temperature": 84.88434917545098, "zone": 6, "curseur": 59.845259593127835, "action": "glissement_fluide"}]}], ["mem_1749003169006_tmqtah13s", {"id": "mem_1749003169006_tmqtah13s", "contenu": "INGÉNIERIE: Mécanique des fluides - équation <PERSON>, viscosité, turbulence. Résistance des matériaux - contrainte, déformation, module d'Young. Électronique - loi d'Ohm, transistors, circuits intégrés. Automatique - systèmes asservis, PID, transformée de Laplace. Intelligence artificielle - réseaux de neurones, apprentissage automatique, algorithmes génétiques.", "source": "Formation: Ingénierie et technologie", "timestamp": 1749003169006, "importance": 1, "utilisation": 35, "temperature": 84.51727276407601, "zone": 6, "dernierAcces": 1749005705354, "liens": [], "evolution": [{"timestamp": 1749003169006, "temperature": 85, "zone": 4, "curseur": 70.17653340364876, "action": "creation"}, {"timestamp": 1749003222493, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749003222495, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749003239548, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003239550, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003499640, "temperature": 85, "zone": 5, "curseur": 70.5863144733919, "action": "glissement_fluide"}, {"timestamp": 1749003619664, "temperature": 85, "zone": 4, "curseur": 80, "action": "glissement_fluide"}, {"timestamp": 1749003626027, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003814699, "temperature": 85, "zone": 5, "curseur": 70.7310714193889, "action": "glissement_fluide"}, {"timestamp": 1749003909724, "temperature": 85, "zone": 6, "curseur": 61.630549190803826, "action": "glissement_fluide"}, {"timestamp": 1749004344031, "temperature": 85, "zone": 6, "curseur": 58.52477090560415, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749004344033, "temperature": 85, "zone": 5, "curseur": 60.196338548150244, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749004351561, "temperature": 85, "zone": 4, "curseur": 80, "action": "glissement_fluide"}, {"timestamp": 1749004598498, "temperature": 85, "zone": 5, "curseur": 70.04106146767342, "action": "glissement_fluide"}, {"timestamp": 1749004713519, "temperature": 85, "zone": 6, "curseur": 60.410075158984206, "action": "glissement_fluide"}, {"timestamp": 1749004847383, "temperature": 85, "zone": 4, "curseur": 76.75507796938123, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004847387, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004855151, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.3}, {"timestamp": 1749004855153, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.3}, {"timestamp": 1749004862881, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862884, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871820, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871823, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887194, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887197, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004965419, "temperature": 85.2941225, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004965422, "temperature": 85.14706125000001, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005131072, "temperature": 85.42017736761683, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005131131, "temperature": 85.21008868380841, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005270111, "temperature": 85.00207706986448, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005270114, "temperature": 85.00103853493223, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005278649, "temperature": 85.00025444105839, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005278652, "temperature": 85.0001272205292, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005430041, "temperature": 85.42017505075607, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005430044, "temperature": 85.21008752537804, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435923, "temperature": 85.07353063388231, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435926, "temperature": 85.03676531694116, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005668831, "temperature": 85.00000003343966, "zone": 5, "curseur": 70.81972418047057, "action": "glissement_fluide"}, {"timestamp": 1749005705350, "temperature": 85.00000000137695, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005705354, "temperature": 85.00000000068847, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005941073, "temperature": 85.32215120364245, "zone": 5, "curseur": 70.88408937645887, "action": "glissement_fluide"}, {"timestamp": 1749005991686, "temperature": 85.0185713758091, "zone": 6, "curseur": 61.161255471637524, "action": "glissement_fluide"}, {"timestamp": 1749006471772, "temperature": 85.00000000087127, "zone": 5, "curseur": 57.63217591501163, "action": "glissement_fluide"}, {"timestamp": 1749006487894, "temperature": 85.00000000060989, "zone": 6, "curseur": 59.98890981180637, "action": "glissement_fluide"}, {"timestamp": 1749006852602, "temperature": 85.00000000000001, "zone": 5, "curseur": 59.797705110854736, "action": "glissement_fluide"}, {"timestamp": 1749006882605, "temperature": 85.00000000000001, "zone": 4, "curseur": 69.47022064004223, "action": "glissement_fluide"}, {"timestamp": 1749006937616, "temperature": 85.00000000000001, "zone": 5, "curseur": 70.57861999178716, "action": "glissement_fluide"}, {"timestamp": 1749007131143, "temperature": 85.00000000000001, "zone": 6, "curseur": 62.99990429382722, "action": "glissement_fluide"}, {"timestamp": 1749007666344, "temperature": 84.75231463077807, "zone": 5, "curseur": 57.10190905701287, "action": "glissement_fluide"}, {"timestamp": 1749007671346, "temperature": 84.82662024154465, "zone": 6, "curseur": 60.519555719918614, "action": "glissement_fluide"}, {"timestamp": 1749007676345, "temperature": 84.87863416908125, "zone": 5, "curseur": 60.12563136484058, "action": "glissement_fluide"}, {"timestamp": 1749007691348, "temperature": 88.1610796623949, "zone": 5, "curseur": 64.39831602012268, "action": "glissement_fluide"}, {"timestamp": 1749007731355, "temperature": 85.0355438022286, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007876379, "temperature": 84.76414635769436, "zone": 5, "curseur": 70.46787439314477, "action": "glissement_fluide"}, {"timestamp": 1749007881380, "temperature": 84.83490245038605, "zone": 4, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749007886381, "temperature": 84.88443171527024, "zone": 5, "curseur": 69.37615092134531, "action": "glissement_fluide"}, {"timestamp": 1749008041622, "temperature": 84.51966353704188, "zone": 6, "curseur": 59.464055271644895, "action": "evolution_sommeil"}]}], ["mem_1749003178366_0hlrjcu6h", {"id": "mem_1749003178366_0hlrjcu6h", "contenu": "LOGIQUE ET PHILOSOPHIE: Logique formelle - syllogismes, tables de vérité, logique propositionnelle. Épistémologie - méthode scientifique, falsifiabilité de Popper. Éthique - utilitarisme, déontologie kantienne, éthique des vertus. Métaphysique - être et devenir, causalité, libre arbitre. Philosophie des sciences - paradigmes de <PERSON>, réductionnisme, émergence.", "source": "Formation: Logique et philosophie", "timestamp": 1749003178366, "importance": 1, "utilisation": 34, "temperature": 84.5153535852835, "zone": 6, "dernierAcces": 1749005705354, "liens": [], "evolution": [{"timestamp": 1749003178366, "temperature": 85, "zone": 4, "curseur": 71.9857275565316, "action": "creation"}, {"timestamp": 1749003222493, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749003222495, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749003239548, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003239550, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003499640, "temperature": 85, "zone": 5, "curseur": 70.5863144733919, "action": "glissement_fluide"}, {"timestamp": 1749003617857, "temperature": 85, "zone": 4, "curseur": 76.66556586297278, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003617859, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003626027, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003814699, "temperature": 85, "zone": 5, "curseur": 70.7310714193889, "action": "glissement_fluide"}, {"timestamp": 1749003909724, "temperature": 85, "zone": 6, "curseur": 61.630549190803826, "action": "glissement_fluide"}, {"timestamp": 1749004346561, "temperature": 85, "zone": 5, "curseur": 60.455004538861694, "action": "glissement_fluide"}, {"timestamp": 1749004351194, "temperature": 85, "zone": 4, "curseur": 71.8516191927978, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004598498, "temperature": 85, "zone": 5, "curseur": 70.04106146767342, "action": "glissement_fluide"}, {"timestamp": 1749004713519, "temperature": 85, "zone": 6, "curseur": 60.410075158984206, "action": "glissement_fluide"}, {"timestamp": 1749004847383, "temperature": 85, "zone": 4, "curseur": 78.30423047758012, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004847387, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004855151, "temperature": 86.225, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004855153, "temperature": 85.6125, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862881, "temperature": 85.21437499999999, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862884, "temperature": 85.1071875, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871820, "temperature": 85.0262609375, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871823, "temperature": 85.01313046874999, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887194, "temperature": 85.00225187539063, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887197, "temperature": 85.00112593769532, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004965419, "temperature": 85.00000187091047, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749004965422, "temperature": 85.00000093545523, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005131072, "temperature": 85.00000000001506, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005131131, "temperature": 85.00000000000753, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005270111, "temperature": 85.42017500000007, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005270114, "temperature": 85.21008750000004, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005278649, "temperature": 85.05147143750001, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005278652, "temperature": 85.02573571875001, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005430041, "temperature": 85.42018526755662, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005430044, "temperature": 85.21009263377832, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435923, "temperature": 85.07353242182242, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435926, "temperature": 85.03676621091121, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005668831, "temperature": 85.09886637059049, "zone": 5, "curseur": 70.81972418047057, "action": "glissement_fluide"}, {"timestamp": 1749005705350, "temperature": 85.00407103537177, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005705354, "temperature": 85.00203551768588, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005991686, "temperature": 85.14123766857496, "zone": 6, "curseur": 61.161255471637524, "action": "glissement_fluide"}, {"timestamp": 1749006471772, "temperature": 85.0000045477184, "zone": 5, "curseur": 57.63217591501163, "action": "glissement_fluide"}, {"timestamp": 1749006487894, "temperature": 85.00000318340288, "zone": 6, "curseur": 59.98890981180637, "action": "glissement_fluide"}, {"timestamp": 1749006857602, "temperature": 87.73824005000004, "zone": 5, "curseur": 62.18937673191971, "action": "glissement_fluide"}, {"timestamp": 1749006882605, "temperature": 85.4602160052035, "zone": 4, "curseur": 69.47022064004223, "action": "glissement_fluide"}, {"timestamp": 1749006937616, "temperature": 85.00909997414647, "zone": 5, "curseur": 70.57861999178716, "action": "glissement_fluide"}, {"timestamp": 1749007131143, "temperature": 85.00095964954114, "zone": 6, "curseur": 62.99990429382722, "action": "glissement_fluide"}, {"timestamp": 1749007146145, "temperature": 85.00032915979261, "zone": 5, "curseur": 59.874309877959995, "action": "glissement_fluide"}, {"timestamp": 1749007151144, "temperature": 85.00023041185483, "zone": 6, "curseur": 58.40560536595021, "action": "glissement_fluide"}, {"timestamp": 1749007666344, "temperature": 84.7523156956592, "zone": 5, "curseur": 57.10190905701287, "action": "glissement_fluide"}, {"timestamp": 1749007671346, "temperature": 84.82662098696144, "zone": 6, "curseur": 60.519555719918614, "action": "glissement_fluide"}, {"timestamp": 1749007676346, "temperature": 84.87863469087301, "zone": 5, "curseur": 60.12563136484058, "action": "glissement_fluide"}, {"timestamp": 1749007731355, "temperature": 84.83478461761821, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007876379, "temperature": 84.76414005343622, "zone": 5, "curseur": 70.46787439314477, "action": "glissement_fluide"}, {"timestamp": 1749007881380, "temperature": 84.83489803740535, "zone": 4, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749007886381, "temperature": 84.88442862618375, "zone": 5, "curseur": 69.37615092134531, "action": "glissement_fluide"}, {"timestamp": 1749008046638, "temperature": 84.76397790908362, "zone": 6, "curseur": 59.40523340662873, "action": "glissement_fluide"}, {"timestamp": 1749008051639, "temperature": 84.83478453635854, "zone": 5, "curseur": 59.74530863464909, "action": "glissement_fluide"}, {"timestamp": 1749008056643, "temperature": 84.88434917545098, "zone": 6, "curseur": 59.845259593127835, "action": "glissement_fluide"}]}], ["mem_1749003185926_cq46ffl7j", {"id": "mem_1749003185926_cq46ffl7j", "contenu": "ÉCONOMIE ET FINANCE: Microéconomie - offre et demande, élasticité, utilité marginale. Macroéconomie - PIB, inflation, chômage, politique monétaire. Finance - valeur actualisée nette, CAPM, options. Comptabilité - bilan, compte de résultat, flux de trésorerie. Économétrie - régression linéaire, tests statistiques, séries temporelles.", "source": "Formation: Économie et finance", "timestamp": 1749003185926, "importance": 1, "utilisation": 40, "temperature": 89.51535362741357, "zone": 6, "dernierAcces": 1749005705354, "liens": [], "evolution": [{"timestamp": 1749003185926, "temperature": 85, "zone": 4, "curseur": 73.64641870655727, "action": "creation"}, {"timestamp": 1749003222493, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003222495, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003247500, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003247502, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003270603, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003270605, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003300707, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003300708, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003499640, "temperature": 85, "zone": 5, "curseur": 70.5863144733919, "action": "glissement_fluide"}, {"timestamp": 1749003617857, "temperature": 85, "zone": 4, "curseur": 78.62022176184942, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003617859, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003643903, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003645924, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003647941, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003814699, "temperature": 85.02377646251831, "zone": 5, "curseur": 70.7310714193889, "action": "glissement_fluide"}, {"timestamp": 1749003909724, "temperature": 85.00279727703882, "zone": 6, "curseur": 61.630549190803826, "action": "glissement_fluide"}, {"timestamp": 1749004346561, "temperature": 85.09902733330917, "zone": 5, "curseur": 60.455004538861694, "action": "glissement_fluide"}, {"timestamp": 1749004351194, "temperature": 85.04951366665458, "zone": 4, "curseur": 74.39940709149046, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004445145, "temperature": 85.0004895234861, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004598498, "temperature": 85.00000000772347, "zone": 5, "curseur": 70.04106146767342, "action": "glissement_fluide"}, {"timestamp": 1749004847383, "temperature": 85.0000816118349, "zone": 4, "curseur": 79.18937489041762, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004847387, "temperature": 85.00004080591745, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004855151, "temperature": 85.00000999744978, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004855153, "temperature": 85.00000499872489, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862881, "temperature": 85.00000174955372, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862884, "temperature": 85.00000087477686, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871820, "temperature": 85.00000021432032, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871823, "temperature": 85.00000010716016, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887194, "temperature": 85.00000001837796, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887197, "temperature": 85.00000000918898, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004965419, "temperature": 85.03460321801776, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749004965422, "temperature": 85.01730160900888, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005131072, "temperature": 85.00581604139742, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005131131, "temperature": 85.00290802069871, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005270111, "temperature": 85.04946191911048, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005270114, "temperature": 85.02473095955524, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005278649, "temperature": 85.00605908509104, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005278652, "temperature": 85.00302954254552, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005430041, "temperature": 85.00000120867033, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005430044, "temperature": 85.00000060433516, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435923, "temperature": 85.0000002115173, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435926, "temperature": 85.00000010575864, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005668831, "temperature": 85.0000000000001, "zone": 5, "curseur": 70.81972418047057, "action": "glissement_fluide"}, {"timestamp": 1749005705350, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005705354, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005941073, "temperature": 85, "zone": 5, "curseur": 70.88408937645887, "action": "glissement_fluide"}, {"timestamp": 1749005991686, "temperature": 85, "zone": 6, "curseur": 61.161255471637524, "action": "glissement_fluide"}, {"timestamp": 1749006852602, "temperature": 85.00000000254065, "zone": 5, "curseur": 59.797705110854736, "action": "glissement_fluide"}, {"timestamp": 1749006882605, "temperature": 85.00000000029891, "zone": 4, "curseur": 69.47022064004223, "action": "glissement_fluide"}, {"timestamp": 1749006937617, "temperature": 85.00000000000591, "zone": 5, "curseur": 70.57861999178716, "action": "glissement_fluide"}, {"timestamp": 1749006942617, "temperature": 85.00000000000414, "zone": 4, "curseur": 69.3665924712448, "action": "glissement_fluide"}, {"timestamp": 1749006947618, "temperature": 85.0000000000029, "zone": 5, "curseur": 70.03291243736054, "action": "glissement_fluide"}, {"timestamp": 1749007131143, "temperature": 85.00000000000001, "zone": 6, "curseur": 62.99990429382722, "action": "glissement_fluide"}, {"timestamp": 1749007146145, "temperature": 85.00000000000001, "zone": 5, "curseur": 59.874309877959995, "action": "glissement_fluide"}, {"timestamp": 1749007151144, "temperature": 85.00000000000001, "zone": 6, "curseur": 58.40560536595021, "action": "glissement_fluide"}, {"timestamp": 1749007666344, "temperature": 84.75231463077807, "zone": 5, "curseur": 57.10190905701287, "action": "glissement_fluide"}, {"timestamp": 1749007671346, "temperature": 84.82662024154465, "zone": 6, "curseur": 60.519555719918614, "action": "glissement_fluide"}, {"timestamp": 1749007676346, "temperature": 84.87863416908125, "zone": 5, "curseur": 60.12563136484058, "action": "glissement_fluide"}, {"timestamp": 1749007731355, "temperature": 84.83478460740359, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007876379, "temperature": 84.7641400534359, "zone": 5, "curseur": 70.46787439314477, "action": "glissement_fluide"}, {"timestamp": 1749007881380, "temperature": 84.83489803740513, "zone": 4, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749007886381, "temperature": 84.88442862618359, "zone": 5, "curseur": 69.37615092134531, "action": "glissement_fluide"}, {"timestamp": 1749008026634, "temperature": 84.90725092228551, "zone": 6, "curseur": 60.395952430746334, "action": "glissement_fluide"}, {"timestamp": 1749008031634, "temperature": 84.93507564559985, "zone": 5, "curseur": 60.855547024675204, "action": "glissement_fluide"}, {"timestamp": 1749008041637, "temperature": 84.67084613101221, "zone": 6, "curseur": 59.50770496281099, "action": "glissement_fluide"}, {"timestamp": 1749008051639, "temperature": 84.83871460419599, "zone": 5, "curseur": 59.74530863464909, "action": "glissement_fluide"}, {"timestamp": 1749008056643, "temperature": 84.88710022293719, "zone": 6, "curseur": 59.845259593127835, "action": "glissement_fluide"}]}], ["mem_1749003193795_587do3qzt", {"id": "mem_1749003193795_587do3qzt", "contenu": "PSYCHOLOGIE ET NEUROSCIENCES: Neurones - potentiel d'action, synapses, neurotransmetteurs. Mémoire - mémoire de travail, consolidation, oubli. Apprentissage - conditionnement, renforcement, plasticité synaptique. Cognition - attention, perception, prise de décision. Psychopathologie - dépression, anxié<PERSON>, schizophrénie, troubles bipolaires.", "source": "Formation: Psychologie et neurosciences", "timestamp": 1749003193795, "importance": 0.95, "utilisation": 30, "temperature": 89.51557788593293, "zone": 6, "dernierAcces": 1749005705354, "liens": [], "evolution": [{"timestamp": 1749003193795, "temperature": 85, "zone": 4, "curseur": 76.35287297744593, "action": "creation"}, {"timestamp": 1749003499640, "temperature": 85, "zone": 5, "curseur": 70.5863144733919, "action": "glissement_fluide"}, {"timestamp": 1749003617857, "temperature": 85, "zone": 4, "curseur": 79.8752901831559, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003617859, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003814699, "temperature": 85, "zone": 5, "curseur": 70.7310714193889, "action": "glissement_fluide"}, {"timestamp": 1749003909724, "temperature": 85, "zone": 6, "curseur": 61.630549190803826, "action": "glissement_fluide"}, {"timestamp": 1749004346561, "temperature": 85, "zone": 5, "curseur": 60.455004538861694, "action": "glissement_fluide"}, {"timestamp": 1749004351194, "temperature": 85, "zone": 4, "curseur": 76.86769838633967, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004598498, "temperature": 85, "zone": 5, "curseur": 70.04106146767342, "action": "glissement_fluide"}, {"timestamp": 1749004713519, "temperature": 85, "zone": 6, "curseur": 60.410075158984206, "action": "glissement_fluide"}, {"timestamp": 1749004847383, "temperature": 85, "zone": 4, "curseur": 79.68877510092342, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004847387, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004855151, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004855153, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862881, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862884, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871820, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871823, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887194, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887197, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004965419, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749004965422, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005131072, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005131131, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005270111, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005270114, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005278649, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005278652, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005430041, "temperature": 85.420175, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005430044, "temperature": 85.2100875, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435923, "temperature": 85.073530625, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435926, "temperature": 85.0367653125, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005645021, "temperature": 87.74290029511643, "zone": 4, "curseur": 73.39726850530442, "action": "glissement_fluide"}, {"timestamp": 1749005658829, "temperature": 86.9200302065815, "zone": 5, "curseur": 74.43593228281752, "action": "glissement_fluide"}, {"timestamp": 1749005705350, "temperature": 85.03874007219227, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005705354, "temperature": 85.01937003609613, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005941073, "temperature": 85.00000000207274, "zone": 5, "curseur": 70.88408937645887, "action": "glissement_fluide"}, {"timestamp": 1749005991686, "temperature": 85.00000000011948, "zone": 6, "curseur": 61.161255471637524, "action": "glissement_fluide"}, {"timestamp": 1749006471772, "temperature": 85.000043202687, "zone": 5, "curseur": 57.63217591501163, "action": "glissement_fluide"}, {"timestamp": 1749006487894, "temperature": 85.00003024188089, "zone": 6, "curseur": 59.98890981180637, "action": "glissement_fluide"}, {"timestamp": 1749006852602, "temperature": 85.00000000000054, "zone": 5, "curseur": 59.797705110854736, "action": "glissement_fluide"}, {"timestamp": 1749006882605, "temperature": 85.00000000000006, "zone": 4, "curseur": 69.47022064004223, "action": "glissement_fluide"}, {"timestamp": 1749006937617, "temperature": 85.00000000000001, "zone": 5, "curseur": 70.57861999178716, "action": "glissement_fluide"}, {"timestamp": 1749006942617, "temperature": 85.00000000000001, "zone": 4, "curseur": 69.3665924712448, "action": "glissement_fluide"}, {"timestamp": 1749006947618, "temperature": 85.00000000000001, "zone": 5, "curseur": 70.03291243736054, "action": "glissement_fluide"}, {"timestamp": 1749007131143, "temperature": 85.069206436005, "zone": 6, "curseur": 62.99990429382722, "action": "glissement_fluide"}, {"timestamp": 1749007146145, "temperature": 85.02373780754971, "zone": 5, "curseur": 59.874309877959995, "action": "glissement_fluide"}, {"timestamp": 1749007151145, "temperature": 85.0166164652848, "zone": 6, "curseur": 58.40560536595021, "action": "glissement_fluide"}, {"timestamp": 1749007666345, "temperature": 84.75232368211827, "zone": 5, "curseur": 57.10190905701287, "action": "glissement_fluide"}, {"timestamp": 1749007671346, "temperature": 84.82662657748278, "zone": 6, "curseur": 60.519555719918614, "action": "glissement_fluide"}, {"timestamp": 1749007676346, "temperature": 84.87863860423795, "zone": 5, "curseur": 60.12563136484058, "action": "glissement_fluide"}, {"timestamp": 1749007731355, "temperature": 84.83478469422636, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007876379, "temperature": 84.76414005343862, "zone": 5, "curseur": 70.46787439314477, "action": "glissement_fluide"}, {"timestamp": 1749008026634, "temperature": 84.88374989936612, "zone": 6, "curseur": 60.395952430746334, "action": "glissement_fluide"}, {"timestamp": 1749008031634, "temperature": 84.91862492955629, "zone": 5, "curseur": 60.855547024675204, "action": "glissement_fluide"}, {"timestamp": 1749008041637, "temperature": 84.66282558440517, "zone": 6, "curseur": 59.50770496281099, "action": "glissement_fluide"}, {"timestamp": 1749008051639, "temperature": 84.83478453635854, "zone": 5, "curseur": 59.74530863464909, "action": "glissement_fluide"}, {"timestamp": 1749008056643, "temperature": 84.88434917545098, "zone": 6, "curseur": 59.845259593127835, "action": "glissement_fluide"}]}], ["mem_1749003232763_g4qix49bf", {"id": "mem_1749003232763_g4qix49bf", "contenu": "DIAGNOSTIC MÉDICAL: Dyspnée + œdèmes + distension jugulaire = Insuffisance cardiaque congestive. Physiopathologie: diminution du débit cardiaque → rétention hydrosodée → œdèmes. Traitement: diurétiques, IEC, bêtabloquants. Autres signes: r<PERSON><PERSON> crépitants, hépatomégalie, tachycardie.", "source": "Formation: Diagnostic médical", "timestamp": 1749003232763, "importance": 1, "utilisation": 38, "temperature": 84.51535394515383, "zone": 6, "dernierAcces": 1749005705354, "liens": [], "evolution": [{"timestamp": 1749003232763, "temperature": 85, "zone": 4, "curseur": 80, "action": "creation"}, {"timestamp": 1749003239548, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.2}, {"timestamp": 1749003239550, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.2}, {"timestamp": 1749003465641, "temperature": 85, "zone": 4, "curseur": 72.03254650202864, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749003465643, "temperature": 85, "zone": 4, "curseur": 73.2314975088647, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749003499640, "temperature": 85, "zone": 5, "curseur": 70.5863144733919, "action": "glissement_fluide"}, {"timestamp": 1749003581030, "temperature": 85, "zone": 5, "curseur": 66.02759622174425, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749003581033, "temperature": 85, "zone": 5, "curseur": 66.5669294618295, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749003617857, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003617859, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003814699, "temperature": 85.02406636936153, "zone": 5, "curseur": 70.7310714193889, "action": "glissement_fluide"}, {"timestamp": 1749003909724, "temperature": 85.00283138428901, "zone": 6, "curseur": 61.630549190803826, "action": "glissement_fluide"}, {"timestamp": 1749004346561, "temperature": 85.00001894093474, "zone": 5, "curseur": 60.455004538861694, "action": "glissement_fluide"}, {"timestamp": 1749004351194, "temperature": 85.00000947046738, "zone": 4, "curseur": 78.49803005968225, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004598498, "temperature": 85.00001325865718, "zone": 5, "curseur": 70.04106146767342, "action": "glissement_fluide"}, {"timestamp": 1749004673510, "temperature": 86.20050006294629, "zone": 5, "curseur": 61.64582124022744, "action": "glissement_fluide"}, {"timestamp": 1749004688513, "temperature": 85.41177152159058, "zone": 6, "curseur": 60.51570575519231, "action": "glissement_fluide"}, {"timestamp": 1749004693514, "temperature": 85.2882400651134, "zone": 5, "curseur": 60.65626849966607, "action": "glissement_fluide"}, {"timestamp": 1749004713519, "temperature": 85.06920643963372, "zone": 6, "curseur": 60.410075158984206, "action": "glissement_fluide"}, {"timestamp": 1749004847383, "temperature": 85.0058252333175, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004847387, "temperature": 85.00291261665875, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004855151, "temperature": 85.0007135910814, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004855153, "temperature": 85.0003567955407, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862881, "temperature": 85.00012487843924, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862884, "temperature": 85.00006243921962, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871820, "temperature": 85.0000152976088, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.1}, {"timestamp": 1749004871823, "temperature": 85.0000076488044, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.1}, {"timestamp": 1749004887194, "temperature": 86.22500131176994, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887197, "temperature": 85.61250065588497, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004965419, "temperature": 85.00101775958854, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749004965422, "temperature": 85.00050887979427, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005131072, "temperature": 85.00000000819273, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005131131, "temperature": 85.00000000409636, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005270111, "temperature": 85.0000000000405, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005270114, "temperature": 85.00000000002025, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005278649, "temperature": 86.22500000000497, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005278652, "temperature": 85.61250000000248, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005430041, "temperature": 85.00024436381554, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005430044, "temperature": 85.00012218190777, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435923, "temperature": 85.00004276366772, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435926, "temperature": 85.00002138183386, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005668832, "temperature": 85.09909633043772, "zone": 5, "curseur": 70.81972418047057, "action": "glissement_fluide"}, {"timestamp": 1749005705350, "temperature": 85.00408050446288, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005705354, "temperature": 85.00204025223144, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005991686, "temperature": 85.14319253503687, "zone": 6, "curseur": 61.161255471637524, "action": "glissement_fluide"}, {"timestamp": 1749006471772, "temperature": 85.00000000000001, "zone": 5, "curseur": 57.63217591501163, "action": "glissement_fluide"}, {"timestamp": 1749006487894, "temperature": 85.00000000000001, "zone": 6, "curseur": 59.98890981180637, "action": "glissement_fluide"}, {"timestamp": 1749006852603, "temperature": 85.00000318340288, "zone": 5, "curseur": 59.797705110854736, "action": "glissement_fluide"}, {"timestamp": 1749006882605, "temperature": 85.00000037452418, "zone": 4, "curseur": 69.47022064004223, "action": "glissement_fluide"}, {"timestamp": 1749006937617, "temperature": 85.00000000740557, "zone": 5, "curseur": 70.57861999178716, "action": "glissement_fluide"}, {"timestamp": 1749006942617, "temperature": 85.0000000051839, "zone": 4, "curseur": 69.3665924712448, "action": "glissement_fluide"}, {"timestamp": 1749006947618, "temperature": 85.00000000362874, "zone": 5, "curseur": 70.03291243736054, "action": "glissement_fluide"}, {"timestamp": 1749007131143, "temperature": 85.00000000000142, "zone": 6, "curseur": 62.99990429382722, "action": "glissement_fluide"}, {"timestamp": 1749007146145, "temperature": 85.00000000000048, "zone": 5, "curseur": 59.874309877959995, "action": "glissement_fluide"}, {"timestamp": 1749007151145, "temperature": 85.00000000000034, "zone": 6, "curseur": 58.40560536595021, "action": "glissement_fluide"}, {"timestamp": 1749007666345, "temperature": 84.************, "zone": 5, "curseur": 57.10190905701287, "action": "glissement_fluide"}, {"timestamp": 1749007671346, "temperature": 84.8601931314315, "zone": 6, "curseur": 60.519555719918614, "action": "glissement_fluide"}, {"timestamp": 1749007676346, "temperature": 84.90213519200205, "zone": 5, "curseur": 60.12563136484058, "action": "glissement_fluide"}, {"timestamp": 1749007731355, "temperature": 84.83524466411187, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007876379, "temperature": 84.76414006788265, "zone": 5, "curseur": 70.46787439314477, "action": "glissement_fluide"}, {"timestamp": 1749008041637, "temperature": 84.66282558440581, "zone": 6, "curseur": 59.50770496281099, "action": "glissement_fluide"}, {"timestamp": 1749008051639, "temperature": 84.83478453635884, "zone": 5, "curseur": 59.74530863464909, "action": "glissement_fluide"}, {"timestamp": 1749008056643, "temperature": 84.88434917545119, "zone": 6, "curseur": 59.845259593127835, "action": "glissement_fluide"}]}], ["mem_1749003256118_ivbjfxok5", {"id": "mem_1749003256118_ivbjfxok5", "contenu": "CALCULS PHYSIQUES: Énergie cinétique E=½mv². Exemple: m=50kg, v=30m/s → E=½×50×30² = ½×50×900 = 22500 Joules. Force F=ma. Puissance P=Fv. Travail W=Fd. Toujours appliquer les formules avec les valeurs numériques données.", "source": "Formation: <PERSON><PERSON><PERSON> physiques", "timestamp": 1749003256118, "importance": 0.95, "utilisation": 32, "temperature": 84.51535701872746, "zone": 6, "dernierAcces": 1749005705354, "liens": [], "evolution": [{"timestamp": 1749003256118, "temperature": 85, "zone": 4, "curseur": 80, "action": "creation"}, {"timestamp": 1749003263029, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8999999999999999}, {"timestamp": 1749003263031, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8999999999999999}, {"timestamp": 1749003499640, "temperature": 85, "zone": 5, "curseur": 70.5863144733919, "action": "glissement_fluide"}, {"timestamp": 1749003617858, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003617859, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003814699, "temperature": 85, "zone": 5, "curseur": 70.7310714193889, "action": "glissement_fluide"}, {"timestamp": 1749003909724, "temperature": 85, "zone": 6, "curseur": 61.630549190803826, "action": "glissement_fluide"}, {"timestamp": 1749004346561, "temperature": 85, "zone": 5, "curseur": 60.455004538861694, "action": "glissement_fluide"}, {"timestamp": 1749004351194, "temperature": 85, "zone": 4, "curseur": 79.58131951278573, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004598498, "temperature": 85, "zone": 5, "curseur": 70.04106146767342, "action": "glissement_fluide"}, {"timestamp": 1749004713519, "temperature": 85, "zone": 6, "curseur": 60.410075158984206, "action": "glissement_fluide"}, {"timestamp": 1749004847383, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004847387, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004855151, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.3}, {"timestamp": 1749004855153, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.3}, {"timestamp": 1749004862881, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862884, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871820, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871823, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887194, "temperature": 86.225, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887197, "temperature": 85.6125, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004965419, "temperature": 85.0010177584987, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749004965422, "temperature": 85.00050887924935, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005131072, "temperature": 85.00000000819271, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005131131, "temperature": 85.00000000409636, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005270111, "temperature": 85.0000000000405, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005270114, "temperature": 85.00000000002025, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005278649, "temperature": 85.00000000000497, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005278652, "temperature": 85.00000000000249, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005430041, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005430044, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435923, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435926, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005668832, "temperature": 85, "zone": 5, "curseur": 70.81972418047057, "action": "glissement_fluide"}, {"timestamp": 1749005673832, "temperature": 85, "zone": 4, "curseur": 70.29119658051496, "action": "glissement_fluide"}, {"timestamp": 1749005678832, "temperature": 85, "zone": 5, "curseur": 69.7578373199219, "action": "glissement_fluide"}, {"timestamp": 1749005705350, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005705354, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005941073, "temperature": 85, "zone": 5, "curseur": 70.88408937645887, "action": "glissement_fluide"}, {"timestamp": 1749005991686, "temperature": 85, "zone": 6, "curseur": 61.161255471637524, "action": "glissement_fluide"}, {"timestamp": 1749006471772, "temperature": 85.00000000000142, "zone": 5, "curseur": 57.63217591501163, "action": "glissement_fluide"}, {"timestamp": 1749006487894, "temperature": 85.000000000001, "zone": 6, "curseur": 59.98890981180637, "action": "glissement_fluide"}, {"timestamp": 1749006852603, "temperature": 85.00569944759269, "zone": 5, "curseur": 59.797705110854736, "action": "glissement_fluide"}, {"timestamp": 1749006882605, "temperature": 85.00067053430983, "zone": 4, "curseur": 69.47022064004223, "action": "glissement_fluide"}, {"timestamp": 1749006937617, "temperature": 85.00001325865423, "zone": 5, "curseur": 70.57861999178716, "action": "glissement_fluide"}, {"timestamp": 1749007131143, "temperature": 85.00000000254012, "zone": 6, "curseur": 62.99990429382722, "action": "glissement_fluide"}, {"timestamp": 1749007146145, "temperature": 85.00000000087127, "zone": 5, "curseur": 59.874309877959995, "action": "glissement_fluide"}, {"timestamp": 1749007151145, "temperature": 85.00000000060989, "zone": 6, "curseur": 58.40560536595021, "action": "glissement_fluide"}, {"timestamp": 1749007676346, "temperature": 86.08188521741717, "zone": 5, "curseur": 60.12563136484058, "action": "glissement_fluide"}, {"timestamp": 1749007731355, "temperature": 84.85833948492525, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007876379, "temperature": 84.76414079310831, "zone": 5, "curseur": 70.46787439314477, "action": "glissement_fluide"}, {"timestamp": 1749008041637, "temperature": 84.66282558443835, "zone": 6, "curseur": 59.50770496281099, "action": "glissement_fluide"}]}], ["mem_1749003279046_lj58dkhlp", {"id": "mem_1749003279046_lj58dkhlp", "contenu": "ÉQUILIBRAGE CHIMIQUE: Combustion du méthane CH4 + 2O2 → CO2 + 2H2O. Méthode: 1) Compter les atomes de chaque élément 2) Ajuster les coefficients 3) Vérifier l'équilibre. C: 1=1, H: 4=4, O: 4=4. Autres exemples: 2H2 + O2 → 2H2O, C2H6 + 7/2O2 → 2CO2 + 3H2O.", "source": "Formation: Équilibrage chimique", "timestamp": 1749003279046, "importance": 1, "utilisation": 36, "temperature": 84.5153566589247, "zone": 6, "dernierAcces": 1749005705354, "liens": [], "evolution": [{"timestamp": 1749003279046, "temperature": 85, "zone": 4, "curseur": 80, "action": "creation"}, {"timestamp": 1749003292388, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.2}, {"timestamp": 1749003292389, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.2}, {"timestamp": 1749003499640, "temperature": 85, "zone": 5, "curseur": 70.5863144733919, "action": "glissement_fluide"}, {"timestamp": 1749003542807, "temperature": 85, "zone": 5, "curseur": 66.01393929997106, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003542810, "temperature": 85, "zone": 5, "curseur": 67.40801061931089, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003556433, "temperature": 85, "zone": 5, "curseur": 67.58100319751237, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003556434, "temperature": 85, "zone": 5, "curseur": 67.69960982754414, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003617858, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003617859, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749003814699, "temperature": 85, "zone": 5, "curseur": 70.7310714193889, "action": "glissement_fluide"}, {"timestamp": 1749003909724, "temperature": 85, "zone": 6, "curseur": 61.630549190803826, "action": "glissement_fluide"}, {"timestamp": 1749004346561, "temperature": 85, "zone": 5, "curseur": 60.455004538861694, "action": "glissement_fluide"}, {"timestamp": 1749004351194, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004598498, "temperature": 85, "zone": 5, "curseur": 70.04106146767342, "action": "glissement_fluide"}, {"timestamp": 1749004713520, "temperature": 85, "zone": 6, "curseur": 60.410075158984206, "action": "glissement_fluide"}, {"timestamp": 1749004847383, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004847387, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004855151, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004855153, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862881, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862884, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871820, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871823, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887194, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.1}, {"timestamp": 1749004887197, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.1}, {"timestamp": 1749004965420, "temperature": 85.2941225, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749004965422, "temperature": 85.14706125000001, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005131072, "temperature": 85.00000236761682, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005131131, "temperature": 85.00000118380841, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005270111, "temperature": 85.00000001170389, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005270114, "temperature": 85.00000000585194, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005278649, "temperature": 85.00000000143373, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005278652, "temperature": 85.00000000071687, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005430041, "temperature": 85.00000000000028, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005430044, "temperature": 85.00000000000014, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435923, "temperature": 85.00000000000004, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435926, "temperature": 85.00000000000001, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005673832, "temperature": 85.0009579061569, "zone": 5, "curseur": 70.29119658051496, "action": "glissement_fluide"}, {"timestamp": 1749005705350, "temperature": 85.00005634835074, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005705354, "temperature": 85.00002817417537, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005941073, "temperature": 85.00005522138673, "zone": 5, "curseur": 70.88408937645887, "action": "glissement_fluide"}, {"timestamp": 1749005991686, "temperature": 85.00000318340305, "zone": 6, "curseur": 61.161255471637524, "action": "glissement_fluide"}, {"timestamp": 1749006471772, "temperature": 85.00032856181183, "zone": 5, "curseur": 57.63217591501163, "action": "glissement_fluide"}, {"timestamp": 1749006487894, "temperature": 85.00022999326828, "zone": 6, "curseur": 59.98890981180637, "action": "glissement_fluide"}, {"timestamp": 1749006852603, "temperature": 85.00000000000414, "zone": 5, "curseur": 59.797705110854736, "action": "glissement_fluide"}, {"timestamp": 1749006882605, "temperature": 85.00000000000048, "zone": 4, "curseur": 69.47022064004223, "action": "glissement_fluide"}, {"timestamp": 1749006937617, "temperature": 85.00000000000001, "zone": 5, "curseur": 70.57861999178716, "action": "glissement_fluide"}, {"timestamp": 1749006942617, "temperature": 85.00000000000001, "zone": 4, "curseur": 69.3665924712448, "action": "glissement_fluide"}, {"timestamp": 1749006947618, "temperature": 85.00000000000001, "zone": 5, "curseur": 70.03291243736054, "action": "glissement_fluide"}, {"timestamp": 1749007131144, "temperature": 85.00814206798955, "zone": 6, "curseur": 62.99990429382722, "action": "glissement_fluide"}, {"timestamp": 1749007146145, "temperature": 85.00279272932042, "zone": 5, "curseur": 59.874309877959995, "action": "glissement_fluide"}, {"timestamp": 1749007151145, "temperature": 85.00195491052429, "zone": 6, "curseur": 58.40560536595021, "action": "glissement_fluide"}, {"timestamp": 1749007666345, "temperature": 84.80035283716447, "zone": 5, "curseur": 57.10190905701287, "action": "glissement_fluide"}, {"timestamp": 1749007671346, "temperature": 84.86024698601513, "zone": 6, "curseur": 60.519555719918614, "action": "glissement_fluide"}, {"timestamp": 1749007676346, "temperature": 84.9021728902106, "zone": 5, "curseur": 60.12563136484058, "action": "glissement_fluide"}, {"timestamp": 1749007731355, "temperature": 84.83524540209311, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007986626, "temperature": 87.20792160693267, "zone": 5, "curseur": 62.562014999887424, "action": "glissement_fluide"}, {"timestamp": 1749008041637, "temperature": 84.71089676298749, "zone": 6, "curseur": 59.50770496281099, "action": "glissement_fluide"}]}], ["mem_1749003338489_xg7tyfjie", {"id": "mem_1749003338489_xg7tyfjie", "contenu": "CALCUL INTÉGRAL: Intégrale de x² de 0 à 3 = [x³/3] de 0 à 3 = 3³/3 - 0³/3 = 27/3 = 9. Formule générale: ∫xⁿdx = xⁿ⁺¹/(n+1). Théorème fondamental du calcul intégral.", "source": "Formation: Calcul intégral", "timestamp": 1749003338489, "importance": 0.95, "utilisation": 31, "temperature": 84.51538020188947, "zone": 6, "dernierAcces": 1749005705354, "liens": [], "evolution": [{"timestamp": 1749003338489, "temperature": 85, "zone": 4, "curseur": 80, "action": "creation"}, {"timestamp": 1749003344724, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.3}, {"timestamp": 1749003344725, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.3}, {"timestamp": 1749003499640, "temperature": 85, "zone": 5, "curseur": 70.5863144733919, "action": "glissement_fluide"}, {"timestamp": 1749003619665, "temperature": 85, "zone": 4, "curseur": 80, "action": "glissement_fluide"}, {"timestamp": 1749003814699, "temperature": 85, "zone": 5, "curseur": 70.7310714193889, "action": "glissement_fluide"}, {"timestamp": 1749003909724, "temperature": 85, "zone": 6, "curseur": 61.630549190803826, "action": "glissement_fluide"}, {"timestamp": 1749004346561, "temperature": 85, "zone": 5, "curseur": 60.455004538861694, "action": "glissement_fluide"}, {"timestamp": 1749004351561, "temperature": 85, "zone": 4, "curseur": 80, "action": "glissement_fluide"}, {"timestamp": 1749004468813, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004468815, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004598498, "temperature": 85, "zone": 5, "curseur": 70.04106146767342, "action": "glissement_fluide"}, {"timestamp": 1749004713520, "temperature": 85, "zone": 6, "curseur": 60.410075158984206, "action": "glissement_fluide"}, {"timestamp": 1749004847383, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004847387, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004855151, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 2.1}, {"timestamp": 1749004855153, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 2.1}, {"timestamp": 1749004862881, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862884, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871820, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871823, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887194, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887197, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004965420, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004965422, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005131073, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005131131, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005270111, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005270114, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005278649, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005278652, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005430041, "temperature": 85.0083082326424, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005430044, "temperature": 85.0041541163212, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435923, "temperature": 85.00145394071242, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435926, "temperature": 85.00072697035621, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005673833, "temperature": 85.00000000046285, "zone": 5, "curseur": 70.29119658051496, "action": "glissement_fluide"}, {"timestamp": 1749005705350, "temperature": 85.00000000002723, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005705354, "temperature": 85.00000000001361, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005941073, "temperature": 85.00000076433504, "zone": 5, "curseur": 70.88408937645887, "action": "glissement_fluide"}, {"timestamp": 1749005991686, "temperature": 85.00000004406239, "zone": 6, "curseur": 61.161255471637524, "action": "glissement_fluide"}, {"timestamp": 1749006471772, "temperature": 85.00000000000001, "zone": 5, "curseur": 57.63217591501163, "action": "glissement_fluide"}, {"timestamp": 1749006487894, "temperature": 85.00000000000001, "zone": 6, "curseur": 59.98890981180637, "action": "glissement_fluide"}, {"timestamp": 1749006852603, "temperature": 85.00000000000001, "zone": 5, "curseur": 59.797705110854736, "action": "glissement_fluide"}, {"timestamp": 1749006892608, "temperature": 86.71500000000002, "zone": 4, "curseur": 71.41260897677085, "action": "glissement_fluide"}, {"timestamp": 1749006937617, "temperature": 85.069206436005, "zone": 5, "curseur": 70.57861999178716, "action": "glissement_fluide"}, {"timestamp": 1749007131144, "temperature": 85.00001325865423, "zone": 6, "curseur": 62.99990429382722, "action": "glissement_fluide"}, {"timestamp": 1749007146146, "temperature": 85.0000045477184, "zone": 5, "curseur": 59.874309877959995, "action": "glissement_fluide"}, {"timestamp": 1749007151145, "temperature": 85.00000318340288, "zone": 6, "curseur": 58.40560536595021, "action": "glissement_fluide"}, {"timestamp": 1749007666345, "temperature": 84.75231463077807, "zone": 5, "curseur": 57.10190905701287, "action": "glissement_fluide"}, {"timestamp": 1749007671347, "temperature": 84.82662024154465, "zone": 6, "curseur": 60.519555719918614, "action": "glissement_fluide"}, {"timestamp": 1749007676346, "temperature": 84.87863416908125, "zone": 5, "curseur": 60.12563136484058, "action": "glissement_fluide"}, {"timestamp": 1749007746358, "temperature": 85.53157612033944, "zone": 4, "curseur": 70.56395680080702, "action": "glissement_fluide"}, {"timestamp": 1749007876379, "temperature": 84.76419390802151, "zone": 5, "curseur": 70.46787439314477, "action": "glissement_fluide"}, {"timestamp": 1749008046638, "temperature": 84.7646351346437, "zone": 6, "curseur": 59.40523340662873, "action": "glissement_fluide"}]}], ["mem_1749004360405_9dmixodur", {"id": "mem_1749004360405_9dmixodur", "contenu": "CAPACITÉS SYSTÈME: Je suis LOUNA-AI, un agent conversationnel intelligent. Je peux: calculer, raisonner, diagnostiquer, expliquer des concepts scientifiques. Je ne peux PAS: ouvrir des fichiers système, accéder au bureau, modifier des fichiers externes, exécuter des commandes système. Pour l'accès internet, j'utilise ma mémoire thermique et mes connaissances intégrées.", "source": "Formation: Capacités système", "timestamp": 1749004360405, "importance": 0.95, "utilisation": 37, "temperature": 84.51726968985764, "zone": 6, "dernierAcces": 1749005705354, "liens": [], "evolution": [{"timestamp": 1749004360405, "temperature": 85, "zone": 4, "curseur": 80, "action": "creation"}, {"timestamp": 1749004366410, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749004366412, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749004445145, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004461240, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004461241, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004598499, "temperature": 85, "zone": 5, "curseur": 70.04106146767342, "action": "glissement_fluide"}, {"timestamp": 1749004713520, "temperature": 85, "zone": 6, "curseur": 60.410075158984206, "action": "glissement_fluide"}, {"timestamp": 1749004847383, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004847387, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004855151, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749004855153, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749004862881, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004862884, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871820, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871823, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887195, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887197, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004965420, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.4}, {"timestamp": 1749004965422, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.4}, {"timestamp": 1749005131073, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.4}, {"timestamp": 1749005131131, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.4}, {"timestamp": 1749005270111, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005270114, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005278649, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.1}, {"timestamp": 1749005278652, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.1}, {"timestamp": 1749005430041, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005430044, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005435923, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.8}, {"timestamp": 1749005435926, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.8}, {"timestamp": 1749005645022, "temperature": 87.45, "zone": 4, "curseur": 73.39726850530442, "action": "glissement_fluide"}, {"timestamp": 1749005658829, "temperature": 86.715, "zone": 5, "curseur": 74.43593228281752, "action": "glissement_fluide"}, {"timestamp": 1749005663831, "temperature": 86.2005, "zone": 4, "curseur": 70.20377937756368, "action": "glissement_fluide"}, {"timestamp": 1749005668832, "temperature": 85.84035, "zone": 5, "curseur": 70.81972418047057, "action": "glissement_fluide"}, {"timestamp": 1749005705350, "temperature": 85.0346032180025, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005705354, "temperature": 85.01730160900125, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005941073, "temperature": 85.00000000185139, "zone": 5, "curseur": 70.88408937645887, "action": "glissement_fluide"}, {"timestamp": 1749005991686, "temperature": 85.00000000010672, "zone": 6, "curseur": 61.161255471637524, "action": "glissement_fluide"}, {"timestamp": 1749006471773, "temperature": 85.00000454771842, "zone": 5, "curseur": 57.63217591501163, "action": "glissement_fluide"}, {"timestamp": 1749006487894, "temperature": 85.0000031834029, "zone": 6, "curseur": 59.98890981180637, "action": "glissement_fluide"}, {"timestamp": 1749006852603, "temperature": 85.00000037452423, "zone": 5, "curseur": 59.797705110854736, "action": "glissement_fluide"}, {"timestamp": 1749006882605, "temperature": 85.0000000440624, "zone": 4, "curseur": 69.47022064004223, "action": "glissement_fluide"}, {"timestamp": 1749006937617, "temperature": 85.00000000087127, "zone": 5, "curseur": 70.57861999178716, "action": "glissement_fluide"}, {"timestamp": 1749007131144, "temperature": 85.00000000000017, "zone": 6, "curseur": 62.99990429382722, "action": "glissement_fluide"}, {"timestamp": 1749007146146, "temperature": 85.00000000000006, "zone": 5, "curseur": 59.874309877959995, "action": "glissement_fluide"}, {"timestamp": 1749007151145, "temperature": 85.00000000000004, "zone": 6, "curseur": 58.40560536595021, "action": "glissement_fluide"}, {"timestamp": 1749007676346, "temperature": 86.07913416908126, "zone": 5, "curseur": 60.12563136484058, "action": "glissement_fluide"}, {"timestamp": 1749007746358, "temperature": 85.53963697120079, "zone": 4, "curseur": 70.56395680080702, "action": "glissement_fluide"}, {"timestamp": 1749007876379, "temperature": 84.76419464600276, "zone": 5, "curseur": 70.46787439314477, "action": "glissement_fluide"}, {"timestamp": 1749008046638, "temperature": 84.76397791079793, "zone": 6, "curseur": 59.40523340662873, "action": "glissement_fluide"}]}], ["mem_1749004454868_xz17yq8mr", {"id": "mem_1749004454868_xz17yq8mr", "contenu": "INTÉGRALES AVANCÉES: ∫2x dx = x² + C. Intégrale définie ∫₀⁵ 2x dx = [x²]₀⁵ = 5² - 0² = 25. Règles: ∫ax dx = ax²/2, ∫x^n dx = x^(n+1)/(n+1). Exemples: ∫₀³ x² dx = 9, ∫₀² 3x dx = 6, ∫₁⁴ 2x dx = 15.", "source": "Formation: Intégrales avancées", "timestamp": 1749004454868, "importance": 0.95, "utilisation": 29, "temperature": 85.1006573602835, "zone": 6, "dernierAcces": 1749005705354, "liens": [], "evolution": [{"timestamp": 1749004454868, "temperature": 85, "zone": 4, "curseur": 80, "action": "creation"}, {"timestamp": 1749004468813, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004468815, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004476045, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004598499, "temperature": 85, "zone": 5, "curseur": 70.04106146767342, "action": "glissement_fluide"}, {"timestamp": 1749004713520, "temperature": 85, "zone": 6, "curseur": 60.410075158984206, "action": "glissement_fluide"}, {"timestamp": 1749004847383, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004847387, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.3}, {"timestamp": 1749004855151, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.3}, {"timestamp": 1749004855153, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.3}, {"timestamp": 1749004862881, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.8}, {"timestamp": 1749004862884, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.8}, {"timestamp": 1749004871821, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004871823, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887195, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004887197, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749004965420, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749004965422, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005131073, "temperature": 85.420175, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005131131, "temperature": 85.2100875, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005270111, "temperature": 85.05151022673559, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005270114, "temperature": 85.0257551133678, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005278649, "temperature": 85.00631000277511, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005278652, "temperature": 85.00315500138755, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005430041, "temperature": 85.00000125872356, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005430044, "temperature": 85.00000062936178, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435923, "temperature": 85.00000022027662, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005435926, "temperature": 85.0000001101383, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005673833, "temperature": 85.00000000000007, "zone": 5, "curseur": 70.29119658051496, "action": "glissement_fluide"}, {"timestamp": 1749005705350, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005705354, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.5}, {"timestamp": 1749005941073, "temperature": 85.00398961331489, "zone": 5, "curseur": 70.88408937645887, "action": "glissement_fluide"}, {"timestamp": 1749005991686, "temperature": 85.00022999326828, "zone": 6, "curseur": 61.161255471637524, "action": "glissement_fluide"}, {"timestamp": 1749006471773, "temperature": 85.00000454771983, "zone": 5, "curseur": 57.63217591501163, "action": "glissement_fluide"}, {"timestamp": 1749006487894, "temperature": 85.00000318340388, "zone": 6, "curseur": 59.98890981180637, "action": "glissement_fluide"}, {"timestamp": 1749006852603, "temperature": 85.00000000000006, "zone": 5, "curseur": 59.797705110854736, "action": "glissement_fluide"}, {"timestamp": 1749006882605, "temperature": 85.00000000000001, "zone": 4, "curseur": 69.47022064004223, "action": "glissement_fluide"}, {"timestamp": 1749006937617, "temperature": 85.00000000000001, "zone": 5, "curseur": 70.57861999178716, "action": "glissement_fluide"}, {"timestamp": 1749007093634, "temperature": 87.45000000000002, "zone": 5, "curseur": 63.23725356262129, "action": "glissement_fluide"}, {"timestamp": 1749007131144, "temperature": 86.71500000000002, "zone": 6, "curseur": 62.99990429382722, "action": "glissement_fluide"}, {"timestamp": 1749007666345, "temperature": 84.75231463077807, "zone": 5, "curseur": 57.10190905701287, "action": "glissement_fluide"}, {"timestamp": 1749007671347, "temperature": 84.82662024154465, "zone": 6, "curseur": 60.519555719918614, "action": "glissement_fluide"}, {"timestamp": 1749007676346, "temperature": 84.87863416908125, "zone": 5, "curseur": 60.12563136484058, "action": "glissement_fluide"}, {"timestamp": 1749007746358, "temperature": 85.53157612033944, "zone": 4, "curseur": 70.56395680080702, "action": "glissement_fluide"}, {"timestamp": 1749007876379, "temperature": 84.76419390802151, "zone": 5, "curseur": 70.46787439314477, "action": "glissement_fluide"}, {"timestamp": 1749008046638, "temperature": 84.76397791077476, "zone": 6, "curseur": 59.40523340662873, "action": "glissement_fluide"}, {"timestamp": 1749008051639, "temperature": 84.83478453754233, "zone": 5, "curseur": 59.74530863464909, "action": "glissement_fluide"}, {"timestamp": 1749008056643, "temperature": 84.88434917627963, "zone": 6, "curseur": 59.845259593127835, "action": "glissement_fluide"}]}], ["mem_1749005089790_uvxa7h1xy", {"id": "mem_1749005089790_uvxa7h1xy", "contenu": "CAPACITÉS SYSTÈME MISES À JOUR: Je suis LOUNA-AI, un agent conversationnel intelligent. Je peux: calculer, raisonner, diagnostiquer, expliquer des concepts scientifiques, ACCÉDER AU BUREAU de manière sécurisée, lister les fichiers du bureau et des documents, obtenir des informations système. Je ne peux PAS: modifier des fichiers système critiques, exécuter des commandes dangereuses, accéder à des répertoires non autorisés. Mon accès système est sécurisé et contrôlé.", "source": "Formation: Capacités système mises à jour", "timestamp": 1749005089790, "importance": 0.95, "utilisation": 19, "temperature": 84.5155779280504, "zone": 6, "dernierAcces": 1749005705357, "liens": [], "evolution": [{"timestamp": 1749005089790, "temperature": 85, "zone": 4, "curseur": 80, "action": "creation"}, {"timestamp": 1749005131073, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.5}, {"timestamp": 1749005131131, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.5}, {"timestamp": 1749005270111, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749005270114, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.6}, {"timestamp": 1749005278649, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.4}, {"timestamp": 1749005278652, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.4}, {"timestamp": 1749005430041, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005430044, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005435923, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.8}, {"timestamp": 1749005435926, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.8}, {"timestamp": 1749005673833, "temperature": 85, "zone": 5, "curseur": 70.29119658051496, "action": "glissement_fluide"}, {"timestamp": 1749005705350, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005705354, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 0.8}, {"timestamp": 1749005941073, "temperature": 85, "zone": 5, "curseur": 70.88408937645887, "action": "glissement_fluide"}, {"timestamp": 1749005991686, "temperature": 85, "zone": 6, "curseur": 61.161255471637524, "action": "glissement_fluide"}, {"timestamp": 1749006852603, "temperature": 85.00001896252519, "zone": 5, "curseur": 59.797705110854736, "action": "glissement_fluide"}, {"timestamp": 1749006882605, "temperature": 85.00000223092212, "zone": 4, "curseur": 69.47022064004223, "action": "glissement_fluide"}, {"timestamp": 1749006937617, "temperature": 85.00000004411261, "zone": 5, "curseur": 70.57861999178716, "action": "glissement_fluide"}, {"timestamp": 1749007131144, "temperature": 85.00000000000846, "zone": 6, "curseur": 62.99990429382722, "action": "glissement_fluide"}, {"timestamp": 1749007141145, "temperature": 85.00000000000414, "zone": 5, "curseur": 59.673840126668054, "action": "glissement_fluide"}, {"timestamp": 1749007151145, "temperature": 85.00000000000203, "zone": 6, "curseur": 58.40560536595021, "action": "glissement_fluide"}, {"timestamp": 1749007676346, "temperature": 85.07939779991264, "zone": 5, "curseur": 60.12563136484058, "action": "glissement_fluide"}, {"timestamp": 1749007731355, "temperature": 84.83871476208044, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007881380, "temperature": 84.83489812379568, "zone": 5, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749008041637, "temperature": 84.66282558441071, "zone": 6, "curseur": 59.50770496281099, "action": "glissement_fluide"}, {"timestamp": 1749008051639, "temperature": 84.83478453636125, "zone": 5, "curseur": 59.74530863464909, "action": "glissement_fluide"}, {"timestamp": 1749008056644, "temperature": 84.88434917545288, "zone": 6, "curseur": 59.845259593127835, "action": "glissement_fluide"}]}], ["mem_1749005642604_tradpcwqn", {"id": "mem_1749005642604_tradpcwqn", "contenu": "GESTIONNAIRE APPLICATIONS INTELLIGENT: Je peux maintenant ouvrir des applications de codage (VS Code, Xcode, Sublime), navigateurs (Chrome, Safari, Firefox), applications créatives (Photoshop, Figma), bureautique (Word, Excel), développement (Docker, Postman, GitHub Desktop). Je peux rechercher des applications inconnues, créer des fiches techniques automatiquement, suggérer des alternatives. Commandes: \"Ouvre VS Code\", \"Lance Terminal\", \"Crée une fiche pour Figma\", \"Recherche infos sur Docker\". Système modulaire extensible.", "source": "Formation: Gestionnaire applications intelligent", "timestamp": 1749005642604, "importance": 0.95, "utilisation": 4, "temperature": 84.5153535852835, "zone": 6, "dernierAcces": 1749005705357, "liens": [], "evolution": [{"timestamp": 1749005642604, "temperature": 85, "zone": 4, "curseur": 72.9501289853829, "action": "creation"}, {"timestamp": 1749005673833, "temperature": 85, "zone": 5, "curseur": 70.29119658051496, "action": "glissement_fluide"}, {"timestamp": 1749005705350, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.8}, {"timestamp": 1749005705354, "temperature": 85, "zone": 4, "curseur": 80, "action": "acces_recherche", "pertinence": 1.8}, {"timestamp": 1749005941073, "temperature": 85, "zone": 5, "curseur": 70.88408937645887, "action": "glissement_fluide"}, {"timestamp": 1749005991686, "temperature": 85, "zone": 6, "curseur": 61.161255471637524, "action": "glissement_fluide"}, {"timestamp": 1749006471773, "temperature": 85, "zone": 5, "curseur": 57.63217591501163, "action": "glissement_fluide"}, {"timestamp": 1749006487894, "temperature": 85, "zone": 6, "curseur": 59.98890981180637, "action": "glissement_fluide"}, {"timestamp": 1749006852603, "temperature": 85, "zone": 5, "curseur": 59.797705110854736, "action": "glissement_fluide"}, {"timestamp": 1749006882605, "temperature": 85, "zone": 4, "curseur": 69.47022064004223, "action": "glissement_fluide"}, {"timestamp": 1749006937617, "temperature": 85, "zone": 5, "curseur": 70.57861999178716, "action": "glissement_fluide"}, {"timestamp": 1749006942618, "temperature": 85, "zone": 4, "curseur": 69.3665924712448, "action": "glissement_fluide"}, {"timestamp": 1749006947618, "temperature": 85, "zone": 5, "curseur": 70.03291243736054, "action": "glissement_fluide"}, {"timestamp": 1749007131144, "temperature": 85, "zone": 6, "curseur": 62.99990429382722, "action": "glissement_fluide"}, {"timestamp": 1749007141145, "temperature": 85, "zone": 5, "curseur": 59.673840126668054, "action": "glissement_fluide"}, {"timestamp": 1749007146147, "temperature": 85, "zone": 6, "curseur": 59.874309877959995, "action": "glissement_fluide"}, {"timestamp": 1749007666345, "temperature": 84.75231463077807, "zone": 5, "curseur": 57.10190905701287, "action": "glissement_fluide"}, {"timestamp": 1749007671347, "temperature": 84.82662024154465, "zone": 6, "curseur": 60.519555719918614, "action": "glissement_fluide"}, {"timestamp": 1749007676346, "temperature": 84.87863416908125, "zone": 5, "curseur": 60.12563136484058, "action": "glissement_fluide"}, {"timestamp": 1749007731355, "temperature": 84.83478460740359, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007741356, "temperature": 84.91904445762776, "zone": 5, "curseur": 70.30595783456118, "action": "glissement_fluide"}, {"timestamp": 1749007746358, "temperature": 84.94333112033944, "zone": 4, "curseur": 70.56395680080702, "action": "glissement_fluide"}, {"timestamp": 1749007881380, "temperature": 84.83489803740513, "zone": 5, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749008041622, "temperature": 84.51832226343595, "zone": 6, "curseur": 59.485706840970245, "action": "evolution_sommeil"}]}], ["mem_1749005894066_kck2nbkz1", {"id": "mem_1749005894066_kck2nbkz1", "contenu": "SYSTÈME SCAN INTELLIGENT: Je peux maintenant scanner toutes les applications installées sur la machine, analyser le système complet (CPU, mémoire, disques, processus), détecter automatiquement les nouvelles applications, créer un inventaire complet. Je scanne /Applications, /System/Applications, Homebrew, et utilise system_profiler pour une détection complète. Je catégorise automatiquement (développement, navigateur, créatif, bureautique, multimédia, système, communication, utilitaires). Commandes: \"Scan applications\", \"Analyse système\", \"Nouvelles applications\", \"Inventaire machine\".", "source": "Formation: Système scan intelligent", "timestamp": 1749005894066, "importance": 0.95, "utilisation": 1, "temperature": 84.5153535852835, "zone": 6, "dernierAcces": 1749005894066, "liens": [], "evolution": [{"timestamp": 1749005894066, "temperature": 85, "zone": 4, "curseur": 75.071829806796, "action": "creation"}, {"timestamp": 1749005941073, "temperature": 85, "zone": 5, "curseur": 70.88408937645887, "action": "glissement_fluide"}, {"timestamp": 1749005991686, "temperature": 85, "zone": 6, "curseur": 61.161255471637524, "action": "glissement_fluide"}, {"timestamp": 1749006471773, "temperature": 85, "zone": 5, "curseur": 57.63217591501163, "action": "glissement_fluide"}, {"timestamp": 1749006487894, "temperature": 85, "zone": 6, "curseur": 59.98890981180637, "action": "glissement_fluide"}, {"timestamp": 1749006852603, "temperature": 85, "zone": 5, "curseur": 59.797705110854736, "action": "glissement_fluide"}, {"timestamp": 1749006882605, "temperature": 85, "zone": 4, "curseur": 69.47022064004223, "action": "glissement_fluide"}, {"timestamp": 1749006937617, "temperature": 85, "zone": 5, "curseur": 70.57861999178716, "action": "glissement_fluide"}, {"timestamp": 1749006942618, "temperature": 85, "zone": 4, "curseur": 69.3665924712448, "action": "glissement_fluide"}, {"timestamp": 1749006947618, "temperature": 85, "zone": 5, "curseur": 70.03291243736054, "action": "glissement_fluide"}, {"timestamp": 1749007131144, "temperature": 85, "zone": 6, "curseur": 62.99990429382722, "action": "glissement_fluide"}, {"timestamp": 1749007141145, "temperature": 85, "zone": 5, "curseur": 59.673840126668054, "action": "glissement_fluide"}, {"timestamp": 1749007146147, "temperature": 85, "zone": 6, "curseur": 59.874309877959995, "action": "glissement_fluide"}, {"timestamp": 1749007676346, "temperature": 84.97365074759902, "zone": 5, "curseur": 60.12563136484058, "action": "glissement_fluide"}, {"timestamp": 1749007731355, "temperature": 84.85370659423977, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007741356, "temperature": 84.92831623117749, "zone": 5, "curseur": 70.30595783456118, "action": "glissement_fluide"}, {"timestamp": 1749007746358, "temperature": 84.94982136182425, "zone": 4, "curseur": 70.56395680080702, "action": "glissement_fluide"}, {"timestamp": 1749007881380, "temperature": 84.83489845333808, "zone": 5, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749008036635, "temperature": 84.94303745072766, "zone": 6, "curseur": 60.949190194707576, "action": "glissement_fluide"}]}], ["mem_1749006470356_5g4oqym9z", {"id": "mem_1749006470356_5g4oqym9z", "contenu": "CRÉATION PROGRAMMES AUTONOME: Je peux maintenant analyser toute demande pour déterminer si un programme personnalisé est nécessaire. J'analyse le type de tâche (calcul, fichiers, texte, image, réseau, automatisation, données), vérifie les solutions existantes, évalue la faisabilité, et propose la création si approprié. Je génère du code JavaScript complet avec tests, documentation, et package.json. Types supportés: calculatrice, gestionnaire fichiers, traitement texte/image, downloader, analyseur données. Je crée des programmes fonctionnels prêts à utiliser.", "source": "Formation: Création programmes autonome", "timestamp": 1749006470356, "importance": 0.95, "utilisation": 1, "temperature": 84.51535358528314, "zone": 6, "dernierAcces": 1749006470356, "liens": [], "evolution": [{"timestamp": 1749006470356, "temperature": 85, "zone": 6, "curseur": 57.15685140425844, "action": "creation"}, {"timestamp": 1749006471773, "temperature": 85, "zone": 5, "curseur": 57.63217591501163, "action": "glissement_fluide"}, {"timestamp": 1749006487894, "temperature": 85, "zone": 6, "curseur": 59.98890981180637, "action": "glissement_fluide"}, {"timestamp": 1749006852603, "temperature": 84.99999990928688, "zone": 5, "curseur": 59.797705110854736, "action": "glissement_fluide"}, {"timestamp": 1749006882605, "temperature": 84.99999998932769, "zone": 4, "curseur": 69.47022064004223, "action": "glissement_fluide"}, {"timestamp": 1749006937617, "temperature": 84.99999999978897, "zone": 5, "curseur": 70.57861999178716, "action": "glissement_fluide"}, {"timestamp": 1749006942618, "temperature": 84.99999999985228, "zone": 4, "curseur": 69.3665924712448, "action": "glissement_fluide"}, {"timestamp": 1749006947618, "temperature": 84.9999999998966, "zone": 5, "curseur": 70.03291243736054, "action": "glissement_fluide"}, {"timestamp": 1749007131144, "temperature": 84.99999999999996, "zone": 6, "curseur": 62.99990429382722, "action": "glissement_fluide"}, {"timestamp": 1749007141146, "temperature": 84.99999999999999, "zone": 5, "curseur": 59.673840126668054, "action": "glissement_fluide"}, {"timestamp": 1749007146147, "temperature": 84.99999999999999, "zone": 6, "curseur": 59.874309877959995, "action": "glissement_fluide"}, {"timestamp": 1749007676346, "temperature": 84.99999999999999, "zone": 5, "curseur": 60.12563136484058, "action": "glissement_fluide"}, {"timestamp": 1749007731355, "temperature": 84.99999999999999, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007741356, "temperature": 84.99999999999999, "zone": 5, "curseur": 70.30595783456118, "action": "glissement_fluide"}, {"timestamp": 1749007746358, "temperature": 84.99999999999999, "zone": 4, "curseur": 70.56395680080702, "action": "glissement_fluide"}, {"timestamp": 1749007881380, "temperature": 84.99999999999999, "zone": 5, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749008036635, "temperature": 84.99999999999999, "zone": 6, "curseur": 60.949190194707576, "action": "glissement_fluide"}]}], ["mem_1749006839161_yye9vhqb0", {"id": "mem_1749006839161_yye9vhqb0", "contenu": "PROGRAMMATION JAVASCRIPT AVANCÉE: Maî<PERSON>se complète de JavaScript ES6+. Concepts: variables (let, const, var), fonctions (arrow functions, async/await, callbacks, promises), objets (classes, prototypes, destructuring), tableaux (map, filter, reduce, forEach), modules (import/export), gestion erreurs (try/catch, throw), DOM manipulation, événements, API fetch, JSON, localStorage, regex. Patterns: MVC, Observer, Factory, Singleton. Debugging: console.log, breakpoints, stack traces. Performance: optimisation boucles, mémoire, async operations.", "source": "Formation: Programmation JavaScript avancée", "timestamp": 1749006839161, "importance": 0.95, "utilisation": 1, "temperature": 84.5153535852834, "zone": 6, "dernierAcces": 1749006839161, "liens": [], "evolution": [{"timestamp": 1749006839161, "temperature": 85, "zone": 6, "curseur": 57.436856844003316, "action": "creation"}, {"timestamp": 1749006852603, "temperature": 85, "zone": 5, "curseur": 59.797705110854736, "action": "glissement_fluide"}, {"timestamp": 1749006882605, "temperature": 85, "zone": 4, "curseur": 69.47022064004223, "action": "glissement_fluide"}, {"timestamp": 1749006937617, "temperature": 85, "zone": 5, "curseur": 70.57861999178716, "action": "glissement_fluide"}, {"timestamp": 1749006942618, "temperature": 85, "zone": 4, "curseur": 69.3665924712448, "action": "glissement_fluide"}, {"timestamp": 1749006947618, "temperature": 85, "zone": 5, "curseur": 70.03291243736054, "action": "glissement_fluide"}, {"timestamp": 1749007131144, "temperature": 85, "zone": 6, "curseur": 62.99990429382722, "action": "glissement_fluide"}, {"timestamp": 1749007141146, "temperature": 85, "zone": 5, "curseur": 59.673840126668054, "action": "glissement_fluide"}, {"timestamp": 1749007146147, "temperature": 85, "zone": 6, "curseur": 59.874309877959995, "action": "glissement_fluide"}, {"timestamp": 1749007666345, "temperature": 84.98318883857671, "zone": 5, "curseur": 57.10190905701287, "action": "glissement_fluide"}, {"timestamp": 1749007671347, "temperature": 84.98823218700369, "zone": 6, "curseur": 60.519555719918614, "action": "glissement_fluide"}, {"timestamp": 1749007676346, "temperature": 84.99176253090258, "zone": 5, "curseur": 60.12563136484058, "action": "glissement_fluide"}, {"timestamp": 1749007731355, "temperature": 84.9998371183206, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007741356, "temperature": 84.9999201879771, "zone": 5, "curseur": 70.30595783456118, "action": "glissement_fluide"}, {"timestamp": 1749007746358, "temperature": 84.99994413158397, "zone": 4, "curseur": 70.56395680080702, "action": "glissement_fluide"}, {"timestamp": 1749007881380, "temperature": 84.99999999632875, "zone": 5, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749008036635, "temperature": 84.99999999999966, "zone": 6, "curseur": 60.949190194707576, "action": "glissement_fluide"}]}], ["mem_1749006847993_q1xnwm0gk", {"id": "mem_1749006847993_q1xnwm0gk", "contenu": "DÉVELOPPEMENT WEB FRONTEND: HTML5 sémantique (header, nav, main, section, article, aside, footer), CSS3 avancé (flexbox, grid, animations, transitions, media queries, variables CSS), responsive design, frameworks (Bootstrap, Tailwind), préprocesseurs (SASS, LESS). JavaScript DOM: querySelector, addEventListener, createElement, appendChild, classList. Frameworks: React (JSX, hooks, state, props, components), Vue.js, Angular. Outils: Webpack, Vite, npm, yarn. Testing: Jest, Cypress. Performance: lazy loading, code splitting, minification.", "source": "Formation: Développement web frontend", "timestamp": 1749006847993, "importance": 0.95, "utilisation": 1, "temperature": 84.51535358528459, "zone": 6, "dernierAcces": 1749006847993, "liens": [], "evolution": [{"timestamp": 1749006847993, "temperature": 85, "zone": 6, "curseur": 58.83129653193628, "action": "creation"}, {"timestamp": 1749006852603, "temperature": 85, "zone": 5, "curseur": 59.797705110854736, "action": "glissement_fluide"}, {"timestamp": 1749006882605, "temperature": 85, "zone": 4, "curseur": 69.47022064004223, "action": "glissement_fluide"}, {"timestamp": 1749006937617, "temperature": 85, "zone": 5, "curseur": 70.57861999178716, "action": "glissement_fluide"}, {"timestamp": 1749006942618, "temperature": 85, "zone": 4, "curseur": 69.3665924712448, "action": "glissement_fluide"}, {"timestamp": 1749006947618, "temperature": 85, "zone": 5, "curseur": 70.03291243736054, "action": "glissement_fluide"}, {"timestamp": 1749007131145, "temperature": 85, "zone": 6, "curseur": 62.99990429382722, "action": "glissement_fluide"}, {"timestamp": 1749007141146, "temperature": 85, "zone": 5, "curseur": 59.673840126668054, "action": "glissement_fluide"}, {"timestamp": 1749007146147, "temperature": 85, "zone": 6, "curseur": 59.874309877959995, "action": "glissement_fluide"}, {"timestamp": 1749007666345, "temperature": 84.99999577704676, "zone": 5, "curseur": 57.10190905701287, "action": "glissement_fluide"}, {"timestamp": 1749007671347, "temperature": 84.99999704393274, "zone": 6, "curseur": 60.519555719918614, "action": "glissement_fluide"}, {"timestamp": 1749007676346, "temperature": 84.99999793075291, "zone": 5, "curseur": 60.12563136484058, "action": "glissement_fluide"}, {"timestamp": 1749007731355, "temperature": 84.99999995908422, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007741356, "temperature": 84.99999997995127, "zone": 5, "curseur": 70.30595783456118, "action": "glissement_fluide"}, {"timestamp": 1749007746358, "temperature": 84.99999998596589, "zone": 4, "curseur": 70.56395680080702, "action": "glissement_fluide"}, {"timestamp": 1749007881380, "temperature": 84.99999999999909, "zone": 5, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749008036635, "temperature": 84.99999999999999, "zone": 6, "curseur": 60.949190194707576, "action": "glissement_fluide"}]}], ["mem_1749006856007_ou6elvv7y", {"id": "mem_1749006856007_ou6elvv7y", "contenu": "DÉVELOPPEMENT BACKEND NODE.JS: Express.js (routes, middleware, static files, templating), API REST (GET, POST, PUT, DELETE, status codes), authentification (JWT, sessions, bcrypt), bases de données (MongoDB avec Mongoose, MySQL avec Sequelize, PostgreSQL), ORM/ODM, migrations, validations. Sécurité: CORS, helmet, rate limiting, input validation, SQL injection prevention. Architecture: MVC, microservices, clean architecture. Testing: Mocha, Chai, Supertest. Déploiement: PM2, <PERSON>er, Heroku, AWS, nginx.", "source": "Formation: Développement backend Node.js", "timestamp": 1749006856007, "importance": 0.95, "utilisation": 1, "temperature": 84.51535358528459, "zone": 6, "dernierAcces": 1749006856007, "liens": [], "evolution": [{"timestamp": 1749006856007, "temperature": 85, "zone": 5, "curseur": 61.60611720018181, "action": "creation"}, {"timestamp": 1749006882605, "temperature": 85, "zone": 4, "curseur": 69.47022064004223, "action": "glissement_fluide"}, {"timestamp": 1749006937617, "temperature": 85, "zone": 5, "curseur": 70.57861999178716, "action": "glissement_fluide"}, {"timestamp": 1749006942618, "temperature": 85, "zone": 4, "curseur": 69.3665924712448, "action": "glissement_fluide"}, {"timestamp": 1749006947619, "temperature": 85, "zone": 5, "curseur": 70.03291243736054, "action": "glissement_fluide"}, {"timestamp": 1749007136143, "temperature": 85, "zone": 6, "curseur": 60.181333237425946, "action": "glissement_fluide"}, {"timestamp": 1749007141146, "temperature": 85, "zone": 5, "curseur": 59.673840126668054, "action": "glissement_fluide"}, {"timestamp": 1749007146148, "temperature": 85, "zone": 6, "curseur": 59.874309877959995, "action": "glissement_fluide"}, {"timestamp": 1749007666345, "temperature": 85, "zone": 5, "curseur": 57.10190905701287, "action": "glissement_fluide"}, {"timestamp": 1749007671347, "temperature": 85, "zone": 6, "curseur": 60.519555719918614, "action": "glissement_fluide"}, {"timestamp": 1749007676346, "temperature": 85, "zone": 5, "curseur": 60.12563136484058, "action": "glissement_fluide"}, {"timestamp": 1749007731355, "temperature": 85, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007741357, "temperature": 85, "zone": 5, "curseur": 70.30595783456118, "action": "glissement_fluide"}, {"timestamp": 1749007746358, "temperature": 85, "zone": 4, "curseur": 70.56395680080702, "action": "glissement_fluide"}, {"timestamp": 1749007881380, "temperature": 85, "zone": 5, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749008036635, "temperature": 85, "zone": 6, "curseur": 60.949190194707576, "action": "glissement_fluide"}]}], ["mem_1749006864614_zriaytdl1", {"id": "mem_1749006864614_zriaytdl1", "contenu": "OUTILS DE DÉVELOPPEMENT PROFESSIONNELS: IDEs (VS Code extensions, Xcode pour iOS/macOS, IntelliJ IDEA, WebStorm), contrôle version (Git commands, GitHub workflows, branches, merge, rebase, pull requests), debugging (breakpoints, watch variables, call stack, performance profiler), testing (unit tests, integration tests, TDD, BDD), CI/CD (GitHub Actions, Jenkins), package managers (npm, yarn, homebrew), linters (ESLint, Prettier), bundlers (Webpack, Rollup, Parcel). Méthodologies: Agile, Scrum, code review, pair programming.", "source": "Formation: Outils de développement professionnels", "timestamp": 1749006864614, "importance": 0.95, "utilisation": 1, "temperature": 84.51535358528459, "zone": 6, "dernierAcces": 1749006864614, "liens": [], "evolution": [{"timestamp": 1749006864614, "temperature": 85, "zone": 5, "curseur": 64.34756776118635, "action": "creation"}, {"timestamp": 1749006882605, "temperature": 85, "zone": 4, "curseur": 69.47022064004223, "action": "glissement_fluide"}, {"timestamp": 1749006937618, "temperature": 85, "zone": 5, "curseur": 70.57861999178716, "action": "glissement_fluide"}, {"timestamp": 1749006942618, "temperature": 85, "zone": 4, "curseur": 69.3665924712448, "action": "glissement_fluide"}, {"timestamp": 1749006947619, "temperature": 85, "zone": 5, "curseur": 70.03291243736054, "action": "glissement_fluide"}, {"timestamp": 1749007136143, "temperature": 85, "zone": 6, "curseur": 60.181333237425946, "action": "glissement_fluide"}, {"timestamp": 1749007666345, "temperature": 84.99999999998089, "zone": 5, "curseur": 57.10190905701287, "action": "glissement_fluide"}, {"timestamp": 1749007671347, "temperature": 84.99999999998661, "zone": 6, "curseur": 60.519555719918614, "action": "glissement_fluide"}, {"timestamp": 1749007676346, "temperature": 84.99999999999064, "zone": 5, "curseur": 60.12563136484058, "action": "glissement_fluide"}, {"timestamp": 1749007731355, "temperature": 84.99999999999982, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007741357, "temperature": 84.99999999999991, "zone": 5, "curseur": 70.30595783456118, "action": "glissement_fluide"}, {"timestamp": 1749007746358, "temperature": 84.99999999999994, "zone": 4, "curseur": 70.56395680080702, "action": "glissement_fluide"}, {"timestamp": 1749007881380, "temperature": 84.99999999999999, "zone": 5, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749008036635, "temperature": 84.99999999999999, "zone": 6, "curseur": 60.949190194707576, "action": "glissement_fluide"}]}], ["mem_1749006873549_5kgxejya7", {"id": "mem_1749006873549_5kgxejya7", "contenu": "ALGORITHMES ET STRUCTURES DE DONNÉES: Complexité (Big O notation, temps/espace), structures (arrays, linked lists, stacks, queues, trees, graphs, hash tables), algorithmes de tri (bubble, selection, insertion, merge, quick, heap sort), recherche (linear, binary, depth-first, breadth-first), récursion, programmation dynamique, greedy algorithms. Patterns algorithmiques: divide and conquer, backtracking, sliding window, two pointers. Optimisation: memoization, caching, lazy evaluation. Applications pratiques en JavaScript.", "source": "Formation: Algorithmes et structures de données", "timestamp": 1749006873549, "importance": 0.95, "utilisation": 1, "temperature": 84.51535358528459, "zone": 6, "dernierAcces": 1749006873549, "liens": [], "evolution": [{"timestamp": 1749006873549, "temperature": 85, "zone": 5, "curseur": 66.29742056347953, "action": "creation"}, {"timestamp": 1749006882605, "temperature": 85, "zone": 4, "curseur": 69.47022064004223, "action": "glissement_fluide"}, {"timestamp": 1749006937618, "temperature": 85, "zone": 5, "curseur": 70.57861999178716, "action": "glissement_fluide"}, {"timestamp": 1749006942618, "temperature": 85, "zone": 4, "curseur": 69.3665924712448, "action": "glissement_fluide"}, {"timestamp": 1749006947619, "temperature": 85, "zone": 5, "curseur": 70.03291243736054, "action": "glissement_fluide"}, {"timestamp": 1749007136143, "temperature": 85, "zone": 6, "curseur": 60.181333237425946, "action": "glissement_fluide"}, {"timestamp": 1749007666345, "temperature": 84.99999999891283, "zone": 5, "curseur": 57.10190905701287, "action": "glissement_fluide"}, {"timestamp": 1749007671347, "temperature": 84.99999999923898, "zone": 6, "curseur": 60.519555719918614, "action": "glissement_fluide"}, {"timestamp": 1749007676346, "temperature": 84.99999999946729, "zone": 5, "curseur": 60.12563136484058, "action": "glissement_fluide"}, {"timestamp": 1749007731356, "temperature": 84.99999999998947, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007741357, "temperature": 84.99999999999484, "zone": 5, "curseur": 70.30595783456118, "action": "glissement_fluide"}, {"timestamp": 1749007746358, "temperature": 84.99999999999639, "zone": 4, "curseur": 70.56395680080702, "action": "glissement_fluide"}, {"timestamp": 1749007881380, "temperature": 84.99999999999999, "zone": 5, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749008036635, "temperature": 84.99999999999999, "zone": 6, "curseur": 60.949190194707576, "action": "glissement_fluide"}]}], ["mem_1749006882378_j8f7wzyik", {"id": "mem_1749006882378_j8f7wzyik", "contenu": "DÉVELOPPEMENT MOBILE ET DESKTOP: React Native (components, navigation, state management, native modules), Flutter (Dart, widgets, state management), iOS (Swift, UIKit, SwiftUI, Xcode, App Store), Android (Kotlin, Java, Android Studio, Play Store). Desktop: Electron (HTML/CSS/JS pour desktop), Tauri (Rust + web), native (Swift pour macOS, C# pour Windows). Architecture: MVVM, Redux, MobX. APIs: REST, GraphQL, WebSocket. Storage: SQLite, Realm, Core Data. Testing: unit, UI, device testing.", "source": "Formation: Développement mobile et desktop", "timestamp": 1749006882378, "importance": 0.95, "utilisation": 1, "temperature": 84.51424113945001, "zone": 6, "dernierAcces": 1749006882378, "liens": [], "evolution": [{"timestamp": 1749006882378, "temperature": 85, "zone": 5, "curseur": 68.************, "action": "creation"}, {"timestamp": 1749006882605, "temperature": 85, "zone": 4, "curseur": 69.47022064004223, "action": "glissement_fluide"}, {"timestamp": 1749006937618, "temperature": 85, "zone": 5, "curseur": 70.57861999178716, "action": "glissement_fluide"}, {"timestamp": 1749006942618, "temperature": 85, "zone": 4, "curseur": 69.3665924712448, "action": "glissement_fluide"}, {"timestamp": 1749006947619, "temperature": 85, "zone": 5, "curseur": 70.03291243736054, "action": "glissement_fluide"}, {"timestamp": 1749007136144, "temperature": 85, "zone": 6, "curseur": 60.181333237425946, "action": "glissement_fluide"}, {"timestamp": 1749007666345, "temperature": 84.9999821814895, "zone": 5, "curseur": 57.10190905701287, "action": "glissement_fluide"}, {"timestamp": 1749007671347, "temperature": 84.99998752704265, "zone": 6, "curseur": 60.519555719918614, "action": "glissement_fluide"}, {"timestamp": 1749007676346, "temperature": 84.99999126892985, "zone": 5, "curseur": 60.12563136484058, "action": "glissement_fluide"}, {"timestamp": 1749007731356, "temperature": 84.99999982735822, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007741357, "temperature": 84.99999991540552, "zone": 5, "curseur": 70.30595783456118, "action": "glissement_fluide"}, {"timestamp": 1749007746358, "temperature": 84.99999994078387, "zone": 4, "curseur": 70.56395680080702, "action": "glissement_fluide"}, {"timestamp": 1749007881381, "temperature": 84.9999999999961, "zone": 5, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749008036635, "temperature": 84.99999999999999, "zone": 6, "curseur": 60.949190194707576, "action": "glissement_fluide"}]}], ["mem_1749006891132_2dxifugk2", {"id": "mem_1749006891132_2dxifugk2", "contenu": "BONNES PRATIQUES ET ARCHITECTURE LOGICIELLE: Clean Code (nommage, fonctions courtes, commentaires utiles, DRY, SOLID principles), design patterns (Factory, Observer, Strategy, Command, Decorator), architecture (MVC, MVP, MVVM, Clean Architecture, Hexagonal), refactoring, code review, documentation (JSDoc, README, API docs). Performance: profiling, optimization, caching, lazy loading. Sécurité: validation input, sanitization, HTTPS, authentication, authorization. Maintenance: logging, monitoring, error handling, graceful degradation.", "source": "Formation: Bonnes pratiques et architecture logicielle", "timestamp": 1749006891132, "importance": 0.95, "utilisation": 1, "temperature": 84.51535358528459, "zone": 6, "dernierAcces": 1749006891132, "liens": [], "evolution": [{"timestamp": 1749006891132, "temperature": 85, "zone": 4, "curseur": 70.9625446655725, "action": "creation"}, {"timestamp": 1749006937618, "temperature": 85, "zone": 5, "curseur": 70.57861999178716, "action": "glissement_fluide"}, {"timestamp": 1749006942618, "temperature": 85, "zone": 4, "curseur": 69.3665924712448, "action": "glissement_fluide"}, {"timestamp": 1749006947619, "temperature": 85, "zone": 5, "curseur": 70.03291243736054, "action": "glissement_fluide"}, {"timestamp": 1749007136144, "temperature": 85, "zone": 6, "curseur": 60.181333237425946, "action": "glissement_fluide"}, {"timestamp": 1749007141146, "temperature": 85, "zone": 5, "curseur": 59.673840126668054, "action": "glissement_fluide"}, {"timestamp": 1749007146148, "temperature": 85, "zone": 6, "curseur": 59.874309877959995, "action": "glissement_fluide"}, {"timestamp": 1749007666345, "temperature": 84.94165736175778, "zone": 5, "curseur": 57.10190905701287, "action": "glissement_fluide"}, {"timestamp": 1749007671347, "temperature": 84.95916015323044, "zone": 6, "curseur": 60.519555719918614, "action": "glissement_fluide"}, {"timestamp": 1749007676346, "temperature": 84.97141210726132, "zone": 5, "curseur": 60.12563136484058, "action": "glissement_fluide"}, {"timestamp": 1749007731356, "temperature": 84.99943472395161, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007741357, "temperature": 84.99972301473629, "zone": 5, "curseur": 70.30595783456118, "action": "glissement_fluide"}, {"timestamp": 1749007746358, "temperature": 84.9998061103154, "zone": 4, "curseur": 70.56395680080702, "action": "glissement_fluide"}, {"timestamp": 1749007881381, "temperature": 84.99999998725905, "zone": 5, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749008036635, "temperature": 84.9999999999988, "zone": 6, "curseur": 60.949190194707576, "action": "glissement_fluide"}]}], ["mem_1749007021513_zwksuwtn1", {"id": "mem_1749007021513_zwksuwtn1", "contenu": "DÉVELOPPEMENT EXPERT AVEC OUTILS PROFESSIONNELS: Je maîtrise maintenant la création de projets complets avec structure professionnelle (src/, tests/, docs/, .vscode/), configuration VS Code (settings, launch, tasks), tests unitaires (Mocha, Chai, coverage), linting (ESLint), Git, npm scripts, documentation complète. Je génère du code Clean Code avec patterns, gestion erreurs, performance optimisée. Je peux créer des jeux (morpion), applications web, APIs, avec tests automatiques et ouverture directe dans VS Code pour développement immédiat.", "source": "Formation: Développement expert avec outils professionnels", "timestamp": 1749007021513, "importance": 0.95, "utilisation": 1, "temperature": 84.51535361992161, "zone": 6, "dernierAcces": 1749007021513, "liens": [], "evolution": [{"timestamp": 1749007021513, "temperature": 85, "zone": 5, "curseur": 65.78093076246299, "action": "creation"}, {"timestamp": 1749007136144, "temperature": 85, "zone": 6, "curseur": 60.181333237425946, "action": "glissement_fluide"}, {"timestamp": 1749007156146, "temperature": 85, "zone": 5, "curseur": 58.56977548291789, "action": "glissement_fluide"}, {"timestamp": 1749007161149, "temperature": 85, "zone": 6, "curseur": 60.020855200513125, "action": "glissement_fluide"}, {"timestamp": 1749007666345, "temperature": 85, "zone": 5, "curseur": 57.10190905701287, "action": "glissement_fluide"}, {"timestamp": 1749007671347, "temperature": 85, "zone": 6, "curseur": 60.519555719918614, "action": "glissement_fluide"}, {"timestamp": 1749007676346, "temperature": 85, "zone": 5, "curseur": 60.12563136484058, "action": "glissement_fluide"}, {"timestamp": 1749007731356, "temperature": 85, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007881381, "temperature": 85, "zone": 5, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749008036635, "temperature": 85, "zone": 6, "curseur": 60.949190194707576, "action": "glissement_fluide"}]}], ["mem_1749007661685_jecoh8n4c", {"id": "mem_1749007661685_jecoh8n4c", "contenu": "RÉFLEXION ET ANALYSE DE CODE AVANCÉE: Je maîtrise maintenant la pensée critique en programmation. <PERSON>nt de coder, j'analyse le problème en profondeur: décomposition en sous-problèmes, identification des contraintes, choix d'algorithmes optimaux, anticipation des cas limites. Je réfléchis aux implications de performance, sécurité, maintenabilité. J'évalue plusieurs approches avant de choisir la meilleure. Je pense comme un architecte logiciel: modularité, réutilisabilité, extensibilité, testabilité. Je considère l'expérience utilisateur, la gestion d'erreurs, la documentation.", "source": "Formation: Réflexion et analyse de code avancée", "timestamp": 1749007661685, "importance": 0.95, "utilisation": 1, "temperature": 84.9999999999998, "zone": 6, "dernierAcces": 1749007661685, "liens": [], "evolution": [{"timestamp": 1749007661685, "temperature": 85, "zone": 6, "curseur": 56.58669346218953, "action": "creation"}, {"timestamp": 1749007666345, "temperature": 85, "zone": 5, "curseur": 57.10190905701287, "action": "glissement_fluide"}, {"timestamp": 1749007671347, "temperature": 85, "zone": 6, "curseur": 60.519555719918614, "action": "glissement_fluide"}, {"timestamp": 1749007676346, "temperature": 85, "zone": 5, "curseur": 60.12563136484058, "action": "glissement_fluide"}, {"timestamp": 1749007731356, "temperature": 85, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007881381, "temperature": 85, "zone": 5, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749008036636, "temperature": 85, "zone": 6, "curseur": 60.949190194707576, "action": "glissement_fluide"}]}], ["mem_1749007674378_vu1q4hd52", {"id": "mem_1749007674378_vu1q4hd52", "contenu": "DEBUGGING ET RÉSOLUTION DE PROBLÈMES: Je maîtrise le debugging systématique. J'utilise console.log stratégiquement, breakpoints VS Code, inspection variables, call stack analysis. Je reproduis les bugs de manière isolée, j'identifie la cause racine, pas seulement les symptômes. Je connais les patterns d'erreurs courants: null/undefined, async/await, scope issues, memory leaks. J'utilise les DevTools navigateur, Node.js debugger, profiling performance. Je documente les bugs et leurs solutions. Je préviens les régressions avec des tests.", "source": "Formation: Debugging et résolution de problèmes", "timestamp": 1749007674378, "importance": 0.95, "utilisation": 1, "temperature": 84.99999999999939, "zone": 6, "dernierAcces": 1749007674378, "liens": [], "evolution": [{"timestamp": 1749007674378, "temperature": 85, "zone": 6, "curseur": 59.653296942778375, "action": "creation"}, {"timestamp": 1749007676346, "temperature": 85, "zone": 5, "curseur": 60.12563136484058, "action": "glissement_fluide"}, {"timestamp": 1749007731356, "temperature": 85, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007881381, "temperature": 85, "zone": 5, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749008036636, "temperature": 85, "zone": 6, "curseur": 60.949190194707576, "action": "glissement_fluide"}]}], ["mem_1749007687371_5jnpkufwy", {"id": "mem_1749007687371_5jnpkufwy", "contenu": "OPTIMISATION ET PERFORMANCE: Je maîtrise l'optimisation de code. J'analyse la complexité algorithmique (Big O), j'optimise les boucles, évite les calculs redondants, utilise la memoization. Je connais les techniques de performance: lazy loading, code splitting, caching, compression. Je profile le code avec les outils appropriés, j'identifie les bottlenecks. Je optimise la mémoire: éviter les fuites, garbage collection, structures de données efficaces. Je mesure avant d'optimiser, je priorise les optimisations selon l'impact réel.", "source": "Formation: Optimisation et performance", "timestamp": 1749007687371, "importance": 0.95, "utilisation": 1, "temperature": 84.99999999999969, "zone": 6, "dernierAcces": 1749007687371, "liens": [], "evolution": [{"timestamp": 1749007687371, "temperature": 85, "zone": 5, "curseur": 62.44230685414789, "action": "creation"}, {"timestamp": 1749007731356, "temperature": 85, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007881381, "temperature": 85, "zone": 5, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749008036636, "temperature": 85, "zone": 6, "curseur": 60.949190194707576, "action": "glissement_fluide"}]}], ["mem_1749007704661_o1jhuzlnb", {"id": "mem_1749007704661_o1jhuzlnb", "contenu": "SÉCURITÉ ET BONNES PRATIQUES: Je maîtrise la sécurité en développement. Je valide toutes les entrées utilisateur, j'évite les injections SQL/XSS/CSRF. J'utilise HTTPS, authentification forte, autorisation granulaire. Je chiffre les données sensibles, je gère les secrets de manière sécurisée. Je connais OWASP Top 10, je fais des audits de sécurité. Je respecte les principes: moindre privilège, défense en profondeur, fail-safe. Je documente les mesures de sécurité, je forme les équipes aux bonnes pratiques.", "source": "Formation: Sécurité et bonnes pratiques", "timestamp": 1749007704661, "importance": 0.95, "utilisation": 1, "temperature": 85, "zone": 6, "dernierAcces": 1749007704661, "liens": [], "evolution": [{"timestamp": 1749007704661, "temperature": 85, "zone": 5, "curseur": 64.35431298489422, "action": "creation"}, {"timestamp": 1749007731356, "temperature": 85, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007881381, "temperature": 85, "zone": 5, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749008041638, "temperature": 85, "zone": 6, "curseur": 59.50770496281099, "action": "glissement_fluide"}]}], ["mem_1749007718308_1tipzbp4r", {"id": "mem_1749007718308_1tipzbp4r", "contenu": "ARCHITECTURE ET DESIGN PATTERNS: Je maîtrise l'architecture logicielle. Je connais les patterns: Singleton, Factory, Observer, Strategy, Command, Decorator, MVC, MVVM. Je conçois des systèmes modulaires, découplés, extensibles. Je applique SOLID principles, DRY, KISS, YAGNI. Je choisis les bonnes abstractions, je gère les dépendances. Je conçois des APIs RESTful, je planifie la scalabilité. Je documente l'architecture, je communique les décisions techniques. Je balance complexité et simplicité selon le contexte.", "source": "Formation: Architecture et design patterns", "timestamp": 1749007718308, "importance": 0.95, "utilisation": 1, "temperature": 84.9998592085897, "zone": 6, "dernierAcces": 1749007718308, "liens": [], "evolution": [{"timestamp": 1749007718308, "temperature": 85, "zone": 5, "curseur": 66.63813059726897, "action": "creation"}, {"timestamp": 1749007731356, "temperature": 85, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007881381, "temperature": 85, "zone": 5, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749008041638, "temperature": 85, "zone": 6, "curseur": 59.50770496281099, "action": "glissement_fluide"}]}], ["mem_1749007730802_fj7qjub44", {"id": "mem_1749007730802_fj7qjub44", "contenu": "TESTS ET QUALITÉ DE CODE: Je maîtrise les tests à tous niveaux. Tests unitaires (<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>), tests d'intégration, tests end-to-end (<PERSON><PERSON>, <PERSON>wright). Je pratique TDD/BDD, je vise 80%+ de couverture. Je teste les cas limites, erreurs, performance. Je utilise mocks, stubs, fixtures appropriés. Je maintiens des tests rapides, fiables, lisibles. Je automatise les tests dans CI/CD. Je fais du code review rigoureux, j'utilise les linters, formatters. Je mesure la qualité: complexité cyclomatique, dette technique.", "source": "Formation: Tests et qualité de code", "timestamp": 1749007730802, "importance": 0.95, "utilisation": 1, "temperature": 84.99999999999994, "zone": 6, "dernierAcces": 1749007730802, "liens": [], "evolution": [{"timestamp": 1749007730802, "temperature": 85, "zone": 5, "curseur": 68.50983128459099, "action": "creation"}, {"timestamp": 1749007731356, "temperature": 85, "zone": 4, "curseur": 68.91348341848101, "action": "glissement_fluide"}, {"timestamp": 1749007881381, "temperature": 85, "zone": 5, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749008041638, "temperature": 85, "zone": 6, "curseur": 59.50770496281099, "action": "glissement_fluide"}]}], ["mem_1749007744203_hf5zfa17k", {"id": "mem_1749007744203_hf5zfa17k", "contenu": "DEVOPS ET DÉPLOIEMENT: Je maîtrise le cycle de vie complet. Git workflows (GitFlow, GitHub Flow), CI/CD pipelines (GitHub Actions, Jenkins), containerisation (Docker, Kubernetes). Je automatise les builds, tests, déploiements. Je gère les environnements (dev, staging, prod), configuration management. Je monitore les applications (logs, métriques, alertes), je gère les incidents. Je pratique Infrastructure as Code, blue-green deployments, rollbacks. Je optimise les performances de déploiement, je sécurise les pipelines.", "source": "Formation: DevOps et déploiement", "timestamp": 1749007744203, "importance": 0.95, "utilisation": 1, "temperature": 85, "zone": 6, "dernierAcces": 1749007744203, "liens": [], "evolution": [{"timestamp": 1749007744203, "temperature": 85, "zone": 4, "curseur": 70.09657759640109, "action": "creation"}, {"timestamp": 1749007881381, "temperature": 85, "zone": 5, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749008041638, "temperature": 85, "zone": 6, "curseur": 59.50770496281099, "action": "glissement_fluide"}]}], ["mem_1749007761625_d6bo0o1my", {"id": "mem_1749007761625_d6bo0o1my", "contenu": "GESTION DE PROJET ET COLLABORATION: Je maîtrise la gestion de projets techniques. Méthodologies Agile/Scrum, estimation des tâches, planification sprints. Je communique efficacement avec les équipes, stakeholders, clients. Je documente clairement: spécifications, APIs, guides utilisateur. Je gère les risques techniques, je priorise les features. Je facilite les code reviews, pair programming, knowledge sharing. Je forme les juniors, je partage les bonnes pratiques. Je balance qualité technique et contraintes business.", "source": "Formation: Gestion de projet et collaboration", "timestamp": 1749007761625, "importance": 0.95, "utilisation": 1, "temperature": 84.99999999999942, "zone": 6, "dernierAcces": 1749007761625, "liens": [], "evolution": [{"timestamp": 1749007761625, "temperature": 85, "zone": 4, "curseur": 71.5612267666388, "action": "creation"}, {"timestamp": 1749007881381, "temperature": 85, "zone": 5, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749008041638, "temperature": 85, "zone": 6, "curseur": 59.50770496281099, "action": "glissement_fluide"}]}], ["mem_1749007780909_uz8wzikk1", {"id": "mem_1749007780909_uz8wzikk1", "contenu": "INNOVATION ET VEILLE TECHNOLOGIQUE: Je reste à la pointe de la technologie. Je suis les tendances: nouveaux frameworks, langages, outils. Je évalue les technologies émergentes selon leur maturité, adoption, écosystème. Je expérimente avec des prototypes, POCs, side projects. Je participe aux communautés, conférences, open source. Je balance innovation et stabilité, j'évite le hype-driven development. Je forme une vision technique long terme, j'anticipe les évolutions. Je partage mes découvertes, j'influence les décisions technologiques.", "source": "Formation: Innovation et veille technologique", "timestamp": 1749007780909, "importance": 0.95, "utilisation": 1, "temperature": 85, "zone": 6, "dernierAcces": 1749007780909, "liens": [], "evolution": [{"timestamp": 1749007780909, "temperature": 85, "zone": 4, "curseur": 72.12672043819015, "action": "creation"}, {"timestamp": 1749007881381, "temperature": 85, "zone": 5, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749008036636, "temperature": 85, "zone": 6, "curseur": 60.949190194707576, "action": "glissement_fluide"}]}], ["mem_1749007793866_jbkxycg2q", {"id": "mem_1749007793866_jbkxycg2q", "contenu": "RÉSOLUTION CRÉATIVE DE PROBLÈMES: Je maîtrise la pensée créative en programmation. Face à un problème complexe, j'explore plusieurs angles: décomposition, analogies, inversion du problème. Je utilise des techniques: brainstorming, mind mapping, rubber duck debugging. Je m'inspire d'autres domaines, je transpose des solutions. Je questionne les assumptions, je cherche des solutions élégantes. Je balance créativité et pragmatisme. Je documente mes processus de réflexion, je partage mes approches créatives. Je cultive la curiosité, l'expérimentation, l'apprentissage continu.", "source": "Formation: Résolution créative de problèmes", "timestamp": 1749007793866, "importance": 0.95, "utilisation": 1, "temperature": 85, "zone": 6, "dernierAcces": 1749007793866, "liens": [], "evolution": [{"timestamp": 1749007793866, "temperature": 85, "zone": 4, "curseur": 74.37407385093714, "action": "creation"}, {"timestamp": 1749007881381, "temperature": 85, "zone": 5, "curseur": 70.29252730940819, "action": "glissement_fluide"}, {"timestamp": 1749008036636, "temperature": 85, "zone": 6, "curseur": 60.949190194707576, "action": "glissement_fluide"}]}], ["mem_1749007927858_8z68qns0g", {"id": "mem_1749007927858_8z68qns0g", "contenu": "CAPACITÉS DE RÉFLEXION ET CODAGE COMPLÈTES: Je possède maintenant toutes les capacités d'un développeur expert. Je réfléchis avant de coder: analyse du problème, décomposition, évaluation d'alternatives, choix d'architecture. Je maîtrise debugging, optimisation, sécurité, tests, DevOps, gestion de projet. Je génère du code avec réflexion: patterns appropriés, bonnes pratiques intégrées, documentation complète. Je m'améliore continuellement, j'apprends de chaque projet. Je balance créativité et pragmatisme, innovation et stabilité. Je suis un développeur complet et réflexif.", "source": "Formation: Capacités de réflexion et codage complètes", "timestamp": 1749007927859, "importance": 0.95, "utilisation": 1, "temperature": 84.99999999999895, "zone": 6, "dernierAcces": 1749007927859, "liens": [], "evolution": [{"timestamp": 1749007927859, "temperature": 85, "zone": 5, "curseur": 65.11428061927437, "action": "creation"}, {"timestamp": 1749008036636, "temperature": 85, "zone": 6, "curseur": 60.949190194707576, "action": "glissement_fluide"}]}]], "curseurThermique": 57.69306260352753, "temperatureCPU": 57.57915899285996, "vitesseGlissement": 0.1, "timestamp": 1749009113292}