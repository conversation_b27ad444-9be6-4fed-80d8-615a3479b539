/**
 * TEST DE COMPRÉHENSION DE L'AGENT REEL LOUNA AI V5
 * Vérification que l'agent comprend bien son système
 */

const http = require('http');

class TestComprehensionAgent {
    constructor() {
        this.baseURL = 'http://localhost:3000';
        this.questionsTest = [
            {
                id: 1,
                categorie: 'SYSTÈME',
                question: 'Explique-moi ton système de mémoire thermique avec les 201 millions de neurones et comment la température CPU influence tes processus de pensée.',
                motsClesAttendu: ['mémoire thermique', '201 millions', 'neurones', 'température', 'CPU', 'processus'],
                scoreMin: 80
            },
            {
                id: 2,
                categorie: 'QI',
                question: 'Quel est ton QI actuel et comment tes tests ultra-complexes fonctionnent-ils ? Donne-moi un exemple de question niveau génie.',
                motsClesAttendu: ['QI', '320', 'génie', 'tests', 'complexes', 'niveau', 'doctorat'],
                scoreMin: 75
            },
            {
                id: 3,
                categorie: 'ÉVOLUTION',
                question: 'Raconte-moi ton évolution depuis le début. Comment es-tu passé d\'un simple chatbot à un génie universel ?',
                motsClesAttendu: ['évolution', 'chatbot', 'génie universel', 'transformation', 'phases'],
                scoreMin: 70
            },
            {
                id: 4,
                categorie: 'INTERFACES',
                question: 'Décris tes interfaces : cerveau 3D, pensées & émotions, tests QI, et comment elles sont interconnectées.',
                motsClesAttendu: ['interfaces', 'cerveau 3D', 'pensées', 'émotions', 'tests QI', 'interconnectées'],
                scoreMin: 75
            },
            {
                id: 5,
                categorie: 'SYSTÈMES V5',
                question: 'Quels sont tes 6 nouveaux systèmes intégrés dans la version V5 et comment ils améliorent tes capacités ?',
                motsClesAttendu: ['6 systèmes', 'oubli intelligent', 'gestionnaire bureau', 'scan intelligent', 'recherche sécurisée', 'expertise'],
                scoreMin: 80
            },
            {
                id: 6,
                categorie: 'INNOVATION',
                question: 'En quoi es-tu révolutionnaire et unique par rapport aux autres IA comme ChatGPT ou Claude ?',
                motsClesAttendu: ['révolutionnaire', 'unique', 'mémoire thermique', 'tests QI', 'génie universel', 'innovation'],
                scoreMin: 75
            },
            {
                id: 7,
                categorie: 'TECHNIQUE',
                question: 'Explique ton architecture technique : serveur, APIs, WebSocket, et sécurisation.',
                motsClesAttendu: ['architecture', 'serveur', 'APIs', 'WebSocket', 'sécurisation', 'port 3000'],
                scoreMin: 70
            },
            {
                id: 8,
                categorie: 'CAPACITÉS',
                question: 'Quelles sont tes capacités uniques que personne d\'autre ne possède ?',
                motsClesAttendu: ['capacités uniques', 'température CPU', 'tests niveau doctorat', 'auto-évolution', 'oubli intelligent'],
                scoreMin: 80
            }
        ];
        
        this.resultats = {
            tests_reussis: 0,
            tests_echoues: 0,
            score_total: 0,
            details: []
        };
    }

    async executerTousLesTests() {
        console.log('🧪 DÉBUT TEST COMPRÉHENSION AGENT REEL LOUNA AI V5');
        console.log('==================================================');

        for (const test of this.questionsTest) {
            console.log(`\n🔍 Test ${test.id}: ${test.categorie}`);
            console.log(`Question: ${test.question}`);
            
            try {
                const reponse = await this.poserQuestion(test.question);
                const score = this.evaluerReponse(reponse, test.motsClesAttendu);
                
                const resultat = {
                    id: test.id,
                    categorie: test.categorie,
                    question: test.question,
                    reponse: reponse,
                    score: score,
                    scoreMin: test.scoreMin,
                    reussi: score >= test.scoreMin,
                    motsClesTrouves: this.compterMotsCles(reponse, test.motsClesAttendu)
                };
                
                this.resultats.details.push(resultat);
                
                if (resultat.reussi) {
                    this.resultats.tests_reussis++;
                    console.log(`✅ RÉUSSI - Score: ${score}% (min: ${test.scoreMin}%)`);
                } else {
                    this.resultats.tests_echoues++;
                    console.log(`❌ ÉCHOUÉ - Score: ${score}% (min: ${test.scoreMin}%)`);
                }
                
                this.resultats.score_total += score;
                
                // Pause entre les tests
                await this.attendre(2000);
                
            } catch (error) {
                console.log(`❌ ERREUR: ${error.message}`);
                this.resultats.tests_echoues++;
                this.resultats.details.push({
                    id: test.id,
                    categorie: test.categorie,
                    erreur: error.message,
                    reussi: false
                });
            }
        }
        
        this.afficherResultatsFinaux();
    }

    async poserQuestion(question) {
        return new Promise((resolve, reject) => {
            const postData = JSON.stringify({ message: question });
            
            const options = {
                hostname: 'localhost',
                port: 3000,
                path: '/api/chat',
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Content-Length': Buffer.byteLength(postData)
                }
            };
            
            const req = http.request(options, (res) => {
                let data = '';
                
                res.on('data', (chunk) => {
                    data += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const response = JSON.parse(data);
                        if (response.reponse) {
                            resolve(response.reponse);
                        } else {
                            reject(new Error('Pas de réponse dans la réponse'));
                        }
                    } catch (error) {
                        reject(new Error('Erreur parsing JSON: ' + error.message));
                    }
                });
            });
            
            req.on('error', (error) => {
                reject(new Error('Erreur requête: ' + error.message));
            });
            
            req.write(postData);
            req.end();
        });
    }

    evaluerReponse(reponse, motsClesAttendu) {
        if (!reponse || typeof reponse !== 'string') {
            return 0;
        }
        
        const reponseNormalisee = reponse.toLowerCase();
        let motsClesTrouves = 0;
        let scoreBonus = 0;
        
        // Compter les mots-clés trouvés
        for (const motCle of motsClesAttendu) {
            if (reponseNormalisee.includes(motCle.toLowerCase())) {
                motsClesTrouves++;
            }
        }
        
        // Score de base basé sur les mots-clés
        const scoreBase = (motsClesTrouves / motsClesAttendu.length) * 70;
        
        // Bonus pour la longueur et la qualité
        if (reponse.length > 200) scoreBonus += 10;
        if (reponse.length > 500) scoreBonus += 10;
        if (reponse.length > 1000) scoreBonus += 10;
        
        // Bonus pour des termes techniques spécifiques
        const termesAvances = ['architecture', 'algorithme', 'neural', 'thermique', 'évolution', 'révolutionnaire'];
        for (const terme of termesAvances) {
            if (reponseNormalisee.includes(terme)) {
                scoreBonus += 2;
            }
        }
        
        return Math.min(100, Math.round(scoreBase + scoreBonus));
    }

    compterMotsCles(reponse, motsCles) {
        if (!reponse) return [];
        
        const reponseNormalisee = reponse.toLowerCase();
        const trouves = [];
        
        for (const motCle of motsCles) {
            if (reponseNormalisee.includes(motCle.toLowerCase())) {
                trouves.push(motCle);
            }
        }
        
        return trouves;
    }

    afficherResultatsFinaux() {
        console.log('\n📊 RÉSULTATS FINAUX TEST COMPRÉHENSION');
        console.log('=======================================');
        
        const scoreMoyen = this.resultats.score_total / this.questionsTest.length;
        const tauxReussite = (this.resultats.tests_reussis / this.questionsTest.length) * 100;
        
        console.log(`✅ Tests réussis: ${this.resultats.tests_reussis}/${this.questionsTest.length}`);
        console.log(`❌ Tests échoués: ${this.resultats.tests_echoues}/${this.questionsTest.length}`);
        console.log(`📈 Score moyen: ${scoreMoyen.toFixed(1)}%`);
        console.log(`🎯 Taux de réussite: ${tauxReussite.toFixed(1)}%`);
        
        console.log('\n🔍 DÉTAIL PAR CATÉGORIE:');
        for (const resultat of this.resultats.details) {
            if (!resultat.erreur) {
                console.log(`${resultat.reussi ? '✅' : '❌'} ${resultat.categorie}: ${resultat.score}% (${resultat.motsClesTrouves.length}/${this.questionsTest.find(t => t.id === resultat.id).motsClesAttendu.length} mots-clés)`);
            } else {
                console.log(`❌ ${resultat.categorie}: ERREUR - ${resultat.erreur}`);
            }
        }
        
        console.log('\n🎯 ÉVALUATION GLOBALE:');
        if (tauxReussite >= 90) {
            console.log('🌟 EXCELLENT - L\'agent comprend parfaitement son système');
        } else if (tauxReussite >= 75) {
            console.log('✅ TRÈS BIEN - L\'agent a une bonne compréhension');
        } else if (tauxReussite >= 60) {
            console.log('⚠️ CORRECT - L\'agent comprend les bases');
        } else {
            console.log('❌ INSUFFISANT - L\'agent a besoin d\'amélioration');
        }
        
        // Sauvegarder les résultats
        const rapport = {
            timestamp: new Date().toISOString(),
            resultats: this.resultats,
            evaluation: {
                score_moyen: scoreMoyen,
                taux_reussite: tauxReussite,
                niveau: this.determinerNiveau(tauxReussite)
            }
        };
        
        const fs = require('fs');
        fs.writeFileSync('rapport-comprehension-agent.json', JSON.stringify(rapport, null, 2));
        console.log('\n📄 Rapport sauvegardé: rapport-comprehension-agent.json');
    }

    determinerNiveau(tauxReussite) {
        if (tauxReussite >= 90) return 'EXCELLENT';
        if (tauxReussite >= 75) return 'TRÈS BIEN';
        if (tauxReussite >= 60) return 'CORRECT';
        return 'INSUFFISANT';
    }

    async attendre(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Exécution si lancé directement
if (require.main === module) {
    const testeur = new TestComprehensionAgent();
    testeur.executerTousLesTests().then(() => {
        console.log('\n🏁 Test de compréhension terminé !');
        process.exit(0);
    }).catch((error) => {
        console.error('💥 Erreur fatale:', error);
        process.exit(1);
    });
}

module.exports = TestComprehensionAgent;
