#!/usr/bin/env node

/**
 * SERVEUR SIMPLE POUR MODÈLE 18GB
 * AUCUN CHARGEMENT AUTOMATIQUE - UTILISE UNIQUEMENT VOTRE MODÈLE EXISTANT
 */

const express = require('express');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

class ServeurSimple18GB {
    constructor() {
        console.log('🚀 SERVEUR SIMPLE 18GB - AUCUN CHARGEMENT');
        console.log('==========================================');
        
        this.app = express();
        this.port = 3000;
        
        // Configuration fixe pour votre modèle 19GB
        this.config19GB = {
            modele: 'codellama:34b-instruct',
            nom: 'CodeLlama 34B Instruct (19GB)',
            taille: '19GB'
        };
        
        this.setupMiddleware();
        this.setupRoutes();
        this.demarrer();
    }

    setupMiddleware() {
        this.app.use(express.json());
        this.app.use(express.static('.'));
        
        // CORS simple
        this.app.use((req, res, next) => {
            res.header('Access-Control-Allow-Origin', '*');
            res.header('Access-Control-Allow-Headers', 'Content-Type');
            res.header('Access-Control-Allow-Methods', 'GET, POST');
            next();
        });
    }

    setupRoutes() {
        // Page d'accueil
        this.app.get('/', (req, res) => {
            res.redirect('/interface-louna-complete.html');
        });

        // API de test de l'agent 18GB
        this.app.post('/api/agent-message', async (req, res) => {
            try {
                const { message } = req.body;
                
                if (!message) {
                    return res.status(400).json({
                        success: false,
                        error: 'Message requis'
                    });
                }

                console.log(`🧠 Test modèle 19GB: ${message}`);

                // Test direct avec votre modèle 19GB
                const reponse = await this.testerModele19GB(message);

                res.json({
                    success: true,
                    reponse: reponse,
                    agent: 'Modèle 19GB Direct',
                    modele: this.config19GB.modele,
                    taille: this.config19GB.taille,
                    timestamp: new Date().toISOString()
                });

            } catch (error) {
                console.error('❌ Erreur test modèle 18GB:', error);
                res.status(500).json({
                    success: false,
                    error: error.message,
                    details: 'Erreur lors du test du modèle 18GB'
                });
            }
        });

        // API de statut
        this.app.get('/api/status', (req, res) => {
            res.json({
                success: true,
                serveur: 'Serveur Simple 18GB',
                modele: this.config19GB,
                timestamp: new Date().toISOString(),
                etat: {
                    qi_actuel: 369,
                    memoires: 104,
                    temperature: 67.57,
                    applications_detectees: 381
                }
            });
        });
    }

    async testerModele19GB(message) {
        try {
            console.log(`🔍 Test direct du modèle ${this.config19GB.modele}...`);
            
            // Charger la mémoire thermique 18GB si elle existe
            let memoireContext = '';
            try {
                const memoireData = fs.readFileSync('./memoire-thermique.json', 'utf8');
                const memoire = JSON.parse(memoireData);
                
                // Chercher la mémoire du transfert 18GB
                const transfert18GB = Object.values(memoire.memoires || {}).find(m => 
                    m && m.source === 'transfert_18gb'
                );
                
                if (transfert18GB) {
                    memoireContext = `\n🧠 MÉMOIRE 18GB: ${transfert18GB.contenu}\n`;
                    console.log('✅ Mémoire thermique 18GB chargée');
                }
            } catch (error) {
                console.log('⚠️ Mémoire thermique non accessible');
            }

            // Requête directe à Ollama avec votre modèle 19GB
            const response = await axios.post('http://localhost:11434/api/generate', {
                model: this.config19GB.modele,
                prompt: `Tu es LOUNA-AI, un assistant intelligent créé par Jean-Luc Passave à Sainte-Anne, Guadeloupe.

🚀 MODÈLE ACTUEL: ${this.config19GB.nom} (${this.config19GB.taille})
${memoireContext}

Question: ${message}

Réponds de manière intelligente et précise. Confirme ton modèle et tes capacités.`,
                stream: false,
                options: {
                    temperature: 0.7,
                    top_p: 0.9,
                    num_predict: 500
                }
            }, {
                timeout: 30000
            });

            if (response.data && response.data.response) {
                console.log(`✅ Réponse ${this.config19GB.nom} reçue: ${response.data.response.substring(0, 100)}...`);
                return response.data.response;
            } else {
                throw new Error('Pas de réponse du modèle');
            }

        } catch (error) {
            console.log(`❌ ERREUR: Modèle 19GB non accessible !`);
            console.log(`🔧 Vérifiez que votre modèle ${this.config19GB.modele} est bien installé avec: ollama list`);

            return `❌ Erreur: Le modèle 19GB (${this.config19GB.modele}) n'est pas accessible.

Vérifications nécessaires :
1. Ollama fonctionne : ollama list
2. Le modèle ${this.config19GB.modele} est installé
3. Le modèle est bien de 19GB

Erreur technique: ${error.message}

🔧 Pour installer le modèle : ollama pull ${this.config19GB.modele}`;
        }
    }

    demarrer() {
        this.app.listen(this.port, () => {
            console.log('');
            console.log('✅ SERVEUR SIMPLE 18GB DÉMARRÉ');
            console.log('==============================');
            console.log(`🌐 URL: http://localhost:${this.port}`);
            console.log(`🧠 Modèle: ${this.config19GB.nom} (${this.config19GB.taille})`);
            console.log(`🎯 Test QI: http://localhost:${this.port}/test-qi-18gb.html`);
            console.log('');
            console.log('🚫 AUCUN CHARGEMENT AUTOMATIQUE');
            console.log('✅ UTILISE UNIQUEMENT VOTRE MODÈLE EXISTANT');
            console.log('');
        });
    }
}

// Démarrage du serveur simple
if (require.main === module) {
    new ServeurSimple18GB();
}

module.exports = ServeurSimple18GB;
