#!/usr/bin/env node

/**
 * 🌊 TEST MOUVEMENT AUTOMATIQUE INFINI ULTRA-FLUIDE
 * Vérification du déplacement automatique sans saccades
 * AUCUNE SIMULATION - QUE DU CODE RÉEL ET FONCTIONNEL
 */

const { MemoireThermiqueReelle } = require('./VERSIONS-NON-VALIDEES/memoires-non-validees/memoire-thermique-reelle.js');

console.log('🌊 TEST MOUVEMENT AUTOMATIQUE INFINI ULTRA-FLUIDE');
console.log('================================================');

async function testerMouvementAutomatique() {
    try {
        // Initialiser la mémoire thermique
        console.log('\n🧠 Initialisation mémoire thermique ultra-fluide...');
        const memoire = new MemoireThermiqueReelle();
        
        // Attendre l'initialisation
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        console.log('✅ Mémoire thermique initialisée');
        console.log(`📊 Mémoires: ${memoire.memoires.size}`);
        console.log(`🌊 Mouvement automatique: ${memoire.mouvement_automatique_actif}`);
        console.log(`⚡ Vitesse curseur: ${memoire.vitesse_curseur}`);
        console.log(`🎯 Fluidité: ${memoire.fluidite_memoire}`);
        console.log(`🔄 Inertie: ${memoire.inertie_thermique}`);
        
        // Test 1: Vérifier mouvement automatique continu
        console.log('\n🌊 TEST 1: MOUVEMENT AUTOMATIQUE CONTINU');
        const positions = [];
        const vitesses = [];
        const temperatures = [];
        
        // Enregistrer positions sur 10 secondes
        for (let i = 0; i < 20; i++) {
            const position = memoire.curseurThermique;
            const vitesse = memoire.vitesse_curseur;
            const temp_moyenne = memoire.temperature_moyenne_systeme || 0;
            
            positions.push(position);
            vitesses.push(vitesse);
            temperatures.push(temp_moyenne);
            
            console.log(`📍 T+${(i * 0.5).toFixed(1)}s: Position=${position.toFixed(6)}, Vitesse=${vitesse.toFixed(6)}, Temp=${temp_moyenne.toFixed(2)}°C`);
            
            await new Promise(resolve => setTimeout(resolve, 500));
        }
        
        // Analyser le mouvement
        console.log('\n📊 ANALYSE DU MOUVEMENT:');
        
        // Vérifier continuité
        let mouvement_continu = true;
        let saccades_detectees = 0;
        for (let i = 1; i < positions.length; i++) {
            const diff = Math.abs(positions[i] - positions[i-1]);
            if (diff > 0.01) { // Seuil de saccade
                saccades_detectees++;
                mouvement_continu = false;
            }
        }
        
        console.log(`• Mouvement continu: ${mouvement_continu ? '✅' : '❌'}`);
        console.log(`• Saccades détectées: ${saccades_detectees}`);
        console.log(`• Position min: ${Math.min(...positions).toFixed(6)}`);
        console.log(`• Position max: ${Math.max(...positions).toFixed(6)}`);
        console.log(`• Amplitude: ${(Math.max(...positions) - Math.min(...positions)).toFixed(6)}`);
        
        // Vérifier variation de vitesse
        const vitesse_min = Math.min(...vitesses);
        const vitesse_max = Math.max(...vitesses);
        console.log(`• Vitesse min: ${vitesse_min.toFixed(6)}`);
        console.log(`• Vitesse max: ${vitesse_max.toFixed(6)}`);
        console.log(`• Variation vitesse: ${((vitesse_max - vitesse_min) / vitesse_min * 100).toFixed(1)}%`);
        
        // Test 2: Vérifier adaptation automatique
        console.log('\n🔧 TEST 2: ADAPTATION AUTOMATIQUE');
        
        // Forcer adaptation
        memoire.adapterVitesseAutomatique();
        const vitesse_apres_adaptation = memoire.vitesse_curseur;
        console.log(`⚡ Vitesse après adaptation: ${vitesse_apres_adaptation.toFixed(6)}`);
        
        // Forcer optimisation
        memoire.optimiserMouvementAutomatique();
        const vitesse_apres_optimisation = memoire.vitesse_curseur;
        console.log(`🎯 Vitesse après optimisation: ${vitesse_apres_optimisation.toFixed(6)}`);
        
        // Test 3: Vérifier déplacement des mémoires
        console.log('\n🧠 TEST 3: DÉPLACEMENT DES MÉMOIRES');
        
        // Prendre quelques mémoires échantillon
        const echantillon = Array.from(memoire.memoires.values()).slice(0, 5);
        const temperatures_initiales = echantillon.map(m => m.temperature);
        const zones_initiales = echantillon.map(m => m.zone);
        
        console.log('📊 État initial des mémoires échantillon:');
        echantillon.forEach((m, i) => {
            console.log(`  • Mémoire ${i+1}: ${m.temperature.toFixed(2)}°C, Zone ${m.zone}`);
        });
        
        // Attendre déplacement
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        const temperatures_finales = echantillon.map(m => m.temperature);
        const zones_finales = echantillon.map(m => m.zone);
        
        console.log('\n📊 État final des mémoires échantillon:');
        let memoires_deplacees = 0;
        let changements_zones = 0;
        
        echantillon.forEach((m, i) => {
            const diff_temp = Math.abs(temperatures_finales[i] - temperatures_initiales[i]);
            if (diff_temp > 0.1) memoires_deplacees++;
            if (zones_finales[i] !== zones_initiales[i]) changements_zones++;
            
            console.log(`  • Mémoire ${i+1}: ${m.temperature.toFixed(2)}°C, Zone ${m.zone} (Δ${diff_temp.toFixed(2)}°C)`);
        });
        
        console.log(`\n✅ Mémoires déplacées: ${memoires_deplacees}/5`);
        console.log(`🔄 Changements de zones: ${changements_zones}/5`);
        
        // Test 4: Vérifier performance système
        console.log('\n📈 TEST 4: PERFORMANCE SYSTÈME');
        
        const performance = memoire.calculerPerformanceSysteme();
        const stats = memoire.getStatistiquesReelles();
        
        console.log(`🎯 Performance système: ${performance.toFixed(1)}%`);
        console.log(`🧠 Mémoires totales: ${stats.totalEntries}`);
        console.log(`🔗 Connexions totales: ${stats.connexions_totales}`);
        console.log(`⚡ Activations récentes: ${stats.activations_recentes}`);
        console.log(`🌡️ Température moyenne: ${stats.averageTemperature.toFixed(2)}°C`);
        
        // Test 5: Vérifier persistance automatique
        console.log('\n♾️ TEST 5: PERSISTANCE AUTOMATIQUE');
        
        const cycle_initial = memoire.cycle_automatique;
        console.log(`🔄 Cycles automatiques: ${cycle_initial}`);
        
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        const cycle_final = memoire.cycle_automatique;
        const cycles_executes = cycle_final - cycle_initial;
        
        console.log(`🔄 Cycles exécutés en 3s: ${cycles_executes}`);
        console.log(`⚡ Fréquence: ${(cycles_executes / 3).toFixed(1)} cycles/seconde`);
        
        if (cycles_executes > 50) {
            console.log('✅ Mouvement automatique ULTRA-RAPIDE confirmé');
        } else if (cycles_executes > 20) {
            console.log('✅ Mouvement automatique RAPIDE confirmé');
        } else {
            console.log('⚠️ Mouvement automatique LENT');
        }
        
        // Résumé final
        console.log('\n🎯 RÉSUMÉ FINAL');
        console.log('===============');
        
        const tests_reussis = [
            mouvement_continu && saccades_detectees === 0,
            vitesse_apres_adaptation > 0 && vitesse_apres_optimisation > 0,
            memoires_deplacees > 0,
            performance > 50,
            cycles_executes > 20
        ];
        
        console.log(`• Mouvement ultra-fluide: ${tests_reussis[0] ? '✅' : '❌'}`);
        console.log(`• Adaptation automatique: ${tests_reussis[1] ? '✅' : '❌'}`);
        console.log(`• Déplacement mémoires: ${tests_reussis[2] ? '✅' : '❌'}`);
        console.log(`• Performance système: ${tests_reussis[3] ? '✅' : '❌'}`);
        console.log(`• Persistance automatique: ${tests_reussis[4] ? '✅' : '❌'}`);
        
        const score = tests_reussis.filter(t => t).length;
        console.log(`\n🏆 SCORE: ${score}/5 tests réussis`);
        
        if (score === 5) {
            console.log('🎉 MOUVEMENT AUTOMATIQUE INFINI PARFAIT !');
            console.log('🌊 Fluidité ultra-douce comme brouillard confirmée');
            console.log('♾️ Persistance automatique infinie opérationnelle');
        } else if (score >= 3) {
            console.log('✅ Mouvement automatique fonctionnel');
        } else {
            console.log('❌ Mouvement automatique à améliorer');
        }
        
        // Informations techniques finales
        console.log('\n🔧 PARAMÈTRES TECHNIQUES FINAUX:');
        console.log(`• Vitesse curseur: ${memoire.vitesse_curseur.toFixed(6)}`);
        console.log(`• Fluidité mémoire: ${memoire.fluidite_memoire.toFixed(6)}`);
        console.log(`• Inertie thermique: ${memoire.inertie_thermique.toFixed(6)}`);
        console.log(`• Lissage mouvement: ${memoire.lissage_mouvement.toFixed(6)}`);
        console.log(`• Cycles automatiques: ${memoire.cycle_automatique}`);
        console.log(`• Position curseur: ${memoire.curseurThermique.toFixed(6)}`);
        console.log(`• Direction: ${memoire.direction_curseur > 0 ? 'Montant' : 'Descendant'}`);
        
        console.log('\n🌊✨ TEST MOUVEMENT AUTOMATIQUE INFINI TERMINÉ ! ✨🌊');
        
    } catch (error) {
        console.error('❌ Erreur test mouvement automatique:', error.message);
        console.error(error.stack);
    }
}

// Lancer le test
testerMouvementAutomatique();
