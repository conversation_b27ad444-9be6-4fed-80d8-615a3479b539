{"name": "http-cache-semantics", "version": "4.2.0", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "repository": {"type": "git", "url": "git+https://github.com/kornelski/http-cache-semantics.git"}, "main": "index.js", "types": "index.js", "scripts": {"test": "mocha"}, "files": ["index.js"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (https://kornel.ski/)", "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^11.0"}}