<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎬 LOUNA-AI - Générateur Vidéo LTX</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a, #1a1a2e, #16213e, #0f3460);
            color: #ffffff;
            overflow-x: hidden;
            min-height: 100vh;
            animation: backgroundShift 20s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% { background: linear-gradient(135deg, #0a0a0a, #1a1a2e, #16213e, #0f3460); }
            25% { background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460, #533483); }
            50% { background: linear-gradient(135deg, #16213e, #0f3460, #533483, #7209b7); }
            75% { background: linear-gradient(135deg, #0f3460, #533483, #7209b7, #a663cc); }
        }

        .header {
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            text-align: center;
            border-bottom: 3px solid;
            border-image: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00) 1;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 2.5em;
            background: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00, #00ff00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
            margin-bottom: 10px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .panel {
            background: rgba(0, 0, 0, 0.7);
            border: 2px solid;
            border-image: linear-gradient(45deg, #00ffff, #ff00ff) 1;
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            box-shadow: 0 0 30px rgba(0, 255, 255, 0.3);
        }

        .panel h2 {
            color: #00ffff;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.5em;
            text-shadow: 0 0 10px #00ffff;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #ff69b4;
            font-weight: bold;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid #333;
            border-radius: 8px;
            color: #ffffff;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            border-color: #00ffff;
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
            outline: none;
        }

        .form-group textarea {
            height: 100px;
            resize: vertical;
        }

        .btn {
            background: linear-gradient(45deg, #00ffff, #ff00ff);
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            color: #ffffff;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            font-size: 16px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 255, 255, 0.4);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-item {
            background: rgba(0, 0, 0, 0.6);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #333;
        }

        .stat-value {
            font-size: 1.8em;
            font-weight: bold;
            color: #00ffff;
            text-shadow: 0 0 10px #00ffff;
        }

        .stat-label {
            color: #cccccc;
            font-size: 0.9em;
            margin-top: 5px;
        }

        .video-preview {
            background: rgba(0, 0, 0, 0.8);
            border: 2px dashed #333;
            border-radius: 10px;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            margin-bottom: 20px;
        }

        .video-preview.has-video {
            border-color: #00ffff;
            background: rgba(0, 255, 255, 0.1);
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 10px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #00ffff, #ff00ff);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        .log-container {
            background: rgba(0, 0, 0, 0.9);
            border: 1px solid #333;
            border-radius: 8px;
            height: 150px;
            overflow-y: auto;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-entry.info { color: #00ffff; }
        .log-entry.success { color: #00ff00; }
        .log-entry.error { color: #ff0000; }
        .log-entry.warning { color: #ffff00; }

        .config-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        .nav-buttons {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #00ffff;
            color: #00ffff;
            padding: 12px 18px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            font-size: 1.1em;
            transition: all 0.3s ease;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(0, 255, 255, 0.3);
        }

        .nav-btn:hover {
            background: linear-gradient(45deg, #00ffff, #ff00ff);
            color: #ffffff;
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 255, 255, 0.5);
        }

        .home-btn {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4) !important;
            border: 2px solid #ff6b6b !important;
            color: white !important;
            font-size: 1.2em !important;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 107, 107, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 107, 107, 0); }
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                padding: 15px;
            }
            
            .config-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="nav-buttons">
        <button class="nav-btn home-btn" onclick="window.location.href='interface-louna-complete.html'" title="Retour à l'accueil LOUNA-AI">
            🏠 ACCUEIL
        </button>
        <button class="nav-btn" onclick="window.open('memoire-3d-accelerateurs.html', '_blank')" title="Ouvrir la visualisation 3D">
            🧠 Mémoire 3D
        </button>
    </div>

    <div class="header">
        <h1>🎬 GÉNÉRATEUR VIDÉO LTX</h1>
        <p>Génération de vidéos en temps réel avec intelligence artificielle</p>
    </div>

    <div class="container">
        <!-- Panel de génération -->
        <div class="panel">
            <h2>🎥 Génération Vidéo</h2>
            
            <div class="form-group">
                <label for="videoType">Type de génération :</label>
                <select id="videoType">
                    <option value="text-to-video">Texte vers Vidéo</option>
                    <option value="image-to-video">Image vers Vidéo</option>
                </select>
            </div>

            <div class="form-group">
                <label for="promptText">Prompt de génération :</label>
                <textarea id="promptText" placeholder="Décrivez la vidéo que vous souhaitez générer..."></textarea>
            </div>

            <div class="form-group" id="imageGroup" style="display: none;">
                <label for="imageFile">Image source :</label>
                <input type="file" id="imageFile" accept="image/*">
            </div>

            <div class="config-grid">
                <div class="form-group">
                    <label for="resolution">Résolution :</label>
                    <select id="resolution">
                        <option value="1216x704">1216x704 (Défaut)</option>
                        <option value="1920x1080">1920x1080 (Full HD)</option>
                        <option value="1280x720">1280x720 (HD)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="fps">FPS :</label>
                    <select id="fps">
                        <option value="30">30 FPS</option>
                        <option value="24">24 FPS</option>
                        <option value="60">60 FPS</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="frames">Nombre de frames :</label>
                    <input type="number" id="frames" value="121" min="30" max="300">
                </div>

                <div class="form-group">
                    <label for="steps">Steps :</label>
                    <input type="number" id="steps" value="8" min="4" max="20">
                </div>
            </div>

            <button class="btn" id="generateBtn">🎬 Générer Vidéo</button>

            <div class="progress-bar" id="progressBar" style="display: none;">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <!-- Panel de prévisualisation et statistiques -->
        <div class="panel">
            <h2>📊 Statistiques & Prévisualisation</h2>
            
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="videosGenerated">0</div>
                    <div class="stat-label">Vidéos générées</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="avgTime">0s</div>
                    <div class="stat-label">Temps moyen</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="ltxStatus">❌</div>
                    <div class="stat-label">LTX Status</div>
                </div>
            </div>

            <div class="video-preview" id="videoPreview">
                <span>Aucune vidéo générée</span>
            </div>

            <!-- GESTIONNAIRE DE VIDÉOS -->
            <div class="video-manager" style="margin: 20px 0; padding: 20px; background: rgba(0,0,0,0.3); border-radius: 15px; border: 1px solid #333;">
                <h3 style="color: #00ffff; margin-bottom: 15px; text-align: center;">📁 Gestionnaire de Vidéos</h3>

                <div class="video-actions" style="display: flex; gap: 10px; margin-bottom: 15px; flex-wrap: wrap; justify-content: center;">
                    <button onclick="ouvrirDossierVideos()" style="background: linear-gradient(45deg, #00ffff, #ff00ff); color: white; border: none; padding: 10px 15px; border-radius: 8px; cursor: pointer; font-size: 0.9em;">
                        📂 Ouvrir Dossier
                    </button>
                    <button onclick="rafraichirListeVideos()" style="background: linear-gradient(45deg, #ffff00, #ff6600); color: white; border: none; padding: 10px 15px; border-radius: 8px; cursor: pointer; font-size: 0.9em;">
                        🔄 Actualiser
                    </button>
                    <button onclick="supprimerToutesVideos()" style="background: linear-gradient(45deg, #ff0000, #ff6600); color: white; border: none; padding: 10px 15px; border-radius: 8px; cursor: pointer; font-size: 0.9em;">
                        🗑️ Tout Supprimer
                    </button>
                </div>

                <div class="video-list" id="videoList" style="background: rgba(0,0,0,0.5); border-radius: 10px; padding: 15px; max-height: 200px; overflow-y: auto; border: 1px solid #444;">
                    <div style="color: #888; text-align: center; padding: 20px;">
                        Chargement de la liste des vidéos...
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>Logs de génération :</label>
                <div class="log-container" id="logContainer">
                    <div class="log-entry info">🎬 Générateur vidéo LTX initialisé</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let isGenerating = false;
        let currentVideoId = null;

        // Éléments DOM
        const videoType = document.getElementById('videoType');
        const promptText = document.getElementById('promptText');
        const imageGroup = document.getElementById('imageGroup');
        const imageFile = document.getElementById('imageFile');
        const generateBtn = document.getElementById('generateBtn');
        const progressBar = document.getElementById('progressBar');
        const progressFill = document.getElementById('progressFill');
        const videoPreview = document.getElementById('videoPreview');
        const logContainer = document.getElementById('logContainer');

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            setupEventListeners();
            rafraichirListeVideos();
            addLog('🎬 Interface vidéo LTX chargée', 'info');
        });

        function setupEventListeners() {
            videoType.addEventListener('change', function() {
                if (this.value === 'image-to-video') {
                    imageGroup.style.display = 'block';
                } else {
                    imageGroup.style.display = 'none';
                }
            });

            generateBtn.addEventListener('click', generateVideo);
        }

        async function generateVideo() {
            if (isGenerating) return;

            const prompt = promptText.value.trim();
            if (!prompt) {
                addLog('❌ Veuillez saisir un prompt', 'error');
                return;
            }

            const type = videoType.value;
            if (type === 'image-to-video' && !imageFile.files[0]) {
                addLog('❌ Veuillez sélectionner une image', 'error');
                return;
            }

            isGenerating = true;
            generateBtn.disabled = true;
            generateBtn.textContent = '🔄 Génération en cours...';
            progressBar.style.display = 'block';

            try {
                addLog(`🎬 Début génération ${type}: "${prompt}"`, 'info');
                
                const options = {
                    resolution: document.getElementById('resolution').value,
                    fps: parseInt(document.getElementById('fps').value),
                    frames: parseInt(document.getElementById('frames').value),
                    steps: parseInt(document.getElementById('steps').value)
                };

                let response;
                if (type === 'text-to-video') {
                    response = await fetch('/api/generer-video-texte', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ prompt, options })
                    });
                } else {
                    // Pour image-to-video, on simule le chemin de l'image
                    const imagePath = 'temp/' + imageFile.files[0].name;
                    response = await fetch('/api/generer-video-image', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ prompt, imagePath, options })
                    });
                }

                const result = await response.json();
                
                if (result.success) {
                    addLog(`✅ Vidéo générée: ${result.video_id}`, 'success');
                    addLog(`⏱️ Temps: ${result.duree_generation}ms`, 'info');
                    currentVideoId = result.video_id;

                    // Déterminer si c'est une simulation
                    const isSimulation = result.message && result.message.includes('simulation');
                    updateVideoPreview(result.chemin_video, isSimulation);
                } else {
                    addLog(`❌ Erreur: ${result.error}`, 'error');
                }

            } catch (error) {
                addLog(`❌ Erreur réseau: ${error.message}`, 'error');
            } finally {
                isGenerating = false;
                generateBtn.disabled = false;
                generateBtn.textContent = '🎬 Générer Vidéo';
                progressBar.style.display = 'none';
                loadStats();
            }
        }

        async function loadStats() {
            try {
                const response = await fetch('/api/stats-video-ltx');
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('videosGenerated').textContent = data.statistiques.videos_generees;
                    document.getElementById('avgTime').textContent = data.statistiques.temps_moyen_format || '0s';
                    document.getElementById('ltxStatus').textContent = data.ltx_installe ? '✅' : '⚠️';
                }
            } catch (error) {
                addLog(`⚠️ Erreur chargement stats: ${error.message}`, 'warning');
            }
        }

        function updateVideoPreview(videoPath, isSimulation = false) {
            if (videoPath.endsWith('.txt')) {
                // Affichage pour simulation texte
                videoPreview.innerHTML = `
                    <div style="text-align: center; padding: 20px;">
                        <div style="color: #ffff00; font-size: 1.5em; margin-bottom: 15px;">🎭 Simulation Vidéo</div>
                        <div style="color: #cccccc; margin-bottom: 10px;">LTX-Video et FFmpeg non installés</div>
                        <div style="color: #00ffff; margin-bottom: 15px; font-family: monospace;">${currentVideoId}</div>
                        <div style="color: #ff69b4; font-size: 0.9em; line-height: 1.4;">
                            💡 Pour voir de vraies vidéos :<br>
                            • Installez FFmpeg pour des vidéos de démonstration<br>
                            • Ou installez LTX-Video pour la génération IA
                        </div>
                        <button onclick="downloadSimulation('${videoPath}')" style="
                            margin-top: 15px;
                            padding: 8px 16px;
                            background: linear-gradient(45deg, #00ffff, #ff00ff);
                            border: none;
                            border-radius: 15px;
                            color: white;
                            cursor: pointer;
                        ">📄 Voir détails simulation</button>
                    </div>
                `;
            } else if (videoPath.endsWith('.mp4')) {
                // Affichage pour vraie vidéo
                videoPreview.innerHTML = `
                    <div style="text-align: center;">
                        <video controls style="max-width: 100%; max-height: 180px; border-radius: 10px;">
                            <source src="${videoPath}" type="video/mp4">
                            Votre navigateur ne supporte pas la lecture vidéo.
                        </video>
                        ${isSimulation ? '<div style="color: #ffff00; margin-top: 10px; font-size: 0.9em;">🎬 Vidéo de démonstration</div>' : ''}
                    </div>
                `;
            } else {
                // Fallback
                videoPreview.innerHTML = `
                    <div style="text-align: center; color: #ff6666;">
                        <div style="font-size: 1.2em; margin-bottom: 10px;">❌ Erreur</div>
                        <div>Format de fichier non reconnu</div>
                    </div>
                `;
            }
            videoPreview.classList.add('has-video');
        }

        function downloadSimulation(filePath) {
            // Télécharger le fichier de simulation
            const link = document.createElement('a');
            link.href = filePath;
            link.download = filePath.split('/').pop();
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // Limiter à 50 entrées
            while (logContainer.children.length > 50) {
                logContainer.removeChild(logContainer.firstChild);
            }
        }

        // === FONCTIONS GESTIONNAIRE DE VIDÉOS ===

        async function rafraichirListeVideos() {
            const videoList = document.getElementById('videoList');
            videoList.innerHTML = '<div style="color: #888; text-align: center; padding: 20px;">🔄 Chargement...</div>';

            try {
                const response = await fetch('/api/videos-ltx');
                const data = await response.json();

                if (data.success && data.videos && data.videos.length > 0) {
                    let html = '';
                    data.videos.forEach((video, index) => {
                        const isSimulation = video.type === 'simulation' || (video.chemin && video.chemin.includes('simulation'));
                        const taille = video.taille || 'N/A';

                        html += `
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px; margin: 5px 0; background: rgba(255,255,255,0.05); border-radius: 8px; border-left: 3px solid ${isSimulation ? '#ffff00' : '#00ffff'};">
                                <div style="flex: 1;">
                                    <div style="color: ${isSimulation ? '#ffff00' : '#00ffff'}; font-weight: bold; font-size: 0.9em;">
                                        ${isSimulation ? '🎭' : '🎬'} ${video.nom || video.id}
                                    </div>
                                    <div style="color: #ccc; font-size: 0.8em; margin-top: 3px;">
                                        ${video.prompt ? video.prompt.substring(0, 50) : 'Aucun prompt'}${(video.prompt && video.prompt.length > 50) ? '...' : ''}
                                    </div>
                                    <div style="color: #888; font-size: 0.7em; margin-top: 2px;">
                                        ${video.date ? new Date(video.date).toLocaleString() : 'Date inconnue'} • ${taille}
                                    </div>
                                    <div style="color: ${video.existe ? '#00ff00' : '#ff4444'}; font-size: 0.7em;">
                                        ${video.existe ? '✅ Disponible' : '❌ Introuvable'}
                                    </div>
                                </div>
                                <div style="display: flex; gap: 5px;">
                                    <button onclick="ouvrirVideo('${video.chemin}', ${isSimulation})" style="background: #00ffff; color: black; border: none; padding: 5px 8px; border-radius: 4px; cursor: pointer; font-size: 0.8em;" ${!video.existe ? 'disabled style="opacity: 0.5;"' : ''}>
                                        ${isSimulation ? '📄' : '▶️'}
                                    </button>
                                    <button onclick="supprimerVideo('${video.id}', '${video.chemin}')" style="background: #ff4444; color: white; border: none; padding: 5px 8px; border-radius: 4px; cursor: pointer; font-size: 0.8em;">
                                        🗑️
                                    </button>
                                </div>
                            </div>
                        `;
                    });
                    videoList.innerHTML = html;
                } else {
                    videoList.innerHTML = '<div style="color: #888; text-align: center; padding: 20px;">📁 Aucune vidéo générée</div>';
                }
            } catch (error) {
                videoList.innerHTML = '<div style="color: #ff6666; text-align: center; padding: 20px;">❌ Erreur de chargement</div>';
                addLog(`❌ Erreur liste vidéos: ${error.message}`, 'error');
            }
        }

        function ouvrirDossierVideos() {
            // Tenter d'ouvrir le dossier des vidéos
            if (navigator.userAgent.includes('Mac')) {
                // macOS
                fetch('/api/ouvrir-dossier-videos', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            addLog('📂 Dossier vidéos ouvert', 'success');
                        } else {
                            addLog('⚠️ Impossible d\'ouvrir le dossier automatiquement', 'warning');
                            addLog('📁 Dossier: videos-ltx/', 'info');
                        }
                    })
                    .catch(error => {
                        addLog('📁 Dossier vidéos: videos-ltx/', 'info');
                    });
            } else {
                addLog('📁 Dossier vidéos: videos-ltx/', 'info');
                addLog('💡 Naviguez manuellement vers le dossier', 'info');
            }
        }

        function ouvrirVideo(cheminVideo, isSimulation) {
            if (isSimulation) {
                // Télécharger le fichier de simulation
                downloadSimulation(cheminVideo);
            } else {
                // Ouvrir la vidéo dans un nouvel onglet
                window.open(cheminVideo, '_blank');
            }
        }

        async function supprimerVideo(videoId, cheminVideo) {
            if (!confirm(`Êtes-vous sûr de vouloir supprimer la vidéo ${videoId} ?`)) {
                return;
            }

            try {
                const response = await fetch('/api/supprimer-video', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ video_id: videoId, chemin_video: cheminVideo })
                });

                const result = await response.json();

                if (result.success) {
                    addLog(`🗑️ Vidéo ${videoId} supprimée`, 'success');
                    rafraichirListeVideos();
                    loadStats();
                } else {
                    addLog(`❌ Erreur suppression: ${result.error}`, 'error');
                }
            } catch (error) {
                addLog(`❌ Erreur suppression: ${error.message}`, 'error');
            }
        }

        async function supprimerToutesVideos() {
            if (!confirm('Êtes-vous sûr de vouloir supprimer TOUTES les vidéos ? Cette action est irréversible.')) {
                return;
            }

            try {
                const response = await fetch('/api/supprimer-toutes-videos', {
                    method: 'POST'
                });

                const result = await response.json();

                if (result.success) {
                    addLog(`🗑️ ${result.videos_supprimees} vidéos supprimées`, 'success');
                    rafraichirListeVideos();
                    loadStats();

                    // Réinitialiser la prévisualisation
                    videoPreview.innerHTML = '<span>Aucune vidéo générée</span>';
                    videoPreview.classList.remove('has-video');
                } else {
                    addLog(`❌ Erreur suppression: ${result.error}`, 'error');
                }
            } catch (error) {
                addLog(`❌ Erreur suppression: ${error.message}`, 'error');
            }
        }

        function formatTaille(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        // Mise à jour automatique des stats et liste
        setInterval(() => {
            loadStats();
            rafraichirListeVideos();
        }, 30000); // Toutes les 30 secondes
    </script>
</body>
</html>
