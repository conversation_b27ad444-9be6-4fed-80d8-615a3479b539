#!/usr/bin/env node

/**
 * VISUALISEUR ORGANISATION MÉMOIRE THERMIQUE
 * Montre comment les fichiers sont rangés et compressés
 */

console.log('📁 ORGANISATION MÉMOIRE THERMIQUE');
console.log('=================================');

const { ThermalMemoryUltraAutomatique } = require('./thermal-memory-ultra-automatique-vrai');
const fs = require('fs');
const path = require('path');

async function visualiserOrganisation() {
    try {
        console.log('\n🔥 Initialisation mémoire thermique...');
        const memoire = new ThermalMemoryUltraAutomatique({
            kyber_auto_install: true,
            neurones_auto_install: true,
            neurones_max_limit: false
        });

        await new Promise(resolve => setTimeout(resolve, 3000));

        console.log('\n📊 ORGANISATION DES FICHIERS:');
        console.log('==============================');

        // Ajouter différents types de données pour voir l'organisation
        console.log('\n📝 Ajout de données test...');
        
        memoire.add('document_urgent', 'Document très important nécessitant accès rapide', 0.95, 'urgent');
        memoire.add('info_importante', 'Information importante pour le travail quotidien', 0.75, 'important');
        memoire.add('apprentissage', 'Nouvelle connaissance apprise aujourd\'hui', 0.65, 'learning');
        memoire.add('idee_creative', 'Idée créative pour projet futur', 0.45, 'creative');
        memoire.add('info_generale', 'Information générale de référence', 0.25, 'general');

        await new Promise(resolve => setTimeout(resolve, 1000));

        console.log('\n🗂️ RÉPARTITION PAR NIVEAU DE MÉMOIRE:');
        console.log('=====================================');

        const stats = memoire.getStats();
        
        console.log(`📊 Mémoire instantanée (temp ≥ 0.8): ${stats.instantEntries} fichiers`);
        console.log(`📊 Mémoire court terme (temp ≥ 0.6): ${stats.shortTermEntries} fichiers`);
        console.log(`📊 Mémoire de travail (temp ≥ 0.4): ${stats.workingMemoryEntries} fichiers`);
        console.log(`📊 Mémoire moyen terme (temp ≥ 0.2): ${stats.mediumTermEntries} fichiers`);
        console.log(`📊 Mémoire long terme (temp < 0.2): ${stats.longTermEntries} fichiers`);
        console.log(`📊 Mémoire rêves/créative: ${stats.dreamMemoryEntries} fichiers`);

        console.log('\n⚡ ACCÉLÉRATEURS KYBER ACTIFS:');
        console.log('==============================');
        console.log(`🔧 Accélérateurs installés: ${stats.kyber_accelerators.installed}`);
        console.log(`⚡ Accélérateurs actifs: ${stats.kyber_accelerators.active}`);
        console.log(`🚀 Auto-installations: ${stats.kyber_accelerators.auto_installs}`);

        console.log('\n🗜️ COMPRESSION AUTOMATIQUE:');
        console.log('============================');
        console.log('✅ Compression sémantique automatique avec accélérateurs KYBER');
        console.log('✅ Décompression automatique lors de l\'accès');
        console.log('✅ Optimisation selon la température des données');
        console.log('✅ Boost performance avec neurones spécialisés');

        console.log('\n💾 SAUVEGARDE AUTOMATIQUE:');
        console.log('===========================');
        
        // Vérifier les fichiers de sauvegarde
        const dataPath = path.join(__dirname, '..', 'data', 'memory');
        if (fs.existsSync(dataPath)) {
            const files = fs.readdirSync(dataPath).filter(f => f.endsWith('.json'));
            console.log(`📁 Répertoire: ${dataPath}`);
            files.forEach(file => {
                const filePath = path.join(dataPath, file);
                const stats = fs.statSync(filePath);
                const sizeKB = (stats.size / 1024).toFixed(1);
                console.log(`📄 ${file}: ${sizeKB} KB`);
            });
        }

        console.log('\n🔄 CYCLES AUTOMATIQUES:');
        console.log('========================');
        console.log(`🔄 Cycles effectués: ${stats.automation.auto_cycles}`);
        console.log(`📊 Opérations depuis dernier cycle: ${stats.automation.operations_since_cycle}`);
        console.log(`👁️ Surveillance active: ${stats.automation.monitoring_active ? 'OUI' : 'NON'}`);

        console.log('\n🧠 SYSTÈME NEURONAL:');
        console.log('====================');
        console.log(`🧠 Neurones totaux: ${(stats.neurones_system.total_installed / 1000000).toFixed(1)}M`);
        console.log(`🧠 Neurones actifs: ${(stats.neurones_system.active_count / 1000000).toFixed(1)}M`);
        console.log(`🎯 Spécialisations: ${stats.neurones_system.specializations}`);
        console.log(`🔢 Auto-installations: ${stats.neurones_system.auto_installations}`);

        return true;

    } catch (error) {
        console.log(`❌ ERREUR: ${error.message}`);
        return false;
    }
}

visualiserOrganisation().then(success => {
    console.log(success ? '\n✅ VISUALISATION TERMINÉE !' : '\n💥 ERREUR VISUALISATION !');
    process.exit(success ? 0 : 1);
});
