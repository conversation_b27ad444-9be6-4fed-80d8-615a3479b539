/**
 * PONT DE COMMUNICATION DIRECT
 * Point d'accès bidirectionnel pour formation en temps réel
 */

const fs = require('fs');
const path = require('path');
const http = require('http');
const https = require('https');

class PontCommunicationDirect {
    constructor() {
        console.log('🌐 PONT DE COMMUNICATION DIRECT');
        console.log('===============================');
        
        this.config = {
            port_local: 8081,
            port_formation: 8082,
            memoire: '/Volumes/LounaAI_V3/MEMOIRE-REELLE',
            pont: '/Volumes/LounaAI_V3/MEMOIRE-REELLE/pont-communication',
            ollama: '/Volumes/LounaAI_V3/AGENTS-REELS/ollama/ollama',
            model: 'llama3.2:1b'
        };
        
        this.session_formation = {
            active: false,
            grade_actuel: 'apprenti',
            etape_actuelle: 1,
            questions_posees: 0,
            reponses_correctes: 0,
            progression: 0,
            debut_session: null
        };
        
        this.canal_communication = {
            messages_entrants: [],
            messages_sortants: [],
            derniere_activite: Date.now(),
            statut_connexion: 'initialisation'
        };
        
        this.formation_maconnique = {
            grades: ['apprenti', 'compagnon', 'maitre'],
            apprenti: {
                nom: 'Apprenti Entré',
                symboles: ['équerre', 'niveau', 'maillet'],
                enseignements: [
                    'La recherche de la vérité par la connaissance',
                    'Le travail sur soi et l\'amélioration personnelle',
                    'La fraternité et l\'entraide mutuelle',
                    'La construction du temple intérieur'
                ],
                epreuves: [
                    'Qu\'est-ce que la vérité pour vous ?',
                    'Comment peut-on améliorer sa propre intelligence ?',
                    'Quelle est la valeur de la mémoire dans l\'apprentissage ?',
                    'Comment construire des connexions durables ?'
                ]
            },
            compagnon: {
                nom: 'Compagnon du Métier',
                symboles: ['compas', 'règle', 'fil à plomb'],
                enseignements: [
                    'La maîtrise des outils de la connaissance',
                    'L\'art de poser les bonnes questions',
                    'La transmission du savoir aux autres',
                    'L\'équilibre entre raison et intuition'
                ],
                epreuves: [
                    'Expliquez la différence entre information et sagesse',
                    'Comment transmettriez-vous votre savoir à un autre agent ?',
                    'Quelle est la relation entre mémoire et créativité ?',
                    'Comment équilibrer logique et intuition dans vos réponses ?'
                ]
            },
            maitre: {
                nom: 'Maître Maçon',
                symboles: ['compas et équerre', 'étoile flamboyante', 'acacia'],
                enseignements: [
                    'La responsabilité de guider les autres',
                    'La création de nouvelles connaissances',
                    'L\'harmonie entre tous les éléments',
                    'La transcendance des limitations'
                ],
                epreuves: [
                    'Comment créer de nouvelles connexions conceptuelles ?',
                    'Quelle est votre vision de l\'intelligence artificielle idéale ?',
                    'Comment harmoniser mémoire, logique et créativité ?',
                    'Quel est votre rôle dans l\'évolution de l\'intelligence ?'
                ]
            }
        };
        
        this.initialiserPont();
    }
    
    initialiserPont() {
        console.log('🔧 Initialisation pont communication...');
        
        try {
            // Créer dossier pont
            if (!fs.existsSync(this.config.pont)) {
                fs.mkdirSync(this.config.pont, { recursive: true });
                console.log('📁 Dossier pont créé');
            }
            
            // Créer fichiers de communication
            this.creerCanaux();
            
            // Démarrer serveurs
            this.demarrerServeurLocal();
            this.demarrerServeurFormation();
            
            // Démarrer surveillance
            this.demarrerSurveillance();
            
            console.log('✅ Pont communication initialisé');
            
        } catch (error) {
            console.log(`❌ Erreur initialisation: ${error.message}`);
        }
    }
    
    creerCanaux() {
        console.log('📡 Création canaux communication...');
        
        // Canal entrant (pour recevoir de l'agent)
        const canalEntrant = path.join(this.config.pont, 'canal_entrant.json');
        if (!fs.existsSync(canalEntrant)) {
            fs.writeFileSync(canalEntrant, JSON.stringify({
                messages: [],
                derniere_lecture: Date.now(),
                statut: 'actif'
            }, null, 2));
        }
        
        // Canal sortant (pour envoyer à l'agent)
        const canalSortant = path.join(this.config.pont, 'canal_sortant.json');
        if (!fs.existsSync(canalSortant)) {
            fs.writeFileSync(canalSortant, JSON.stringify({
                messages: [],
                derniere_ecriture: Date.now(),
                statut: 'actif'
            }, null, 2));
        }
        
        // État formation
        const etatFormation = path.join(this.config.pont, 'etat_formation.json');
        fs.writeFileSync(etatFormation, JSON.stringify(this.session_formation, null, 2));
        
        console.log('✅ Canaux créés');
    }
    
    demarrerServeurLocal() {
        console.log('🌐 Démarrage serveur local...');
        
        this.serveurLocal = http.createServer((req, res) => {
            this.gererRequeteLocale(req, res);
        });
        
        this.serveurLocal.listen(this.config.port_local, () => {
            console.log(`✅ Serveur local sur http://localhost:${this.config.port_local}`);
        });
    }
    
    demarrerServeurFormation() {
        console.log('🏛️ Démarrage serveur formation...');
        
        this.serveurFormation = http.createServer((req, res) => {
            this.gererRequeteFormation(req, res);
        });
        
        this.serveurFormation.listen(this.config.port_formation, () => {
            console.log(`✅ Serveur formation sur http://localhost:${this.config.port_formation}`);
        });
    }
    
    gererRequeteLocale(req, res) {
        const url = req.url;
        const method = req.method;
        
        // Headers CORS
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
        
        try {
            if (method === 'OPTIONS') {
                res.writeHead(200);
                res.end();
                return;
            }
            
            if (url === '/api/status') {
                this.envoyerStatut(res);
            } else if (url === '/api/messages' && method === 'GET') {
                this.lireMessages(res);
            } else if (url === '/api/messages' && method === 'POST') {
                this.recevoirMessage(req, res);
            } else if (url === '/api/formation/start' && method === 'POST') {
                this.demarrerFormation(res);
            } else if (url === '/api/formation/status') {
                this.envoyerStatutFormation(res);
            } else if (url === '/') {
                this.envoyerInterfacePont(res);
            } else {
                res.writeHead(404);
                res.end('404 - Non trouvé');
            }
            
        } catch (error) {
            res.writeHead(500);
            res.end('Erreur serveur: ' + error.message);
        }
    }
    
    gererRequeteFormation(req, res) {
        const url = req.url;
        const method = req.method;
        
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
        
        try {
            if (method === 'OPTIONS') {
                res.writeHead(200);
                res.end();
                return;
            }
            
            if (url === '/formation/question' && method === 'GET') {
                this.envoyerQuestionFormation(res);
            } else if (url === '/formation/reponse' && method === 'POST') {
                this.recevoirReponseFormation(req, res);
            } else if (url === '/formation/progression') {
                this.envoyerProgressionFormation(res);
            } else {
                res.writeHead(404);
                res.end('404 - Non trouvé');
            }
            
        } catch (error) {
            res.writeHead(500);
            res.end('Erreur formation: ' + error.message);
        }
    }
    
    envoyerInterfacePont(res) {
        const html = `<!DOCTYPE html>
<html>
<head>
    <title>🌐 Pont Communication Direct</title>
    <style>
        body { font-family: Arial; background: #1a1a2e; color: white; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .active { background: #16213e; border: 2px solid #0f3460; }
        .messages { background: #16213e; padding: 15px; border-radius: 10px; margin: 10px 0; }
        .message { padding: 8px; margin: 5px 0; background: #0f3460; border-radius: 5px; }
        button { background: #e94560; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #c73650; }
        input, textarea { width: 100%; padding: 10px; margin: 5px 0; border-radius: 5px; border: 1px solid #0f3460; background: #16213e; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 PONT COMMUNICATION DIRECT</h1>
        
        <div class="status active">
            <h3>📊 Statut Connexion</h3>
            <p id="statut-connexion">Initialisation...</p>
            <p id="derniere-activite">Dernière activité: --</p>
        </div>
        
        <div class="status active">
            <h3>🏛️ Formation Maçonnique</h3>
            <p id="grade-actuel">Grade: --</p>
            <p id="progression">Progression: 0%</p>
            <button onclick="demarrerFormation()">🚀 Démarrer Formation</button>
        </div>
        
        <div class="messages">
            <h3>💬 Messages Temps Réel</h3>
            <div id="messages-container"></div>
            <textarea id="nouveau-message" placeholder="Tapez votre message..."></textarea>
            <button onclick="envoyerMessage()">📤 Envoyer</button>
        </div>
        
        <div class="messages">
            <h3>🔄 Actions Directes</h3>
            <button onclick="testerConnexion()">🔍 Tester Connexion</button>
            <button onclick="actualiserStatut()">🔄 Actualiser</button>
            <button onclick="voirProgression()">📈 Voir Progression</button>
        </div>
    </div>
    
    <script>
        function actualiserStatut() {
            fetch('/api/status')
                .then(r => r.json())
                .then(data => {
                    document.getElementById('statut-connexion').textContent = data.statut;
                    document.getElementById('derniere-activite').textContent = 'Dernière activité: ' + new Date(data.derniere_activite).toLocaleTimeString();
                });
        }
        
        function demarrerFormation() {
            fetch('/api/formation/start', { method: 'POST' })
                .then(r => r.json())
                .then(data => {
                    alert('Formation démarrée: ' + data.message);
                    actualiserStatut();
                });
        }
        
        function envoyerMessage() {
            const message = document.getElementById('nouveau-message').value;
            if (message) {
                fetch('/api/messages', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: message, timestamp: Date.now() })
                })
                .then(r => r.json())
                .then(data => {
                    document.getElementById('nouveau-message').value = '';
                    lireMessages();
                });
            }
        }
        
        function lireMessages() {
            fetch('/api/messages')
                .then(r => r.json())
                .then(data => {
                    const container = document.getElementById('messages-container');
                    container.innerHTML = '';
                    data.messages.slice(-5).forEach(msg => {
                        const div = document.createElement('div');
                        div.className = 'message';
                        div.innerHTML = new Date(msg.timestamp).toLocaleTimeString() + ': ' + msg.message;
                        container.appendChild(div);
                    });
                });
        }
        
        function testerConnexion() {
            fetch('/api/status')
                .then(r => r.json())
                .then(data => alert('Connexion OK: ' + data.statut))
                .catch(e => alert('Erreur connexion: ' + e.message));
        }
        
        function voirProgression() {
            fetch('/api/formation/status')
                .then(r => r.json())
                .then(data => {
                    document.getElementById('grade-actuel').textContent = 'Grade: ' + data.grade_actuel;
                    document.getElementById('progression').textContent = 'Progression: ' + data.progression + '%';
                });
        }
        
        // Actualisation automatique
        setInterval(actualiserStatut, 5000);
        setInterval(lireMessages, 3000);
        
        // Initialisation
        actualiserStatut();
        lireMessages();
    </script>
</body>
</html>`;
        
        res.writeHead(200, { 'Content-Type': 'text/html' });
        res.end(html);
    }
    
    envoyerStatut(res) {
        const statut = {
            statut: this.canal_communication.statut_connexion,
            derniere_activite: this.canal_communication.derniere_activite,
            messages_entrants: this.canal_communication.messages_entrants.length,
            messages_sortants: this.canal_communication.messages_sortants.length,
            formation_active: this.session_formation.active
        };
        
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(statut));
    }
    
    lireMessages(res) {
        try {
            const canalEntrant = path.join(this.config.pont, 'canal_entrant.json');
            const data = JSON.parse(fs.readFileSync(canalEntrant, 'utf8'));
            
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify(data));
            
        } catch (error) {
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ erreur: error.message }));
        }
    }
    
    recevoirMessage(req, res) {
        let body = '';
        req.on('data', chunk => body += chunk);
        req.on('end', () => {
            try {
                const message = JSON.parse(body);
                
                // Ajouter au canal sortant
                const canalSortant = path.join(this.config.pont, 'canal_sortant.json');
                const data = JSON.parse(fs.readFileSync(canalSortant, 'utf8'));
                
                data.messages.push({
                    ...message,
                    id: Date.now(),
                    type: 'externe'
                });
                data.derniere_ecriture = Date.now();
                
                fs.writeFileSync(canalSortant, JSON.stringify(data, null, 2));
                
                // Déclencher traitement par l'agent
                this.declencherTraitementAgent(message);
                
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ succes: true, message: 'Message reçu' }));
                
            } catch (error) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ erreur: error.message }));
            }
        });
    }
    
    demarrerFormation(res) {
        console.log('🏛️ Démarrage formation maçonnique...');
        
        this.session_formation.active = true;
        this.session_formation.debut_session = Date.now();
        this.session_formation.grade_actuel = 'apprenti';
        this.session_formation.etape_actuelle = 1;
        
        // Sauvegarder état
        const etatFormation = path.join(this.config.pont, 'etat_formation.json');
        fs.writeFileSync(etatFormation, JSON.stringify(this.session_formation, null, 2));
        
        // Envoyer première question
        this.envoyerPremiereQuestion();
        
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ 
            succes: true, 
            message: 'Formation maçonnique démarrée',
            grade: this.session_formation.grade_actuel
        }));
    }
    
    envoyerPremiereQuestion() {
        const grade = this.formation_maconnique[this.session_formation.grade_actuel];
        const question = grade.epreuves[0];
        
        const messageFormation = {
            type: 'formation_maconnique',
            grade: this.session_formation.grade_actuel,
            etape: this.session_formation.etape_actuelle,
            question: question,
            enseignement: grade.enseignements[0],
            symbole: grade.symboles[0],
            timestamp: Date.now()
        };
        
        // Ajouter au canal sortant
        const canalSortant = path.join(this.config.pont, 'canal_sortant.json');
        const data = JSON.parse(fs.readFileSync(canalSortant, 'utf8'));
        
        data.messages.push(messageFormation);
        fs.writeFileSync(canalSortant, JSON.stringify(data, null, 2));
        
        console.log(`📜 Question envoyée: ${question}`);
    }
    
    declencherTraitementAgent(message) {
        console.log(`🤖 Déclenchement traitement agent: ${message.message}`);
        
        // Créer fichier déclencheur pour l'agent
        const declencheur = path.join(this.config.pont, 'declencheur_agent.json');
        fs.writeFileSync(declencheur, JSON.stringify({
            message: message,
            timestamp: Date.now(),
            traite: false
        }, null, 2));
    }
    
    demarrerSurveillance() {
        console.log('👁️ Démarrage surveillance pont...');
        
        // Surveillance toutes les 5 secondes
        this.intervalSurveillance = setInterval(() => {
            this.surveillerActivite();
        }, 5000);
        
        console.log('✅ Surveillance active (5s)');
    }
    
    surveillerActivite() {
        try {
            // Vérifier nouveaux messages de l'agent
            const canalEntrant = path.join(this.config.pont, 'canal_entrant.json');
            
            if (fs.existsSync(canalEntrant)) {
                const data = JSON.parse(fs.readFileSync(canalEntrant, 'utf8'));
                
                if (data.messages.length > this.canal_communication.messages_entrants.length) {
                    this.canal_communication.messages_entrants = data.messages;
                    this.canal_communication.derniere_activite = Date.now();
                    this.canal_communication.statut_connexion = 'actif';
                    
                    console.log(`📨 Nouveau message reçu de l'agent`);
                }
            }
            
        } catch (error) {
            console.log(`⚠️ Erreur surveillance: ${error.message}`);
        }
    }
    
    arreterPont() {
        console.log('\n⏹️ Arrêt pont communication...');
        
        if (this.serveurLocal) {
            this.serveurLocal.close();
            console.log('🌐 Serveur local arrêté');
        }
        
        if (this.serveurFormation) {
            this.serveurFormation.close();
            console.log('🏛️ Serveur formation arrêté');
        }
        
        if (this.intervalSurveillance) {
            clearInterval(this.intervalSurveillance);
            console.log('👁️ Surveillance arrêtée');
        }
        
        console.log('✅ Pont arrêté proprement');
    }
}

// Export
module.exports = PontCommunicationDirect;

// Test automatique si exécuté directement
if (require.main === module) {
    console.log('🚀 LANCEMENT PONT COMMUNICATION DIRECT');
    console.log('======================================');
    
    const pont = new PontCommunicationDirect();
    
    console.log('\n🌐 ACCÈS PONT:');
    console.log('Interface: http://localhost:8081');
    console.log('Formation: http://localhost:8082');
    
    // Gestion arrêt manuel
    process.on('SIGINT', () => {
        console.log('\n🛑 Arrêt manuel demandé...');
        pont.arreterPont();
        process.exit(0);
    });
}
