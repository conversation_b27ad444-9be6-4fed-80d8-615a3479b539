<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Chat LOUNA-AI</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        
        .chat-container {
            border: 1px solid #333;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            background: #2a2a2a;
        }
        
        .message {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        
        .user-message {
            background: #0066cc;
            text-align: right;
        }
        
        .ai-message {
            background: #006600;
            text-align: left;
        }
        
        .error-message {
            background: #cc0000;
            text-align: center;
        }
        
        .input-container {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        input[type="text"] {
            flex: 1;
            padding: 10px;
            border: 1px solid #333;
            border-radius: 5px;
            background: #333;
            color: #fff;
        }
        
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            background: #0066cc;
            color: #fff;
            cursor: pointer;
        }
        
        button:hover {
            background: #0088ff;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            background: #333;
            text-align: center;
        }
    </style>
</head>
<body>
    <h1>🧠 Test Chat LOUNA-AI</h1>
    
    <div class="status" id="status">
        Prêt à tester...
    </div>
    
    <div class="chat-container" id="chat-container">
        <!-- Messages apparaîtront ici -->
    </div>
    
    <div class="input-container">
        <input type="text" id="message-input" placeholder="Tapez votre message..." onkeypress="handleKeyPress(event)">
        <button onclick="sendMessage()">Envoyer</button>
        <button onclick="testQuestions()">Test Auto</button>
        <button onclick="clearChat()">Effacer</button>
    </div>

    <script>
        const chatContainer = document.getElementById('chat-container');
        const messageInput = document.getElementById('message-input');
        const statusDiv = document.getElementById('status');
        
        function addMessage(content, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message`;
            messageDiv.innerHTML = content;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
        
        function updateStatus(message, color = '#333') {
            statusDiv.textContent = message;
            statusDiv.style.background = color;
        }
        
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;
            
            // Afficher message utilisateur
            addMessage(`👤 ${message}`, 'user');
            messageInput.value = '';
            
            updateStatus('Envoi en cours...', '#666');
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ message: message })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                if (data.response) {
                    addMessage(`🤖 ${data.response}`, 'ai');
                    updateStatus('Réponse reçue !', '#006600');
                } else {
                    addMessage(`❌ Erreur: ${data.error || 'Réponse vide'}`, 'error');
                    updateStatus('Erreur dans la réponse', '#cc0000');
                }
                
            } catch (error) {
                console.error('Erreur:', error);
                addMessage(`❌ Erreur de connexion: ${error.message}`, 'error');
                updateStatus('Erreur de connexion', '#cc0000');
            }
        }
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        async function testQuestions() {
            const questions = [
                "Bonjour, comment ça va ?",
                "Quelle est la capitale de l'Allemagne ?",
                "Explique-moi l'intelligence artificielle en une phrase",
                "Calcule 15 + 27",
                "Raconte-moi une blague courte"
            ];
            
            updateStatus('Test automatique en cours...', '#666');
            
            for (let i = 0; i < questions.length; i++) {
                const question = questions[i];
                
                // Afficher la question
                addMessage(`👤 ${question}`, 'user');
                updateStatus(`Test ${i+1}/${questions.length}: ${question}`, '#666');
                
                try {
                    const response = await fetch('/api/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ message: question })
                    });
                    
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}`);
                    }
                    
                    const data = await response.json();
                    
                    if (data.response) {
                        addMessage(`🤖 ${data.response}`, 'ai');
                        
                        // Vérifier si c'est toujours la même réponse
                        if (data.response === "Paris est la capitale de la France.") {
                            addMessage(`🚨 PROBLÈME DÉTECTÉ: Réponse figée !`, 'error');
                        }
                    } else {
                        addMessage(`❌ Erreur: ${data.error || 'Réponse vide'}`, 'error');
                    }
                    
                } catch (error) {
                    addMessage(`❌ Erreur: ${error.message}`, 'error');
                }
                
                // Pause entre questions
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
            
            updateStatus('Test automatique terminé', '#006600');
        }
        
        function clearChat() {
            chatContainer.innerHTML = '';
            updateStatus('Chat effacé', '#333');
        }
        
        // Test de connexion au démarrage
        window.addEventListener('load', async () => {
            try {
                const response = await fetch('/api/status');
                if (response.ok) {
                    updateStatus('✅ Connexion au serveur OK', '#006600');
                } else {
                    updateStatus('⚠️ Serveur accessible mais problème', '#cc6600');
                }
            } catch (error) {
                updateStatus('❌ Impossible de se connecter au serveur', '#cc0000');
            }
        });
    </script>
</body>
</html>
