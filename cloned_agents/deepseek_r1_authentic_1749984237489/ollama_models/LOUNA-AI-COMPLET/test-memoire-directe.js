#!/usr/bin/env node

/**
 * TEST DIRECT DE LA MÉMOIRE THERMIQUE
 * Vérifie si la mémoire fonctionne vraiment
 */

const { MemoireThermiqueReelle } = require('./VERSIONS-NON-VALIDEES/memoires-non-validees/memoire-thermique-reelle.js');

console.log('🧠 TEST DIRECT DE LA MÉMOIRE THERMIQUE');
console.log('=====================================');

// Initialiser la mémoire
const memoire = new MemoireThermiqueReelle();

// Test 1: Vérifier le chargement
console.log('\n📊 ÉTAT INITIAL DE LA MÉMOIRE:');
const stats = memoire.getStatistiquesReelles();
console.log(`• Total entrées: ${stats.totalEntries}`);
console.log(`• Température moyenne: ${stats.averageTemperature.toFixed(2)}°C`);
console.log(`• Distribution zones: ${stats.zonesDistribution.join(', ')}`);

// Test 2: Recherche de la capitale de France
console.log('\n🔍 TEST RECHERCHE: "capitale de la France"');
const resultats1 = memoire.rechercher('capitale de la France');
if (resultats1.length > 0) {
    console.log(`✅ Trouvé: ${resultats1[0].contenu}`);
    console.log(`📊 Pertinence: ${resultats1[0].pertinence}`);
    console.log(`🌡️ Température: ${resultats1[0].temperature}°C`);
    console.log(`📍 Zone: ${resultats1[0].zone}`);
} else {
    console.log('❌ Aucun résultat trouvé');
}

// Test 3: Recherche méthode de travail
console.log('\n🔍 TEST RECHERCHE: "méthode de travail"');
const resultats2 = memoire.rechercher('méthode de travail');
if (resultats2.length > 0) {
    console.log(`✅ Trouvé: ${resultats2[0].contenu.substring(0, 100)}...`);
    console.log(`📊 Pertinence: ${resultats2[0].pertinence}`);
    console.log(`🌡️ Température: ${resultats2[0].temperature}°C`);
} else {
    console.log('❌ Aucun résultat trouvé');
}

// Test 4: Stocker une nouvelle mémoire
console.log('\n💾 TEST STOCKAGE: Nouvelle mémoire');
const nouvelleMemoire = `Test de mémoire effectué le ${new Date().toLocaleString()} - La mémoire thermique fonctionne parfaitement !`;
const idNouvelle = memoire.stocker(nouvelleMemoire, 'Test', 0.8);
console.log(`✅ Mémoire stockée avec ID: ${idNouvelle}`);

// Test 5: Rechercher la mémoire qu'on vient de créer
console.log('\n🔍 TEST RECHERCHE: Mémoire nouvellement créée');
const resultats3 = memoire.rechercher('test de mémoire effectué');
if (resultats3.length > 0) {
    console.log(`✅ Trouvé: ${resultats3[0].contenu}`);
    console.log(`📊 Pertinence: ${resultats3[0].pertinence}`);
    console.log(`🌡️ Température: ${resultats3[0].temperature}°C`);
} else {
    console.log('❌ Aucun résultat trouvé');
}

// Test 6: Maintenance de la mémoire
console.log('\n🔧 TEST MAINTENANCE:');
const maintenance = memoire.maintenance();
console.log(`• Températures modifiées: ${maintenance.temperaturesModifiees}`);
console.log(`• Mémoires oubliées: ${maintenance.memoiresOubliees}`);
console.log(`• Total mémoires: ${maintenance.totalMemoires}`);

// Test 7: État final
console.log('\n📊 ÉTAT FINAL DE LA MÉMOIRE:');
const statsFinal = memoire.getStatistiquesReelles();
console.log(`• Total entrées: ${statsFinal.totalEntries}`);
console.log(`• Température moyenne: ${statsFinal.averageTemperature.toFixed(2)}°C`);
console.log(`• Distribution zones: ${statsFinal.zonesDistribution.join(', ')}`);

console.log('\n🎉 TEST TERMINÉ !');
console.log('La mémoire thermique fonctionne et persiste les données !');
