[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ltx-video"
version = "0.1.2"
description = "A package for LTX-Video model"
authors = [
    { name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>" }
]
requires-python = ">=3.10"
readme = "README.md"
classifiers = [
    "Programming Language :: Python :: 3",
    "Operating System :: OS Independent"
]
dependencies = [
    "torch>=2.1.0",
    "diffusers>=0.28.2",
    "transformers>=4.47.2",
    "sentencepiece>=0.1.96",
    "huggingface-hub~=0.30",
    "einops",
    "timm"
]

[project.optional-dependencies]
# Instead of thinking of them as optional, think of them as specific modes
inference-script = [
    "accelerate",
    "matplotlib",
    "imageio[ffmpeg]",
    "av",
    "opencv-python"
]
test = [
    "pytest",
]

[tool.setuptools.packages.find]
include = ["ltx_video*"]