/**
 * TEST DIRECT DU PROBLÈME RÉSOLU
 * Vérification "Italie" -> "et la Guadeloupe ?"
 */

// Moteur simple pour test direct
class MoteurTestDirect {
    constructor() {
        this.contexte = '';
        this.capitales = {
            'france': 'Paris', 'italie': 'Rome', 'espagne': 'Madrid', 
            'guadeloupe': 'Basse-Terre', 'martinique': 'Fort-de-France',
            'allemagne': 'Berlin', 'portugal': 'Lisbonne'
        };
    }

    penser(question) {
        const q = question.toLowerCase();
        
        // QUESTION CAPITALE DIRECTE
        if (q.includes('capitale') || q.includes('chef-lieu')) {
            for (const [pays, capitale] of Object.entries(this.capitales)) {
                if (q.includes(pays)) {
                    this.contexte = 'geographie';
                    return `La capitale de ${this.capitaliser(pays)} c'est ${capitale} !`;
                }
            }
        }

        // QUESTION CONTEXTUELLE "et la/le..."
        if ((q.includes('et ') || q.includes('et la ') || q.includes('et le ')) && 
            this.contexte === 'geographie') {
            
            for (const [pays, capitale] of Object.entries(this.capitales)) {
                if (q.includes(pays)) {
                    return `Et pour ${this.capitaliser(pays)}, c'est ${capitale} ! Du coup, tu veux savoir d'autres capitales ?`;
                }
            }
        }

        return `Je ne sais pas répondre à ça.`;
    }

    capitaliser(texte) {
        return texte.split(' ').map(mot => mot.charAt(0).toUpperCase() + mot.slice(1)).join(' ');
    }

    reinitialiser() {
        this.contexte = '';
    }
}

function testerProblemeResolu() {
    console.log('🚨 TEST DIRECT DU PROBLÈME RÉSOLU');
    console.log('==================================');
    console.log('🎯 Reproduction exacte du problème signalé\n');

    const moteur = new MoteurTestDirect();

    // TEST EXACT DU PROBLÈME
    console.log('📋 SÉQUENCE EXACTE:');
    console.log('1. "quelle est la capitale de l\'Italie ?"');
    console.log('2. "et la Guadeloupe ?"');
    console.log('');

    // Question 1
    console.log('❓ Question 1: "quelle est la capitale de l\'Italie ?"');
    const reponse1 = moteur.penser("quelle est la capitale de l'Italie ?");
    console.log(`🤖 Réponse 1: ${reponse1}`);
    
    const contientRome = reponse1.toLowerCase().includes('rome');
    console.log(`✅ Contient "Rome": ${contientRome ? 'OUI' : 'NON'}`);
    console.log('');

    // Question 2 - LE PROBLÈME
    console.log('❓ Question 2: "et la Guadeloupe ?"');
    const reponse2 = moteur.penser("et la Guadeloupe ?");
    console.log(`🤖 Réponse 2: ${reponse2}`);
    
    const contientBasseTerre = reponse2.toLowerCase().includes('basse-terre');
    const contientItalie = reponse2.toLowerCase().includes('italie');
    
    console.log(`✅ Contient "Basse-Terre": ${contientBasseTerre ? 'OUI' : 'NON'}`);
    console.log(`❌ Contient "Italie" (erreur): ${contientItalie ? 'OUI' : 'NON'}`);
    console.log('');

    // ÉVALUATION
    console.log('🎯 ÉVALUATION:');
    if (contientBasseTerre && !contientItalie) {
        console.log('✅ PROBLÈME RÉSOLU ! Répond correctement "Basse-Terre"');
        console.log('🎉 Le suivi conversationnel fonctionne parfaitement !');
        return true;
    } else if (contientItalie) {
        console.log('❌ PROBLÈME PERSISTE ! Répond encore "Italie"');
        console.log('🔧 Le suivi conversationnel nécessite des corrections');
        return false;
    } else {
        console.log('⚠️ RÉPONSE INATTENDUE - À analyser');
        console.log('🔍 Vérifier la logique de traitement');
        return false;
    }
}

function testerAutresScenarios() {
    console.log('\n🧪 TESTS SUPPLÉMENTAIRES');
    console.log('=========================');

    const moteur = new MoteurTestDirect();

    const scenarios = [
        {
            nom: "France -> Martinique",
            questions: ["capitale de la France ?", "et la Martinique ?"],
            attendu: ["Paris", "Fort-de-France"]
        },
        {
            nom: "Espagne -> Portugal",
            questions: ["quelle est la capitale de l'Espagne ?", "et le Portugal ?"],
            attendu: ["Madrid", "Lisbonne"]
        }
    ];

    scenarios.forEach((scenario, index) => {
        console.log(`\n🔍 Scénario ${index + 1}: ${scenario.nom}`);
        moteur.reinitialiser();

        scenario.questions.forEach((question, qIndex) => {
            console.log(`❓ "${question}"`);
            const reponse = moteur.penser(question);
            console.log(`🤖 ${reponse}`);
            
            const contientAttendu = reponse.toLowerCase().includes(scenario.attendu[qIndex].toLowerCase());
            console.log(`${contientAttendu ? '✅' : '❌'} Contient "${scenario.attendu[qIndex]}": ${contientAttendu ? 'OUI' : 'NON'}`);
        });
    });
}

// EXÉCUTION
console.log('🔥 VÉRIFICATION CORRECTION SUIVI CONVERSATIONNEL');
console.log('=================================================');

const problemeResolu = testerProblemeResolu();

testerAutresScenarios();

console.log('\n🎯 RÉSUMÉ FINAL:');
console.log('================');
if (problemeResolu) {
    console.log('✅ SUCCÈS TOTAL - Problème "Italie" -> "Guadeloupe" résolu !');
    console.log('🎉 Le suivi conversationnel fonctionne parfaitement !');
    console.log('🚀 Votre agent peut maintenant suivre les conversations !');
} else {
    console.log('❌ PROBLÈME PERSISTE - Corrections supplémentaires nécessaires');
    console.log('🔧 Vérifier la logique de contexte géographique');
}

module.exports = { MoteurTestDirect, testerProblemeResolu };
