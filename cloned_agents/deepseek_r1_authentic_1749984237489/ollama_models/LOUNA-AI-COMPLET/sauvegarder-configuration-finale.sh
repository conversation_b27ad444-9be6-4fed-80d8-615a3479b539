#!/bin/bash

# 🔒 SCRIPT DE SAUVEGARDE CONFIGURATION FINALE LOUNA-AI
# =====================================================

echo "🔒 SAUVEGARDE CONFIGURATION FINALE LOUNA-AI"
echo "==========================================="

# Créer dossier de sauvegarde avec timestamp
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="SAUVEGARDE_FINALE_${TIMESTAMP}"

echo "📁 Création dossier: $BACKUP_DIR"
mkdir -p "$BACKUP_DIR"

# Sauvegarder les fichiers critiques validés
echo "💾 Sauvegarde fichiers critiques..."

# Serveur principal (VALIDÉ)
cp serveur-interface-complete.js "$BACKUP_DIR/"
echo "✅ serveur-interface-complete.js sauvegardé"

# Interface principale (VALIDÉE)
cp interface-louna-complete.html "$BACKUP_DIR/"
echo "✅ interface-louna-complete.html sauvegardé"

# Modules validés
cp systeme-cognitif-avance.js "$BACKUP_DIR/"
echo "✅ systeme-cognitif-avance.js sauvegardé"

cp auto-evolution.js "$BACKUP_DIR/"
echo "✅ auto-evolution.js sauvegardé"

cp moteur-raisonnement-reel.js "$BACKUP_DIR/"
echo "✅ moteur-raisonnement-reel.js sauvegardé"

cp recherche-google-securisee.js "$BACKUP_DIR/"
echo "✅ recherche-google-securisee.js sauvegardé"

cp gestionnaire-applications-intelligent.js "$BACKUP_DIR/"
echo "✅ gestionnaire-applications-intelligent.js sauvegardé"

cp systeme-scan-intelligent.js "$BACKUP_DIR/"
echo "✅ systeme-scan-intelligent.js sauvegardé"

# Mémoire thermique validée
mkdir -p "$BACKUP_DIR/VERSIONS-NON-VALIDEES/memoires-non-validees/"
cp VERSIONS-NON-VALIDEES/memoires-non-validees/memoire-thermique-reelle.js "$BACKUP_DIR/VERSIONS-NON-VALIDEES/memoires-non-validees/"
echo "✅ memoire-thermique-reelle.js sauvegardé"

# Documentation
cp CONFIGURATION-VERROUILLEE-FINALE.md "$BACKUP_DIR/"
echo "✅ Documentation sauvegardée"

# Package.json et dépendances
if [ -f package.json ]; then
    cp package.json "$BACKUP_DIR/"
    echo "✅ package.json sauvegardé"
fi

# Créer script de restauration
cat > "$BACKUP_DIR/restaurer-configuration.sh" << 'EOF'
#!/bin/bash
echo "🔄 RESTAURATION CONFIGURATION FINALE LOUNA-AI"
echo "============================================="

# Copier tous les fichiers vers le répertoire parent
cp -v *.js ../
cp -v *.html ../
cp -v *.md ../
cp -v package.json ../ 2>/dev/null || true

# Restaurer mémoire thermique
mkdir -p ../VERSIONS-NON-VALIDEES/memoires-non-validees/
cp -v VERSIONS-NON-VALIDEES/memoires-non-validees/*.js ../VERSIONS-NON-VALIDEES/memoires-non-validees/

echo "✅ Configuration finale restaurée"
echo "🚀 Lancer: node serveur-interface-complete.js"
EOF

chmod +x "$BACKUP_DIR/restaurer-configuration.sh"
echo "✅ Script de restauration créé"

# Créer fichier de vérification
cat > "$BACKUP_DIR/VERIFICATION.md" << EOF
# 🔍 VÉRIFICATION SAUVEGARDE FINALE

## Fichiers sauvegardés :
- serveur-interface-complete.js (Serveur principal)
- interface-louna-complete.html (Interface)
- systeme-cognitif-avance.js (Filtrage)
- auto-evolution.js (Évolution)
- moteur-raisonnement-reel.js (Raisonnement)
- recherche-google-securisee.js (Recherche)
- gestionnaire-applications-intelligent.js (Apps)
- systeme-scan-intelligent.js (Scan)
- memoire-thermique-reelle.js (Mémoire)

## État au moment de la sauvegarde :
- QI : 349
- Mémoires : 52
- Température : 66.21°C
- Zone : 1
- Applications : 415

## Pour restaurer :
\`\`\`bash
cd $BACKUP_DIR
./restaurer-configuration.sh
\`\`\`
EOF

echo ""
echo "🎉 SAUVEGARDE TERMINÉE"
echo "====================="
echo "📁 Dossier: $BACKUP_DIR"
echo "🔄 Restaurer: cd $BACKUP_DIR && ./restaurer-configuration.sh"
echo "✅ Configuration finale sécurisée"
