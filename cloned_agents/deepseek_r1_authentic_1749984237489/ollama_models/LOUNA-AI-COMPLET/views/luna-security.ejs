<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %></title>
  <link rel="stylesheet" href="/luna/css/luna-main.css">
  <link rel="stylesheet" href="/luna/css/luna-security.css">
  <link rel="stylesheet" href="/luna/css/luna-security-new.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="/socket.io/socket.io.js"></script>
  <style>
    /* Styles de secours pour les icônes si Bootstrap Icons ne se charge pas */
    .status-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
    }
    .status-icon:before {
      content: "";
      display: inline-block;
      width: 100%;
      height: 100%;
      background-size: 60%;
      background-position: center;
      background-repeat: no-repeat;
    }
    #vpn-icon:before { content: "🔒"; }
    #antivirus-icon:before { content: "🛡️"; }
    #firewall-icon:before { content: "🔥"; }
    #security-level-icon:before { content: "🔐"; }
    #encryption-icon:before { content: "🔑"; }
    #behavioral-icon:before { content: "🧠"; }
  </style>
</head>
<body class="luna-body">
  <div class="luna-container">
    <header class="luna-header">
      <div class="luna-logo">
        <img src="/luna/images/luna-logo.png" alt="Luna Logo">
        <h1>Luna - Sécurité</h1>
      </div>
      <nav class="luna-nav">
        <ul>
          <li><a href="/luna" class="nav-link">Accueil</a></li>
          <li><a href="/luna/memory" class="nav-link">Mémoire</a></li>
          <li><a href="/luna/accelerators" class="nav-link">Accélérateurs</a></li>
          <li><a href="/luna/training" class="nav-link">Formation</a></li>
          <li><a href="/luna/brain" class="nav-link">Cerveau</a></li>
          <li><a href="/luna/code" class="nav-link">Code</a></li>
          <li><a href="/luna/security" class="nav-link active">Sécurité</a></li>
        </ul>
      </nav>
    </header>

    <main class="luna-main">
      <div class="security-container">
        <div class="security-header">
          <h2>Système de Sécurité</h2>
          <div class="security-controls">
            <button id="refresh-security" class="luna-button"><i class="bi bi-arrow-clockwise me-2"></i>Rafraîchir</button>
            <button id="scan-now-btn" class="luna-button primary"><i class="bi bi-search me-2"></i>Analyser maintenant</button>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="card mb-4">
              <div class="card-header">
                <h3><i class="bi bi-shield-lock me-2"></i>Protection VPN</h3>
              </div>
              <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                  <div class="status-badge me-3" id="vpn-status-badge">Déconnecté</div>
                  <h4 class="mb-0">Statut: <span id="vpn-status-text">Non protégé</span></h4>
                </div>

                <div class="vpn-details mb-4">
                  <div class="row mb-2">
                    <div class="col-5">Adresse IP:</div>
                    <div class="col-7" id="vpn-ip">Non masquée</div>
                  </div>
                  <div class="row mb-2">
                    <div class="col-5">Localisation:</div>
                    <div class="col-7" id="vpn-location">Guadeloupe</div>
                  </div>
                  <div class="row mb-2">
                    <div class="col-5">Protection:</div>
                    <div class="col-7" id="vpn-protection">0%</div>
                  </div>
                  <div class="row mb-2">
                    <div class="col-5">Temps de connexion:</div>
                    <div class="col-7" id="vpn-uptime">00:00:00</div>
                  </div>
                </div>

                <button id="vpn-connect-btn" class="btn btn-luna w-100">
                  <i class="bi bi-shield-check me-2"></i>Connecter le VPN
                </button>
              </div>
            </div>
          </div>

          <div class="col-md-6">
            <div class="card mb-4">
              <div class="card-header">
                <h3><i class="bi bi-virus me-2"></i>Protection Antivirus</h3>
              </div>
              <div class="card-body">
                <div class="form-check form-switch mb-3">
                  <input class="form-check-input" type="checkbox" id="antivirus-toggle" checked>
                  <label class="form-check-label" for="antivirus-toggle">Protection antivirus active</label>
                </div>

                <div class="antivirus-details mb-4">
                  <div class="row mb-2">
                    <div class="col-5">Dernière analyse:</div>
                    <div class="col-7" id="last-scan">Jamais</div>
                  </div>
                  <div class="row mb-2">
                    <div class="col-5">Fichiers analysés:</div>
                    <div class="col-7" id="scanned-files">0</div>
                  </div>
                  <div class="row mb-2">
                    <div class="col-5">Menaces détectées:</div>
                    <div class="col-7" id="detected-threats">0</div>
                  </div>
                  <div class="row mb-2">
                    <div class="col-5">Protection en temps réel:</div>
                    <div class="col-7" id="realtime-protection">Active</div>
                  </div>
                </div>

                <button id="scan-now-btn" class="btn btn-luna w-100">
                  <i class="bi bi-search me-2"></i>Analyser maintenant
                </button>
              </div>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-12">
            <div class="card mb-4">
              <div class="card-header">
                <h3><i class="bi bi-gear me-2"></i>Paramètres de sécurité</h3>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label for="vpn-server-select" class="form-label">Serveur VPN</label>
                      <select class="form-select" id="vpn-server-select">
                        <option value="fr" selected>France (Recommandé)</option>
                        <option value="us">États-Unis</option>
                        <option value="uk">Royaume-Uni</option>
                        <option value="de">Allemagne</option>
                        <option value="jp">Japon</option>
                      </select>
                    </div>

                    <div class="mb-3">
                      <label for="security-level" class="form-label">Niveau de sécurité</label>
                      <select class="form-select" id="security-level">
                        <option value="standard" selected>Standard</option>
                        <option value="high">Élevé</option>
                        <option value="maximum">Maximum</option>
                      </select>
                    </div>
                  </div>

                  <div class="col-md-6">
                    <div class="form-check mb-3">
                      <input class="form-check-input" type="checkbox" id="firewall-toggle" checked>
                      <label class="form-check-label" for="firewall-toggle">Pare-feu actif</label>
                    </div>

                    <div class="form-check mb-3">
                      <input class="form-check-input" type="checkbox" id="auto-update-toggle" checked>
                      <label class="form-check-label" for="auto-update-toggle">Mises à jour automatiques</label>
                    </div>

                    <div class="form-check mb-3">
                      <input class="form-check-input" type="checkbox" id="data-protection-toggle" checked>
                      <label class="form-check-label" for="data-protection-toggle">Protection des données</label>
                    </div>

                    <div class="form-check mb-3">
                      <input class="form-check-input" type="checkbox" id="phishing-protection-toggle" checked>
                      <label class="form-check-label" for="phishing-protection-toggle">Protection contre le phishing</label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="card mb-4">
              <div class="card-header">
                <h3><i class="bi bi-shield-check me-2"></i>Score de sécurité</h3>
              </div>
              <div class="card-body">
                <div class="text-center mb-4">
                  <div class="security-score-circle">
                    <span id="security-score">85</span>
                    <span class="score-label">/100</span>
                  </div>
                  <h4 class="mt-3" id="security-level-text">Protection élevée</h4>
                </div>

                <div class="security-score-details">
                  <div class="row mb-2">
                    <div class="col-8">Protection VPN</div>
                    <div class="col-4 text-end">
                      <span class="badge bg-success" id="vpn-score-badge">20/20</span>
                    </div>
                  </div>
                  <div class="row mb-2">
                    <div class="col-8">Protection antivirus</div>
                    <div class="col-4 text-end">
                      <span class="badge bg-success" id="antivirus-score-badge">25/25</span>
                    </div>
                  </div>
                  <div class="row mb-2">
                    <div class="col-8">Pare-feu</div>
                    <div class="col-4 text-end">
                      <span class="badge bg-success" id="firewall-score-badge">20/20</span>
                    </div>
                  </div>
                  <div class="row mb-2">
                    <div class="col-8">Protection des données</div>
                    <div class="col-4 text-end">
                      <span class="badge bg-warning" id="data-score-badge">15/20</span>
                    </div>
                  </div>
                  <div class="row mb-2">
                    <div class="col-8">Mises à jour</div>
                    <div class="col-4 text-end">
                      <span class="badge bg-danger" id="updates-score-badge">5/15</span>
                    </div>
                  </div>
                </div>

                <button id="improve-security-btn" class="btn btn-luna w-100 mt-3">
                  <i class="bi bi-lightning-charge me-2"></i>Améliorer la sécurité
                </button>
              </div>
            </div>
          </div>

          <div class="col-md-6">
            <div class="card mb-4">
              <div class="card-header">
                <h3><i class="bi bi-journal-text me-2"></i>Journal de sécurité</h3>
              </div>
              <div class="card-body p-0">
                <div class="security-log" id="settings-log">
                  <div class="log-entry">
                    <span class="log-time"><%= new Date().toLocaleString('fr-FR', {day: '2-digit', month: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit'}) %></span>
                    <span class="log-message">Système de sécurité initialisé</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-12">
            <div class="card mb-4">
              <div class="card-header">
                <h3><i class="bi bi-graph-up me-2"></i>Statistiques de sécurité</h3>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-3 col-sm-6 mb-4">
                    <div class="stat-card text-center">
                      <div class="stat-icon bg-primary">
                        <i class="bi bi-shield"></i>
                      </div>
                      <h4>Menaces bloquées</h4>
                      <div class="stat-value">0</div>
                      <div class="stat-label">Aujourd'hui</div>
                    </div>
                  </div>

                  <div class="col-md-3 col-sm-6 mb-4">
                    <div class="stat-card text-center">
                      <div class="stat-icon bg-success">
                        <i class="bi bi-check-circle"></i>
                      </div>
                      <h4>Fichiers analysés</h4>
                      <div class="stat-value">1,245</div>
                      <div class="stat-label">Cette semaine</div>
                    </div>
                  </div>

                  <div class="col-md-3 col-sm-6 mb-4">
                    <div class="stat-card text-center">
                      <div class="stat-icon bg-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                      </div>
                      <h4>Alertes</h4>
                      <div class="stat-value">2</div>
                      <div class="stat-label">Ce mois</div>
                    </div>
                  </div>

                  <div class="col-md-3 col-sm-6 mb-4">
                    <div class="stat-card text-center">
                      <div class="stat-icon bg-info">
                        <i class="bi bi-clock-history"></i>
                      </div>
                      <h4>Dernière analyse</h4>
                      <div class="stat-value" id="last-scan-time-stat">Jamais</div>
                      <div class="stat-label" id="last-scan-date-stat"></div>
                    </div>
                  </div>
                </div>

                <div class="row mt-4">
                  <div class="col-md-6">
                    <h4 class="mb-3">Activité de sécurité</h4>
                    <canvas id="security-activity-chart" height="200"></canvas>
                  </div>

                  <div class="col-md-6">
                    <h4 class="mb-3">Menaces détectées</h4>
                    <canvas id="threats-chart" height="200"></canvas>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </main>

    <!-- Pied de page fixe -->
    <footer class="footer-fixed">
      <small>Vision Ultra - Interface Sécurité - <span id="current-datetime"></span> - &copy; 2024 Jean Passave - Sainte-Anne, Guadeloupe (97180)</small>
    </footer>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const socket = io();
      let securityState = {};
      let securityActivityChart = null;
      let threatsChart = null;

      // Initialiser les graphiques
      initCharts();

      // Charger les données de sécurité au chargement de la page
      loadSecurityData();

      // Gestionnaire d'événements pour le bouton de rafraîchissement
      document.getElementById('refresh-security').addEventListener('click', loadSecurityData);

      // Gestionnaire d'événements pour le bouton d'analyse
      document.getElementById('scan-now').addEventListener('click', startScan);

      // Gestionnaire d'événements pour le bouton de VPN
      document.getElementById('toggle-vpn').addEventListener('click', toggleVPN);

      // Gestionnaire d'événements pour le bouton d'antivirus
      document.getElementById('toggle-antivirus').addEventListener('click', toggleAntivirus);

      // Gestionnaire d'événements pour le bouton de pare-feu
      document.getElementById('toggle-firewall').addEventListener('click', toggleFirewall);

      // Gestionnaire d'événements pour le sélecteur de niveau de sécurité
      document.getElementById('security-level-select').addEventListener('change', function() {
        setSecurityLevel(this.value);
      });

      // Gestionnaire d'événements pour le bouton de chiffrement des données
      document.getElementById('toggle-encryption').addEventListener('click', toggleEncryption);

      // Gestionnaire d'événements pour le bouton d'analyse comportementale
      document.getElementById('toggle-behavioral').addEventListener('click', toggleBehavioralAnalysis);

      // Gestionnaire d'événements pour le bouton de vérification des mises à jour
      document.getElementById('check-updates').addEventListener('click', checkSecurityUpdates);

      // Gestionnaire d'événements pour le bouton d'application des paramètres VPN
      document.getElementById('apply-vpn-settings').addEventListener('click', applyVpnSettings);

      // Gestionnaire d'événements pour le bouton d'enregistrement des paramètres d'analyse
      document.getElementById('save-scan-settings').addEventListener('click', saveScanSettings);

      // Gestionnaire d'événements pour le bouton d'enregistrement des paramètres de protection
      document.getElementById('save-protection-settings').addEventListener('click', saveProtectionSettings);

      // Fonction pour charger les données de sécurité
      function loadSecurityData() {
        addLogEntry('Chargement des données de sécurité...', 'info');
        socket.emit('get security state');
      }

      // Écouter les données de sécurité
      socket.on('security state', function(data) {
        if (data.success) {
          securityState = data.state;
          updateSecurityUI(securityState);
          addLogEntry('Données de sécurité chargées avec succès', 'success');
        } else {
          addLogEntry('Erreur lors du chargement des données de sécurité: ' + (data.error || 'Erreur inconnue'), 'error');
          showError('Erreur lors du chargement des données de sécurité');
        }
      });

      // Fonction pour démarrer une analyse
      function startScan() {
        const button = document.getElementById('scan-now');
        button.disabled = true;
        button.textContent = 'Analyse en cours...';

        addLogEntry('Démarrage de l\'analyse...', 'info');
        socket.emit('start security scan');
      }

      // Fonction pour activer/désactiver le VPN
      function toggleVPN() {
        const button = document.getElementById('toggle-vpn');
        button.disabled = true;

        if (securityState.vpnConnected) {
          addLogEntry('Déconnexion du VPN...', 'info');
          socket.emit('disconnect vpn');
        } else {
          addLogEntry('Connexion au VPN...', 'info');
          socket.emit('connect vpn');
        }
      }

      // Fonction pour activer/désactiver l'antivirus
      function toggleAntivirus() {
        const button = document.getElementById('toggle-antivirus');
        button.disabled = true;

        if (securityState.options.antivirusEnabled) {
          addLogEntry('Désactivation de l\'antivirus...', 'info');
          socket.emit('toggle antivirus', { enabled: false });
        } else {
          addLogEntry('Activation de l\'antivirus...', 'info');
          socket.emit('toggle antivirus', { enabled: true });
        }
      }

      // Fonction pour activer/désactiver le pare-feu
      function toggleFirewall() {
        const button = document.getElementById('toggle-firewall');
        button.disabled = true;

        if (securityState.firewallActive) {
          addLogEntry('Désactivation du pare-feu...', 'info');
          socket.emit('toggle firewall', { enabled: false });
        } else {
          addLogEntry('Activation du pare-feu...', 'info');
          socket.emit('toggle firewall', { enabled: true });
        }
      }

      // Fonction pour définir le niveau de sécurité
      function setSecurityLevel(level) {
        addLogEntry(`Définition du niveau de sécurité: ${level}`, 'info');
        socket.emit('set security level', { level });
      }

      // Fonction pour activer/désactiver le chiffrement des données
      function toggleEncryption() {
        const button = document.getElementById('toggle-encryption');
        button.disabled = true;

        if (securityState.dataEncryption) {
          addLogEntry('Désactivation du chiffrement des données...', 'info');
          socket.emit('disable data encryption');
        } else {
          addLogEntry('Activation du chiffrement des données...', 'info');
          socket.emit('enable data encryption');
        }
      }

      // Fonction pour activer/désactiver l'analyse comportementale
      function toggleBehavioralAnalysis() {
        const button = document.getElementById('toggle-behavioral');
        button.disabled = true;

        if (securityState.behavioralAnalysis) {
          addLogEntry('Désactivation de l\'analyse comportementale...', 'info');
          socket.emit('disable behavioral analysis');
        } else {
          addLogEntry('Activation de l\'analyse comportementale...', 'info');
          socket.emit('enable behavioral analysis');
        }
      }

      // Fonction pour vérifier les mises à jour de sécurité
      function checkSecurityUpdates() {
        const button = document.getElementById('check-updates');
        if (button) button.disabled = true;

        addLogEntry('Vérification des mises à jour de sécurité...', 'info');

        const statusElement = document.getElementById('updates-status');
        if (statusElement) {
          statusElement.textContent = 'Statut: Vérification en cours...';
          statusElement.className = '';
        }

        socket.emit('check security updates');
      }

      // Fonction pour appliquer les paramètres VPN
      function applyVpnSettings() {
        const provider = document.getElementById('vpn-provider').value;
        const country = document.getElementById('vpn-country').value;
        const protocol = document.getElementById('vpn-protocol').value;
        const encryption = document.getElementById('vpn-encryption').value;

        const button = document.getElementById('apply-vpn-settings');
        if (button) button.disabled = true;

        addLogEntry(`Application des paramètres VPN (Fournisseur: ${provider}, Pays: ${country}, Protocole: ${protocol})...`, 'info');

        // Préparer les options VPN
        const vpnOptions = {
          provider,
          country,
          protocol,
          encryption
        };

        // Si le VPN est déjà connecté, le déconnecter d'abord
        if (securityState.vpnConnected) {
          socket.emit('disconnect vpn', () => {
            // Après la déconnexion, se reconnecter avec les nouveaux paramètres
            setTimeout(() => {
              socket.emit('connect vpn', vpnOptions);
            }, 1000);
          });
        } else {
          // Sinon, se connecter directement avec les nouveaux paramètres
          socket.emit('connect vpn', vpnOptions);
        }

        // Réactiver le bouton après un délai
        setTimeout(() => {
          if (button) button.disabled = false;
        }, 3000);
      }

      // Fonction pour enregistrer les paramètres d'analyse
      function saveScanSettings() {
        const frequency = document.getElementById('scan-frequency').value;
        const time = document.getElementById('scan-time').value;
        const type = document.getElementById('scan-type').value;
        const notify = document.getElementById('scan-notify').checked;

        const button = document.getElementById('save-scan-settings');
        if (button) button.disabled = true;

        addLogEntry(`Enregistrement des paramètres d'analyse (Fréquence: ${frequency}, Heure: ${time}, Type: ${type})...`, 'info');

        // Préparer les options d'analyse
        const scanOptions = {
          frequency,
          time,
          type,
          notify
        };

        // Envoyer les paramètres au serveur
        socket.emit('set scan options', scanOptions);

        // Simuler un délai pour l'enregistrement
        setTimeout(() => {
          addLogEntry('Paramètres d\'analyse enregistrés avec succès', 'success');
          if (button) button.disabled = false;

          // Afficher un message de succès
          showSuccess('Paramètres d\'analyse enregistrés');
        }, 1000);
      }

      // Fonction pour enregistrer les paramètres de protection
      function saveProtectionSettings() {
        const realtimeProtection = document.getElementById('realtime-protection').checked;
        const behavioralAnalysis = document.getElementById('behavioral-analysis').checked;
        const ransomwareProtection = document.getElementById('ransomware-protection').checked;
        const exploitProtection = document.getElementById('exploit-protection').checked;
        const networkProtection = document.getElementById('network-protection').checked;

        const button = document.getElementById('save-protection-settings');
        if (button) button.disabled = true;

        addLogEntry('Enregistrement des paramètres de protection...', 'info');

        // Préparer les options de protection
        const protectionOptions = {
          realtimeProtection,
          behavioralAnalysis,
          ransomwareProtection,
          exploitProtection,
          networkProtection
        };

        // Envoyer les paramètres au serveur
        socket.emit('set protection options', protectionOptions);

        // Simuler un délai pour l'enregistrement
        setTimeout(() => {
          addLogEntry('Paramètres de protection enregistrés avec succès', 'success');
          if (button) button.disabled = false;

          // Mettre à jour l'état de sécurité
          securityState.realTimeProtection = realtimeProtection;
          securityState.behavioralAnalysis = behavioralAnalysis;

          // Mettre à jour l'interface utilisateur
          updateBehavioralAnalysisStatus();

          // Afficher un message de succès
          showSuccess('Paramètres de protection enregistrés');
        }, 1000);
      }

      // Fonction pour afficher un message de succès
      function showSuccess(message) {
        const successElement = document.createElement('div');
        successElement.className = 'success-message';
        successElement.textContent = message;
        document.body.appendChild(successElement);

        // Afficher le message
        setTimeout(() => {
          successElement.classList.add('show');
        }, 100);

        // Masquer et supprimer le message après un délai
        setTimeout(() => {
          successElement.classList.remove('show');
          setTimeout(() => {
            document.body.removeChild(successElement);
          }, 300);
        }, 3000);
      }

      // Écouter les événements de sécurité
      socket.on('security scan started', function() {
        addLogEntry('Analyse de sécurité démarrée', 'info');
      });

      socket.on('security scan completed', function(data) {
        const button = document.getElementById('scan-now');
        button.disabled = false;
        button.textContent = 'Analyser maintenant';

        if (data.success) {
          addLogEntry(`Analyse terminée: ${data.results.scannedFiles} fichiers analysés, ${data.results.threats} menaces détectées`, 'success');
          updateScanResults(data.results);
          updateCharts(data.chartData);
        } else {
          addLogEntry('Erreur lors de l\'analyse: ' + (data.error || 'Erreur inconnue'), 'error');
          showError('Erreur lors de l\'analyse');
        }
      });

      socket.on('vpn connected', function(data) {
        const button = document.getElementById('toggle-vpn');
        button.disabled = false;

        if (data.success) {
          addLogEntry(`VPN connecté: IP ${data.ip}, Pays ${data.country}`, 'success');
          securityState.vpnConnected = true;
          securityState.vpnIP = data.ip;
          securityState.vpnCountry = data.country;
          updateVPNStatus();
        } else {
          addLogEntry('Erreur lors de la connexion au VPN: ' + (data.error || 'Erreur inconnue'), 'error');
          showError('Erreur lors de la connexion au VPN');
        }
      });

      socket.on('vpn disconnected', function(data) {
        const button = document.getElementById('toggle-vpn');
        button.disabled = false;

        if (data.success) {
          addLogEntry('VPN déconnecté', 'info');
          securityState.vpnConnected = false;
          securityState.vpnIP = null;
          securityState.vpnCountry = null;
          updateVPNStatus();
        } else {
          addLogEntry('Erreur lors de la déconnexion du VPN: ' + (data.error || 'Erreur inconnue'), 'error');
          showError('Erreur lors de la déconnexion du VPN');
        }
      });

      socket.on('antivirus toggled', function(data) {
        const button = document.getElementById('toggle-antivirus');
        button.disabled = false;

        if (data.success) {
          addLogEntry(`Antivirus ${data.enabled ? 'activé' : 'désactivé'}`, data.enabled ? 'success' : 'warning');
          securityState.options.antivirusEnabled = data.enabled;
          updateAntivirusStatus();
        } else {
          addLogEntry(`Erreur lors de la ${data.enabled ? 'l\'activation' : 'la désactivation'} de l'antivirus: ` + (data.error || 'Erreur inconnue'), 'error');
          showError(`Erreur lors de la ${data.enabled ? 'l\'activation' : 'la désactivation'} de l'antivirus`);
        }
      });

      socket.on('firewall toggled', function(data) {
        const button = document.getElementById('toggle-firewall');
        button.disabled = false;

        if (data.success) {
          addLogEntry(`Pare-feu ${data.enabled ? 'activé' : 'désactivé'}`, data.enabled ? 'success' : 'warning');
          securityState.firewallActive = data.enabled;
          updateFirewallStatus();
        } else {
          addLogEntry(`Erreur lors de la ${data.enabled ? 'l\'activation' : 'la désactivation'} du pare-feu: ` + (data.error || 'Erreur inconnue'), 'error');
          showError(`Erreur lors de la ${data.enabled ? 'l\'activation' : 'la désactivation'} du pare-feu`);
        }
      });

      socket.on('security level changed', function(data) {
        if (data.success) {
          addLogEntry(`Niveau de sécurité défini sur: ${data.level}`, 'success');
          securityState.securityLevel = data.level;
          updateSecurityLevelStatus();
        } else {
          addLogEntry('Erreur lors de la définition du niveau de sécurité: ' + (data.error || 'Erreur inconnue'), 'error');
          showError('Erreur lors de la définition du niveau de sécurité');
        }
      });

      socket.on('security data encryption', function(data) {
        const button = document.getElementById('toggle-encryption');
        button.disabled = false;

        if (data.success) {
          addLogEntry(`Chiffrement des données ${data.enabled ? 'activé' : 'désactivé'}`, data.enabled ? 'success' : 'warning');
          securityState.dataEncryption = data.enabled;
          updateEncryptionStatus();
        } else {
          addLogEntry(`Erreur lors de la ${data.enabled ? 'l\'activation' : 'la désactivation'} du chiffrement des données: ` + (data.error || 'Erreur inconnue'), 'error');
          showError(`Erreur lors de la ${data.enabled ? 'l\'activation' : 'la désactivation'} du chiffrement des données`);
        }
      });

      socket.on('security behavioral analysis', function(data) {
        const button = document.getElementById('toggle-behavioral');
        button.disabled = false;

        if (data.success) {
          addLogEntry(`Analyse comportementale ${data.enabled ? 'activée' : 'désactivée'}`, data.enabled ? 'success' : 'warning');
          securityState.behavioralAnalysis = data.enabled;
          updateBehavioralAnalysisStatus();
        } else {
          addLogEntry(`Erreur lors de la ${data.enabled ? 'l\'activation' : 'la désactivation'} de l'analyse comportementale: ` + (data.error || 'Erreur inconnue'), 'error');
          showError(`Erreur lors de la ${data.enabled ? 'l\'activation' : 'la désactivation'} de l'analyse comportementale`);
        }
      });

      socket.on('security updates', function(data) {
        const button = document.getElementById('check-updates');
        if (button) button.disabled = false;

        const statusElement = document.getElementById('updates-status');
        const detailsElement = document.getElementById('updates-details');

        if (data.success) {
          const results = data.results;

          if (results.available) {
            addLogEntry(`Mises à jour disponibles: version ${results.version}`, results.critical ? 'error' : 'warning');

            if (statusElement) {
              statusElement.textContent = `Statut: Mises à jour disponibles (v${results.version})`;
              statusElement.className = results.critical ? 'status-danger' : 'status-warning';
            }

            if (detailsElement) {
              detailsElement.textContent = `Dernière vérification: ${new Date().toLocaleString()}`;
            }

            showUpdateDetails(results);
          } else {
            addLogEntry('Aucune mise à jour disponible', 'success');

            if (statusElement) {
              statusElement.textContent = 'Statut: À jour';
              statusElement.className = 'status-success';
            }

            if (detailsElement) {
              detailsElement.textContent = `Dernière vérification: ${new Date().toLocaleString()}`;
            }
          }
        } else {
          addLogEntry('Erreur lors de la vérification des mises à jour: ' + (data.error || 'Erreur inconnue'), 'error');
          showError('Erreur lors de la vérification des mises à jour');

          if (statusElement) {
            statusElement.textContent = 'Statut: Erreur de vérification';
            statusElement.className = 'status-danger';
          }
        }
      });

      socket.on('security log', function(data) {
        addLogEntry(data.message, data.level || 'info');
      });

      // Fonction pour mettre à jour l'interface utilisateur de sécurité
      function updateSecurityUI(state) {
        updateVPNStatus();
        updateAntivirusStatus();
        updateFirewallStatus();
        updateSecurityLevelStatus();
        updateScanResults(state.lastScanResults);
        updateQuarantineList(state.quarantinedFiles);

        // Mettre à jour les nouvelles fonctionnalités
        if (state.dataEncryption !== undefined) {
          updateEncryptionStatus();
        }

        if (state.behavioralAnalysis !== undefined) {
          updateBehavioralAnalysisStatus();
        }

        if (state.securityScore !== undefined) {
          updateSecurityScore();
        }

        // Mettre à jour les statistiques en temps réel
        updateRealTimeStats(state);

        // Mettre à jour les formulaires avec les valeurs actuelles
        updateSettingsForms(state);
      }

      // Fonction pour mettre à jour les statistiques en temps réel
      function updateRealTimeStats(state) {
        // Mettre à jour le niveau de protection
        const protectionLevel = document.getElementById('protection-level');
        const protectionProgress = document.getElementById('protection-progress');

        if (protectionLevel && protectionProgress) {
          let level = 'Standard';
          let percentage = 65;
          let className = 'progress medium';

          switch (state.securityLevel) {
            case 'low':
              level = 'Faible';
              percentage = 30;
              className = 'progress low';
              break;
            case 'standard':
              level = 'Standard';
              percentage = 65;
              className = 'progress medium';
              break;
            case 'high':
              level = 'Élevé';
              percentage = 85;
              className = 'progress high';
              break;
            case 'paranoid':
              level = 'Paranoïaque';
              percentage = 100;
              className = 'progress high';
              break;
          }

          protectionLevel.textContent = level;
          protectionProgress.style.width = `${percentage}%`;
          protectionProgress.className = className;
        }

        // Mettre à jour les connexions sécurisées
        const secureConnections = document.getElementById('secure-connections');
        const connectionsProgress = document.getElementById('connections-progress');

        if (secureConnections && connectionsProgress) {
          // Simuler des connexions sécurisées
          const total = Math.floor(Math.random() * 10) + 5;
          const secure = state.vpnConnected ? total : Math.floor(total * 0.7);
          const percentage = Math.floor((secure / total) * 100);

          secureConnections.textContent = `${secure}/${total}`;
          connectionsProgress.style.width = `${percentage}%`;

          if (percentage >= 80) {
            connectionsProgress.className = 'progress high';
          } else if (percentage >= 50) {
            connectionsProgress.className = 'progress medium';
          } else {
            connectionsProgress.className = 'progress low';
          }
        }

        // Mettre à jour les menaces bloquées
        const blockedThreats = document.getElementById('blocked-threats');
        const threatsDetails = document.getElementById('threats-details');

        if (blockedThreats && threatsDetails) {
          const count = state.threatCount || 0;

          blockedThreats.textContent = count;

          if (count === 0) {
            threatsDetails.textContent = 'Aucune menace détectée';
          } else if (count === 1) {
            threatsDetails.textContent = '1 menace bloquée';
          } else {
            threatsDetails.textContent = `${count} menaces bloquées`;
          }
        }

        // Mettre à jour la dernière analyse
        const lastScanTime = document.getElementById('last-scan-time');
        const scanTimeDetails = document.getElementById('scan-time-details');

        if (lastScanTime && scanTimeDetails && state.lastScanTime) {
          const scanDate = new Date(state.lastScanTime);
          lastScanTime.textContent = scanDate.toLocaleString();

          if (state.lastScanResults) {
            scanTimeDetails.textContent = `${state.lastScanResults.scannedFiles} fichiers analysés`;
          } else {
            scanTimeDetails.textContent = 'Aucun détail disponible';
          }
        }
      }

      // Fonction pour mettre à jour les formulaires avec les valeurs actuelles
      function updateSettingsForms(state) {
        // Mettre à jour les paramètres VPN
        if (state.vpnProvider) {
          const vpnProvider = document.getElementById('vpn-provider');
          if (vpnProvider) vpnProvider.value = state.vpnProvider;
        }

        if (state.vpnCountry) {
          const vpnCountry = document.getElementById('vpn-country');
          if (vpnCountry) vpnCountry.value = state.vpnCountry;
        }

        if (state.vpnProtocol) {
          const vpnProtocol = document.getElementById('vpn-protocol');
          if (vpnProtocol) vpnProtocol.value = state.vpnProtocol;
        }

        if (state.vpnEncryption) {
          const vpnEncryption = document.getElementById('vpn-encryption');
          if (vpnEncryption) vpnEncryption.value = state.vpnEncryption;
        }

        // Mettre à jour les paramètres de protection
        const realtimeProtection = document.getElementById('realtime-protection');
        if (realtimeProtection) realtimeProtection.checked = state.realTimeProtection !== false;

        const behavioralAnalysis = document.getElementById('behavioral-analysis');
        if (behavioralAnalysis) behavioralAnalysis.checked = state.behavioralAnalysis === true;
      }

      // Fonction pour mettre à jour le statut du VPN
      function updateVPNStatus() {
        const vpnStatus = document.getElementById('vpn-status');
        const vpnDetails = document.getElementById('vpn-details');
        const vpnIcon = document.getElementById('vpn-icon');
        const toggleButton = document.getElementById('toggle-vpn');

        if (securityState.vpnConnected) {
          vpnStatus.textContent = 'Connecté';
          vpnStatus.className = 'status-active';
          vpnDetails.textContent = `IP: ${securityState.vpnIP || 'Inconnue'}, Pays: ${securityState.vpnCountry || 'Inconnu'}`;
          vpnIcon.className = 'status-icon active';
          toggleButton.textContent = 'Déconnecter';
        } else {
          vpnStatus.textContent = 'Déconnecté';
          vpnStatus.className = 'status-inactive';
          vpnDetails.textContent = 'Aucune connexion VPN active';
          vpnIcon.className = 'status-icon inactive';
          toggleButton.textContent = 'Connecter';
        }
      }

      // Fonction pour mettre à jour le statut de l'antivirus
      function updateAntivirusStatus() {
        const antivirusStatus = document.getElementById('antivirus-status');
        const antivirusDetails = document.getElementById('antivirus-details');
        const antivirusIcon = document.getElementById('antivirus-icon');
        const toggleButton = document.getElementById('toggle-antivirus');

        if (securityState.options && securityState.options.antivirusEnabled) {
          antivirusStatus.textContent = 'Actif';
          antivirusStatus.className = 'status-active';
          antivirusIcon.className = 'status-icon active';
          toggleButton.textContent = 'Désactiver';
        } else {
          antivirusStatus.textContent = 'Inactif';
          antivirusStatus.className = 'status-inactive';
          antivirusIcon.className = 'status-icon inactive';
          toggleButton.textContent = 'Activer';
        }

        if (securityState.lastScanTime) {
          const lastScanDate = new Date(securityState.lastScanTime);
          antivirusDetails.textContent = `Dernière analyse: ${lastScanDate.toLocaleString()}`;
        } else {
          antivirusDetails.textContent = 'Dernière analyse: Jamais';
        }
      }

      // Fonction pour mettre à jour le statut du pare-feu
      function updateFirewallStatus() {
        const firewallStatus = document.getElementById('firewall-status');
        const firewallDetails = document.getElementById('firewall-details');
        const firewallIcon = document.getElementById('firewall-icon');
        const toggleButton = document.getElementById('toggle-firewall');

        if (securityState.firewallActive) {
          firewallStatus.textContent = 'Actif';
          firewallStatus.className = 'status-active';
          firewallIcon.className = 'status-icon active';
          toggleButton.textContent = 'Désactiver';
        } else {
          firewallStatus.textContent = 'Inactif';
          firewallStatus.className = 'status-inactive';
          firewallIcon.className = 'status-icon inactive';
          toggleButton.textContent = 'Activer';
        }

        firewallDetails.textContent = `${securityState.blockedConnections || 0} connexions bloquées`;
      }

      // Fonction pour mettre à jour le statut du niveau de sécurité
      function updateSecurityLevelStatus() {
        const securityLevelStatus = document.getElementById('security-level-status');
        const securityLevelIcon = document.getElementById('security-level-icon');
        const securityLevelSelect = document.getElementById('security-level-select');

        securityLevelSelect.value = securityState.securityLevel || 'standard';

        let levelText = 'Standard';
        let levelClass = 'status-active';

        switch (securityState.securityLevel) {
          case 'low':
            levelText = 'Faible';
            levelClass = 'status-warning';
            break;
          case 'standard':
            levelText = 'Standard';
            levelClass = 'status-active';
            break;
          case 'high':
            levelText = 'Élevé';
            levelClass = 'status-success';
            break;
          case 'paranoid':
            levelText = 'Paranoïaque';
            levelClass = 'status-danger';
            break;
        }

        securityLevelStatus.textContent = levelText;
        securityLevelStatus.className = levelClass;
        securityLevelIcon.className = `status-icon ${levelClass}`;
      }

      // Fonction pour mettre à jour les résultats de l'analyse
      function updateScanResults(results) {
        const scanResultsContent = document.getElementById('scan-results-content');

        if (!results) {
          scanResultsContent.innerHTML = '<p>Aucune analyse effectuée</p>';
          return;
        }

        const startTime = new Date(results.startTime);
        const endTime = new Date(results.endTime);
        const duration = (results.duration / 1000).toFixed(2);

        scanResultsContent.innerHTML = `
          <div class="scan-result-item">
            <span class="result-label">Date de l'analyse:</span>
            <span class="result-value">${startTime.toLocaleString()}</span>
          </div>
          <div class="scan-result-item">
            <span class="result-label">Durée:</span>
            <span class="result-value">${duration} secondes</span>
          </div>
          <div class="scan-result-item">
            <span class="result-label">Fichiers analysés:</span>
            <span class="result-value">${results.scannedFiles}</span>
          </div>
          <div class="scan-result-item">
            <span class="result-label">Répertoires analysés:</span>
            <span class="result-value">${results.scannedDirectories}</span>
          </div>
          <div class="scan-result-item">
            <span class="result-label">Menaces détectées:</span>
            <span class="result-value ${results.threats > 0 ? 'status-danger' : 'status-success'}">${results.threats}</span>
          </div>
          <div class="scan-result-item">
            <span class="result-label">Fichiers mis en quarantaine:</span>
            <span class="result-value">${results.quarantined}</span>
          </div>
        `;
      }

      // Fonction pour mettre à jour la liste des fichiers en quarantaine
      function updateQuarantineList(files) {
        const quarantineContent = document.getElementById('quarantine-content');

        if (!files || files.length === 0) {
          quarantineContent.innerHTML = '<p>Aucun fichier en quarantaine</p>';
          return;
        }

        let html = '<ul class="quarantine-list">';
        files.forEach(file => {
          html += `<li class="quarantine-item">${file}</li>`;
        });
        html += '</ul>';

        quarantineContent.innerHTML = html;
      }

      // Fonction pour mettre à jour le statut du chiffrement des données
      function updateEncryptionStatus() {
        const status = document.getElementById('encryption-status');
        const details = document.getElementById('encryption-details');
        const icon = document.getElementById('encryption-icon');
        const button = document.getElementById('toggle-encryption');

        if (securityState.dataEncryption) {
          status.textContent = 'Activé';
          status.className = 'status-active';
          details.textContent = 'Vos données sont protégées';
          icon.className = 'status-icon active';
          button.textContent = 'Désactiver';
        } else {
          status.textContent = 'Désactivé';
          status.className = 'status-inactive';
          details.textContent = 'Protégez vos données sensibles';
          icon.className = 'status-icon inactive';
          button.textContent = 'Activer';
        }
      }

      // Fonction pour mettre à jour le statut de l'analyse comportementale
      function updateBehavioralAnalysisStatus() {
        const status = document.getElementById('behavioral-status');
        const details = document.getElementById('behavioral-details');
        const icon = document.getElementById('behavioral-icon');
        const button = document.getElementById('toggle-behavioral');

        if (securityState.behavioralAnalysis) {
          status.textContent = 'Activé';
          status.className = 'status-active';
          details.textContent = 'Protection avancée contre les menaces';
          icon.className = 'status-icon active';
          button.textContent = 'Désactiver';
        } else {
          status.textContent = 'Désactivé';
          status.className = 'status-inactive';
          details.textContent = 'Détection avancée des menaces';
          icon.className = 'status-icon inactive';
          button.textContent = 'Activer';
        }
      }

      // Fonction pour mettre à jour le score de sécurité
      function updateSecurityScore() {
        if (!securityState.securityScore) {
          securityState.securityScore = 70; // Valeur par défaut
        }

        const score = securityState.securityScore;
        const scoreElement = document.getElementById('security-score');
        const detailsElement = document.getElementById('security-score-details');
        const progressElement = document.getElementById('security-score-progress');

        if (!scoreElement || !detailsElement || !progressElement) return;

        scoreElement.textContent = score;
        progressElement.style.width = `${score}%`;

        // Définir la couleur en fonction du score
        if (score >= 80) {
          progressElement.className = 'progress high';
          detailsElement.textContent = 'Niveau de protection: Excellent';
        } else if (score >= 60) {
          progressElement.className = 'progress medium';
          detailsElement.textContent = 'Niveau de protection: Bon';
        } else if (score >= 40) {
          progressElement.className = 'progress low';
          detailsElement.textContent = 'Niveau de protection: Moyen';
        } else {
          progressElement.className = 'progress critical';
          detailsElement.textContent = 'Niveau de protection: Faible';
        }
      }

      // Fonction pour afficher les détails des mises à jour
      function showUpdateDetails(results) {
        const modal = document.createElement('div');
        modal.className = 'security-modal';

        const modalContent = document.createElement('div');
        modalContent.className = 'security-modal-content';

        const closeButton = document.createElement('span');
        closeButton.className = 'security-modal-close';
        closeButton.innerHTML = '&times;';
        closeButton.onclick = function() {
          document.body.removeChild(modal);
        };

        const title = document.createElement('h3');
        title.textContent = `Mises à jour de sécurité disponibles - Version ${results.version}`;

        const date = document.createElement('p');
        date.textContent = `Date de sortie: ${new Date(results.releaseDate).toLocaleDateString()}`;

        const criticalBadge = document.createElement('span');
        criticalBadge.className = results.critical ? 'security-badge critical' : 'security-badge';
        criticalBadge.textContent = results.critical ? 'Critique' : 'Normale';

        const list = document.createElement('ul');
        results.updateItems.forEach(item => {
          const listItem = document.createElement('li');
          listItem.textContent = item;
          list.appendChild(listItem);
        });

        const installButton = document.createElement('button');
        installButton.className = 'luna-button primary';
        installButton.textContent = 'Installer les mises à jour';
        installButton.onclick = function() {
          addLogEntry('Installation des mises à jour...', 'info');
          // Simuler l'installation des mises à jour
          setTimeout(() => {
            addLogEntry('Mises à jour installées avec succès', 'success');
            document.body.removeChild(modal);
          }, 2000);
        };

        modalContent.appendChild(closeButton);
        modalContent.appendChild(title);
        modalContent.appendChild(date);
        modalContent.appendChild(criticalBadge);
        modalContent.appendChild(document.createElement('hr'));
        modalContent.appendChild(list);
        modalContent.appendChild(installButton);

        modal.appendChild(modalContent);
        document.body.appendChild(modal);
      }

      // Fonction pour initialiser les graphiques
      function initCharts() {
        const securityActivityCtx = document.getElementById('security-activity-chart').getContext('2d');
        securityActivityChart = new Chart(securityActivityCtx, {
          type: 'line',
          data: {
            labels: [],
            datasets: [{
              label: 'Activité VPN',
              data: [],
              backgroundColor: 'rgba(54, 162, 235, 0.2)',
              borderColor: 'rgba(54, 162, 235, 1)',
              borderWidth: 2,
              tension: 0.4
            }, {
              label: 'Activité Pare-feu',
              data: [],
              backgroundColor: 'rgba(255, 99, 132, 0.2)',
              borderColor: 'rgba(255, 99, 132, 1)',
              borderWidth: 2,
              tension: 0.4
            }]
          },
          options: {
            responsive: true,
            scales: {
              y: {
                beginAtZero: true
              }
            }
          }
        });

        const threatsCtx = document.getElementById('threats-chart').getContext('2d');
        threatsChart = new Chart(threatsCtx, {
          type: 'bar',
          data: {
            labels: [],
            datasets: [{
              label: 'Menaces détectées',
              data: [],
              backgroundColor: 'rgba(255, 99, 132, 0.5)',
              borderColor: 'rgba(255, 99, 132, 1)',
              borderWidth: 1
            }, {
              label: 'Menaces bloquées',
              data: [],
              backgroundColor: 'rgba(75, 192, 192, 0.5)',
              borderColor: 'rgba(75, 192, 192, 1)',
              borderWidth: 1
            }]
          },
          options: {
            responsive: true,
            scales: {
              y: {
                beginAtZero: true
              }
            }
          }
        });
      }

      // Fonction pour mettre à jour les graphiques
      function updateCharts(chartData) {
        if (!chartData) return;

        // Mettre à jour le graphique d'activité de sécurité
        if (chartData.securityActivity) {
          securityActivityChart.data.labels = chartData.securityActivity.labels;
          securityActivityChart.data.datasets[0].data = chartData.securityActivity.vpnActivity;
          securityActivityChart.data.datasets[1].data = chartData.securityActivity.firewallActivity;
          securityActivityChart.update();
        }

        // Mettre à jour le graphique des menaces
        if (chartData.threats) {
          threatsChart.data.labels = chartData.threats.labels;
          threatsChart.data.datasets[0].data = chartData.threats.detected;
          threatsChart.data.datasets[1].data = chartData.threats.blocked;
          threatsChart.update();
        }
      }

      // Fonction pour ajouter une entrée au journal
      function addLogEntry(message, level = 'info') {
        const logContainer = document.getElementById('security-log-container');
        const entry = document.createElement('div');
        entry.className = `log-entry ${level}`;

        const timestamp = new Date().toLocaleTimeString();
        entry.textContent = `[${timestamp}] ${message}`;

        logContainer.appendChild(entry);
        logContainer.scrollTop = logContainer.scrollHeight;
      }

      // Fonction pour afficher un message d'erreur
      function showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.textContent = message;
        document.body.appendChild(errorDiv);
        setTimeout(() => {
          errorDiv.classList.add('show');
          setTimeout(() => {
            errorDiv.classList.remove('show');
            setTimeout(() => {
              errorDiv.remove();
            }, 300);
          }, 3000);
        }, 10);
      }

      // Fonction pour afficher un message de succès
      function showSuccess(message) {
        const successDiv = document.createElement('div');
        successDiv.className = 'success-message';
        successDiv.textContent = message;
        document.body.appendChild(successDiv);
        setTimeout(() => {
          successDiv.classList.add('show');
          setTimeout(() => {
            successDiv.classList.remove('show');
            setTimeout(() => {
              successDiv.remove();
            }, 300);
          }, 3000);
        }, 10);
      }
    });
  </script>
  <script src="/js/luna-security-new.js"></script>
</body>
</html>
