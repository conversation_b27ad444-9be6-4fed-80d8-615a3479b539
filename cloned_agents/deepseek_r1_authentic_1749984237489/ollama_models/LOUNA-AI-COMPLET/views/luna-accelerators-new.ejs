<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %></title>
  <link rel="stylesheet" href="/luna/css/luna-main.css">
  <link rel="stylesheet" href="/luna/css/luna-accelerators.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="/socket.io/socket.io.js"></script>
</head>
<body class="luna-body">
  <div class="luna-container">
    <header class="luna-header">
      <div class="luna-logo">
        <img src="/luna/images/luna-logo.png" alt="Luna Logo">
        <h1>Luna - Accélérateurs Kyber</h1>
      </div>
      <nav class="luna-nav">
        <ul>
          <li><a href="/luna" class="nav-link">Accueil</a></li>
          <li><a href="/luna/memory" class="nav-link">Mémoire</a></li>
          <li><a href="/luna/accelerators" class="nav-link active">Accélérateurs</a></li>
          <li><a href="/luna/training" class="nav-link">Formation</a></li>
          <li><a href="/luna/brain" class="nav-link">Cerveau</a></li>
          <li><a href="/luna/code" class="nav-link">Code</a></li>
        </ul>
      </nav>
    </header>

    <main class="luna-main">
      <div class="accelerators-dashboard">
        <div class="dashboard-header">
          <h2>Tableau de bord des accélérateurs Kyber</h2>
          <div class="dashboard-controls">
            <button id="refresh-accelerators" class="luna-button">Rafraîchir</button>
            <button id="optimize-accelerators" class="luna-button primary">Optimiser</button>
          </div>
        </div>

        <div class="dashboard-stats">
          <div class="stat-card">
            <h3>Efficacité globale</h3>
            <div class="stat-value" id="global-efficiency">0%</div>
            <div class="progress-bar">
              <div class="progress" id="efficiency-progress" style="width: 0%"></div>
            </div>
          </div>
          <div class="stat-card">
            <h3>Débit de traitement</h3>
            <div class="stat-value" id="global-throughput">0 MB/s</div>
            <div class="progress-bar">
              <div class="progress" id="throughput-progress" style="width: 0%"></div>
            </div>
          </div>
          <div class="stat-card">
            <h3>Température moyenne</h3>
            <div class="stat-value" id="global-temperature">0°C</div>
            <div class="progress-bar">
              <div class="progress" id="temperature-progress" style="width: 0%"></div>
            </div>
          </div>
          <div class="stat-card">
            <h3>Charge système</h3>
            <div class="stat-value" id="global-load">0%</div>
            <div class="progress-bar">
              <div class="progress" id="load-progress" style="width: 0%"></div>
            </div>
          </div>
        </div>

        <div class="accelerator-types">
          <div class="type-selector">
            <button class="type-button active" data-type="memory">Mémoire</button>
            <button class="type-button" data-type="video">Vidéo</button>
            <button class="type-button" data-type="audio">Audio</button>
            <button class="type-button" data-type="thermal">Thermique</button>
            <button class="type-button" data-type="reflection">Réflexion</button>
            <button class="type-button" data-type="zones">Zones</button>
          </div>
        </div>

        <div class="accelerators-grid" id="accelerators-container">
          <!-- Les accélérateurs seront ajoutés ici dynamiquement -->
          <div class="loading-container">
            <div class="loading-spinner"></div>
            <p>Chargement des accélérateurs...</p>
          </div>
        </div>

        <div class="accelerator-details" id="accelerator-details">
          <h3>Détails de l'accélérateur</h3>
          <div class="details-content">
            <p>Sélectionnez un accélérateur pour voir ses détails</p>
          </div>
        </div>

        <div class="accelerator-charts">
          <div class="chart-container">
            <h3>Performance des accélérateurs</h3>
            <canvas id="performance-chart"></canvas>
          </div>
          <div class="chart-container">
            <h3>Activité par zone</h3>
            <canvas id="zone-activity-chart"></canvas>
          </div>
        </div>
      </div>
    </main>

    <footer class="luna-footer">
      <p>Luna - Interface Cognitive Avancée &copy; 2025</p>
      <div class="system-status">
        <span class="status-indicator <%= systemStatus.active ? 'active' : 'inactive' %>"></span>
        <span class="status-text">Système <%= systemStatus.active ? 'actif' : 'inactif' %></span>
        <span class="version">v<%= systemStatus.version %></span>
      </div>
    </footer>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const socket = io();
      let currentAcceleratorType = 'memory';
      let acceleratorsData = {};
      let performanceChart = null;
      let zoneActivityChart = null;

      // Initialiser les graphiques
      initCharts();

      // Charger les accélérateurs au chargement de la page
      loadAccelerators();

      // Gestionnaire d'événements pour les boutons de type d'accélérateur
      document.querySelectorAll('.type-button').forEach(button => {
        button.addEventListener('click', function() {
          document.querySelectorAll('.type-button').forEach(btn => btn.classList.remove('active'));
          this.classList.add('active');
          currentAcceleratorType = this.dataset.type;
          displayAccelerators(currentAcceleratorType);
        });
      });

      // Gestionnaire d'événements pour le bouton de rafraîchissement
      document.getElementById('refresh-accelerators').addEventListener('click', loadAccelerators);

      // Gestionnaire d'événements pour le bouton d'optimisation
      document.getElementById('optimize-accelerators').addEventListener('click', optimizeAccelerators);

      // Fonction pour charger les accélérateurs
      function loadAccelerators() {
        const loadingContainer = document.createElement('div');
        loadingContainer.className = 'loading-container';
        loadingContainer.innerHTML = `
          <div class="loading-spinner"></div>
          <p>Chargement des accélérateurs...</p>
        `;
        
        const container = document.getElementById('accelerators-container');
        container.innerHTML = '';
        container.appendChild(loadingContainer);

        // Émettre l'événement pour obtenir les accélérateurs
        socket.emit('get all accelerators');
      }

      // Écouter les données des accélérateurs
      socket.on('all accelerators data', function(data) {
        if (data.success) {
          acceleratorsData = data.accelerators;
          updateGlobalStats(data.stats);
          displayAccelerators(currentAcceleratorType);
          updateCharts(data.stats);
        } else {
          showError('Erreur lors du chargement des accélérateurs');
        }
      });

      // Fonction pour optimiser les accélérateurs
      function optimizeAccelerators() {
        const button = document.getElementById('optimize-accelerators');
        button.disabled = true;
        button.textContent = 'Optimisation en cours...';

        // Émettre l'événement pour optimiser les accélérateurs
        socket.emit('optimize all accelerators');
      }

      // Écouter les données des accélérateurs optimisés
      socket.on('all accelerators optimized', function(data) {
        const button = document.getElementById('optimize-accelerators');
        button.disabled = false;
        button.textContent = 'Optimiser';

        if (data.success) {
          acceleratorsData = data.accelerators;
          updateGlobalStats(data.stats);
          displayAccelerators(currentAcceleratorType);
          updateCharts(data.stats);
          showSuccess(data.message || 'Accélérateurs optimisés avec succès');
        } else {
          showError('Erreur lors de l\'optimisation des accélérateurs');
        }
      });

      // Fonction pour afficher les accélérateurs d'un type spécifique
      function displayAccelerators(type) {
        const container = document.getElementById('accelerators-container');
        container.innerHTML = '';

        if (!acceleratorsData[type] || acceleratorsData[type].length === 0) {
          container.innerHTML = `<div class="no-data">Aucun accélérateur de type ${type} disponible</div>`;
          return;
        }

        acceleratorsData[type].forEach(acc => {
          const card = document.createElement('div');
          card.className = 'accelerator-card';
          card.dataset.id = acc.id;

          // Calculer la classe de température
          let tempClass = 'normal';
          if (acc.temperature > 80) tempClass = 'hot';
          else if (acc.temperature > 60) tempClass = 'warm';
          else if (acc.temperature < 30) tempClass = 'cold';

          // Calculer la classe d'efficacité
          let efficiencyClass = 'normal';
          if (acc.efficiency > 0.9) efficiencyClass = 'high';
          else if (acc.efficiency < 0.7) efficiencyClass = 'low';

          card.innerHTML = `
            <div class="accelerator-header">
              <h4>${acc.name || `Accélérateur ${type}`}</h4>
              <span class="accelerator-status ${acc.active ? 'active' : 'inactive'}">${acc.active ? 'Actif' : 'Inactif'}</span>
            </div>
            <div class="accelerator-stats">
              <div class="stat">
                <span class="stat-label">Efficacité</span>
                <span class="stat-value ${efficiencyClass}">${Math.round(acc.efficiency * 100)}%</span>
              </div>
              <div class="stat">
                <span class="stat-label">Débit</span>
                <span class="stat-value">${Math.round(acc.throughput)} MB/s</span>
              </div>
              <div class="stat">
                <span class="stat-label">Température</span>
                <span class="stat-value ${tempClass}">${Math.round(acc.temperature)}°C</span>
              </div>
              <div class="stat">
                <span class="stat-label">Charge</span>
                <span class="stat-value">${Math.round(acc.load * 100)}%</span>
              </div>
            </div>
            <div class="accelerator-progress">
              <div class="progress-bar">
                <div class="progress" style="width: ${Math.round(acc.efficiency * 100)}%"></div>
              </div>
            </div>
          `;

          card.addEventListener('click', () => showAcceleratorDetails(acc));
          container.appendChild(card);
        });
      }

      // Fonction pour afficher les détails d'un accélérateur
      function showAcceleratorDetails(accelerator) {
        const detailsContainer = document.getElementById('accelerator-details');
        detailsContainer.innerHTML = `
          <h3>Détails de l'accélérateur</h3>
          <div class="details-content">
            <div class="detail-row">
              <span class="detail-label">ID</span>
              <span class="detail-value">${accelerator.id}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Type</span>
              <span class="detail-value">${accelerator.type}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Statut</span>
              <span class="detail-value ${accelerator.active ? 'active' : 'inactive'}">${accelerator.active ? 'Actif' : 'Inactif'}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Efficacité</span>
              <span class="detail-value">${Math.round(accelerator.efficiency * 100)}%</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Débit</span>
              <span class="detail-value">${Math.round(accelerator.throughput)} MB/s</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Température</span>
              <span class="detail-value">${Math.round(accelerator.temperature)}°C</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Charge</span>
              <span class="detail-value">${Math.round(accelerator.load * 100)}%</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Créé le</span>
              <span class="detail-value">${new Date(accelerator.created).toLocaleString()}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Dernière activité</span>
              <span class="detail-value">${new Date(accelerator.lastActivity).toLocaleString()}</span>
            </div>
            <div class="detail-actions">
              <button class="luna-button" onclick="toggleAccelerator('${accelerator.id}', ${!accelerator.active})">
                ${accelerator.active ? 'Désactiver' : 'Activer'}
              </button>
              <button class="luna-button primary" onclick="optimizeAccelerator('${accelerator.id}')">
                Optimiser
              </button>
            </div>
          </div>
        `;
      }

      // Fonction pour mettre à jour les statistiques globales
      function updateGlobalStats(stats) {
        document.getElementById('global-efficiency').textContent = `${Math.round(stats.efficiency * 100)}%`;
        document.getElementById('global-throughput').textContent = `${Math.round(stats.throughput)} MB/s`;
        document.getElementById('global-temperature').textContent = `${Math.round(stats.temperature)}°C`;
        document.getElementById('global-load').textContent = `${Math.round(stats.load * 100)}%`;

        document.getElementById('efficiency-progress').style.width = `${Math.round(stats.efficiency * 100)}%`;
        document.getElementById('throughput-progress').style.width = `${Math.min(100, Math.round(stats.throughput / 100))}%`;
        document.getElementById('temperature-progress').style.width = `${Math.round(stats.temperature)}%`;
        document.getElementById('load-progress').style.width = `${Math.round(stats.load * 100)}%`;
      }

      // Fonction pour initialiser les graphiques
      function initCharts() {
        const performanceCtx = document.getElementById('performance-chart').getContext('2d');
        performanceChart = new Chart(performanceCtx, {
          type: 'bar',
          data: {
            labels: ['Mémoire', 'Vidéo', 'Audio', 'Thermique', 'Réflexion', 'Zones'],
            datasets: [{
              label: 'Efficacité (%)',
              data: [0, 0, 0, 0, 0, 0],
              backgroundColor: 'rgba(54, 162, 235, 0.5)',
              borderColor: 'rgba(54, 162, 235, 1)',
              borderWidth: 1
            }, {
              label: 'Débit (MB/s)',
              data: [0, 0, 0, 0, 0, 0],
              backgroundColor: 'rgba(255, 99, 132, 0.5)',
              borderColor: 'rgba(255, 99, 132, 1)',
              borderWidth: 1
            }]
          },
          options: {
            responsive: true,
            scales: {
              y: {
                beginAtZero: true
              }
            }
          }
        });

        const zoneActivityCtx = document.getElementById('zone-activity-chart').getContext('2d');
        zoneActivityChart = new Chart(zoneActivityCtx, {
          type: 'doughnut',
          data: {
            labels: ['Zone 1', 'Zone 2', 'Zone 3', 'Zone 4', 'Zone 5', 'Zone 6'],
            datasets: [{
              label: 'Activité par zone',
              data: [0, 0, 0, 0, 0, 0],
              backgroundColor: [
                'rgba(255, 99, 132, 0.5)',
                'rgba(54, 162, 235, 0.5)',
                'rgba(255, 206, 86, 0.5)',
                'rgba(75, 192, 192, 0.5)',
                'rgba(153, 102, 255, 0.5)',
                'rgba(255, 159, 64, 0.5)'
              ],
              borderColor: [
                'rgba(255, 99, 132, 1)',
                'rgba(54, 162, 235, 1)',
                'rgba(255, 206, 86, 1)',
                'rgba(75, 192, 192, 1)',
                'rgba(153, 102, 255, 1)',
                'rgba(255, 159, 64, 1)'
              ],
              borderWidth: 1
            }]
          },
          options: {
            responsive: true
          }
        });
      }

      // Fonction pour mettre à jour les graphiques
      function updateCharts(stats) {
        // Mettre à jour le graphique de performance
        const types = ['memory', 'video', 'audio', 'thermal', 'reflection', 'zones'];
        const efficiencyData = [];
        const throughputData = [];

        types.forEach(type => {
          if (stats[type]) {
            efficiencyData.push(Math.round(stats[type].efficiency * 100));
            throughputData.push(Math.round(stats[type].throughput));
          } else {
            efficiencyData.push(0);
            throughputData.push(0);
          }
        });

        performanceChart.data.datasets[0].data = efficiencyData;
        performanceChart.data.datasets[1].data = throughputData;
        performanceChart.update();

        // Mettre à jour le graphique d'activité par zone
        if (stats.zones) {
          const zoneData = stats.zones.map(zone => Math.round(zone.activity * 100));
          zoneActivityChart.data.datasets[0].data = zoneData;
          zoneActivityChart.update();
        }
      }

      // Fonction pour afficher un message d'erreur
      function showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.textContent = message;
        document.body.appendChild(errorDiv);
        setTimeout(() => {
          errorDiv.classList.add('show');
          setTimeout(() => {
            errorDiv.classList.remove('show');
            setTimeout(() => {
              errorDiv.remove();
            }, 300);
          }, 3000);
        }, 10);
      }

      // Fonction pour afficher un message de succès
      function showSuccess(message) {
        const successDiv = document.createElement('div');
        successDiv.className = 'success-message';
        successDiv.textContent = message;
        document.body.appendChild(successDiv);
        setTimeout(() => {
          successDiv.classList.add('show');
          setTimeout(() => {
            successDiv.classList.remove('show');
            setTimeout(() => {
              successDiv.remove();
            }, 300);
          }, 3000);
        }, 10);
      }

      // Fonctions globales pour les actions sur les accélérateurs
      window.toggleAccelerator = function(id, active) {
        socket.emit('toggle accelerator', { id, active });
      };

      window.optimizeAccelerator = function(id) {
        socket.emit('optimize accelerator', { id });
      };

      // Écouter les événements de mise à jour des accélérateurs
      socket.on('accelerator updated', function(data) {
        if (data.success) {
          loadAccelerators();
          showSuccess(data.message || 'Accélérateur mis à jour avec succès');
        } else {
          showError(data.error || 'Erreur lors de la mise à jour de l\'accélérateur');
        }
      });
    });
  </script>
</body>
</html>
