<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %></title>
  <link rel="stylesheet" href="/luna/css/luna-main.css">
  <link rel="stylesheet" href="/luna/css/luna-monitor.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="/socket.io/socket.io.js"></script>
</head>
<body class="luna-body">
  <div class="luna-container">
    <header class="luna-header">
      <div class="luna-logo">
        <img src="/luna/images/luna-logo.png" alt="Luna Logo">
        <h1>Luna - Surveillance Système</h1>
      </div>
      <nav class="luna-nav">
        <ul>
          <li><a href="/luna" class="nav-link">Accueil</a></li>
          <li><a href="/luna/memory" class="nav-link">Mémoire</a></li>
          <li><a href="/luna/accelerators" class="nav-link">Accélérateurs</a></li>
          <li><a href="/luna/training" class="nav-link">Formation</a></li>
          <li><a href="/luna/brain" class="nav-link">Cerveau</a></li>
          <li><a href="/luna/code" class="nav-link">Code</a></li>
          <li><a href="/luna/security" class="nav-link">Sécurité</a></li>
          <li><a href="/luna/backup" class="nav-link">Sauvegarde</a></li>
          <li><a href="/luna/monitor" class="nav-link active">Surveillance</a></li>
        </ul>
      </nav>
    </header>

    <main class="luna-main">
      <div class="monitor-container">
        <div class="monitor-header">
          <h2>Surveillance du Système</h2>
          <div class="monitor-controls">
            <button id="refresh-monitor" class="luna-button">Rafraîchir</button>
            <button id="toggle-monitoring" class="luna-button primary">Arrêter la surveillance</button>
          </div>
        </div>

        <div class="monitor-overview">
          <div class="metric-card" id="cpu-card">
            <h3>CPU</h3>
            <div class="metric-value" id="cpu-value">0%</div>
            <div class="metric-details" id="cpu-details">8 cœurs, 2.3 GHz</div>
            <div class="progress-bar">
              <div class="progress normal" id="cpu-progress" style="width: 0%"></div>
            </div>
          </div>

          <div class="metric-card" id="memory-card">
            <h3>Mémoire</h3>
            <div class="metric-value" id="memory-value">0%</div>
            <div class="metric-details" id="memory-details">0 GB / 0 GB</div>
            <div class="progress-bar">
              <div class="progress normal" id="memory-progress" style="width: 0%"></div>
            </div>
          </div>

          <div class="metric-card" id="disk-card">
            <h3>Disque</h3>
            <div class="metric-value" id="disk-value">0%</div>
            <div class="metric-details" id="disk-details">0 GB / 0 GB</div>
            <div class="progress-bar">
              <div class="progress normal" id="disk-progress" style="width: 0%"></div>
            </div>
          </div>

          <div class="metric-card" id="temperature-card">
            <h3>Température</h3>
            <div class="metric-value" id="temperature-value">0°C</div>
            <div class="metric-details" id="temperature-details">Normal</div>
            <div class="progress-bar">
              <div class="progress normal" id="temperature-progress" style="width: 0%"></div>
            </div>
          </div>
        </div>

        <div class="monitor-charts">
          <div class="chart-container">
            <h3>Utilisation CPU & Mémoire</h3>
            <canvas id="resources-chart"></canvas>
          </div>
          <div class="chart-container">
            <h3>Température & Disque</h3>
            <canvas id="temp-disk-chart"></canvas>
          </div>
        </div>

        <div class="monitor-details">
          <div class="details-card">
            <h3>Informations Système</h3>
            <div class="system-info" id="system-info">
              <div class="info-item">
                <span class="info-label">Plateforme</span>
                <span class="info-value" id="platform-value">-</span>
              </div>
              <div class="info-item">
                <span class="info-label">Architecture</span>
                <span class="info-value" id="arch-value">-</span>
              </div>
              <div class="info-item">
                <span class="info-label">Hostname</span>
                <span class="info-value" id="hostname-value">-</span>
              </div>
              <div class="info-item">
                <span class="info-label">Version OS</span>
                <span class="info-value" id="release-value">-</span>
              </div>
              <div class="info-item">
                <span class="info-label">Uptime</span>
                <span class="info-value" id="uptime-value">-</span>
              </div>
              <div class="info-item">
                <span class="info-label">Mémoire Totale</span>
                <span class="info-value" id="total-memory-value">-</span>
              </div>
              <div class="info-item">
                <span class="info-label">Cœurs CPU</span>
                <span class="info-value" id="cpu-count-value">-</span>
              </div>
              <div class="info-item">
                <span class="info-label">Modèle CPU</span>
                <span class="info-value" id="cpu-model-value">-</span>
              </div>
            </div>
          </div>

          <div class="details-card">
            <h3>Processus</h3>
            <table class="process-table">
              <thead>
                <tr>
                  <th>PID</th>
                  <th>Nom</th>
                  <th>CPU</th>
                  <th>Mémoire</th>
                </tr>
              </thead>
              <tbody id="processes-table">
                <tr>
                  <td colspan="4" class="no-data">Chargement des processus...</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div class="details-card">
          <h3>Alertes</h3>
          <div class="alerts-list" id="alerts-list">
            <div class="no-data">Aucune alerte</div>
          </div>
          <div class="form-actions">
            <button id="clear-alerts" class="luna-button">Effacer les alertes</button>
          </div>
        </div>

        <div class="monitor-settings">
          <h3>Paramètres de surveillance</h3>
          <form id="monitor-settings-form" class="settings-form">
            <div class="form-group">
              <label for="monitoring-enabled">
                <input type="checkbox" id="monitoring-enabled" name="monitoringEnabled" checked>
                Activer la surveillance
              </label>
            </div>

            <div class="form-group">
              <label for="monitoring-interval">Intervalle de surveillance (ms)</label>
              <input type="number" id="monitoring-interval" name="monitoringInterval" min="1000" max="60000" value="5000">
            </div>

            <div class="form-group">
              <label for="cpu-threshold">Seuil d'alerte CPU (%)</label>
              <input type="number" id="cpu-threshold" name="alertThresholds.cpu" min="50" max="100" value="80">
            </div>

            <div class="form-group">
              <label for="memory-threshold">Seuil d'alerte Mémoire (%)</label>
              <input type="number" id="memory-threshold" name="alertThresholds.memory" min="50" max="100" value="80">
            </div>

            <div class="form-group">
              <label for="disk-threshold">Seuil d'alerte Disque (%)</label>
              <input type="number" id="disk-threshold" name="alertThresholds.disk" min="50" max="100" value="90">
            </div>

            <div class="form-group">
              <label for="temperature-threshold">Seuil d'alerte Température (°C)</label>
              <input type="number" id="temperature-threshold" name="alertThresholds.temperature" min="50" max="100" value="80">
            </div>

            <div class="form-actions">
              <button type="button" id="reset-settings" class="luna-button">Réinitialiser</button>
              <button type="submit" class="luna-button primary">Enregistrer</button>
            </div>
          </form>
        </div>

        <div class="monitor-log">
          <h3>Journal de surveillance</h3>
          <div class="log-container" id="monitor-log-container">
            <div class="log-entry">Système de surveillance initialisé</div>
          </div>
        </div>
      </div>
    </main>

    <footer class="luna-footer">
      <p>Luna - Interface Cognitive Avancée &copy; 2025</p>
      <div class="system-status">
        <span class="status-indicator <%= systemStatus.active ? 'active' : 'inactive' %>"></span>
        <span class="status-text">Système <%= systemStatus.active ? 'actif' : 'inactif' %></span>
        <span class="version">v<%= systemStatus.version %></span>
      </div>
    </footer>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const socket = io();
      let monitorState = {};
      let resourcesChart = null;
      let tempDiskChart = null;
      let monitoring = true;

      // Initialiser les graphiques
      initCharts();

      // Charger les données de surveillance au chargement de la page
      loadMonitorData();

      // Gestionnaire d'événements pour le bouton de rafraîchissement
      document.getElementById('refresh-monitor').addEventListener('click', loadMonitorData);

      // Gestionnaire d'événements pour le bouton de surveillance
      document.getElementById('toggle-monitoring').addEventListener('click', toggleMonitoring);

      // Gestionnaire d'événements pour le formulaire de paramètres
      document.getElementById('monitor-settings-form').addEventListener('submit', function(e) {
        e.preventDefault();
        saveMonitorSettings();
      });

      // Gestionnaire d'événements pour le bouton de réinitialisation des paramètres
      document.getElementById('reset-settings').addEventListener('click', resetMonitorSettings);

      // Gestionnaire d'événements pour le bouton d'effacement des alertes
      document.getElementById('clear-alerts').addEventListener('click', clearAlerts);

      // Fonction pour charger les données de surveillance
      function loadMonitorData() {
        addLogEntry('Chargement des données de surveillance...', 'info');
        socket.emit('get monitor state');
      }

      // Écouter les données de surveillance
      socket.on('monitor state', function(data) {
        if (data.success) {
          monitorState = data.state;
          updateMonitorUI(monitorState);
          addLogEntry('Données de surveillance chargées avec succès', 'success');
          
          // Mettre à jour l'état du bouton de surveillance
          monitoring = monitorState.monitoring;
          updateMonitoringButton();
        } else {
          addLogEntry('Erreur lors du chargement des données de surveillance: ' + (data.error || 'Erreur inconnue'), 'error');
          showError('Erreur lors du chargement des données de surveillance');
        }
      });

      // Fonction pour activer/désactiver la surveillance
      function toggleMonitoring() {
        if (monitoring) {
          socket.emit('stop monitoring');
        } else {
          socket.emit('start monitoring');
        }
      }

      // Fonction pour sauvegarder les paramètres de surveillance
      function saveMonitorSettings() {
        const form = document.getElementById('monitor-settings-form');
        const formData = new FormData(form);
        
        const settings = {
          monitoringEnabled: formData.get('monitoringEnabled') === 'on',
          monitoringInterval: parseInt(formData.get('monitoringInterval')),
          alertThresholds: {
            cpu: parseInt(formData.get('alertThresholds.cpu')),
            memory: parseInt(formData.get('alertThresholds.memory')),
            disk: parseInt(formData.get('alertThresholds.disk')),
            temperature: parseInt(formData.get('alertThresholds.temperature'))
          }
        };
        
        addLogEntry('Sauvegarde des paramètres...', 'info');
        socket.emit('set monitor options', settings);
      }

      // Fonction pour réinitialiser les paramètres de surveillance
      function resetMonitorSettings() {
        if (!monitorState.options) return;
        
        document.getElementById('monitoring-enabled').checked = monitorState.options.monitoringEnabled;
        document.getElementById('monitoring-interval').value = monitorState.options.monitoringInterval;
        document.getElementById('cpu-threshold').value = monitorState.options.alertThresholds.cpu;
        document.getElementById('memory-threshold').value = monitorState.options.alertThresholds.memory;
        document.getElementById('disk-threshold').value = monitorState.options.alertThresholds.disk;
        document.getElementById('temperature-threshold').value = monitorState.options.alertThresholds.temperature;
        
        addLogEntry('Paramètres réinitialisés', 'info');
      }

      // Fonction pour effacer les alertes
      function clearAlerts() {
        socket.emit('clear alerts');
      }

      // Écouter les événements de surveillance
      socket.on('monitor started', function() {
        monitoring = true;
        updateMonitoringButton();
        addLogEntry('Surveillance démarrée', 'success');
        showSuccess('Surveillance démarrée');
      });

      socket.on('monitor stopped', function() {
        monitoring = false;
        updateMonitoringButton();
        addLogEntry('Surveillance arrêtée', 'info');
        showSuccess('Surveillance arrêtée');
      });

      socket.on('monitor updated', function(data) {
        updateMetrics(data.metrics);
        updateCharts(data.metrics, data.timestamp);
      });

      socket.on('monitor options updated', function(data) {
        if (data.success) {
          addLogEntry('Paramètres de surveillance mis à jour', 'success');
          showSuccess('Paramètres mis à jour avec succès');
          loadMonitorData(); // Recharger les données
        } else {
          addLogEntry('Erreur lors de la mise à jour des paramètres: ' + (data.error || 'Erreur inconnue'), 'error');
          showError('Erreur lors de la mise à jour des paramètres');
        }
      });

      socket.on('monitor alerts', function(data) {
        updateAlerts(data.alerts);
      });

      socket.on('monitor alerts cleared', function() {
        clearAlertsUI();
        addLogEntry('Alertes effacées', 'info');
        showSuccess('Alertes effacées');
      });

      socket.on('monitor log', function(data) {
        addLogEntry(data.message, data.level || 'info');
      });

      // Fonction pour mettre à jour l'interface utilisateur de surveillance
      function updateMonitorUI(state) {
        // Mettre à jour les métriques
        updateMetrics(state.currentMetrics);
        
        // Mettre à jour les informations système
        updateSystemInfo(state.systemInfo);
        
        // Mettre à jour les processus
        updateProcesses(state.processes);
        
        // Mettre à jour les alertes
        updateAlerts(state.alerts);
        
        // Mettre à jour les paramètres du formulaire
        if (state.options) {
          document.getElementById('monitoring-enabled').checked = state.options.monitoringEnabled;
          document.getElementById('monitoring-interval').value = state.options.monitoringInterval;
          document.getElementById('cpu-threshold').value = state.options.alertThresholds.cpu;
          document.getElementById('memory-threshold').value = state.options.alertThresholds.memory;
          document.getElementById('disk-threshold').value = state.options.alertThresholds.disk;
          document.getElementById('temperature-threshold').value = state.options.alertThresholds.temperature;
        }
      }

      // Fonction pour mettre à jour les métriques
      function updateMetrics(metrics) {
        // CPU
        document.getElementById('cpu-value').textContent = `${metrics.cpu}%`;
        document.getElementById('cpu-progress').style.width = `${metrics.cpu}%`;
        updateProgressClass('cpu-progress', metrics.cpu, 70, 90);
        
        // Mémoire
        document.getElementById('memory-value').textContent = `${metrics.memory}%`;
        document.getElementById('memory-progress').style.width = `${metrics.memory}%`;
        updateProgressClass('memory-progress', metrics.memory, 70, 90);
        
        // Disque
        document.getElementById('disk-value').textContent = `${metrics.disk}%`;
        document.getElementById('disk-progress').style.width = `${metrics.disk}%`;
        updateProgressClass('disk-progress', metrics.disk, 80, 95);
        
        // Température
        document.getElementById('temperature-value').textContent = `${metrics.temperature}°C`;
        document.getElementById('temperature-progress').style.width = `${metrics.temperature}%`;
        updateProgressClass('temperature-progress', metrics.temperature, 70, 85);
        
        // Détails
        if (monitorState.systemInfo) {
          document.getElementById('cpu-details').textContent = `${monitorState.systemInfo.cpuCount} cœurs, ${monitorState.systemInfo.cpuModel.split('@')[1] || ''}`;
          
          const totalMemoryGB = Math.round(monitorState.systemInfo.totalMemory / (1024 * 1024 * 1024));
          const usedMemoryGB = Math.round(totalMemoryGB * metrics.memory / 100);
          document.getElementById('memory-details').textContent = `${usedMemoryGB} GB / ${totalMemoryGB} GB`;
          
          document.getElementById('temperature-details').textContent = getTemperatureStatus(metrics.temperature);
        }
      }

      // Fonction pour mettre à jour la classe de la barre de progression
      function updateProgressClass(elementId, value, warningThreshold, dangerThreshold) {
        const element = document.getElementById(elementId);
        
        if (value >= dangerThreshold) {
          element.className = 'progress danger';
        } else if (value >= warningThreshold) {
          element.className = 'progress warning';
        } else {
          element.className = 'progress normal';
        }
      }

      // Fonction pour obtenir le statut de la température
      function getTemperatureStatus(temperature) {
        if (temperature >= 85) {
          return 'Critique';
        } else if (temperature >= 70) {
          return 'Élevée';
        } else if (temperature >= 50) {
          return 'Normale';
        } else {
          return 'Basse';
        }
      }

      // Fonction pour mettre à jour les informations système
      function updateSystemInfo(systemInfo) {
        document.getElementById('platform-value').textContent = systemInfo.platform;
        document.getElementById('arch-value').textContent = systemInfo.arch;
        document.getElementById('hostname-value').textContent = systemInfo.hostname;
        document.getElementById('release-value').textContent = systemInfo.release;
        document.getElementById('uptime-value').textContent = formatUptime(systemInfo.uptime);
        document.getElementById('total-memory-value').textContent = formatSize(systemInfo.totalMemory);
        document.getElementById('cpu-count-value').textContent = systemInfo.cpuCount;
        document.getElementById('cpu-model-value').textContent = systemInfo.cpuModel;
      }

      // Fonction pour mettre à jour les processus
      function updateProcesses(processes) {
        const tableBody = document.getElementById('processes-table');
        
        if (!processes || processes.length === 0) {
          tableBody.innerHTML = `<tr><td colspan="4" class="no-data">Aucun processus disponible</td></tr>`;
          return;
        }
        
        tableBody.innerHTML = '';
        
        // Trier les processus par utilisation CPU (plus élevée en premier)
        const sortedProcesses = [...processes].sort((a, b) => b.cpu - a.cpu);
        
        sortedProcesses.forEach(process => {
          const row = document.createElement('tr');
          
          row.innerHTML = `
            <td>${process.pid}</td>
            <td>${process.name}</td>
            <td>${process.cpu.toFixed(1)}%</td>
            <td>${formatSize(process.memory * 1024)}</td>
          `;
          
          tableBody.appendChild(row);
        });
      }

      // Fonction pour mettre à jour les alertes
      function updateAlerts(alerts) {
        const alertsList = document.getElementById('alerts-list');
        
        if (!alerts || alerts.length === 0) {
          alertsList.innerHTML = `<div class="no-data">Aucune alerte</div>`;
          return;
        }
        
        alertsList.innerHTML = '';
        
        // Trier les alertes par date (plus récente en premier)
        const sortedAlerts = [...alerts].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        
        sortedAlerts.forEach(alert => {
          const alertItem = document.createElement('div');
          alertItem.className = `alert-item ${alert.level}`;
          
          alertItem.innerHTML = `
            <div class="alert-content">
              <div class="alert-message">${alert.message}</div>
              <div class="alert-timestamp">${new Date(alert.timestamp).toLocaleString()}</div>
            </div>
          `;
          
          alertsList.appendChild(alertItem);
        });
      }

      // Fonction pour effacer les alertes dans l'interface
      function clearAlertsUI() {
        const alertsList = document.getElementById('alerts-list');
        alertsList.innerHTML = `<div class="no-data">Aucune alerte</div>`;
      }

      // Fonction pour mettre à jour le bouton de surveillance
      function updateMonitoringButton() {
        const button = document.getElementById('toggle-monitoring');
        
        if (monitoring) {
          button.textContent = 'Arrêter la surveillance';
        } else {
          button.textContent = 'Démarrer la surveillance';
        }
      }

      // Fonction pour initialiser les graphiques
      function initCharts() {
        const resourcesCtx = document.getElementById('resources-chart').getContext('2d');
        resourcesChart = new Chart(resourcesCtx, {
          type: 'line',
          data: {
            labels: [],
            datasets: [{
              label: 'CPU (%)',
              data: [],
              backgroundColor: 'rgba(52, 152, 219, 0.2)',
              borderColor: 'rgba(52, 152, 219, 1)',
              borderWidth: 2,
              tension: 0.4
            }, {
              label: 'Mémoire (%)',
              data: [],
              backgroundColor: 'rgba(155, 89, 182, 0.2)',
              borderColor: 'rgba(155, 89, 182, 1)',
              borderWidth: 2,
              tension: 0.4
            }]
          },
          options: {
            responsive: true,
            scales: {
              y: {
                beginAtZero: true,
                max: 100
              }
            }
          }
        });

        const tempDiskCtx = document.getElementById('temp-disk-chart').getContext('2d');
        tempDiskChart = new Chart(tempDiskCtx, {
          type: 'line',
          data: {
            labels: [],
            datasets: [{
              label: 'Température (°C)',
              data: [],
              backgroundColor: 'rgba(231, 76, 60, 0.2)',
              borderColor: 'rgba(231, 76, 60, 1)',
              borderWidth: 2,
              tension: 0.4,
              yAxisID: 'y'
            }, {
              label: 'Disque (%)',
              data: [],
              backgroundColor: 'rgba(46, 204, 113, 0.2)',
              borderColor: 'rgba(46, 204, 113, 1)',
              borderWidth: 2,
              tension: 0.4,
              yAxisID: 'y1'
            }]
          },
          options: {
            responsive: true,
            scales: {
              y: {
                beginAtZero: true,
                position: 'left',
                title: {
                  display: true,
                  text: 'Température (°C)'
                }
              },
              y1: {
                beginAtZero: true,
                max: 100,
                position: 'right',
                grid: {
                  drawOnChartArea: false
                },
                title: {
                  display: true,
                  text: 'Disque (%)'
                }
              }
            }
          }
        });
      }

      // Fonction pour mettre à jour les graphiques
      function updateCharts(metrics, timestamp) {
        const time = new Date(timestamp).toLocaleTimeString();
        
        // Mettre à jour le graphique des ressources
        resourcesChart.data.labels.push(time);
        resourcesChart.data.datasets[0].data.push(metrics.cpu);
        resourcesChart.data.datasets[1].data.push(metrics.memory);
        
        // Limiter à 20 points de données
        if (resourcesChart.data.labels.length > 20) {
          resourcesChart.data.labels.shift();
          resourcesChart.data.datasets[0].data.shift();
          resourcesChart.data.datasets[1].data.shift();
        }
        
        resourcesChart.update();
        
        // Mettre à jour le graphique de température et disque
        tempDiskChart.data.labels.push(time);
        tempDiskChart.data.datasets[0].data.push(metrics.temperature);
        tempDiskChart.data.datasets[1].data.push(metrics.disk);
        
        // Limiter à 20 points de données
        if (tempDiskChart.data.labels.length > 20) {
          tempDiskChart.data.labels.shift();
          tempDiskChart.data.datasets[0].data.shift();
          tempDiskChart.data.datasets[1].data.shift();
        }
        
        tempDiskChart.update();
      }

      // Fonction pour ajouter une entrée au journal
      function addLogEntry(message, level = 'info') {
        const logContainer = document.getElementById('monitor-log-container');
        const entry = document.createElement('div');
        entry.className = `log-entry ${level}`;
        
        const timestamp = new Date().toLocaleTimeString();
        entry.textContent = `[${timestamp}] ${message}`;
        
        logContainer.appendChild(entry);
        logContainer.scrollTop = logContainer.scrollHeight;
      }

      // Fonction pour formater une taille en octets
      function formatSize(size) {
        const units = ['B', 'KB', 'MB', 'GB', 'TB'];
        let formattedSize = size;
        let unitIndex = 0;
        
        while (formattedSize >= 1024 && unitIndex < units.length - 1) {
          formattedSize /= 1024;
          unitIndex++;
        }
        
        return `${formattedSize.toFixed(2)} ${units[unitIndex]}`;
      }

      // Fonction pour formater un temps d'activité en secondes
      function formatUptime(uptime) {
        const days = Math.floor(uptime / 86400);
        const hours = Math.floor((uptime % 86400) / 3600);
        const minutes = Math.floor((uptime % 3600) / 60);
        const seconds = Math.floor(uptime % 60);
        
        if (days > 0) {
          return `${days}j ${hours}h ${minutes}m ${seconds}s`;
        } else if (hours > 0) {
          return `${hours}h ${minutes}m ${seconds}s`;
        } else if (minutes > 0) {
          return `${minutes}m ${seconds}s`;
        } else {
          return `${seconds}s`;
        }
      }

      // Fonction pour afficher un message d'erreur
      function showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.textContent = message;
        document.body.appendChild(errorDiv);
        setTimeout(() => {
          errorDiv.classList.add('show');
          setTimeout(() => {
            errorDiv.classList.remove('show');
            setTimeout(() => {
              errorDiv.remove();
            }, 300);
          }, 3000);
        }, 10);
      }

      // Fonction pour afficher un message de succès
      function showSuccess(message) {
        const successDiv = document.createElement('div');
        successDiv.className = 'success-message';
        successDiv.textContent = message;
        document.body.appendChild(successDiv);
        setTimeout(() => {
          successDiv.classList.add('show');
          setTimeout(() => {
            successDiv.classList.remove('show');
            setTimeout(() => {
              successDiv.remove();
            }, 300);
          }, 3000);
        }, 10);
      }
    });
  </script>
</body>
</html>
