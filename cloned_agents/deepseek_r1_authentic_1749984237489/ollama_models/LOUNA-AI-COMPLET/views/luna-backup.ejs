<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %></title>
  <link rel="stylesheet" href="/luna/css/luna-main.css">
  <link rel="stylesheet" href="/luna/css/luna-backup.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="/socket.io/socket.io.js"></script>
</head>
<body class="luna-body">
  <div class="luna-container">
    <header class="luna-header">
      <div class="luna-logo">
        <img src="/luna/images/luna-logo.png" alt="Luna Logo">
        <h1>Luna - Sauvegarde</h1>
      </div>
      <nav class="luna-nav">
        <ul>
          <li><a href="/luna" class="nav-link">Accueil</a></li>
          <li><a href="/luna/memory" class="nav-link">Mémoire</a></li>
          <li><a href="/luna/accelerators" class="nav-link">Accélérateurs</a></li>
          <li><a href="/luna/training" class="nav-link">Formation</a></li>
          <li><a href="/luna/brain" class="nav-link">Cerveau</a></li>
          <li><a href="/luna/code" class="nav-link">Code</a></li>
          <li><a href="/luna/security" class="nav-link">Sécurité</a></li>
          <li><a href="/luna/backup" class="nav-link active">Sauvegarde</a></li>
        </ul>
      </nav>
    </header>

    <main class="luna-main">
      <div class="backup-container">
        <div class="backup-header">
          <h2>Système de Sauvegarde</h2>
          <div class="backup-controls">
            <button id="refresh-backup" class="luna-button">Rafraîchir</button>
            <button id="create-backup" class="luna-button primary">Créer une sauvegarde</button>
          </div>
        </div>

        <div class="backup-status">
          <div class="status-card">
            <h3>Dernière sauvegarde</h3>
            <div class="status-value" id="last-backup-time">Jamais</div>
            <p id="last-backup-size">Taille: 0 MB</p>
          </div>

          <div class="status-card">
            <h3>Sauvegardes totales</h3>
            <div class="status-value" id="total-backups">0</div>
            <p id="total-saved-size">Taille totale: 0 MB</p>
          </div>

          <div class="status-card">
            <h3>Disque externe</h3>
            <div class="status-value" id="external-drive-status">Non disponible</div>
            <p id="external-drive-path">Chemin: Non défini</p>
          </div>

          <div class="status-card">
            <h3>État du système</h3>
            <div class="status-value" id="backup-system-status">Inactif</div>
            <p id="backup-interval">Intervalle: 1 heure</p>
          </div>
        </div>

        <div class="backup-history">
          <h3>Historique des sauvegardes</h3>
          <table class="history-table">
            <thead>
              <tr>
                <th>Date</th>
                <th>Description</th>
                <th>Taille</th>
                <th>Durée</th>
                <th>Chiffré</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody id="backup-history-table">
              <tr>
                <td colspan="6" class="no-data">Aucune sauvegarde disponible</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="backup-settings">
          <h3>Paramètres de sauvegarde</h3>
          <form id="backup-settings-form" class="settings-form">
            <div class="form-group">
              <label for="backup-enabled">
                <input type="checkbox" id="backup-enabled" name="backupEnabled" checked>
                Activer les sauvegardes automatiques
              </label>
            </div>

            <div class="form-group">
              <label for="backup-interval">Intervalle de sauvegarde</label>
              <select id="backup-interval-select" name="backupInterval" class="luna-select">
                <option value="900000">15 minutes</option>
                <option value="1800000">30 minutes</option>
                <option value="3600000" selected>1 heure</option>
                <option value="7200000">2 heures</option>
                <option value="14400000">4 heures</option>
                <option value="28800000">8 heures</option>
                <option value="86400000">24 heures</option>
              </select>
            </div>

            <div class="form-group">
              <label for="max-backups">Nombre maximum de sauvegardes</label>
              <input type="number" id="max-backups" name="maxBackups" min="1" max="50" value="10">
            </div>

            <div class="form-group">
              <label for="encrypt-backups">
                <input type="checkbox" id="encrypt-backups" name="encryptBackups" checked>
                Chiffrer les sauvegardes
              </label>
            </div>

            <div class="form-group">
              <label for="compression-level">Niveau de compression</label>
              <select id="compression-level" name="compressionLevel" class="luna-select">
                <option value="low">Faible (plus rapide)</option>
                <option value="medium" selected>Moyen</option>
                <option value="high">Élevé (plus lent)</option>
              </select>
            </div>

            <div class="form-group">
              <label for="external-backup-path">Chemin de sauvegarde externe</label>
              <input type="text" id="external-backup-path" name="externalBackupPath" value="/Volumes/seagate/Jarvis_Working/backups">
            </div>

            <div class="form-actions">
              <button type="button" id="reset-settings" class="luna-button">Réinitialiser</button>
              <button type="submit" class="luna-button primary">Enregistrer</button>
            </div>
          </form>
        </div>

        <div class="backup-log">
          <h3>Journal de sauvegarde</h3>
          <div class="log-container" id="backup-log-container">
            <div class="log-entry">Système de sauvegarde initialisé</div>
          </div>
        </div>

        <div class="backup-charts">
          <div class="chart-container">
            <h3>Taille des sauvegardes</h3>
            <canvas id="backup-size-chart"></canvas>
          </div>
          <div class="chart-container">
            <h3>Durée des sauvegardes</h3>
            <canvas id="backup-duration-chart"></canvas>
          </div>
        </div>
      </div>

      <!-- Overlay de progression de sauvegarde -->
      <div id="backup-progress-overlay" class="backup-progress-overlay" style="display: none;">
        <div class="backup-progress-card">
          <h3>Sauvegarde en cours</h3>
          <div class="backup-progress-bar">
            <div id="backup-progress" class="backup-progress" style="width: 0%"></div>
          </div>
          <div class="backup-progress-status">
            <span class="status">Statut:</span>
            <span id="backup-progress-status">Préparation...</span>
            <span class="percentage" id="backup-progress-percentage">0%</span>
          </div>
          <div id="backup-progress-details" class="backup-progress-details">
            Initialisation de la sauvegarde...
          </div>
          <button id="cancel-backup" class="luna-button danger">Annuler</button>
        </div>
      </div>
    </main>

    <footer class="luna-footer">
      <p>Luna - Interface Cognitive Avancée &copy; 2025</p>
      <div class="system-status">
        <span class="status-indicator <%= systemStatus.active ? 'active' : 'inactive' %>"></span>
        <span class="status-text">Système <%= systemStatus.active ? 'actif' : 'inactif' %></span>
        <span class="version">v<%= systemStatus.version %></span>
      </div>
    </footer>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const socket = io();
      let backupState = {};
      let backupSizeChart = null;
      let backupDurationChart = null;

      // Initialiser les graphiques
      initCharts();

      // Charger les données de sauvegarde au chargement de la page
      loadBackupData();

      // Gestionnaire d'événements pour le bouton de rafraîchissement
      document.getElementById('refresh-backup').addEventListener('click', loadBackupData);

      // Gestionnaire d'événements pour le bouton de création de sauvegarde
      document.getElementById('create-backup').addEventListener('click', createBackup);

      // Gestionnaire d'événements pour le formulaire de paramètres
      document.getElementById('backup-settings-form').addEventListener('submit', function(e) {
        e.preventDefault();
        saveBackupSettings();
      });

      // Gestionnaire d'événements pour le bouton de réinitialisation des paramètres
      document.getElementById('reset-settings').addEventListener('click', resetBackupSettings);

      // Gestionnaire d'événements pour le bouton d'annulation de sauvegarde
      document.getElementById('cancel-backup').addEventListener('click', cancelBackup);

      // Fonction pour charger les données de sauvegarde
      function loadBackupData() {
        addLogEntry('Chargement des données de sauvegarde...', 'info');
        socket.emit('get backup state');
      }

      // Écouter les données de sauvegarde
      socket.on('backup state', function(data) {
        if (data.success) {
          backupState = data.state;
          updateBackupUI(backupState);
          addLogEntry('Données de sauvegarde chargées avec succès', 'success');
        } else {
          addLogEntry('Erreur lors du chargement des données de sauvegarde: ' + (data.error || 'Erreur inconnue'), 'error');
          showError('Erreur lors du chargement des données de sauvegarde');
        }
      });

      // Fonction pour créer une sauvegarde
      function createBackup() {
        const description = prompt('Description de la sauvegarde:', 'Sauvegarde manuelle');
        
        if (description === null) {
          return; // L'utilisateur a annulé
        }
        
        showBackupProgress(0, 'Préparation...', 'Initialisation de la sauvegarde...');
        
        addLogEntry(`Démarrage de la sauvegarde: ${description}`, 'info');
        socket.emit('create backup', { description });
      }

      // Fonction pour sauvegarder les paramètres de sauvegarde
      function saveBackupSettings() {
        const form = document.getElementById('backup-settings-form');
        const formData = new FormData(form);
        
        const settings = {
          backupEnabled: formData.get('backupEnabled') === 'on',
          backupInterval: parseInt(formData.get('backupInterval')),
          maxBackups: parseInt(formData.get('maxBackups')),
          encryptBackups: formData.get('encryptBackups') === 'on',
          compressionLevel: formData.get('compressionLevel'),
          externalBackupPath: formData.get('externalBackupPath')
        };
        
        addLogEntry('Sauvegarde des paramètres...', 'info');
        socket.emit('set backup options', settings);
      }

      // Fonction pour réinitialiser les paramètres de sauvegarde
      function resetBackupSettings() {
        if (!backupState.options) return;
        
        document.getElementById('backup-enabled').checked = backupState.options.backupEnabled;
        document.getElementById('backup-interval-select').value = backupState.options.backupInterval;
        document.getElementById('max-backups').value = backupState.options.maxBackups;
        document.getElementById('encrypt-backups').checked = backupState.options.encryptBackups;
        document.getElementById('compression-level').value = backupState.options.compressionLevel;
        document.getElementById('external-backup-path').value = backupState.options.externalBackupPath || '/Volumes/seagate/Jarvis_Working/backups';
        
        addLogEntry('Paramètres réinitialisés', 'info');
      }

      // Fonction pour annuler une sauvegarde en cours
      function cancelBackup() {
        if (confirm('Êtes-vous sûr de vouloir annuler la sauvegarde en cours ?')) {
          socket.emit('cancel backup');
          hideBackupProgress();
        }
      }

      // Fonction pour restaurer une sauvegarde
      function restoreBackup(backupId) {
        if (confirm('Êtes-vous sûr de vouloir restaurer cette sauvegarde ? Toutes les données actuelles seront remplacées.')) {
          addLogEntry('Démarrage de la restauration...', 'info');
          socket.emit('restore backup', { backupId });
        }
      }

      // Fonction pour supprimer une sauvegarde
      function deleteBackup(backupId) {
        if (confirm('Êtes-vous sûr de vouloir supprimer cette sauvegarde ?')) {
          addLogEntry('Suppression de la sauvegarde...', 'info');
          socket.emit('delete backup', { backupId });
        }
      }

      // Écouter les événements de sauvegarde
      socket.on('backup started', function(data) {
        showBackupProgress(5, 'Démarrage...', `Sauvegarde "${data.description}" en cours de démarrage...`);
        addLogEntry(`Sauvegarde "${data.description}" démarrée`, 'info');
      });

      socket.on('backup progress', function(data) {
        showBackupProgress(
          data.progress,
          data.status,
          data.details
        );
      });

      socket.on('backup completed', function(data) {
        hideBackupProgress();
        
        if (data.success) {
          addLogEntry(`Sauvegarde terminée: ${data.backup.fileName} (${formatSize(data.backup.size)})`, 'success');
          showSuccess('Sauvegarde terminée avec succès');
          loadBackupData(); // Recharger les données
        } else {
          addLogEntry('Erreur lors de la sauvegarde: ' + (data.error || 'Erreur inconnue'), 'error');
          showError('Erreur lors de la sauvegarde');
        }
      });

      socket.on('backup cancelled', function() {
        hideBackupProgress();
        addLogEntry('Sauvegarde annulée', 'warning');
      });

      socket.on('backup options updated', function(data) {
        if (data.success) {
          addLogEntry('Paramètres de sauvegarde mis à jour', 'success');
          showSuccess('Paramètres mis à jour avec succès');
          loadBackupData(); // Recharger les données
        } else {
          addLogEntry('Erreur lors de la mise à jour des paramètres: ' + (data.error || 'Erreur inconnue'), 'error');
          showError('Erreur lors de la mise à jour des paramètres');
        }
      });

      socket.on('backup restored', function(data) {
        if (data.success) {
          addLogEntry(`Sauvegarde restaurée: ${data.backup.fileName}`, 'success');
          showSuccess('Sauvegarde restaurée avec succès');
          loadBackupData(); // Recharger les données
        } else {
          addLogEntry('Erreur lors de la restauration: ' + (data.error || 'Erreur inconnue'), 'error');
          showError('Erreur lors de la restauration');
        }
      });

      socket.on('backup deleted', function(data) {
        if (data.success) {
          addLogEntry(`Sauvegarde supprimée: ${data.backupId}`, 'success');
          showSuccess('Sauvegarde supprimée avec succès');
          loadBackupData(); // Recharger les données
        } else {
          addLogEntry('Erreur lors de la suppression: ' + (data.error || 'Erreur inconnue'), 'error');
          showError('Erreur lors de la suppression');
        }
      });

      socket.on('backup log', function(data) {
        addLogEntry(data.message, data.level || 'info');
      });

      // Fonction pour mettre à jour l'interface utilisateur de sauvegarde
      function updateBackupUI(state) {
        // Mettre à jour les informations de statut
        document.getElementById('last-backup-time').textContent = state.lastBackupTime ? 
          new Date(state.lastBackupTime).toLocaleString() : 'Jamais';
        document.getElementById('last-backup-size').textContent = `Taille: ${formatSize(state.lastBackupSize)}`;
        document.getElementById('total-backups').textContent = state.totalBackups || 0;
        document.getElementById('total-saved-size').textContent = `Taille totale: ${formatSize(state.totalSavedSize)}`;
        document.getElementById('external-drive-status').textContent = state.externalDriveAvailable ? 'Disponible' : 'Non disponible';
        document.getElementById('external-drive-status').style.color = state.externalDriveAvailable ? '#2ecc71' : '#e74c3c';
        document.getElementById('external-drive-path').textContent = `Chemin: ${state.options?.externalBackupPath || 'Non défini'}`;
        document.getElementById('backup-system-status').textContent = state.backupInProgress ? 'Sauvegarde en cours' : 
          (state.options?.backupEnabled ? 'Actif' : 'Inactif');
        document.getElementById('backup-system-status').style.color = state.backupInProgress ? '#f39c12' : 
          (state.options?.backupEnabled ? '#2ecc71' : '#e74c3c');
        document.getElementById('backup-interval').textContent = `Intervalle: ${formatInterval(state.options?.backupInterval)}`;

        // Mettre à jour le tableau d'historique
        updateBackupHistory(state.backupHistory);

        // Mettre à jour les paramètres du formulaire
        if (state.options) {
          document.getElementById('backup-enabled').checked = state.options.backupEnabled;
          document.getElementById('backup-interval-select').value = state.options.backupInterval;
          document.getElementById('max-backups').value = state.options.maxBackups;
          document.getElementById('encrypt-backups').checked = state.options.encryptBackups;
          document.getElementById('compression-level').value = state.options.compressionLevel;
          document.getElementById('external-backup-path').value = state.options.externalBackupPath || '/Volumes/seagate/Jarvis_Working/backups';
        }

        // Mettre à jour les graphiques
        updateCharts(state.backupHistory);
      }

      // Fonction pour mettre à jour l'historique des sauvegardes
      function updateBackupHistory(history) {
        const tableBody = document.getElementById('backup-history-table');
        
        if (!history || history.length === 0) {
          tableBody.innerHTML = `<tr><td colspan="6" class="no-data">Aucune sauvegarde disponible</td></tr>`;
          return;
        }
        
        tableBody.innerHTML = '';
        
        // Trier l'historique par date (plus récent en premier)
        const sortedHistory = [...history].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        
        sortedHistory.forEach(backup => {
          const row = document.createElement('tr');
          
          row.innerHTML = `
            <td>${new Date(backup.timestamp).toLocaleString()}</td>
            <td>${backup.description}</td>
            <td>${formatSize(backup.size)}</td>
            <td>${formatDuration(backup.duration)}</td>
            <td>${backup.encrypted ? 'Oui' : 'Non'}</td>
            <td class="actions">
              <button class="luna-button" onclick="restoreBackup('${backup.id}')">Restaurer</button>
              <button class="luna-button danger" onclick="deleteBackup('${backup.id}')">Supprimer</button>
            </td>
          `;
          
          tableBody.appendChild(row);
        });
        
        // Ajouter les fonctions au contexte global
        window.restoreBackup = restoreBackup;
        window.deleteBackup = deleteBackup;
      }

      // Fonction pour initialiser les graphiques
      function initCharts() {
        const sizeCtx = document.getElementById('backup-size-chart').getContext('2d');
        backupSizeChart = new Chart(sizeCtx, {
          type: 'bar',
          data: {
            labels: [],
            datasets: [{
              label: 'Taille (MB)',
              data: [],
              backgroundColor: 'rgba(54, 162, 235, 0.5)',
              borderColor: 'rgba(54, 162, 235, 1)',
              borderWidth: 1
            }]
          },
          options: {
            responsive: true,
            scales: {
              y: {
                beginAtZero: true
              }
            }
          }
        });

        const durationCtx = document.getElementById('backup-duration-chart').getContext('2d');
        backupDurationChart = new Chart(durationCtx, {
          type: 'line',
          data: {
            labels: [],
            datasets: [{
              label: 'Durée (secondes)',
              data: [],
              backgroundColor: 'rgba(255, 99, 132, 0.2)',
              borderColor: 'rgba(255, 99, 132, 1)',
              borderWidth: 2,
              tension: 0.4
            }]
          },
          options: {
            responsive: true,
            scales: {
              y: {
                beginAtZero: true
              }
            }
          }
        });
      }

      // Fonction pour mettre à jour les graphiques
      function updateCharts(history) {
        if (!history || history.length === 0) return;
        
        // Trier l'historique par date (plus ancien en premier)
        const sortedHistory = [...history].sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
        
        // Limiter à 10 dernières sauvegardes
        const recentHistory = sortedHistory.slice(-10);
        
        const labels = recentHistory.map(backup => {
          const date = new Date(backup.timestamp);
          return date.toLocaleDateString();
        });
        
        const sizes = recentHistory.map(backup => Math.round(backup.size / (1024 * 1024))); // Convertir en MB
        const durations = recentHistory.map(backup => Math.round(backup.duration / 1000)); // Convertir en secondes
        
        backupSizeChart.data.labels = labels;
        backupSizeChart.data.datasets[0].data = sizes;
        backupSizeChart.update();
        
        backupDurationChart.data.labels = labels;
        backupDurationChart.data.datasets[0].data = durations;
        backupDurationChart.update();
      }

      // Fonction pour afficher la progression de la sauvegarde
      function showBackupProgress(progress, status, details) {
        document.getElementById('backup-progress-overlay').style.display = 'flex';
        document.getElementById('backup-progress').style.width = `${progress}%`;
        document.getElementById('backup-progress-status').textContent = status;
        document.getElementById('backup-progress-percentage').textContent = `${progress}%`;
        document.getElementById('backup-progress-details').textContent = details;
      }

      // Fonction pour masquer la progression de la sauvegarde
      function hideBackupProgress() {
        document.getElementById('backup-progress-overlay').style.display = 'none';
      }

      // Fonction pour ajouter une entrée au journal
      function addLogEntry(message, level = 'info') {
        const logContainer = document.getElementById('backup-log-container');
        const entry = document.createElement('div');
        entry.className = `log-entry ${level}`;
        
        const timestamp = new Date().toLocaleTimeString();
        entry.textContent = `[${timestamp}] ${message}`;
        
        logContainer.appendChild(entry);
        logContainer.scrollTop = logContainer.scrollHeight;
      }

      // Fonction pour formater une taille en octets
      function formatSize(size) {
        const units = ['B', 'KB', 'MB', 'GB', 'TB'];
        let formattedSize = size;
        let unitIndex = 0;
        
        while (formattedSize >= 1024 && unitIndex < units.length - 1) {
          formattedSize /= 1024;
          unitIndex++;
        }
        
        return `${formattedSize.toFixed(2)} ${units[unitIndex]}`;
      }

      // Fonction pour formater une durée en millisecondes
      function formatDuration(duration) {
        const seconds = Math.floor(duration / 1000);
        
        if (seconds < 60) {
          return `${seconds} sec`;
        }
        
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        
        return `${minutes} min ${remainingSeconds} sec`;
      }

      // Fonction pour formater un intervalle en millisecondes
      function formatInterval(interval) {
        const minutes = interval / 60000;
        
        if (minutes < 60) {
          return `${minutes} minutes`;
        }
        
        const hours = minutes / 60;
        
        if (hours === 1) {
          return '1 heure';
        }
        
        return `${hours} heures`;
      }

      // Fonction pour afficher un message d'erreur
      function showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.textContent = message;
        document.body.appendChild(errorDiv);
        setTimeout(() => {
          errorDiv.classList.add('show');
          setTimeout(() => {
            errorDiv.classList.remove('show');
            setTimeout(() => {
              errorDiv.remove();
            }, 300);
          }, 3000);
        }, 10);
      }

      // Fonction pour afficher un message de succès
      function showSuccess(message) {
        const successDiv = document.createElement('div');
        successDiv.className = 'success-message';
        successDiv.textContent = message;
        document.body.appendChild(successDiv);
        setTimeout(() => {
          successDiv.classList.add('show');
          setTimeout(() => {
            successDiv.classList.remove('show');
            setTimeout(() => {
              successDiv.remove();
            }, 300);
          }, 3000);
        }, 10);
      }
    });
  </script>
</body>
</html>
