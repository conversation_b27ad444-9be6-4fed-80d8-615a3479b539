<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %></title>
  <link rel="stylesheet" href="/luna/css/luna-main.css">
  <link rel="stylesheet" href="/luna/css/luna-training.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="/socket.io/socket.io.js"></script>
</head>
<body class="luna-body">
  <div class="luna-container">
    <header class="luna-header">
      <div class="luna-logo">
        <img src="/luna/images/luna-logo.png" alt="Luna Logo">
        <h1>Luna - Formation IA</h1>
      </div>
      <nav class="luna-nav">
        <ul>
          <li><a href="/luna" class="nav-link">Accueil</a></li>
          <li><a href="/luna/memory" class="nav-link">Mémoire</a></li>
          <li><a href="/luna/accelerators" class="nav-link">Accélérateurs</a></li>
          <li><a href="/luna/training" class="nav-link active">Formation</a></li>
          <li><a href="/luna/brain" class="nav-link">Cerveau</a></li>
          <li><a href="/luna/code" class="nav-link">Code</a></li>
        </ul>
      </nav>
    </header>

    <main class="luna-main">
      <div class="training-container">
        <div class="training-header">
          <h2>Formation et Évolution Neuronale</h2>
          <div class="training-controls">
            <button id="refresh-training" class="luna-button">Rafraîchir</button>
            <button id="start-training" class="luna-button primary">Démarrer la formation</button>
          </div>
        </div>

        <div class="training-stats">
          <div class="stat-card">
            <h3>QI Estimé</h3>
            <div class="stat-value" id="iq-value">0</div>
            <div class="progress-bar">
              <div class="progress" id="iq-progress" style="width: 0%"></div>
            </div>
          </div>
          <div class="stat-card">
            <h3>Neurones</h3>
            <div class="stat-value" id="neurons-value">0</div>
            <div class="progress-bar">
              <div class="progress" id="neurons-progress" style="width: 0%"></div>
            </div>
          </div>
          <div class="stat-card">
            <h3>Vitesse d'apprentissage</h3>
            <div class="stat-value" id="learning-speed-value">0%</div>
            <div class="progress-bar">
              <div class="progress" id="learning-speed-progress" style="width: 0%"></div>
            </div>
          </div>
          <div class="stat-card">
            <h3>Temps d'évolution</h3>
            <div class="stat-value" id="evolution-time-value">0h</div>
            <div class="progress-bar">
              <div class="progress" id="evolution-time-progress" style="width: 0%"></div>
            </div>
          </div>
        </div>

        <div class="training-modules">
          <div class="loading-container">
            <div class="loading-spinner"></div>
            <p>Chargement des modules de formation...</p>
          </div>
        </div>

        <div class="training-details" id="training-details">
          <h3>Détails de la formation</h3>
          <div class="details-content">
            <div class="detail-row">
              <span class="detail-label">Statut</span>
              <span class="detail-value" id="training-status">Inactif</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Modèle de base</span>
              <span class="detail-value" id="base-model">deepseek-r1:7b</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Modèle d'entraînement</span>
              <span class="detail-value" id="training-model">deepseek-r1:7b</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Cycles complétés</span>
              <span class="detail-value" id="completed-cycles">0</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Temps total</span>
              <span class="detail-value" id="total-time">0h 0m 0s</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Dernière mise à jour</span>
              <span class="detail-value" id="last-update">Jamais</span>
            </div>
            <div class="detail-actions">
              <button id="pause-training" class="luna-button" disabled>Pause</button>
              <button id="stop-training" class="luna-button" disabled>Arrêter</button>
              <button id="reset-training" class="luna-button danger" disabled>Réinitialiser</button>
            </div>
          </div>
        </div>

        <div class="training-log">
          <h3>Journal de formation</h3>
          <div class="log-container" id="training-log">
            <div class="log-entry">Système de formation prêt. Cliquez sur "Démarrer la formation" pour commencer.</div>
          </div>
        </div>

        <div class="training-charts">
          <div class="chart-container">
            <h3>Progression de l'apprentissage</h3>
            <canvas id="learning-progress-chart"></canvas>
          </div>
          <div class="chart-container">
            <h3>Évolution neuronale</h3>
            <canvas id="neural-evolution-chart"></canvas>
          </div>
        </div>
      </div>
    </main>

    <footer class="luna-footer">
      <p>Luna - Interface Cognitive Avancée &copy; 2025</p>
      <div class="system-status">
        <span class="status-indicator <%= systemStatus.active ? 'active' : 'inactive' %>"></span>
        <span class="status-text">Système <%= systemStatus.active ? 'actif' : 'inactif' %></span>
        <span class="version">v<%= systemStatus.version %></span>
      </div>
    </footer>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const socket = io();
      let trainingActive = false;
      let trainingData = {};
      let learningProgressChart = null;
      let neuralEvolutionChart = null;

      // Initialiser les graphiques
      initCharts();

      // Charger les données de formation au chargement de la page
      loadTrainingData();

      // Gestionnaire d'événements pour le bouton de rafraîchissement
      document.getElementById('refresh-training').addEventListener('click', loadTrainingData);

      // Gestionnaire d'événements pour le bouton de démarrage de la formation
      document.getElementById('start-training').addEventListener('click', startTraining);

      // Gestionnaire d'événements pour le bouton de pause de la formation
      document.getElementById('pause-training').addEventListener('click', pauseTraining);

      // Gestionnaire d'événements pour le bouton d'arrêt de la formation
      document.getElementById('stop-training').addEventListener('click', stopTraining);

      // Gestionnaire d'événements pour le bouton de réinitialisation de la formation
      document.getElementById('reset-training').addEventListener('click', resetTraining);

      // Fonction pour charger les données de formation
      function loadTrainingData() {
        addLogEntry('Chargement des données de formation...', 'info');
        
        // Afficher l'indicateur de chargement
        const modulesContainer = document.querySelector('.training-modules');
        modulesContainer.innerHTML = `
          <div class="loading-container">
            <div class="loading-spinner"></div>
            <p>Chargement des modules de formation...</p>
          </div>
        `;

        // Émettre l'événement pour obtenir les données de formation
        socket.emit('get training data');
      }

      // Écouter les données de formation
      socket.on('training data', function(data) {
        if (data.success) {
          trainingData = data;
          updateTrainingStats(data.stats);
          displayTrainingModules(data.modules);
          updateTrainingDetails(data.details);
          updateCharts(data);
          
          // Mettre à jour l'état des boutons
          trainingActive = data.details.status === 'active';
          updateButtonStates();
          
          addLogEntry('Données de formation chargées avec succès', 'success');
        } else {
          addLogEntry('Erreur lors du chargement des données de formation: ' + (data.error || 'Erreur inconnue'), 'error');
          showError('Erreur lors du chargement des données de formation');
        }
      });

      // Fonction pour démarrer la formation
      function startTraining() {
        const button = document.getElementById('start-training');
        button.disabled = true;
        button.textContent = 'Démarrage...';

        addLogEntry('Démarrage de la formation...', 'info');

        // Émettre l'événement pour démarrer la formation
        socket.emit('start training');
      }

      // Fonction pour mettre en pause la formation
      function pauseTraining() {
        const button = document.getElementById('pause-training');
        button.disabled = true;
        
        if (button.textContent === 'Pause') {
          button.textContent = 'Reprise...';
          addLogEntry('Mise en pause de la formation...', 'info');
          socket.emit('pause training');
        } else {
          button.textContent = 'Pause...';
          addLogEntry('Reprise de la formation...', 'info');
          socket.emit('resume training');
        }
      }

      // Fonction pour arrêter la formation
      function stopTraining() {
        const button = document.getElementById('stop-training');
        button.disabled = true;
        button.textContent = 'Arrêt...';

        addLogEntry('Arrêt de la formation...', 'info');

        // Émettre l'événement pour arrêter la formation
        socket.emit('stop training');
      }

      // Fonction pour réinitialiser la formation
      function resetTraining() {
        if (!confirm('Êtes-vous sûr de vouloir réinitialiser la formation ? Toutes les données de formation seront perdues.')) {
          return;
        }

        const button = document.getElementById('reset-training');
        button.disabled = true;
        button.textContent = 'Réinitialisation...';

        addLogEntry('Réinitialisation de la formation...', 'warning');

        // Émettre l'événement pour réinitialiser la formation
        socket.emit('reset training');
      }

      // Écouter les événements de formation
      socket.on('training started', function(data) {
        if (data.success) {
          trainingActive = true;
          updateButtonStates();
          
          const button = document.getElementById('start-training');
          button.textContent = 'Démarrer la formation';
          
          addLogEntry('Formation démarrée avec succès', 'success');
          showSuccess(data.message || 'Formation démarrée avec succès');
          
          // Mettre à jour les détails de la formation
          if (data.details) {
            updateTrainingDetails(data.details);
          }
        } else {
          const button = document.getElementById('start-training');
          button.disabled = false;
          button.textContent = 'Démarrer la formation';
          
          addLogEntry('Erreur lors du démarrage de la formation: ' + (data.error || 'Erreur inconnue'), 'error');
          showError(data.error || 'Erreur lors du démarrage de la formation');
        }
      });

      socket.on('training paused', function(data) {
        const button = document.getElementById('pause-training');
        button.disabled = false;
        
        if (data.success) {
          button.textContent = 'Reprendre';
          addLogEntry('Formation mise en pause', 'info');
          showSuccess(data.message || 'Formation mise en pause');
          
          // Mettre à jour les détails de la formation
          if (data.details) {
            updateTrainingDetails(data.details);
          }
        } else {
          button.textContent = 'Pause';
          addLogEntry('Erreur lors de la mise en pause de la formation: ' + (data.error || 'Erreur inconnue'), 'error');
          showError(data.error || 'Erreur lors de la mise en pause de la formation');
        }
      });

      socket.on('training resumed', function(data) {
        const button = document.getElementById('pause-training');
        button.disabled = false;
        
        if (data.success) {
          button.textContent = 'Pause';
          addLogEntry('Formation reprise', 'info');
          showSuccess(data.message || 'Formation reprise');
          
          // Mettre à jour les détails de la formation
          if (data.details) {
            updateTrainingDetails(data.details);
          }
        } else {
          button.textContent = 'Reprendre';
          addLogEntry('Erreur lors de la reprise de la formation: ' + (data.error || 'Erreur inconnue'), 'error');
          showError(data.error || 'Erreur lors de la reprise de la formation');
        }
      });

      socket.on('training stopped', function(data) {
        const button = document.getElementById('stop-training');
        button.disabled = false;
        button.textContent = 'Arrêter';
        
        trainingActive = false;
        updateButtonStates();
        
        if (data.success) {
          addLogEntry('Formation arrêtée', 'info');
          showSuccess(data.message || 'Formation arrêtée');
          
          // Mettre à jour les détails de la formation
          if (data.details) {
            updateTrainingDetails(data.details);
          }
        } else {
          addLogEntry('Erreur lors de l\'arrêt de la formation: ' + (data.error || 'Erreur inconnue'), 'error');
          showError(data.error || 'Erreur lors de l\'arrêt de la formation');
        }
      });

      socket.on('training reset', function(data) {
        const button = document.getElementById('reset-training');
        button.disabled = false;
        button.textContent = 'Réinitialiser';
        
        trainingActive = false;
        updateButtonStates();
        
        if (data.success) {
          addLogEntry('Formation réinitialisée', 'warning');
          showSuccess(data.message || 'Formation réinitialisée');
          
          // Recharger les données de formation
          loadTrainingData();
        } else {
          addLogEntry('Erreur lors de la réinitialisation de la formation: ' + (data.error || 'Erreur inconnue'), 'error');
          showError(data.error || 'Erreur lors de la réinitialisation de la formation');
        }
      });

      socket.on('training progress', function(data) {
        if (data.log) {
          addLogEntry(data.log, data.logType || 'info');
        }
        
        if (data.stats) {
          updateTrainingStats(data.stats);
        }
        
        if (data.details) {
          updateTrainingDetails(data.details);
        }
        
        if (data.chartData) {
          updateCharts(data.chartData);
        }
      });

      // Fonction pour mettre à jour les statistiques de formation
      function updateTrainingStats(stats) {
        document.getElementById('iq-value').textContent = stats.iq || 0;
        document.getElementById('neurons-value').textContent = stats.neurons || 0;
        document.getElementById('learning-speed-value').textContent = `${stats.learningSpeed || 0}%`;
        document.getElementById('evolution-time-value').textContent = `${stats.evolutionTime || 0}h`;

        document.getElementById('iq-progress').style.width = `${Math.min(100, ((stats.iq || 0) / 200) * 100)}%`;
        document.getElementById('neurons-progress').style.width = `${Math.min(100, ((stats.neurons || 0) / 10000) * 100)}%`;
        document.getElementById('learning-speed-progress').style.width = `${stats.learningSpeed || 0}%`;
        document.getElementById('evolution-time-progress').style.width = `${Math.min(100, ((stats.evolutionTime || 0) / 100) * 100)}%`;
      }

      // Fonction pour afficher les modules de formation
      function displayTrainingModules(modules) {
        const container = document.querySelector('.training-modules');
        container.innerHTML = '';

        if (!modules || modules.length === 0) {
          container.innerHTML = `<div class="no-data">Aucun module de formation disponible</div>`;
          return;
        }

        modules.forEach(module => {
          const card = document.createElement('div');
          card.className = 'module-card';
          card.dataset.id = module.id;

          // Déterminer le statut du module
          let statusClass = 'inactive';
          let statusText = 'Inactif';
          
          if (module.active) {
            statusClass = 'active';
            statusText = 'Actif';
          } else if (module.pending) {
            statusClass = 'pending';
            statusText = 'En attente';
          }

          card.innerHTML = `
            <div class="module-header">
              <h4>${module.name}</h4>
              <span class="module-status ${statusClass}">${statusText}</span>
            </div>
            <div class="module-description">${module.description}</div>
            <div class="module-stats">
              <div class="module-stat">
                <span class="module-stat-label">Progression</span>
                <span class="module-stat-value">${module.progress || 0}%</span>
              </div>
              <div class="module-stat">
                <span class="module-stat-label">Cycles</span>
                <span class="module-stat-value">${module.cycles || 0}</span>
              </div>
              <div class="module-stat">
                <span class="module-stat-label">Temps</span>
                <span class="module-stat-value">${module.time || '0h'}</span>
              </div>
              <div class="module-stat">
                <span class="module-stat-label">Priorité</span>
                <span class="module-stat-value">${module.priority || 'Normale'}</span>
              </div>
            </div>
            <div class="module-progress">
              <div class="progress-bar">
                <div class="progress" style="width: ${module.progress || 0}%"></div>
              </div>
            </div>
          `;

          card.addEventListener('click', () => showModuleDetails(module));
          container.appendChild(card);
        });
      }

      // Fonction pour afficher les détails d'un module
      function showModuleDetails(module) {
        // Implémenter l'affichage des détails du module
        console.log('Module sélectionné:', module);
      }

      // Fonction pour mettre à jour les détails de la formation
      function updateTrainingDetails(details) {
        document.getElementById('training-status').textContent = details.status === 'active' ? 'Actif' : details.status === 'paused' ? 'En pause' : 'Inactif';
        document.getElementById('base-model').textContent = details.baseModel || 'deepseek-r1:7b';
        document.getElementById('training-model').textContent = details.trainingModel || 'deepseek-r1:7b';
        document.getElementById('completed-cycles').textContent = details.completedCycles || 0;
        
        // Formater le temps total
        const hours = Math.floor(details.totalTime / 3600) || 0;
        const minutes = Math.floor((details.totalTime % 3600) / 60) || 0;
        const seconds = Math.floor(details.totalTime % 60) || 0;
        document.getElementById('total-time').textContent = `${hours}h ${minutes}m ${seconds}s`;
        
        document.getElementById('last-update').textContent = details.lastUpdate ? new Date(details.lastUpdate).toLocaleString() : 'Jamais';
      }

      // Fonction pour mettre à jour l'état des boutons
      function updateButtonStates() {
        document.getElementById('start-training').disabled = trainingActive;
        document.getElementById('pause-training').disabled = !trainingActive;
        document.getElementById('stop-training').disabled = !trainingActive;
        document.getElementById('reset-training').disabled = trainingActive;
        
        // Mettre à jour le texte du bouton de pause
        if (trainingData.details && trainingData.details.status === 'paused') {
          document.getElementById('pause-training').textContent = 'Reprendre';
        } else {
          document.getElementById('pause-training').textContent = 'Pause';
        }
      }

      // Fonction pour initialiser les graphiques
      function initCharts() {
        const learningProgressCtx = document.getElementById('learning-progress-chart').getContext('2d');
        learningProgressChart = new Chart(learningProgressCtx, {
          type: 'line',
          data: {
            labels: [],
            datasets: [{
              label: 'QI',
              data: [],
              backgroundColor: 'rgba(54, 162, 235, 0.2)',
              borderColor: 'rgba(54, 162, 235, 1)',
              borderWidth: 2,
              tension: 0.4
            }, {
              label: 'Vitesse d\'apprentissage',
              data: [],
              backgroundColor: 'rgba(255, 99, 132, 0.2)',
              borderColor: 'rgba(255, 99, 132, 1)',
              borderWidth: 2,
              tension: 0.4
            }]
          },
          options: {
            responsive: true,
            scales: {
              y: {
                beginAtZero: true
              }
            }
          }
        });

        const neuralEvolutionCtx = document.getElementById('neural-evolution-chart').getContext('2d');
        neuralEvolutionChart = new Chart(neuralEvolutionCtx, {
          type: 'line',
          data: {
            labels: [],
            datasets: [{
              label: 'Neurones',
              data: [],
              backgroundColor: 'rgba(75, 192, 192, 0.2)',
              borderColor: 'rgba(75, 192, 192, 1)',
              borderWidth: 2,
              tension: 0.4
            }, {
              label: 'Connexions',
              data: [],
              backgroundColor: 'rgba(153, 102, 255, 0.2)',
              borderColor: 'rgba(153, 102, 255, 1)',
              borderWidth: 2,
              tension: 0.4
            }]
          },
          options: {
            responsive: true,
            scales: {
              y: {
                beginAtZero: true
              }
            }
          }
        });
      }

      // Fonction pour mettre à jour les graphiques
      function updateCharts(data) {
        if (!data.chartData) return;
        
        // Mettre à jour le graphique de progression de l'apprentissage
        if (data.chartData.learningProgress) {
          const chartData = data.chartData.learningProgress;
          
          learningProgressChart.data.labels = chartData.labels;
          learningProgressChart.data.datasets[0].data = chartData.iq;
          learningProgressChart.data.datasets[1].data = chartData.learningSpeed;
          learningProgressChart.update();
        }
        
        // Mettre à jour le graphique d'évolution neuronale
        if (data.chartData.neuralEvolution) {
          const chartData = data.chartData.neuralEvolution;
          
          neuralEvolutionChart.data.labels = chartData.labels;
          neuralEvolutionChart.data.datasets[0].data = chartData.neurons;
          neuralEvolutionChart.data.datasets[1].data = chartData.connections;
          neuralEvolutionChart.update();
        }
      }

      // Fonction pour ajouter une entrée au journal
      function addLogEntry(message, type = 'info') {
        const logContainer = document.getElementById('training-log');
        const entry = document.createElement('div');
        entry.className = `log-entry ${type}`;
        
        const timestamp = new Date().toLocaleTimeString();
        entry.textContent = `[${timestamp}] ${message}`;
        
        logContainer.appendChild(entry);
        logContainer.scrollTop = logContainer.scrollHeight;
      }

      // Fonction pour afficher un message d'erreur
      function showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.textContent = message;
        document.body.appendChild(errorDiv);
        setTimeout(() => {
          errorDiv.classList.add('show');
          setTimeout(() => {
            errorDiv.classList.remove('show');
            setTimeout(() => {
              errorDiv.remove();
            }, 300);
          }, 3000);
        }, 10);
      }

      // Fonction pour afficher un message de succès
      function showSuccess(message) {
        const successDiv = document.createElement('div');
        successDiv.className = 'success-message';
        successDiv.textContent = message;
        document.body.appendChild(successDiv);
        setTimeout(() => {
          successDiv.classList.add('show');
          setTimeout(() => {
            successDiv.classList.remove('show');
            setTimeout(() => {
              successDiv.remove();
            }, 300);
          }, 3000);
        }, 10);
      }
    });
  </script>
</body>
</html>
