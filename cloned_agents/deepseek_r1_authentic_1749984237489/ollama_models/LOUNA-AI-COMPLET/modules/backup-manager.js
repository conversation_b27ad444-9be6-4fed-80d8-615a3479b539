/**
 * Module de gestion des sauvegardes pour Luna
 * Intègre des sauvegardes automatiques et chiffrées
 */

const fs = require('fs');
const path = require('path');
const { exec, spawn } = require('child_process');
const EventEmitter = require('events');
const crypto = require('crypto');

class BackupManager extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      backupEnabled: options.backupEnabled || true,
      backupInterval: options.backupInterval || 3600000, // 1 heure par défaut
      backupPath: options.backupPath || path.join(__dirname, '../backups'),
      externalBackupPath: options.externalBackupPath || '/Volumes/seagate/Jarvis_Working/backups',
      maxBackups: options.maxBackups || 10,
      encryptBackups: options.encryptBackups || true,
      compressionLevel: options.compressionLevel || 'medium', // 'low', 'medium', 'high'
      excludeDirs: options.excludeDirs || ['node_modules', 'backups', 'logs', 'data/temp'],
      logPath: options.logPath || path.join(__dirname, '../logs/backup'),
      debug: options.debug || false,
      ...options
    };
    
    // État du système de sauvegarde
    this.state = {
      lastBackupTime: null,
      lastBackupSize: 0,
      lastBackupDuration: 0,
      totalBackups: 0,
      backupInProgress: false,
      backupHistory: [],
      totalSavedSize: 0,
      externalDriveAvailable: false
    };
    
    // Créer les répertoires nécessaires s'ils n'existent pas
    this._createDirectories();
    
    // Initialiser le système de sauvegarde
    this._init();
  }
  
  /**
   * Initialise le système de sauvegarde
   * @private
   */
  async _init() {
    try {
      // Créer le fichier de log
      this._log('Initialisation du système de sauvegarde');
      
      // Vérifier si le disque externe est disponible
      this.state.externalDriveAvailable = await this._checkExternalDrive();
      
      // Charger l'historique des sauvegardes
      await this._loadBackupHistory();
      
      // Planifier les sauvegardes automatiques
      if (this.options.backupEnabled) {
        this._scheduleBackups();
      }
      
      this._log('Système de sauvegarde initialisé avec succès');
      this.emit('backup:initialized', { success: true });
    } catch (error) {
      this._log(`Erreur lors de l'initialisation du système de sauvegarde: ${error.message}`, 'error');
      this.emit('backup:error', { error: error.message });
    }
  }
  
  /**
   * Vérifie si le disque externe est disponible
   * @private
   * @returns {Promise<boolean>} - true si le disque externe est disponible, false sinon
   */
  async _checkExternalDrive() {
    return new Promise((resolve) => {
      fs.access(path.dirname(this.options.externalBackupPath), fs.constants.W_OK, (err) => {
        if (err) {
          this._log(`Disque externe non disponible: ${err.message}`, 'warning');
          resolve(false);
        } else {
          this._log('Disque externe disponible');
          resolve(true);
        }
      });
    });
  }
  
  /**
   * Crée les répertoires nécessaires
   * @private
   */
  _createDirectories() {
    try {
      // Créer le répertoire de logs
      if (!fs.existsSync(this.options.logPath)) {
        fs.mkdirSync(this.options.logPath, { recursive: true });
      }
      
      // Créer le répertoire de sauvegardes
      if (!fs.existsSync(this.options.backupPath)) {
        fs.mkdirSync(this.options.backupPath, { recursive: true });
      }
      
      // Créer le répertoire de sauvegardes externes si le disque est disponible
      if (fs.existsSync(path.dirname(this.options.externalBackupPath))) {
        if (!fs.existsSync(this.options.externalBackupPath)) {
          fs.mkdirSync(this.options.externalBackupPath, { recursive: true });
        }
      }
    } catch (error) {
      console.error(`Erreur lors de la création des répertoires: ${error.message}`);
    }
  }
  
  /**
   * Écrit un message dans le fichier de log
   * @param {string} message - Le message à logger
   * @param {string} level - Le niveau de log (info, warning, error)
   * @private
   */
  _log(message, level = 'info') {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}\n`;
    
    // Afficher dans la console en mode debug
    if (this.options.debug) {
      console.log(`[BACKUP] ${message}`);
    }
    
    // Écrire dans le fichier de log
    try {
      const logFile = path.join(this.options.logPath, `backup-${new Date().toISOString().split('T')[0]}.log`);
      fs.appendFileSync(logFile, logMessage);
    } catch (error) {
      console.error(`Erreur lors de l'écriture dans le fichier de log: ${error.message}`);
    }
  }
  
  /**
   * Planifie les sauvegardes automatiques
   * @private
   */
  _scheduleBackups() {
    // Effectuer une sauvegarde initiale après 5 minutes
    setTimeout(() => {
      this.createBackup();
    }, 300000);
    
    // Planifier les sauvegardes régulières
    setInterval(() => {
      this.createBackup();
    }, this.options.backupInterval);
  }
  
  /**
   * Charge l'historique des sauvegardes
   * @private
   */
  async _loadBackupHistory() {
    try {
      const historyFile = path.join(this.options.backupPath, 'backup-history.json');
      
      if (fs.existsSync(historyFile)) {
        const historyData = JSON.parse(fs.readFileSync(historyFile, 'utf8'));
        this.state.backupHistory = historyData.history || [];
        this.state.totalBackups = this.state.backupHistory.length;
        this.state.lastBackupTime = this.state.backupHistory.length > 0 ? 
          this.state.backupHistory[this.state.backupHistory.length - 1].timestamp : null;
        
        // Calculer la taille totale sauvegardée
        this.state.totalSavedSize = this.state.backupHistory.reduce((total, backup) => total + backup.size, 0);
      }
    } catch (error) {
      this._log(`Erreur lors du chargement de l'historique des sauvegardes: ${error.message}`, 'error');
    }
  }
  
  /**
   * Sauvegarde l'historique des sauvegardes
   * @private
   */
  async _saveBackupHistory() {
    try {
      const historyFile = path.join(this.options.backupPath, 'backup-history.json');
      
      fs.writeFileSync(historyFile, JSON.stringify({
        history: this.state.backupHistory,
        lastUpdated: new Date().toISOString()
      }, null, 2));
    } catch (error) {
      this._log(`Erreur lors de la sauvegarde de l'historique des sauvegardes: ${error.message}`, 'error');
    }
  }
  
  /**
   * Crée une sauvegarde
   * @param {string} [description='Sauvegarde automatique'] - Description de la sauvegarde
   * @returns {Promise<object>} - Informations sur la sauvegarde
   */
  async createBackup(description = 'Sauvegarde automatique') {
    if (this.state.backupInProgress) {
      this._log('Une sauvegarde est déjà en cours', 'warning');
      return null;
    }
    
    this.state.backupInProgress = true;
    const startTime = Date.now();
    
    try {
      this._log(`Démarrage de la sauvegarde: ${description}`);
      this.emit('backup:started', { description, startTime });
      
      // Générer un nom de fichier unique
      const timestamp = new Date().toISOString().replace(/:/g, '-');
      const backupFileName = `backup-${timestamp}.tar.gz`;
      const backupFilePath = path.join(this.options.backupPath, backupFileName);
      
      // Créer la liste des répertoires à exclure
      const excludeArgs = this.options.excludeDirs.map(dir => `--exclude='${dir}'`).join(' ');
      
      // Créer la commande tar
      const compressionLevel = this._getCompressionLevel();
      const tarCommand = `tar -cz${compressionLevel} ${excludeArgs} -f "${backupFilePath}" .`;
      
      // Exécuter la commande tar
      await new Promise((resolve, reject) => {
        exec(tarCommand, { cwd: path.resolve(__dirname, '..') }, (error, stdout, stderr) => {
          if (error) {
            reject(error);
            return;
          }
          resolve();
        });
      });
      
      // Chiffrer la sauvegarde si nécessaire
      let finalBackupPath = backupFilePath;
      if (this.options.encryptBackups) {
        finalBackupPath = await this._encryptBackup(backupFilePath);
        
        // Supprimer le fichier non chiffré
        fs.unlinkSync(backupFilePath);
      }
      
      // Copier la sauvegarde sur le disque externe si disponible
      let externalBackupPath = null;
      if (this.state.externalDriveAvailable) {
        externalBackupPath = path.join(this.options.externalBackupPath, path.basename(finalBackupPath));
        fs.copyFileSync(finalBackupPath, externalBackupPath);
      }
      
      // Obtenir la taille du fichier
      const stats = fs.statSync(finalBackupPath);
      const backupSize = stats.size;
      
      // Calculer la durée
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Mettre à jour l'état
      this.state.lastBackupTime = new Date().toISOString();
      this.state.lastBackupSize = backupSize;
      this.state.lastBackupDuration = duration;
      this.state.totalBackups++;
      this.state.totalSavedSize += backupSize;
      
      // Ajouter à l'historique
      const backupInfo = {
        id: `backup-${Date.now()}`,
        timestamp: new Date().toISOString(),
        description,
        fileName: path.basename(finalBackupPath),
        path: finalBackupPath,
        externalPath: externalBackupPath,
        size: backupSize,
        duration,
        encrypted: this.options.encryptBackups
      };
      
      this.state.backupHistory.push(backupInfo);
      
      // Limiter le nombre de sauvegardes dans l'historique
      if (this.state.backupHistory.length > this.options.maxBackups * 2) {
        this.state.backupHistory = this.state.backupHistory.slice(-this.options.maxBackups);
      }
      
      // Sauvegarder l'historique
      await this._saveBackupHistory();
      
      // Nettoyer les anciennes sauvegardes
      await this._cleanupOldBackups();
      
      this._log(`Sauvegarde terminée: ${path.basename(finalBackupPath)} (${this._formatSize(backupSize)})`);
      this.emit('backup:completed', backupInfo);
      
      this.state.backupInProgress = false;
      return backupInfo;
    } catch (error) {
      this._log(`Erreur lors de la sauvegarde: ${error.message}`, 'error');
      this.emit('backup:error', { error: error.message });
      this.state.backupInProgress = false;
      return null;
    }
  }
  
  /**
   * Chiffre un fichier de sauvegarde
   * @param {string} filePath - Chemin du fichier à chiffrer
   * @returns {Promise<string>} - Chemin du fichier chiffré
   * @private
   */
  async _encryptBackup(filePath) {
    return new Promise((resolve, reject) => {
      try {
        // Générer une clé de chiffrement
        const key = crypto.randomBytes(32);
        const iv = crypto.randomBytes(16);
        
        // Créer le fichier de sortie
        const encryptedFilePath = `${filePath}.enc`;
        const keyFilePath = `${filePath}.key`;
        
        // Créer les flux
        const readStream = fs.createReadStream(filePath);
        const writeStream = fs.createWriteStream(encryptedFilePath);
        const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
        
        // Chiffrer le fichier
        readStream.pipe(cipher).pipe(writeStream);
        
        writeStream.on('finish', () => {
          // Sauvegarder la clé et l'IV
          fs.writeFileSync(keyFilePath, JSON.stringify({
            key: key.toString('hex'),
            iv: iv.toString('hex')
          }));
          
          resolve(encryptedFilePath);
        });
        
        writeStream.on('error', (error) => {
          reject(error);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
  
  /**
   * Nettoie les anciennes sauvegardes
   * @private
   */
  async _cleanupOldBackups() {
    try {
      // Obtenir la liste des fichiers de sauvegarde
      const backupFiles = fs.readdirSync(this.options.backupPath)
        .filter(file => file.startsWith('backup-') && (file.endsWith('.tar.gz') || file.endsWith('.tar.gz.enc')))
        .map(file => ({
          name: file,
          path: path.join(this.options.backupPath, file),
          time: fs.statSync(path.join(this.options.backupPath, file)).mtime.getTime()
        }))
        .sort((a, b) => b.time - a.time); // Trier par date (plus récent en premier)
      
      // Supprimer les sauvegardes excédentaires
      if (backupFiles.length > this.options.maxBackups) {
        const filesToDelete = backupFiles.slice(this.options.maxBackups);
        
        for (const file of filesToDelete) {
          // Supprimer le fichier de sauvegarde
          fs.unlinkSync(file.path);
          
          // Supprimer le fichier de clé si nécessaire
          const keyFile = `${file.path}.key`;
          if (fs.existsSync(keyFile)) {
            fs.unlinkSync(keyFile);
          }
          
          this._log(`Suppression de l'ancienne sauvegarde: ${file.name}`);
        }
      }
    } catch (error) {
      this._log(`Erreur lors du nettoyage des anciennes sauvegardes: ${error.message}`, 'error');
    }
  }
  
  /**
   * Restaure une sauvegarde
   * @param {string} backupId - ID de la sauvegarde à restaurer
   * @returns {Promise<boolean>} - true si la restauration a réussi, false sinon
   */
  async restoreBackup(backupId) {
    if (this.state.backupInProgress) {
      this._log('Une sauvegarde est déjà en cours, impossible de restaurer', 'warning');
      return false;
    }
    
    try {
      // Trouver la sauvegarde dans l'historique
      const backup = this.state.backupHistory.find(b => b.id === backupId);
      
      if (!backup) {
        this._log(`Sauvegarde non trouvée: ${backupId}`, 'error');
        return false;
      }
      
      this._log(`Démarrage de la restauration: ${backup.fileName}`);
      this.emit('backup:restore-started', { backup });
      
      // Vérifier si le fichier existe
      let backupPath = backup.path;
      if (!fs.existsSync(backupPath)) {
        // Essayer le chemin externe
        if (backup.externalPath && fs.existsSync(backup.externalPath)) {
          backupPath = backup.externalPath;
        } else {
          this._log(`Fichier de sauvegarde non trouvé: ${backup.fileName}`, 'error');
          return false;
        }
      }
      
      // Créer un répertoire temporaire pour la restauration
      const tempDir = path.join(__dirname, '../data/temp/restore');
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }
      
      // Déchiffrer la sauvegarde si nécessaire
      let fileToRestore = backupPath;
      if (backup.encrypted) {
        fileToRestore = await this._decryptBackup(backupPath, tempDir);
      }
      
      // Extraire la sauvegarde
      await new Promise((resolve, reject) => {
        exec(`tar -xzf "${fileToRestore}" -C "${tempDir}"`, (error, stdout, stderr) => {
          if (error) {
            reject(error);
            return;
          }
          resolve();
        });
      });
      
      // Copier les fichiers restaurés vers le répertoire principal
      // Exclure les répertoires spécifiés
      const excludeArgs = this.options.excludeDirs.map(dir => `--exclude='${dir}'`).join(' ');
      await new Promise((resolve, reject) => {
        exec(`rsync -a ${excludeArgs} "${tempDir}/" "${path.resolve(__dirname, '..')}"`, (error, stdout, stderr) => {
          if (error) {
            reject(error);
            return;
          }
          resolve();
        });
      });
      
      // Nettoyer le répertoire temporaire
      fs.rmSync(tempDir, { recursive: true, force: true });
      
      this._log(`Restauration terminée: ${backup.fileName}`);
      this.emit('backup:restore-completed', { backup });
      
      return true;
    } catch (error) {
      this._log(`Erreur lors de la restauration: ${error.message}`, 'error');
      this.emit('backup:error', { error: error.message });
      return false;
    }
  }
  
  /**
   * Déchiffre un fichier de sauvegarde
   * @param {string} filePath - Chemin du fichier à déchiffrer
   * @param {string} outputDir - Répertoire de sortie
   * @returns {Promise<string>} - Chemin du fichier déchiffré
   * @private
   */
  async _decryptBackup(filePath, outputDir) {
    return new Promise((resolve, reject) => {
      try {
        // Lire la clé et l'IV
        const keyFilePath = `${filePath.replace('.enc', '')}.key`;
        if (!fs.existsSync(keyFilePath)) {
          reject(new Error(`Fichier de clé non trouvé: ${keyFilePath}`));
          return;
        }
        
        const keyData = JSON.parse(fs.readFileSync(keyFilePath, 'utf8'));
        const key = Buffer.from(keyData.key, 'hex');
        const iv = Buffer.from(keyData.iv, 'hex');
        
        // Créer le fichier de sortie
        const decryptedFilePath = path.join(outputDir, path.basename(filePath, '.enc'));
        
        // Créer les flux
        const readStream = fs.createReadStream(filePath);
        const writeStream = fs.createWriteStream(decryptedFilePath);
        const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);
        
        // Déchiffrer le fichier
        readStream.pipe(decipher).pipe(writeStream);
        
        writeStream.on('finish', () => {
          resolve(decryptedFilePath);
        });
        
        writeStream.on('error', (error) => {
          reject(error);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
  
  /**
   * Obtient le niveau de compression pour tar
   * @returns {string} - Option de compression pour tar
   * @private
   */
  _getCompressionLevel() {
    switch (this.options.compressionLevel) {
      case 'low':
        return '1';
      case 'high':
        return '9';
      case 'medium':
      default:
        return '5';
    }
  }
  
  /**
   * Formate une taille en octets en une chaîne lisible
   * @param {number} size - Taille en octets
   * @returns {string} - Taille formatée
   * @private
   */
  _formatSize(size) {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let formattedSize = size;
    let unitIndex = 0;
    
    while (formattedSize >= 1024 && unitIndex < units.length - 1) {
      formattedSize /= 1024;
      unitIndex++;
    }
    
    return `${formattedSize.toFixed(2)} ${units[unitIndex]}`;
  }
  
  /**
   * Obtient l'état actuel du système de sauvegarde
   * @returns {object} - L'état du système de sauvegarde
   */
  getBackupState() {
    return {
      ...this.state,
      options: {
        backupEnabled: this.options.backupEnabled,
        backupInterval: this.options.backupInterval,
        maxBackups: this.options.maxBackups,
        encryptBackups: this.options.encryptBackups,
        compressionLevel: this.options.compressionLevel
      }
    };
  }
  
  /**
   * Définit les options du système de sauvegarde
   * @param {object} options - Nouvelles options
   * @returns {boolean} - true si les options ont été définies, false sinon
   */
  setBackupOptions(options) {
    try {
      // Mettre à jour les options
      this.options = {
        ...this.options,
        ...options
      };
      
      // Sauvegarder les options
      const optionsFile = path.join(this.options.backupPath, 'backup-options.json');
      fs.writeFileSync(optionsFile, JSON.stringify(this.options, null, 2));
      
      this._log('Options de sauvegarde mises à jour');
      this.emit('backup:options-updated', { options: this.options });
      
      return true;
    } catch (error) {
      this._log(`Erreur lors de la mise à jour des options: ${error.message}`, 'error');
      return false;
    }
  }
}

module.exports = BackupManager;
