/**
 * Module de surveillance du système pour Luna
 * Surveille les performances du système et détecte les anomalies
 */

const os = require('os');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const EventEmitter = require('events');

class SystemMonitor extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      monitoringEnabled: options.monitoringEnabled || true,
      monitoringInterval: options.monitoringInterval || 5000, // 5 secondes par défaut
      alertThresholds: {
        cpu: options.alertThresholds?.cpu || 80, // Pourcentage d'utilisation du CPU
        memory: options.alertThresholds?.memory || 80, // Pourcentage d'utilisation de la mémoire
        disk: options.alertThresholds?.disk || 90, // Pourcentage d'utilisation du disque
        temperature: options.alertThresholds?.temperature || 80 // Température en degrés Celsius
      },
      historyLength: options.historyLength || 100, // Nombre de points de données à conserver
      logPath: options.logPath || path.join(__dirname, '../logs/system'),
      debug: options.debug || false,
      ...options
    };
    
    // État du système de surveillance
    this.state = {
      monitoring: false,
      monitoringInterval: null,
      lastUpdate: null,
      systemInfo: {
        platform: os.platform(),
        arch: os.arch(),
        hostname: os.hostname(),
        release: os.release(),
        uptime: os.uptime(),
        totalMemory: os.totalmem(),
        freeMemory: os.freemem(),
        cpuCount: os.cpus().length,
        cpuModel: os.cpus()[0].model,
        loadAverage: os.loadavg()
      },
      currentMetrics: {
        cpu: 0,
        memory: 0,
        disk: 0,
        temperature: 0,
        network: {
          rx: 0,
          tx: 0
        }
      },
      history: {
        timestamps: [],
        cpu: [],
        memory: [],
        disk: [],
        temperature: [],
        network: {
          rx: [],
          tx: []
        }
      },
      alerts: [],
      processes: []
    };
    
    // Créer les répertoires nécessaires s'ils n'existent pas
    this._createDirectories();
    
    // Initialiser le système de surveillance
    this._init();
  }
  
  /**
   * Initialise le système de surveillance
   * @private
   */
  async _init() {
    try {
      // Créer le fichier de log
      this._log('Initialisation du système de surveillance');
      
      // Collecter les informations système initiales
      await this._collectSystemInfo();
      
      // Démarrer la surveillance si activée
      if (this.options.monitoringEnabled) {
        this.startMonitoring();
      }
      
      this._log('Système de surveillance initialisé avec succès');
      this.emit('monitor:initialized', { success: true });
    } catch (error) {
      this._log(`Erreur lors de l'initialisation du système de surveillance: ${error.message}`, 'error');
      this.emit('monitor:error', { error: error.message });
    }
  }
  
  /**
   * Crée les répertoires nécessaires
   * @private
   */
  _createDirectories() {
    try {
      // Créer le répertoire de logs
      if (!fs.existsSync(this.options.logPath)) {
        fs.mkdirSync(this.options.logPath, { recursive: true });
      }
    } catch (error) {
      console.error(`Erreur lors de la création des répertoires: ${error.message}`);
    }
  }
  
  /**
   * Écrit un message dans le fichier de log
   * @param {string} message - Le message à logger
   * @param {string} level - Le niveau de log (info, warning, error)
   * @private
   */
  _log(message, level = 'info') {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}\n`;
    
    // Afficher dans la console en mode debug
    if (this.options.debug) {
      console.log(`[MONITOR] ${message}`);
    }
    
    // Écrire dans le fichier de log
    try {
      const logFile = path.join(this.options.logPath, `system-${new Date().toISOString().split('T')[0]}.log`);
      fs.appendFileSync(logFile, logMessage);
    } catch (error) {
      console.error(`Erreur lors de l'écriture dans le fichier de log: ${error.message}`);
    }
  }
  
  /**
   * Démarre la surveillance du système
   * @returns {boolean} - true si la surveillance a démarré, false sinon
   */
  startMonitoring() {
    if (this.state.monitoring) {
      this._log('La surveillance est déjà en cours');
      return false;
    }
    
    this._log('Démarrage de la surveillance du système');
    
    // Collecter les métriques immédiatement
    this._collectMetrics();
    
    // Démarrer l'intervalle de surveillance
    this.state.monitoringInterval = setInterval(() => {
      this._collectMetrics();
    }, this.options.monitoringInterval);
    
    this.state.monitoring = true;
    this.emit('monitor:started');
    
    return true;
  }
  
  /**
   * Arrête la surveillance du système
   * @returns {boolean} - true si la surveillance a été arrêtée, false sinon
   */
  stopMonitoring() {
    if (!this.state.monitoring) {
      this._log('La surveillance n\'est pas en cours');
      return false;
    }
    
    this._log('Arrêt de la surveillance du système');
    
    // Arrêter l'intervalle de surveillance
    clearInterval(this.state.monitoringInterval);
    this.state.monitoringInterval = null;
    
    this.state.monitoring = false;
    this.emit('monitor:stopped');
    
    return true;
  }
  
  /**
   * Collecte les informations système
   * @private
   */
  async _collectSystemInfo() {
    try {
      // Mettre à jour les informations système
      this.state.systemInfo = {
        platform: os.platform(),
        arch: os.arch(),
        hostname: os.hostname(),
        release: os.release(),
        uptime: os.uptime(),
        totalMemory: os.totalmem(),
        freeMemory: os.freemem(),
        cpuCount: os.cpus().length,
        cpuModel: os.cpus()[0].model,
        loadAverage: os.loadavg()
      };
      
      // Collecter les informations sur le disque
      if (os.platform() === 'darwin' || os.platform() === 'linux') {
        await this._collectDiskInfo();
      }
    } catch (error) {
      this._log(`Erreur lors de la collecte des informations système: ${error.message}`, 'error');
    }
  }
  
  /**
   * Collecte les informations sur le disque
   * @private
   */
  async _collectDiskInfo() {
    return new Promise((resolve, reject) => {
      const command = os.platform() === 'darwin' ? 
        'df -h / | tail -1 | awk \'{print $5}\'' : 
        'df -h / | tail -1 | awk \'{print $5}\'';
      
      exec(command, (error, stdout, stderr) => {
        if (error) {
          reject(error);
          return;
        }
        
        try {
          // Extraire le pourcentage d'utilisation du disque
          const diskUsage = parseInt(stdout.trim().replace('%', ''));
          this.state.currentMetrics.disk = diskUsage;
        } catch (err) {
          this._log(`Erreur lors de l'analyse des informations du disque: ${err.message}`, 'error');
        }
        
        resolve();
      });
    });
  }
  
  /**
   * Collecte les métriques système
   * @private
   */
  async _collectMetrics() {
    try {
      // Collecter les informations système
      await this._collectSystemInfo();
      
      // Calculer l'utilisation du CPU
      const cpuUsage = await this._calculateCpuUsage();
      this.state.currentMetrics.cpu = cpuUsage;
      
      // Calculer l'utilisation de la mémoire
      const memoryUsage = Math.round((1 - (os.freemem() / os.totalmem())) * 100);
      this.state.currentMetrics.memory = memoryUsage;
      
      // Collecter la température (simulée pour les plateformes non supportées)
      const temperature = await this._getTemperature();
      this.state.currentMetrics.temperature = temperature;
      
      // Collecter les informations réseau (simulées)
      const network = this._getNetworkUsage();
      this.state.currentMetrics.network = network;
      
      // Collecter les processus
      const processes = await this._getTopProcesses();
      this.state.processes = processes;
      
      // Ajouter les métriques à l'historique
      this._addToHistory();
      
      // Vérifier les alertes
      this._checkAlerts();
      
      // Mettre à jour la dernière mise à jour
      this.state.lastUpdate = new Date().toISOString();
      
      // Émettre l'événement de mise à jour
      this.emit('monitor:updated', {
        metrics: this.state.currentMetrics,
        timestamp: this.state.lastUpdate
      });
    } catch (error) {
      this._log(`Erreur lors de la collecte des métriques: ${error.message}`, 'error');
    }
  }
  
  /**
   * Calcule l'utilisation du CPU
   * @returns {Promise<number>} - Pourcentage d'utilisation du CPU
   * @private
   */
  async _calculateCpuUsage() {
    return new Promise((resolve) => {
      const startMeasure = os.cpus();
      
      // Attendre un court instant pour mesurer l'utilisation du CPU
      setTimeout(() => {
        const endMeasure = os.cpus();
        let totalIdle = 0;
        let totalTick = 0;
        
        for (let i = 0; i < startMeasure.length; i++) {
          const startCpu = startMeasure[i];
          const endCpu = endMeasure[i];
          
          for (const type in endCpu.times) {
            totalTick += (endCpu.times[type] - startCpu.times[type]);
          }
          
          totalIdle += (endCpu.times.idle - startCpu.times.idle);
        }
        
        const cpuUsage = Math.round(100 - (totalIdle / totalTick * 100));
        resolve(cpuUsage);
      }, 100);
    });
  }
  
  /**
   * Obtient la température du système (simulée)
   * @returns {Promise<number>} - Température en degrés Celsius
   * @private
   */
  async _getTemperature() {
    // Simuler la température pour les plateformes non supportées
    return Math.floor(Math.random() * 20) + 40; // 40-60°C
  }
  
  /**
   * Obtient l'utilisation du réseau (simulée)
   * @returns {object} - Utilisation du réseau en octets/s
   * @private
   */
  _getNetworkUsage() {
    // Simuler l'utilisation du réseau
    return {
      rx: Math.floor(Math.random() * 1000000), // 0-1MB/s
      tx: Math.floor(Math.random() * 500000) // 0-500KB/s
    };
  }
  
  /**
   * Obtient les processus les plus consommateurs de ressources
   * @returns {Promise<Array>} - Liste des processus
   * @private
   */
  async _getTopProcesses() {
    return new Promise((resolve) => {
      // Simuler les processus
      const processes = [
        { pid: 1234, name: 'node', cpu: Math.random() * 10, memory: Math.random() * 500 },
        { pid: 2345, name: 'chrome', cpu: Math.random() * 20, memory: Math.random() * 1000 },
        { pid: 3456, name: 'finder', cpu: Math.random() * 5, memory: Math.random() * 200 },
        { pid: 4567, name: 'safari', cpu: Math.random() * 15, memory: Math.random() * 800 },
        { pid: 5678, name: 'terminal', cpu: Math.random() * 2, memory: Math.random() * 100 }
      ];
      
      resolve(processes);
    });
  }
  
  /**
   * Ajoute les métriques actuelles à l'historique
   * @private
   */
  _addToHistory() {
    const timestamp = new Date().toISOString();
    
    // Ajouter le timestamp
    this.state.history.timestamps.push(timestamp);
    
    // Ajouter les métriques
    this.state.history.cpu.push(this.state.currentMetrics.cpu);
    this.state.history.memory.push(this.state.currentMetrics.memory);
    this.state.history.disk.push(this.state.currentMetrics.disk);
    this.state.history.temperature.push(this.state.currentMetrics.temperature);
    this.state.history.network.rx.push(this.state.currentMetrics.network.rx);
    this.state.history.network.tx.push(this.state.currentMetrics.network.tx);
    
    // Limiter la taille de l'historique
    if (this.state.history.timestamps.length > this.options.historyLength) {
      this.state.history.timestamps.shift();
      this.state.history.cpu.shift();
      this.state.history.memory.shift();
      this.state.history.disk.shift();
      this.state.history.temperature.shift();
      this.state.history.network.rx.shift();
      this.state.history.network.tx.shift();
    }
  }
  
  /**
   * Vérifie les alertes
   * @private
   */
  _checkAlerts() {
    const alerts = [];
    
    // Vérifier l'utilisation du CPU
    if (this.state.currentMetrics.cpu >= this.options.alertThresholds.cpu) {
      alerts.push({
        type: 'cpu',
        level: 'warning',
        message: `Utilisation élevée du CPU: ${this.state.currentMetrics.cpu}%`,
        timestamp: new Date().toISOString()
      });
    }
    
    // Vérifier l'utilisation de la mémoire
    if (this.state.currentMetrics.memory >= this.options.alertThresholds.memory) {
      alerts.push({
        type: 'memory',
        level: 'warning',
        message: `Utilisation élevée de la mémoire: ${this.state.currentMetrics.memory}%`,
        timestamp: new Date().toISOString()
      });
    }
    
    // Vérifier l'utilisation du disque
    if (this.state.currentMetrics.disk >= this.options.alertThresholds.disk) {
      alerts.push({
        type: 'disk',
        level: 'warning',
        message: `Utilisation élevée du disque: ${this.state.currentMetrics.disk}%`,
        timestamp: new Date().toISOString()
      });
    }
    
    // Vérifier la température
    if (this.state.currentMetrics.temperature >= this.options.alertThresholds.temperature) {
      alerts.push({
        type: 'temperature',
        level: 'warning',
        message: `Température élevée: ${this.state.currentMetrics.temperature}°C`,
        timestamp: new Date().toISOString()
      });
    }
    
    // Ajouter les alertes à l'état
    if (alerts.length > 0) {
      this.state.alerts = [...this.state.alerts, ...alerts];
      
      // Limiter le nombre d'alertes
      if (this.state.alerts.length > 100) {
        this.state.alerts = this.state.alerts.slice(-100);
      }
      
      // Logger les alertes
      alerts.forEach(alert => {
        this._log(alert.message, alert.level);
      });
      
      // Émettre l'événement d'alerte
      this.emit('monitor:alerts', { alerts });
    }
  }
  
  /**
   * Obtient l'état actuel du système de surveillance
   * @returns {object} - L'état du système de surveillance
   */
  getMonitorState() {
    return {
      ...this.state,
      options: {
        monitoringEnabled: this.options.monitoringEnabled,
        monitoringInterval: this.options.monitoringInterval,
        alertThresholds: this.options.alertThresholds
      }
    };
  }
  
  /**
   * Définit les options du système de surveillance
   * @param {object} options - Nouvelles options
   * @returns {boolean} - true si les options ont été définies, false sinon
   */
  setMonitorOptions(options) {
    try {
      // Sauvegarder l'état de surveillance actuel
      const wasMonitoring = this.state.monitoring;
      
      // Arrêter la surveillance si elle est en cours
      if (wasMonitoring) {
        this.stopMonitoring();
      }
      
      // Mettre à jour les options
      this.options = {
        ...this.options,
        ...options
      };
      
      // Redémarrer la surveillance si elle était en cours
      if (wasMonitoring && this.options.monitoringEnabled) {
        this.startMonitoring();
      }
      
      this._log('Options de surveillance mises à jour');
      this.emit('monitor:options-updated', { options: this.options });
      
      return true;
    } catch (error) {
      this._log(`Erreur lors de la mise à jour des options: ${error.message}`, 'error');
      return false;
    }
  }
  
  /**
   * Efface les alertes
   * @returns {boolean} - true si les alertes ont été effacées, false sinon
   */
  clearAlerts() {
    try {
      this.state.alerts = [];
      
      this._log('Alertes effacées');
      this.emit('monitor:alerts-cleared');
      
      return true;
    } catch (error) {
      this._log(`Erreur lors de l'effacement des alertes: ${error.message}`, 'error');
      return false;
    }
  }
}

module.exports = SystemMonitor;
