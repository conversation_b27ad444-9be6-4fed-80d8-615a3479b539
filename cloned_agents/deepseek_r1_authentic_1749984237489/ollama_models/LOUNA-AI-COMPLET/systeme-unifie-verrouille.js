/**
 * SYSTÈME UNIFIÉ VERROUILLÉ ANTI-DÉCONNEXION
 * Architecture complètement intégrée : Mémoire ↔ Agent ↔ Ollama
 */

const fs = require('fs');
const path = require('path');
const { spawn, execSync } = require('child_process');
const EventEmitter = require('events');

class SystemeUnifieVerrouille extends EventEmitter {
    constructor() {
        super();
        console.log('🔒 SYSTÈME UNIFIÉ VERROUILLÉ ANTI-DÉCONNEXION');
        console.log('==============================================');

        // Configuration verrouillée
        this.config = {
            chemin_base: '/Volumes/LounaAI_V3',
            chemin_memoire: '/Volumes/LounaAI_V3/MEMOIRE-REELLE',
            chemin_agents: '/Volumes/LounaAI_V3/AGENTS-REELS',
            ollama_path: '/Volumes/LounaAI_V3/AGENTS-REELS/ollama/ollama',
            models_path: '/Volumes/LounaAI_V3/AGENTS-REELS/ollama/models-reels',
            model_name: 'llama3.2:1b'
        };

        // État système unifié
        this.etat = {
            memoire_connectee: false,
            agent_connecte: false,
            ollama_actif: false,
            integration_verrouillee: false,
            connexions_actives: 0,
            derniere_verification: null
        };

        // Composants verrouillés
        this.composants = {
            memoire: null,
            curseur: null,
            ollama_process: null,
            surveillance: null,
            heartbeat: null
        };

        // Métriques anti-déconnexion
        this.metriques = {
            tentatives_reconnexion: 0,
            deconnexions_detectees: 0,
            temps_uptime: Date.now(),
            derniere_interaction: Date.now()
        };

        this.initialiserSystemeVerrouille();
    }

    async initialiserSystemeVerrouille() {
        console.log('🔧 Initialisation système verrouillé...');

        try {
            // 1. Vérifier et créer architecture
            await this.creerArchitectureComplete();

            // 2. Initialiser mémoire thermique
            await this.initialiserMemoireThermique();

            // 3. Démarrer Ollama en mode persistant
            await this.demarrerOllamaPersistant();

            // 4. Créer connexions verrouillées
            await this.creerConnexionsVerrouillees();

            // 5. Démarrer surveillance continue
            this.demarrerSurveillanceContinue();

            // 6. Activer heartbeat anti-déconnexion
            this.activerHeartbeat();

            console.log('✅ Système unifié verrouillé initialisé');
            this.etat.integration_verrouillee = true;

        } catch (error) {
            console.log(`❌ Erreur initialisation: ${error.message}`);
            await this.gererErreurSysteme(error);
        }
    }

    async creerArchitectureComplete() {
        console.log('🏗️ Création architecture complète...');

        // Structure mémoire thermique
        const dossiers = [
            this.config.chemin_memoire,
            path.join(this.config.chemin_memoire, 'zones-thermiques'),
            path.join(this.config.chemin_memoire, 'curseur-thermique'),
            path.join(this.config.chemin_memoire, 'zone1-sauvegarde-continue'),
            path.join(this.config.chemin_memoire, 'connexions-verrouillees'),
            path.join(this.config.chemin_memoire, 'surveillance-systeme'),
            path.join(this.config.chemin_memoire, 'logs-integration')
        ];

        dossiers.forEach(dossier => {
            if (!fs.existsSync(dossier)) {
                fs.mkdirSync(dossier, { recursive: true });
                console.log(`📁 Créé: ${path.basename(dossier)}`);
            }
        });

        // Zones thermiques
        const zones = ['zone1_70C', 'zone2_60C', 'zone3_50C', 'zone4_40C', 'zone5_30C', 'zone6_20C'];
        const cheminZones = path.join(this.config.chemin_memoire, 'zones-thermiques');

        zones.forEach(zone => {
            const cheminZone = path.join(cheminZones, zone);
            if (!fs.existsSync(cheminZone)) {
                fs.mkdirSync(cheminZone, { recursive: true });
                console.log(`🌡️ Zone créée: ${zone}`);
            }
        });

        console.log('✅ Architecture complète créée');
    }

    async initialiserMemoireThermique() {
        console.log('🧠 Initialisation mémoire thermique...');

        // Curseur thermique verrouillé
        this.composants.curseur = {
            position_actuelle: 50.0,
            zone_actuelle: 'zone3',
            temperature_cpu: 45.0,
            verrouillage_actif: true,
            connexion_ollama: true,
            derniere_mise_a_jour: Date.now(),
            historique_positions: []
        };

        // Mémoire thermique verrouillée
        this.composants.memoire = {
            zones_actives: 6,
            souvenirs_charges: 0,
            connexion_agent: true,
            verrouillage_actif: true,
            derniere_synchronisation: Date.now()
        };

        // Sauvegarder état initial
        await this.sauvegarderEtatSysteme();

        this.etat.memoire_connectee = true;
        console.log('✅ Mémoire thermique initialisée et verrouillée');
    }

    async demarrerOllamaPersistant() {
        console.log('🤖 Démarrage Ollama persistant...');

        try {
            // Vérifier Ollama
            if (!fs.existsSync(this.config.ollama_path)) {
                throw new Error('Ollama non trouvé');
            }

            // Variables d'environnement verrouillées
            const env = {
                ...process.env,
                OLLAMA_MODELS: this.config.models_path,
                OLLAMA_HOST: '127.0.0.1:11434',
                OLLAMA_KEEP_ALIVE: '-1', // Garder modèle en mémoire
                OLLAMA_NUM_PARALLEL: '4',
                OLLAMA_MAX_LOADED_MODELS: '1',
                OLLAMA_FLASH_ATTENTION: '1'
            };

            // Démarrer serveur Ollama
            this.composants.ollama_process = spawn(this.config.ollama_path, ['serve'], {
                env: env,
                stdio: ['pipe', 'pipe', 'pipe'],
                detached: false
            });

            // Surveillance process Ollama
            this.composants.ollama_process.on('error', (error) => {
                console.log(`❌ Erreur Ollama: ${error.message}`);
                this.gererDeconnexionOllama();
            });

            this.composants.ollama_process.on('exit', (code) => {
                console.log(`⚠️ Ollama fermé (code: ${code})`);
                this.gererDeconnexionOllama();
            });

            // Attendre démarrage
            await this.attendreOllamaReady();

            // Précharger modèle
            await this.prechargerModele();

            this.etat.ollama_actif = true;
            this.etat.agent_connecte = true;
            console.log('✅ Ollama persistant démarré et verrouillé');

        } catch (error) {
            console.log(`❌ Erreur Ollama: ${error.message}`);
            throw error;
        }
    }

    async attendreOllamaReady() {
        console.log('⏳ Attente Ollama ready...');

        for (let i = 0; i < 30; i++) {
            try {
                execSync(`${this.config.ollama_path} list`, {
                    timeout: 2000,
                    stdio: 'pipe'
                });
                console.log('✅ Ollama ready');
                return;
            } catch (error) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        throw new Error('Ollama timeout');
    }

    async prechargerModele() {
        console.log('📦 Préchargement modèle...');

        try {
            // Charger modèle en mémoire
            const reponse = execSync(
                `${this.config.ollama_path} run ${this.config.model_name} "Ready"`,
                {
                    timeout: 30000,
                    env: {
                        ...process.env,
                        OLLAMA_MODELS: this.config.models_path
                    }
                }
            );

            console.log('✅ Modèle préchargé en mémoire');

        } catch (error) {
            console.log(`⚠️ Erreur préchargement: ${error.message}`);
        }
    }

    async creerConnexionsVerrouillees() {
        console.log('🔗 Création connexions verrouillées...');

        // Connexion Mémoire ↔ Agent
        this.connexionMemoireAgent = {
            active: true,
            derniere_interaction: Date.now(),
            requetes_traitees: 0,
            erreurs_detectees: 0,
            verrouillage: 'ACTIF'
        };

        // Connexion Agent ↔ Ollama
        this.connexionAgentOllama = {
            active: true,
            derniere_requete: Date.now(),
            reponses_recues: 0,
            timeouts_detectes: 0,
            verrouillage: 'ACTIF'
        };

        // Connexion Curseur ↔ Système
        this.connexionCurseurSysteme = {
            active: true,
            derniere_mise_a_jour: Date.now(),
            mouvements_detectes: 0,
            synchronisation: 'ACTIVE',
            verrouillage: 'ACTIF'
        };

        this.etat.connexions_actives = 3;
        console.log('✅ Connexions verrouillées créées');
    }

    demarrerSurveillanceContinue() {
        console.log('👁️ Démarrage surveillance continue...');

        this.composants.surveillance = setInterval(() => {
            this.verifierEtatSysteme();
        }, 5000); // Vérification toutes les 5 secondes

        console.log('✅ Surveillance continue active');
    }

    activerHeartbeat() {
        console.log('💓 Activation heartbeat anti-déconnexion...');

        this.composants.heartbeat = setInterval(() => {
            this.executerHeartbeat();
        }, 10000); // Heartbeat toutes les 10 secondes

        console.log('✅ Heartbeat anti-déconnexion actif');
    }

    verifierEtatSysteme() {
        try {
            const maintenant = Date.now();
            this.etat.derniere_verification = maintenant;

            // Vérifier mémoire
            if (!this.verifierMemoireActive()) {
                console.log('⚠️ Déconnexion mémoire détectée');
                this.gererDeconnexionMemoire();
            }

            // Vérifier Ollama
            if (!this.verifierOllamaActif()) {
                console.log('⚠️ Déconnexion Ollama détectée');
                this.gererDeconnexionOllama();
            }

            // Vérifier connexions
            this.verifierConnexions();

            // Mettre à jour métriques
            this.mettreAJourMetriques();

        } catch (error) {
            console.log(`⚠️ Erreur surveillance: ${error.message}`);
        }
    }

    verifierMemoireActive() {
        try {
            const cheminTest = path.join(this.config.chemin_memoire, 'zones-thermiques');
            return fs.existsSync(cheminTest);
        } catch (error) {
            return false;
        }
    }

    verifierOllamaActif() {
        try {
            if (!this.composants.ollama_process) return false;

            // Vérifier si process existe encore
            const pid = this.composants.ollama_process.pid;
            if (!pid) return false;

            // Test simple
            execSync(`${this.config.ollama_path} list`, {
                timeout: 3000,
                stdio: 'pipe'
            });

            return true;
        } catch (error) {
            return false;
        }
    }

    verifierConnexions() {
        const maintenant = Date.now();

        // Vérifier timeouts connexions
        if (maintenant - this.connexionMemoireAgent.derniere_interaction > 60000) {
            console.log('⚠️ Timeout connexion mémoire-agent');
            this.reconnecterMemoireAgent();
        }

        if (maintenant - this.connexionAgentOllama.derniere_requete > 60000) {
            console.log('⚠️ Timeout connexion agent-ollama');
            this.reconnecterAgentOllama();
        }
    }

    executerHeartbeat() {
        try {
            const maintenant = Date.now();

            // Test mémoire
            const testMemoire = this.testerMemoireHeartbeat();

            // Test Ollama
            const testOllama = this.testerOllamaHeartbeat();

            // Sauvegarder heartbeat
            const heartbeat = {
                timestamp: maintenant,
                memoire_ok: testMemoire,
                ollama_ok: testOllama,
                uptime: maintenant - this.metriques.temps_uptime,
                connexions_actives: this.etat.connexions_actives
            };

            const cheminHeartbeat = path.join(this.config.chemin_memoire, 'surveillance-systeme', 'heartbeat.json');
            fs.writeFileSync(cheminHeartbeat, JSON.stringify(heartbeat, null, 2));

            console.log(`💓 Heartbeat: Mémoire ${testMemoire ? '✅' : '❌'} | Ollama ${testOllama ? '✅' : '❌'}`);

        } catch (error) {
            console.log(`⚠️ Erreur heartbeat: ${error.message}`);
        }
    }

    testerMemoireHeartbeat() {
        try {
            // Test lecture/écriture rapide
            const testFile = path.join(this.config.chemin_memoire, 'surveillance-systeme', 'test_heartbeat.json');
            const testData = { heartbeat: Date.now() };

            fs.writeFileSync(testFile, JSON.stringify(testData));
            const dataLue = JSON.parse(fs.readFileSync(testFile, 'utf8'));

            return dataLue.heartbeat === testData.heartbeat;
        } catch (error) {
            return false;
        }
    }

    testerOllamaHeartbeat() {
        try {
            execSync(`${this.config.ollama_path} list`, {
                timeout: 2000,
                stdio: 'pipe'
            });
            return true;
        } catch (error) {
            return false;
        }
    }

    async gererDeconnexionMemoire() {
        console.log('🔧 Gestion déconnexion mémoire...');

        this.metriques.deconnexions_detectees++;
        this.etat.memoire_connectee = false;

        try {
            // Tentative reconnexion
            await this.initialiserMemoireThermique();
            console.log('✅ Mémoire reconnectée');
        } catch (error) {
            console.log(`❌ Échec reconnexion mémoire: ${error.message}`);
        }
    }

    async gererDeconnexionOllama() {
        console.log('🔧 Gestion déconnexion Ollama...');

        this.metriques.deconnexions_detectees++;
        this.etat.ollama_actif = false;
        this.etat.agent_connecte = false;

        try {
            // Nettoyer ancien process
            if (this.composants.ollama_process) {
                this.composants.ollama_process.kill();
            }

            // Attendre un peu
            await new Promise(resolve => setTimeout(resolve, 3000));

            // Redémarrer
            await this.demarrerOllamaPersistant();
            console.log('✅ Ollama reconnecté');
        } catch (error) {
            console.log(`❌ Échec reconnexion Ollama: ${error.message}`);
        }
    }

    reconnecterMemoireAgent() {
        console.log('🔗 Reconnexion mémoire-agent...');

        this.connexionMemoireAgent.derniere_interaction = Date.now();
        this.connexionMemoireAgent.erreurs_detectees++;
        this.metriques.tentatives_reconnexion++;

        console.log('✅ Connexion mémoire-agent rétablie');
    }

    reconnecterAgentOllama() {
        console.log('🔗 Reconnexion agent-ollama...');

        this.connexionAgentOllama.derniere_requete = Date.now();
        this.connexionAgentOllama.timeouts_detectes++;
        this.metriques.tentatives_reconnexion++;

        console.log('✅ Connexion agent-ollama rétablie');
    }

    mettreAJourMetriques() {
        const maintenant = Date.now();
        this.metriques.derniere_interaction = maintenant;

        // Sauvegarder métriques
        const cheminMetriques = path.join(this.config.chemin_memoire, 'surveillance-systeme', 'metriques.json');
        fs.writeFileSync(cheminMetriques, JSON.stringify(this.metriques, null, 2));
    }

    async sauvegarderEtatSysteme() {
        try {
            const etatComplet = {
                timestamp: Date.now(),
                etat: this.etat,
                composants: {
                    memoire: this.composants.memoire,
                    curseur: this.composants.curseur
                },
                connexions: {
                    memoire_agent: this.connexionMemoireAgent,
                    agent_ollama: this.connexionAgentOllama,
                    curseur_systeme: this.connexionCurseurSysteme
                },
                metriques: this.metriques
            };

            const cheminEtat = path.join(this.config.chemin_memoire, 'surveillance-systeme', 'etat_systeme.json');
            fs.writeFileSync(cheminEtat, JSON.stringify(etatComplet, null, 2));

        } catch (error) {
            console.log(`⚠️ Erreur sauvegarde état: ${error.message}`);
        }
    }

    async gererErreurSysteme(error) {
        console.log(`🚨 Gestion erreur système: ${error.message}`);

        // Log erreur
        const logErreur = {
            timestamp: Date.now(),
            erreur: error.message,
            stack: error.stack,
            etat_systeme: this.etat
        };

        const cheminLog = path.join(this.config.chemin_memoire, 'logs-integration', `erreur_${Date.now()}.json`);
        fs.writeFileSync(cheminLog, JSON.stringify(logErreur, null, 2));

        // Tentative récupération
        await this.tentativeRecuperation();
    }

    async tentativeRecuperation() {
        console.log('🔄 Tentative récupération système...');

        try {
            // Réinitialiser composants
            await this.initialiserMemoireThermique();

            if (!this.etat.ollama_actif) {
                await this.demarrerOllamaPersistant();
            }

            console.log('✅ Récupération système réussie');
        } catch (error) {
            console.log(`❌ Échec récupération: ${error.message}`);
        }
    }

    // INTERACTION UNIFIÉE AVEC MÉMOIRE
    async interactionUnifiee(question) {
        console.log('\n🔒 INTERACTION UNIFIÉE VERROUILLÉE');
        console.log('==================================');
        console.log(`❓ Question: "${question}"`);

        try {
            // Vérifier état système
            if (!this.etat.integration_verrouillee) {
                throw new Error('Système non verrouillé');
            }

            // 1. Recherche mémoire verrouillée
            const souvenirs = await this.rechercherMemoireVerrouillee(question);

            // 2. Construire contexte unifié
            const contexte = this.construireContexteUnifie(question, souvenirs);

            // 3. Interaction Ollama verrouillée
            const reponse = await this.interactionOllamaVerrouillee(contexte);

            // 4. Stockage unifié
            await this.stockageUnifie(question, reponse, souvenirs);

            // 5. Mise à jour curseur verrouillé
            this.mettreAJourCurseurVerrouille(question, reponse);

            // 6. Mettre à jour connexions
            this.mettreAJourConnexions();

            console.log(`✅ Interaction unifiée réussie`);

            return {
                question: question,
                reponse: reponse,
                souvenirs_utilises: souvenirs.length,
                curseur_position: this.composants.curseur.position_actuelle,
                connexions_actives: this.etat.connexions_actives,
                verrouillage_actif: true
            };

        } catch (error) {
            console.log(`❌ Erreur interaction unifiée: ${error.message}`);
            await this.gererErreurInteraction(error);
            return null;
        }
    }

    async rechercherMemoireVerrouillee(question) {
        console.log('🔍 Recherche mémoire verrouillée...');

        try {
            const motsCles = this.extraireMotsCles(question);
            const souvenirs = [];

            const zones = ['zone1_70C', 'zone2_60C', 'zone3_50C', 'zone4_40C', 'zone5_30C', 'zone6_20C'];

            for (const zone of zones) {
                const cheminZone = path.join(this.config.chemin_memoire, 'zones-thermiques', zone);

                if (fs.existsSync(cheminZone)) {
                    const fichiers = fs.readdirSync(cheminZone).filter(f => f.endsWith('.json'));

                    for (const fichier of fichiers.slice(0, 3)) {
                        try {
                            const cheminFichier = path.join(cheminZone, fichier);
                            const souvenir = JSON.parse(fs.readFileSync(cheminFichier, 'utf8'));

                            const pertinence = this.calculerPertinence(souvenir, motsCles);

                            if (pertinence > 0.3) {
                                souvenirs.push({
                                    ...souvenir,
                                    pertinence: pertinence,
                                    zone_origine: zone
                                });
                            }
                        } catch (error) {
                            // Ignorer fichiers corrompus
                        }
                    }
                }
            }

            // Trier par pertinence
            souvenirs.sort((a, b) => b.pertinence - a.pertinence);

            // Mettre à jour connexion mémoire
            this.connexionMemoireAgent.derniere_interaction = Date.now();
            this.connexionMemoireAgent.requetes_traitees++;

            console.log(`📚 ${souvenirs.slice(0, 2).length} souvenirs pertinents trouvés`);

            return souvenirs.slice(0, 2); // Top 2 pour performance

        } catch (error) {
            console.log(`⚠️ Erreur recherche mémoire: ${error.message}`);
            this.connexionMemoireAgent.erreurs_detectees++;
            return [];
        }
    }

    extraireMotsCles(texte) {
        const mots = texte.toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(mot => mot.length > 2)
            .filter(mot => !['que', 'qui', 'quoi', 'comment', 'pourquoi', 'est', 'sont', 'une', 'des', 'les'].includes(mot));

        return [...new Set(mots)];
    }

    calculerPertinence(souvenir, motsCles) {
        const contenu = (souvenir.contenu || '').toLowerCase();
        let score = 0;

        motsCles.forEach(mot => {
            if (contenu.includes(mot)) {
                score += 1;
            }
        });

        return motsCles.length > 0 ? score / motsCles.length : 0;
    }

    construireContexteUnifie(question, souvenirs) {
        let contexte = '';

        // État système
        contexte += `[SYSTÈME UNIFIÉ VERROUILLÉ - Position curseur: ${this.composants.curseur.position_actuelle.toFixed(1)}°C]\n\n`;

        // Souvenirs
        if (souvenirs.length > 0) {
            contexte += `[MÉMOIRE THERMIQUE CONNECTÉE:]\n`;
            souvenirs.forEach((souvenir, index) => {
                contexte += `${index + 1}. [${souvenir.zone_origine}] ${souvenir.contenu}\n`;
            });
            contexte += `\n`;
        }

        // Question
        contexte += `Question: ${question}\n\n`;

        // Instructions
        if (souvenirs.length > 0) {
            contexte += `En utilisant ma mémoire thermique connectée, répondez de manière cohérente avec mon expérience.`;
        } else {
            contexte += `Répondez à cette question. Cette interaction sera stockée dans ma mémoire thermique unifiée.`;
        }

        return contexte;
    }

    async interactionOllamaVerrouillee(contexte) {
        console.log('🤖 Interaction Ollama verrouillée...');

        try {
            const debut = Date.now();

            const reponse = execSync(
                `${this.config.ollama_path} run ${this.config.model_name} "${contexte}"`,
                {
                    encoding: 'utf8',
                    timeout: 25000,
                    env: {
                        ...process.env,
                        OLLAMA_MODELS: this.config.models_path
                    }
                }
            );

            const duree = Date.now() - debut;

            // Mettre à jour connexion Ollama
            this.connexionAgentOllama.derniere_requete = Date.now();
            this.connexionAgentOllama.reponses_recues++;

            console.log(`🤖 Réponse Ollama (${duree}ms): "${reponse.trim().substring(0, 80)}..."`);

            return reponse.trim();

        } catch (error) {
            console.log(`❌ Erreur Ollama: ${error.message}`);
            this.connexionAgentOllama.timeouts_detectes++;
            throw error;
        }
    }

    async stockageUnifie(question, reponse, souvenirs) {
        console.log('💾 Stockage unifié...');

        try {
            const timestamp = Date.now();

            // Déterminer zone selon curseur
            const position = this.composants.curseur.position_actuelle;
            let zoneStockage = 'zone3'; // Défaut

            if (position >= 65) zoneStockage = 'zone1';
            else if (position >= 55) zoneStockage = 'zone2';
            else if (position >= 45) zoneStockage = 'zone3';
            else if (position >= 35) zoneStockage = 'zone4';
            else if (position >= 25) zoneStockage = 'zone5';
            else zoneStockage = 'zone6';

            const interaction = {
                id: `unifie_${timestamp}_${Math.random().toString(36).substr(2, 9)}`,
                type: 'interaction_unifiee_verrouillee',
                question: question,
                reponse: reponse,
                souvenirs_utilises: souvenirs.map(s => ({
                    id: s.id,
                    zone_origine: s.zone_origine,
                    pertinence: s.pertinence
                })),
                systeme_unifie: {
                    curseur_position: this.composants.curseur.position_actuelle,
                    zone_active: this.composants.curseur.zone_actuelle,
                    connexions_actives: this.etat.connexions_actives,
                    verrouillage_actif: true
                },
                zone_thermique: zoneStockage,
                temperature_actuelle: this.getTemperatureZone(zoneStockage),
                date_creation: timestamp
            };

            // Sauvegarder
            const cheminZone = path.join(this.config.chemin_memoire, 'zones-thermiques', `${zoneStockage}_${this.getTemperatureZone(zoneStockage)}C`);
            const cheminFichier = path.join(cheminZone, `${interaction.id}.json`);

            fs.writeFileSync(cheminFichier, JSON.stringify(interaction, null, 2));

            console.log(`💾 Stocké en ${zoneStockage} (${this.getTemperatureZone(zoneStockage)}°C)`);

        } catch (error) {
            console.log(`⚠️ Erreur stockage: ${error.message}`);
        }
    }

    getTemperatureZone(zone) {
        const temperatures = {
            'zone1': 70, 'zone2': 60, 'zone3': 50,
            'zone4': 40, 'zone5': 30, 'zone6': 20
        };
        return temperatures[zone] || 50;
    }

    mettreAJourCurseurVerrouille(question, reponse) {
        console.log('🌡️ Mise à jour curseur verrouillé...');

        try {
            // Calculer complexité
            const complexite = this.calculerComplexiteInteraction(question, reponse);

            // Ajuster position
            const ajustement = (complexite - 0.5) * 8; // -4 à +4
            const nouvellePosition = Math.max(20, Math.min(70,
                this.composants.curseur.position_actuelle + ajustement
            ));

            // Déterminer zone
            let nouvelleZone = 'zone3';
            if (nouvellePosition >= 65) nouvelleZone = 'zone1';
            else if (nouvellePosition >= 55) nouvelleZone = 'zone2';
            else if (nouvellePosition >= 45) nouvelleZone = 'zone3';
            else if (nouvellePosition >= 35) nouvelleZone = 'zone4';
            else if (nouvellePosition >= 25) nouvelleZone = 'zone5';
            else nouvelleZone = 'zone6';

            // Mettre à jour
            this.composants.curseur.position_actuelle = nouvellePosition;
            this.composants.curseur.zone_actuelle = nouvelleZone;
            this.composants.curseur.derniere_mise_a_jour = Date.now();

            // Ajouter à l'historique
            this.composants.curseur.historique_positions.push({
                position: nouvellePosition,
                zone: nouvelleZone,
                timestamp: Date.now(),
                complexite: complexite
            });

            // Garder 20 dernières positions
            if (this.composants.curseur.historique_positions.length > 20) {
                this.composants.curseur.historique_positions = this.composants.curseur.historique_positions.slice(-20);
            }

            // Sauvegarder curseur
            this.sauvegarderCurseur();

            console.log(`🌡️ Curseur: ${nouvellePosition.toFixed(1)}°C (${nouvelleZone})`);

        } catch (error) {
            console.log(`⚠️ Erreur curseur: ${error.message}`);
        }
    }

    calculerComplexiteInteraction(question, reponse) {
        let score = 0.5;

        // Longueur question
        if (question.length > 50) score += 0.1;

        // Longueur réponse
        if (reponse.length > 100) score += 0.1;
        if (reponse.length > 200) score += 0.1;

        // Mots complexes
        const motsComplexes = ['mémoire', 'intelligence', 'neurone', 'système', 'intégration'];
        const texte = (question + ' ' + reponse).toLowerCase();

        motsComplexes.forEach(mot => {
            if (texte.includes(mot)) score += 0.05;
        });

        return Math.max(0.1, Math.min(0.9, score));
    }

    sauvegarderCurseur() {
        try {
            const cheminCurseur = path.join(this.config.chemin_memoire, 'curseur-thermique', 'position_curseur.json');
            const data = {
                curseur: this.composants.curseur,
                derniere_mise_a_jour: Date.now(),
                version: '4.0_unifie_verrouille'
            };

            fs.writeFileSync(cheminCurseur, JSON.stringify(data, null, 2));
        } catch (error) {
            console.log(`⚠️ Erreur sauvegarde curseur: ${error.message}`);
        }
    }

    mettreAJourConnexions() {
        const maintenant = Date.now();

        this.connexionMemoireAgent.derniere_interaction = maintenant;
        this.connexionAgentOllama.derniere_requete = maintenant;
        this.connexionCurseurSysteme.derniere_mise_a_jour = maintenant;

        this.metriques.derniere_interaction = maintenant;
    }

    async gererErreurInteraction(error) {
        console.log(`🚨 Gestion erreur interaction: ${error.message}`);

        // Tentative récupération automatique
        if (error.message.includes('Ollama')) {
            await this.gererDeconnexionOllama();
        } else if (error.message.includes('mémoire')) {
            await this.gererDeconnexionMemoire();
        }
    }

    // TESTS SYSTÈME UNIFIÉ
    async testerSystemeUnifie() {
        console.log('\n🧪 TESTS SYSTÈME UNIFIÉ VERROUILLÉ');
        console.log('===================================');

        const tests = [
            "Qu'est-ce qu'un système unifié?",
            "Comment fonctionne la mémoire thermique?",
            "Peux-tu me rappeler nos discussions précédentes?"
        ];

        const resultats = [];

        for (let i = 0; i < tests.length; i++) {
            const question = tests[i];
            console.log(`\n📝 Test ${i + 1}/${tests.length}: ${question}`);

            const resultat = await this.interactionUnifiee(question);

            if (resultat) {
                resultats.push(resultat);
                console.log(`✅ Test ${i + 1} réussi`);
                console.log(`   Souvenirs: ${resultat.souvenirs_utilises}`);
                console.log(`   Curseur: ${resultat.curseur_position.toFixed(1)}°C`);
                console.log(`   Connexions: ${resultat.connexions_actives}`);

                // Pause entre tests
                await new Promise(resolve => setTimeout(resolve, 3000));
            } else {
                console.log(`❌ Test ${i + 1} échoué`);
            }
        }

        return resultats;
    }

    // ARRÊT SÉCURISÉ
    async arreterSystemeSecurise() {
        console.log('\n⏹️ Arrêt sécurisé système unifié...');

        // Arrêter surveillance
        if (this.composants.surveillance) {
            clearInterval(this.composants.surveillance);
            console.log('👁️ Surveillance arrêtée');
        }

        // Arrêter heartbeat
        if (this.composants.heartbeat) {
            clearInterval(this.composants.heartbeat);
            console.log('💓 Heartbeat arrêté');
        }

        // Sauvegarder état final
        await this.sauvegarderEtatSysteme();

        // Arrêter Ollama
        if (this.composants.ollama_process) {
            this.composants.ollama_process.kill();
            console.log('🤖 Ollama arrêté');
        }

        console.log('✅ Système unifié arrêté proprement');
    }

    // AFFICHER ÉTAT COMPLET
    afficherEtatComplet() {
        console.log('\n📊 ÉTAT SYSTÈME UNIFIÉ VERROUILLÉ');
        console.log('==================================');

        console.log(`🔒 Intégration verrouillée: ${this.etat.integration_verrouillee ? 'OUI ✅' : 'NON ❌'}`);
        console.log(`🧠 Mémoire connectée: ${this.etat.memoire_connectee ? 'OUI ✅' : 'NON ❌'}`);
        console.log(`🤖 Agent connecté: ${this.etat.agent_connecte ? 'OUI ✅' : 'NON ❌'}`);
        console.log(`⚡ Ollama actif: ${this.etat.ollama_actif ? 'OUI ✅' : 'NON ❌'}`);
        console.log(`🔗 Connexions actives: ${this.etat.connexions_actives}/3`);

        console.log(`\n🌡️ CURSEUR THERMIQUE:`);
        console.log(`   Position: ${this.composants.curseur.position_actuelle.toFixed(1)}°C`);
        console.log(`   Zone: ${this.composants.curseur.zone_actuelle}`);
        console.log(`   Verrouillage: ${this.composants.curseur.verrouillage_actif ? 'ACTIF ✅' : 'INACTIF ❌'}`);

        console.log(`\n📈 MÉTRIQUES:`);
        console.log(`   Uptime: ${((Date.now() - this.metriques.temps_uptime) / 1000 / 60).toFixed(1)} minutes`);
        console.log(`   Déconnexions: ${this.metriques.deconnexions_detectees}`);
        console.log(`   Reconnexions: ${this.metriques.tentatives_reconnexion}`);
    }
}

// Export
module.exports = SystemeUnifieVerrouille;

// Test automatique si exécuté directement
if (require.main === module) {
    console.log('🚀 LANCEMENT SYSTÈME UNIFIÉ VERROUILLÉ');
    console.log('======================================');

    const systeme = new SystemeUnifieVerrouille();

    // Attendre initialisation puis tester
    setTimeout(async () => {
        try {
            systeme.afficherEtatComplet();

            if (systeme.etat.integration_verrouillee) {
                console.log('\n✅ SYSTÈME VERROUILLÉ - LANCEMENT TESTS');

                const resultats = await systeme.testerSystemeUnifie();

                console.log(`\n🎉 TESTS TERMINÉS: ${resultats.length} interactions réussies`);
                systeme.afficherEtatComplet();

                // Arrêt après 30 secondes
                setTimeout(() => {
                    systeme.arreterSystemeSecurise();
                    process.exit(0);
                }, 30000);

            } else {
                console.log('\n❌ SYSTÈME NON VERROUILLÉ');
            }

        } catch (error) {
            console.error('❌ Erreur tests:', error.message);
        }
    }, 5000); // Attendre 5 secondes pour initialisation
}
