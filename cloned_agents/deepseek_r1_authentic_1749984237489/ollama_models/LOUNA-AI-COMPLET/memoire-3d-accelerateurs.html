<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 LOUNA-AI - Mémoire 3D & Accélérateurs</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a, #1a1a2e, #16213e, #0f3460);
            color: #ffffff;
            overflow-x: hidden;
            min-height: 100vh;
            animation: backgroundShift 20s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% { background: linear-gradient(135deg, #0a0a0a, #1a1a2e, #16213e, #0f3460); }
            25% { background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460, #533483); }
            50% { background: linear-gradient(135deg, #16213e, #0f3460, #533483, #7209b7); }
            75% { background: linear-gradient(135deg, #0f3460, #533483, #7209b7, #a663cc); }
        }

        .header {
            background: rgba(255, 20, 147, 0.2);
            padding: 20px;
            text-align: center;
            border-bottom: 2px solid #ff1493;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 2.5em;
            color: #ff69b4;
            text-shadow: 0 0 20px #ff1493;
            margin-bottom: 10px;
        }

        .nav-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
        }

        .nav-btn {
            background: linear-gradient(45deg, #ff1493, #ff69b4);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .nav-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 0 20px #ff1493;
        }

        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .panel {
            background: rgba(255, 20, 147, 0.1);
            border: 2px solid #ff1493;
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }

        .panel h2 {
            color: #ff69b4;
            margin-bottom: 15px;
            text-align: center;
            font-size: 1.8em;
        }

        .memory-3d {
            height: 500px;
            background:
                radial-gradient(circle at 20% 30%, rgba(0, 255, 255, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(255, 0, 255, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(0, 255, 0, 0.2) 0%, transparent 70%),
                linear-gradient(135deg, #0a0a0a, #1a1a2e, #16213e);
            border: 3px solid;
            border-image: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00, #00ff00) 1;
            border-radius: 15px;
            position: relative;
            overflow: hidden;
            box-shadow:
                0 0 50px rgba(0, 255, 255, 0.3),
                inset 0 0 50px rgba(255, 0, 255, 0.2);
        }

        .memory-node {
            position: absolute;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            animation: memoryPulse 3s infinite ease-in-out;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .memory-node:nth-child(odd) {
            background: radial-gradient(circle, #00ffff, #0080ff);
            box-shadow:
                0 0 20px #00ffff,
                0 0 40px rgba(0, 255, 255, 0.5),
                inset 0 0 10px rgba(255, 255, 255, 0.3);
        }

        .memory-node:nth-child(even) {
            background: radial-gradient(circle, #ff00ff, #ff0080);
            box-shadow:
                0 0 20px #ff00ff,
                0 0 40px rgba(255, 0, 255, 0.5),
                inset 0 0 10px rgba(255, 255, 255, 0.3);
        }

        .memory-node:hover {
            transform: scale(2) !important;
            z-index: 100;
            box-shadow:
                0 0 50px currentColor,
                0 0 100px rgba(255, 255, 255, 0.8);
        }

        @keyframes memoryPulse {
            0%, 100% {
                transform: scale(1) translateZ(0);
                opacity: 0.7;
            }
            25% {
                transform: scale(1.3) translateZ(10px);
                opacity: 1;
            }
            50% {
                transform: scale(0.8) translateZ(-5px);
                opacity: 0.9;
            }
            75% {
                transform: scale(1.5) translateZ(15px);
                opacity: 1;
            }
        }

        .memory-connection {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, #ff1493, #ff69b4);
            opacity: 0.6;
            animation: flow 3s infinite;
        }

        @keyframes flow {
            0% { opacity: 0.3; }
            50% { opacity: 1; }
            100% { opacity: 0.3; }
        }

        .accelerator-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .accelerator-card {
            background: rgba(255, 105, 180, 0.1);
            border: 1px solid #ff69b4;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .accelerator-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(255, 20, 147, 0.3);
        }

        .accelerator-card.active {
            background: rgba(0, 255, 0, 0.2);
            border-color: #00ff00;
            box-shadow: 0 0 15px #00ff00;
        }

        .accelerator-name {
            font-weight: bold;
            color: #ff69b4;
            margin-bottom: 5px;
        }

        .accelerator-value {
            font-size: 1.2em;
            color: #ffffff;
        }

        .accelerator-status {
            margin-top: 5px;
            padding: 3px 8px;
            border-radius: 15px;
            font-size: 0.8em;
        }

        .status-active {
            background: #00ff00;
            color: #000;
        }

        .status-inactive {
            background: #666;
            color: #fff;
        }

        .stats-panel {
            grid-column: 1 / -1;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .stat-item {
            background: rgba(255, 20, 147, 0.2);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #ff1493;
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #ff69b4;
            text-shadow: 0 0 10px #ff1493;
        }

        .stat-label {
            color: #ffffff;
            margin-top: 5px;
        }

        .brain-visualization {
            height: 300px;
            background: radial-gradient(ellipse, #ff1493, #330066);
            border-radius: 50%;
            position: relative;
            margin: 20px auto;
            animation: brainPulse 4s infinite;
        }

        @keyframes brainPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .brain-activity {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: radial-gradient(circle, transparent 40%, rgba(255, 105, 180, 0.3) 70%);
            animation: rotate 10s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 LOUNA-AI - Mémoire 3D & Accélérateurs</h1>
        <div class="nav-buttons">
            <button class="nav-btn" onclick="window.location.href='interface-louna-complete.html'">🏠 Accueil</button>
            <button class="nav-btn" onclick="window.open('interface-video-ltx.html', '_blank')">🎬 Générateur Vidéo</button>
            <button class="nav-btn" onclick="window.open('configuration.html', '_blank')">⚙️ Configuration</button>
            <button class="nav-btn" onclick="refreshData()">🔄 Actualiser</button>
        </div>
    </div>

    <div class="container">
        <!-- Visualisation Mémoire 3D -->
        <div class="panel">
            <h2>🧠 Mémoire Thermique 3D</h2>
            <div class="memory-3d" id="memory3d">
                <!-- Les nœuds de mémoire seront générés dynamiquement -->
            </div>
            <div class="brain-visualization">
                <div class="brain-activity"></div>
            </div>
        </div>

        <!-- Accélérateurs Kyber -->
        <div class="panel">
            <h2>⚡ Accélérateurs Kyber</h2>
            <div class="accelerator-grid" id="acceleratorGrid">
                <!-- Les accélérateurs seront chargés dynamiquement -->
            </div>
        </div>

        <!-- Statistiques Système -->
        <div class="stats-panel panel">
            <h2>📊 Statistiques Système</h2>
            <div class="stat-item">
                <div class="stat-value" id="qiValue">369</div>
                <div class="stat-label">QI Actuel</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="memoryCount">104</div>
                <div class="stat-label">Mémoires</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="tempValue">67.5°C</div>
                <div class="stat-label">Température</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="acceleratorCount">10</div>
                <div class="stat-label">Accélérateurs Actifs</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="zoneValue">Zone 5</div>
                <div class="stat-label">Zone Active</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="healthValue">100%</div>
                <div class="stat-label">Santé Système</div>
            </div>
        </div>
    </div>

    <script>
        // GÉNÉRATION MÉMOIRE 3D
        function generateMemory3D() {
            const container = document.getElementById('memory3d');
            container.innerHTML = '';
            
            // Générer des nœuds de mémoire
            for (let i = 0; i < 25; i++) {
                const node = document.createElement('div');
                node.className = 'memory-node';
                node.style.left = Math.random() * 90 + '%';
                node.style.top = Math.random() * 90 + '%';
                node.style.animationDelay = Math.random() * 2 + 's';
                container.appendChild(node);
                
                // Ajouter des connexions
                if (i > 0 && Math.random() > 0.7) {
                    const connection = document.createElement('div');
                    connection.className = 'memory-connection';
                    connection.style.left = Math.random() * 80 + '%';
                    connection.style.top = Math.random() * 80 + '%';
                    connection.style.width = Math.random() * 100 + 50 + 'px';
                    connection.style.transform = `rotate(${Math.random() * 360}deg)`;
                    container.appendChild(connection);
                }
            }
        }

        // CHARGEMENT DES ACCÉLÉRATEURS KYBER
        function loadAccelerators() {
            fetch('http://localhost:3000/api/accelerateurs-kyber')
                .then(response => response.json())
                .then(data => {
                    const grid = document.getElementById('acceleratorGrid');
                    grid.innerHTML = '';

                    if (data.success && data.accelerateurs) {
                        data.accelerateurs.forEach(acc => {
                            const card = document.createElement('div');
                            card.className = `accelerator-card ${acc.actif ? 'active' : ''}`;
                            card.innerHTML = `
                                <div class="accelerator-name">${acc.id}</div>
                                <div class="accelerator-type">${acc.type}</div>
                                <div class="accelerator-value">${acc.performance}%</div>
                                <div class="accelerator-temp">${acc.temperature}°C</div>
                                <div class="accelerator-status ${acc.actif ? 'status-active' : 'status-inactive'}">
                                    ${acc.actif ? 'ACTIF' : 'INACTIF'}
                                </div>
                            `;

                            // Ajouter couleur selon le type
                            card.style.borderColor = acc.couleur;
                            card.style.boxShadow = `0 0 10px ${acc.couleur}50`;

                            grid.appendChild(card);
                        });

                        // Mettre à jour le compteur
                        document.getElementById('acceleratorCount').textContent = data.actifs + '/' + data.total;
                    } else {
                        // Afficher message si pas d'accélérateurs
                        grid.innerHTML = '<div style="color: #ffff00; text-align: center; padding: 20px;">🔄 Chargement des accélérateurs Kyber...</div>';
                    }
                })
                .catch(error => {
                    console.error('Erreur chargement accélérateurs:', error);
                    const grid = document.getElementById('acceleratorGrid');
                    grid.innerHTML = '<div style="color: #ff6666; text-align: center; padding: 20px;">❌ Erreur connexion accélérateurs</div>';
                });
        }

        // MISE À JOUR DES STATS
        function updateStats() {
            fetch('http://localhost:3000/api/stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('qiValue').textContent = data.qi_actuel || data.coefficient_intellectuel || '369';
                        document.getElementById('memoryCount').textContent = data.memoires || '104';
                        document.getElementById('tempValue').textContent = (data.temperature || 67.5).toFixed(1) + '°C';
                        document.getElementById('zoneValue').textContent = 'Zone ' + (data.zone_active || '1');

                        // Mise à jour avec animation
                        animateStatUpdate('qiValue');
                        animateStatUpdate('memoryCount');
                        animateStatUpdate('tempValue');
                    }
                })
                .catch(error => {
                    console.error('Erreur stats:', error);
                    // Valeurs par défaut en cas d'erreur
                    document.getElementById('qiValue').textContent = '369';
                    document.getElementById('memoryCount').textContent = '104';
                    document.getElementById('tempValue').textContent = '67.5°C';
                });
        }

        // Animation de mise à jour des stats
        function animateStatUpdate(elementId) {
            const element = document.getElementById(elementId);
            element.style.transform = 'scale(1.2)';
            element.style.color = '#00ffff';
            element.style.textShadow = '0 0 20px #00ffff';

            setTimeout(() => {
                element.style.transform = 'scale(1)';
                element.style.color = '#ff69b4';
                element.style.textShadow = '0 0 10px #ff1493';
            }, 500);
        }

        // ACTUALISATION COMPLÈTE
        function refreshData() {
            generateMemory3D();
            loadAccelerators();
            updateStats();
        }

        // INITIALISATION
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
            
            // Mise à jour automatique toutes les 5 secondes
            setInterval(updateStats, 5000);
            setInterval(generateMemory3D, 10000); // Régénérer la mémoire 3D toutes les 10s
        });
    </script>
</body>
</html>
