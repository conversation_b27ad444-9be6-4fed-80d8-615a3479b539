#!/bin/bash

# 🚀 SCRIPT DE LANCEMENT LOUNA-AI APPLICATION COMPLÈTE
# ===================================================

echo "🚀 LANCEMENT LOUNA-AI APPLICATION COMPLÈTE"
echo "=========================================="

# Vérifier si nous sommes dans le bon répertoire
if [ ! -f "LOUNA-AI-APP.js" ]; then
    echo "❌ Erreur: LOUNA-AI-APP.js non trouvé"
    echo "📁 Assurez-vous d'être dans le répertoire LOUNA-AI-COMPLET"
    exit 1
fi

# Vérifier si la configuration est verrouillée
if [ ! -f "CONFIGURATION-VERROUILLEE-FINALE.md" ]; then
    echo "⚠️  Configuration finale non trouvée"
    echo "🔒 Exécution de la sauvegarde de configuration..."
    ./sauvegarder-configuration-finale.sh
fi

# Vérifier Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js n'est pas installé"
    echo "📥 Installez Node.js depuis https://nodejs.org/"
    exit 1
fi

# Vérifier npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm n'est pas installé"
    exit 1
fi

echo "✅ Node.js version: $(node --version)"
echo "✅ npm version: $(npm --version)"

# Installer les dépendances si nécessaire
if [ ! -d "node_modules" ] || [ ! -f "node_modules/.package-lock.json" ]; then
    echo "📦 Installation des dépendances..."
    
    # Copier le package.json de l'app
    if [ -f "package-app.json" ]; then
        cp package-app.json package.json
        echo "✅ package.json configuré pour l'application"
    fi
    
    npm install
    
    if [ $? -ne 0 ]; then
        echo "❌ Erreur lors de l'installation des dépendances"
        exit 1
    fi
    
    echo "✅ Dépendances installées"
fi

# Vérifier Electron
if [ ! -d "node_modules/electron" ]; then
    echo "📦 Installation d'Electron..."
    npm install electron --save-dev
fi

# Créer le dossier assets s'il n'existe pas
if [ ! -d "assets" ]; then
    echo "📁 Création du dossier assets..."
    mkdir -p assets
    
    # Créer une icône simple (placeholder)
    echo "🎨 Création d'icône placeholder..."
    # Note: En production, remplacer par de vraies icônes
    touch assets/louna-icon.png
    touch assets/icon.icns
    touch assets/icon.ico
fi

# Vérifier que le serveur peut démarrer
echo "🔍 Vérification du serveur LOUNA-AI..."
timeout 5s node -e "
const ServeurInterfaceComplete = require('./serveur-interface-complete.js');
console.log('✅ Serveur LOUNA-AI validé');
process.exit(0);
" 2>/dev/null

if [ $? -ne 0 ]; then
    echo "❌ Erreur: Le serveur LOUNA-AI ne peut pas démarrer"
    echo "🔧 Vérifiez la configuration dans serveur-interface-complete.js"
    exit 1
fi

echo "✅ Serveur LOUNA-AI validé"

# Lancer l'application
echo ""
echo "🚀 LANCEMENT DE L'APPLICATION LOUNA-AI"
echo "======================================"

# Définir l'environnement
export NODE_ENV=production

# Lancer avec Electron
if command -v npx &> /dev/null; then
    echo "🔄 Lancement avec npx electron..."
    npx electron LOUNA-AI-APP.js
else
    echo "🔄 Lancement avec node..."
    ./node_modules/.bin/electron LOUNA-AI-APP.js
fi

# Vérifier le code de sortie
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ LOUNA-AI fermée normalement"
else
    echo ""
    echo "⚠️  LOUNA-AI fermée avec erreur"
    echo "📋 Consultez les logs ci-dessus pour plus d'informations"
fi

echo ""
echo "🎯 LOUNA-AI APPLICATION COMPLÈTE"
echo "Merci d'avoir utilisé LOUNA-AI !"
