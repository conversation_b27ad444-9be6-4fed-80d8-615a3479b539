/**
 * SERVEUR LOUNA-AI ULTRA-STABLE
 * Correction de tous les bugs détectés
 */

const express = require('express');
const path = require('path');
const { MoteurRaisonnementReel } = require('./moteur-raisonnement-reel');
const { MemoireThermiqueGlissante } = require('./memoire-thermique-glissante');
const { AutoEvaluation } = require('./auto-evaluation');
const { MiseAJourTempsReel } = require('./mise-a-jour-temps-reel');
const { MemoireConversationnelle } = require('./memoire-conversationnelle');

const app = express();
const PORT = 8080;

// INITIALISATION AVEC GESTION D'ERREURS
let moteurRaisonnement = null;
let memoireThermique = null;
let autoEvaluation = null;
let miseAJourTempsReel = null;
let memoireConversationnelle = null;

// Middleware avec gestion d'erreurs
app.use(express.json({ limit: '10mb' }));
app.use(express.static(__dirname));

// Gestion globale des erreurs
process.on('uncaughtException', (error) => {
    console.error('❌ Erreur non capturée:', error);
    // Ne pas arrêter le serveur
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Promesse rejetée:', reason);
    // Ne pas arrêter le serveur
});

console.log('🚀 INITIALISATION LOUNA-AI ULTRA-STABLE');
console.log('=====================================');

// Initialisation sécurisée
function initialiserComposant(nom, ClasseComposant, ...args) {
    try {
        const instance = new ClasseComposant(...args);
        console.log(`✅ ${nom} initialisé`);
        return instance;
    } catch (error) {
        console.error(`❌ Erreur ${nom}:`, error.message);
        return null;
    }
}

moteurRaisonnement = initialiserComposant('Moteur de raisonnement', MoteurRaisonnementReel);
memoireThermique = initialiserComposant('Mémoire thermique', MemoireThermiqueGlissante);
autoEvaluation = initialiserComposant('Auto-évaluation', AutoEvaluation);
miseAJourTempsReel = initialiserComposant('Mise à jour temps réel', MiseAJourTempsReel, memoireThermique);
memoireConversationnelle = initialiserComposant('Mémoire conversationnelle', MemoireConversationnelle);

// ROUTE INTERFACE PRINCIPALE
app.get('/', (req, res) => {
    try {
        res.sendFile(path.join(__dirname, 'interface-louna-grande.html'));
    } catch (error) {
        console.error('❌ Erreur interface:', error);
        res.status(500).send('Erreur chargement interface');
    }
});

// ROUTE CHAT ULTRA-STABLE
app.post('/chat', async (req, res) => {
    try {
        const { message } = req.body;
        
        if (!message) {
            return res.json({
                success: false,
                error: 'Message requis'
            });
        }

        console.log(`💬 Question reçue: "${message}"`);

        // ÉTAPE 1: CONTEXTE CONVERSATIONNEL (SÉCURISÉ)
        let contexteConversation = null;
        try {
            if (memoireConversationnelle) {
                contexteConversation = memoireConversationnelle.obtenirContexte(message);
                console.log(`📋 Contexte:`, {
                    sujet: contexteConversation?.sujet_principal || 'aucun',
                    messages: contexteConversation?.nombre_messages || 0
                });
            }
        } catch (error) {
            console.error('❌ Erreur contexte:', error.message);
            contexteConversation = null;
        }

        // ÉTAPE 2: HISTORIQUE (SÉCURISÉ)
        let resultatsHistorique = [];
        try {
            if (memoireConversationnelle) {
                resultatsHistorique = memoireConversationnelle.rechercherDansHistorique(message, 3);
                console.log(`🔍 Historique: ${resultatsHistorique.length} résultats`);
            }
        } catch (error) {
            console.error('❌ Erreur historique:', error.message);
            resultatsHistorique = [];
        }

        // ÉTAPE 3: RAISONNEMENT (SÉCURISÉ)
        let resultatRaisonnement = null;
        try {
            if (moteurRaisonnement) {
                resultatRaisonnement = moteurRaisonnement.penser(message);
                console.log(`🧠 Raisonnement:`, resultatRaisonnement?.source || 'aucun');
            }
        } catch (error) {
            console.error('❌ Erreur raisonnement:', error.message);
            resultatRaisonnement = null;
        }

        // ÉTAPE 4: MÉMOIRE THERMIQUE (SÉCURISÉ)
        let resultatsMemoire = [];
        try {
            if (memoireThermique) {
                resultatsMemoire = memoireThermique.rechercher(message, 3);
                console.log(`💾 Mémoire: ${resultatsMemoire.length} résultats`);
            }
        } catch (error) {
            console.error('❌ Erreur mémoire:', error.message);
            resultatsMemoire = [];
        }

        // ÉTAPE 5: DÉCISION DE RÉPONSE (LOGIQUE AMÉLIORÉE)
        let reponseFinale = null;
        let source = null;
        let sourceComplete = null;

        // Priorité 1: Raisonnement interne (le plus fiable)
        if (resultatRaisonnement && resultatRaisonnement.reponse !== null && resultatRaisonnement.reponse !== undefined) {
            reponseFinale = resultatRaisonnement.reponse;
            source = 'Raisonnement interne';
            sourceComplete = 'raisonnement_interne';
            console.log(`✅ Réponse par raisonnement interne`);
        }
        // Priorité 2: Historique conversationnel pertinent
        else if (resultatsHistorique.length > 0 && resultatsHistorique[0].pertinence > 0.7) {
            const messageHistorique = resultatsHistorique[0].message;
            reponseFinale = `Basé sur notre conversation: ${String(messageHistorique.reponse)}`;
            source = `Historique conversation (message ${messageHistorique.numero})`;
            sourceComplete = 'historique_conversation';
            console.log(`✅ Réponse par historique conversationnel`);
        }
        // Priorité 3: Mémoire thermique
        else if (resultatsMemoire.length > 0 && resultatsMemoire[0].pertinence > 0.3) {
            reponseFinale = resultatsMemoire[0].contenu;
            source = `Mémoire thermique (${resultatsMemoire[0].source})`;
            sourceComplete = 'memoire_thermique';
            console.log(`✅ Réponse par mémoire thermique`);
        }
        // Priorité 4: Réponse contextuelle
        else if (contexteConversation && contexteConversation.sujet_principal === 'programmation') {
            reponseFinale = "Je comprends que nous travaillons sur un projet de programmation. Pouvez-vous me donner plus de détails ?";
            source = 'Réponse contextuelle programmation';
            sourceComplete = 'contexte_programmation';
            console.log(`✅ Réponse contextuelle programmation`);
        }
        // Défaut
        else {
            reponseFinale = "Je ne trouve pas d'information pertinente pour répondre à cette question.";
            source = 'Réponse par défaut';
            sourceComplete = 'defaut';
            console.log(`❌ Aucune réponse trouvée`);
        }

        // CALCUL QI SÉCURISÉ
        let qiCalcule = 127; // Base
        
        try {
            if (moteurRaisonnement) {
                const stats = moteurRaisonnement.getStatistiquesReelles();
                qiCalcule += Math.min(stats.connaissances_base * 2, 40);
            }
            
            if (memoireThermique) {
                const statsMemoire = memoireThermique.getStatistiquesGlissantes();
                qiCalcule += Math.min(statsMemoire.totalEntries, 30);
            }

            // Bonus pour contexte
            if (contexteConversation) {
                qiCalcule += Math.min(contexteConversation.nombre_messages * 0.5, 20);
                qiCalcule += (contexteConversation.classes_definies?.length || 0) * 2;
                qiCalcule += (contexteConversation.etapes_projet?.length || 0) * 3;
            }

            // Bonus pour type de réponse
            switch (sourceComplete) {
                case 'raisonnement_interne': qiCalcule += 15; break;
                case 'historique_conversation': qiCalcule += 12; break;
                case 'memoire_thermique': qiCalcule += 8; break;
                case 'contexte_programmation': qiCalcule += 10; break;
            }
        } catch (error) {
            console.error('❌ Erreur calcul QI:', error.message);
        }

        console.log(`🧠 QI calculé: ${qiCalcule}`);

        // MISE À JOUR TEMPS RÉEL (SÉCURISÉ)
        try {
            if (miseAJourTempsReel && sourceComplete !== 'defaut') {
                miseAJourTempsReel.renforcerMemoire(message, reponseFinale, source);
            }
        } catch (error) {
            console.error('❌ Erreur mise à jour:', error.message);
        }

        // ENREGISTREMENT CONVERSATIONNEL (SÉCURISÉ)
        try {
            if (memoireConversationnelle) {
                memoireConversationnelle.ajouterMessage(message, reponseFinale, source, {
                    qi: qiCalcule,
                    source_complete: sourceComplete,
                    contexte_utilise: contexteConversation !== null
                });
            }
        } catch (error) {
            console.error('❌ Erreur enregistrement conversation:', error.message);
        }

        // AUTO-ÉVALUATION (SÉCURISÉ)
        let evaluation = null;
        try {
            if (autoEvaluation) {
                evaluation = autoEvaluation.enregistrerInteraction(message, reponseFinale, source, qiCalcule);
                if (evaluation) {
                    console.log(`📊 Auto-évaluation déclenchée`);
                }
            }
        } catch (error) {
            console.error('❌ Erreur auto-évaluation:', error.message);
        }

        // RÉPONSE FINALE
        const response = {
            success: true,
            response: reponseFinale,
            source: source,
            qi_actuel: qiCalcule,
            memory_used: resultatsMemoire.length > 0,
            reasoning_used: resultatRaisonnement && resultatRaisonnement.reponse !== null,
            conversation_context: contexteConversation !== null,
            history_used: resultatsHistorique.length > 0,
            timestamp: Date.now()
        };

        if (evaluation) {
            response.auto_evaluation = evaluation;
        }

        res.json(response);

    } catch (error) {
        console.error('❌ Erreur critique chat:', error);
        res.json({
            success: false,
            error: 'Erreur interne du serveur',
            details: error.message,
            timestamp: Date.now()
        });
    }
});

// ROUTE STATISTIQUES SÉCURISÉE
app.get('/stats', (req, res) => {
    try {
        let stats = {
            success: true,
            stats: {},
            timestamp: Date.now()
        };

        if (moteurRaisonnement) {
            try {
                stats.stats.moteur_raisonnement = moteurRaisonnement.getStatistiquesReelles();
            } catch (error) {
                console.error('❌ Erreur stats moteur:', error.message);
                stats.stats.moteur_raisonnement = { erreur: error.message };
            }
        }

        if (memoireThermique) {
            try {
                stats.stats.memoire_thermique = memoireThermique.getStatistiquesGlissantes();
            } catch (error) {
                console.error('❌ Erreur stats mémoire:', error.message);
                stats.stats.memoire_thermique = { erreur: error.message };
            }
        }

        if (autoEvaluation) {
            try {
                stats.stats.auto_evaluation = autoEvaluation.getStats();
            } catch (error) {
                console.error('❌ Erreur stats évaluation:', error.message);
                stats.stats.auto_evaluation = { erreur: error.message };
            }
        }

        if (memoireConversationnelle) {
            try {
                stats.stats.memoire_conversationnelle = memoireConversationnelle.getStats();
            } catch (error) {
                console.error('❌ Erreur stats conversation:', error.message);
                stats.stats.memoire_conversationnelle = { erreur: error.message };
            }
        }

        // Calcul QI sécurisé
        let qi = 127;
        try {
            if (moteurRaisonnement && stats.stats.moteur_raisonnement.connaissances_base) {
                qi += Math.min(stats.stats.moteur_raisonnement.connaissances_base * 2, 40);
            }
            if (memoireThermique && stats.stats.memoire_thermique.totalEntries) {
                qi += Math.min(stats.stats.memoire_thermique.totalEntries, 30);
            }
            if (memoireConversationnelle && stats.stats.memoire_conversationnelle.nombre_messages) {
                qi += Math.min(stats.stats.memoire_conversationnelle.nombre_messages * 0.5, 20);
            }
        } catch (error) {
            console.error('❌ Erreur calcul QI stats:', error.message);
        }

        stats.coefficient_intellectuel = qi;
        res.json(stats);

    } catch (error) {
        console.error('❌ Erreur stats globales:', error);
        res.json({
            success: false,
            error: 'Erreur récupération statistiques',
            details: error.message,
            timestamp: Date.now()
        });
    }
});

// ROUTE FORMATION SÉCURISÉE
app.post('/formation', (req, res) => {
    try {
        const { sujet, contenu } = req.body;
        
        if (!sujet || !contenu) {
            return res.json({
                success: false,
                error: 'Sujet et contenu requis'
            });
        }

        console.log(`🎓 Formation: ${sujet}`);

        let memoireId = null;
        let connaissanceId = null;

        try {
            if (memoireThermique) {
                memoireId = memoireThermique.stocker(contenu, `Formation: ${sujet}`, 0.95);
            }
        } catch (error) {
            console.error('❌ Erreur stockage mémoire formation:', error.message);
        }

        try {
            if (moteurRaisonnement) {
                connaissanceId = moteurRaisonnement.apprendreNouvelleFait(contenu, `Formation: ${sujet}`);
            }
        } catch (error) {
            console.error('❌ Erreur apprentissage formation:', error.message);
        }

        res.json({
            success: true,
            message: `Formation "${sujet}" intégrée`,
            memoire_id: memoireId,
            connaissance_id: connaissanceId
        });

    } catch (error) {
        console.error('❌ Erreur formation:', error);
        res.json({
            success: false,
            error: 'Erreur pendant la formation',
            details: error.message
        });
    }
});

// MAINTENANCE AUTOMATIQUE SÉCURISÉE
setInterval(() => {
    try {
        if (memoireThermique) {
            memoireThermique.maintenance();
        }
    } catch (error) {
        console.error('❌ Erreur maintenance mémoire:', error.message);
    }

    try {
        if (miseAJourTempsReel) {
            miseAJourTempsReel.optimiserOrganisation();
        }
    } catch (error) {
        console.error('❌ Erreur maintenance mise à jour:', error.message);
    }
}, 10 * 60 * 1000); // Toutes les 10 minutes

// DÉMARRAGE SERVEUR ULTRA-STABLE
const server = app.listen(PORT, () => {
    console.log('');
    console.log('✅ LOUNA-AI ULTRA-STABLE OPÉRATIONNEL');
    console.log('====================================');
    console.log(`🔗 Interface: http://localhost:${PORT}`);
    console.log(`🧠 Moteur raisonnement: ${moteurRaisonnement ? 'STABLE' : 'INACTIF'}`);
    console.log(`💾 Mémoire thermique: ${memoireThermique ? 'STABLE' : 'INACTIVE'}`);
    console.log(`🔍 Auto-évaluation: ${autoEvaluation ? 'STABLE' : 'INACTIVE'}`);
    console.log(`⚡ Mise à jour temps réel: ${miseAJourTempsReel ? 'STABLE' : 'INACTIVE'}`);
    console.log(`💬 Mémoire conversationnelle: ${memoireConversationnelle ? 'STABLE' : 'INACTIVE'}`);
    console.log('');
});

// Gestion propre de l'arrêt
process.on('SIGTERM', () => {
    console.log('🔄 Arrêt propre du serveur...');
    server.close(() => {
        console.log('✅ Serveur arrêté proprement');
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log('🔄 Arrêt propre du serveur...');
    server.close(() => {
        console.log('✅ Serveur arrêté proprement');
        process.exit(0);
    });
});
