<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LOUNA-AI - Interface Grande</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, 
                #000000 0%, 
                #1a0a1a 25%, 
                #2d1b2d 50%, 
                #4a2c4a 75%, 
                #663d66 100%);
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }

        /* BARRE D'INFORMATIONS EN HAUT */
        .info-bar {
            background: rgba(0, 0, 0, 0.8);
            border-bottom: 2px solid #ff69b4;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
            backdrop-filter: blur(10px);
        }

        .info-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 120px;
        }

        .info-title {
            font-size: 12px;
            color: #ff69b4;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .info-value {
            font-size: 18px;
            font-weight: bold;
            color: #ffffff;
            text-shadow: 0 0 10px #ff69b4;
        }

        .info-detail {
            font-size: 10px;
            color: #cccccc;
            margin-top: 2px;
        }

        /* ZONE DE CHAT PRINCIPALE */
        .chat-container {
            height: calc(100vh - 120px);
            display: flex;
            flex-direction: column;
            padding: 20px;
        }

        .chat-messages {
            flex: 1;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 20px;
            overflow-y: auto;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 105, 180, 0.3);
            backdrop-filter: blur(5px);
        }

        .message {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 10px;
            max-width: 80%;
            word-wrap: break-word;
        }

        .message.user {
            background: linear-gradient(135deg, #ff1493, #ff69b4);
            margin-left: auto;
            color: white;
            text-align: right;
        }

        .message.assistant {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 105, 180, 0.3);
            color: #ffffff;
        }

        .message.system {
            background: rgba(255, 215, 0, 0.2);
            border: 1px solid rgba(255, 215, 0, 0.5);
            color: #ffd700;
            font-size: 14px;
            text-align: center;
            margin: 10px auto;
            max-width: 60%;
        }

        .message-source {
            font-size: 12px;
            color: #ff69b4;
            margin-top: 5px;
            font-style: italic;
        }

        /* ZONE DE SAISIE */
        .input-container {
            display: flex;
            gap: 15px;
            background: rgba(0, 0, 0, 0.5);
            padding: 20px;
            border-radius: 15px;
            border: 2px solid rgba(255, 105, 180, 0.3);
            backdrop-filter: blur(10px);
        }

        .input-field {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 105, 180, 0.5);
            border-radius: 10px;
            padding: 15px;
            color: #ffffff;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;
        }

        .input-field:focus {
            border-color: #ff69b4;
            box-shadow: 0 0 20px rgba(255, 105, 180, 0.3);
        }

        .input-field::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .send-button {
            background: linear-gradient(135deg, #ff1493, #ff69b4);
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            color: white;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .send-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(255, 105, 180, 0.4);
        }

        .send-button:active {
            transform: translateY(0);
        }

        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* ANIMATIONS */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .loading {
            animation: pulse 1.5s infinite;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message {
            animation: slideIn 0.3s ease-out;
        }

        /* SCROLLBAR PERSONNALISÉE */
        .chat-messages::-webkit-scrollbar {
            width: 8px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 4px;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #ff1493, #ff69b4);
            border-radius: 4px;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #ff69b4, #ff1493);
        }
    </style>
</head>
<body>
    <!-- BARRE D'INFORMATIONS EN HAUT -->
    <div class="info-bar">
        <div class="info-item">
            <div class="info-title">🧠 QI Évolutif</div>
            <div class="info-value" id="coefficientQI">127</div>
            <div class="info-detail">Intelligence</div>
        </div>
        <div class="info-item">
            <div class="info-title">🧠 Neurones</div>
            <div class="info-value" id="neuronesCount">201.0M</div>
            <div class="info-detail">Auto-expansion</div>
        </div>
        <div class="info-item">
            <div class="info-title">⚡ Accélérateurs</div>
            <div class="info-value" id="accelerateursCount">3</div>
            <div class="info-detail">KYBER actifs</div>
        </div>
        <div class="info-item">
            <div class="info-title">💾 Mémoires</div>
            <div class="info-value" id="memoriesCount">0</div>
            <div class="info-detail">Stockées</div>
        </div>
        <div class="info-item">
            <div class="info-title">🌡️ Température</div>
            <div class="info-value" id="temperatureValue">0.5</div>
            <div class="info-detail">Moyenne</div>
        </div>
        <div class="info-item">
            <div class="info-title">🔍 Sécurité</div>
            <div class="info-value" id="securityScore">100%</div>
            <div class="info-detail">Google sécurisé</div>
        </div>
    </div>

    <!-- ZONE DE CHAT PRINCIPALE -->
    <div class="chat-container">
        <div class="chat-messages" id="chatMessages">
            <div class="message system">
                🚀 LOUNA-AI initialisée - Interface grande activée
            </div>
            <div class="message assistant">
                Bonjour ! Je suis LOUNA-AI, votre assistant avec mémoire thermique vivante. 
                Posez-moi vos questions, je peux chercher dans ma mémoire ou sur Internet de manière sécurisée.
                <div class="message-source">Système LOUNA-AI</div>
            </div>
        </div>

        <div class="input-container">
            <input 
                type="text" 
                class="input-field" 
                id="messageInput" 
                placeholder="Posez votre question à LOUNA-AI..."
                autocomplete="off"
            >
            <button class="send-button" id="sendButton">
                Envoyer
            </button>
        </div>
    </div>

    <script>
        // Variables globales
        let isLoading = false;

        // Éléments DOM
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const chatMessages = document.getElementById('chatMessages');

        // FONCTION PRINCIPALE D'ENVOI
        async function sendMessage() {
            const message = messageInput.value.trim();
            
            if (!message || isLoading) {
                return;
            }

            // Désactiver l'interface pendant l'envoi
            isLoading = true;
            sendButton.disabled = true;
            sendButton.textContent = 'Envoi...';
            sendButton.classList.add('loading');

            // Ajouter le message utilisateur
            addMessage(message, 'user');
            messageInput.value = '';

            try {
                // Envoyer la requête au serveur
                const response = await fetch('/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });

                const data = await response.json();

                if (data.success) {
                    // Ajouter la réponse de l'assistant
                    addMessage(data.response, 'assistant', data.source);
                    
                    // Mettre à jour le QI si évolution
                    if (data.coefficient_intellectuel) {
                        updateQI(data.coefficient_intellectuel, data.evolution_qi);
                        // Mettre à jour aussi les neurones et accélérateurs
                        updateNeurones(data.coefficient_intellectuel);
                        updateAccelerateurs(data.coefficient_intellectuel);
                    }
                } else {
                    addMessage('Erreur: ' + (data.error || 'Réponse invalide'), 'system');
                }

            } catch (error) {
                console.error('Erreur envoi message:', error);
                addMessage('Erreur de connexion au serveur', 'system');
            } finally {
                // Réactiver l'interface
                isLoading = false;
                sendButton.disabled = false;
                sendButton.textContent = 'Envoyer';
                sendButton.classList.remove('loading');
                messageInput.focus();
            }
        }

        // FONCTION POUR AJOUTER UN MESSAGE
        function addMessage(content, type, source = null) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            let messageHTML = content;
            
            if (source && type === 'assistant') {
                messageHTML += `<div class="message-source">Source: ${source}</div>`;
            }
            
            messageDiv.innerHTML = messageHTML;
            chatMessages.appendChild(messageDiv);
            
            // Scroll vers le bas
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // FONCTION POUR METTRE À JOUR LE QI
        function updateQI(newQI, evolution) {
        }

        // FONCTION POUR METTRE À JOUR LES NEURONES
        function updateNeurones(qi) {
            const neuronesElement = document.getElementById("neuronesCount");
            const neurones = (qi * 1.0).toFixed(1); // 1 neurone par point de QI
            neuronesElement.textContent = neurones + "M";
        }

        // FONCTION POUR METTRE À JOUR LES ACCÉLÉRATEURS
        function updateAccelerateurs(qi) {
            const accelerateursElement = document.getElementById("accelerateursCount");
            const accelerateurs = Math.min(15, Math.floor(qi / 20)); // 1 accélérateur par 20 points de QI
            accelerateursElement.textContent = accelerateurs;
        }

        // FONCTION ORIGINALE updateQI
        function updateQI_original(newQI, evolution) {            const qiElement = document.getElementById('coefficientQI');
            qiElement.textContent = newQI;
            
            if (evolution && evolution > 0) {
                addMessage(`🧠 Évolution QI: +${evolution} points (Total: ${newQI})`, 'system');
                qiElement.style.animation = 'pulse 2s ease-in-out';
                setTimeout(() => {
                    qiElement.style.animation = '';
                }, 2000);
            }
        }

        // ÉVÉNEMENTS
        sendButton.addEventListener('click', sendMessage);

        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // MISE À JOUR AUTOMATIQUE DES STATS
        async function updateStats() {
            try {
                const response = await fetch('/stats');
                const data = await response.json();
                
                if (data.success && data.stats) {
                    const stats = data.stats;
                    
                    // Mettre à jour QI
                    if (data.coefficient_intellectuel) {
                        document.getElementById('coefficientQI').textContent = data.coefficient_intellectuel;
                    }
                    
                    // Mettre à jour neurones
                    if (stats.neurones_system && stats.neurones_system.total_installed) {
                        const neurones = (stats.neurones_system.total_installed / 1000000).toFixed(1);
                        document.getElementById('neuronesCount').textContent = neurones + 'M';
                    }
                    
                    // Mettre à jour accélérateurs
                    if (stats.kyber_accelerators && stats.kyber_accelerators.active_count) {
                        document.getElementById('accelerateursCount').textContent = stats.kyber_accelerators.active_count;
                    }
                    
                    // Mettre à jour mémoires
                    if (stats.totalEntries !== undefined) {
                        document.getElementById('memoriesCount').textContent = stats.totalEntries;
                    }
                    
                    // Mettre à jour température
                    if (stats.averageTemperature !== undefined) {
                        document.getElementById('temperatureValue').textContent = stats.averageTemperature.toFixed(2);
                    }
                }
            } catch (error) {
                console.error('Erreur mise à jour stats:', error);
            }
        }

        // INITIALISATION
        document.addEventListener('DOMContentLoaded', function() {
            messageInput.focus();
            updateStats();
            setInterval(updateStats, 5000); // Mise à jour toutes les 5 secondes
        });
    </script>
</body>
</html>
