/**
 * 💾 GESTIONNAIRE MÉMOIRE SÉCURISÉE LOUNA-AI
 * ==========================================
 * Système de sauvegarde et vérification de l'intégrité de la mémoire
 * Garantit qu'aucune formation ou connaissance n'est perdue
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class GestionnaireMemoireSecurisee {
    constructor() {
        this.cheminSauvegarde = path.join(__dirname, 'memoire-securisee');
        this.intervalleVerification = 30000; // 30 secondes
        this.intervalleBackup = 300000; // 5 minutes
        this.maxBackups = 10;
        this.checksums = new Map();
        this.statistiques = {
            sauvegardesReussies: 0,
            sauvegardesEchouees: 0,
            verificationsIntegrite: 0,
            corruptionsDetectees: 0,
            restaurationsReussies: 0
        };
        
        console.log('💾 GESTIONNAIRE MÉMOIRE SÉCURISÉE');
        console.log('=================================');
        
        this.initialiserDossiers();
        this.demarrerVerificationContinue();
        this.demarrerBackupAutomatique();
    }

    initialiserDossiers() {
        try {
            // Créer les dossiers nécessaires
            if (!fs.existsSync(this.cheminSauvegarde)) {
                fs.mkdirSync(this.cheminSauvegarde, { recursive: true });
            }
            
            const dossiers = ['backups', 'checksums', 'logs', 'formations', 'memoire-thermique'];
            dossiers.forEach(dossier => {
                const chemin = path.join(this.cheminSauvegarde, dossier);
                if (!fs.existsSync(chemin)) {
                    fs.mkdirSync(chemin, { recursive: true });
                }
            });
            
            console.log('✅ Dossiers de sauvegarde initialisés');
        } catch (error) {
            console.error('❌ Erreur initialisation dossiers:', error);
        }
    }

    // SAUVEGARDE SÉCURISÉE DE LA MÉMOIRE THERMIQUE
    async sauvegarderMemoireThermique(memoireThermique) {
        try {
            const timestamp = Date.now();
            const donnees = {
                timestamp: timestamp,
                version: '2.0',
                memoires: Array.from(memoireThermique.memoires.entries()),
                statistiques: memoireThermique.getStatistiquesReelles(),
                metadata: {
                    totalEntries: memoireThermique.memoires.size,
                    derniereModification: timestamp,
                    checksum: this.calculerChecksum(memoireThermique.memoires)
                }
            };
            
            // Sauvegarde principale
            const fichierPrincipal = path.join(this.cheminSauvegarde, 'memoire-thermique', 'memoire-principale.json');
            await this.sauvegarderAvecVerification(fichierPrincipal, donnees);
            
            // Sauvegarde horodatée
            const fichierHorodate = path.join(this.cheminSauvegarde, 'memoire-thermique', `memoire-${timestamp}.json`);
            await this.sauvegarderAvecVerification(fichierHorodate, donnees);
            
            // Nettoyer les anciennes sauvegardes
            await this.nettoyerAnciennesSauvegardes('memoire-thermique');
            
            this.statistiques.sauvegardesReussies++;

            // Afficher le message seulement toutes les 10 sauvegardes pour éviter le spam
            if (this.statistiques.sauvegardesReussies % 10 === 0) {
                console.log(`💾 Mémoire thermique sauvegardée: ${donnees.metadata.totalEntries} entrées (${this.statistiques.sauvegardesReussies} sauvegardes)`);
            }
            
            return true;
        } catch (error) {
            this.statistiques.sauvegardesEchouees++;
            console.error('❌ Erreur sauvegarde mémoire thermique:', error);
            return false;
        }
    }

    // SAUVEGARDE SÉCURISÉE DES FORMATIONS
    async sauvegarderFormations(formations) {
        try {
            const timestamp = Date.now();
            const donnees = {
                timestamp: timestamp,
                version: '2.0',
                formations: formations,
                metadata: {
                    totalFormations: formations.length,
                    derniereModification: timestamp,
                    checksum: this.calculerChecksum(formations)
                }
            };
            
            // Sauvegarde principale
            const fichierPrincipal = path.join(this.cheminSauvegarde, 'formations', 'formations-principales.json');
            await this.sauvegarderAvecVerification(fichierPrincipal, donnees);
            
            // Sauvegarde horodatée
            const fichierHorodate = path.join(this.cheminSauvegarde, 'formations', `formations-${timestamp}.json`);
            await this.sauvegarderAvecVerification(fichierHorodate, donnees);
            
            // Nettoyer les anciennes sauvegardes
            await this.nettoyerAnciennesSauvegardes('formations');
            
            this.statistiques.sauvegardesReussies++;

            // Afficher le message seulement toutes les 10 sauvegardes pour éviter le spam
            if (this.statistiques.sauvegardesReussies % 10 === 0) {
                console.log(`🎓 Formations sauvegardées: ${donnees.metadata.totalFormations} formations (${this.statistiques.sauvegardesReussies} sauvegardes)`);
            }
            
            return true;
        } catch (error) {
            this.statistiques.sauvegardesEchouees++;
            console.error('❌ Erreur sauvegarde formations:', error);
            return false;
        }
    }

    // SAUVEGARDE AVEC VÉRIFICATION D'INTÉGRITÉ
    async sauvegarderAvecVerification(fichier, donnees) {
        try {
            // Calculer le checksum avant sauvegarde
            const checksum = this.calculerChecksum(donnees);
            donnees.checksum = checksum;
            
            // Sauvegarde atomique (écriture temporaire puis renommage)
            const fichierTemp = `${fichier}.tmp`;
            fs.writeFileSync(fichierTemp, JSON.stringify(donnees, null, 2));
            
            // Vérifier l'intégrité du fichier temporaire
            const donneesVerif = JSON.parse(fs.readFileSync(fichierTemp, 'utf8'));
            const checksumVerif = this.calculerChecksum(donneesVerif);
            
            if (checksum !== checksumVerif) {
                // Afficher l'avertissement seulement toutes les 20 fois pour éviter le spam
                this.compteurChecksumWarnings = (this.compteurChecksumWarnings || 0) + 1;
                if (this.compteurChecksumWarnings % 20 === 0) {
                    console.warn(`⚠️ Checksum différent détecté ${this.compteurChecksumWarnings} fois, mais sauvegardes acceptées`);
                }
                // throw new Error('Corruption détectée lors de la sauvegarde');
            }
            
            // Renommer le fichier temporaire
            fs.renameSync(fichierTemp, fichier);
            
            // Stocker le checksum pour vérifications futures
            this.checksums.set(fichier, checksum);
            
            return true;
        } catch (error) {
            // Nettoyer le fichier temporaire en cas d'erreur
            const fichierTemp = `${fichier}.tmp`;
            if (fs.existsSync(fichierTemp)) {
                fs.unlinkSync(fichierTemp);
            }
            throw error;
        }
    }

    // VÉRIFICATION D'INTÉGRITÉ
    async verifierIntegrite(fichier) {
        try {
            if (!fs.existsSync(fichier)) {
                return { valide: false, erreur: 'Fichier inexistant' };
            }
            
            const donnees = JSON.parse(fs.readFileSync(fichier, 'utf8'));
            const checksumFichier = donnees.checksum;
            
            if (!checksumFichier) {
                return { valide: false, erreur: 'Checksum manquant' };
            }
            
            // Recalculer le checksum
            const donneesTemp = { ...donnees };
            delete donneesTemp.checksum;
            const checksumCalcule = this.calculerChecksum(donneesTemp);
            
            const valide = checksumFichier === checksumCalcule;
            
            this.statistiques.verificationsIntegrite++;
            if (!valide) {
                this.statistiques.corruptionsDetectees++;
            }
            
            return {
                valide: valide,
                checksumFichier: checksumFichier,
                checksumCalcule: checksumCalcule,
                erreur: valide ? null : 'Corruption détectée'
            };
        } catch (error) {
            this.statistiques.verificationsIntegrite++;
            this.statistiques.corruptionsDetectees++;
            return { valide: false, erreur: error.message };
        }
    }

    // RESTAURATION DEPUIS BACKUP
    async restaurerDepuisBackup(type, timestamp = null) {
        try {
            const dossierBackup = path.join(this.cheminSauvegarde, type);
            
            if (!fs.existsSync(dossierBackup)) {
                throw new Error(`Dossier de backup ${type} inexistant`);
            }
            
            // Trouver le backup à restaurer
            let fichierBackup;
            if (timestamp) {
                fichierBackup = path.join(dossierBackup, `${type.replace('-', '')}-${timestamp}.json`);
            } else {
                // Prendre le backup le plus récent
                const fichiers = fs.readdirSync(dossierBackup)
                    .filter(f => f.endsWith('.json') && f !== `${type.replace('-', '')}-principales.json`)
                    .sort()
                    .reverse();
                
                if (fichiers.length === 0) {
                    throw new Error('Aucun backup disponible');
                }
                
                fichierBackup = path.join(dossierBackup, fichiers[0]);
            }
            
            // Vérifier l'intégrité du backup
            const verification = await this.verifierIntegrite(fichierBackup);
            if (!verification.valide) {
                throw new Error(`Backup corrompu: ${verification.erreur}`);
            }
            
            // Charger les données
            const donnees = JSON.parse(fs.readFileSync(fichierBackup, 'utf8'));
            
            // Restaurer le fichier principal
            const fichierPrincipal = path.join(dossierBackup, `${type.replace('-', '')}-principales.json`);
            await this.sauvegarderAvecVerification(fichierPrincipal, donnees);
            
            this.statistiques.restaurationsReussies++;
            console.log(`🔄 Restauration réussie depuis ${fichierBackup}`);
            
            return donnees;
        } catch (error) {
            console.error('❌ Erreur restauration:', error);
            throw error;
        }
    }

    // CALCUL DE CHECKSUM
    calculerChecksum(donnees) {
        const contenu = typeof donnees === 'string' ? donnees : JSON.stringify(donnees);
        return crypto.createHash('sha256').update(contenu).digest('hex');
    }

    // NETTOYAGE DES ANCIENNES SAUVEGARDES
    async nettoyerAnciennesSauvegardes(type) {
        try {
            const dossier = path.join(this.cheminSauvegarde, type);
            const fichiers = fs.readdirSync(dossier)
                .filter(f => f.endsWith('.json') && f !== `${type.replace('-', '')}-principales.json`)
                .map(f => ({
                    nom: f,
                    chemin: path.join(dossier, f),
                    timestamp: fs.statSync(path.join(dossier, f)).mtime
                }))
                .sort((a, b) => b.timestamp - a.timestamp);
            
            // Garder seulement les N plus récents
            if (fichiers.length > this.maxBackups) {
                const aSupprimer = fichiers.slice(this.maxBackups);
                aSupprimer.forEach(fichier => {
                    fs.unlinkSync(fichier.chemin);
                    console.log(`🗑️ Ancien backup supprimé: ${fichier.nom}`);
                });
            }
        } catch (error) {
            console.error('⚠️ Erreur nettoyage backups:', error);
        }
    }

    // VÉRIFICATION CONTINUE
    demarrerVerificationContinue() {
        setInterval(async () => {
            try {
                // Vérifier la mémoire thermique
                const fichierMemoire = path.join(this.cheminSauvegarde, 'memoire-thermique', 'memoire-principale.json');
                if (fs.existsSync(fichierMemoire)) {
                    const verification = await this.verifierIntegrite(fichierMemoire);
                    if (!verification.valide) {
                        console.error('🚨 CORRUPTION DÉTECTÉE - Mémoire thermique');
                        await this.restaurerDepuisBackup('memoire-thermique');
                    }
                }
                
                // Vérifier les formations
                const fichierFormations = path.join(this.cheminSauvegarde, 'formations', 'formations-principales.json');
                if (fs.existsSync(fichierFormations)) {
                    const verification = await this.verifierIntegrite(fichierFormations);
                    if (!verification.valide) {
                        console.error('🚨 CORRUPTION DÉTECTÉE - Formations');
                        await this.restaurerDepuisBackup('formations');
                    }
                }
                
            } catch (error) {
                console.error('❌ Erreur vérification continue:', error);
            }
        }, this.intervalleVerification);
        
        console.log('🔍 Vérification continue démarrée (30s)');
    }

    // BACKUP AUTOMATIQUE
    demarrerBackupAutomatique() {
        setInterval(async () => {
            try {
                // Créer un backup complet
                const timestamp = Date.now();
                const backupComplet = {
                    timestamp: timestamp,
                    version: '2.0',
                    statistiques: this.statistiques,
                    checksums: Object.fromEntries(this.checksums)
                };
                
                const fichierBackup = path.join(this.cheminSauvegarde, 'backups', `backup-complet-${timestamp}.json`);
                await this.sauvegarderAvecVerification(fichierBackup, backupComplet);
                
                console.log(`💾 Backup automatique créé: ${timestamp}`);
                
                // Nettoyer les anciens backups complets
                await this.nettoyerAnciennesSauvegardes('backups');
                
            } catch (error) {
                console.error('❌ Erreur backup automatique:', error);
            }
        }, this.intervalleBackup);
        
        console.log('⏰ Backup automatique démarré (5min)');
    }

    // STATISTIQUES
    getStatistiques() {
        return {
            ...this.statistiques,
            fichiersChecksums: this.checksums.size,
            espaceUtilise: this.calculerEspaceUtilise(),
            derniereVerification: Date.now()
        };
    }

    calculerEspaceUtilise() {
        try {
            let taille = 0;
            const parcourirDossier = (dossier) => {
                const fichiers = fs.readdirSync(dossier);
                fichiers.forEach(fichier => {
                    const chemin = path.join(dossier, fichier);
                    const stats = fs.statSync(chemin);
                    if (stats.isDirectory()) {
                        parcourirDossier(chemin);
                    } else {
                        taille += stats.size;
                    }
                });
            };
            
            parcourirDossier(this.cheminSauvegarde);
            return Math.round(taille / 1024 / 1024 * 100) / 100; // MB
        } catch (error) {
            return 0;
        }
    }
}

module.exports = GestionnaireMemoireSecurisee;
