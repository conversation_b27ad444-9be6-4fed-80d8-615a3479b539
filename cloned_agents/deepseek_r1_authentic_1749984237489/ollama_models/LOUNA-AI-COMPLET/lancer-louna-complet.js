#!/usr/bin/env node

/**
 * LANCEUR LOUNA-AI COMPLET
 * Système complet avec mémoire thermique authentique
 * Fonctionne depuis le disque ALDO
 */

console.log('🚀 LANCEUR LOUNA-AI COMPLET');
console.log('===========================');
console.log('📍 Depuis disque ALDO (93GB disponible)');
console.log('🧠 Mémoire thermique authentique de Jean-Luc Passave');
console.log('');

// Vérifier que nous sommes sur le bon disque
const currentDir = __dirname;
if (!currentDir.includes('ALDO et MIM')) {
    console.log('❌ ERREUR: Ce script doit être lancé depuis le disque ALDO');
    console.log('📍 Répertoire actuel:', currentDir);
    process.exit(1);
}

console.log('✅ Disque ALDO détecté');
console.log('📂 Répertoire:', currentDir);
console.log('');

// Lancer l'interface complète
console.log('🧠 Lancement LOUNA-AI avec mémoire thermique complète...');
console.log('⚡ Toutes les fonctionnalités activées');
console.log('');

try {
    require('./interface-complete-fonctionnelle.js');
} catch (error) {
    console.log('❌ Erreur lancement:', error.message);
    console.log('');
    console.log('🔧 Vérifications:');
    console.log('1. Node.js installé ?');
    console.log('2. Dépendances installées ? (npm install)');
    console.log('3. Ollama installé et démarré ?');
    process.exit(1);
}
