/**
 * RECHERCHE ÉTENDUE MÉMOIRE
 * Système de recherche avancée multi-zones avec IA
 */

const fs = require('fs');
const path = require('path');

class RechercheEtendue {
    constructor() {
        console.log('🔍 RECHERCHE ÉTENDUE MÉMOIRE');
        console.log('============================');
        
        this.config = {
            memoire: '/Volumes/LounaAI_V3/MEMOIRE-REELLE',
            compression: '/Volumes/LounaAI_V3/MEMOIRE-REELLE/compression',
            recherche: '/Volumes/LounaAI_V3/MEMOIRE-REELLE/recherche',
            max_resultats: 20,
            seuil_pertinence: 0.3
        };
        
        this.index = {
            mots_cles: new Map(),
            concepts: new Map(),
            relations: new Map(),
            derniere_indexation: null
        };
        
        this.stats = {
            recherches_effectuees: 0,
            resultats_trouves: 0,
            temps_moyen_recherche: 0,
            mots_indexes: 0
        };
        
        this.initialiserRecherche();
    }
    
    initialiserRecherche() {
        console.log('🔧 Initialisation recherche étendue...');
        
        try {
            // Créer dossier recherche
            if (!fs.existsSync(this.config.recherche)) {
                fs.mkdirSync(this.config.recherche, { recursive: true });
                console.log('📁 Dossier recherche créé');
            }
            
            // Charger index existant
            this.chargerIndex();
            
            // Créer index si nécessaire
            if (!this.index.derniere_indexation) {
                this.creerIndexComplet();
            }
            
            console.log('✅ Recherche étendue initialisée');
            
        } catch (error) {
            console.log(`❌ Erreur initialisation: ${error.message}`);
        }
    }
    
    chargerIndex() {
        try {
            const cheminIndex = path.join(this.config.recherche, 'index.json');
            
            if (fs.existsSync(cheminIndex)) {
                const indexData = JSON.parse(fs.readFileSync(cheminIndex, 'utf8'));
                
                // Reconstituer Maps
                this.index.mots_cles = new Map(indexData.mots_cles || []);
                this.index.concepts = new Map(indexData.concepts || []);
                this.index.relations = new Map(indexData.relations || []);
                this.index.derniere_indexation = indexData.derniere_indexation;
                
                console.log(`📚 Index chargé: ${this.index.mots_cles.size} mots-clés`);
            } else {
                console.log('🆕 Nouvel index à créer');
            }
            
        } catch (error) {
            console.log(`⚠️ Erreur chargement index: ${error.message}`);
        }
    }
    
    sauvegarderIndex() {
        try {
            const indexData = {
                mots_cles: Array.from(this.index.mots_cles.entries()),
                concepts: Array.from(this.index.concepts.entries()),
                relations: Array.from(this.index.relations.entries()),
                derniere_indexation: this.index.derniere_indexation,
                stats: this.stats
            };
            
            const cheminIndex = path.join(this.config.recherche, 'index.json');
            fs.writeFileSync(cheminIndex, JSON.stringify(indexData, null, 2));
            
        } catch (error) {
            console.log(`⚠️ Erreur sauvegarde index: ${error.message}`);
        }
    }
    
    creerIndexComplet() {
        console.log('\n📚 CRÉATION INDEX COMPLET');
        console.log('=========================');
        
        const debut = Date.now();
        let fichiersIndexes = 0;
        
        try {
            // Indexer zones thermiques
            this.indexerZonesThermiques();
            
            // Indexer fichiers comprimés
            this.indexerFichiersComprimes();
            
            // Créer relations sémantiques
            this.creerRelationsSemaniques();
            
            this.index.derniere_indexation = Date.now();
            this.stats.mots_indexes = this.index.mots_cles.size;
            
            const duree = Date.now() - debut;
            console.log(`✅ Index créé en ${duree}ms`);
            console.log(`📊 ${this.index.mots_cles.size} mots-clés indexés`);
            
            this.sauvegarderIndex();
            
        } catch (error) {
            console.log(`❌ Erreur création index: ${error.message}`);
        }
    }
    
    indexerZonesThermiques() {
        console.log('🌡️ Indexation zones thermiques...');
        
        try {
            const cheminZones = path.join(this.config.memoire, 'zones-thermiques');
            
            if (fs.existsSync(cheminZones)) {
                const zones = fs.readdirSync(cheminZones);
                
                zones.forEach(zone => {
                    const cheminZone = path.join(cheminZones, zone);
                    
                    if (fs.existsSync(cheminZone)) {
                        const fichiers = fs.readdirSync(cheminZone).filter(f => f.endsWith('.json'));
                        
                        fichiers.forEach(fichier => {
                            try {
                                const cheminFichier = path.join(cheminZone, fichier);
                                const souvenir = JSON.parse(fs.readFileSync(cheminFichier, 'utf8'));
                                
                                this.indexerSouvenir(souvenir, zone, fichier);
                                
                            } catch (error) {
                                // Ignorer fichiers corrompus
                            }
                        });
                    }
                });
            }
            
        } catch (error) {
            console.log(`❌ Erreur indexation zones: ${error.message}`);
        }
    }
    
    indexerFichiersComprimes() {
        console.log('🗜️ Indexation fichiers comprimés...');
        
        try {
            if (fs.existsSync(this.config.compression)) {
                const zones = fs.readdirSync(this.config.compression);
                
                zones.forEach(zone => {
                    const cheminZone = path.join(this.config.compression, zone);
                    
                    if (fs.existsSync(cheminZone)) {
                        const fichiers = fs.readdirSync(cheminZone).filter(f => f.endsWith('.gz'));
                        
                        fichiers.forEach(fichier => {
                            try {
                                // Note: Pour l'indexation, on stocke juste la référence
                                // La décompression se fera à la demande
                                const reference = {
                                    fichier: fichier,
                                    zone: zone,
                                    comprime: true,
                                    chemin: path.join(cheminZone, fichier)
                                };
                                
                                // Indexer le nom du fichier au minimum
                                const nomSansExtension = fichier.replace('.json.gz', '');
                                this.ajouterMotCle(nomSansExtension, reference);
                                
                            } catch (error) {
                                // Ignorer fichiers corrompus
                            }
                        });
                    }
                });
            }
            
        } catch (error) {
            console.log(`❌ Erreur indexation comprimés: ${error.message}`);
        }
    }
    
    indexerSouvenir(souvenir, zone, fichier) {
        const reference = {
            fichier: fichier,
            zone: zone,
            comprime: false,
            id: souvenir.id,
            timestamp: souvenir.timestamp
        };
        
        // Indexer contenu principal
        if (souvenir.contenu) {
            this.extraireMotsCles(souvenir.contenu).forEach(mot => {
                this.ajouterMotCle(mot, reference);
            });
        }
        
        // Indexer question
        if (souvenir.question) {
            this.extraireMotsCles(souvenir.question).forEach(mot => {
                this.ajouterMotCle(mot, reference);
            });
        }
        
        // Indexer réponse
        if (souvenir.reponse) {
            this.extraireMotsCles(souvenir.reponse).forEach(mot => {
                this.ajouterMotCle(mot, reference);
            });
        }
        
        // Indexer concepts
        this.extraireConcepts(souvenir).forEach(concept => {
            this.ajouterConcept(concept, reference);
        });
    }
    
    extraireMotsCles(texte) {
        return texte.toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(mot => mot.length > 2)
            .filter(mot => !this.estMotVide(mot));
    }
    
    estMotVide(mot) {
        const motsVides = ['les', 'des', 'une', 'est', 'sont', 'avec', 'dans', 'pour', 'que', 'qui', 'sur', 'par'];
        return motsVides.includes(mot);
    }
    
    extraireConcepts(souvenir) {
        const concepts = [];
        
        // Concepts basés sur le type
        if (souvenir.type) {
            concepts.push(souvenir.type);
        }
        
        // Concepts basés sur la zone
        if (souvenir.zone) {
            concepts.push('zone_' + souvenir.zone);
        }
        
        // Concepts basés sur le contenu
        const texte = (souvenir.contenu || '') + ' ' + (souvenir.question || '') + ' ' + (souvenir.reponse || '');
        
        if (texte.includes('mémoire')) concepts.push('memoire');
        if (texte.includes('intelligence')) concepts.push('intelligence');
        if (texte.includes('test')) concepts.push('test');
        if (texte.includes('calcul')) concepts.push('mathematiques');
        if (texte.includes('logique')) concepts.push('logique');
        
        return concepts;
    }
    
    ajouterMotCle(mot, reference) {
        if (!this.index.mots_cles.has(mot)) {
            this.index.mots_cles.set(mot, []);
        }
        this.index.mots_cles.get(mot).push(reference);
    }
    
    ajouterConcept(concept, reference) {
        if (!this.index.concepts.has(concept)) {
            this.index.concepts.set(concept, []);
        }
        this.index.concepts.get(concept).push(reference);
    }
    
    creerRelationsSemaniques() {
        console.log('🔗 Création relations sémantiques...');
        
        // Relations prédéfinies
        const relations = {
            'memoire': ['souvenir', 'rappel', 'stockage'],
            'intelligence': ['qi', 'smart', 'clever'],
            'test': ['evaluation', 'examen', 'verification'],
            'calcul': ['mathematiques', 'nombre', 'addition'],
            'logique': ['raisonnement', 'deduction', 'inference']
        };
        
        Object.entries(relations).forEach(([concept, synonymes]) => {
            this.index.relations.set(concept, synonymes);
        });
        
        console.log(`🔗 ${this.index.relations.size} relations créées`);
    }
    
    // RECHERCHE PRINCIPALE
    rechercherEtendue(requete, options = {}) {
        console.log(`🔍 Recherche étendue: "${requete}"`);
        
        const debut = Date.now();
        this.stats.recherches_effectuees++;
        
        const config = {
            max_resultats: options.max_resultats || this.config.max_resultats,
            inclure_comprimes: options.inclure_comprimes !== false,
            zones_specifiques: options.zones || null,
            seuil_pertinence: options.seuil_pertinence || this.config.seuil_pertinence
        };
        
        try {
            // 1. Extraire mots-clés de la requête
            const motsCles = this.extraireMotsCles(requete);
            
            // 2. Étendre avec synonymes
            const motsEtendus = this.etendreAvecSynonymes(motsCles);
            
            // 3. Rechercher par mots-clés
            const resultatsMotsCles = this.rechercherParMotsCles(motsEtendus, config);
            
            // 4. Rechercher par concepts
            const resultatsConcepts = this.rechercherParConcepts(motsEtendus, config);
            
            // 5. Fusionner et scorer
            const resultats = this.fusionnerResultats(resultatsMotsCles, resultatsConcepts);
            
            // 6. Trier par pertinence
            const resultatsTriés = this.trierParPertinence(resultats, motsCles);
            
            // 7. Limiter résultats
            const resultatsFinaux = resultatsTriés.slice(0, config.max_resultats);
            
            // 8. Enrichir avec contenu
            const resultatsEnrichis = this.enrichirResultats(resultatsFinaux, config);
            
            const duree = Date.now() - debut;
            this.stats.temps_moyen_recherche = (this.stats.temps_moyen_recherche + duree) / 2;
            this.stats.resultats_trouves += resultatsFinaux.length;
            
            console.log(`✅ ${resultatsFinaux.length} résultats trouvés en ${duree}ms`);
            
            return {
                requete: requete,
                resultats: resultatsEnrichis,
                stats: {
                    nombre_resultats: resultatsFinaux.length,
                    duree_ms: duree,
                    mots_cles: motsCles,
                    mots_etendus: motsEtendus
                }
            };
            
        } catch (error) {
            console.log(`❌ Erreur recherche: ${error.message}`);
            return {
                requete: requete,
                resultats: [],
                erreur: error.message
            };
        }
    }
    
    etendreAvecSynonymes(motsCles) {
        const motsEtendus = new Set(motsCles);
        
        motsCles.forEach(mot => {
            // Chercher synonymes directs
            if (this.index.relations.has(mot)) {
                this.index.relations.get(mot).forEach(synonyme => {
                    motsEtendus.add(synonyme);
                });
            }
            
            // Chercher relations inverses
            this.index.relations.forEach((synonymes, concept) => {
                if (synonymes.includes(mot)) {
                    motsEtendus.add(concept);
                }
            });
        });
        
        return Array.from(motsEtendus);
    }
    
    rechercherParMotsCles(mots, config) {
        const resultats = new Map();
        
        mots.forEach(mot => {
            if (this.index.mots_cles.has(mot)) {
                this.index.mots_cles.get(mot).forEach(reference => {
                    // Filtrer par zones si spécifié
                    if (config.zones_specifiques && !config.zones_specifiques.includes(reference.zone)) {
                        return;
                    }
                    
                    // Exclure comprimés si demandé
                    if (!config.inclure_comprimes && reference.comprime) {
                        return;
                    }
                    
                    const cle = reference.fichier + '_' + reference.zone;
                    if (!resultats.has(cle)) {
                        resultats.set(cle, {
                            reference: reference,
                            score_mots: 0,
                            score_concepts: 0,
                            mots_trouves: []
                        });
                    }
                    
                    const resultat = resultats.get(cle);
                    resultat.score_mots++;
                    resultat.mots_trouves.push(mot);
                });
            }
        });
        
        return resultats;
    }
    
    rechercherParConcepts(mots, config) {
        const resultats = new Map();
        
        mots.forEach(mot => {
            if (this.index.concepts.has(mot)) {
                this.index.concepts.get(mot).forEach(reference => {
                    // Filtrer par zones si spécifié
                    if (config.zones_specifiques && !config.zones_specifiques.includes(reference.zone)) {
                        return;
                    }
                    
                    const cle = reference.fichier + '_' + reference.zone;
                    if (!resultats.has(cle)) {
                        resultats.set(cle, {
                            reference: reference,
                            score_concepts: 0,
                            concepts_trouves: []
                        });
                    }
                    
                    const resultat = resultats.get(cle);
                    resultat.score_concepts++;
                    resultat.concepts_trouves.push(mot);
                });
            }
        });
        
        return resultats;
    }
    
    fusionnerResultats(resultatsMotsCles, resultatsConcepts) {
        const fusion = new Map();
        
        // Ajouter résultats mots-clés
        resultatsMotsCles.forEach((resultat, cle) => {
            fusion.set(cle, resultat);
        });
        
        // Fusionner avec résultats concepts
        resultatsConcepts.forEach((resultat, cle) => {
            if (fusion.has(cle)) {
                const existant = fusion.get(cle);
                existant.score_concepts = resultat.score_concepts;
                existant.concepts_trouves = resultat.concepts_trouves;
            } else {
                fusion.set(cle, {
                    reference: resultat.reference,
                    score_mots: 0,
                    score_concepts: resultat.score_concepts,
                    mots_trouves: [],
                    concepts_trouves: resultat.concepts_trouves
                });
            }
        });
        
        return Array.from(fusion.values());
    }
    
    trierParPertinence(resultats, motsClesOriginaux) {
        return resultats.map(resultat => {
            // Score combiné
            const scoreTotal = (resultat.score_mots * 2) + resultat.score_concepts;
            
            // Bonus pour correspondance exacte
            let bonusExact = 0;
            motsClesOriginaux.forEach(mot => {
                if (resultat.mots_trouves.includes(mot)) {
                    bonusExact += 0.5;
                }
            });
            
            // Score final
            resultat.pertinence = scoreTotal + bonusExact;
            
            return resultat;
        }).sort((a, b) => b.pertinence - a.pertinence);
    }
    
    enrichirResultats(resultats, config) {
        return resultats.map(resultat => {
            try {
                let contenu = null;
                
                if (resultat.reference.comprime) {
                    // Décompresser à la demande si nécessaire
                    contenu = "Contenu comprimé - décompression à la demande";
                } else {
                    // Charger contenu normal
                    const cheminFichier = path.join(
                        this.config.memoire, 
                        'zones-thermiques', 
                        resultat.reference.zone, 
                        resultat.reference.fichier
                    );
                    
                    if (fs.existsSync(cheminFichier)) {
                        const souvenir = JSON.parse(fs.readFileSync(cheminFichier, 'utf8'));
                        contenu = souvenir.contenu || souvenir.question || souvenir.reponse || "Contenu vide";
                    }
                }
                
                return {
                    fichier: resultat.reference.fichier,
                    zone: resultat.reference.zone,
                    pertinence: resultat.pertinence.toFixed(2),
                    score_mots: resultat.score_mots,
                    score_concepts: resultat.score_concepts,
                    mots_trouves: resultat.mots_trouves,
                    concepts_trouves: resultat.concepts_trouves || [],
                    contenu: contenu ? contenu.substring(0, 200) + '...' : 'Contenu non disponible',
                    comprime: resultat.reference.comprime
                };
                
            } catch (error) {
                return {
                    fichier: resultat.reference.fichier,
                    zone: resultat.reference.zone,
                    pertinence: resultat.pertinence.toFixed(2),
                    contenu: 'Erreur chargement contenu',
                    erreur: error.message
                };
            }
        });
    }
    
    // RECHERCHE RAPIDE (compatible avec ancien système)
    rechercherRapide(question) {
        const resultats = this.rechercherEtendue(question, {
            max_resultats: 2,
            inclure_comprimes: false
        });
        
        return resultats.resultats.map(r => ({
            contenu: r.contenu,
            zone: r.zone,
            pertinence: parseFloat(r.pertinence)
        }));
    }
    
    afficherStats() {
        console.log('\n📊 STATISTIQUES RECHERCHE ÉTENDUE');
        console.log('=================================');
        
        console.log(`🔍 Recherches effectuées: ${this.stats.recherches_effectuees}`);
        console.log(`📚 Résultats trouvés: ${this.stats.resultats_trouves}`);
        console.log(`⏱️ Temps moyen: ${this.stats.temps_moyen_recherche.toFixed(1)}ms`);
        console.log(`📖 Mots indexés: ${this.index.mots_cles.size}`);
        console.log(`🔗 Concepts indexés: ${this.index.concepts.size}`);
        console.log(`🌐 Relations: ${this.index.relations.size}`);
        
        if (this.index.derniere_indexation) {
            console.log(`🕐 Dernière indexation: ${new Date(this.index.derniere_indexation).toLocaleString()}`);
        }
    }
}

// Export
module.exports = RechercheEtendue;

// Test automatique si exécuté directement
if (require.main === module) {
    console.log('🚀 LANCEMENT RECHERCHE ÉTENDUE');
    console.log('==============================');
    
    const recherche = new RechercheEtendue();
    
    setTimeout(() => {
        // Test recherche
        const resultats = recherche.rechercherEtendue('mémoire intelligence test');
        
        console.log('\n🔍 RÉSULTATS TEST:');
        resultats.resultats.forEach((r, i) => {
            console.log(`${i + 1}. ${r.fichier} (${r.zone}) - Pertinence: ${r.pertinence}`);
            console.log(`   Contenu: ${r.contenu}`);
        });
        
        recherche.afficherStats();
        
    }, 2000);
}
