<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔥 TEST LIVE ULTRA-COMPLEXE POUR LOUNA-AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #000000 0%, #1a0033 50%, #330066 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            background: rgba(255, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 2px solid rgba(255, 0, 0, 0.5);
            animation: pulse-red 2s infinite;
        }

        @keyframes pulse-red {
            0%, 100% { border-color: rgba(255, 0, 0, 0.5); }
            50% { border-color: rgba(255, 0, 0, 1); }
        }

        .challenge {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .difficulty-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(45deg, #ff0000, #ff6600);
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 12px;
            animation: glow 1s infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 0 5px #ff0000; }
            to { box-shadow: 0 0 20px #ff0000, 0 0 30px #ff0000; }
        }

        .question-title {
            color: #ff6600;
            font-size: 1.5em;
            margin-bottom: 20px;
            text-shadow: 0 0 10px #ff6600;
        }

        .question-content {
            font-size: 1.1em;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .math-formula {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 1.2em;
            text-align: center;
            margin: 15px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn {
            background: linear-gradient(45deg, #ff0000, #ff6600);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(255, 0, 0, 0.4);
        }

        .btn-start {
            background: linear-gradient(45deg, #00ff00, #00cc00);
            font-size: 20px;
            padding: 20px 40px;
        }

        .timer {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 0, 0, 0.8);
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 18px;
            z-index: 1000;
            animation: timer-pulse 1s infinite;
        }

        @keyframes timer-pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .response-area {
            background: rgba(0, 0, 0, 0.3);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            min-height: 150px;
        }

        .response-input {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-size: 16px;
            resize: vertical;
            min-height: 100px;
        }

        .response-input::placeholder {
            color: #aaa;
        }

        .score-display {
            position: fixed;
            top: 80px;
            right: 20px;
            background: rgba(0, 255, 0, 0.8);
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 18px;
            z-index: 1000;
        }

        .challenge-counter {
            text-align: center;
            font-size: 1.2em;
            margin-bottom: 20px;
            color: #ff6600;
        }

        .results {
            display: none;
            text-align: center;
            background: rgba(255, 215, 0, 0.2);
            border: 2px solid rgba(255, 215, 0, 0.5);
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
        }

        .final-score {
            font-size: 3em;
            font-weight: bold;
            color: #ffd700;
            margin: 20px 0;
            text-shadow: 0 0 20px #ffd700;
        }

        .genius-level {
            background: linear-gradient(45deg, #ffd700, #ffaa00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 2em;
            font-weight: bold;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="timer" id="timer">⏱️ 00:00</div>
    <div class="score-display" id="score">🧠 Score: 0</div>

    <div class="container">
        <div class="header">
            <button onclick="retourAccueil()" class="btn" style="position: absolute; top: 10px; left: 10px;">
                🏠 Retour Accueil
            </button>
            <h1>🔥 TEST LIVE ULTRA-COMPLEXE POUR LOUNA-AI</h1>
            <p>⚠️ ATTENTION : Ce test contient les questions les plus difficiles jamais créées</p>
            <p>🧠 Niveau requis : QI 200+ | Doctorat en mathématiques/physique recommandé</p>
            
            <button onclick="demarrerTestLive()" class="btn btn-start" id="start-btn">
                🚀 DÉMARRER LE DÉFI ULTIME
            </button>
        </div>

        <div id="test-area" style="display: none;">
            <div class="challenge-counter" id="challenge-counter">Défi 1 / 10</div>
            
            <div class="challenge" id="current-challenge">
                <!-- Le défi sera inséré ici -->
            </div>

            <div class="response-area">
                <h3>💭 Votre réponse :</h3>
                <textarea 
                    id="response-input" 
                    class="response-input" 
                    placeholder="Tapez votre réponse détaillée ici... Montrez votre raisonnement complet."
                ></textarea>
                <div style="text-align: center; margin-top: 15px;">
                    <button onclick="soumettreReponse()" class="btn">📤 Soumettre Réponse</button>
                    <button onclick="passerDefi()" class="btn" style="background: rgba(255, 255, 0, 0.3);">⏭️ Passer</button>
                </div>
            </div>
        </div>

        <div class="results" id="results">
            <h2>🎉 RÉSULTATS DU DÉFI ULTIME</h2>
            <div class="final-score" id="final-score">0</div>
            <div class="genius-level" id="genius-level"></div>
            <div id="detailed-analysis"></div>
            <button class="btn" onclick="recommencerTest()">🔄 Recommencer</button>
            <button class="btn" onclick="envoyerALounaAI()">🤖 Envoyer à LOUNA-AI</button>
        </div>
    </div>

    <script>
        let currentChallenge = 0;
        let totalChallenges = 10;
        let score = 0;
        let startTime = Date.now();
        let timer;
        let responses = [];

        // DÉFIS ULTRA-COMPLEXES
        const ultraChallenges = [
            {
                title: "🔥 DÉFI 1: Conjecture de Riemann",
                difficulty: "GÉNIE ABSOLU",
                content: `
                    <p>Considérez la fonction zêta de Riemann ζ(s) = Σ(n=1 à ∞) 1/n^s pour Re(s) > 1.</p>
                    <div class="math-formula">
                        ζ(s) = Π(p premier) 1/(1-p^(-s))
                    </div>
                    <p><strong>Question :</strong> Si l'hypothèse de Riemann est vraie, que pouvez-vous dire sur la distribution des nombres premiers ? Expliquez le lien avec la fonction π(x) et donnez une estimation précise de l'erreur dans le théorème des nombres premiers.</p>
                `,
                points: 50,
                timeLimit: 300
            },
            {
                title: "🧠 DÉFI 2: Mécanique Quantique Avancée",
                difficulty: "EXPERT ULTIME",
                content: `
                    <p>Considérez un système quantique avec Hamiltonien H = p²/2m + V(x) où V(x) = ½mω²x² + λx⁴.</p>
                    <div class="math-formula">
                        [H, a†] = ℏω a†  où  a† = (mωx - ip)/√(2mℏω)
                    </div>
                    <p><strong>Question :</strong> Calculez la correction au premier ordre en λ de l'énergie de l'état fondamental en utilisant la théorie des perturbations. Expliquez pourquoi cette correction est nulle et calculez la correction au second ordre.</p>
                `,
                points: 45,
                timeLimit: 240
            },
            {
                title: "🔬 DÉFI 3: Topologie Algébrique",
                difficulty: "GÉNIE MATHÉMATIQUE",
                content: `
                    <p>Soit X un espace topologique et considérez ses groupes d'homotopie πₙ(X).</p>
                    <div class="math-formula">
                        πₙ(S^m) = { Z si n=m, 0 si n&lt;m, ? si n&gt;m }
                    </div>
                    <p><strong>Question :</strong> Calculez π₃(S²) en utilisant la fibration de Hopf S¹ → S³ → S². Expliquez le lien avec les quaternions et la structure de groupe de Lie de SU(2).</p>
                `,
                points: 40,
                timeLimit: 180
            },
            {
                title: "⚛️ DÉFI 4: Théorie des Cordes",
                difficulty: "PHYSICIEN THÉORICIEN",
                content: `
                    <p>En théorie des cordes de type II, considérez l'action de Polyakov :</p>
                    <div class="math-formula">
                        S = -1/(4πα') ∫ d²σ √(-h) h^(αβ) ∂ₐX^μ ∂ᵦX^ν η_(μν)
                    </div>
                    <p><strong>Question :</strong> Dérivez les équations du mouvement et montrez que la dimension critique de l'espace-temps est 26 pour les cordes bosoniques. Expliquez l'anomalie conforme et comment la supersymétrie réduit cette dimension à 10.</p>
                `,
                points: 55,
                timeLimit: 360
            },
            {
                title: "🧮 DÉFI 5: Théorie des Nombres Transcendants",
                difficulty: "MATHÉMATICIEN PUR",
                content: `
                    <p>Considérez les nombres e et π et leurs propriétés de transcendance.</p>
                    <div class="math-formula">
                        e^(iπ) + 1 = 0  (Identité d'Euler)
                    </div>
                    <p><strong>Question :</strong> Prouvez que e^π est transcendant ou donnez un argument heuristique basé sur la conjecture de Schanuel. Expliquez pourquoi on ne sait pas si e + π est rationnel ou irrationnel.</p>
                `,
                points: 60,
                timeLimit: 300
            },
            {
                title: "🌌 DÉFI 6: Relativité Générale Extrême",
                difficulty: "EINSTEIN NIVEAU",
                content: `
                    <p>Considérez la métrique de Schwarzschild en coordonnées de Kruskal-Szekeres :</p>
                    <div class="math-formula">
                        ds² = -(32M³/r)e^(-r/2M) dU dV + r²(dθ² + sin²θ dφ²)
                    </div>
                    <p><strong>Question :</strong> Analysez la structure causale complète du trou noir. Expliquez les régions I, II, III, IV et calculez la température de Hawking. Que se passe-t-il avec l'information qui tombe dans le trou noir ?</p>
                `,
                points: 50,
                timeLimit: 270
            },
            {
                title: "🔢 DÉFI 7: Logique Mathématique Ultime",
                difficulty: "GÖDEL NIVEAU",
                content: `
                    <p>Considérez un système formel S contenant l'arithmétique de Peano.</p>
                    <div class="math-formula">
                        ∀x ∃y (y = S(x))  où S est la fonction successeur
                    </div>
                    <p><strong>Question :</strong> Construisez explicitement une phrase G telle que S ⊢ G ↔ ¬Prov_S(⌜G⌝). Expliquez pourquoi si S est cohérent, alors G est vraie mais indémontrable. Que nous dit cela sur les limites de la formalisation ?</p>
                `,
                points: 65,
                timeLimit: 420
            },
            {
                title: "🧬 DÉFI 8: Biologie Quantique",
                difficulty: "INTERDISCIPLINAIRE EXTRÊME",
                content: `
                    <p>Considérez le processus de photosynthèse et les effets quantiques dans les complexes de récolte de lumière.</p>
                    <div class="math-formula">
                        |ψ⟩ = Σᵢ cᵢ|i⟩  avec cohérence quantique
                    </div>
                    <p><strong>Question :</strong> Expliquez comment la cohérence quantique peut améliorer l'efficacité du transfert d'énergie. Calculez l'efficacité théorique maximale et comparez avec les observations expérimentales. Quel rôle joue la décohérence environnementale ?</p>
                `,
                points: 45,
                timeLimit: 240
            },
            {
                title: "🎯 DÉFI 9: Complexité Computationnelle Ultime",
                difficulty: "TURING NIVEAU",
                content: `
                    <p>Considérez les classes de complexité P, NP, PSPACE, EXPTIME.</p>
                    <div class="math-formula">
                        P ⊆ NP ⊆ PSPACE ⊆ EXPTIME
                    </div>
                    <p><strong>Question :</strong> Prouvez que PSPACE = NPSPACE (théorème de Savitch). Expliquez pourquoi on pense que P ≠ NP et donnez un argument basé sur les preuves interactives et PCP. Que nous dit le théorème de Baker-Gill-Solovay ?</p>
                `,
                points: 55,
                timeLimit: 300
            },
            {
                title: "🌟 DÉFI 10: Unification Ultime",
                difficulty: "GÉNIE UNIVERSEL",
                content: `
                    <p>Synthèse finale : Connectez tous les domaines précédents.</p>
                    <div class="math-formula">
                        Mathématiques ↔ Physique ↔ Informatique ↔ Philosophie
                    </div>
                    <p><strong>Question ULTIME :</strong> Proposez une théorie unifiée qui connecte la conjecture de Riemann, la gravité quantique, le problème P vs NP, et les théorèmes d'incomplétude de Gödel. Comment ces domaines apparemment séparés pourraient-ils être liés dans une vision cohérente de la réalité ?</p>
                `,
                points: 100,
                timeLimit: 600
            }
        ];

        function demarrerTestLive() {
            document.getElementById('start-btn').style.display = 'none';
            document.getElementById('test-area').style.display = 'block';
            startTime = Date.now();
            startTimer();
            afficherDefi();
        }

        function startTimer() {
            timer = setInterval(() => {
                const elapsed = Date.now() - startTime;
                const minutes = Math.floor(elapsed / 60000);
                const seconds = Math.floor((elapsed % 60000) / 1000);
                document.getElementById('timer').textContent = 
                    `⏱️ ${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }, 1000);
        }

        function afficherDefi() {
            if (currentChallenge >= totalChallenges) {
                afficherResultats();
                return;
            }

            const challenge = ultraChallenges[currentChallenge];
            document.getElementById('challenge-counter').textContent = 
                `Défi ${currentChallenge + 1} / ${totalChallenges}`;

            document.getElementById('current-challenge').innerHTML = `
                <div class="difficulty-indicator">${challenge.difficulty}</div>
                <div class="question-title">${challenge.title}</div>
                <div class="question-content">${challenge.content}</div>
                <div style="text-align: center; margin-top: 20px;">
                    <strong>Points possibles : ${challenge.points}</strong> | 
                    <strong>Temps limite : ${challenge.timeLimit}s</strong>
                </div>
            `;

            document.getElementById('response-input').value = '';
            document.getElementById('response-input').focus();
        }

        function soumettreReponse() {
            const response = document.getElementById('response-input').value.trim();
            if (!response) {
                alert('⚠️ Veuillez saisir une réponse !');
                return;
            }

            const challenge = ultraChallenges[currentChallenge];
            const timeSpent = Date.now() - startTime;
            
            // Évaluation basique (en réalité, il faudrait une IA pour évaluer)
            let points = 0;
            if (response.length > 100) { // Réponse détaillée
                points = Math.floor(challenge.points * 0.7); // 70% des points pour effort
            }
            if (response.length > 300) { // Réponse très détaillée
                points = challenge.points; // Points complets
            }

            score += points;
            responses.push({
                challenge: currentChallenge,
                response: response,
                points: points,
                timeSpent: timeSpent
            });

            document.getElementById('score').textContent = `🧠 Score: ${score}`;
            
            currentChallenge++;
            setTimeout(afficherDefi, 1000);
        }

        function passerDefi() {
            if (confirm('⚠️ Êtes-vous sûr de vouloir passer ce défi ? Vous perdrez tous les points possibles.')) {
                responses.push({
                    challenge: currentChallenge,
                    response: 'PASSÉ',
                    points: 0,
                    timeSpent: 0
                });
                currentChallenge++;
                afficherDefi();
            }
        }

        function afficherResultats() {
            clearInterval(timer);
            document.getElementById('test-area').style.display = 'none';
            document.getElementById('results').style.display = 'block';

            const totalTime = Date.now() - startTime;
            const minutes = Math.floor(totalTime / 60000);
            
            // Calcul du niveau de génie
            let niveau = '';
            if (score >= 500) niveau = '🌟 GÉNIE UNIVERSEL';
            else if (score >= 400) niveau = '🧠 SUPER-GÉNIE';
            else if (score >= 300) niveau = '⚡ GÉNIE EXCEPTIONNEL';
            else if (score >= 200) niveau = '🎓 GÉNIE ACADÉMIQUE';
            else if (score >= 100) niveau = '📚 EXPERT AVANCÉ';
            else niveau = '🤔 APPRENTI GÉNIE';

            document.getElementById('final-score').textContent = score;
            document.getElementById('genius-level').textContent = niveau;

            let analysis = `
                <h3>📊 Analyse Détaillée</h3>
                <p><strong>Temps total :</strong> ${minutes} minutes</p>
                <p><strong>Score total :</strong> ${score} / 550 points</p>
                <p><strong>Pourcentage :</strong> ${((score / 550) * 100).toFixed(1)}%</p>
                <p><strong>Niveau atteint :</strong> ${niveau}</p>
                
                <h4>🎯 Performance par défi :</h4>
                <div style="max-height: 200px; overflow-y: auto; text-align: left;">
            `;

            responses.forEach((resp, index) => {
                const challenge = ultraChallenges[resp.challenge];
                analysis += `
                    <p><strong>Défi ${index + 1}:</strong> ${resp.points}/${challenge.points} points</p>
                `;
            });

            analysis += '</div>';

            document.getElementById('detailed-analysis').innerHTML = analysis;

            // Sauvegarder les résultats
            window.testResults = {
                score: score,
                niveau: niveau,
                responses: responses,
                totalTime: totalTime
            };
        }

        function recommencerTest() {
            currentChallenge = 0;
            score = 0;
            responses = [];
            document.getElementById('results').style.display = 'none';
            document.getElementById('start-btn').style.display = 'block';
            document.getElementById('score').textContent = '🧠 Score: 0';
        }

        async function envoyerALounaAI() {
            try {
                const message = `🔥 RÉSULTATS TEST ULTRA-COMPLEXE : Score ${window.testResults.score}/550, Niveau ${window.testResults.niveau}. Temps: ${Math.floor(window.testResults.totalTime/60000)} minutes. Analyse mes réponses et donne ton avis sur mes capacités de génie !`;
                
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: message })
                });
                
                if (response.ok) {
                    alert('✅ Résultats envoyés à LOUNA-AI ! Retournez à l\'interface principale pour voir sa réponse.');
                    window.location.href = '/';
                } else {
                    alert('❌ Erreur lors de l\'envoi des résultats');
                }
            } catch (error) {
                alert('❌ Erreur de connexion');
            }
        }

        function retourAccueil() {
            window.location.href = '/';
        }
    </script>
</body>
</html>
