/**
 * MONITORING TEMPS RÉEL
 * Surveillance continue du système unifié
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class MonitoringTempsReel {
    constructor() {
        console.log('👁️ MONITORING TEMPS RÉEL');
        console.log('========================');
        
        this.config = {
            memoire: '/Volumes/LounaAI_V3/MEMOIRE-REELLE',
            ollama: '/Volumes/LounaAI_V3/AGENTS-REELS/ollama/ollama',
            monitoring: '/Volumes/LounaAI_V3/MEMOIRE-REELLE/monitoring'
        };
        
        this.metriques = {
            debut_monitoring: Date.now(),
            verifications_totales: 0,
            erreurs_detectees: 0,
            recuperations_reussies: 0,
            uptime_systeme: 0,
            derniere_verification: null
        };
        
        this.etat_systeme = {
            usb_accessible: false,
            memoire_active: false,
            agent_disponible: false,
            curseur_mobile: false,
            zones_operationnelles: 0,
            derniere_interaction: null
        };
        
        this.alertes = {
            usb_deconnecte: false,
            memoire_corrompue: false,
            agent_inaccessible: false,
            curseur_bloque: false,
            zones_manquantes: false
        };
        
        this.initialiserMonitoring();
    }
    
    initialiserMonitoring() {
        console.log('🔧 Initialisation monitoring...');
        
        try {
            // Créer dossier monitoring
            if (!fs.existsSync(this.config.monitoring)) {
                fs.mkdirSync(this.config.monitoring, { recursive: true });
                console.log('📁 Dossier monitoring créé');
            }
            
            // Première vérification
            this.verifierSystemeComplet();
            
            // Démarrer surveillance continue
            this.demarrerSurveillanceContinue();
            
            console.log('✅ Monitoring temps réel initialisé');
            
        } catch (error) {
            console.log(`❌ Erreur initialisation monitoring: ${error.message}`);
        }
    }
    
    verifierSystemeComplet() {
        console.log('\n🔍 VÉRIFICATION SYSTÈME COMPLÈTE');
        console.log('================================');
        
        this.metriques.verifications_totales++;
        this.metriques.derniere_verification = Date.now();
        
        // Vérifier USB
        this.verifierUSB();
        
        // Vérifier mémoire
        this.verifierMemoire();
        
        // Vérifier agent
        this.verifierAgent();
        
        // Vérifier curseur
        this.verifierCurseur();
        
        // Vérifier zones
        this.verifierZones();
        
        // Calculer uptime
        this.metriques.uptime_systeme = Date.now() - this.metriques.debut_monitoring;
        
        // Sauvegarder état
        this.sauvegarderEtatMonitoring();
        
        // Afficher résumé
        this.afficherResume();
    }
    
    verifierUSB() {
        try {
            const usbPath = '/Volumes/LounaAI_V3';
            
            if (fs.existsSync(usbPath)) {
                this.etat_systeme.usb_accessible = true;
                this.alertes.usb_deconnecte = false;
                console.log('✅ USB accessible');
            } else {
                this.etat_systeme.usb_accessible = false;
                this.alertes.usb_deconnecte = true;
                this.metriques.erreurs_detectees++;
                console.log('❌ USB déconnecté');
            }
            
        } catch (error) {
            this.etat_systeme.usb_accessible = false;
            this.alertes.usb_deconnecte = true;
            console.log(`❌ Erreur USB: ${error.message}`);
        }
    }
    
    verifierMemoire() {
        try {
            if (fs.existsSync(this.config.memoire)) {
                // Test lecture/écriture
                const testFile = path.join(this.config.memoire, 'test_monitoring.json');
                const testData = { 
                    test: 'monitoring', 
                    timestamp: Date.now(),
                    verification: this.metriques.verifications_totales
                };
                
                fs.writeFileSync(testFile, JSON.stringify(testData));
                const dataLue = JSON.parse(fs.readFileSync(testFile, 'utf8'));
                
                if (dataLue.test === 'monitoring') {
                    this.etat_systeme.memoire_active = true;
                    this.alertes.memoire_corrompue = false;
                    console.log('✅ Mémoire active');
                    
                    // Nettoyer
                    fs.unlinkSync(testFile);
                } else {
                    throw new Error('Données corrompues');
                }
                
            } else {
                throw new Error('Mémoire non trouvée');
            }
            
        } catch (error) {
            this.etat_systeme.memoire_active = false;
            this.alertes.memoire_corrompue = true;
            this.metriques.erreurs_detectees++;
            console.log(`❌ Erreur mémoire: ${error.message}`);
        }
    }
    
    verifierAgent() {
        try {
            if (fs.existsSync(this.config.ollama)) {
                const stats = fs.statSync(this.config.ollama);
                
                if (stats.mode & parseInt('111', 8)) {
                    this.etat_systeme.agent_disponible = true;
                    this.alertes.agent_inaccessible = false;
                    console.log('✅ Agent disponible');
                } else {
                    throw new Error('Permissions insuffisantes');
                }
            } else {
                throw new Error('Agent non trouvé');
            }
            
        } catch (error) {
            this.etat_systeme.agent_disponible = false;
            this.alertes.agent_inaccessible = true;
            this.metriques.erreurs_detectees++;
            console.log(`❌ Erreur agent: ${error.message}`);
        }
    }
    
    verifierCurseur() {
        try {
            const cheminCurseur = path.join(this.config.memoire, 'curseur-thermique', 'position.json');
            
            if (fs.existsSync(cheminCurseur)) {
                const curseur = JSON.parse(fs.readFileSync(cheminCurseur, 'utf8'));
                
                if (curseur.position && curseur.zone) {
                    this.etat_systeme.curseur_mobile = true;
                    this.alertes.curseur_bloque = false;
                    console.log(`✅ Curseur mobile: ${curseur.position}°C (${curseur.zone})`);
                } else {
                    throw new Error('Données curseur invalides');
                }
            } else {
                throw new Error('Curseur non trouvé');
            }
            
        } catch (error) {
            this.etat_systeme.curseur_mobile = false;
            this.alertes.curseur_bloque = true;
            this.metriques.erreurs_detectees++;
            console.log(`❌ Erreur curseur: ${error.message}`);
        }
    }
    
    verifierZones() {
        try {
            const cheminZones = path.join(this.config.memoire, 'zones-thermiques');
            const zones = ['zone1_70C', 'zone2_60C', 'zone3_50C', 'zone4_40C', 'zone5_30C', 'zone6_20C'];
            
            let zonesOK = 0;
            
            if (fs.existsSync(cheminZones)) {
                zones.forEach(zone => {
                    const cheminZone = path.join(cheminZones, zone);
                    if (fs.existsSync(cheminZone)) {
                        zonesOK++;
                    }
                });
            }
            
            this.etat_systeme.zones_operationnelles = zonesOK;
            
            if (zonesOK === 6) {
                this.alertes.zones_manquantes = false;
                console.log(`✅ Zones thermiques: ${zonesOK}/6`);
            } else {
                this.alertes.zones_manquantes = true;
                this.metriques.erreurs_detectees++;
                console.log(`⚠️ Zones thermiques: ${zonesOK}/6`);
            }
            
        } catch (error) {
            this.etat_systeme.zones_operationnelles = 0;
            this.alertes.zones_manquantes = true;
            console.log(`❌ Erreur zones: ${error.message}`);
        }
    }
    
    demarrerSurveillanceContinue() {
        console.log('👁️ Démarrage surveillance continue...');
        
        // Surveillance toutes les 10 secondes
        this.intervalSurveillance = setInterval(() => {
            this.verifierSystemeComplet();
            this.detecterProblemes();
            this.tenterRecuperationAutomatique();
        }, 10000);
        
        console.log('✅ Surveillance continue active (10s)');
    }
    
    detecterProblemes() {
        const problemes = [];
        
        if (this.alertes.usb_deconnecte) problemes.push('USB déconnecté');
        if (this.alertes.memoire_corrompue) problemes.push('Mémoire corrompue');
        if (this.alertes.agent_inaccessible) problemes.push('Agent inaccessible');
        if (this.alertes.curseur_bloque) problemes.push('Curseur bloqué');
        if (this.alertes.zones_manquantes) problemes.push('Zones manquantes');
        
        if (problemes.length > 0) {
            console.log('\n🚨 PROBLÈMES DÉTECTÉS:');
            problemes.forEach(probleme => {
                console.log(`   ❌ ${probleme}`);
            });
        }
    }
    
    tenterRecuperationAutomatique() {
        console.log('\n🔄 TENTATIVE RÉCUPÉRATION AUTOMATIQUE');
        console.log('=====================================');
        
        let recuperations = 0;
        
        // Récupération mémoire
        if (this.alertes.memoire_corrompue && this.etat_systeme.usb_accessible) {
            try {
                this.recreerStructureMemoire();
                console.log('✅ Structure mémoire recréée');
                recuperations++;
            } catch (error) {
                console.log(`❌ Échec récupération mémoire: ${error.message}`);
            }
        }
        
        // Récupération curseur
        if (this.alertes.curseur_bloque && this.etat_systeme.memoire_active) {
            try {
                this.reinitialiserCurseur();
                console.log('✅ Curseur réinitialisé');
                recuperations++;
            } catch (error) {
                console.log(`❌ Échec récupération curseur: ${error.message}`);
            }
        }
        
        // Récupération zones
        if (this.alertes.zones_manquantes && this.etat_systeme.memoire_active) {
            try {
                this.recreerZones();
                console.log('✅ Zones recréées');
                recuperations++;
            } catch (error) {
                console.log(`❌ Échec récupération zones: ${error.message}`);
            }
        }
        
        if (recuperations > 0) {
            this.metriques.recuperations_reussies += recuperations;
            console.log(`✅ ${recuperations} récupérations réussies`);
        }
    }
    
    recreerStructureMemoire() {
        const dossiers = [
            this.config.memoire,
            path.join(this.config.memoire, 'zones-thermiques'),
            path.join(this.config.memoire, 'curseur-thermique'),
            path.join(this.config.memoire, 'interactions'),
            path.join(this.config.memoire, 'monitoring')
        ];
        
        dossiers.forEach(dossier => {
            if (!fs.existsSync(dossier)) {
                fs.mkdirSync(dossier, { recursive: true });
            }
        });
    }
    
    reinitialiserCurseur() {
        const curseur = {
            position: 50.0,
            zone: 'zone3',
            derniere_maj: Date.now(),
            reinitialise_par_monitoring: true
        };
        
        const cheminCurseur = path.join(this.config.memoire, 'curseur-thermique');
        if (!fs.existsSync(cheminCurseur)) {
            fs.mkdirSync(cheminCurseur, { recursive: true });
        }
        
        const fichierCurseur = path.join(cheminCurseur, 'position.json');
        fs.writeFileSync(fichierCurseur, JSON.stringify(curseur, null, 2));
    }
    
    recreerZones() {
        const zones = ['zone1_70C', 'zone2_60C', 'zone3_50C', 'zone4_40C', 'zone5_30C', 'zone6_20C'];
        const cheminZones = path.join(this.config.memoire, 'zones-thermiques');
        
        if (!fs.existsSync(cheminZones)) {
            fs.mkdirSync(cheminZones, { recursive: true });
        }
        
        zones.forEach(zone => {
            const cheminZone = path.join(cheminZones, zone);
            if (!fs.existsSync(cheminZone)) {
                fs.mkdirSync(cheminZone, { recursive: true });
            }
        });
    }
    
    sauvegarderEtatMonitoring() {
        try {
            const rapport = {
                timestamp: Date.now(),
                date: new Date().toISOString(),
                metriques: this.metriques,
                etat_systeme: this.etat_systeme,
                alertes: this.alertes,
                score_sante: this.calculerScoreSante()
            };
            
            const cheminRapport = path.join(this.config.monitoring, 'rapport_monitoring.json');
            fs.writeFileSync(cheminRapport, JSON.stringify(rapport, null, 2));
            
            // Historique (garder 10 derniers rapports)
            const cheminHistorique = path.join(this.config.monitoring, `historique_${Date.now()}.json`);
            fs.writeFileSync(cheminHistorique, JSON.stringify(rapport, null, 2));
            
            // Nettoyer anciens rapports
            this.nettoyerHistorique();
            
        } catch (error) {
            console.log(`⚠️ Erreur sauvegarde monitoring: ${error.message}`);
        }
    }
    
    calculerScoreSante() {
        let score = 0;
        const criteres = 5;
        
        if (this.etat_systeme.usb_accessible) score++;
        if (this.etat_systeme.memoire_active) score++;
        if (this.etat_systeme.agent_disponible) score++;
        if (this.etat_systeme.curseur_mobile) score++;
        if (this.etat_systeme.zones_operationnelles === 6) score++;
        
        return (score / criteres * 100).toFixed(1);
    }
    
    nettoyerHistorique() {
        try {
            const fichiers = fs.readdirSync(this.config.monitoring)
                .filter(f => f.startsWith('historique_'))
                .sort()
                .reverse();
            
            // Garder seulement les 10 plus récents
            if (fichiers.length > 10) {
                fichiers.slice(10).forEach(fichier => {
                    const cheminFichier = path.join(this.config.monitoring, fichier);
                    fs.unlinkSync(cheminFichier);
                });
            }
        } catch (error) {
            // Ignorer erreurs nettoyage
        }
    }
    
    afficherResume() {
        console.log('\n📊 RÉSUMÉ MONITORING');
        console.log('====================');
        
        const scoreSante = this.calculerScoreSante();
        console.log(`🏥 Score santé système: ${scoreSante}%`);
        
        console.log(`\n📈 MÉTRIQUES:`);
        console.log(`   Vérifications: ${this.metriques.verifications_totales}`);
        console.log(`   Erreurs détectées: ${this.metriques.erreurs_detectees}`);
        console.log(`   Récupérations: ${this.metriques.recuperations_reussies}`);
        console.log(`   Uptime: ${(this.metriques.uptime_systeme / 1000 / 60).toFixed(1)} min`);
        
        console.log(`\n🎯 ÉTAT SYSTÈME:`);
        console.log(`   USB: ${this.etat_systeme.usb_accessible ? '✅' : '❌'}`);
        console.log(`   Mémoire: ${this.etat_systeme.memoire_active ? '✅' : '❌'}`);
        console.log(`   Agent: ${this.etat_systeme.agent_disponible ? '✅' : '❌'}`);
        console.log(`   Curseur: ${this.etat_systeme.curseur_mobile ? '✅' : '❌'}`);
        console.log(`   Zones: ${this.etat_systeme.zones_operationnelles}/6`);
        
        if (scoreSante >= 80) {
            console.log('\n🎉 SYSTÈME EN EXCELLENTE SANTÉ');
        } else if (scoreSante >= 60) {
            console.log('\n✅ Système fonctionnel');
        } else {
            console.log('\n⚠️ Système nécessite attention');
        }
    }
    
    arreterMonitoring() {
        console.log('\n⏹️ Arrêt monitoring...');
        
        if (this.intervalSurveillance) {
            clearInterval(this.intervalSurveillance);
            console.log('👁️ Surveillance arrêtée');
        }
        
        // Rapport final
        this.sauvegarderEtatMonitoring();
        console.log('📊 Rapport final sauvegardé');
        
        console.log('✅ Monitoring arrêté proprement');
    }
}

// Export
module.exports = MonitoringTempsReel;

// Test automatique si exécuté directement
if (require.main === module) {
    console.log('🚀 LANCEMENT MONITORING TEMPS RÉEL');
    console.log('===================================');
    
    const monitoring = new MonitoringTempsReel();
    
    // Arrêt automatique après 60 secondes
    setTimeout(() => {
        monitoring.arreterMonitoring();
        process.exit(0);
    }, 60000);
    
    // Gestion arrêt manuel
    process.on('SIGINT', () => {
        console.log('\n🛑 Arrêt manuel demandé...');
        monitoring.arreterMonitoring();
        process.exit(0);
    });
}
