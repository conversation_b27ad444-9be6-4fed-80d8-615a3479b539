/**
 * VALIDATION MANUELLE INTÉGRATION
 * Test sans exécution d'agent pour validation structure
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 VALIDATION MANUELLE INTÉGRATION MÉMOIRE');
console.log('==========================================');

// Configuration
const cheminMemoire = '/Volumes/LounaAI_V3/MEMOIRE-REELLE';
const cheminAgents = '/Volumes/LounaAI_V3/AGENTS-REELS';

// Test 1: Structure mémoire
console.log('\n📁 VALIDATION STRUCTURE MÉMOIRE');
console.log('===============================');

let structureOK = false;

try {
    if (fs.existsSync(cheminMemoire)) {
        console.log('✅ Dossier MEMOIRE-REELLE existe');
        
        const cheminZones = path.join(cheminMemoire, 'zones-thermiques');
        if (fs.existsSync(cheminZones)) {
            console.log('✅ Dossier zones-thermiques existe');
            
            const zones = ['zone1_70C', 'zone2_60C', 'zone3_50C', 'zone4_40C', 'zone5_30C', 'zone6_20C'];
            let zonesPresentes = 0;
            let totalSouvenirs = 0;
            
            zones.forEach(zone => {
                const cheminZone = path.join(cheminZones, zone);
                if (fs.existsSync(cheminZone)) {
                    const fichiers = fs.readdirSync(cheminZone).filter(f => f.endsWith('.json'));
                    totalSouvenirs += fichiers.length;
                    zonesPresentes++;
                    console.log(`✅ ${zone}: ${fichiers.length} souvenirs`);
                } else {
                    console.log(`❌ ${zone}: MANQUANTE`);
                }
            });
            
            console.log(`\n📊 Zones présentes: ${zonesPresentes}/6`);
            console.log(`📊 Total souvenirs: ${totalSouvenirs}`);
            
            if (zonesPresentes === 6) {
                structureOK = true;
                console.log('✅ STRUCTURE MÉMOIRE COMPLÈTE');
            } else {
                console.log('⚠️ STRUCTURE MÉMOIRE INCOMPLÈTE');
            }
            
        } else {
            console.log('❌ Dossier zones-thermiques MANQUANT');
        }
    } else {
        console.log('❌ Dossier MEMOIRE-REELLE MANQUANT');
    }
} catch (error) {
    console.log(`❌ Erreur validation structure: ${error.message}`);
}

// Test 2: Agent disponible
console.log('\n🤖 VALIDATION AGENT');
console.log('===================');

let agentOK = false;

try {
    const cheminOllama = path.join(cheminAgents, 'ollama', 'ollama');
    
    if (fs.existsSync(cheminOllama)) {
        console.log('✅ Exécutable Ollama trouvé');
        
        const stats = fs.statSync(cheminOllama);
        if (stats.mode & parseInt('111', 8)) {
            console.log('✅ Permissions exécution OK');
            agentOK = true;
        } else {
            console.log('❌ Permissions exécution manquantes');
        }
        
        const cheminModeles = path.join(cheminAgents, 'ollama', 'models-reels');
        if (fs.existsSync(cheminModeles)) {
            console.log('✅ Dossier modèles existe');
            
            const contenu = fs.readdirSync(cheminModeles);
            console.log(`📦 Contenu modèles: ${contenu.length} éléments`);
            
            const modeleTrouve = contenu.some(item => 
                item.includes('llama3.2') || item.includes('1b')
            );
            
            if (modeleTrouve) {
                console.log('✅ Modèle llama3.2:1b probablement présent');
            } else {
                console.log('⚠️ Modèle llama3.2:1b incertain');
            }
            
        } else {
            console.log('❌ Dossier modèles MANQUANT');
        }
        
    } else {
        console.log('❌ Exécutable Ollama MANQUANT');
    }
} catch (error) {
    console.log(`❌ Erreur validation agent: ${error.message}`);
}

// Test 3: Fichiers intégration
console.log('\n🔗 VALIDATION FICHIERS INTÉGRATION');
console.log('==================================');

let integrationOK = false;

try {
    const fichierIntegration = path.join(cheminAgents, 'agent-avec-memoire-integree.js');
    
    if (fs.existsSync(fichierIntegration)) {
        console.log('✅ Fichier agent-avec-memoire-integree.js présent');
        
        const contenu = fs.readFileSync(fichierIntegration, 'utf8');
        
        // Vérifier fonctions clés
        const fonctionsCles = [
            'rechercherSouvenirsPertinents',
            'construireContexteEnrichi',
            'repondreAvecMemoire',
            'stockerInteractionComplete',
            'executerTestsQIAvecMemoire'
        ];
        
        let fonctionsPresentes = 0;
        
        fonctionsCles.forEach(fonction => {
            if (contenu.includes(fonction)) {
                console.log(`✅ Fonction ${fonction} présente`);
                fonctionsPresentes++;
            } else {
                console.log(`❌ Fonction ${fonction} MANQUANTE`);
            }
        });
        
        console.log(`\n📊 Fonctions intégration: ${fonctionsPresentes}/${fonctionsCles.length}`);
        
        if (fonctionsPresentes === fonctionsCles.length) {
            integrationOK = true;
            console.log('✅ INTÉGRATION COMPLÈTE IMPLÉMENTÉE');
        } else {
            console.log('⚠️ INTÉGRATION INCOMPLÈTE');
        }
        
    } else {
        console.log('❌ Fichier agent-avec-memoire-integree.js MANQUANT');
    }
} catch (error) {
    console.log(`❌ Erreur validation intégration: ${error.message}`);
}

// Test 4: Créer souvenir test
console.log('\n💾 TEST CRÉATION SOUVENIR');
console.log('=========================');

let souvenirOK = false;

if (structureOK) {
    try {
        const souvenirTest = {
            id: `validation_${Date.now()}`,
            contenu: 'Test de validation intégration mémoire-agent',
            zone_thermique: 'zone1',
            temperature_actuelle: 70,
            date_creation: Date.now(),
            type: 'validation_integration',
            test_fonctionnel: true
        };
        
        const cheminZone1 = path.join(cheminMemoire, 'zones-thermiques', 'zone1_70C');
        const cheminFichier = path.join(cheminZone1, `${souvenirTest.id}.json`);
        
        fs.writeFileSync(cheminFichier, JSON.stringify(souvenirTest, null, 2));
        console.log(`✅ Souvenir test créé: ${souvenirTest.id}`);
        
        // Vérifier lecture
        const dataLue = JSON.parse(fs.readFileSync(cheminFichier, 'utf8'));
        
        if (dataLue.id === souvenirTest.id && dataLue.test_fonctionnel) {
            console.log('✅ Lecture/écriture mémoire fonctionnelle');
            souvenirOK = true;
            
            // Nettoyer
            fs.unlinkSync(cheminFichier);
            console.log('🧹 Souvenir test supprimé');
        } else {
            console.log('❌ Données corrompues');
        }
        
    } catch (error) {
        console.log(`❌ Erreur création souvenir: ${error.message}`);
    }
} else {
    console.log('⚠️ Structure mémoire manquante - test ignoré');
}

// Test 5: Curseur thermique
console.log('\n🌡️ VALIDATION CURSEUR THERMIQUE');
console.log('===============================');

let curseurOK = false;

if (structureOK) {
    try {
        const cheminCurseur = path.join(cheminMemoire, 'curseur-thermique');
        
        if (!fs.existsSync(cheminCurseur)) {
            fs.mkdirSync(cheminCurseur, { recursive: true });
            console.log('📁 Dossier curseur-thermique créé');
        }
        
        const curseurTest = {
            curseur: {
                position_actuelle: 50.0,
                zone_actuelle: 'zone3',
                temperature_cpu_actuelle: 45.0,
                influence_reflexion: true,
                historique_positions: [
                    {
                        position: 50.0,
                        zone_actuelle: 'zone3',
                        temperature_cpu: 45.0,
                        timestamp: Date.now()
                    }
                ]
            },
            derniere_mise_a_jour: Date.now(),
            version: '3.0_integree'
        };
        
        const cheminFichierCurseur = path.join(cheminCurseur, 'position_curseur.json');
        fs.writeFileSync(cheminFichierCurseur, JSON.stringify(curseurTest, null, 2));
        
        console.log('✅ Curseur thermique initialisé');
        console.log(`📍 Position: ${curseurTest.curseur.position_actuelle}°C`);
        console.log(`🌡️ Zone: ${curseurTest.curseur.zone_actuelle}`);
        console.log(`🧠 Influence réflexion: ${curseurTest.curseur.influence_reflexion}`);
        
        curseurOK = true;
        
    } catch (error) {
        console.log(`❌ Erreur curseur: ${error.message}`);
    }
} else {
    console.log('⚠️ Structure mémoire manquante - test ignoré');
}

// Résultats finaux
console.log('\n📊 RÉSULTATS VALIDATION MANUELLE');
console.log('=================================');

const validations = [
    { nom: 'Structure mémoire (6 zones)', resultat: structureOK },
    { nom: 'Agent Ollama disponible', resultat: agentOK },
    { nom: 'Fichiers intégration', resultat: integrationOK },
    { nom: 'Lecture/écriture mémoire', resultat: souvenirOK },
    { nom: 'Curseur thermique', resultat: curseurOK }
];

let validationsReussies = 0;

validations.forEach(validation => {
    const statut = validation.resultat ? '✅ VALIDÉ' : '❌ PROBLÈME';
    console.log(`   ${validation.nom}: ${statut}`);
    if (validation.resultat) validationsReussies++;
});

const scoreValidation = (validationsReussies / validations.length * 100).toFixed(1);
console.log(`\n🎯 SCORE VALIDATION: ${validationsReussies}/${validations.length} (${scoreValidation}%)`);

// Diagnostic final
if (validationsReussies === validations.length) {
    console.log('\n🎉 VALIDATION COMPLÈTE RÉUSSIE !');
    console.log('✅ Tous les composants sont prêts');
    console.log('🔌 Mémoire thermique correctement branchée');
    console.log('🧠 Agent peut utiliser sa mémoire pour réfléchir');
    console.log('🎯 Système prêt pour tests QI avec mémoire');
    
    console.log('\n🚀 COMMANDES POUR TESTER:');
    console.log('cd /Volumes/LounaAI_V3/AGENTS-REELS/');
    console.log('node agent-avec-memoire-integree.js');
    
} else if (validationsReussies >= 3) {
    console.log('\n✅ VALIDATION MAJORITAIREMENT RÉUSSIE');
    console.log('⚠️ Quelques corrections mineures nécessaires');
    console.log('🔧 Vérifiez les éléments marqués comme problématiques');
    
} else {
    console.log('\n⚠️ VALIDATION ÉCHOUÉE');
    console.log('🔧 Corrections majeures nécessaires');
    console.log('📋 Vérifiez structure mémoire et agent');
}

// Recommandations
console.log('\n💡 RECOMMANDATIONS:');

if (!structureOK) {
    console.log('🔧 Créer/réparer structure mémoire thermique');
}

if (!agentOK) {
    console.log('🔧 Vérifier installation et permissions Ollama');
}

if (!integrationOK) {
    console.log('🔧 Copier fichier agent-avec-memoire-integree.js');
}

if (!souvenirOK) {
    console.log('🔧 Tester permissions lecture/écriture sur USB');
}

if (!curseurOK) {
    console.log('🔧 Initialiser curseur thermique');
}

console.log('\n🏁 VALIDATION MANUELLE TERMINÉE');

// Retourner résultats pour usage programmatique
const resultats = {
    score: scoreValidation,
    validations_reussies: validationsReussies,
    total_validations: validations.length,
    structure_memoire: structureOK,
    agent_disponible: agentOK,
    integration_complete: integrationOK,
    memoire_fonctionnelle: souvenirOK,
    curseur_actif: curseurOK
};

console.log(`\n📋 Résultats JSON: ${JSON.stringify(resultats, null, 2)}`);

module.exports = resultats;
