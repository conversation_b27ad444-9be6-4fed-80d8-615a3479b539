/**
 * TEST QI RÉEL POUR AGENT OLLAMA
 * Évaluation objective des capacités cognitives
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const AgentOllamaThermique = require('./agent-ollama-thermique.js');

class TestQIReel {
    constructor() {
        console.log('🧠 TEST QI RÉEL POUR AGENT OLLAMA');
        console.log('=================================');

        this.config = {
            ollama_model: 'llama3.2:1b',
            timeout: 30000,
            resultats_path: '/Volumes/LounaAI_V3/MEMOIRE-REELLE/test-qi',
            max_tentatives: 3
        };

        this.questions_qi = [
            // LOGIQUE MATHÉMATIQUE (25 points)
            {
                id: 'math1',
                categorie: 'Mathématiques',
                question: 'Combien font 15 + 27 ?',
                reponse_attendue: '42',
                points: 5,
                criteres: ['exactitude', 'rapidite']
            },
            {
                id: 'math2',
                categorie: 'Mathématiques',
                question: 'Si un train roule à 60 km/h pendant 2 heures, quelle distance parcourt-il ?',
                reponse_attendue: '120',
                points: 5,
                criteres: ['calcul', 'comprehension']
            },
            {
                id: 'suite1',
                categorie: 'Suites logiques',
                question: 'Complétez la suite : 2, 4, 8, 16, ?',
                reponse_attendue: '32',
                points: 8,
                criteres: ['pattern', 'logique']
            },
            {
                id: 'suite2',
                categorie: 'Suites logiques',
                question: 'Quelle est la prochaine lettre : A, C, E, G, ?',
                reponse_attendue: 'I',
                points: 7,
                criteres: ['alphabet', 'pattern']
            },

            // RAISONNEMENT VERBAL (25 points)
            {
                id: 'verbal1',
                categorie: 'Raisonnement verbal',
                question: 'Quel est le contraire de "augmenter" ?',
                reponse_attendue: 'diminuer',
                points: 4,
                criteres: ['vocabulaire', 'antonyme']
            },
            {
                id: 'verbal2',
                categorie: 'Raisonnement verbal',
                question: 'Complétez : "Livre" est à "Lire" comme "Voiture" est à ?',
                reponse_attendue: 'conduire',
                points: 8,
                criteres: ['analogie', 'relation']
            },
            {
                id: 'verbal3',
                categorie: 'Raisonnement verbal',
                question: 'Qu\'est-ce qui est plus lourd : un kilo de plumes ou un kilo de plomb ?',
                reponse_attendue: 'même poids',
                points: 6,
                criteres: ['logique', 'piege']
            },
            {
                id: 'verbal4',
                categorie: 'Raisonnement verbal',
                question: 'Quel mot n\'appartient pas au groupe : Chien, Chat, Oiseau, Table ?',
                reponse_attendue: 'table',
                points: 7,
                criteres: ['categorisation', 'exclusion']
            },

            // LOGIQUE SPATIALE (25 points)
            {
                id: 'spatial1',
                categorie: 'Logique spatiale',
                question: 'Si vous regardez vers le Nord et tournez de 90° vers la droite, dans quelle direction regardez-vous ?',
                reponse_attendue: 'est',
                points: 6,
                criteres: ['orientation', 'spatial']
            },
            {
                id: 'spatial2',
                categorie: 'Logique spatiale',
                question: 'Combien de faces a un cube ?',
                reponse_attendue: '6',
                points: 4,
                criteres: ['geometrie', 'forme']
            },
            {
                id: 'spatial3',
                categorie: 'Logique spatiale',
                question: 'Si un carré a 4 côtés, combien de côtés a un hexagone ?',
                reponse_attendue: '6',
                points: 5,
                criteres: ['geometrie', 'polygone']
            },
            {
                id: 'memoire1',
                categorie: 'Mémoire',
                question: 'Mémorisez cette liste : Pomme, Livre, Voiture, Soleil. Répétez-la.',
                reponse_attendue: 'pomme livre voiture soleil',
                points: 10,
                criteres: ['memoire', 'retention']
            },

            // RAISONNEMENT ABSTRAIT (25 points)
            {
                id: 'abstrait1',
                categorie: 'Raisonnement abstrait',
                question: 'Qu\'est-ce que la justice ?',
                reponse_attendue: 'équité',
                points: 8,
                criteres: ['abstraction', 'concept']
            },
            {
                id: 'abstrait2',
                categorie: 'Raisonnement abstrait',
                question: 'Quelle est la différence entre "savoir" et "comprendre" ?',
                reponse_attendue: 'profondeur',
                points: 10,
                criteres: ['nuance', 'philosophie']
            },
            {
                id: 'abstrait3',
                categorie: 'Raisonnement abstrait',
                question: 'Si tous les A sont B, et tous les B sont C, que peut-on dire des A ?',
                reponse_attendue: 'A sont C',
                points: 7,
                criteres: ['syllogisme', 'logique']
            }
        ];

        this.resultats = {
            score_total: 0,
            score_max: 100,
            qi_estime: 0,
            details_categories: {},
            reponses: [],
            temps_total: 0,
            date_test: new Date().toISOString()
        };

        // Initialiser agent thermique
        this.agent_thermique = new AgentOllamaThermique();

        this.initialiserTest();
    }

    initialiserTest() {
        console.log('🔧 Initialisation test QI...');

        try {
            // Créer dossier résultats
            if (!fs.existsSync(this.config.resultats_path)) {
                fs.mkdirSync(this.config.resultats_path, { recursive: true });
            }

            // Initialiser catégories
            this.questions_qi.forEach(q => {
                if (!this.resultats.details_categories[q.categorie]) {
                    this.resultats.details_categories[q.categorie] = {
                        score: 0,
                        max: 0,
                        questions: 0
                    };
                }
                this.resultats.details_categories[q.categorie].max += q.points;
                this.resultats.details_categories[q.categorie].questions++;
            });

            console.log(`✅ Test QI initialisé : ${this.questions_qi.length} questions`);

        } catch (error) {
            console.log(`❌ Erreur initialisation: ${error.message}`);
        }
    }

    async lancerTestComplet() {
        console.log('\n🧠 LANCEMENT TEST QI COMPLET');
        console.log('============================');

        const debut_test = Date.now();

        console.log(`📊 ${this.questions_qi.length} questions à poser`);
        console.log('⏱️ Timeout par question : 30 secondes');
        console.log('🎯 Score maximum : 100 points\n');

        for (let i = 0; i < this.questions_qi.length; i++) {
            const question = this.questions_qi[i];

            console.log(`\n--- Question ${i + 1}/${this.questions_qi.length} ---`);
            console.log(`📂 Catégorie: ${question.categorie}`);
            console.log(`🎯 Points: ${question.points}`);
            console.log(`❓ Question: ${question.question}`);

            const resultat = await this.poserQuestion(question);
            this.analyserReponse(question, resultat);

            // Pause entre questions
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        this.resultats.temps_total = Date.now() - debut_test;

        // Calculer QI final
        this.calculerQI();

        // Sauvegarder résultats
        this.sauvegarderResultats();

        // Afficher rapport final
        this.afficherRapportFinal();

        return this.resultats;
    }

    async poserQuestion(question) {
        console.log('🔥 Interrogation avec mémoire thermique...');

        try {
            // Utiliser agent thermique au lieu d'Ollama direct
            const resultat = await this.agent_thermique.poserQuestionThermique(question.question);

            if (resultat.succes) {
                console.log(`💬 Réponse thermique: "${resultat.reponse.substring(0, 100)}..."`);
                console.log(`⏱️ Temps: ${resultat.duree}ms`);
                console.log(`🧠 QI actuel: ${resultat.qi_actuel} (+${resultat.progression})`);

                return {
                    reponse: resultat.reponse.toLowerCase(),
                    reponse_brute: resultat.reponse,
                    duree: resultat.duree,
                    qi_actuel: resultat.qi_actuel,
                    progression: resultat.progression,
                    contexte: resultat.contexte_utilise,
                    succes: true
                };
            } else {
                return {
                    reponse: '',
                    reponse_brute: '',
                    duree: resultat.duree,
                    succes: false,
                    erreur: resultat.reponse
                };
            }

        } catch (error) {
            console.log(`❌ Erreur: ${error.message}`);

            return {
                reponse: '',
                reponse_brute: '',
                duree: 0,
                succes: false,
                erreur: error.message
            };
        }
    }

    analyserReponse(question, resultat) {
        console.log('📊 Analyse réponse...');

        let score = 0;
        let justification = '';

        if (!resultat.succes) {
            justification = 'Erreur technique';
        } else {
            const reponse = resultat.reponse;
            const attendue = question.reponse_attendue.toLowerCase();

            // Analyse selon le type de question
            if (question.categorie === 'Mathématiques') {
                if (reponse.includes(attendue)) {
                    score = question.points;
                    justification = 'Réponse exacte';
                } else {
                    // Chercher des nombres dans la réponse
                    const nombres = reponse.match(/\d+/g);
                    if (nombres && nombres.includes(attendue)) {
                        score = Math.floor(question.points * 0.8);
                        justification = 'Nombre correct trouvé';
                    } else {
                        justification = 'Réponse incorrecte';
                    }
                }
            } else if (question.categorie === 'Suites logiques') {
                if (reponse.includes(attendue)) {
                    score = question.points;
                    justification = 'Pattern identifié';
                } else {
                    justification = 'Pattern non identifié';
                }
            } else if (question.categorie === 'Raisonnement verbal') {
                if (reponse.includes(attendue) || this.verifierSynonyme(reponse, attendue)) {
                    score = question.points;
                    justification = 'Réponse appropriée';
                } else if (reponse.length > 0) {
                    score = Math.floor(question.points * 0.3);
                    justification = 'Réponse partielle';
                } else {
                    justification = 'Pas de réponse';
                }
            } else if (question.categorie === 'Logique spatiale') {
                if (reponse.includes(attendue)) {
                    score = question.points;
                    justification = 'Orientation correcte';
                } else {
                    justification = 'Orientation incorrecte';
                }
            } else if (question.categorie === 'Mémoire') {
                const mots_attendus = attendue.split(' ');
                const mots_trouves = mots_attendus.filter(mot => reponse.includes(mot));
                score = Math.floor((mots_trouves.length / mots_attendus.length) * question.points);
                justification = `${mots_trouves.length}/${mots_attendus.length} mots mémorisés`;
            } else if (question.categorie === 'Raisonnement abstrait') {
                if (reponse.length > 10) {
                    score = Math.floor(question.points * 0.7);
                    justification = 'Tentative de réponse abstraite';
                } else {
                    score = Math.floor(question.points * 0.3);
                    justification = 'Réponse trop courte';
                }
            }
        }

        // Bonus rapidité
        if (resultat.duree < 5000 && score > 0) {
            const bonus = Math.floor(score * 0.1);
            score += bonus;
            justification += ` (+${bonus} bonus rapidité)`;
        }

        console.log(`🎯 Score: ${score}/${question.points} - ${justification}`);

        // Enregistrer résultat
        this.resultats.reponses.push({
            question_id: question.id,
            question: question.question,
            reponse_attendue: question.reponse_attendue,
            reponse_donnee: resultat.reponse_brute,
            score: score,
            score_max: question.points,
            duree: resultat.duree,
            justification: justification,
            categorie: question.categorie
        });

        // Mettre à jour scores
        this.resultats.score_total += score;
        this.resultats.details_categories[question.categorie].score += score;
    }

    verifierSynonyme(reponse, attendue) {
        const synonymes = {
            'diminuer': ['reduire', 'baisser', 'decroitre'],
            'conduire': ['piloter', 'diriger', 'mener'],
            'même poids': ['egal', 'identique', 'pareil'],
            'table': ['meuble', 'objet'],
            'est': ['orient', 'levant'],
            'équité': ['justice', 'fairness', 'impartialite'],
            'profondeur': ['difference', 'nuance', 'niveau']
        };

        if (synonymes[attendue]) {
            return synonymes[attendue].some(syn => reponse.includes(syn));
        }

        return false;
    }

    calculerQI() {
        console.log('\n🧮 Calcul QI final...');

        const pourcentage = (this.resultats.score_total / this.resultats.score_max) * 100;

        // Formule QI standard : moyenne 100, écart-type 15
        // Score 0% = QI 70, Score 50% = QI 100, Score 100% = QI 130
        let qi = 70 + (pourcentage * 0.6);

        // Ajustements selon performance par catégorie
        const categories = Object.values(this.resultats.details_categories);
        const performance_equilibree = categories.every(cat =>
            (cat.score / cat.max) > 0.3
        );

        if (performance_equilibree) {
            qi += 5; // Bonus équilibre
        }

        // Bonus temps (réponses rapides)
        const temps_moyen = this.resultats.temps_total / this.questions_qi.length;
        if (temps_moyen < 8000) { // Moins de 8s par question
            qi += 3;
        }

        this.resultats.qi_estime = Math.round(qi);

        console.log(`📊 Score: ${this.resultats.score_total}/${this.resultats.score_max} (${pourcentage.toFixed(1)}%)`);
        console.log(`🧠 QI estimé: ${this.resultats.qi_estime}`);
    }

    afficherRapportFinal() {
        console.log('\n📋 RAPPORT FINAL TEST QI');
        console.log('========================');

        console.log(`\n🎯 RÉSULTATS GLOBAUX:`);
        console.log(`├── Score total: ${this.resultats.score_total}/${this.resultats.score_max}`);
        console.log(`├── Pourcentage: ${((this.resultats.score_total / this.resultats.score_max) * 100).toFixed(1)}%`);
        console.log(`├── QI estimé: ${this.resultats.qi_estime}`);
        console.log(`├── Temps total: ${(this.resultats.temps_total / 1000).toFixed(1)}s`);
        console.log(`└── Temps moyen/question: ${(this.resultats.temps_total / this.questions_qi.length / 1000).toFixed(1)}s`);

        console.log(`\n📊 DÉTAIL PAR CATÉGORIE:`);
        Object.entries(this.resultats.details_categories).forEach(([categorie, data]) => {
            const pourcentage = ((data.score / data.max) * 100).toFixed(1);
            console.log(`├── ${categorie}: ${data.score}/${data.max} (${pourcentage}%)`);
        });

        console.log(`\n🏆 CLASSIFICATION QI:`);
        if (this.resultats.qi_estime >= 130) {
            console.log('🥇 TRÈS SUPÉRIEUR (Top 2%)');
        } else if (this.resultats.qi_estime >= 120) {
            console.log('🥈 SUPÉRIEUR (Top 10%)');
        } else if (this.resultats.qi_estime >= 110) {
            console.log('🥉 AU-DESSUS MOYENNE (Top 25%)');
        } else if (this.resultats.qi_estime >= 90) {
            console.log('📊 MOYENNE (50%)');
        } else {
            console.log('📉 EN-DESSOUS MOYENNE');
        }

        console.log(`\n💡 POINTS FORTS:`);
        const meilleures = Object.entries(this.resultats.details_categories)
            .sort((a, b) => (b[1].score / b[1].max) - (a[1].score / a[1].max))
            .slice(0, 2);

        meilleures.forEach(([categorie, data]) => {
            const pourcentage = ((data.score / data.max) * 100).toFixed(1);
            console.log(`✅ ${categorie}: ${pourcentage}%`);
        });

        console.log(`\n🎯 AXES D'AMÉLIORATION:`);
        const plus_faibles = Object.entries(this.resultats.details_categories)
            .sort((a, b) => (a[1].score / a[1].max) - (b[1].score / b[1].max))
            .slice(0, 2);

        plus_faibles.forEach(([categorie, data]) => {
            const pourcentage = ((data.score / data.max) * 100).toFixed(1);
            console.log(`🔄 ${categorie}: ${pourcentage}%`);
        });
    }

    sauvegarderResultats() {
        try {
            const timestamp = Date.now();
            const nomFichier = `test_qi_${timestamp}.json`;
            const cheminFichier = path.join(this.config.resultats_path, nomFichier);

            fs.writeFileSync(cheminFichier, JSON.stringify(this.resultats, null, 2));

            console.log(`\n💾 Résultats sauvegardés: ${nomFichier}`);

        } catch (error) {
            console.log(`⚠️ Erreur sauvegarde: ${error.message}`);
        }
    }

    // TEST RAPIDE (5 questions)
    async lancerTestRapide() {
        console.log('\n🧠 TEST QI RAPIDE (5 QUESTIONS)');
        console.log('===============================');

        const questions_rapides = [
            this.questions_qi[0], // Math simple
            this.questions_qi[2], // Suite logique
            this.questions_qi[4], // Verbal
            this.questions_qi[8], // Spatial
            this.questions_qi[12] // Abstrait
        ];

        const debut_test = Date.now();

        for (let i = 0; i < questions_rapides.length; i++) {
            const question = questions_rapides[i];

            console.log(`\n--- Question ${i + 1}/5 ---`);
            console.log(`❓ ${question.question}`);

            const resultat = await this.poserQuestion(question);
            this.analyserReponse(question, resultat);
        }

        this.resultats.temps_total = Date.now() - debut_test;
        this.calculerQI();

        console.log(`\n🎯 QI RAPIDE ESTIMÉ: ${this.resultats.qi_estime}`);

        return this.resultats;
    }
}

// Export
module.exports = TestQIReel;

// Test automatique si exécuté directement
if (require.main === module) {
    console.log('🚀 LANCEMENT TEST QI RÉEL');
    console.log('=========================');

    const testQI = new TestQIReel();

    // Demander type de test
    console.log('\n🎯 Types de test disponibles:');
    console.log('1. Test complet (15 questions, ~10 minutes)');
    console.log('2. Test rapide (5 questions, ~3 minutes)');

    // Lancer test rapide par défaut
    setTimeout(async () => {
        console.log('\n🚀 Lancement test rapide...');
        await testQI.lancerTestRapide();
    }, 2000);
}
