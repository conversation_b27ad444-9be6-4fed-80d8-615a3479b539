/**
 * SYSTÈME DE RECHERCHE GOOGLE INTELLIGENTE POUR LOUNA-AI
 * Remplace le système Wikipedia par de vraies recherches Google
 * Similaire à Perplexity avec sources multiples et réponses détaillées
 */

const https = require('https');
const { URL } = require('url');

class RechercheGoogleIntelligente {
    constructor() {
        this.userAgent = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
        this.sourcesPrioritaires = [
            'stackoverflow.com',
            'github.com',
            'developer.mozilla.org',
            'docs.microsoft.com',
            'python.org',
            'nodejs.org',
            'reactjs.org',
            'vuejs.org',
            'angular.io',
            'medium.com',
            'dev.to',
            'techcrunch.com',
            'arstechnica.com',
            'wired.com',
            'nature.com',
            'science.org',
            'ieee.org',
            'acm.org'
        ];
        this.cache = new Map();
        this.maxCacheSize = 100;
    }

    // RECHERCHE GOOGLE AVEC SCRAPING INTELLIGENT
    async rechercherGoogle(query, options = {}) {
        const {
            nombreResultats = 10,
            langue = 'fr',
            region = 'FR',
            includeSnippets = true,
            filtrerSources = true
        } = options;

        try {
            console.log(`🔍 Recherche Google: "${query}"`);
            
            // Vérifier le cache
            const cacheKey = `${query}_${nombreResultats}_${langue}`;
            if (this.cache.has(cacheKey)) {
                console.log('📋 Résultat trouvé en cache');
                return this.cache.get(cacheKey);
            }

            // Construire l'URL de recherche Google
            const searchUrl = this.construireUrlRecherche(query, {
                num: nombreResultats,
                hl: langue,
                gl: region
            });

            // Effectuer la recherche
            const resultatsGoogle = await this.effectuerRequeteGoogle(searchUrl);
            
            // Parser les résultats
            const resultatsParses = this.parserResultatsGoogle(resultatsGoogle);
            
            // Filtrer et enrichir les résultats
            let resultatsFinaux = resultatsParses;
            
            if (filtrerSources) {
                resultatsFinaux = this.filtrerSourcesQualite(resultatsParses);
            }
            
            if (includeSnippets) {
                resultatsFinaux = await this.enrichirAvecSnippets(resultatsFinaux);
            }

            // Générer une réponse synthétique
            const reponseSynthetique = await this.genererReponseSynthetique(query, resultatsFinaux);

            const resultatFinal = {
                query: query,
                timestamp: Date.now(),
                nombreResultats: resultatsFinaux.length,
                reponse: reponseSynthetique,
                sources: resultatsFinaux,
                qualiteRecherche: this.evaluerQualiteRecherche(resultatsFinaux)
            };

            // Mettre en cache
            this.ajouterAuCache(cacheKey, resultatFinal);

            return resultatFinal;

        } catch (error) {
            console.error('❌ Erreur recherche Google:', error.message);
            return this.genererReponseErreur(query, error);
        }
    }

    // CONSTRUIRE URL DE RECHERCHE GOOGLE
    construireUrlRecherche(query, params) {
        const baseUrl = 'https://www.google.com/search';
        const searchParams = new URLSearchParams({
            q: query,
            num: params.num || 10,
            hl: params.hl || 'fr',
            gl: params.gl || 'FR',
            safe: 'active',
            filter: '0'
        });

        return `${baseUrl}?${searchParams.toString()}`;
    }

    // EFFECTUER REQUÊTE GOOGLE
    async effectuerRequeteGoogle(url) {
        return new Promise((resolve, reject) => {
            const options = {
                headers: {
                    'User-Agent': this.userAgent,
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'DNT': '1',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1'
                }
            };

            https.get(url, options, (response) => {
                let data = '';
                
                response.on('data', (chunk) => {
                    data += chunk;
                });
                
                response.on('end', () => {
                    if (response.statusCode === 200) {
                        resolve(data);
                    } else {
                        reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
                    }
                });
            }).on('error', (error) => {
                reject(error);
            });
        });
    }

    // PARSER RÉSULTATS GOOGLE
    parserResultatsGoogle(html) {
        const resultats = [];
        
        try {
            // Regex pour extraire les résultats de recherche Google
            const regexResultats = /<div class="g"[^>]*>.*?<h3[^>]*>.*?<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>.*?<\/h3>.*?<span[^>]*>(.*?)<\/span>/gs;
            const regexSnippet = /<span class="st"[^>]*>(.*?)<\/span>/gs;
            
            let match;
            let index = 0;
            
            while ((match = regexResultats.exec(html)) !== null && index < 15) {
                const url = this.nettoyerUrl(match[1]);
                const titre = this.nettoyerTexte(match[2]);
                const domaine = this.extraireDomaine(url);
                
                // Chercher le snippet correspondant
                const snippetMatch = regexSnippet.exec(html);
                const snippet = snippetMatch ? this.nettoyerTexte(snippetMatch[1]) : '';
                
                if (url && titre && this.estUrlValide(url)) {
                    resultats.push({
                        id: index + 1,
                        titre: titre,
                        url: url,
                        domaine: domaine,
                        snippet: snippet,
                        score: this.calculerScoreSource(domaine),
                        timestamp: Date.now()
                    });
                    index++;
                }
            }
            
            // Si pas assez de résultats avec la première regex, essayer une alternative
            if (resultats.length < 3) {
                const regexAlternative = /<a[^>]*href="\/url\?q=([^&"]*)"[^>]*><h3[^>]*>(.*?)<\/h3>/gs;
                let matchAlt;
                
                while ((matchAlt = regexAlternative.exec(html)) !== null && resultats.length < 10) {
                    const url = decodeURIComponent(matchAlt[1]);
                    const titre = this.nettoyerTexte(matchAlt[2]);
                    const domaine = this.extraireDomaine(url);
                    
                    if (url && titre && this.estUrlValide(url) && !resultats.find(r => r.url === url)) {
                        resultats.push({
                            id: resultats.length + 1,
                            titre: titre,
                            url: url,
                            domaine: domaine,
                            snippet: '',
                            score: this.calculerScoreSource(domaine),
                            timestamp: Date.now()
                        });
                    }
                }
            }
            
        } catch (error) {
            console.error('❌ Erreur parsing Google:', error.message);
        }
        
        return resultats.sort((a, b) => b.score - a.score);
    }

    // FILTRER SOURCES DE QUALITÉ
    filtrerSourcesQualite(resultats) {
        return resultats.filter(resultat => {
            // Exclure les sources de faible qualité
            const domainesExclus = [
                'pinterest.com',
                'facebook.com',
                'twitter.com',
                'instagram.com',
                'tiktok.com',
                'spam-site.com'
            ];
            
            const estExclu = domainesExclus.some(domaine => 
                resultat.domaine.includes(domaine)
            );
            
            // Privilégier les sources techniques et académiques
            const estSourcePrioritaire = this.sourcesPrioritaires.some(domaine =>
                resultat.domaine.includes(domaine)
            );
            
            return !estExclu && (estSourcePrioritaire || resultat.score > 0.3);
        });
    }

    // ENRICHIR AVEC SNIPPETS
    async enrichirAvecSnippets(resultats) {
        const resultatsEnrichis = [];
        
        for (const resultat of resultats.slice(0, 5)) { // Limiter à 5 pour éviter la surcharge
            try {
                const contenu = await this.extraireContenuPage(resultat.url);
                resultatsEnrichis.push({
                    ...resultat,
                    contenuExtrait: contenu.substring(0, 500),
                    motsCles: this.extraireMotsCles(contenu)
                });
            } catch (error) {
                console.log(`⚠️ Impossible d'extraire le contenu de ${resultat.url}`);
                resultatsEnrichis.push(resultat);
            }
        }
        
        return resultatsEnrichis;
    }

    // GÉNÉRER RÉPONSE SYNTHÉTIQUE
    async genererReponseSynthetique(query, resultats) {
        if (resultats.length === 0) {
            return `❌ Aucun résultat trouvé pour "${query}". Essayez de reformuler votre recherche.`;
        }

        const sourcesPrincipales = resultats.slice(0, 3);
        let reponse = `🔍 **Recherche: "${query}"**\n\n`;
        
        // Résumé basé sur les meilleures sources
        reponse += `📊 **Résumé (basé sur ${resultats.length} sources):**\n`;
        
        if (sourcesPrincipales.length > 0) {
            const snippetsPrincipaux = sourcesPrincipales
                .filter(s => s.snippet || s.contenuExtrait)
                .map(s => s.snippet || s.contenuExtrait)
                .join(' ');
            
            const resumeIntelligent = this.genererResumeIntelligent(query, snippetsPrincipaux);
            reponse += `${resumeIntelligent}\n\n`;
        }
        
        // Sources principales
        reponse += `📚 **Sources principales:**\n`;
        sourcesPrincipales.forEach((source, index) => {
            reponse += `${index + 1}. **${source.titre}**\n`;
            reponse += `   🔗 ${source.url}\n`;
            if (source.snippet) {
                reponse += `   📝 ${source.snippet.substring(0, 150)}...\n`;
            }
            reponse += `\n`;
        });
        
        // Suggestions de recherches connexes
        const suggestionsConnexes = this.genererSuggestionsConnexes(query);
        if (suggestionsConnexes.length > 0) {
            reponse += `💡 **Recherches connexes suggérées:**\n`;
            suggestionsConnexes.forEach(suggestion => {
                reponse += `• ${suggestion}\n`;
            });
        }
        
        return reponse;
    }

    // GÉNÉRER RÉSUMÉ INTELLIGENT
    genererResumeIntelligent(query, contenu) {
        // Analyse simple du contenu pour générer un résumé pertinent
        const phrases = contenu.split(/[.!?]+/).filter(p => p.trim().length > 20);
        const phrasesRelevantes = phrases.filter(phrase => {
            const motsClesQuery = query.toLowerCase().split(' ');
            return motsClesQuery.some(mot => phrase.toLowerCase().includes(mot));
        });
        
        if (phrasesRelevantes.length > 0) {
            return phrasesRelevantes.slice(0, 2).join('. ') + '.';
        } else if (phrases.length > 0) {
            return phrases.slice(0, 2).join('. ') + '.';
        } else {
            return 'Informations trouvées dans les sources listées ci-dessous.';
        }
    }

    // UTILITAIRES
    nettoyerUrl(url) {
        try {
            // Nettoyer les URLs Google
            if (url.startsWith('/url?q=')) {
                const match = url.match(/\/url\?q=([^&]*)/);
                if (match) {
                    return decodeURIComponent(match[1]);
                }
            }
            return url.startsWith('http') ? url : `https://${url}`;
        } catch (error) {
            return url;
        }
    }

    nettoyerTexte(texte) {
        return texte
            .replace(/<[^>]*>/g, '') // Supprimer HTML
            .replace(/&[^;]+;/g, ' ') // Supprimer entités HTML
            .replace(/\s+/g, ' ') // Normaliser espaces
            .trim();
    }

    extraireDomaine(url) {
        try {
            return new URL(url).hostname.replace('www.', '');
        } catch (error) {
            return url;
        }
    }

    estUrlValide(url) {
        try {
            new URL(url);
            return !url.includes('google.com') && !url.includes('youtube.com/redirect');
        } catch (error) {
            return false;
        }
    }

    calculerScoreSource(domaine) {
        // Score basé sur la qualité de la source
        if (this.sourcesPrioritaires.includes(domaine)) {
            return 1.0;
        }
        
        const scoresDomaines = {
            '.edu': 0.9,
            '.org': 0.8,
            '.gov': 0.9,
            'wikipedia.org': 0.7,
            'reddit.com': 0.6,
            'quora.com': 0.5
        };
        
        for (const [pattern, score] of Object.entries(scoresDomaines)) {
            if (domaine.includes(pattern)) {
                return score;
            }
        }
        
        return 0.4; // Score par défaut
    }

    genererSuggestionsConnexes(query) {
        const suggestions = [];
        const mots = query.toLowerCase().split(' ');
        
        // Suggestions basées sur le contexte
        if (mots.some(m => ['javascript', 'js', 'node'].includes(m))) {
            suggestions.push('JavaScript frameworks 2024', 'Node.js best practices', 'JavaScript performance optimization');
        }
        
        if (mots.some(m => ['python', 'py'].includes(m))) {
            suggestions.push('Python libraries 2024', 'Python machine learning', 'Python web development');
        }
        
        if (mots.some(m => ['ai', 'intelligence', 'artificielle'].includes(m))) {
            suggestions.push('AI trends 2024', 'Machine learning algorithms', 'AI ethics');
        }
        
        return suggestions.slice(0, 3);
    }

    // GESTION DU CACHE
    ajouterAuCache(key, value) {
        if (this.cache.size >= this.maxCacheSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
        this.cache.set(key, value);
    }

    // EXTRACTION DE CONTENU (simplifié)
    async extraireContenuPage(url) {
        // Version simplifiée - dans un vrai système, utiliser un parser HTML complet
        try {
            const html = await this.effectuerRequeteGoogle(url);
            return this.nettoyerTexte(html).substring(0, 1000);
        } catch (error) {
            return '';
        }
    }

    extraireMotsCles(contenu) {
        const mots = contenu.toLowerCase().split(/\W+/);
        const motsClesFrequents = {};
        
        mots.forEach(mot => {
            if (mot.length > 3) {
                motsClesFrequents[mot] = (motsClesFrequents[mot] || 0) + 1;
            }
        });
        
        return Object.entries(motsClesFrequents)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10)
            .map(([mot]) => mot);
    }

    evaluerQualiteRecherche(resultats) {
        if (resultats.length === 0) return 0;
        
        const scoresMoyens = resultats.reduce((sum, r) => sum + r.score, 0) / resultats.length;
        const diversiteSources = new Set(resultats.map(r => r.domaine)).size;
        
        return Math.min(1.0, (scoresMoyens * 0.7) + (diversiteSources / 10 * 0.3));
    }

    genererReponseErreur(query, error) {
        return {
            query: query,
            timestamp: Date.now(),
            nombreResultats: 0,
            reponse: `❌ **Erreur lors de la recherche "${query}"**\n\nErreur: ${error.message}\n\n💡 Suggestions:\n• Vérifiez votre connexion internet\n• Reformulez votre recherche\n• Essayez des mots-clés plus spécifiques`,
            sources: [],
            qualiteRecherche: 0,
            erreur: true
        };
    }
}

module.exports = RechercheGoogleIntelligente;
