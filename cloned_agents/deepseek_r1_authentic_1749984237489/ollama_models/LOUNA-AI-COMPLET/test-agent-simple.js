#!/usr/bin/env node

/**
 * TEST SIMPLE DE L'AGENT LOUNA-AI
 * ================================
 * Test direct et simple pour vérifier que l'agent répond bien
 */

const axios = require('axios');

async function testerAgentSimple() {
    console.log('🧪 TEST SIMPLE DE L\'AGENT LOUNA-AI');
    console.log('===================================\n');

    const questions = [
        "Bonjour LOUNA",
        "Comment allez-vous ?",
        "Quelle est la capitale de la France ?",
        "Combien font 2 + 2 ?",
        "Quel est votre nom ?"
    ];

    for (let i = 0; i < questions.length; i++) {
        const question = questions[i];
        console.log(`\n📝 Question ${i + 1}: "${question}"`);
        
        try {
            const debut = Date.now();
            
            const response = await axios.post('http://localhost:3001/api/chat', {
                message: question,
                conversationId: 'test-simple-' + Date.now()
            }, {
                timeout: 15000,
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const duree = Date.now() - debut;
            const reponse = response.data.response || response.data.message || 'Pas de réponse';
            
            console.log(`✅ Réponse (${duree}ms):`);
            console.log(`"${reponse}"`);
            
            if (reponse && reponse.trim().length > 0) {
                console.log(`🎉 SUCCÈS - L'agent répond correctement !`);
            } else {
                console.log(`❌ ÉCHEC - Réponse vide ou invalide`);
            }
            
        } catch (error) {
            console.log(`❌ ERREUR: ${error.message}`);
            
            if (error.code === 'ECONNREFUSED') {
                console.log('🔌 Le serveur n\'est pas accessible sur le port 3001');
            } else if (error.code === 'ETIMEDOUT') {
                console.log('⏰ Timeout - L\'agent met trop de temps à répondre');
            }
        }
        
        console.log('─'.repeat(60));
        
        // Pause entre les questions
        if (i < questions.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }

    console.log('\n🏁 TEST TERMINÉ');
}

// Exécution du test
testerAgentSimple().catch(console.error);
