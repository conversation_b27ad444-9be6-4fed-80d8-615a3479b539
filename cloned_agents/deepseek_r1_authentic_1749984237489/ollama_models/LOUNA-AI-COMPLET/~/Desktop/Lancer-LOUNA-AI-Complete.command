#!/bin/bash

# 🚀 LANCEUR LOUNA-AI INTERFACE COMPLÈTE
# Script pour démarrer LOUNA-AI avec l'interface la plus avancée

echo "🤖 LANCEMENT LOUNA-AI INTERFACE COMPLÈTE"
echo "========================================"

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Fonction d'affichage coloré
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${PURPLE}🧠 $1${NC}"
}

# Vérification des prérequis
print_header "Vérification des prérequis..."

# Vérifier Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js n'est pas installé"
    exit 1
fi
print_status "Node.js détecté: $(node --version)"

# Aller dans le répertoire LOUNA-AI
LOUNA_DIR="/Volumes/ALDO et MIM/LOUNA-AI-COMPLET"
if [ ! -d "$LOUNA_DIR" ]; then
    print_error "Répertoire LOUNA-AI non trouvé: $LOUNA_DIR"
    exit 1
fi

cd "$LOUNA_DIR"
print_status "Répertoire LOUNA-AI: $LOUNA_DIR"

# Vérifier les fichiers nécessaires
if [ ! -f "serveur-interface-complete.js" ]; then
    print_error "Serveur interface complète non trouvé"
    exit 1
fi

if [ ! -f "interface-louna-complete.html" ]; then
    print_error "Interface complète non trouvée"
    exit 1
fi

print_status "Tous les fichiers nécessaires sont présents"

# Arrêter les processus existants
print_info "Arrêt des processus LOUNA-AI existants..."
pkill -f "serveur-interface-complete.js" 2>/dev/null
pkill -f "serveur-louna" 2>/dev/null
sleep 2

# Démarrer le serveur
print_header "Démarrage du serveur LOUNA-AI..."
node serveur-interface-complete.js &
SERVER_PID=$!

# Attendre que le serveur démarre
print_info "Attente du démarrage du serveur..."
sleep 5

# Vérifier que le serveur fonctionne
if curl -s http://localhost:3000/api/status > /dev/null; then
    print_status "Serveur LOUNA-AI démarré avec succès (PID: $SERVER_PID)"
else
    print_error "Échec du démarrage du serveur"
    kill $SERVER_PID 2>/dev/null
    exit 1
fi

# Ouvrir l'interface dans le navigateur
print_header "Ouverture de l'interface LOUNA-AI..."
open "http://localhost:3000/interface-louna-complete.html"

print_status "Interface LOUNA-AI ouverte dans le navigateur"

# Afficher les informations
echo ""
print_header "🎉 LOUNA-AI INTERFACE COMPLÈTE LANCÉE !"
echo ""
print_info "🌐 URL: http://localhost:3000/interface-louna-complete.html"
print_info "📊 Caractéristiques:"
echo "   • QI Actuel: 320"
echo "   • Mémoires: 42"
echo "   • Température: 67.43°C"
echo "   • Zone Active: Zone 5"
echo "   • Applications détectées: 414+"
echo ""
print_info "🎮 Fonctionnalités disponibles:"
echo "   • Chat intelligent avec LOUNA-AI"
echo "   • Scan et contrôle d'applications"
echo "   • Monitoring temps réel"
echo "   • Interface rose/magenta avancée"
echo ""
print_warning "Pour arrêter LOUNA-AI, fermez cette fenêtre ou appuyez sur Ctrl+C"

# Garder le script ouvert
echo ""
print_info "Serveur en cours d'exécution... (PID: $SERVER_PID)"
echo "Appuyez sur Ctrl+C pour arrêter"

# Fonction de nettoyage
cleanup() {
    echo ""
    print_info "Arrêt de LOUNA-AI..."
    kill $SERVER_PID 2>/dev/null
    print_status "LOUNA-AI arrêté"
    exit 0
}

# Capturer Ctrl+C
trap cleanup INT

# Attendre indéfiniment
while true; do
    sleep 1
    # Vérifier si le serveur fonctionne toujours
    if ! kill -0 $SERVER_PID 2>/dev/null; then
        print_error "Le serveur LOUNA-AI s'est arrêté de manière inattendue"
        exit 1
    fi
done
