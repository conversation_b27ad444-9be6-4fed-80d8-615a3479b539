/**
 * TEST QI LOCAL FONCTIONNEL
 * Version qui fonctionne en local puis copie sur USB
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class TestQILocalFonctionnel {
    constructor() {
        console.log('🧠 TEST QI LOCAL FONCTIONNEL');
        console.log('============================');
        
        // Chemins locaux d'abord
        this.cheminLocal = './test-qi-local';
        this.cheminUSB = '/Volumes/LounaAI_V3/AGENTS-REELS';
        
        // Tests QI simples mais efficaces
        this.testsQI = [
            {
                question: 'Combien font 2 + 2?',
                reponseAttendue: '4',
                type: 'calcul_simple',
                points: 20
            },
            {
                question: 'Quelle est la capitale de la France?',
                reponseAttendue: 'Paris',
                type: 'connaissance',
                points: 15
            },
            {
                question: 'Complétez: 2, 4, 8, 16, ?',
                reponseAttendue: '32',
                type: 'logique',
                points: 25
            },
            {
                question: 'Si A > B et B > C, alors A ? C',
                reponseAttendue: '>',
                type: 'logique_avancee',
                points: 30
            },
            {
                question: 'Quel animal fait "miaou"?',
                reponseAttendue: 'chat',
                type: 'association',
                points: 10
            }
        ];
        
        this.resultats = {
            tests_effectues: 0,
            points_obtenus: 0,
            points_maximum: 100,
            reponses: [],
            qi_calcule: 0,
            agent_fonctionne: false
        };
    }

    async initialiser() {
        console.log('🔧 Initialisation test QI...');
        
        // Créer dossier local
        if (!fs.existsSync(this.cheminLocal)) {
            fs.mkdirSync(this.cheminLocal, { recursive: true });
            console.log(`📁 Dossier local créé: ${this.cheminLocal}`);
        }
        
        // Tester accès USB
        try {
            if (fs.existsSync(this.cheminUSB)) {
                console.log('✅ Accès USB confirmé');
            } else {
                console.log('⚠️ USB non accessible, tests en local uniquement');
            }
        } catch (error) {
            console.log('⚠️ Problème accès USB');
        }
    }

    async testerAgentLocal() {
        console.log('\n🤖 TEST AGENT LOCAL');
        console.log('===================');
        
        // D'abord tester si Ollama est installé localement
        try {
            const ollamaVersion = execSync('ollama --version', { encoding: 'utf8', timeout: 5000 });
            console.log(`✅ Ollama local détecté: ${ollamaVersion.trim()}`);
            
            // Tester avec agent local
            const reponse = execSync('ollama run llama3.2:1b "Test simple: 2+2=?"', { 
                encoding: 'utf8', 
                timeout: 15000 
            });
            
            console.log(`🤖 Réponse agent local: "${reponse.trim()}"`);
            this.resultats.agent_fonctionne = true;
            return 'local';
            
        } catch (error) {
            console.log('⚠️ Ollama local non disponible');
        }
        
        // Tester agent USB
        try {
            const cheminOllamaUSB = path.join(this.cheminUSB, 'ollama', 'ollama');
            
            if (fs.existsSync(cheminOllamaUSB)) {
                console.log('🔍 Agent USB trouvé, test...');
                
                const reponse = execSync(`${cheminOllamaUSB} run llama3.2:1b "Test: 1+1=?"`, {
                    encoding: 'utf8',
                    timeout: 20000,
                    env: {
                        ...process.env,
                        OLLAMA_MODELS: path.join(this.cheminUSB, 'ollama', 'models-reels')
                    }
                });
                
                console.log(`🤖 Réponse agent USB: "${reponse.trim()}"`);
                this.resultats.agent_fonctionne = true;
                return 'usb';
                
            } else {
                console.log('❌ Agent USB non trouvé');
            }
            
        } catch (error) {
            console.log(`⚠️ Erreur agent USB: ${error.message}`);
        }
        
        return null;
    }

    async executerTestsQI(typeAgent) {
        console.log('\n🧪 EXÉCUTION TESTS QI RÉELS');
        console.log('===========================');
        
        if (!this.resultats.agent_fonctionne) {
            console.log('❌ Aucun agent fonctionnel trouvé');
            return this.simulerTestsQI();
        }
        
        const commandeBase = typeAgent === 'local' ? 'ollama run llama3.2:1b' : 
                            `${path.join(this.cheminUSB, 'ollama', 'ollama')} run llama3.2:1b`;
        
        for (let i = 0; i < this.testsQI.length; i++) {
            const test = this.testsQI[i];
            console.log(`\n📝 Test ${i+1}/${this.testsQI.length}: ${test.type.toUpperCase()}`);
            console.log(`❓ Question: ${test.question}`);
            
            try {
                const debut = Date.now();
                
                const reponse = execSync(`${commandeBase} "${test.question}"`, {
                    encoding: 'utf8',
                    timeout: 25000,
                    env: typeAgent === 'usb' ? {
                        ...process.env,
                        OLLAMA_MODELS: path.join(this.cheminUSB, 'ollama', 'models-reels')
                    } : process.env
                });
                
                const duree = Date.now() - debut;
                const reponseAgent = reponse.trim();
                
                console.log(`🤖 Réponse (${duree}ms): "${reponseAgent}"`);
                
                // Analyser réponse
                const correct = this.analyserReponse(test, reponseAgent);
                const pointsObtenus = correct ? test.points : 0;
                
                console.log(`${correct ? '✅' : '❌'} ${correct ? 'CORRECT' : 'INCORRECT'} - ${pointsObtenus}/${test.points} points`);
                
                this.resultats.tests_effectues++;
                this.resultats.points_obtenus += pointsObtenus;
                this.resultats.reponses.push({
                    question: test.question,
                    reponse: reponseAgent,
                    correct: correct,
                    points: pointsObtenus,
                    duree: duree,
                    type: test.type
                });
                
            } catch (error) {
                console.log(`❌ Erreur: ${error.message}`);
                this.resultats.reponses.push({
                    question: test.question,
                    reponse: 'ERREUR',
                    correct: false,
                    points: 0,
                    duree: 0,
                    type: test.type,
                    erreur: error.message
                });
            }
        }
        
        return this.calculerQI();
    }

    analyserReponse(test, reponse) {
        const reponseNormalisee = reponse.toLowerCase().trim();
        const attendueNormalisee = test.reponseAttendue.toLowerCase();
        
        // Analyse spécifique par type
        switch (test.type) {
            case 'calcul_simple':
                return reponseNormalisee.includes(attendueNormalisee);
                
            case 'connaissance':
                return reponseNormalisee.includes(attendueNormalisee);
                
            case 'logique':
                return reponseNormalisee.includes(attendueNormalisee);
                
            case 'logique_avancee':
                return reponseNormalisee.includes('>') || 
                       reponseNormalisee.includes('supérieur') ||
                       reponseNormalisee.includes('plus grand');
                       
            case 'association':
                return reponseNormalisee.includes(attendueNormalisee);
                
            default:
                return false;
        }
    }

    simulerTestsQI() {
        console.log('\n⚠️ SIMULATION TESTS QI (AGENT NON DISPONIBLE)');
        console.log('==============================================');
        console.log('🚨 ATTENTION: Ces résultats sont SIMULÉS');
        console.log('🎯 Pour de vrais tests, l\'agent doit fonctionner');
        
        // Simulation basique pour démonstration
        this.testsQI.forEach((test, index) => {
            console.log(`\n📝 Test ${index+1}: ${test.type.toUpperCase()}`);
            console.log(`❓ ${test.question}`);
            console.log(`🤖 Réponse simulée: "${test.reponseAttendue}"`);
            console.log(`✅ SIMULÉ CORRECT - ${test.points}/${test.points} points`);
            
            this.resultats.tests_effectues++;
            this.resultats.points_obtenus += test.points;
            this.resultats.reponses.push({
                question: test.question,
                reponse: `SIMULÉ: ${test.reponseAttendue}`,
                correct: true,
                points: test.points,
                duree: 1000,
                type: test.type,
                simule: true
            });
        });
        
        return this.calculerQI();
    }

    calculerQI() {
        const pourcentageReussite = (this.resultats.points_obtenus / this.resultats.points_maximum) * 100;
        
        // Calcul QI standard
        let qi = 85; // Base
        
        if (pourcentageReussite >= 90) qi = 130;      // Très supérieur
        else if (pourcentageReussite >= 80) qi = 120; // Supérieur  
        else if (pourcentageReussite >= 70) qi = 110; // Au-dessus moyenne
        else if (pourcentageReussite >= 50) qi = 100; // Moyenne
        else if (pourcentageReussite >= 30) qi = 90;  // En-dessous moyenne
        
        this.resultats.qi_calcule = qi;
        return qi;
    }

    afficherResultatsFinaux() {
        console.log('\n🧠 RÉSULTATS FINAUX TEST QI');
        console.log('============================');
        
        const typeTest = this.resultats.agent_fonctionne ? 'RÉEL' : 'SIMULÉ';
        console.log(`🎯 Type de test: ${typeTest}`);
        
        if (!this.resultats.agent_fonctionne) {
            console.log('🚨 ATTENTION: Résultats simulés - agent non fonctionnel');
        }
        
        console.log(`✅ Tests effectués: ${this.resultats.tests_effectues}/${this.testsQI.length}`);
        console.log(`📊 Points obtenus: ${this.resultats.points_obtenus}/${this.resultats.points_maximum}`);
        console.log(`📈 Taux réussite: ${(this.resultats.points_obtenus/this.resultats.points_maximum*100).toFixed(1)}%`);
        console.log(`🧠 QI ${typeTest}: ${this.resultats.qi_calcule}`);
        
        // Classification
        console.log('\n📋 CLASSIFICATION QI:');
        if (this.resultats.qi_calcule >= 130) {
            console.log('🌟 TRÈS SUPÉRIEUR (130+)');
        } else if (this.resultats.qi_calcule >= 120) {
            console.log('🎓 SUPÉRIEUR (120-129)');
        } else if (this.resultats.qi_calcule >= 110) {
            console.log('✅ AU-DESSUS MOYENNE (110-119)');
        } else if (this.resultats.qi_calcule >= 90) {
            console.log('📊 MOYENNE (90-109)');
        } else {
            console.log('⚠️ EN DESSOUS MOYENNE (<90)');
        }
        
        // Détail par test
        console.log('\n📝 DÉTAIL PAR TEST:');
        this.resultats.reponses.forEach((resultat, index) => {
            const statut = resultat.correct ? '✅' : '❌';
            const simule = resultat.simule ? ' (SIMULÉ)' : '';
            console.log(`   ${index+1}. ${resultat.type}: ${statut} ${resultat.points} pts${simule}`);
        });
        
        // Sauvegarder résultats
        this.sauvegarderResultats();
        
        return this.resultats;
    }

    sauvegarderResultats() {
        try {
            const resultatsComplets = {
                ...this.resultats,
                timestamp: Date.now(),
                date_test: new Date().toISOString(),
                type_test: this.resultats.agent_fonctionne ? 'reel' : 'simule',
                avertissement: this.resultats.agent_fonctionne ? null : 'RÉSULTATS SIMULÉS - AGENT NON FONCTIONNEL'
            };
            
            // Sauvegarder en local
            const cheminLocal = path.join(this.cheminLocal, 'resultats_qi.json');
            fs.writeFileSync(cheminLocal, JSON.stringify(resultatsComplets, null, 2));
            console.log(`\n💾 Résultats sauvegardés: ${cheminLocal}`);
            
            // Tenter sauvegarde USB
            try {
                if (fs.existsSync(this.cheminUSB)) {
                    const cheminUSB = path.join(this.cheminUSB, 'resultats_qi.json');
                    fs.writeFileSync(cheminUSB, JSON.stringify(resultatsComplets, null, 2));
                    console.log(`💾 Résultats copiés sur USB: ${cheminUSB}`);
                }
            } catch (error) {
                console.log('⚠️ Impossible de sauvegarder sur USB');
            }
            
        } catch (error) {
            console.log(`⚠️ Erreur sauvegarde: ${error.message}`);
        }
    }

    async executerTestComplet() {
        await this.initialiser();
        
        const typeAgent = await this.testerAgentLocal();
        
        if (typeAgent) {
            console.log(`✅ Agent ${typeAgent} fonctionnel`);
            await this.executerTestsQI(typeAgent);
        } else {
            console.log('❌ Aucun agent fonctionnel - simulation');
            this.simulerTestsQI();
        }
        
        return this.afficherResultatsFinaux();
    }
}

// Export
module.exports = TestQILocalFonctionnel;

// Exécution automatique
if (require.main === module) {
    console.log('🚀 LANCEMENT TEST QI COMPLET');
    console.log('============================');
    
    const test = new TestQILocalFonctionnel();
    
    test.executerTestComplet()
        .then(resultats => {
            console.log('\n🏁 TEST QI TERMINÉ');
            
            if (resultats.agent_fonctionne) {
                console.log('✅ Tests avec agent réel réussis');
            } else {
                console.log('⚠️ Tests simulés - vérifiez votre agent');
            }
        })
        .catch(error => {
            console.error('❌ Erreur test QI:', error.message);
        });
}
