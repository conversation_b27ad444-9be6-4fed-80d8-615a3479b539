/**
 * Serveur Luna - Interface cognitive avancée pour DeepSeek r1
 * Version simplifiée pour tester la mémoire thermique
 */

const express = require('express');
const http = require('http');
const path = require('path');
const fs = require('fs');

// Configuration
const app = express();
const server = http.createServer(app);
const PORT = process.env.PORT || 3001;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// Configuration du moteur de template
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Créer les dossiers nécessaires s'ils n'existent pas
const MEMORY_DIR = path.join(__dirname, 'data/memory');
if (!fs.existsSync(MEMORY_DIR)) {
  fs.mkdirSync(MEMORY_DIR, { recursive: true });
}

// Charger les services
const ThermalMemory = require('./services/thermal-memory');
const BrainPresence = require('./services/brain-presence');

// Initialiser la mémoire thermique
const thermalMemory = new ThermalMemory(path.join(__dirname, 'data/memory/thermal_memory.json'));
console.log('Mémoire thermique initialisée');

// Initialiser le service de présence cérébrale
const brainPresence = new BrainPresence(thermalMemory);
console.log('Service de présence cérébrale initialisé');

// Activer le service de présence cérébrale
brainPresence.initialize();
console.log('Service de présence cérébrale activé');

// Route principale
app.get('/luna', (req, res) => {
  res.render('luna-chat', {
    title: 'Luna - Interface Cognitive',
    page: 'chat'
  });
});

// Route pour la page d'accueil
app.get('/luna/home', (req, res) => {
  res.render('luna-home', {
    title: 'Luna - Accueil',
    page: 'home'
  });
});

// Route pour la page de chat
app.get('/luna/chat', (req, res) => {
  res.render('luna-chat', {
    title: 'Luna - Chat',
    page: 'chat'
  });
});

// Route pour la page de mémoire
app.get('/luna/memory', (req, res) => {
  res.render('luna-memory', {
    title: 'Luna - Mémoire',
    page: 'memory'
  });
});

// Route pour la page de formation
app.get('/luna/training', (req, res) => {
  res.render('luna-training', {
    title: 'Luna - Formation',
    page: 'training'
  });
});

// Route pour la page de code
app.get('/luna/code', (req, res) => {
  res.render('luna-code', {
    title: 'Luna - Code',
    page: 'code'
  });
});

// Route pour la page de sécurité
app.get('/luna/security', (req, res) => {
  res.render('luna-security', {
    title: 'Luna - Sécurité',
    page: 'security',
    systemStatus: {
      active: true,
      version: '1.0.0'
    }
  });
});

// Route pour la page de sauvegarde
app.get('/luna/backup', (req, res) => {
  res.render('luna-backup', {
    title: 'Luna - Sauvegarde',
    page: 'backup'
  });
});

// Route pour la page de surveillance
app.get('/luna/monitor', (req, res) => {
  res.render('luna-monitor', {
    title: 'Luna - Surveillance',
    page: 'monitor'
  });
});

// Route pour la page des accélérateurs
app.get('/luna/accelerators', (req, res) => {
  res.render('luna-accelerators', {
    title: 'Luna - Accélérateurs',
    page: 'accelerators'
  });
});

// Route pour la page des statistiques
app.get('/luna/stats', (req, res) => {
  res.render('luna-stats', {
    title: 'Luna - Statistiques',
    page: 'stats'
  });
});

// Route pour la page des paramètres
app.get('/luna/settings', (req, res) => {
  res.render('luna-settings', {
    title: 'Luna - Paramètres',
    page: 'settings'
  });
});

// Route pour la page des modèles
app.get('/luna/models', (req, res) => {
  res.render('luna-models', {
    title: 'Luna - Modèles',
    page: 'models'
  });
});

// Route pour la page des documents
app.get('/luna/documents', (req, res) => {
  res.render('luna-documents', {
    title: 'Luna - Documents',
    page: 'documents'
  });
});

// Route pour la page des prompts
app.get('/luna/prompts', (req, res) => {
  res.render('luna-prompts', {
    title: 'Luna - Prompts',
    page: 'prompts'
  });
});

// Route pour la page MCP
app.get('/luna/mcp', (req, res) => {
  res.render('luna-mcp', {
    title: 'Luna - MCP',
    page: 'mcp'
  });
});

// Route pour la page Internet
app.get('/luna/internet', (req, res) => {
  res.render('luna-internet', {
    title: 'Luna - Internet',
    page: 'internet'
  });
});

// Route pour la page VPN
app.get('/luna/vpn', (req, res) => {
  res.render('luna-vpn', {
    title: 'Luna - VPN',
    page: 'vpn'
  });
});

// Route pour la page Antivirus
app.get('/luna/antivirus', (req, res) => {
  res.render('luna-antivirus', {
    title: 'Luna - Antivirus',
    page: 'antivirus'
  });
});

// Route pour la page Cognitive
app.get('/luna/cognitive', (req, res) => {
  res.render('luna-cognitive', {
    title: 'Luna - Cognitive',
    page: 'cognitive'
  });
});

// Démarrer le serveur
server.listen(PORT, () => {
  console.log(`Serveur Luna démarré sur le port ${PORT}`);
  console.log(`Interface accessible à l'adresse http://localhost:${PORT}/luna`);
});
