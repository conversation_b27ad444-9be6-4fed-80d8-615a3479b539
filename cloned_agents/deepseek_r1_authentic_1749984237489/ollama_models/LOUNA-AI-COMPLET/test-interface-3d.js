#!/usr/bin/env node

/**
 * 🧪 TEST INTERFACE 3D CERVEAU VIVANT
 * 
 * Validation de l'interface 3D et de ses fonctionnalités
 */

const axios = require('axios').default;
const fs = require('fs');

async function testerInterface3D() {
    console.log('🧪 TEST INTERFACE 3D CERVEAU VIVANT');
    console.log('===================================');
    
    try {
        console.log('\n📡 Test connectivité interface 3D...');
        const response = await axios.get('http://localhost:3000/3d', { timeout: 5000 });
        
        console.log(`✅ Status HTTP: ${response.status}`);
        console.log(`📊 Taille réponse: ${response.data.length} caractères`);
        
        // Vérifications contenu
        const content = response.data;
        const verifications = [
            {
                nom: 'HTML valide',
                test: content.includes('<!DOCTYPE html>'),
                description: 'Structure HTML correcte'
            },
            {
                nom: 'Three.js inclus',
                test: content.includes('three.min.js'),
                description: 'Bibliothèque 3D chargée'
            },
            {
                nom: 'Titre correct',
                test: content.includes('CERVEAU THERMIQUE VIVANT 3D'),
                description: 'Titre de la page'
            },
            {
                nom: 'Classe CerveauThermique3D',
                test: content.includes('class CerveauThermique3D'),
                description: 'Classe principale définie'
            },
            {
                nom: 'Méthodes essentielles',
                test: content.includes('creerZonesCerebrales') && 
                      content.includes('creerNeurones') && 
                      content.includes('creerSynapses'),
                description: 'Méthodes de création 3D'
            },
            {
                nom: 'Animation loop',
                test: content.includes('animate()') && content.includes('requestAnimationFrame'),
                description: 'Boucle d\'animation'
            },
            {
                nom: 'Simulation données',
                test: content.includes('simulerActiviteNeuronale') && 
                      content.includes('mettreAJourInterface'),
                description: 'Simulation temps réel'
            },
            {
                nom: 'Contrôles souris',
                test: content.includes('ajouterControlesSouris') && 
                      content.includes('mousedown'),
                description: 'Interactions utilisateur'
            },
            {
                nom: 'Mode démonstration CSS',
                test: content.includes('creerModeDemonstrationCSS') && 
                      content.includes('demo-brain'),
                description: 'Fallback CSS si Three.js échoue'
            },
            {
                nom: 'Gestion erreurs',
                test: content.includes('typeof THREE === \'undefined\'') && 
                      content.includes('try {') && content.includes('catch'),
                description: 'Gestion robuste des erreurs'
            },
            {
                nom: 'Zones cérébrales',
                test: content.includes('cortex_prefrontal') && 
                      content.includes('hippocampe') && 
                      content.includes('amygdale'),
                description: '6 zones cérébrales définies'
            },
            {
                nom: 'Neurotransmetteurs',
                test: content.includes('dopamine') && 
                      content.includes('serotonine') && 
                      content.includes('gaba'),
                description: 'Système neurotransmetteurs'
            },
            {
                nom: 'Métriques temps réel',
                test: content.includes('temp-display') && 
                      content.includes('qi-display') && 
                      content.includes('activity-level'),
                description: 'Affichage métriques'
            },
            {
                nom: 'Flux données',
                test: content.includes('data-stream') && 
                      content.includes('ajouterLigneFluxDonnees'),
                description: 'Stream temps réel'
            },
            {
                nom: 'Responsive design',
                test: content.includes('onWindowResize') && 
                      content.includes('grid-template'),
                description: 'Interface adaptative'
            }
        ];
        
        console.log('\n🔍 VÉRIFICATIONS CONTENU:');
        let verifications_reussies = 0;
        
        verifications.forEach(verif => {
            const status = verif.test ? '✅' : '❌';
            console.log(`${status} ${verif.nom}: ${verif.description}`);
            if (verif.test) verifications_reussies++;
        });
        
        const pourcentage = (verifications_reussies / verifications.length * 100).toFixed(1);
        
        console.log('\n📊 RÉSULTATS:');
        console.log(`✅ Vérifications réussies: ${verifications_reussies}/${verifications.length} (${pourcentage}%)`);
        
        // Analyse détaillée
        console.log('\n🔍 ANALYSE DÉTAILLÉE:');
        
        if (pourcentage >= 90) {
            console.log('🎉 INTERFACE 3D EXCELLENTE !');
            console.log('✅ Toutes les fonctionnalités principales présentes');
            console.log('🧠 Cerveau 3D complet et fonctionnel');
            console.log('🎭 Mode démonstration CSS en fallback');
            console.log('🔧 Gestion d\'erreurs robuste');
            
        } else if (pourcentage >= 75) {
            console.log('✅ Interface 3D fonctionnelle');
            console.log('🔧 Quelques optimisations possibles');
            
        } else {
            console.log('⚠️ Interface 3D incomplète');
            console.log('🚨 Développement supplémentaire requis');
        }
        
        // Fonctionnalités détectées
        console.log('\n🌟 FONCTIONNALITÉS DÉTECTÉES:');
        
        if (content.includes('THREE.Scene')) {
            console.log('🧠 Rendu 3D avec Three.js');
        }
        if (content.includes('SphereGeometry')) {
            console.log('⚪ Géométries sphériques (zones/neurones)');
        }
        if (content.includes('LineBasicMaterial')) {
            console.log('🔗 Connexions synaptiques');
        }
        if (content.includes('PerspectiveCamera')) {
            console.log('📷 Caméra perspective 3D');
        }
        if (content.includes('WebGLRenderer')) {
            console.log('🎮 Rendu WebGL');
        }
        if (content.includes('AmbientLight')) {
            console.log('💡 Éclairage ambiant');
        }
        if (content.includes('DirectionalLight')) {
            console.log('☀️ Éclairage directionnel');
        }
        if (content.includes('emissiveIntensity')) {
            console.log('✨ Matériaux émissifs');
        }
        if (content.includes('requestAnimationFrame')) {
            console.log('🎬 Animation 60 FPS');
        }
        if (content.includes('Math.sin')) {
            console.log('🌊 Animations sinusoïdales');
        }
        
        // Compter éléments
        const nb_zones = (content.match(/zone-temp-/g) || []).length;
        const nb_neurotransmetteurs = (content.match(/dopamine|serotonine|gaba|glutamate/g) || []).length;
        
        console.log('\n📊 ÉLÉMENTS COMPTÉS:');
        console.log(`🧠 Zones cérébrales: ${nb_zones}`);
        console.log(`🧪 Neurotransmetteurs: ${nb_neurotransmetteurs}`);
        console.log(`📏 Taille code: ${(content.length / 1024).toFixed(1)} KB`);
        
        // Recommandations
        console.log('\n💡 RECOMMANDATIONS:');
        console.log('1. Testez avec différents navigateurs');
        console.log('2. Vérifiez les performances sur mobile');
        console.log('3. Surveillez la consommation GPU');
        console.log('4. Optimisez le nombre de particules si nécessaire');
        console.log('5. Testez le mode démonstration CSS');
        
        // Sauvegarder rapport
        const rapport = {
            date_test: new Date().toISOString(),
            url_testee: 'http://localhost:3000/3d',
            status_http: response.status,
            taille_contenu: content.length,
            verifications: verifications,
            pourcentage_reussite: parseFloat(pourcentage),
            nb_zones_cerebrales: nb_zones,
            nb_neurotransmetteurs: nb_neurotransmetteurs,
            verdict: pourcentage >= 90 ? 'EXCELLENT' : 
                    pourcentage >= 75 ? 'BON' : 'INCOMPLET'
        };
        
        fs.writeFileSync('RAPPORT-TEST-INTERFACE-3D.json', JSON.stringify(rapport, null, 2));
        console.log('\n📋 Rapport sauvegardé: RAPPORT-TEST-INTERFACE-3D.json');
        
        return rapport;
        
    } catch (error) {
        console.error('❌ Erreur test interface 3D:', error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('🚨 Serveur non démarré !');
            console.log('   Lancez: node serveur-interface-complete.js');
        }
        
        return { erreur: error.message };
    }
}

// Lancer test
if (require.main === module) {
    testerInterface3D().catch(console.error);
}

module.exports = { testerInterface3D };
