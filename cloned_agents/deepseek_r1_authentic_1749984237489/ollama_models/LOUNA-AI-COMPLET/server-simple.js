/**
 * Serveur simple pour tester les interfaces
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const fs = require('fs');

// Configuration
const app = express();
const server = http.createServer(app);
const io = socketIo(server);
const PORT = process.env.PORT || 4000;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// Routes
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'test-chat.html'));
});

// Gestionnaire de socket pour les messages
io.on('connection', (socket) => {
  console.log('Nouvelle connexion WebSocket');
  
  // Gérer les messages Luna
  socket.on('luna message', (data) => {
    console.log('Message Luna reçu:', data.message);
    
    // Simuler une réponse
    setTimeout(() => {
      const response = {
        message: `Je suis Vision Ultra, votre assistant cognitif. Vous avez dit: "${data.message}"`,
        timestamp: new Date().toISOString()
      };
      
      // Envoyer la réponse au client
      socket.emit('luna response', response);
      console.log('Réponse Luna envoyée:', response.message);
    }, 1000);
  });
  
  // Gérer les messages Louna
  socket.on('louna message', (data) => {
    console.log('Message Louna reçu:', data.message);
    
    // Simuler une réponse
    setTimeout(() => {
      const response = {
        message: `Je suis Vision Ultra, votre assistant cognitif. Vous avez dit: "${data.message}"`,
        timestamp: new Date().toISOString()
      };
      
      // Envoyer la réponse au client
      socket.emit('louna response', response);
      console.log('Réponse Louna envoyée:', response.message);
    }, 1000);
  });
  
  // Gérer les messages Lounas
  socket.on('lounas message', (data) => {
    console.log('Message Lounas reçu:', data.message);
    
    // Simuler une réponse
    setTimeout(() => {
      const response = {
        message: `Je suis Vision Ultra, votre assistant cognitif. Vous avez dit: "${data.message}"`,
        timestamp: new Date().toISOString()
      };
      
      // Envoyer la réponse au client
      socket.emit('lounas response', response);
      console.log('Réponse Lounas envoyée:', response.message);
    }, 1000);
  });
  
  // Gérer la déconnexion
  socket.on('disconnect', () => {
    console.log('Déconnexion WebSocket');
  });
});

// Démarrer le serveur
server.listen(PORT, () => {
  console.log(`Serveur simple démarré sur le port ${PORT}`);
  console.log(`Interface accessible à l'adresse http://localhost:${PORT}`);
});
