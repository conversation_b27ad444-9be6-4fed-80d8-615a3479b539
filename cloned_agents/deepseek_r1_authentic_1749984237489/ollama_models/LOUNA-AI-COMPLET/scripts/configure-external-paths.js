/**
 * Script pour configurer les chemins d'accès du serveur Luna
 * afin qu'il utilise les fichiers sur le disque externe
 */

const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG_FILE = path.join(__dirname, '..', 'config.json');
const OLLAMA_CONFIG_FILE = path.join(__dirname, '..', 'config-ollama.json');
const EXTERNAL_DRIVE = '/Volumes/seagate';
const EXTERNAL_CODE_DIR = path.join(EXTERNAL_DRIVE, 'Jarvis_Working/code');
const EXTERNAL_MODELS_DIR = path.join(EXTERNAL_DRIVE, 'Jarvis_Working/models');

// Vérifier si le disque externe est monté
if (!fs.existsSync(EXTERNAL_DRIVE)) {
  console.error(`ERREUR: Le disque externe n'est pas monté. Veuillez connecter le disque Seagate.`);
  process.exit(1);
}

// Vérifier si les répertoires existent
if (!fs.existsSync(EXTERNAL_CODE_DIR)) {
  console.error(`ERREUR: Le répertoire de code externe n'existe pas: ${EXTERNAL_CODE_DIR}`);
  console.error('Veuillez exécuter le script work-from-external.sh d\'abord.');
  process.exit(1);
}

if (!fs.existsSync(EXTERNAL_MODELS_DIR)) {
  console.error(`ERREUR: Le répertoire de modèles externe n'existe pas: ${EXTERNAL_MODELS_DIR}`);
  console.error('Veuillez exécuter le script work-from-external.sh d\'abord.');
  process.exit(1);
}

// Fonction pour mettre à jour un fichier de configuration
function updateConfig(configFile, updateFunction) {
  try {
    // Lire le fichier de configuration
    const configData = fs.readFileSync(configFile, 'utf8');
    const config = JSON.parse(configData);
    
    // Mettre à jour la configuration
    const updatedConfig = updateFunction(config);
    
    // Sauvegarder la configuration mise à jour
    fs.writeFileSync(configFile, JSON.stringify(updatedConfig, null, 2), 'utf8');
    console.log(`Configuration mise à jour: ${configFile}`);
    
    // Créer une sauvegarde de la configuration originale
    fs.writeFileSync(`${configFile}.bak`, configData, 'utf8');
    console.log(`Sauvegarde de la configuration originale créée: ${configFile}.bak`);
    
    return true;
  } catch (error) {
    console.error(`ERREUR lors de la mise à jour de la configuration ${configFile}:`, error.message);
    return false;
  }
}

// Mettre à jour la configuration principale
const mainConfigUpdated = updateConfig(CONFIG_FILE, (config) => {
  // Ajouter ou mettre à jour les chemins d'accès
  config.externalDrive = EXTERNAL_DRIVE;
  config.externalCodeDir = EXTERNAL_CODE_DIR;
  config.externalModelsDir = EXTERNAL_MODELS_DIR;
  config.useExternalDrive = true;
  
  return config;
});

// Mettre à jour la configuration d'Ollama
const ollamaConfigUpdated = updateConfig(OLLAMA_CONFIG_FILE, (config) => {
  // Mettre à jour le chemin des modèles
  if (config.modelPath) {
    config.originalModelPath = config.modelPath;
    config.modelPath = EXTERNAL_MODELS_DIR;
  }
  
  return config;
});

// Créer un script de démarrage pour utiliser les chemins externes
const startScript = `#!/bin/bash

# Script pour démarrer le serveur Luna avec les chemins externes
# Créé automatiquement par configure-external-paths.js

# Vérifier si le disque externe est monté
if [ ! -d "${EXTERNAL_DRIVE}" ]; then
  echo "ERREUR: Le disque externe n'est pas monté. Veuillez connecter le disque Seagate."
  exit 1
fi

# Démarrer le serveur Luna
cd "${EXTERNAL_CODE_DIR}/deepseek-node-ui"
node server-luna.js
`;

// Sauvegarder le script de démarrage
const startScriptPath = path.join(__dirname, 'start-luna-external.sh');
fs.writeFileSync(startScriptPath, startScript, 'utf8');
fs.chmodSync(startScriptPath, '755'); // Rendre le script exécutable
console.log(`Script de démarrage créé: ${startScriptPath}`);

// Résumé
if (mainConfigUpdated && ollamaConfigUpdated) {
  console.log('\n=== Configuration terminée avec succès ===');
  console.log('Le serveur Luna est maintenant configuré pour utiliser les fichiers sur le disque externe.');
  console.log(`\nPour démarrer le serveur, exécutez: ${startScriptPath}`);
  console.log('\nVous pouvez maintenant travailler directement depuis le disque externe.');
  console.log('Si tout fonctionne correctement, vous pourrez supprimer les fichiers originaux.');
  console.log('\nPour revenir à la configuration originale, restaurez les fichiers de sauvegarde:');
  console.log(`- ${CONFIG_FILE}.bak -> ${CONFIG_FILE}`);
  console.log(`- ${OLLAMA_CONFIG_FILE}.bak -> ${OLLAMA_CONFIG_FILE}`);
} else {
  console.log('\n=== Configuration terminée avec des erreurs ===');
  console.log('Veuillez corriger les erreurs avant de continuer.');
}
