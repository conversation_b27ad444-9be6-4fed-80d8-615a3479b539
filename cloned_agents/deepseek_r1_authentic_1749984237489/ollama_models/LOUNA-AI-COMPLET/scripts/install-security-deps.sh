#!/bin/bash
# Script pour installer les dépendances nécessaires pour le module de sécurité
# Créé le 16 mai 2025

# Définir les couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages avec un préfixe coloré
log() {
  echo -e "${BLUE}[SECURITY]${NC} $1"
}

error() {
  echo -e "${RED}[ERREUR]${NC} $1"
}

success() {
  echo -e "${GREEN}[SUCCÈS]${NC} $1"
}

warning() {
  echo -e "${YELLOW}[ATTENTION]${NC} $1"
}

# Vérifier si npm est installé
if ! command -v npm &> /dev/null; then
  error "npm n'est pas installé. Veuillez installer Node.js et npm."
  exit 1
fi

# Vérifier si le disque externe est monté
if [ ! -d "/Volumes/seagate" ]; then
  error "Le disque externe 'seagate' n'est pas monté. Veuillez le connecter et réessayer."
  exit 1
fi

# Vérifier si le répertoire de travail existe
if [ ! -d "/Volumes/seagate/Jarvis_Working/code/deepseek-node-ui" ]; then
  error "Le répertoire de travail n'existe pas sur le disque externe."
  exit 1
fi

# Se déplacer dans le répertoire de travail
cd "/Volumes/seagate/Jarvis_Working/code/deepseek-node-ui" || {
  error "Impossible d'accéder au répertoire de travail."
  exit 1
}

# Créer les répertoires nécessaires
log "Création des répertoires nécessaires..."
mkdir -p modules
mkdir -p data/quarantine
mkdir -p logs/security

# Installer les dépendances
log "Installation des dépendances pour le module de sécurité..."
npm install --save axios

# Vérifier si l'installation a réussi
if [ $? -eq 0 ]; then
  success "Dépendances installées avec succès."
else
  error "Erreur lors de l'installation des dépendances."
  exit 1
fi

# Vérifier si les fichiers du module de sécurité existent
if [ ! -f "modules/security-manager.js" ]; then
  warning "Le fichier modules/security-manager.js n'existe pas. Assurez-vous de l'avoir créé."
fi

if [ ! -f "routes/luna-security.js" ]; then
  warning "Le fichier routes/luna-security.js n'existe pas. Assurez-vous de l'avoir créé."
fi

if [ ! -f "views/luna-security.ejs" ]; then
  warning "Le fichier views/luna-security.ejs n'existe pas. Assurez-vous de l'avoir créé."
fi

if [ ! -f "public/luna/css/luna-security.css" ]; then
  warning "Le fichier public/luna/css/luna-security.css n'existe pas. Assurez-vous de l'avoir créé."
fi

# Afficher un message de succès
success "Installation des dépendances pour le module de sécurité terminée."
log "Vous pouvez maintenant redémarrer le serveur Luna pour activer le module de sécurité."
log "Utilisez la commande: ./restart-luna-server.sh"
