#!/bin/bash

# Script pour vérifier si le disque externe est correctement monté
# et s'il contient les fichiers nécessaires pour travailler

# Configuration
EXTERNAL_DRIVE="/Volumes/seagate"
BACKUP_DIR="${EXTERNAL_DRIVE}/Jarvis_Backup"
WORKING_DIR="${EXTERNAL_DRIVE}/Jarvis_Working"

# Fonction pour afficher un message coloré
print_status() {
    local status=$1
    local message=$2
    
    if [ "$status" = "OK" ]; then
        echo -e "\033[32m✅ $message\033[0m"  # Vert
    elif [ "$status" = "WARNING" ]; then
        echo -e "\033[33m⚠️ $message\033[0m"  # Jaune
    else
        echo -e "\033[31m❌ $message\033[0m"  # Rouge
    fi
}

echo "=== Vérification du disque externe ==="

# Vérifier si le disque externe est monté
if [ -d "$EXTERNAL_DRIVE" ]; then
    print_status "OK" "Le disque externe est monté: $EXTERNAL_DRIVE"
    
    # Vérifier l'espace disponible
    SPACE_AVAILABLE=$(df -h "$EXTERNAL_DRIVE" | awk 'NR==2 {print $4}')
    print_status "OK" "Espace disponible sur le disque externe: $SPACE_AVAILABLE"
    
    # Vérifier si le répertoire de sauvegarde existe
    if [ -d "$BACKUP_DIR" ]; then
        print_status "OK" "Le répertoire de sauvegarde existe: $BACKUP_DIR"
        
        # Vérifier s'il y a des sauvegardes
        BACKUP_COUNT=$(find "$BACKUP_DIR" -maxdepth 1 -type d -name "Jarvis_Backup_*" | wc -l)
        if [ "$BACKUP_COUNT" -gt 0 ]; then
            print_status "OK" "Nombre de sauvegardes trouvées: $BACKUP_COUNT"
            
            # Trouver la dernière sauvegarde
            LATEST_BACKUP=$(find "$BACKUP_DIR" -maxdepth 1 -type d -name "Jarvis_Backup_*" | sort -r | head -n 1)
            print_status "OK" "Dernière sauvegarde: $(basename "$LATEST_BACKUP")"
            
            # Vérifier si la dernière sauvegarde contient les fichiers nécessaires
            if [ -d "${LATEST_BACKUP}/code/deepseek-node-ui" ]; then
                print_status "OK" "La dernière sauvegarde contient le code du projet"
            else
                print_status "ERROR" "La dernière sauvegarde ne contient pas le code du projet"
            fi
            
            if [ -d "${LATEST_BACKUP}/models" ]; then
                print_status "OK" "La dernière sauvegarde contient les modèles d'IA"
            else
                print_status "ERROR" "La dernière sauvegarde ne contient pas les modèles d'IA"
            fi
        else
            print_status "WARNING" "Aucune sauvegarde trouvée dans $BACKUP_DIR"
        fi
    else
        print_status "WARNING" "Le répertoire de sauvegarde n'existe pas: $BACKUP_DIR"
    fi
    
    # Vérifier si le répertoire de travail existe
    if [ -d "$WORKING_DIR" ]; then
        print_status "OK" "Le répertoire de travail existe: $WORKING_DIR"
        
        # Vérifier si le répertoire de travail contient les fichiers nécessaires
        if [ -d "${WORKING_DIR}/code/deepseek-node-ui" ]; then
            print_status "OK" "Le répertoire de travail contient le code du projet"
            
            # Vérifier si le serveur Luna peut démarrer
            if [ -f "${WORKING_DIR}/code/deepseek-node-ui/server-luna.js" ]; then
                print_status "OK" "Le serveur Luna est présent dans le répertoire de travail"
            else
                print_status "ERROR" "Le serveur Luna n'est pas présent dans le répertoire de travail"
            fi
        else
            print_status "WARNING" "Le répertoire de travail ne contient pas le code du projet"
        fi
        
        if [ -d "${WORKING_DIR}/models" ]; then
            print_status "OK" "Le répertoire de travail contient les modèles d'IA"
        else
            print_status "WARNING" "Le répertoire de travail ne contient pas les modèles d'IA"
        fi
    else
        print_status "WARNING" "Le répertoire de travail n'existe pas: $WORKING_DIR"
    fi
else
    print_status "ERROR" "Le disque externe n'est pas monté: $EXTERNAL_DRIVE"
    exit 1
fi

echo ""
echo "=== Recommandations ==="
if [ ! -d "$BACKUP_DIR" ] || [ "$BACKUP_COUNT" -eq 0 ]; then
    print_status "WARNING" "Exécutez d'abord le script de sauvegarde: ./scripts/backup-project.sh"
elif [ ! -d "$WORKING_DIR" ]; then
    print_status "WARNING" "Exécutez le script de configuration de l'environnement de travail: ./scripts/work-from-external.sh"
elif [ ! -d "${WORKING_DIR}/code/deepseek-node-ui" ] || [ ! -d "${WORKING_DIR}/models" ]; then
    print_status "WARNING" "Exécutez le script de configuration de l'environnement de travail: ./scripts/work-from-external.sh"
else
    print_status "OK" "Vous pouvez maintenant travailler depuis le disque externe"
    print_status "OK" "Pour démarrer le serveur Luna avec les chemins externes, exécutez: ${WORKING_DIR}/code/deepseek-node-ui/scripts/start-luna-external.sh"
fi
