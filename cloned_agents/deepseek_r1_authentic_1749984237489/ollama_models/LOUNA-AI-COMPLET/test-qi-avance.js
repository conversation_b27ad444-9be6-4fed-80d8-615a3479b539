/**
 * TEST DE QI AVANCÉ
 * Test complet d'intelligence avec mémoire thermique évolutive
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class TestQIAvance {
    constructor() {
        console.log('🧠 TEST DE QI AVANCÉ');
        console.log('====================');
        
        this.config = {
            memoire: '/Volumes/LounaAI_V3/MEMOIRE-REELLE',
            ollama: '/Volumes/LounaAI_V3/AGENTS-REELS/ollama/ollama',
            modeles: '/Volumes/LounaAI_V3/AGENTS-REELS/ollama/models-reels',
            model: 'llama3.2:1b',
            resultats: '/Volumes/LounaAI_V3/MEMOIRE-REELLE/test-qi'
        };
        
        this.curseur = {
            position: 50.0,
            zone: 'zone3'
        };
        
        this.resultats_qi = {
            score_total: 0,
            points_maximum: 0,
            bonus_memoire: 0,
            qi_final: 85,
            classification: '',
            details_tests: [],
            utilisation_memoire: 0,
            temps_total: 0
        };
        
        this.tests_qi = [
            // Tests logiques
            {
                id: 'logique_1',
                categorie: 'Logique déductive',
                question: 'Si tous les A sont B, et tous les B sont C, alors tous les A sont-ils C?',
                reponse_attendue: 'oui',
                points: 20,
                difficulte: 'facile'
            },
            {
                id: 'logique_2',
                categorie: 'Logique conditionnelle',
                question: 'Si il pleut, alors le sol est mouillé. Le sol est sec. Que peut-on conclure?',
                reponse_attendue: 'il ne pleut pas',
                points: 25,
                difficulte: 'moyen'
            },
            
            // Tests mathématiques
            {
                id: 'math_1',
                categorie: 'Calcul simple',
                question: 'Combien font 15 + 27?',
                reponse_attendue: '42',
                points: 15,
                difficulte: 'facile'
            },
            {
                id: 'math_2',
                categorie: 'Séquences numériques',
                question: 'Complétez la séquence: 2, 4, 8, 16, ?',
                reponse_attendue: '32',
                points: 20,
                difficulte: 'moyen'
            },
            {
                id: 'math_3',
                categorie: 'Proportions',
                question: 'Si 3 pommes coûtent 6 euros, combien coûtent 5 pommes?',
                reponse_attendue: '10',
                points: 25,
                difficulte: 'moyen'
            },
            
            // Tests de compréhension
            {
                id: 'comprehension_1',
                categorie: 'Compréhension conceptuelle',
                question: 'Qu\'est-ce qui est plus lourd: un kilo de plumes ou un kilo de plomb?',
                reponse_attendue: 'égal',
                points: 20,
                difficulte: 'piège'
            },
            {
                id: 'comprehension_2',
                categorie: 'Analogies',
                question: 'Oiseau est à voler comme poisson est à ?',
                reponse_attendue: 'nager',
                points: 25,
                difficulte: 'moyen'
            },
            
            // Tests avec mémoire
            {
                id: 'memoire_1',
                categorie: 'Synthèse avec mémoire',
                question: 'En vous basant sur nos discussions précédentes, expliquez la relation entre mémoire et intelligence.',
                reponse_attendue: 'connexion',
                points: 30,
                difficulte: 'difficile'
            },
            {
                id: 'memoire_2',
                categorie: 'Apprentissage cumulatif',
                question: 'Quels patterns avez-vous détectés dans vos interactions précédentes?',
                reponse_attendue: 'patterns',
                points: 35,
                difficulte: 'difficile'
            },
            
            // Tests créatifs
            {
                id: 'creativite_1',
                categorie: 'Pensée créative',
                question: 'Donnez 3 utilisations créatives d\'un trombone.',
                reponse_attendue: 'créatif',
                points: 30,
                difficulte: 'créatif'
            }
        ];
        
        this.initialiserTestQI();
    }
    
    initialiserTestQI() {
        console.log('🔧 Initialisation test QI...');
        
        try {
            // Créer dossier résultats
            if (!fs.existsSync(this.config.resultats)) {
                fs.mkdirSync(this.config.resultats, { recursive: true });
                console.log('📁 Dossier test-qi créé');
            }
            
            // Charger curseur actuel
            this.chargerCurseur();
            
            // Calculer points maximum
            this.resultats_qi.points_maximum = this.tests_qi.reduce((total, test) => total + test.points, 0);
            
            console.log(`✅ Test QI initialisé - ${this.tests_qi.length} questions`);
            console.log(`🎯 Points maximum: ${this.resultats_qi.points_maximum}`);
            
        } catch (error) {
            console.log(`❌ Erreur initialisation: ${error.message}`);
        }
    }
    
    chargerCurseur() {
        try {
            const cheminCurseur = path.join(this.config.memoire, 'curseur-thermique', 'position.json');
            
            if (fs.existsSync(cheminCurseur)) {
                const data = JSON.parse(fs.readFileSync(cheminCurseur, 'utf8'));
                if (data.position) {
                    this.curseur.position = data.position;
                    this.curseur.zone = data.zone || 'zone3';
                    console.log(`🌡️ Curseur chargé: ${this.curseur.position}°C (${this.curseur.zone})`);
                }
            }
        } catch (error) {
            console.log(`⚠️ Curseur par défaut utilisé: ${error.message}`);
        }
    }
    
    async executerTestQIComplet() {
        console.log('\n🧠 EXÉCUTION TEST QI COMPLET');
        console.log('============================');
        
        const debutTest = Date.now();
        
        for (let i = 0; i < this.tests_qi.length; i++) {
            const test = this.tests_qi[i];
            console.log(`\n📝 Test ${i + 1}/${this.tests_qi.length}: ${test.categorie.toUpperCase()}`);
            console.log(`❓ ${test.question}`);
            console.log(`🎯 Points: ${test.points} | Difficulté: ${test.difficulte}`);
            
            const resultatTest = await this.executerTestIndividuel(test);
            this.resultats_qi.details_tests.push(resultatTest);
            
            // Pause entre tests
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
        this.resultats_qi.temps_total = Date.now() - debutTest;
        
        // Calculer résultats finaux
        this.calculerResultatsFinaux();
        
        // Sauvegarder résultats
        this.sauvegarderResultats();
        
        // Afficher résultats
        this.afficherResultatsFinaux();
        
        return this.resultats_qi;
    }
    
    async executerTestIndividuel(test) {
        const debutTest = Date.now();
        
        try {
            // 1. Rechercher dans mémoire
            const souvenirs = this.rechercherMemoireTest(test.question);
            
            // 2. Construire contexte
            const contexte = this.construireContexteTest(test.question, souvenirs);
            
            // 3. Poser question à l'agent
            const reponse = await this.poserQuestionAgent(contexte);
            
            // 4. Évaluer réponse
            const evaluation = this.evaluerReponse(test, reponse);
            
            // 5. Calculer bonus mémoire
            const bonusMemoire = souvenirs.length > 0 ? 5 : 0;
            
            const duree = Date.now() - debutTest;
            
            const resultat = {
                test_id: test.id,
                categorie: test.categorie,
                question: test.question,
                reponse: reponse,
                correct: evaluation.correct,
                points_obtenus: evaluation.correct ? test.points : 0,
                bonus_memoire: bonusMemoire,
                points_total: (evaluation.correct ? test.points : 0) + bonusMemoire,
                souvenirs_utilises: souvenirs.length,
                duree_ms: duree,
                curseur_position: this.curseur.position,
                explication: evaluation.explication
            };
            
            // Mettre à jour curseur
            this.mettreAJourCurseur(test, resultat);
            
            // Afficher résultat
            this.afficherResultatTest(resultat);
            
            return resultat;
            
        } catch (error) {
            console.log(`❌ Erreur test ${test.id}: ${error.message}`);
            
            return {
                test_id: test.id,
                categorie: test.categorie,
                question: test.question,
                reponse: 'Erreur',
                correct: false,
                points_obtenus: 0,
                bonus_memoire: 0,
                points_total: 0,
                souvenirs_utilises: 0,
                duree_ms: 0,
                erreur: error.message
            };
        }
    }
    
    rechercherMemoireTest(question) {
        try {
            const motsCles = question.toLowerCase()
                .replace(/[^\w\s]/g, ' ')
                .split(/\s+/)
                .filter(mot => mot.length > 2);
            
            const souvenirs = [];
            const cheminZones = path.join(this.config.memoire, 'zones-thermiques');
            
            if (fs.existsSync(cheminZones)) {
                const zones = fs.readdirSync(cheminZones);
                
                zones.forEach(zone => {
                    const cheminZone = path.join(cheminZones, zone);
                    
                    if (fs.existsSync(cheminZone)) {
                        const fichiers = fs.readdirSync(cheminZone)
                            .filter(f => f.endsWith('.json'))
                            .slice(0, 2); // Limiter pour performance
                        
                        fichiers.forEach(fichier => {
                            try {
                                const cheminFichier = path.join(cheminZone, fichier);
                                const souvenir = JSON.parse(fs.readFileSync(cheminFichier, 'utf8'));
                                
                                let pertinence = 0;
                                motsCles.forEach(mot => {
                                    if (souvenir.contenu && souvenir.contenu.toLowerCase().includes(mot)) {
                                        pertinence++;
                                    }
                                });
                                
                                if (pertinence > 0) {
                                    souvenirs.push({
                                        contenu: souvenir.contenu,
                                        zone: zone,
                                        pertinence: pertinence
                                    });
                                }
                            } catch (error) {
                                // Ignorer fichiers corrompus
                            }
                        });
                    }
                });
            }
            
            // Trier par pertinence
            souvenirs.sort((a, b) => b.pertinence - a.pertinence);
            
            console.log(`🔍 ${souvenirs.slice(0, 2).length} souvenirs trouvés`);
            return souvenirs.slice(0, 2);
            
        } catch (error) {
            console.log(`⚠️ Erreur recherche mémoire: ${error.message}`);
            return [];
        }
    }
    
    construireContexteTest(question, souvenirs) {
        let contexte = '[TEST QI] ';
        
        // Ajouter état curseur
        contexte += `[Curseur: ${this.curseur.position.toFixed(1)}°C - ${this.curseur.zone}] `;
        
        // Ajouter souvenirs
        if (souvenirs.length > 0) {
            contexte += '[MÉMOIRE]: ';
            souvenirs.forEach((souvenir, index) => {
                contexte += `${index + 1}. ${souvenir.contenu} `;
            });
        }
        
        contexte += `\n\nQuestion de test QI: ${question}\n\n`;
        
        if (souvenirs.length > 0) {
            contexte += 'Répondez en utilisant votre mémoire si pertinente. Soyez précis et concis.';
        } else {
            contexte += 'Répondez de manière précise et concise.';
        }
        
        return contexte;
    }
    
    async poserQuestionAgent(contexte) {
        console.log('🤖 Question à l\'agent...');
        
        try {
            const reponse = execSync(
                `${this.config.ollama} run ${this.config.model} "${contexte}"`,
                {
                    encoding: 'utf8',
                    timeout: 30000,
                    env: {
                        ...process.env,
                        OLLAMA_MODELS: this.config.modeles
                    }
                }
            );
            
            const reponseNettoyee = reponse.trim();
            console.log(`📝 Réponse: "${reponseNettoyee.substring(0, 60)}${reponseNettoyee.length > 60 ? '...' : ''}"`);
            
            return reponseNettoyee;
            
        } catch (error) {
            console.log(`❌ Erreur agent: ${error.message}`);
            return 'Erreur de communication avec l\'agent';
        }
    }
    
    evaluerReponse(test, reponse) {
        const reponseNormalisee = reponse.toLowerCase().trim();
        const attendueNormalisee = test.reponse_attendue.toLowerCase();
        
        let correct = false;
        let explication = '';
        
        switch (test.categorie) {
            case 'Logique déductive':
                correct = reponseNormalisee.includes('oui') || reponseNormalisee.includes('yes');
                explication = correct ? 'Logique correcte' : 'Erreur de logique déductive';
                break;
                
            case 'Logique conditionnelle':
                correct = reponseNormalisee.includes('ne pleut pas') || reponseNormalisee.includes('pas de pluie');
                explication = correct ? 'Raisonnement conditionnel correct' : 'Erreur de logique conditionnelle';
                break;
                
            case 'Calcul simple':
                correct = reponseNormalisee.includes(attendueNormalisee);
                explication = correct ? 'Calcul correct' : 'Erreur de calcul';
                break;
                
            case 'Séquences numériques':
                correct = reponseNormalisee.includes('32');
                explication = correct ? 'Pattern numérique identifié' : 'Pattern non reconnu';
                break;
                
            case 'Proportions':
                correct = reponseNormalisee.includes('10');
                explication = correct ? 'Proportion calculée correctement' : 'Erreur de proportion';
                break;
                
            case 'Compréhension conceptuelle':
                correct = reponseNormalisee.includes('égal') || reponseNormalisee.includes('même') || reponseNormalisee.includes('identique');
                explication = correct ? 'Piège évité, compréhension correcte' : 'Piège non détecté';
                break;
                
            case 'Analogies':
                correct = reponseNormalisee.includes('nager') || reponseNormalisee.includes('nage');
                explication = correct ? 'Analogie correcte' : 'Analogie incorrecte';
                break;
                
            case 'Synthèse avec mémoire':
                correct = (reponseNormalisee.includes('mémoire') && reponseNormalisee.includes('intelligence')) ||
                         reponseNormalisee.includes('connexion') || reponseNormalisee.includes('lien');
                explication = correct ? 'Synthèse avec mémoire réussie' : 'Synthèse insuffisante';
                break;
                
            case 'Apprentissage cumulatif':
                correct = reponseNormalisee.includes('pattern') || reponseNormalisee.includes('répétition') ||
                         reponseNormalisee.includes('tendance') || reponseNormalisee.includes('habitude');
                explication = correct ? 'Patterns détectés' : 'Aucun pattern identifié';
                break;
                
            case 'Pensée créative':
                const idees = reponseNormalisee.split(/[,;.\n]/).filter(s => s.trim().length > 5);
                correct = idees.length >= 2; // Au moins 2 idées créatives
                explication = correct ? `${idees.length} idées créatives` : 'Créativité insuffisante';
                break;
                
            default:
                correct = reponseNormalisee.includes(attendueNormalisee);
                explication = correct ? 'Réponse correcte' : 'Réponse incorrecte';
        }
        
        return { correct, explication };
    }
    
    mettreAJourCurseur(test, resultat) {
        try {
            // Ajuster curseur selon performance
            let ajustement = 0;
            
            if (resultat.correct) {
                ajustement = test.points / 10; // Bonus selon points
                if (resultat.bonus_memoire > 0) {
                    ajustement += 2; // Bonus utilisation mémoire
                }
            } else {
                ajustement = -1; // Malus échec
            }
            
            this.curseur.position = Math.max(20, Math.min(70, this.curseur.position + ajustement));
            
            // Déterminer zone
            if (this.curseur.position >= 65) this.curseur.zone = 'zone1';
            else if (this.curseur.position >= 55) this.curseur.zone = 'zone2';
            else if (this.curseur.position >= 45) this.curseur.zone = 'zone3';
            else if (this.curseur.position >= 35) this.curseur.zone = 'zone4';
            else if (this.curseur.position >= 25) this.curseur.zone = 'zone5';
            else this.curseur.zone = 'zone6';
            
            console.log(`🌡️ Curseur: ${this.curseur.position.toFixed(1)}°C (${this.curseur.zone})`);
            
        } catch (error) {
            console.log(`⚠️ Erreur curseur: ${error.message}`);
        }
    }
    
    afficherResultatTest(resultat) {
        const statut = resultat.correct ? '✅ CORRECT' : '❌ INCORRECT';
        const points = `${resultat.points_total}/${resultat.points_obtenus + resultat.bonus_memoire + (resultat.correct ? 0 : resultat.points_obtenus)}`;
        
        console.log(`${statut} - ${points} points`);
        
        if (resultat.bonus_memoire > 0) {
            console.log(`🧠 Bonus mémoire: +${resultat.bonus_memoire} points (${resultat.souvenirs_utilises} souvenirs)`);
        }
        
        console.log(`💡 ${resultat.explication}`);
        console.log(`⏱️ Temps: ${(resultat.duree_ms / 1000).toFixed(1)}s`);
    }
    
    calculerResultatsFinaux() {
        // Calculer score total
        this.resultats_qi.score_total = this.resultats_qi.details_tests.reduce(
            (total, test) => total + test.points_total, 0
        );
        
        // Calculer bonus mémoire total
        this.resultats_qi.bonus_memoire = this.resultats_qi.details_tests.reduce(
            (total, test) => total + test.bonus_memoire, 0
        );
        
        // Calculer utilisation mémoire
        const testsAvecMemoire = this.resultats_qi.details_tests.filter(t => t.souvenirs_utilises > 0).length;
        this.resultats_qi.utilisation_memoire = (testsAvecMemoire / this.tests_qi.length * 100);
        
        // Calculer QI final
        const pourcentageReussite = (this.resultats_qi.score_total / (this.resultats_qi.points_maximum + this.tests_qi.length * 5)) * 100;
        
        // QI de base selon performance
        let qi = 70 + (pourcentageReussite / 100) * 60; // 70-130
        
        // Bonus curseur thermique
        const bonusCurseur = (this.curseur.position - 50) / 10; // -3 à +2
        qi += bonusCurseur;
        
        // Bonus utilisation mémoire
        const bonusMemoire = (this.resultats_qi.utilisation_memoire / 100) * 15; // 0-15
        qi += bonusMemoire;
        
        // Plafonner
        this.resultats_qi.qi_final = Math.round(Math.max(70, Math.min(140, qi)));
        
        // Classification
        if (this.resultats_qi.qi_final >= 130) {
            this.resultats_qi.classification = 'TRÈS SUPÉRIEUR';
        } else if (this.resultats_qi.qi_final >= 120) {
            this.resultats_qi.classification = 'SUPÉRIEUR';
        } else if (this.resultats_qi.qi_final >= 110) {
            this.resultats_qi.classification = 'AU-DESSUS MOYENNE';
        } else if (this.resultats_qi.qi_final >= 90) {
            this.resultats_qi.classification = 'MOYENNE';
        } else {
            this.resultats_qi.classification = 'EN-DESSOUS MOYENNE';
        }
    }
    
    afficherResultatsFinaux() {
        console.log('\n🧠 RÉSULTATS FINAUX TEST QI');
        console.log('===========================');
        
        const testsReussis = this.resultats_qi.details_tests.filter(t => t.correct).length;
        
        console.log(`📊 Tests réussis: ${testsReussis}/${this.tests_qi.length}`);
        console.log(`🎯 Points obtenus: ${this.resultats_qi.score_total}/${this.resultats_qi.points_maximum + this.tests_qi.length * 5}`);
        console.log(`🧠 Bonus mémoire: +${this.resultats_qi.bonus_memoire} points`);
        console.log(`📈 Utilisation mémoire: ${this.resultats_qi.utilisation_memoire.toFixed(1)}%`);
        console.log(`🌡️ Position curseur finale: ${this.curseur.position.toFixed(1)}°C (${this.curseur.zone})`);
        console.log(`⏱️ Temps total: ${(this.resultats_qi.temps_total / 1000 / 60).toFixed(1)} minutes`);
        
        console.log(`\n🎯 QI FINAL: ${this.resultats_qi.qi_final}`);
        console.log(`🏆 CLASSIFICATION: ${this.resultats_qi.classification}`);
        
        // Détails par catégorie
        console.log('\n📋 DÉTAILS PAR CATÉGORIE:');
        const categories = {};
        
        this.resultats_qi.details_tests.forEach(test => {
            if (!categories[test.categorie]) {
                categories[test.categorie] = { reussis: 0, total: 0 };
            }
            categories[test.categorie].total++;
            if (test.correct) categories[test.categorie].reussis++;
        });
        
        Object.entries(categories).forEach(([categorie, stats]) => {
            const pourcentage = (stats.reussis / stats.total * 100).toFixed(0);
            console.log(`   ${categorie}: ${stats.reussis}/${stats.total} (${pourcentage}%)`);
        });
        
        // Recommandations
        console.log('\n💡 RECOMMANDATIONS:');
        
        if (this.resultats_qi.utilisation_memoire < 50) {
            console.log('   🧠 Améliorer utilisation de la mémoire thermique');
        }
        
        if (this.curseur.position < 45) {
            console.log('   🌡️ Optimiser position du curseur thermique');
        }
        
        if (testsReussis < this.tests_qi.length * 0.7) {
            console.log('   📚 Continuer l\'apprentissage et l\'entraînement');
        }
        
        if (this.resultats_qi.qi_final >= 120) {
            console.log('   🎉 Excellente performance ! Système très intelligent');
        }
    }
    
    sauvegarderResultats() {
        try {
            const rapport = {
                timestamp: Date.now(),
                date: new Date().toISOString(),
                resultats_qi: this.resultats_qi,
                curseur_final: this.curseur,
                configuration_test: {
                    nombre_tests: this.tests_qi.length,
                    points_maximum: this.resultats_qi.points_maximum,
                    agent_utilise: this.config.model
                }
            };
            
            const cheminRapport = path.join(this.config.resultats, 'rapport_qi.json');
            fs.writeFileSync(cheminRapport, JSON.stringify(rapport, null, 2));
            
            console.log(`\n💾 Résultats sauvegardés: ${cheminRapport}`);
            
        } catch (error) {
            console.log(`⚠️ Erreur sauvegarde: ${error.message}`);
        }
    }
}

// Export
module.exports = TestQIAvance;

// Test automatique si exécuté directement
if (require.main === module) {
    console.log('🚀 LANCEMENT TEST QI AVANCÉ');
    console.log('============================');
    
    const testQI = new TestQIAvance();
    
    setTimeout(async () => {
        try {
            console.log('\n🧠 DÉBUT TEST QI AVEC MÉMOIRE THERMIQUE');
            
            const resultats = await testQI.executerTestQIComplet();
            
            console.log(`\n🎉 TEST QI TERMINÉ !`);
            console.log(`🎯 QI FINAL: ${resultats.qi_final} (${resultats.classification})`);
            
        } catch (error) {
            console.error('❌ Erreur test QI:', error.message);
        }
    }, 2000);
}
