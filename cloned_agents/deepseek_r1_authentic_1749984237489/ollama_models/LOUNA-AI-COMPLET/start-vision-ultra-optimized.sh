#!/bin/bash

# Script de démarrage pour Vision Ultra optimisé
# Créé par <PERSON>, Sainte-Anne, Guadeloupe (97180)

# Définir les couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Afficher le logo
echo -e "${PURPLE}"
echo "██╗   ██╗██╗███████╗██╗ ██████╗ ███╗   ██╗    ██╗   ██╗██╗  ████████╗██████╗  █████╗ "
echo "██║   ██║██║██╔════╝██║██╔═══██╗████╗  ██║    ██║   ██║██║  ╚══██╔══╝██╔══██╗██╔══██╗"
echo "██║   ██║██║███████╗██║██║   ██║██╔██╗ ██║    ██║   ██║██║     ██║   ██████╔╝███████║"
echo "╚██╗ ██╔╝██║╚════██║██║██║   ██║██║╚██╗██║    ██║   ██║██║     ██║   ██╔══██╗██╔══██║"
echo " ╚████╔╝ ██║███████║██║╚██████╔╝██║ ╚████║    ╚██████╔╝███████╗██║   ██║  ██║██║  ██║"
echo "  ╚═══╝  ╚═╝╚══════╝╚═╝ ╚═════╝ ╚═╝  ╚═══╝     ╚═════╝ ╚══════╝╚═╝   ╚═╝  ╚═╝╚═╝  ╚═╝"
echo -e "${NC}"

echo -e "${CYAN}Créé par <PERSON>, Sainte-Anne, Guadeloupe (97180)${NC}"
echo ""

# Chemin vers le répertoire de travail
WORK_DIR="/Volumes/seagate/Jarvis_Working/code/deepseek-node-ui"

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
    echo -e "${RED}Node.js n'est pas installé. Veuillez installer Node.js pour exécuter ce serveur.${NC}"
    exit 1
fi

# Vérifier si le fichier server-luna.js existe
if [ ! -f "$WORK_DIR/server-luna.js" ]; then
    echo -e "${RED}Le fichier server-luna.js n'existe pas dans le répertoire de travail.${NC}"
    exit 1
fi

# Vérifier si les modules Node.js sont installés
if [ ! -d "$WORK_DIR/node_modules" ]; then
    echo -e "${YELLOW}Installation des dépendances Node.js...${NC}"
    cd "$WORK_DIR" && npm install
fi

# Arrêter les serveurs existants
echo -e "${YELLOW}Arrêt des serveurs existants...${NC}"
pkill -f "node server-" || true

# Créer le dossier de configuration s'il n'existe pas
if [ ! -d "$WORK_DIR/data/config" ]; then
    echo -e "${YELLOW}Création du dossier de configuration...${NC}"
    mkdir -p "$WORK_DIR/data/config"
fi

# S'assurer que la configuration MCP est correcte
echo -e "${YELLOW}Configuration du MCP avec accès au bureau...${NC}"
cat > "$WORK_DIR/data/config/mcp-config.json" << EOF
{
  "port": 3002,
  "allowInternet": true,
  "allowDesktop": true,
  "allowSystemCommands": true,
  "debug": true
}
EOF
echo -e "${GREEN}Configuration MCP mise à jour avec accès Internet et bureau activés${NC}"

# S'assurer que la configuration du cerveau est correcte
echo -e "${YELLOW}Configuration du cerveau pour une réflexion rapide...${NC}"
cat > "$WORK_DIR/data/config/brain-config.json" << EOF
{
  "isActive": true,
  "autoActivate": true,
  "backgroundActivityInterval": 1000,
  "presenceUpdateInterval": 500,
  "thoughtGenerationInterval": 3000,
  "lastActivated": "$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")"
}
EOF
echo -e "${GREEN}Configuration du cerveau optimisée pour une réflexion rapide${NC}"

# Vérifier et créer le fichier de configuration de langue
echo -e "${YELLOW}Configuration de la langue en français...${NC}"
cat > "$WORK_DIR/data/config/language-config.json" << EOF
{
  "defaultLanguage": "fr-FR",
  "forceLanguage": true,
  "translationEnabled": true,
  "lastUpdated": "$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")"
}
EOF
echo -e "${GREEN}Configuration de langue mise à jour (français forcé)${NC}"

# Vérifier et créer le fichier de configuration de connexion
echo -e "${YELLOW}Configuration de la connexion stable...${NC}"
cat > "$WORK_DIR/data/config/connection-config.json" << EOF
{
  "maxReconnectAttempts": 20,
  "reconnectInterval": 3000,
  "autoReconnect": true,
  "keepAliveInterval": 30000,
  "lastUpdated": "$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")"
}
EOF
echo -e "${GREEN}Configuration de connexion mise à jour${NC}"

# Vérifier et créer le fichier de configuration des périphériques
echo -e "${YELLOW}Configuration des périphériques...${NC}"
cat > "$WORK_DIR/data/config/devices-config.json" << EOF
{
  "autoActivateMicrophone": true,
  "autoActivateCamera": true,
  "autoActivateSpeech": true,
  "persistDeviceState": true,
  "lastUpdated": "$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")"
}
EOF
echo -e "${GREEN}Configuration des périphériques mise à jour${NC}"

# Vérifier et créer le fichier de configuration des accélérateurs Kyber
echo -e "${YELLOW}Configuration des accélérateurs Kyber...${NC}"
cat > "$WORK_DIR/data/config/kyber-config.json" << EOF
{
  "enabled": true,
  "accelerationFactor": 3.0,
  "parallelProcessing": true,
  "optimizationLevel": "maximum",
  "lastUpdated": "$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")"
}
EOF
echo -e "${GREEN}Configuration des accélérateurs Kyber mise à jour${NC}"

# Démarrer le serveur Luna
echo -e "${GREEN}Démarrage de Vision Ultra optimisé...${NC}"
cd "$WORK_DIR" && NODE_OPTIONS="--max-old-space-size=4096" node server-luna.js &
SERVER_PID=$!

# Attendre que le serveur démarre
echo -e "${YELLOW}Attente du démarrage du serveur...${NC}"
sleep 3

# Vérifier si le serveur est toujours en cours d'exécution
if kill -0 $SERVER_PID 2>/dev/null; then
    echo -e "${GREEN}Serveur démarré avec succès (PID: $SERVER_PID)${NC}"
    
    # Ouvrir l'interface dans Opera
    echo -e "${GREEN}Ouverture de l'interface dans Opera...${NC}"
    open -a Opera http://localhost:3001/luna
    
    echo -e "${GREEN}Vision Ultra est maintenant opérationnel !${NC}"
    echo -e "${CYAN}Accédez à l'interface à l'adresse : http://localhost:3001/luna${NC}"
    echo -e "${YELLOW}Appuyez sur Ctrl+C pour arrêter le serveur${NC}"
    
    # Attendre que l'utilisateur appuie sur Ctrl+C
    wait $SERVER_PID
else
    echo -e "${RED}Erreur lors du démarrage du serveur${NC}"
    exit 1
fi
