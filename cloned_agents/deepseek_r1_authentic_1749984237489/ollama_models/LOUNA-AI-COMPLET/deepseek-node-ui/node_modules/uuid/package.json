{"name": "uuid", "version": "11.1.0", "description": "RFC9562 UUIDs", "type": "module", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122", "rfc9562"], "license": "MIT", "bin": {"uuid": "./dist/esm/bin/uuid"}, "sideEffects": false, "main": "./dist/cjs/index.js", "exports": {".": {"node": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "browser": {"import": "./dist/esm-browser/index.js", "require": "./dist/cjs-browser/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "module": "./dist/esm/index.js", "browser": {"./dist/esm/index.js": "./dist/esm-browser/index.js", "./dist/cjs/index.js": "./dist/cjs-browser/index.js"}, "files": ["dist", "!dist/**/test"], "devDependencies": {"@babel/eslint-parser": "7.25.9", "@commitlint/cli": "19.6.1", "@commitlint/config-conventional": "19.6.0", "@eslint/js": "9.17.0", "@types/eslint__js": "8.42.3", "bundlewatch": "0.4.0", "commander": "12.1.0", "eslint": "9.17.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "globals": "15.14.0", "husky": "9.1.7", "jest": "29.7.0", "lint-staged": "15.2.11", "neostandard": "0.12.0", "npm-run-all": "4.1.5", "prettier": "3.4.2", "release-please": "16.15.0", "runmd": "1.4.1", "standard-version": "9.5.0", "typescript": "5.0.4", "typescript-eslint": "8.18.2"}, "optionalDevDependencies": {"@wdio/browserstack-service": "9.2.1", "@wdio/cli": "9.2.1", "@wdio/jasmine-framework": "9.2.1", "@wdio/local-runner": "9.2.1", "@wdio/spec-reporter": "9.1.3", "@wdio/static-server-service": "9.1.3"}, "scripts": {"build": "./scripts/build.sh", "build:watch": "tsc --watch -p tsconfig.esm.json", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "docs:diff": "npm run docs && git diff --quiet README.md", "docs": "npm run build && npx runmd --output=README.md README_js.md", "eslint:check": "eslint src/ test/ examples/ *.[jt]s", "eslint:fix": "eslint --fix src/ test/ examples/ *.[jt]s", "examples:browser:rollup:build": "cd examples/browser-rollup && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm test", "examples:node:jest:test": "cd examples/node-jest && npm test", "examples:node:typescript:test": "cd examples/typescript && npm test", "lint": "npm run eslint:check && npm run prettier:check", "md": "runmd --watch --output=README.md README_js.md", "prepack": "npm run build -- --no-pack", "prepare": "husky", "prepublishOnly": "npm run build", "pretest:benchmark": "npm run build", "pretest:browser": "./scripts/iodd && npm run build && npm-run-all --parallel examples:browser:**", "pretest:node": "npm run build", "pretest": "npm run build", "prettier:check": "prettier --check .", "prettier:fix": "prettier --write .", "release": "standard-version --no-verify", "test:benchmark": "cd examples/benchmark && npm test", "test:browser": "wdio run ./wdio.conf.js", "test:node": "npm-run-all --parallel examples:node:**", "test:watch": "node --test --enable-source-maps --watch dist/esm/test/*.js", "test": "node --test --enable-source-maps dist/esm/test/*.js"}, "repository": {"type": "git", "url": "https://github.com/uuidjs/uuid.git"}, "lint-staged": {"*": ["prettier --no-error-on-unmatched-pattern --write"], "*.{js,jsx}": ["eslint --no-error-on-unmatched-pattern --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "packageManager": "npm@11.0.0"}