#!/bin/bash

# Script de démarrage pour l'Interface Louna locale
# Basé sur start-louna-thermal.sh mais utilisant les fichiers locaux

# Définir les couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Afficher le logo
echo -e "${PURPLE}"
echo "██╗      ██████╗ ██╗   ██╗███╗   ██╗ █████╗ "
echo "██║     ██╔═══██╗██║   ██║████╗  ██║██╔══██╗"
echo "██║     ██║   ██║██║   ██║██╔██╗ ██║███████║"
echo "██║     ██║   ██║██║   ██║██║╚██╗██║██╔══██║"
echo "███████╗╚██████╔╝╚██████╔╝██║ ╚████║██║  ██║"
echo "╚══════╝ ╚═════╝  ╚═════╝ ╚═╝  ╚═══╝╚═╝  ╚═╝"
echo -e "${NC}"

echo -e "${CYAN}Interface Cognitive avec Mémoire Thermique (Version Locale)${NC}"
echo ""

# Chemin vers le répertoire de travail local
LOCAL_DIR="/Users/<USER>/Documents/augment-projects/Jarvis/deepseek-node-ui"
# Chemin vers le répertoire externe pour les assets
EXTERNAL_DIR="/Volumes/seagate/Jarvis_Working/code/deepseek-node-ui"

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
    echo -e "${RED}Node.js n'est pas installé. Veuillez installer Node.js pour exécuter ce serveur.${NC}"
    exit 1
fi

# Vérifier si les modules Node.js sont installés
if [ ! -d "$LOCAL_DIR/node_modules" ]; then
    echo -e "${YELLOW}Installation des dépendances Node.js...${NC}"
    cd "$LOCAL_DIR" && npm install
fi

# Arrêter les serveurs existants
echo -e "${YELLOW}Arrêt des serveurs existants...${NC}"
pkill -f "node server-" || true

# Copier des fichiers de vues manquantes si nécessaire
echo -e "${YELLOW}Vérification des templates EJS...${NC}"
mkdir -p "$LOCAL_DIR/views" "$LOCAL_DIR/views/partials" "$LOCAL_DIR/public" "$LOCAL_DIR/public/js" "$LOCAL_DIR/public/css"

# Copie des fichiers depuis le disque externe
echo -e "${YELLOW}Copie des fichiers depuis le disque externe...${NC}"

# Copier les vues
if [ -d "$EXTERNAL_DIR/views" ]; then
    echo -e "${GREEN}Copie des vues...${NC}"
    cp -R "$EXTERNAL_DIR/views"/* "$LOCAL_DIR/views/"
fi

# Copier les fichiers statiques
if [ -d "$EXTERNAL_DIR/public" ]; then
    echo -e "${GREEN}Copie des fichiers statiques...${NC}"
    cp -R "$EXTERNAL_DIR/public"/* "$LOCAL_DIR/public/"
fi

# Démarrer le serveur Louna local
echo -e "${GREEN}Démarrage de l'Interface Louna Locale...${NC}"
cd "$LOCAL_DIR" && node server-louna.js &

# Attendre que le serveur démarre
echo -e "${YELLOW}Attente du démarrage du serveur...${NC}"
sleep 3

# Ouvrir l'interface dans le navigateur par défaut
echo -e "${GREEN}Ouverture de l'interface dans le navigateur...${NC}"
# Attendre un peu plus longtemps que le serveur soit complètement démarré
sleep 5
open http://localhost:3002

echo -e "${GREEN}L'Interface Louna est maintenant opérationnelle !${NC}"
echo -e "${CYAN}Accédez à l'interface à l'adresse : http://localhost:3002/louna${NC}"
echo -e "${YELLOW}Appuyez sur Ctrl+C pour arrêter le serveur${NC}"

# Attendre que l'utilisateur appuie sur Ctrl+C
wait
