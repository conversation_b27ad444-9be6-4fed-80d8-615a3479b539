# Système de Mémoire Thermique avec Régulation Réelle

Ce module fournit un système de mémoire thermique à 6 niveaux qui s'aligne sur les températures réelles de l'ordinateur pour assurer une circulation naturelle des informations.

## Niveaux de Mémoire Thermique

1. **Zone 1 (100°)** - Mémoire instantanée/immédiate (CPU très chaud, sous forte charge)
2. **Zone 2 (80°)** - Mémoire à court terme (CPU chaud, activité normale)
3. **Zone 3 (60°)** - Mémoire de travail (CPU tiède, charge modérée)
4. **Zone 4 (40°)** - Mémoire à moyen terme (CPU normal, légère activité)
5. **Zone 5 (20°)** - Zone créative et rêves (CPU frais, quasi au repos)
6. **Zone 6 (5°)** - Mémoire à long terme/archives (CPU froid, ventilateurs actifs)

## Régulation de Température Réelle

Le nouveau module `temperature-regulation.js` connecte le système de mémoire thermique aux températures réelles de l'ordinateur :

- Surveillance en temps réel de la température du CPU
- Correspondance entre température CPU et zones de mémoire
- Cycles de refroidissement périodiques pour permettre l'archivage profond (5°)
- Régulation automatique basée sur l'activité de l'ordinateur

## Fonctionnement

1. **Circulation Naturelle** : Quand le CPU est chaud (charge élevée), les informations sont dans les zones chaudes (1-2)
2. **Refroidissement Progressif** : Au fur et à mesure que le CPU se refroidit, les informations descendent vers les zones plus froides
3. **Archivage Profond** : Pendant les périodes de refroidissement (ventilateurs actifs, CPU peu sollicité), les informations peuvent atteindre la zone 6 (5°)

## Configuration

La régulation thermique est automatique mais peut être ajustée dans les paramètres de l'interface Luna.
