/**
 * Module de traitement des documents pour la mémoire thermique
 * Permet d'intégrer des documents de tout type dans le système de mémoire thermique
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { promisify } = require('util');
const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);

// Types de documents supportés et leurs processeurs
const SUPPORTED_DOCUMENT_TYPES = {
  // Documents texte
  '.txt': 'text',
  '.md': 'text',
  '.html': 'text',
  '.css': 'text',
  '.js': 'text',
  '.json': 'text',
  '.csv': 'text',
  '.xml': 'text',
  
  // Documents bureautiques
  '.pdf': 'binary',
  '.docx': 'binary',
  '.xlsx': 'binary',
  '.pptx': 'binary',
  
  // Images
  '.jpg': 'image',
  '.jpeg': 'image',
  '.png': 'image',
  '.gif': 'image',
  '.svg': 'image',
  
  // Audio
  '.mp3': 'audio',
  '.wav': 'audio',
  '.ogg': 'audio',
  
  // Vidéo
  '.mp4': 'video',
  '.webm': 'video',
  '.avi': 'video',
  
  // Archives
  '.zip': 'archive',
  '.rar': 'archive',
  '.tar': 'archive',
  '.gz': 'archive'
};

class DocumentProcessor {
  constructor(thermalMemory) {
    this.thermalMemory = thermalMemory;
    this.documentsPath = path.join(this.thermalMemory.memoryFolder, 'documents');
    
    // S'assurer que le dossier des documents existe
    if (!fs.existsSync(this.documentsPath)) {
      fs.mkdirSync(this.documentsPath, { recursive: true });
    }
    
    // Sous-dossiers par type
    this.ensureTypeDirectories();
    
    console.log('Processeur de documents pour la mémoire thermique initialisé');
  }
  
  // Créer les sous-dossiers par type de document
  ensureTypeDirectories() {
    const typeDirectories = ['text', 'binary', 'image', 'audio', 'video', 'archive'];
    
    typeDirectories.forEach(type => {
      const typeDir = path.join(this.documentsPath, type);
      if (!fs.existsSync(typeDir)) {
        fs.mkdirSync(typeDir, { recursive: true });
      }
    });
  }
  
  // Détecter le type de document
  detectDocumentType(filePath) {
    const extension = path.extname(filePath).toLowerCase();
    return SUPPORTED_DOCUMENT_TYPES[extension] || 'binary'; // Par défaut, considérer comme binaire
  }
  
  // Générer un hachage du fichier pour identifier les doublons
  async generateFileHash(filePath) {
    try {
      const fileData = await readFileAsync(filePath);
      return crypto.createHash('sha256').update(fileData).digest('hex');
    } catch (error) {
      console.error('Erreur lors de la génération du hash du fichier:', error);
      return crypto.createHash('sha256').update(Date.now().toString()).digest('hex');
    }
  }
  
  // Traiter un document et l'ajouter à la mémoire thermique
  async processDocument(filePath, options = {}) {
    try {
      if (!fs.existsSync(filePath)) {
        throw new Error(`Le fichier ${filePath} n'existe pas`);
      }
      
      // Détecter le type de document
      const documentType = this.detectDocumentType(filePath);
      
      // Générer un identifiant unique basé sur le contenu
      const fileHash = await this.generateFileHash(filePath);
      const documentId = `doc_${fileHash.substring(0, 12)}_${Date.now()}`;
      
      // Créer le document dans la mémoire thermique
      const documentInfo = {
        id: documentId,
        originalPath: filePath,
        filename: path.basename(filePath),
        extension: path.extname(filePath).toLowerCase(),
        type: documentType,
        size: fs.statSync(filePath).size,
        created: new Date().toISOString(),
        lastAccessed: new Date().toISOString(),
        metadata: options.metadata || {},
        zone: options.zone || 1, // Par défaut, placer dans la zone 1 (chaude)
        temperature: this.thermalMemory.config.temperatureZones[0].temp, // Température de la zone 1
        processed: false
      };
      
      // Copier le document dans le dossier approprié
      const targetDirectory = path.join(this.documentsPath, documentType);
      const targetPath = path.join(targetDirectory, `${documentId}${documentInfo.extension}`);
      
      await fs.promises.copyFile(filePath, targetPath);
      documentInfo.storagePath = targetPath;
      
      // Traitement spécifique selon le type
      await this.processDocumentByType(documentInfo);
      
      // Ajouter à la mémoire thermique
      this.addDocumentToMemory(documentInfo);
      
      console.log(`Document ajouté à la mémoire thermique: ${documentInfo.filename} (ID: ${documentId})`);
      
      return documentInfo;
    } catch (error) {
      console.error('Erreur lors du traitement du document:', error);
      throw error;
    }
  }
  
  // Traitement spécifique selon le type de document
  async processDocumentByType(documentInfo) {
    try {
      switch (documentInfo.type) {
        case 'text':
          // Pour les documents texte, extraire le contenu pour l'indexation
          const textContent = await readFileAsync(documentInfo.storagePath, 'utf8');
          documentInfo.textContent = textContent.substring(0, 10000); // Limiter la taille
          
          // Extraire des mots-clés pour la recherche
          documentInfo.keywords = this.extractKeywords(textContent);
          break;
          
        case 'image':
          // Pour les images, stocker des métadonnées de base
          documentInfo.metadata.description = documentInfo.metadata.description || 'Image';
          break;
          
        case 'audio':
          // Pour les fichiers audio, stocker des métadonnées de base
          documentInfo.metadata.description = documentInfo.metadata.description || 'Fichier audio';
          break;
          
        case 'video':
          // Pour les fichiers vidéo, stocker des métadonnées de base
          documentInfo.metadata.description = documentInfo.metadata.description || 'Fichier vidéo';
          break;
          
        default:
          // Pour les autres types, pas de traitement spécial
          break;
      }
      
      documentInfo.processed = true;
      return documentInfo;
    } catch (error) {
      console.error(`Erreur lors du traitement spécifique du document ${documentInfo.id}:`, error);
      documentInfo.processed = false;
      documentInfo.error = error.message;
    }
  }
  
  // Extraire des mots-clés d'un texte
  extractKeywords(text) {
    if (!text) return [];
    
    // Simplification : prendre les mots de plus de 4 lettres qui apparaissent plus d'une fois
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ') // Enlever la ponctuation
      .split(/\s+/) // Diviser par espace
      .filter(word => word.length > 4); // Mots de plus de 4 lettres
    
    // Compter les occurrences
    const wordCounts = {};
    words.forEach(word => {
      wordCounts[word] = (wordCounts[word] || 0) + 1;
    });
    
    // Prendre les mots qui apparaissent plus d'une fois
    const keywords = Object.entries(wordCounts)
      .filter(([_, count]) => count > 1)
      .map(([word]) => word)
      .slice(0, 20); // Limiter à 20 mots-clés
    
    return keywords;
  }
  
  // Ajouter le document à la mémoire thermique
  addDocumentToMemory(documentInfo) {
    try {
      // Vérifier si le document existe déjà
      const existingDocIndex = this.thermalMemory.memory.documents?.findIndex(doc => doc.id === documentInfo.id);
      
      // S'assurer que la propriété documents existe
      if (!this.thermalMemory.memory.documents) {
        this.thermalMemory.memory.documents = [];
      }
      
      // Mettre à jour ou ajouter
      if (existingDocIndex !== -1 && existingDocIndex !== undefined) {
        this.thermalMemory.memory.documents[existingDocIndex] = documentInfo;
      } else {
        this.thermalMemory.memory.documents.push(documentInfo);
      }
      
      // Sauvegarder la mémoire mise à jour
      this.thermalMemory.saveMemories();
      
      // Créer automatiquement une trace neurale pour ce document
      this.createNeuralTraceForDocument(documentInfo);
    } catch (error) {
      console.error(`Erreur lors de l'ajout du document ${documentInfo.id} à la mémoire:`, error);
    }
  }
  
  // Créer une trace neurale pour le document
  createNeuralTraceForDocument(documentInfo) {
    try {
      // S'assurer que l'état neural existe
      if (!this.thermalMemory.neuralState) {
        console.error('État neural non initialisé');
        return;
      }
      
      // Créer une essence (résumé) du document
      let essence = `Document: ${documentInfo.filename}`;
      if (documentInfo.metadata.description) {
        essence += ` - ${documentInfo.metadata.description}`;
      }
      
      // Extraire les mots-clés
      const keywords = documentInfo.keywords || [];
      
      // Créer la trace neurale
      const trace = {
        id: `trace_doc_${documentInfo.id}`,
        documentId: documentInfo.id,
        zone: documentInfo.zone,
        essence: essence,
        keywords: keywords,
        created: new Date().toISOString(),
        dreamCycles: 0,
        connections: [],
        strength: 0.5 // Force initiale
      };
      
      // Ajouter la trace à l'état neural
      this.thermalMemory.neuralState.dreams.push(trace);
      
      // Sauvegarder l'état neural
      this.thermalMemory.saveNeuralState();
      
      console.log(`Trace neurale créée pour le document: ${documentInfo.filename}`);
    } catch (error) {
      console.error(`Erreur lors de la création de la trace neurale pour le document ${documentInfo.id}:`, error);
    }
  }
  
  // Obtenir tous les documents dans la mémoire thermique
  getDocuments() {
    try {
      if (!this.thermalMemory.memory.documents) {
        return [];
      }
      return this.thermalMemory.memory.documents;
    } catch (error) {
      console.error('Erreur lors de la récupération des documents:', error);
      return [];
    }
  }
  
  // Rechercher des documents par mots-clés
  searchDocuments(query) {
    try {
      if (!this.thermalMemory.memory.documents) {
        return [];
      }
      
      const documents = this.thermalMemory.memory.documents;
      const queryTerms = query.toLowerCase().split(/\s+/).filter(term => term.length > 2);
      
      if (queryTerms.length === 0) {
        return documents;
      }
      
      // Fonction pour calculer un score de pertinence
      const calculateRelevance = (doc) => {
        let score = 0;
        
        // Recherche dans le nom de fichier
        const filename = doc.filename.toLowerCase();
        queryTerms.forEach(term => {
          if (filename.includes(term)) score += 5;
        });
        
        // Recherche dans les mots-clés
        if (doc.keywords) {
          queryTerms.forEach(term => {
            doc.keywords.forEach(keyword => {
              if (keyword.includes(term)) score += 3;
            });
          });
        }
        
        // Recherche dans le contenu textuel (si disponible)
        if (doc.textContent) {
          queryTerms.forEach(term => {
            if (doc.textContent.toLowerCase().includes(term)) score += 1;
          });
        }
        
        // Recherche dans la description (si disponible)
        if (doc.metadata && doc.metadata.description) {
          queryTerms.forEach(term => {
            if (doc.metadata.description.toLowerCase().includes(term)) score += 2;
          });
        }
        
        return score;
      };
      
      // Calculer le score pour chaque document et filtrer ceux avec un score > 0
      const scoredDocuments = documents
        .map(doc => ({ ...doc, relevance: calculateRelevance(doc) }))
        .filter(doc => doc.relevance > 0)
        .sort((a, b) => b.relevance - a.relevance);
      
      return scoredDocuments;
    } catch (error) {
      console.error('Erreur lors de la recherche de documents:', error);
      return [];
    }
  }
  
  // Accéder à un document et mettre à jour sa température/zone
  accessDocument(documentId) {
    try {
      if (!this.thermalMemory.memory.documents) {
        return null;
      }
      
      const docIndex = this.thermalMemory.memory.documents.findIndex(doc => doc.id === documentId);
      
      if (docIndex === -1) {
        return null;
      }
      
      // Mettre à jour la date d'accès
      this.thermalMemory.memory.documents[docIndex].lastAccessed = new Date().toISOString();
      
      // Mettre à jour la zone et la température (remonter vers la zone 1 si nécessaire)
      if (this.thermalMemory.memory.documents[docIndex].zone > 1) {
        this.thermalMemory.memory.documents[docIndex].zone--;
        this.thermalMemory.memory.documents[docIndex].temperature = 
          this.thermalMemory.config.temperatureZones[this.thermalMemory.memory.documents[docIndex].zone - 1].temp;
        
        console.log(`Document ${documentId} remonté vers la zone ${this.thermalMemory.memory.documents[docIndex].zone}`);
      }
      
      // Sauvegarder les changements
      this.thermalMemory.saveMemories();
      
      return this.thermalMemory.memory.documents[docIndex];
    } catch (error) {
      console.error(`Erreur lors de l'accès au document ${documentId}:`, error);
      return null;
    }
  }
  
  // Mettre à jour les zones des documents selon les principes thermodynamiques
  updateDocumentZones() {
    try {
      if (!this.thermalMemory.memory.documents || this.thermalMemory.memory.documents.length === 0) {
        return;
      }
      
      const now = new Date();
      
      // Parcourir tous les documents
      this.thermalMemory.memory.documents.forEach(doc => {
        const lastAccessed = new Date(doc.lastAccessed);
        const daysSinceAccess = (now - lastAccessed) / (1000 * 60 * 60 * 24);
        
        // Réduire la température des documents non accédés
        if (daysSinceAccess > 7 && doc.zone < 6) {
          // Après 7 jours sans accès, descendre d'une zone
          doc.zone++;
          doc.temperature = this.thermalMemory.config.temperatureZones[doc.zone - 1].temp;
          console.log(`Document ${doc.id} descendu vers la zone ${doc.zone} (inactif depuis ${Math.floor(daysSinceAccess)} jours)`);
        }
      });
      
      // Sauvegarder les changements
      this.thermalMemory.saveMemories();
    } catch (error) {
      console.error('Erreur lors de la mise à jour des zones des documents:', error);
    }
  }
}

module.exports = DocumentProcessor;