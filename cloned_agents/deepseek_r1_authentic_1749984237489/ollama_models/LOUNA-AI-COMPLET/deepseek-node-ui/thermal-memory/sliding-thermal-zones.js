/**
 * Module de zones thermiques glissantes pour le système de mémoire thermique
 * Ce module adapte dynamiquement les 6 zones de mémoire à la plage de température réelle de l'ordinateur
 * Les zones se déplacent ensemble comme un curseur unifié en fonction de la température
 */

const { EventEmitter } = require('events');
const os = require('os');
const fs = require('fs');
const path = require('path');

class SlidingThermalZones extends EventEmitter {
  constructor(thermalMemory) {
    super();
    this.thermalMemory = thermalMemory;

    // Configuration des zones thermiques
    this.zoneConfig = {
      count: 6,                     // Nombre de zones thermiques
      zoneNames: [                  // Noms descriptifs des zones
        'Instantanée',             // Zone 1 - 100° (Mémoire immédiate)
        'Court terme',             // Zone 2 - 80° (Mémoire à court terme)
        'Travail',                 // Zone 3 - 60° (Mémoire de travail)
        'Moyen terme',             // Zone 4 - 40° (Mémoire à moyen terme)
        'Créative/Rêve',           // Zone 5 - 20° (Zone créative et rêves)
        'Long terme'               // Zone 6 - 5° (Archives profondes)
      ],
      zoneTemps: [100, 80, 60, 40, 20, 5], // Températures idéales des zones
      adjustmentFactor: 1.0,        // Facteur d'ajustement pour le déplacement des zones
    };

    // État du système
    this.systemState = {
      cpuTemp: 0,                   // Température CPU actuelle
      fanSpeed: 0,                  // Vitesse du ventilateur
      tempRange: {
        min: 35,                    // Température minimale observée
        max: 85,                    // Température maximale observée
        current: {min: 35, max: 50} // Plage de température actuelle (glissante)
      },
      isInitialized: false,         // Indique si le système a été initialisé
      sleepMode: false,             // Mode sommeil
      lastUpdate: new Date(),       // Dernière mise à jour
      activeZone: 1                 // Zone active actuellement
    };

    // Zones thermiques calculées dynamiquement (seront mises à jour par le système)
    this.thermalZones = [];

    // Paramètres configurables
    this.config = {
      updateInterval: 60000,         // Intervalle de mise à jour (60 secondes - réduit davantage pour moins de logs)
      slidingWindowSize: 15,         // Taille de la fenêtre glissante (en degrés - augmentée pour transitions plus douces)
      minZoneSpacing: 3,             // Espacement minimum entre les zones (en degrés - augmenté pour plus de stabilité)
      temperatureTrackingInterval: 300000, // Intervalle de suivi des températures (5 minutes - réduit davantage la fréquence)
      sleepModeInterval: 600000,     // Intervalle de profondeur du mode sommeil (10 minutes - augmenté pour réduire les logs)
      sleepModeDepthFactor: 1.5,     // Facteur de profondeur du mode sommeil (réduit pour transitions plus douces)
      debugMode: false,              // Mode debug pour les logs détaillés (désactivé pour réduire les logs)
      logFrequency: 5                // Ne logger qu'une mise à jour sur 5 pour réduire le bruit
    };

    // Historique pour le suivi des températures
    this.temperatureHistory = [];

    // Dossier pour les logs
    this.logPath = path.join(__dirname, '../data/sliding-zones-logs');
    if (!fs.existsSync(this.logPath)) {
      fs.mkdirSync(this.logPath, { recursive: true });
    }

    this.log('Module de zones thermiques glissantes initialisé');

    // Initialiser le système
    this.initialize();
  }

  // Initialiser le système
  initialize() {
    // Créer les zones thermiques initiales
    this.updateThermalZones();

    // Démarrer la surveillance de la température
    this.startMonitoring();

    this.systemState.isInitialized = true;
    this.log('Système de zones thermiques glissantes démarré');

    // Émettre un événement d'initialisation
    this.emit('initialized', {
      thermalZones: this.thermalZones,
      systemState: this.systemState
    });
  }

  // Démarrer la surveillance de la température
  startMonitoring() {
    // Mettre à jour les zones thermiques périodiquement
    this.updateInterval = setInterval(() => {
      this.updateTemperature();
      this.updateThermalZones();
    }, this.config.updateInterval);

    // Mettre à jour la plage de température et l'historique
    this.trackingInterval = setInterval(() => {
      this.updateTemperatureRange();
    }, this.config.temperatureTrackingInterval);

    // Si le mode sommeil est activé, effectuer un traitement périodique profond
    this.sleepInterval = setInterval(() => {
      if (this.systemState.sleepMode) {
        this.processSleepMode();
      }
    }, this.config.sleepModeInterval);
  }

  // Arrêter la surveillance
  stopMonitoring() {
    if (this.updateInterval) clearInterval(this.updateInterval);
    if (this.trackingInterval) clearInterval(this.trackingInterval);
    if (this.sleepInterval) clearInterval(this.sleepInterval);

    this.log('Surveillance des zones thermiques arrêtée');
  }

  // Activer/désactiver le mode sommeil
  setSleepMode(enabled) {
    const previousState = this.systemState.sleepMode;
    this.systemState.sleepMode = enabled;

    if (enabled && !previousState) {
      this.log('🌙 Mode sommeil activé - Traitement profond et consolidation de la mémoire');
      this.processSleepMode(true); // Traitement initial immédiat
    } else if (!enabled && previousState) {
      this.log('☀️ Mode sommeil désactivé - Retour au mode normal');
    }

    // Émettre un événement de changement d'état
    this.emit('sleepModeChanged', {
      enabled: this.systemState.sleepMode,
      timestamp: new Date()
    });

    return this.systemState.sleepMode;
  }

  // Traiter le mode sommeil (déplacer les informations vers des zones plus profondes)
  processSleepMode(isInitial = false) {
    if (!this.systemState.sleepMode) return;

    // En mode sommeil, forcer les zones à se déplacer plus bas dans la mémoire
    const depthFactor = isInitial ? 1.5 : this.config.sleepModeDepthFactor;

    // Simuler une température plus basse pour décaler le curseur vers le bas
    const originalTemp = this.systemState.cpuTemp;
    const simulatedTemp = Math.max(
      this.systemState.tempRange.min,
      originalTemp - (this.config.slidingWindowSize * depthFactor)
    );

    this.log(`💤 Traitement du mode sommeil: Température ${originalTemp.toFixed(1)}°C -> ${simulatedTemp.toFixed(1)}°C (simulée)`);

    // Sauvegarder temporairement la température actuelle
    const savedTemp = this.systemState.cpuTemp;

    // Appliquer la température simulée et mettre à jour les zones
    this.systemState.cpuTemp = simulatedTemp;
    this.updateThermalZones();

    // Effectuer un traitement profond de la mémoire
    if (this.thermalMemory && this.thermalMemory.processDeepMemory) {
      this.thermalMemory.processDeepMemory();
      this.log('🧠 Traitement profond de la mémoire effectué en mode sommeil');
    }

    // Restaurer la température réelle après 30 secondes
    setTimeout(() => {
      this.systemState.cpuTemp = savedTemp;
      this.updateThermalZones();
      this.log(`Mode sommeil: Retour à la température réelle ${savedTemp.toFixed(1)}°C`);
    }, 30000);
  }

  // Mettre à jour la température CPU actuelle
  updateTemperature() {
    // Utiliser l'API os pour obtenir des informations CPU
    const cpuLoad = os.loadavg()[0]; // Charge CPU moyenne sur 1 minute
    const cpuCount = os.cpus().length;
    const cpuPercentage = (cpuLoad / cpuCount) * 100;

    // Estimer la température en fonction de la charge CPU
    // Plage approx. 35°C (idle) - 85°C (pleine charge)
    let estimatedTemp;

    if (this.temperatureHistory.length > 0) {
      // Variation très progressive basée sur la dernière lecture + charge CPU
      const lastTemp = this.systemState.cpuTemp;
      const targetTemp = 35 + (cpuPercentage / 100) * 50; // 35-85°C

      // Transition beaucoup plus douce (95% dernière temp, 5% nouvelle cible)
      // Comme une feuille qui tombe ou de l'eau qui coule
      estimatedTemp = lastTemp * 0.95 + targetTemp * 0.05;
    } else {
      // Première estimation directe
      estimatedTemp = 35 + (cpuPercentage / 100) * 50;
    }

    // Simuler la vitesse du ventilateur en fonction de la température
    const fanSpeed = 1000 + ((estimatedTemp - 35) / 50) * 5000; // 1000-6000 RPM

    // Mettre à jour l'état du système
    this.systemState.cpuTemp = estimatedTemp;
    this.systemState.fanSpeed = fanSpeed;
    this.systemState.lastUpdate = new Date();

    // Ajouter à l'historique de température
    this.temperatureHistory.push({
      timestamp: new Date(),
      temperature: estimatedTemp,
      cpuLoad: cpuPercentage,
      fanSpeed: fanSpeed
    });

    // Limiter la taille de l'historique
    if (this.temperatureHistory.length > 100) {
      this.temperatureHistory.shift();
    }

    if (this.config.debugMode) {
      this.log(`Température CPU: ${estimatedTemp.toFixed(1)}°C, Charge: ${cpuPercentage.toFixed(1)}%, Ventilateur: ${fanSpeed.toFixed(0)} RPM`);
    }

    // Émettre un événement de mise à jour de température
    this.emit('temperatureUpdated', {
      cpuTemp: estimatedTemp,
      fanSpeed: fanSpeed,
      cpuLoad: cpuPercentage
    });
  }

  // Mettre à jour la plage de température
  updateTemperatureRange() {
    if (this.temperatureHistory.length < 3) return; // Besoin de suffisamment de données

    // Extraire les températures de l'historique
    const temps = this.temperatureHistory.map(entry => entry.temperature);

    // Trouver les min/max récents (sur les 10 dernières lectures)
    const recentTemps = temps.slice(-10);
    const recentMin = Math.min(...recentTemps);
    const recentMax = Math.max(...recentTemps);

    // Mettre à jour la plage globale min/max si nécessaire
    if (recentMin < this.systemState.tempRange.min) {
      this.systemState.tempRange.min = recentMin;
    }

    if (recentMax > this.systemState.tempRange.max) {
      this.systemState.tempRange.max = recentMax;
    }

    // Ajuster la fenêtre glissante actuelle
    // Centrer sur la température actuelle avec une largeur de fenêtre configurable
    const currentTemp = this.systemState.cpuTemp;
    const halfWindow = this.config.slidingWindowSize / 2;

    let windowMin = Math.max(this.systemState.tempRange.min, currentTemp - halfWindow);
    let windowMax = Math.min(this.systemState.tempRange.max, currentTemp + halfWindow);

    // Assurer une taille minimale de fenêtre
    if (windowMax - windowMin < this.config.slidingWindowSize) {
      // Si la fenêtre est trop petite, l'étendre en respectant les limites min/max
      if (windowMax < this.systemState.tempRange.max) {
        windowMax = Math.min(this.systemState.tempRange.max, windowMin + this.config.slidingWindowSize);
      }
      if (windowMin > this.systemState.tempRange.min && (windowMax - windowMin) < this.config.slidingWindowSize) {
        windowMin = Math.max(this.systemState.tempRange.min, windowMax - this.config.slidingWindowSize);
      }
    }

    // Mettre à jour la plage actuelle
    this.systemState.tempRange.current.min = windowMin;
    this.systemState.tempRange.current.max = windowMax;

    if (this.config.debugMode) {
      this.log(`Plage de température mise à jour: ${windowMin.toFixed(1)}°C - ${windowMax.toFixed(1)}°C (Fenêtre glissante)`);
      this.log(`Plage globale: ${this.systemState.tempRange.min.toFixed(1)}°C - ${this.systemState.tempRange.max.toFixed(1)}°C`);
    }
  }

  // Calculer et mettre à jour les zones thermiques
  updateThermalZones() {
    // Si nous n'avons pas encore initialisé la plage de température, utiliser des valeurs par défaut
    if (!this.systemState.tempRange.current) {
      this.systemState.tempRange.current = {
        min: this.systemState.tempRange.min,
        max: this.systemState.tempRange.max
      };
    }

    // Obtenir la plage de température actuelle
    const minTemp = this.systemState.tempRange.current.min;
    const maxTemp = this.systemState.tempRange.current.max;
    const currentTemp = this.systemState.cpuTemp;

    // Calculer l'espacement des zones en fonction de la plage disponible
    // Les zones doivent être réparties uniformément dans la plage disponible
    const zoneCount = this.zoneConfig.count;
    const tempRange = maxTemp - minTemp;
    const zoneSpacing = Math.max(this.config.minZoneSpacing, tempRange / (zoneCount - 1));

    // Créer les zones thermiques
    this.thermalZones = [];
    for (let i = 0; i < zoneCount; i++) {
      // Température réelle pour cette zone (de la plus chaude à la plus froide)
      // Zone 1 (plus chaude) -> zone 6 (plus froide)
      const zoneRealTemp = maxTemp - (i * zoneSpacing);

      // Température virtuelle associée pour la mémoire thermique
      // Mappe les 6 zones de la plage réelle vers l'échelle idéale (100°-5°)
      const zoneIdealTemp = this.zoneConfig.zoneTemps[i];

      // Calculer le niveau d'activité de la zone (0-100%)
      // Plus la température réelle est proche, plus la zone est active
      // Utiliser une fonction sinusoïdale pour une transition plus douce entre les zones
      const tempDiff = Math.abs(currentTemp - zoneRealTemp);
      // Transition douce avec une courbe sinusoïdale (comme une onde)
      const zoneActivity = Math.max(0, Math.cos(Math.min(Math.PI/2, tempDiff * 0.3)) * 100);

      // Créer l'objet de zone
      this.thermalZones.push({
        id: i + 1,
        name: this.zoneConfig.zoneNames[i],
        realTemp: zoneRealTemp,
        idealTemp: zoneIdealTemp,
        activity: zoneActivity,
        active: tempDiff < 3 // La zone est active si la température est à moins de 3° de la zone
      });
    }

    // Déterminer la zone active
    let activeZone = 1;
    let minDiff = Infinity;

    for (let i = 0; i < this.thermalZones.length; i++) {
      const zone = this.thermalZones[i];
      const diff = Math.abs(currentTemp - zone.realTemp);

      if (diff < minDiff) {
        minDiff = diff;
        activeZone = i + 1;
      }
    }

    // Mettre à jour la zone active
    const previousActiveZone = this.systemState.activeZone;
    this.systemState.activeZone = activeZone;

    // Si la zone active a changé, notifier le système de mémoire thermique
    if (activeZone !== previousActiveZone) {
      const newZone = this.thermalZones[activeZone - 1];
      this.log(`Zone active: ${previousActiveZone} -> ${activeZone} (${newZone.idealTemp}°)`);

      // Informer le système de mémoire thermique
      if (this.thermalMemory && this.thermalMemory.setActiveZone) {
        this.thermalMemory.setActiveZone(activeZone, newZone.idealTemp);
      }

      // Émettre un événement de changement de zone
      this.emit('zoneChanged', {
        previousZone: previousActiveZone,
        newZone: activeZone,
        zones: this.thermalZones
      });
    }

    if (this.config.debugMode) {
      this.logZones();
    }

    return this.thermalZones;
  }

  // Obtenir l'état actuel des zones thermiques
  getThermalZonesState() {
    return {
      zones: this.thermalZones,
      systemState: this.systemState,
      temperature: {
        current: this.systemState.cpuTemp,
        range: this.systemState.tempRange,
        history: this.temperatureHistory.slice(-20) // Dernières 20 mesures
      },
      sleepMode: this.systemState.sleepMode
    };
  }

  // Journaliser l'état des zones thermiques
  logZones() {
    // Compteur statique pour limiter la fréquence des logs
    if (!this.logCounter) this.logCounter = 0;
    this.logCounter++;

    // Ne logger qu'une fois sur N (selon la fréquence configurée)
    if (this.logCounter % (this.config.logFrequency || 5) !== 0) {
      return;
    }

    let message = '🌡️ ZONES THERMIQUES GLISSANTES 🌡️\n';

    // Ajouter l'information de plage
    message += `Température CPU: ${this.systemState.cpuTemp.toFixed(1)}°C\n`;
    message += `Plage active: ${this.systemState.tempRange.current.min.toFixed(1)}°C - ${this.systemState.tempRange.current.max.toFixed(1)}°C\n`;
    message += `Mode sommeil: ${this.systemState.sleepMode ? 'ACTIVÉ' : 'désactivé'}\n`;
    message += '────────────────────────────────────\n';

    // Ajouter l'information de chaque zone
    for (const zone of this.thermalZones) {
      const activeMarker = zone.id === this.systemState.activeZone ? '✓' : ' ';
      message += `${activeMarker} Zone ${zone.id}: ${zone.realTemp.toFixed(1)}°C réel → ${zone.idealTemp}° mémoire (${zone.name}) - Activité: ${zone.activity.toFixed(0)}%\n`;
    }

    console.log(message);
  }

  // Fonction utilitaire de journalisation
  log(message) {
    // Compteur statique pour limiter la fréquence des logs
    if (!this.logMessageCounter) this.logMessageCounter = 0;
    this.logMessageCounter++;

    // Ne logger qu'une fois sur N (selon la fréquence configurée)
    // Sauf pour les messages importants (contenant certains mots-clés)
    const isImportantMessage =
      message.includes('erreur') ||
      message.includes('Erreur') ||
      message.includes('activé') ||
      message.includes('désactivé') ||
      message.includes('démarré') ||
      message.includes('arrêté');

    if (!isImportantMessage && this.logMessageCounter % (this.config.logFrequency || 5) !== 0) {
      return;
    }

    console.log(`[SlidingThermalZones] ${message}`);
  }
}

module.exports = SlidingThermalZones;
