/**
 * Module de régulation de température réelle pour le système de mémoire thermique
 * Ce module se connecte aux capteurs de température matériels et règle la mémoire thermique
 * en fonction des températures réelles de l'ordinateur
 */

const { exec } = require('child_process');
const os = require('os');
const fs = require('fs');
const path = require('path');

class TemperatureRegulation {
  constructor(thermalMemory) {
    this.thermalMemory = thermalMemory;
    this.temperatureData = {
      cpu: 0,
      gpu: 0,
      fan: 0,
      ambient: 20, // Température ambiante par défaut en °C
      lastUpdate: new Date(),
      minObserved: 35, // Température minimale observée (sera mise à jour dynamiquement)
      maxObserved: 85  // Température maximale observée (sera mise à jour dynamiquement)
    };
    
    // Constantes pour les zones thermiques
    this.memoryZones = {
      zone1: 100, // Mémoire instantanée/immédiate
      zone2: 80,  // Mémoire à court terme
      zone3: 60,  // Mémoire de travail
      zone4: 40,  // Mémoire à moyen terme
      zone5: 20,  // Zone créative et rêves
      zone6: 5    // Mémoire à long terme/archives
    };
    
    this.regulationSettings = {
      enabled: true,
      adaptToRealTemperature: true, // Adaptation dynamique à la plage réelle
      temperatureUpdateInterval: 60000, // Intervalle de mise à jour de la plage de température (1 minute)
      maxCoolingCycles: 3,    // Nombre maximum de cycles de refroidissement consécutifs
      minCoolingCycles: 1,    // Nombre minimum de cycles de refroidissement
      coolingInterval: 60000, // Intervalle entre les cycles de refroidissement (ms)
      temperatureRangeBuffer: 5, // Marge de sécurité pour la plage (5°C)
      autoAdjustMapping: true, // Ajustement automatique du mapping
      temperatureMapping: [], // Sera calculé dynamiquement
      currentCycleCount: 0
    };
    
    this.dataLog = [];
    this.loggingEnabled = true;
    this.logPath = path.join(__dirname, '../data/temperature-logs');
    
    // S'assurer que le répertoire de logs existe
    if (!fs.existsSync(this.logPath)) {
      fs.mkdirSync(this.logPath, { recursive: true });
    }
    
    console.log('Module de régulation de température réelle initialisé');
    this.startMonitoring();
  }
  
  // Démarrer la surveillance de température
  startMonitoring() {
    // Détecter le système d'exploitation
    this.platform = os.platform();
    console.log(`Surveillance de température initialisée sur plateforme: ${this.platform}`);
    
    // Initialiser le mapping de température
    this.recalculateTemperatureMapping();
    
    // Lancer la surveillance périodique
    this.monitoringInterval = setInterval(() => {
      this.updateTemperatures();
    }, 10000); // Vérifier toutes les 10 secondes
    
    // Configurer les cycles de refroidissement
    this.coolingInterval = setInterval(() => {
      this.runCoolingCycle();
    }, this.regulationSettings.coolingInterval);
    
    // Mise à jour périodique de la plage de température et recalibration
    this.temperatureRangeInterval = setInterval(() => {
      if (this.regulationSettings.adaptToRealTemperature) {
        this.recalculateTemperatureMapping();
        console.log(`🌡️ Plage de température recalculée: ${this.temperatureData.minObserved}°C-${this.temperatureData.maxObserved}°C`);
      }
    }, this.regulationSettings.temperatureUpdateInterval);
    
    // Effectuer une première mesure immédiate
    this.updateTemperatures();
  }
  
  // Recalculer le mapping de température basé sur les observations réelles
  recalculateTemperatureMapping() {
    // Assurer une plage minimale pour éviter les divisions par zéro
    const minTemp = this.temperatureData.minObserved;
    const maxTemp = Math.max(this.temperatureData.maxObserved, minTemp + 10);
    
    // Ajouter une marge de sécurité pour les températures extrêmes
    const buffer = this.regulationSettings.temperatureRangeBuffer;
    const safeMin = Math.max(minTemp - buffer, 20); // Jamais en dessous de 20°C
    const safeMax = maxTemp + buffer;
    
    // Calculer l'échelle de température (mapper la plage réelle vers 5-100°C)
    const realRange = safeMax - safeMin;
    
    console.log(`🌡️ Calibration de la température: ${safeMin}°C-${safeMax}°C -> 5°C-100°C`);
    
    // Générer le nouveau mapping
    this.regulationSettings.temperatureMapping = [
      { systemTemp: safeMax,                                  memoryZone: this.memoryZones.zone1 }, // Zone 1 - 100°
      { systemTemp: safeMax - (realRange * 0.2),              memoryZone: this.memoryZones.zone2 }, // Zone 2 - 80°
      { systemTemp: safeMax - (realRange * 0.4),              memoryZone: this.memoryZones.zone3 }, // Zone 3 - 60°
      { systemTemp: safeMax - (realRange * 0.6),              memoryZone: this.memoryZones.zone4 }, // Zone 4 - 40°
      { systemTemp: safeMax - (realRange * 0.8),              memoryZone: this.memoryZones.zone5 }, // Zone 5 - 20°
      { systemTemp: safeMin,                                  memoryZone: this.memoryZones.zone6 }  // Zone 6 - 5°
    ];
    
    // Afficher le nouveau mapping pour debug
    console.log('Nouveau mapping de température:');
    this.regulationSettings.temperatureMapping.forEach((mapping, index) => {
      console.log(`Zone ${index+1}: ${mapping.systemTemp.toFixed(1)}°C -> ${mapping.memoryZone}°`);
    });
  }
  
  // Mettre à jour les données de température
  updateTemperatures() {
    if (this.platform === 'darwin') {
      // macOS - Utiliser différentes méthodes pour obtenir les températures
      this.getMacTemperatures();
    } else if (this.platform === 'linux') {
      // Linux - Utiliser les capteurs
      this.getLinuxTemperatures();
    } else if (this.platform === 'win32') {
      // Windows - Utiliser WMI
      this.getWindowsTemperatures();
    } else {
      console.log(`Plateforme ${this.platform} non supportée pour la lecture directe des températures.`);
      this.simulateTemperatures(); // Utiliser des données simulées basées sur la charge CPU
    }
    
    // Mettre à jour la plage de température observée
    if (this.temperatureData.cpu < this.temperatureData.minObserved) {
      this.temperatureData.minObserved = this.temperatureData.cpu;
      // Recalculer le mapping si la variation est significative
      if (this.regulationSettings.autoAdjustMapping) {
        this.recalculateTemperatureMapping();
      }
    }
    
    if (this.temperatureData.cpu > this.temperatureData.maxObserved) {
      this.temperatureData.maxObserved = this.temperatureData.cpu;
      // Recalculer le mapping si la variation est significative
      if (this.regulationSettings.autoAdjustMapping) {
        this.recalculateTemperatureMapping();
      }
    }
  }
  
  // Obtenir les températures sur macOS
  getMacTemperatures() {
    // Utiliser l'activité CPU comme approximation de la température
    const cpuLoad = os.loadavg()[0]; // Charge CPU moyenne sur 1 minute
    const cpuCount = os.cpus().length;
    const cpuPercentage = (cpuLoad / cpuCount) * 100;
    
    // Calculer une température estimée basée sur la charge
    // CPU au repos ~ 45°C, CPU à pleine charge ~ 85°C
    const estimatedTemp = 45 + (cpuPercentage / 100) * 40;
    
    // Mettre à jour les données
    this.temperatureData.cpu = estimatedTemp;
    this.temperatureData.lastUpdate = new Date();
    
    // Estimer la vitesse du ventilateur basée sur la température
    // Ventilateur au repos ~ 1000 RPM, Ventilateur à fond ~ 5600 RPM
    this.temperatureData.fan = 1000 + ((estimatedTemp - 45) / 40) * 4600;
    
    this.logTemperatureData();
    this.adjustMemoryTemperatures();
    
    console.log(`Température CPU estimée: ${estimatedTemp.toFixed(1)}°C, Ventilateur: ${this.temperatureData.fan.toFixed(0)} RPM`);
  }
  
  // Obtenir les températures sur Linux (via sensors)
  getLinuxTemperatures() {
    exec('sensors', (error, stdout, stderr) => {
      if (error) {
        console.error(`Erreur lors de l'exécution de sensors: ${error.message}`);
        this.simulateTemperatures();
        return;
      }
      
      try {
        // Analyser la sortie de sensors pour trouver les températures
        const cpuTempMatch = stdout.match(/Core 0:\s+\+(\d+\.\d+)°C/);
        if (cpuTempMatch && cpuTempMatch[1]) {
          this.temperatureData.cpu = parseFloat(cpuTempMatch[1]);
        }
        
        // Rechercher les données du ventilateur
        const fanMatch = stdout.match(/fan1:\s+(\d+)\s+RPM/);
        if (fanMatch && fanMatch[1]) {
          this.temperatureData.fan = parseFloat(fanMatch[1]);
        }
        
        this.temperatureData.lastUpdate = new Date();
        this.logTemperatureData();
        this.adjustMemoryTemperatures();
      } catch (e) {
        console.error('Erreur lors de l\'analyse des données des capteurs:', e);
        this.simulateTemperatures();
      }
    });
  }
  
  // Obtenir les températures sur Windows
  getWindowsTemperatures() {
    // Windows nécessite des outils externes, utiliser la simulation pour l'instant
    this.simulateTemperatures();
  }
  
  // Simuler les températures basées sur la charge CPU
  simulateTemperatures() {
    const cpuLoad = os.loadavg()[0];
    const cpuCount = os.cpus().length;
    const cpuPercentage = (cpuLoad / cpuCount) * 100;
    
    // Température simulée: 40°C (idle) à 90°C (full load)
    const simulatedTemp = 40 + (cpuPercentage / 100) * 50;
    
    // Vitesse ventilateur simulée: 1200 RPM (idle) à 6000 RPM (full load)
    const simulatedFan = 1200 + (cpuPercentage / 100) * 4800;
    
    this.temperatureData.cpu = simulatedTemp;
    this.temperatureData.fan = simulatedFan;
    this.temperatureData.lastUpdate = new Date();
    
    this.logTemperatureData();
    this.adjustMemoryTemperatures();
    
    console.log(`Température CPU simulée: ${simulatedTemp.toFixed(1)}°C, Charge: ${cpuPercentage.toFixed(1)}%, Ventilateur: ${simulatedFan.toFixed(0)} RPM`);
  }
  
  // Ajuster les températures des zones de mémoire en fonction des températures système
  adjustMemoryTemperatures() {
    if (!this.thermalMemory || !this.regulationSettings.enabled) return;
    
    try {
      // Trouver la zone de mémoire correspondant à la température CPU actuelle
      const cpuTemp = this.temperatureData.cpu;
      
      // Vérifier que le mapping de température existe
      if (!this.regulationSettings.temperatureMapping || this.regulationSettings.temperatureMapping.length === 0) {
        console.log('Mapping de température non disponible, recalcul...');
        this.recalculateTemperatureMapping();
      }
      
      // Déterminer la zone mémoire correspondante en fonction de la température CPU
      let targetZoneTemp = this.memoryZones.zone1; // Par défaut: zone 1 (100°)
      let activeZone = 1;
      
      // Utiliser le mapping dynamique pour trouver la zone correspondante
      for (let i = 0; i < this.regulationSettings.temperatureMapping.length; i++) {
        const mapping = this.regulationSettings.temperatureMapping[i];
        if (cpuTemp <= mapping.systemTemp) {
          targetZoneTemp = mapping.memoryZone;
          activeZone = i + 1;
          break;
        }
      }
      
      // Afficher des informations de débogage sur la conversion de température
      console.log(`🌡️ Conversion: CPU à ${cpuTemp.toFixed(1)}°C réelle -> Mémoire à ${targetZoneTemp}° (Zone ${activeZone})`);
      
      // Log détaillé du mapping réel utilisé
      console.log(`Mapping utilisé: Température ${cpuTemp.toFixed(1)}°C dans la plage ${this.temperatureData.minObserved.toFixed(1)}-${this.temperatureData.maxObserved.toFixed(1)}°C`);
      
      // Si thermalMemory a une méthode pour ajuster les températures de zone
      if (this.thermalMemory.setActiveZone) {
        this.thermalMemory.setActiveZone(activeZone, targetZoneTemp);
      }
      
      // Si nous atteignons la zone 6 (archivage profond), activer l'archivage spécial
      if (activeZone === 6) {
        console.log('❄️ Zone d\'archivage profond (5°) active: déclenchement de l\'archivage profond');
        console.log(`   Température réelle: ${cpuTemp.toFixed(1)}°C, mappée à ${targetZoneTemp}° (zone thermique)`);
        if (this.thermalMemory.triggerDeepArchiving) {
          this.thermalMemory.triggerDeepArchiving();
        }
      }
    } catch (error) {
      console.error('Erreur lors de l\'ajustement des températures de mémoire:', error);
    }
  }
  
  // Exécuter un cycle de refroidissement
  runCoolingCycle() {
    if (!this.regulationSettings.enabled) return;
    
    // Incrémenter le compteur de cycles
    this.regulationSettings.currentCycleCount++;
    
    // Limiter le nombre maximum de cycles consécutifs
    if (this.regulationSettings.currentCycleCount > this.regulationSettings.maxCoolingCycles) {
      console.log(`Limite de ${this.regulationSettings.maxCoolingCycles} cycles de refroidissement atteinte. Pause.`);
      this.regulationSettings.currentCycleCount = 0;
      return;
    }
    
    console.log(`🧊 Cycle de refroidissement #${this.regulationSettings.currentCycleCount} initié`);
    
    // Simuler une baisse temporaire de température du système
    const originalCpuTemp = this.temperatureData.cpu;
    
    // Calculer un effet de refroidissement qui garantit d'atteindre la zone 6
    // Force la température à descendre au minimum observé ou en dessous
    const targetMinTemp = Math.min(this.temperatureData.minObserved, originalCpuTemp - 5);
    const coolingEffect = originalCpuTemp - targetMinTemp;
    
    this.temperatureData.cpu = targetMinTemp;
    console.log(`Refroidissement force: ${originalCpuTemp.toFixed(1)}°C -> ${this.temperatureData.cpu.toFixed(1)}°C (-${coolingEffect.toFixed(1)}°C)`);
    console.log(`Température minimale ${this.temperatureData.minObserved.toFixed(1)}°C mappée à la zone 6 (5°)`);
    
    // Ajuster les températures de mémoire
    this.adjustMemoryTemperatures();
    
    // Planifier la restauration de la température
    setTimeout(() => {
      this.temperatureData.cpu = originalCpuTemp;
      console.log(`Retour à la température normale: ${this.temperatureData.cpu.toFixed(1)}°C`);
      this.adjustMemoryTemperatures();
    }, 30000); // 30 secondes de refroidissement
  }
  
  // Enregistrer les données de température
  logTemperatureData() {
    if (!this.loggingEnabled) return;
    
    // Ajouter les données actuelles au journal
    this.dataLog.push({
      timestamp: new Date(),
      cpu: this.temperatureData.cpu,
      fan: this.temperatureData.fan
    });
    
    // Limiter la taille du journal en mémoire
    if (this.dataLog.length > 1000) {
      this.dataLog.shift();
    }
    
    // Périodiquement, écrire dans un fichier
    if (this.dataLog.length % 60 === 0) {
      const logFilePath = path.join(this.logPath, `temp-log-${new Date().toISOString().slice(0,10)}.json`);
      
      try {
        // Lire le fichier existant s'il existe
        let existingData = [];
        if (fs.existsSync(logFilePath)) {
          existingData = JSON.parse(fs.readFileSync(logFilePath, 'utf8'));
        }
        
        // Ajouter les nouvelles données
        const updatedData = existingData.concat(this.dataLog);
        
        // Écrire le fichier
        fs.writeFileSync(logFilePath, JSON.stringify(updatedData, null, 2));
        
        // Vider le journal en mémoire
        this.dataLog = [];
      } catch (error) {
        console.error('Erreur lors de l\'écriture du journal de température:', error);
      }
    }
  }
  
  // Obtenir les données de température actuelles
  getTemperatureData() {
    // Déterminer la zone active en fonction de la température actuelle
    let activeZone = 1;
    const cpuTemp = this.temperatureData.cpu;
    
    if (this.regulationSettings.temperatureMapping && this.regulationSettings.temperatureMapping.length > 0) {
      for (let i = 0; i < this.regulationSettings.temperatureMapping.length; i++) {
        if (cpuTemp <= this.regulationSettings.temperatureMapping[i].systemTemp) {
          activeZone = i + 1;
          break;
        }
      }
    }
    
    return {
      ...this.temperatureData,
      coolingCycleCount: this.regulationSettings.currentCycleCount,
      regulationEnabled: this.regulationSettings.enabled,
      adaptToRealTemperature: this.regulationSettings.adaptToRealTemperature,
      temperatureMapping: this.regulationSettings.temperatureMapping,
      activeZone: activeZone,
      zoneTemperature: this.regulationSettings.temperatureMapping[activeZone-1]?.memoryZone || 100
    };
  }
  
  // Activer/désactiver la régulation de température
  setRegulationEnabled(enabled) {
    this.regulationSettings.enabled = enabled;
    console.log(`Régulation de température ${enabled ? 'activée' : 'désactivée'}`);
    return this.regulationSettings.enabled;
  }
  
  // Déclencher manuellement un cycle de refroidissement
  triggerManualCooling() {
    console.log('Déclenchement manuel d\'un cycle de refroidissement');
    this.runCoolingCycle();
    return true;
  }
  
  // Arrêter la surveillance
  stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }
    if (this.coolingInterval) {
      clearInterval(this.coolingInterval);
    }
    console.log('Surveillance de température arrêtée');
  }
}

module.exports = TemperatureRegulation;
