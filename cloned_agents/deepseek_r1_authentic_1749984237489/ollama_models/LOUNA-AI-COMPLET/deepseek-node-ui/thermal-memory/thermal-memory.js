/**
 * Module de mémoire thermique pour DeepSeek
 *
 * Permet de conserver les conversations dans différentes zones de température
 * même si elles sont visuellement effacées de l'interface utilisateur.
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

class ThermalMemory {
  constructor() {
    // Utiliser le répertoire data/memory dans le projet au lieu du bureau
    this.memoryFolder = path.join(__dirname, '../data/memory');
    this.configPath = path.join(this.memoryFolder, 'memory_config.json');
    this.memoryPath = path.join(this.memoryFolder, 'thermal_memory.json');
    this.dreamPath = path.join(this.memoryFolder, 'dreams.json');
    this.neuralNetPath = path.join(this.memoryFolder, 'neural-connections.json');
    this.acceleratorsPath = path.join(this.memoryFolder, 'kyber-accelerators.json');
    this.monitorDataPath = path.join(this.memoryFolder, 'monitors-data.json');

    // S'assurer que les dossiers existent
    if (!fs.existsSync(this.memoryFolder)) {
      fs.mkdirSync(this.memoryFolder, { recursive: true });
    }

    console.log(`Mémoire thermique configurée pour utiliser le répertoire: ${this.memoryFolder}`);

    // État neuronal - similaire au cerveau (pour toutes les zones, pas juste la zone 6)
    this.neuralState = {
      dreams: [],
      connections: {},
      patterns: {},
      insights: [],
      lastDreamCycle: null,
      evolutionGeneration: 0
    };

    // Système d'accélérateurs Kyber - pour compression/décompression
    this.accelerators = {
      memory: Array(3).fill().map(() => this.createAccelerator('memory')),
      video: Array(3).fill().map(() => this.createAccelerator('video')),
      audio: Array(3).fill().map(() => this.createAccelerator('audio')),
      thermal: Array(3).fill().map(() => this.createAccelerator('thermal')),
      // Un accélérateur par zone thermique (pas seulement zone 6)
      zones: Array(6).fill().map((_, i) => this.createZoneAccelerator(i + 1))
    };

    // Données de monitoring pour les accélérateurs
    this.monitors = {
      memory: { usage: 0, compression: 0, throughput: 0, lastUpdate: null },
      video: { usage: 0, compression: 0, throughput: 0, lastUpdate: null },
      audio: { usage: 0, compression: 0, throughput: 0, lastUpdate: null },
      thermal: { usage: 0, compression: 0, throughput: 0, lastUpdate: null },
      zones: Array(6).fill().map(() => ({
        activity: 0,
        temperature: 0,
        compression: 0,
        evolution: 0,
        lastUpdate: null
      }))
    };

    // Charger la configuration et les mémoires
    this.loadConfig();
    this.loadMemories();
    this.loadNeuralState();
    this.loadAccelerators();

    // Initialiser le monitoring CPU si activé
    if (this.config.cpuMonitoring) {
      this.initCpuMonitoring();
    }

    // Initialiser le nettoyage automatique si activé
    if (this.config.autoCleanup) {
      this.initAutoCleanup();
    }

    // Initialiser le cycle de rêve et d'évolution (pour toutes les zones)
    this.initDreamCycle();

    // Initialiser le système d'accélérateurs Kyber
    this.initAccelerators();

    // Initialiser le moniteur des accélérateurs
    this.initAcceleratorMonitors();

    console.log('Module de mémoire thermique et réseau d\'accélérateurs Kyber initialisés');
  }

  // Créer un nouvel accélérateur Kyber
  createAccelerator(type) {
    return {
      id: `kyber-${type}-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
      type: type,
      compression: Math.random() * 0.4 + 0.6, // 60-100% d'efficacité de compression
      throughput: Math.random() * 500 + 500, // 500-1000 MB/s
      temperature: 0, // Température initiale
      load: 0, // Charge initiale
      active: true,
      created: new Date().toISOString(),
      lastActivity: new Date().toISOString(),
      evolutionLevel: 1, // Niveau d'évolution de l'accélérateur
      connections: [] // Connexions avec d'autres accélérateurs
    };
  }

  // Créer un accélérateur spécifique pour une zone thermique
  createZoneAccelerator(zoneNumber) {
    const acc = this.createAccelerator('zone');
    acc.id = `kyber-zone${zoneNumber}-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    acc.zone = zoneNumber;

    // Les zones plus chaudes ont des accélérateurs plus rapides
    if (zoneNumber <= 2) {
      // Zones chaudes - compression plus efficace, traitement plus rapide
      acc.compression = Math.random() * 0.2 + 0.8; // 80-100%
      acc.throughput = Math.random() * 800 + 700; // 700-1500 MB/s
    } else if (zoneNumber <= 4) {
      // Zones moyennes
      acc.compression = Math.random() * 0.3 + 0.6; // 60-90%
      acc.throughput = Math.random() * 500 + 500; // 500-1000 MB/s
    } else {
      // Zones froides - compression maximale pour stockage à long terme
      acc.compression = Math.random() * 0.1 + 0.85; // 85-95%
      acc.throughput = Math.random() * 300 + 200; // 200-500 MB/s
    }

    return acc;
  }

  // Charger les accélérateurs depuis le stockage
  loadAccelerators() {
    try {
      if (fs.existsSync(this.acceleratorsPath)) {
        this.accelerators = JSON.parse(fs.readFileSync(this.acceleratorsPath, 'utf8'));

        // Vérifier si tous les types d'accélérateurs sont présents
        if (!this.accelerators.memory || this.accelerators.memory.length < 3) {
          this.accelerators.memory = Array(3).fill().map(() => this.createAccelerator('memory'));
        }
        if (!this.accelerators.video || this.accelerators.video.length < 3) {
          this.accelerators.video = Array(3).fill().map(() => this.createAccelerator('video'));
        }
        if (!this.accelerators.audio || this.accelerators.audio.length < 3) {
          this.accelerators.audio = Array(3).fill().map(() => this.createAccelerator('audio'));
        }
        if (!this.accelerators.thermal || this.accelerators.thermal.length < 3) {
          this.accelerators.thermal = Array(3).fill().map(() => this.createAccelerator('thermal'));
        }
        if (!this.accelerators.zones || this.accelerators.zones.length < 6) {
          this.accelerators.zones = Array(6).fill().map((_, i) => this.createZoneAccelerator(i + 1));
        }

        console.log(`Accélérateurs Kyber chargés: ${this.countActiveAccelerators()} accélérateurs actifs`);
      } else {
        // Fichier n'existe pas, sauvegarder les accélérateurs par défaut
        this.saveAccelerators();
      }

      // Charger les données de monitoring si elles existent
      if (fs.existsSync(this.monitorDataPath)) {
        this.monitors = JSON.parse(fs.readFileSync(this.monitorDataPath, 'utf8'));
      }
    } catch (error) {
      console.error('Erreur lors du chargement des accélérateurs:', error);
    }
  }

  // Sauvegarder les accélérateurs
  saveAccelerators() {
    try {
      fs.writeFileSync(this.acceleratorsPath, JSON.stringify(this.accelerators, null, 2), 'utf8');
      fs.writeFileSync(this.monitorDataPath, JSON.stringify(this.monitors, null, 2), 'utf8');
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des accélérateurs:', error);
    }
  }

  // Compter le nombre d'accélérateurs actifs
  countActiveAccelerators() {
    let count = 0;

    // Compter pour chaque type
    Object.values(this.accelerators).forEach(accGroup => {
      if (Array.isArray(accGroup)) {
        count += accGroup.filter(acc => acc.active).length;
      }
    });

    return count;
  }

  // Charger l'état neuronal (zone d'évolution - zone 6)
  loadNeuralState() {
    try {
      // Charger les données de rêve (traitement de la zone 6)
      if (fs.existsSync(this.dreamPath)) {
        this.neuralState.dreams = JSON.parse(fs.readFileSync(this.dreamPath, 'utf8'));
        console.log(`${this.neuralState.dreams.length} rêves neuronaux chargés`);
      } else {
        this.neuralState.dreams = [];
        fs.writeFileSync(this.dreamPath, JSON.stringify(this.neuralState.dreams, null, 2), 'utf8');
      }

      // Charger les connexions neuronales
      if (fs.existsSync(this.neuralNetPath)) {
        const neuralData = JSON.parse(fs.readFileSync(this.neuralNetPath, 'utf8'));
        this.neuralState.connections = neuralData.connections || {};
        this.neuralState.patterns = neuralData.patterns || {};
        this.neuralState.insights = neuralData.insights || [];
        this.neuralState.evolutionGeneration = neuralData.evolutionGeneration || 0;
        console.log(`Réseau neuronal chargé: génération ${this.neuralState.evolutionGeneration}, ${Object.keys(this.neuralState.connections).length} connexions`);
      } else {
        // Créer un nouveau réseau vide
        this.saveNeuralState();
      }
    } catch (error) {
      console.error('Erreur lors du chargement de l\'état neuronal:', error);
      this.neuralState = {
        dreams: [],
        connections: {},
        patterns: {},
        insights: [],
        lastDreamCycle: null,
        evolutionGeneration: 0
      };
    }
  }

  // Sauvegarder l'état neuronal (cerveau)
  saveNeuralState() {
    try {
      // Sauvegarder les rêves (consolidation de mémoire, comme pendant le sommeil)
      fs.writeFileSync(this.dreamPath, JSON.stringify(this.neuralState.dreams, null, 2), 'utf8');

      // Sauvegarder les connexions neuronales et autres données d'évolution
      const neuralData = {
        connections: this.neuralState.connections,
        patterns: this.neuralState.patterns,
        insights: this.neuralState.insights,
        evolutionGeneration: this.neuralState.evolutionGeneration,
        lastUpdate: new Date().toISOString()
      };

      fs.writeFileSync(this.neuralNetPath, JSON.stringify(neuralData, null, 2), 'utf8');
      console.log(`État neuronal sauvegardé: génération ${this.neuralState.evolutionGeneration}`);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde de l\'état neuronal:', error);
    }
  }

  // Charger la configuration
  loadConfig() {
    try {
      if (!fs.existsSync(this.configPath)) {
        // Créer une configuration par défaut si elle n'existe pas
        this.config = {
          version: "1.0",
          created: new Date().toISOString(),
          maxMemorySize: 1000,
          temperatureZones: [
            { name: "Zone 1 - Récente", temp: 100, maxAgeHours: 24 },
            { name: "Zone 2 - Chaude", temp: 80, maxAgeHours: 72 },
            { name: "Zone 3 - Tiède", temp: 60, maxAgeHours: 168 },
            { name: "Zone 4 - Fraîche", temp: 40, maxAgeHours: 336 },
            { name: "Zone 5 - Froide", temp: 20, maxAgeHours: 720 },
            { name: "Zone 6 - Archive", temp: 5, maxAgeHours: null }
          ],
          cpuMonitoring: true,
          autoCleanup: true,
          cleanupInterval: 86400 // 24 heures en secondes
        };

        fs.writeFileSync(this.configPath, JSON.stringify(this.config, null, 2), 'utf8');
      } else {
        this.config = JSON.parse(fs.readFileSync(this.configPath, 'utf8'));
      }

      console.log('Configuration de mémoire thermique chargée');
    } catch (error) {
      console.error('Erreur lors du chargement de la configuration:', error);
      // Utiliser une configuration par défaut en cas d'erreur
      this.config = {
        version: "1.0",
        maxMemorySize: 1000,
        temperatureZones: [
          { name: "Zone 1", temp: 100, maxAgeHours: 24 }
        ],
        cpuMonitoring: false,
        autoCleanup: false
      };
    }
  }

  // Charger les mémoires
  loadMemories() {
    try {
      if (!fs.existsSync(this.memoryPath)) {
        // Créer un fichier de mémoire vide s'il n'existe pas
        this.memory = {
          memories: [],
          lastUpdated: new Date().toISOString(),
          stats: {
            totalMemories: 0,
            zone1Count: 0,
            zone2Count: 0,
            zone3Count: 0,
            zone4Count: 0,
            zone5Count: 0,
            zone6Count: 0
          }
        };

        fs.writeFileSync(this.memoryPath, JSON.stringify(this.memory, null, 2), 'utf8');
      } else {
        this.memory = JSON.parse(fs.readFileSync(this.memoryPath, 'utf8'));
      }

      console.log(`${this.memory.memories.length} mémoires chargées`);
    } catch (error) {
      console.error('Erreur lors du chargement des mémoires:', error);
      // Utiliser une mémoire vide en cas d'erreur
      this.memory = {
        memories: [],
        lastUpdated: new Date().toISOString(),
        stats: { totalMemories: 0 }
      };
    }
  }

  // Sauvegarder les mémoires
  saveMemories() {
    try {
      this.memory.lastUpdated = new Date().toISOString();
      fs.writeFileSync(this.memoryPath, JSON.stringify(this.memory, null, 2), 'utf8');
      console.log('Mémoires sauvegardées');
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des mémoires:', error);
    }
  }

  // Ajouter une conversation à la mémoire thermique
  addConversation(conversation) {
    try {
      // Vérifier si la conversation existe déjà
      const existingIndex = this.memory.memories.findIndex(mem => mem.id === conversation.id);

      if (existingIndex >= 0) {
        // Mettre à jour la conversation existante
        this.memory.memories[existingIndex] = {
          ...this.memory.memories[existingIndex],
          ...conversation,
          temperature: 100, // Réinitialiser la température à 100 (Zone 1)
          lastAccessed: new Date().toISOString(),
          isVisible: true // La conversation est visible dans l'interface
        };

        console.log(`Conversation existante mise à jour: ${conversation.id}`);
      } else {
        // Ajouter une nouvelle conversation
        this.memory.memories.push({
          ...conversation,
          temperature: 100,
          zone: 1,
          firstSaved: new Date().toISOString(),
          lastAccessed: new Date().toISOString(),
          isVisible: true
        });

        console.log(`Nouvelle conversation ajoutée: ${conversation.id}`);
      }

      // Mettre à jour les statistiques
      this.updateStats();

      // Sauvegarder les changements
      this.saveMemories();

      return true;
    } catch (error) {
      console.error('Erreur lors de l\'ajout d\'une conversation:', error);
      return false;
    }
  }

  // Stocker une entrée dans une zone spécifique
  storeInZone(zoneNumber, entry) {
    try {
      // Vérifier que la zone est valide (1-6)
      if (zoneNumber < 1 || zoneNumber > 6) {
        console.error(`Zone invalide: ${zoneNumber}. Doit être entre 1 et 6.`);
        return false;
      }

      // Créer un ID unique pour l'entrée si elle n'en a pas
      const entryId = entry.id || `mem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Déterminer la température en fonction de la zone
      const zoneTemp = this.config.temperatureZones[zoneNumber - 1].temp;

      // Créer l'entrée de mémoire
      const memoryEntry = {
        id: entryId,
        type: entry.type || 'general',
        content: entry.content,
        metadata: entry.metadata || {},
        temperature: zoneTemp,
        zone: zoneNumber,
        created: new Date().toISOString(),
        lastAccessed: new Date().toISOString(),
        accessCount: 0,
        isVisible: true
      };

      // Ajouter l'entrée à la mémoire
      this.memory.memories.push(memoryEntry);

      // Mettre à jour les statistiques
      this.updateStats();

      // Sauvegarder les changements
      this.saveMemories();

      console.log(`Entrée stockée dans la zone ${zoneNumber}: ${entryId}`);
      return true;
    } catch (error) {
      console.error(`Erreur lors du stockage dans la zone ${zoneNumber}:`, error);
      return false;
    }
  }

  // Marquer une conversation comme cachée (effacée visuellement)
  hideConversation(conversationId) {
    try {
      const index = this.memory.memories.findIndex(mem => mem.id === conversationId);

      if (index >= 0) {
        this.memory.memories[index].isVisible = false;
        this.memory.memories[index].lastAccessed = new Date().toISOString();

        console.log(`Conversation cachée: ${conversationId}`);

        // Sauvegarder les changements
        this.saveMemories();

        return true;
      }

      return false;
    } catch (error) {
      console.error('Erreur lors du masquage d\'une conversation:', error);
      return false;
    }
  }

  // Rendre une conversation visible à nouveau
  showConversation(conversationId) {
    try {
      const index = this.memory.memories.findIndex(mem => mem.id === conversationId);

      if (index >= 0) {
        this.memory.memories[index].isVisible = true;
        this.memory.memories[index].lastAccessed = new Date().toISOString();

        // Rafraîchir la température (la rendre plus chaude)
        const currentZone = this.memory.memories[index].zone;
        if (currentZone > 1) {
          this.memory.memories[index].zone = currentZone - 1;
          this.memory.memories[index].temperature = this.config.temperatureZones[currentZone - 2].temp;
        }

        console.log(`Conversation rendue visible: ${conversationId}`);

        // Sauvegarder les changements
        this.saveMemories();

        return this.memory.memories[index];
      }

      return null;
    } catch (error) {
      console.error('Erreur lors de l\'affichage d\'une conversation:', error);
      return null;
    }
  }

  // Obtenir une conversation par son ID
  getConversation(conversationId) {
    try {
      const conversation = this.memory.memories.find(mem => mem.id === conversationId);

      if (conversation) {
        // Mettre à jour l'horodatage d'accès
        conversation.lastAccessed = new Date().toISOString();
        this.saveMemories();
        return conversation;
      }

      return null;
    } catch (error) {
      console.error('Erreur lors de la récupération d\'une conversation:', error);
      return null;
    }
  }

  // Obtenir toutes les conversations (visibles ou non)
  getAllConversations(includeHidden = false) {
    try {
      if (includeHidden) {
        return this.memory.memories;
      } else {
        return this.memory.memories.filter(conv => conv.isVisible);
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des conversations:', error);
      return [];
    }
  }

  // Obtenir les conversations pour une zone de température spécifique
  getConversationsByZone(zoneNumber) {
    try {
      return this.memory.memories.filter(conv => conv.zone === zoneNumber);
    } catch (error) {
      console.error(`Erreur lors de la récupération des conversations de la zone ${zoneNumber}:`, error);
      return [];
    }
  }

  // Obtenir les entrées d'une zone spécifique
  getEntriesFromZone(zoneNumber, limit = 10) {
    try {
      // Récupérer les conversations de la zone spécifiée
      const zoneConversations = this.getConversationsByZone(zoneNumber);

      // Limiter le nombre de résultats
      return zoneConversations.slice(0, limit);
    } catch (error) {
      console.error(`Erreur lors de la récupération des entrées de la zone ${zoneNumber}:`, error);
      return [];
    }
  }

  // Obtenir les mémoires récentes pour le contexte
  getRecentMemoriesForContext(limit = 5) {
    try {
      console.log(`Récupération des ${limit} mémoires les plus récentes pour le contexte...`);

      // Récupérer toutes les mémoires visibles
      const visibleMemories = this.memory.memories.filter(mem => mem.isVisible !== false);

      // Trier par date d'accès (la plus récente en premier)
      const sortedMemories = visibleMemories.sort((a, b) => {
        const dateA = new Date(a.lastAccessed);
        const dateB = new Date(b.lastAccessed);
        return dateB - dateA;
      });

      // Limiter le nombre de mémoires
      const limitedMemories = sortedMemories.slice(0, limit);

      // Formater les mémoires pour le contexte
      const formattedMemories = limitedMemories.map(mem => {
        // Format pour les mémoires avec content.user et content.agent
        if (mem.content && mem.content.user && mem.content.agent) {
          return {
            id: mem.id,
            content: `Conversation précédente:\nUtilisateur: ${mem.content.user}\nAssistant: ${mem.content.agent}`,
            timestamp: mem.lastAccessed
          };
        }

        // Format pour les mémoires avec messages
        if (mem.messages && Array.isArray(mem.messages) && mem.messages.length > 0) {
          const formattedMessages = mem.messages.map(m =>
            `${m.role === 'user' ? 'Utilisateur' : 'Assistant'}: ${m.content}`
          ).join('\n');

          return {
            id: mem.id,
            content: `Conversation précédente:\n${formattedMessages}`,
            timestamp: mem.lastAccessed
          };
        }

        // Format par défaut
        return {
          id: mem.id,
          content: `Information mémorisée: ${JSON.stringify(mem.content || mem.messages || mem)}`,
          timestamp: mem.lastAccessed
        };
      });

      // Ajouter des instructions explicites pour le modèle sur l'utilisation de la mémoire
      const memoryInstructions = {
        id: 'memory_instructions',
        content: `INSTRUCTIONS CRITIQUES POUR L'UTILISATION DE LA MÉMOIRE:
1. Les informations ci-dessus proviennent de votre mémoire thermique et contiennent des données sur l'UTILISATEUR.
2. VOUS êtes Louna, l'assistante IA. L'UTILISATEUR est la personne qui vous parle.
3. Quand l'utilisateur demande "Comment je m'appelle ?", il veut savoir SON propre nom (celui de l'utilisateur), PAS le vôtre.
4. Quand l'utilisateur demande "Où j'habite ?", il veut savoir où LUI habite, PAS où vous habitez.
5. Si l'utilisateur a dit précédemment "Je m'appelle [Nom]", alors c'est SON nom, pas le vôtre.
6. Ne confondez JAMAIS votre identité (Louna) avec celle de l'utilisateur.
7. Ne répondez JAMAIS "Je m'appelle Louna" quand l'utilisateur demande son propre nom.
8. Utilisez TOUJOURS les informations de la mémoire pour répondre aux questions sur l'identité de l'utilisateur.
9. Si vous trouvez dans la mémoire que l'utilisateur s'appelle "Jean-Luc Picard", répondez "Vous vous appelez Jean-Luc Picard" et NON PAS "Je m'appelle Jean-Luc Picard".
10. Si vous trouvez dans la mémoire que l'utilisateur habite à "Paris", répondez "Vous habitez à Paris" et NON PAS "J'habite à Paris".`,
        timestamp: new Date().toISOString()
      };

      // Ajouter les instructions aux mémoires formatées
      formattedMemories.push(memoryInstructions);

      console.log(`${formattedMemories.length} mémoires récupérées pour le contexte (avec instructions)`);

      // Afficher un aperçu des mémoires pour le débogage
      formattedMemories.forEach((mem, index) => {
        console.log(`Mémoire ${index + 1}: ${mem.content.substring(0, 100)}...`);
      });

      return formattedMemories;
    } catch (error) {
      console.error('Erreur lors de la récupération des mémoires pour le contexte:', error);
      return [];
    }
  }

  // Supprimer définitivement une conversation
  deleteConversation(conversationId) {
    try {
      const initialLength = this.memory.memories.length;
      this.memory.memories = this.memory.memories.filter(conv => conv.id !== conversationId);

      if (initialLength > this.memory.memories.length) {
        console.log(`Conversation supprimée: ${conversationId}`);

        // Mettre à jour les statistiques
        this.updateStats();

        // Sauvegarder les changements
        this.saveMemories();

        return true;
      }

      return false;
    } catch (error) {
      console.error('Erreur lors de la suppression d\'une conversation:', error);
      return false;
    }
  }

  // Mettre à jour les statistiques de la mémoire
  updateStats() {
    try {
      // Réinitialiser les compteurs
      const stats = {
        totalMemories: this.memory.memories.length,
        zone1Count: 0,
        zone2Count: 0,
        zone3Count: 0,
        zone4Count: 0,
        zone5Count: 0,
        zone6Count: 0
      };

      // Compter les conversations par zone
      for (const conv of this.memory.memories) {
        const zoneKey = `zone${conv.zone}Count`;
        stats[zoneKey] = (stats[zoneKey] || 0) + 1;
      }

      this.memory.stats = stats;
    } catch (error) {
      console.error('Erreur lors de la mise à jour des statistiques:', error);
    }
  }

  // Mettre à jour les zones de température selon un vrai modèle thermodynamique
  updateTemperatureZones(systemHeatRatio = 0) {
    try {
      const now = new Date();
      let changesCount = 0;

      // Paramètres du modèle thermodynamique
      const baseThermodynamicRate = 0.2;  // Taux de base des mouvements thermiques
      const heatAccelerationFactor = 1 + (systemHeatRatio * 3); // L'accélération due à la chaleur du système

      for (const conv of this.memory.memories) {
        const lastAccessed = new Date(conv.lastAccessed);
        const ageHours = (now - lastAccessed) / (1000 * 60 * 60);

        // 1. Calculer la zone de base en fonction de l'âge (comme avant, mais ce n'est qu'un point de départ)
        let targetZone = 6; // Par défaut, zone d'archive

        for (let i = 0; i < this.config.temperatureZones.length; i++) {
          const zone = this.config.temperatureZones[i];

          if (zone.maxAgeHours === null || ageHours <= zone.maxAgeHours) {
            targetZone = i + 1;
            break;
          }
        }

        // 2. Déterminer l'activité thermique propre à cette conversation
        // Une conversation génère sa propre chaleur en fonction de son utilisation

        // Calculer la "chaleur" générée par cette conversation
        let convHeat = 0;

        // a. Facteur basé sur la récence d'utilisation
        const recencyFactor = Math.exp(-ageHours/24); // Décroissance exponentielle, 1.0 à 0.0

        // b. Facteur basé sur la richesse du contenu
        let contentRichnessFactor = 0;
        if (conv.messages) {
          // Plus il y a de messages, plus la conversation est "chaude"
          contentRichnessFactor = Math.min(1, conv.messages.length / 20); // Max à 20 messages
        }

        // c. Calculer la chaleur totale de la conversation (0-1)
        convHeat = (recencyFactor * 0.7) + (contentRichnessFactor * 0.3);

        // 3. Appliquer la physique thermique - mouvements probabilistes basés sur la chaleur

        // Si la zone actuelle est différente de la zone cible, calculer la probabilité de mouvement
        if (conv.zone !== targetZone) {
          // Direction du mouvement (monter = -1, descendre = +1)
          const direction = targetZone > conv.zone ? 1 : -1;

          // Probabilité de base de déplacement
          let moveProbability = baseThermodynamicRate;

          // Ajuster la probabilité en fonction de la chaleur du système et de la conversation
          if (direction === -1) { // Mouvement vers le haut (réchauffement)
            // La probabilité de monter augmente avec la chaleur de la conversation
            moveProbability *= (convHeat * heatAccelerationFactor);
          } else { // Mouvement vers le bas (refroidissement)
            // La probabilité de descendre augmente avec le manque de chaleur
            moveProbability *= ((1 - convHeat) * heatAccelerationFactor);
          }

          // Probabilité maximum de 90% pour éviter des sauts trop brusques
          moveProbability = Math.min(0.9, moveProbability);

          // Appliquer le changement de zone en fonction de la probabilité
          if (Math.random() < moveProbability) {
            // Mouvement d'une seule zone à la fois (comme dans un vrai système thermique)
            const oldZone = conv.zone;
            conv.zone += direction;

            // Limiter aux bornes des zones disponibles
            conv.zone = Math.max(1, Math.min(6, conv.zone));

            // Mettre à jour la température
            conv.temperature = this.config.temperatureZones[conv.zone - 1].temp;

            console.log(`Flux thermique: Conversation ${conv.id.substring(0, 6)}... ${direction < 0 ? '↑ monte' : '↓ descend'} de Zone ${oldZone} → ${conv.zone} (chaleur: ${(convHeat*100).toFixed(0)}%)`);
            changesCount++;
          }
        }

        // 4. Les conversations dans la zone 6 (archive) deviennent automatiquement cachées
        if (conv.zone === 6 && !conv.hidden) {
          conv.hidden = true;
          console.log(`Conversation ${conv.id.substring(0, 6)}... archivée automatiquement (zone 6)`);
        }

        // 5. Les conversations dans les zones supérieures (1-3) redeviennent visibles
        if (conv.zone <= 3 && conv.hidden) {
          conv.hidden = false;
          console.log(`Conversation ${conv.id.substring(0, 6)}... restaurée automatiquement (zone chaude ${conv.zone})`);
        }
      }

      // Mettre à jour les statistiques si des changements ont eu lieu
      if (changesCount > 0) {
        this.updateStats();
        // Sauvegarder les changements
        this.saveMemories();
        console.log(`Flux thermodynamique: ${changesCount} conversations ont migré entre les zones`);
      }
    } catch (error) {
      console.error('Erreur lors de la mise à jour des zones de température:', error);
    }
  }

  // Initialiser le vrai monitoring thermique du système avec accélérateurs Kyber
  initCpuMonitoring() {
    try {
      console.log('Réseau d\'accélérateurs Kyber activé');

      // Mesurer la véritable activité du système pour créer un modèle thermique réel
      const cpuThreshold = 0.75; // Seuil au-delà duquel le système est considéré "chaud"
      let systemHeatRatio = 0;
      let systemActivity = 0;

      // Créer un modèle de calcul thermique réel plutôt qu'une simulation
      setInterval(() => {
        try {
          // Mesurer l'activité réelle du système (conversations actives vs inactives)
          const totalConvs = this.memory.memories.length;
          if (totalConvs === 0) return;

          // Calculer le ratio d'activité (conversations récemment utilisées vs total)
          const now = new Date();
          const activeConvs = this.memory.memories.filter(conv => {
            const lastAccessed = new Date(conv.lastAccessed);
            const hoursSinceAccess = (now - lastAccessed) / (1000 * 60 * 60);
            return hoursSinceAccess < 12; // Conversations utilisées dans les 12 dernières heures
          }).length;

          // Ratio d'activité thermique du système (0-1)
          systemActivity = activeConvs / totalConvs;

          // Utiliser les accélérateurs Kyber pour amplifier l'activité thermique
          const thermalAcceleratorsEfficiency = this.getAcceleratorsTotalEfficiency('thermal');

          // Accumuler la chaleur dans le système en fonction de l'activité
          // Plus le système est actif, plus il chauffe rapidement (modèle thermodynamique)
          // Utiliser les accélérateurs pour amplifier ce processus
          systemHeatRatio = Math.min(1, systemHeatRatio + (systemActivity * 0.1 * thermalAcceleratorsEfficiency));

          // Dissipation naturelle de la chaleur (suit une courbe exponentielle comme un vrai système thermique)
          systemHeatRatio *= 0.9; // La chaleur se dissipe naturellement

          console.log(`État thermique: Activité ${(systemActivity*100).toFixed(1)}%, Chaleur ${(systemHeatRatio*100).toFixed(1)}%, Accélération Kyber ${thermalAcceleratorsEfficiency.toFixed(2)}x`);

          // Mettre à jour les moniteurs des accélérateurs thermiques
          this.updateAcceleratorMonitors('thermal', systemActivity, systemHeatRatio);

          // Si le système est chaud, les transferts thermiques sont plus rapides
          // Cela crée un véritable modèle thermodynamique où la chaleur accélère les mouvements
          if (systemHeatRatio > cpuThreshold) {
            console.log(`🔥 Cascade d'accélérateurs Kyber activée (${(systemHeatRatio*100).toFixed(1)}%)`);
            // La chaleur accumulée accélère les mouvements d'information via les accélérateurs
            this.accelerateThermodynamics(systemHeatRatio);
          }

          // Appliquer le modèle thermique aux zones de température
          this.updateTemperatureZones(systemHeatRatio);

          // Compression/décompression adaptive avec les accélérateurs Kyber
          if (totalConvs > 10) {
            this.compressMemories(systemHeatRatio);
          }
        } catch (err) {
          console.error('Erreur dans le cycle thermique:', err);
        }
      }, 30000); // Cycle thermique toutes les 30 secondes
    } catch (error) {
      console.error('Erreur lors de l\'initialisation de l\'accélérateur thermique:', error);
    }
  }

  // Initialiser les accélérateurs Kyber pour toutes les zones
  initAccelerators() {
    try {
      console.log('Initialisation des accélérateurs Kyber en cascade');

      // Vérifier si des accélérateurs supplémentaires sont nécessaires
      setInterval(() => {
        this.balanceAccelerators();
      }, 120000); // Vérifier l'équilibre des accélérateurs toutes les 2 minutes

      // Cycle de rafraîchissement des accélérateurs (pour éviter la surchauffe)
      setInterval(() => {
        this.refreshAccelerators();
      }, 300000); // Rafraîchir les accélérateurs toutes les 5 minutes

      // Premier équilibrage immédiat
      setTimeout(() => {
        this.balanceAccelerators();
      }, 10000);

      // Compression/décompression périodique des données
      setInterval(() => {
        const systemLoad = Math.random(); // Simuler une charge système
        this.compressMemories(systemLoad);
      }, 180000); // Toutes les 3 minutes
    } catch (error) {
      console.error('Erreur lors de l\'initialisation des accélérateurs Kyber:', error);
    }
  }

  // Initialiser les moniteurs pour les accélérateurs
  initAcceleratorMonitors() {
    try {
      console.log('Initialisation des moniteurs pour les accélérateurs Kyber');

      // Mise à jour périodique des moniteurs
      setInterval(() => {
        this.updateAllMonitors();
      }, 60000); // Mise à jour toutes les minutes

      // Premier rapport immédiat
      setTimeout(() => {
        this.generateAcceleratorsReport();
      }, 15000);
    } catch (error) {
      console.error('Erreur lors de l\'initialisation des moniteurs d\'accélérateurs:', error);
    }
  }

  // Mettre à jour tous les moniteurs
  updateAllMonitors() {
    try {
      // Mettre à jour les moniteurs pour chaque type d'accélérateur
      ['memory', 'video', 'audio', 'thermal'].forEach(type => {
        const usage = Math.random(); // Simuler l'utilisation
        this.updateAcceleratorMonitors(type, usage, Math.random());
      });

      // Mettre à jour les moniteurs pour chaque zone
      for (let i = 0; i < 6; i++) {
        const zoneIndex = i;
        const zoneNumber = i + 1;

        // Trouver les conversations dans cette zone
        const zoneConvs = this.memory.memories.filter(conv => conv.zone === zoneNumber);

        // Calculer l'activité de la zone
        const activity = zoneConvs.length / Math.max(1, this.memory.memories.length);

        // Mettre à jour le moniteur de la zone
        if (this.monitors.zones[zoneIndex]) {
          this.monitors.zones[zoneIndex].activity = activity;
          this.monitors.zones[zoneIndex].temperature = this.getZoneTemperature(zoneNumber);
          this.monitors.zones[zoneIndex].compression = this.getZoneCompression(zoneNumber);
          this.monitors.zones[zoneIndex].evolution = this.getZoneEvolution(zoneNumber);
          this.monitors.zones[zoneIndex].lastUpdate = new Date().toISOString();
        }
      }

      // Sauvegarder les données de monitoring
      this.saveAccelerators();
    } catch (error) {
      console.error('Erreur lors de la mise à jour des moniteurs:', error);
    }
  }

  // Obtenir la température d'une zone
  getZoneTemperature(zoneNumber) {
    try {
      // Vérifier que la configuration et les zones de température sont correctement initialisées
      if (!this.config || !this.config.temperatureZones || !Array.isArray(this.config.temperatureZones)) {
        console.warn('Configuration des zones de température non initialisée, utilisation des valeurs par défaut');
        // Retourner des valeurs par défaut basées sur le numéro de zone
        return 100 - ((zoneNumber - 1) * 20);
      }

      // Vérifier que l'index est valide
      if (zoneNumber >= 1 && zoneNumber <= 6 && this.config.temperatureZones.length >= zoneNumber) {
        const zone = this.config.temperatureZones[zoneNumber - 1];
        if (zone && typeof zone.temp === 'number') {
          return zone.temp;
        }
      }

      // Valeur par défaut si la zone n'existe pas ou n'a pas de température
      return 100 - ((zoneNumber - 1) * 20);
    } catch (error) {
      console.error('Erreur lors de l\'obtention de la température de zone:', error);
      // Valeur par défaut en cas d'erreur
      return 100 - ((zoneNumber - 1) * 20);
    }
  }

  // Obtenir le taux de compression d'une zone
  getZoneCompression(zoneNumber) {
    try {
      // Les zones plus froides ont un taux de compression plus élevé
      switch (zoneNumber) {
        case 1: return 0.3; // Zone 1: compression minimale pour accès rapide
        case 2: return 0.5;
        case 3: return 0.7;
        case 4: return 0.8;
        case 5: return 0.9;
        case 6: return 0.95; // Zone 6: compression maximale pour stockage à long terme
        default: return 0.5;
      }
    } catch (error) {
      console.error('Erreur lors de l\'obtention du taux de compression:', error);
      return 0.5;
    }
  }

  // Obtenir le niveau d'évolution d'une zone
  getZoneEvolution(zoneNumber) {
    try {
      // Calculer le niveau d'évolution moyen des accélérateurs de cette zone
      if (this.accelerators.zones && this.accelerators.zones[zoneNumber - 1]) {
        return this.accelerators.zones[zoneNumber - 1].evolutionLevel;
      }
      return 1;
    } catch (error) {
      console.error('Erreur lors de l\'obtention du niveau d\'évolution:', error);
      return 1;
    }
  }

  // Mettre à jour les moniteurs des accélérateurs d'un type spécifique
  updateAcceleratorMonitors(type, usage, activity) {
    try {
      if (this.monitors[type]) {
        this.monitors[type].usage = usage;
        this.monitors[type].throughput = this.getAcceleratorsThroughput(type);
        this.monitors[type].compression = this.getAcceleratorsCompression(type);
        this.monitors[type].lastUpdate = new Date().toISOString();
      }

      // Mettre à jour l'état des accélérateurs individuels
      if (this.accelerators[type]) {
        this.accelerators[type].forEach(acc => {
          acc.load = Math.min(1, acc.load * 0.7 + usage * 0.3); // Lissage de la charge
          acc.temperature = Math.min(1, acc.temperature * 0.8 + activity * 0.2); // Lissage de la température
          acc.lastActivity = new Date().toISOString();
        });
      }
    } catch (error) {
      console.error(`Erreur lors de la mise à jour des moniteurs ${type}:`, error);
    }
  }

  // Obtenir l'efficacité totale des accélérateurs d'un type
  getAcceleratorsTotalEfficiency(type) {
    try {
      if (!this.accelerators[type] || this.accelerators[type].length === 0) {
        return 1.0; // Valeur par défaut si aucun accélérateur
      }

      // Calculer l'efficacité moyenne des accélérateurs actifs
      const activeAccelerators = this.accelerators[type].filter(acc => acc.active);
      if (activeAccelerators.length === 0) return 1.0;

      // L'efficacité augmente avec le nombre d'accélérateurs en cascade
      const baseEfficiency = activeAccelerators.reduce((sum, acc) => sum + acc.compression, 0) / activeAccelerators.length;

      // Effet de cascade: plus d'accélérateurs = effet multiplicateur
      const cascadeMultiplier = 1 + (Math.log2(activeAccelerators.length) / 2);

      return baseEfficiency * cascadeMultiplier;
    } catch (error) {
      console.error(`Erreur lors du calcul de l'efficacité des accélérateurs ${type}:`, error);
      return 1.0;
    }
  }

  // Obtenir le débit total des accélérateurs d'un type
  getAcceleratorsThroughput(type) {
    try {
      if (!this.accelerators[type] || this.accelerators[type].length === 0) {
        return 0;
      }

      // Calculer le débit total des accélérateurs actifs
      return this.accelerators[type]
        .filter(acc => acc.active)
        .reduce((total, acc) => total + acc.throughput, 0);
    } catch (error) {
      console.error(`Erreur lors du calcul du débit des accélérateurs ${type}:`, error);
      return 0;
    }
  }

  // Obtenir le taux de compression moyen des accélérateurs d'un type
  getAcceleratorsCompression(type) {
    try {
      if (!this.accelerators[type] || this.accelerators[type].length === 0) {
        return 0;
      }

      const activeAccelerators = this.accelerators[type].filter(acc => acc.active);
      if (activeAccelerators.length === 0) return 0;

      return activeAccelerators.reduce((sum, acc) => sum + acc.compression, 0) / activeAccelerators.length;
    } catch (error) {
      console.error(`Erreur lors du calcul de la compression des accélérateurs ${type}:`, error);
      return 0;
    }
  }

  // Équilibrer les accélérateurs en fonction de la charge
  balanceAccelerators() {
    try {
      console.log('Équilibrage des accélérateurs Kyber');

      // Vérifier chaque type d'accélérateur
      ['memory', 'video', 'audio', 'thermal'].forEach(type => {
        // Si la charge moyenne est trop élevée, ajouter des accélérateurs par groupe de 3
        const activeAccelerators = this.accelerators[type].filter(acc => acc.active);
        if (activeAccelerators.length === 0) return;

        const avgLoad = activeAccelerators.reduce((sum, acc) => sum + acc.load, 0) / activeAccelerators.length;

        if (avgLoad > 0.8 && activeAccelerators.length < 9) {
          // Charge élevée, ajouter un groupe de 3 accélérateurs
          console.log(`Charge élevée détectée pour les accélérateurs ${type} (${(avgLoad*100).toFixed(1)}%)`);
          console.log(`Ajout d'un groupe de 3 accélérateurs ${type}`);

          for (let i = 0; i < 3; i++) {
            this.accelerators[type].push(this.createAccelerator(type));
          }

          console.log(`Nouveau total: ${this.accelerators[type].length} accélérateurs ${type}`);
        } else if (avgLoad < 0.2 && activeAccelerators.length > 3) {
          // Charge faible, réduire le nombre d'accélérateurs (mais garder au moins 3)
          console.log(`Charge faible détectée pour les accélérateurs ${type} (${(avgLoad*100).toFixed(1)}%)`);

          // Désactiver les accélérateurs les moins efficaces
          const excessCount = Math.min(3, activeAccelerators.length - 3);
          if (excessCount > 0) {
            console.log(`Désactivation de ${excessCount} accélérateurs ${type} inutilisés`);

            // Trier par charge (les moins utilisés d'abord)
            const sortedAccelerators = [...activeAccelerators].sort((a, b) => a.load - b.load);

            // Désactiver les accélérateurs les moins utilisés
            for (let i = 0; i < excessCount; i++) {
              const index = this.accelerators[type].findIndex(acc => acc.id === sortedAccelerators[i].id);
              if (index !== -1) {
                this.accelerators[type][index].active = false;
              }
            }
          }
        }
      });

      // Équilibrer les accélérateurs de zone
      for (let zoneNumber = 1; zoneNumber <= 6; zoneNumber++) {
        const zoneIndex = zoneNumber - 1;
        const zoneAccelerator = this.accelerators.zones[zoneIndex];

        // Évoluer l'accélérateur de zone en fonction de l'activité
        if (this.monitors.zones[zoneIndex] && this.monitors.zones[zoneIndex].activity > 0.5) {
          zoneAccelerator.evolutionLevel = Math.min(10, zoneAccelerator.evolutionLevel + 0.1);
          console.log(`Évolution de l'accélérateur de la zone ${zoneNumber} vers le niveau ${zoneAccelerator.evolutionLevel.toFixed(1)}`);
        }
      }

      // Sauvegarder les modifications
      this.saveAccelerators();

      // Générer un rapport
      this.generateAcceleratorsReport();
    } catch (error) {
      console.error('Erreur lors de l\'équilibrage des accélérateurs:', error);
    }
  }

  // Rafraîchir les accélérateurs pour éviter la surchauffe
  refreshAccelerators() {
    try {
      console.log('Rafraîchissement des accélérateurs Kyber');

      let refreshCount = 0;

      // Vérifier chaque type d'accélérateur
      Object.keys(this.accelerators).forEach(type => {
        if (Array.isArray(this.accelerators[type])) {
          this.accelerators[type].forEach(acc => {
            // Si l'accélérateur est trop chaud, le refroidir
            if (acc.temperature > 0.8) {
              acc.temperature *= 0.5; // Refroidissement rapide
              refreshCount++;
              console.log(`Refroidissement de l'accélérateur ${acc.id} (${type})`);
            }
          });
        }
      });

      if (refreshCount > 0) {
        console.log(`${refreshCount} accélérateurs rafraîchis`);
        this.saveAccelerators();
      }
    } catch (error) {
      console.error('Erreur lors du rafraîchissement des accélérateurs:', error);
    }
  }

  // Générer un rapport sur l'état des accélérateurs
  generateAcceleratorsReport() {
    try {
      console.log('📊 RAPPORT DES ACCÉLÉRATEURS KYBER 📊');
      console.log('────────────────────────────────────');

      const totalActive = this.countActiveAccelerators();
      console.log(`Total des accélérateurs actifs: ${totalActive}`);

      // Rapport pour chaque type d'accélérateur
      ['memory', 'video', 'audio', 'thermal'].forEach(type => {
        const active = this.accelerators[type].filter(acc => acc.active).length;
        const efficiency = this.getAcceleratorsTotalEfficiency(type);
        const throughput = this.getAcceleratorsThroughput(type);
        console.log(`${type.toUpperCase()}: ${active} actifs, efficacité ${(efficiency*100).toFixed(1)}%, débit ${throughput.toFixed(0)} MB/s`);
      });

      console.log('────────────────────────────────────');
      console.log('ACCÉLÉRATEURS DE ZONE:');

      // Rapport pour chaque zone
      for (let i = 0; i < 6; i++) {
        const zoneNumber = i + 1;
        const zoneAcc = this.accelerators.zones[i];
        if (zoneAcc) {
          const zoneStats = this.monitors.zones[i];
          const activity = zoneStats ? (zoneStats.activity * 100).toFixed(1) : '0.0';
          const evolution = zoneAcc.evolutionLevel.toFixed(1);

          console.log(`Zone ${zoneNumber}: Niveau ${evolution}, Activité ${activity}%, Compression ${(zoneAcc.compression*100).toFixed(0)}%`);
        }
      }

      console.log('────────────────────────────────────');
    } catch (error) {
      console.error('Erreur lors de la génération du rapport:', error);
    }
  }

  // Véritable accélération thermodynamique avec réseau d'accélérateurs Kyber dans toutes les zones
  accelerateThermodynamics(heatRatio) {
    try {
      // Utiliser les accélérateurs pour amplifier l'effet de transfert thermodynamique
      const thermalAcceleratorsEfficiency = this.getAcceleratorsTotalEfficiency('thermal');

      // Déterminer la probabilité de transfert en fonction de la chaleur et des accélérateurs
      const transferMultiplier = Math.min(3, 1 + (thermalAcceleratorsEfficiency * 2));

      console.log(`🔥 Accélération thermodynamique: x${transferMultiplier.toFixed(2)} (chaleur: ${(heatRatio*100).toFixed(1)}%, amplification Kyber: ${thermalAcceleratorsEfficiency.toFixed(2)}x)`);

      // Appliquer l'accélération dans chaque zone avec son accélérateur dédié
      for (let zoneNumber = 1; zoneNumber <= 6; zoneNumber++) {
        // Traiter chaque zone avec son propre accélérateur Kyber
        const zoneAccelerator = this.accelerators.zones[zoneNumber - 1];
        const zoneEfficiency = zoneAccelerator ? zoneAccelerator.compression : 1.0;

        // L'efficacité de l'accélérateur augmente avec son niveau d'évolution
        const evolutionBonus = zoneAccelerator ? (zoneAccelerator.evolutionLevel / 10) : 0;

        // Appliquer l'efficacité de l'accélérateur de zone
        const zoneTransferMultiplier = transferMultiplier * (1 + evolutionBonus) * zoneEfficiency;

        // 1. Conversations froides dans cette zone - probabilité de descendre
        const colderProb = Math.min(0.8, heatRatio * 0.5 * zoneTransferMultiplier);

        // 2. Conversations chaudes dans cette zone - probabilité de monter
        const hotterProb = Math.min(0.8, heatRatio * 0.7 * zoneTransferMultiplier);

        // Obtenir les conversations dans cette zone
        const zoneConversations = this.memory.memories.filter(conv => conv.zone === zoneNumber);

        if (zoneConversations.length > 0) {
          console.log(`Zone ${zoneNumber} | ${zoneConversations.length} conversations | Accélérateur: niveau ${zoneAccelerator.evolutionLevel.toFixed(1)}, efficacité ${(zoneEfficiency*100).toFixed(0)}%`);
        }

        // Traiter les conversations froides de cette zone (risque de descendre)
        const now = new Date();
        zoneConversations.forEach(conv => {
          // Vérifier l'âge et l'inactivité
          const lastAccessed = new Date(conv.lastAccessed);
          const hoursSinceAccess = (now - lastAccessed) / (1000 * 60 * 60);

          // Mise à jour de l'accélérateur de zone
          if (zoneAccelerator) {
            zoneAccelerator.lastActivity = new Date().toISOString();
            zoneAccelerator.load = Math.min(1, zoneAccelerator.load + 0.05);
          }

          if (hoursSinceAccess > 24 && conv.zone < 6) {
            // Conversation inactive - peut descendre vers une zone plus froide
            if (Math.random() < colderProb) {
              const oldZone = conv.zone;
              conv.zone = Math.min(6, conv.zone + 1);
              conv.temperature = this.config.temperatureZones[conv.zone - 1].temp;

              console.log(`⬇️ Accélérateur ${zoneAccelerator.id.substring(0, 8)}: Conversation "${conv.title}" refroidie Zone ${oldZone} → ${conv.zone}`);

              // Connexion neurale entre zones adjacentes
              this.createNeuralConnection('zone_transfer', oldZone, conv.zone, 0.3);

              // Faire évoluer l'accélérateur par l'usage
              if (zoneAccelerator) {
                zoneAccelerator.evolutionLevel = Math.min(10, zoneAccelerator.evolutionLevel + 0.01);
              }
            }
          } else if (hoursSinceAccess < 12 && conv.zone > 1) {
            // Conversation active - peut monter vers une zone plus chaude
            if (Math.random() < hotterProb) {
              const oldZone = conv.zone;
              conv.zone = Math.max(1, conv.zone - 1);
              conv.temperature = this.config.temperatureZones[conv.zone - 1].temp;

              console.log(`⬆️ Accélérateur ${zoneAccelerator.id.substring(0, 8)}: Conversation "${conv.title}" réchauffée Zone ${oldZone} → ${conv.zone}`);

              // Connexion neurale entre zones adjacentes
              this.createNeuralConnection('zone_transfer', oldZone, conv.zone, 0.3);

              // Faire évoluer l'accélérateur par l'usage
              if (zoneAccelerator) {
                zoneAccelerator.evolutionLevel = Math.min(10, zoneAccelerator.evolutionLevel + 0.01);
              }
            }
          }
        });
      }

      // Générer des connexions entre zones complémentaires (chaudes/froides)
      this.generateCrossZoneConnections(heatRatio);

      // Sauvegarder les changements de l'accélération thermique
      this.saveMemories();
      this.saveAccelerators();
      this.saveNeuralState();
    } catch (error) {
      console.error('Erreur dans l\'accélération thermodynamique:', error);
    }
  }

  // Créer une connexion neurale entre deux éléments
  createNeuralConnection(type, source, target, strength) {
    try {
      // Créer un identifiant unique pour cette connexion
      const connectionId = `${type}_${source}_${target}`;

      // Si la connexion existe déjà, renforcer sa force
      if (this.neuralState.connections[connectionId]) {
        this.neuralState.connections[connectionId].strength = Math.min(
          1.0,
          this.neuralState.connections[connectionId].strength + strength
        );
        this.neuralState.connections[connectionId].activations++;
      } else {
        // Sinon, créer une nouvelle connexion
        this.neuralState.connections[connectionId] = {
          type: type,
          source: source,
          target: target,
          strength: strength,
          created: new Date().toISOString(),
          lastActivated: new Date().toISOString(),
          activations: 1
        };
      }
    } catch (error) {
      console.error('Erreur lors de la création d\'une connexion neurale:', error);
    }
  }

  // Générer des connexions entre zones complémentaires
  generateCrossZoneConnections(heatRatio) {
    try {
      // Probabilité basée sur la chaleur du système
      const connectionProb = Math.min(0.3, heatRatio * 0.4);

      if (Math.random() < connectionProb) {
        // Connecter une zone chaude avec une zone froide
        const hotZone = Math.floor(Math.random() * 3) + 1; // Zones 1-3 (chaudes)
        const coldZone = Math.floor(Math.random() * 3) + 4; // Zones 4-6 (froides)

        // Force de connexion corrélée avec la différence de température
        const tempDiff = Math.abs(this.getZoneTemperature(hotZone) - this.getZoneTemperature(coldZone));
        const connectionStrength = Math.min(0.5, tempDiff / 200);

        this.createNeuralConnection('cross_zone', hotZone, coldZone, connectionStrength);

        console.log(`🔄 Connexion neurale créée: Zone ${hotZone} ↔ Zone ${coldZone} (force: ${(connectionStrength*100).toFixed(1)}%)`);
      }
    } catch (error) {
      console.error('Erreur lors de la génération de connexions inter-zones:', error);
    }
  }

  // Compresser/décompresser les mémoires en fonction de la charge système
  compressMemories(systemLoad) {
    try {
      // Utiliser les accélérateurs de mémoire pour la compression
      const memoryAcceleratorsEfficiency = this.getAcceleratorsTotalEfficiency('memory');

      console.log(`🧩 Cycle de compression/décompression Kyber: efficacité ${(memoryAcceleratorsEfficiency*100).toFixed(1)}%`);

      // Compter le nombre total de conversations
      const totalConvs = this.memory.memories.length;
      if (totalConvs === 0) return;

      let compressedCount = 0;
      let decompressedCount = 0;

      // Parcourir toutes les zones avec leurs accélérateurs dédiés
      for (let zoneNumber = 1; zoneNumber <= 6; zoneNumber++) {
        // Obtenir les conversations dans cette zone
        const zoneConversations = this.memory.memories.filter(conv => conv.zone === zoneNumber);
        if (zoneConversations.length === 0) continue;

        // Obtenir l'accélérateur de zone
        const zoneAccelerator = this.accelerators.zones[zoneNumber - 1];
        const zoneCompression = this.getZoneCompression(zoneNumber);

        // Les zones plus froides ont un taux de compression plus élevé
        const compressionThreshold = 0.3 + (zoneNumber * 0.1); // 0.4 pour zone 1, jusqu'à 0.9 pour zone 6
        const decompressionThreshold = 0.2 + (zoneNumber * 0.05); // 0.25 pour zone 1, jusqu'à 0.5 pour zone 6

        // Probabilité de compression/décompression
        const compressProbability = Math.min(0.7, systemLoad * memoryAcceleratorsEfficiency * zoneCompression);
        const decompressProbability = Math.min(0.3, (1 - systemLoad) * memoryAcceleratorsEfficiency * (1 - zoneCompression));

        // Parcourir les conversations de cette zone
        zoneConversations.forEach(conv => {
          // Si la conversation n'a pas d'attribut de compression, l'initialiser
          if (typeof conv.compressed === 'undefined') {
            conv.compressed = false;
            conv.compressionRatio = 1.0;
          }

          // Décider si on compresse ou décompresse
          if (!conv.compressed && Math.random() < compressProbability) {
            // Compression: seulement si la charge système est élevée et la conversation n'est pas déjà compressée
            if (systemLoad > compressionThreshold) {
              conv.compressed = true;
              conv.compressionRatio = zoneCompression;
              compressedCount++;

              // Mettre à jour l'activité de l'accélérateur
              zoneAccelerator.lastActivity = new Date().toISOString();
              zoneAccelerator.load = Math.min(1, zoneAccelerator.load + 0.1);
            }
          } else if (conv.compressed && Math.random() < decompressProbability) {
            // Décompression: seulement si la charge système est faible et la conversation est compressée
            if (systemLoad < decompressionThreshold) {
              conv.compressed = false;
              conv.compressionRatio = 1.0;
              decompressedCount++;

              // Mettre à jour l'activité de l'accélérateur
              zoneAccelerator.lastActivity = new Date().toISOString();
              zoneAccelerator.load = Math.min(1, zoneAccelerator.load + 0.1);
            }
          }
        });
      }

      if (compressedCount > 0 || decompressedCount > 0) {
        console.log(`🧩 Cycle Kyber: ${compressedCount} conversations compressées, ${decompressedCount} décompressées`);

        // Sauvegarder les changements
        this.saveMemories();
        this.saveAccelerators();
      }
    } catch (error) {
      console.error('Erreur lors de la compression/décompression des mémoires:', error);
    }
  }

  // Initialiser le nettoyage automatique et l'archivage
  initAutoCleanup() {
    try {
      console.log('Nettoyage automatique et archivage initialisés');

      // Nettoyage périodique (suppression des conversations les plus anciennes et froides si limite atteinte)
      setInterval(() => {
        this.cleanup();
      }, this.config.cleanupInterval * 1000);

      // Archivage automatique (mise à jour des zones de température) plus fréquent
      setInterval(() => {
        this.autoArchive();
      }, 300000); // Toutes les 5 minutes

      // Premier archivage immédiat
      setTimeout(() => {
        this.autoArchive();
      }, 5000);
    } catch (error) {
      console.error('Erreur lors de l\'initialisation du nettoyage automatique:', error);
    }
  }

  // Initialiser le cycle de rêve et d'évolution pour la zone 6 (fonctionne comme un vrai cerveau)
  initDreamCycle() {
    try {
      console.log('💭 Cycle de rêve et évolution neuronale initialisés dans toutes les zones');

      // Cycle de rêve principal (comme le sommeil REM) - traitement profond des mémoires
      // Ce cycle traite maintenant toutes les zones, pas seulement la zone 6
      setInterval(() => {
        this.dreamPhase();
      }, 600000); // Toutes les 10 minutes - cycle de sommeil REM

      // Cycle d'évolution - formations de nouvelles connexions neuronales
      setInterval(() => {
        this.evolveNeuralConnections();

        // Faire évoluer les accélérateurs Kyber en même temps
        this.evolveAccelerators();
      }, 900000); // Toutes les 15 minutes - évolution plus lente que les rêves

      // Cycle de consolidation - transformer les insights en connaissances (comme le sommeil profond)
      setInterval(() => {
        this.consolidateMemories();

        // Optimiser les accélérateurs Kyber lors de la consolidation
        this.optimizeAccelerators();
      }, 1800000); // Toutes les 30 minutes - consolidation lente et profonde

      // Cycle de maintenance des accélérateurs Kyber
      setInterval(() => {
        this.maintainAccelerators();
      }, 1200000); // Toutes les 20 minutes

      // Premier cycle de rêve après un court délai pour laisser le système se stabiliser
      setTimeout(() => {
        this.dreamPhase();
        this.evolveAccelerators();
      }, 30000); // Premier rêve après 30 secondes
    } catch (error) {
      console.error('Erreur lors de l\'initialisation du cycle de rêve:', error);
    }
  }

  // Faire évoluer les accélérateurs Kyber
  evolveAccelerators() {
    try {
      console.log('🧬 Évolution des accélérateurs Kyber...');

      // Compter le nombre d'accélérateurs actifs par type
      const counts = {
        memory: this.accelerators.memory.filter(acc => acc.active).length,
        video: this.accelerators.video.filter(acc => acc.active).length,
        audio: this.accelerators.audio.filter(acc => acc.active).length,
        thermal: this.accelerators.thermal.filter(acc => acc.active).length
      };

      // Évolution pour chaque type d'accélérateur
      Object.keys(counts).forEach(type => {
        // Plus il y a d'accélérateurs en cascade, plus l'évolution est rapide
        const evolutionRate = Math.min(0.05, 0.01 + (counts[type] * 0.005));

        // Faire évoluer chaque accélérateur actif
        this.accelerators[type].forEach(acc => {
          if (acc.active) {
            // L'évolution est basée sur l'usage récent
            const lastActivity = new Date(acc.lastActivity);
            const hoursSinceActivity = (new Date() - lastActivity) / (1000 * 60 * 60);

            if (hoursSinceActivity < 2) {
              // Accélérateur récemment utilisé - évolution plus rapide
              acc.evolutionLevel = Math.min(10, acc.evolutionLevel + evolutionRate);
              acc.compression = Math.min(0.95, acc.compression + (evolutionRate * 0.1));
              acc.throughput = Math.min(2000, acc.throughput * (1 + evolutionRate * 0.2));

              console.log(`Accélérateur ${type} ${acc.id.substring(0, 8)} évolue vers niveau ${acc.evolutionLevel.toFixed(2)}`);
            }
          }
        });
      });

      // Évolution spéciale pour les accélérateurs de zone
      for (let zoneNumber = 1; zoneNumber <= 6; zoneNumber++) {
        const zoneIndex = zoneNumber - 1;
        const zoneAcc = this.accelerators.zones[zoneIndex];

        if (zoneAcc) {
          // Compter les conversations dans cette zone
          const zoneConvs = this.memory.memories.filter(conv => conv.zone === zoneNumber).length;

          // L'évolution dépend du nombre de conversations et de l'activité récente
          const lastActivity = new Date(zoneAcc.lastActivity);
          const hoursSinceActivity = (new Date() - lastActivity) / (1000 * 60 * 60);

          if (hoursSinceActivity < 4 && zoneConvs > 0) {
            // Zone active avec des conversations - évolution proportionnelle
            const zoneEvolutionRate = Math.min(0.1, 0.02 + (zoneConvs * 0.01));
            zoneAcc.evolutionLevel = Math.min(10, zoneAcc.evolutionLevel + zoneEvolutionRate);

            console.log(`Accélérateur Zone ${zoneNumber} évolue vers niveau ${zoneAcc.evolutionLevel.toFixed(2)}`);
          }
        }
      }

      // Sauvegarder les changements
      this.saveAccelerators();
    } catch (error) {
      console.error('Erreur lors de l\'évolution des accélérateurs Kyber:', error);
    }
  }

  // Optimiser les accélérateurs Kyber
  optimizeAccelerators() {
    try {
      console.log('⚙️ Optimisation des accélérateurs Kyber...');

      // Optimiser les accélérateurs de chaque type
      ['memory', 'video', 'audio', 'thermal'].forEach(type => {
        // Trier les accélérateurs par niveau d'évolution (décroissant)
        const sortedAccs = [...this.accelerators[type]]
          .filter(acc => acc.active)
          .sort((a, b) => b.evolutionLevel - a.evolutionLevel);

        if (sortedAccs.length >= 3) {
          // Distribuer la charge de manière optimale
          sortedAccs.forEach((acc, index) => {
            // Les accélérateurs les plus évolués reçoivent plus de charge
            acc.load = Math.max(0.1, 1 - (index * 0.2));

            // Optimiser également les paramètres de compression et débit
            if (index === 0) {
              // Le meilleur accélérateur est optimisé pour la vitesse
              acc.compression = Math.min(0.9, acc.compression - 0.05);
              acc.throughput = Math.min(2000, acc.throughput * 1.05);
            } else if (index === sortedAccs.length - 1) {
              // Le dernier accélérateur est optimisé pour la compression
              acc.compression = Math.min(0.95, acc.compression + 0.05);
              acc.throughput = Math.max(100, acc.throughput * 0.95);
            }
          });
        }
      });

      // Optimiser les accélérateurs de zone
      // Zones chaudes: optimisées pour la vitesse
      // Zones froides: optimisées pour la compression
      for (let zoneNumber = 1; zoneNumber <= 6; zoneNumber++) {
        const zoneIndex = zoneNumber - 1;
        const zoneAcc = this.accelerators.zones[zoneIndex];

        if (zoneAcc) {
          if (zoneNumber <= 2) {
            // Zones chaudes - priorité à la vitesse
            zoneAcc.compression = Math.max(0.6, zoneAcc.compression - 0.02);
            zoneAcc.throughput = Math.min(2000, zoneAcc.throughput * 1.02);
          } else if (zoneNumber >= 5) {
            // Zones froides - priorité à la compression
            zoneAcc.compression = Math.min(0.95, zoneAcc.compression + 0.02);
            zoneAcc.throughput = Math.max(200, zoneAcc.throughput * 0.98);
          }
        }
      }

      // Sauvegarder les changements
      this.saveAccelerators();

      // Générer un rapport d'optimisation
      this.generateAcceleratorsReport();
    } catch (error) {
      console.error('Erreur lors de l\'optimisation des accélérateurs Kyber:', error);
    }
  }

  // Maintenance des accélérateurs
  maintainAccelerators() {
    try {
      console.log('🔧 Maintenance des accélérateurs Kyber...');

      // Vérifier les connexions entre accélérateurs
      for (let zoneNumber = 1; zoneNumber <= 6; zoneNumber++) {
        const zoneIndex = zoneNumber - 1;
        const zoneAcc = this.accelerators.zones[zoneIndex];

        if (zoneAcc) {
          // Réinitialiser les connexions
          zoneAcc.connections = [];

          // Connecter avec les zones adjacentes
          if (zoneNumber > 1) {
            // Connexion avec la zone précédente
            zoneAcc.connections.push({
              target: `zone${zoneNumber - 1}`,
              strength: 0.8, // Forte connexion avec zone adjacente
              type: 'adjacent'
            });
          }

          if (zoneNumber < 6) {
            // Connexion avec la zone suivante
            zoneAcc.connections.push({
              target: `zone${zoneNumber + 1}`,
              strength: 0.8, // Forte connexion avec zone adjacente
              type: 'adjacent'
            });
          }

          // Ajouter des connexions avec une zone thermodynamiquement complémentaire
          // Zone 1 <-> Zone 6, Zone 2 <-> Zone 5, Zone 3 <-> Zone 4
          const complementaryZone = 7 - zoneNumber;
          zoneAcc.connections.push({
            target: `zone${complementaryZone}`,
            strength: 0.5, // Connexion moyenne avec zone complémentaire
            type: 'complementary'
          });

          // Connexions avec les accélérateurs principaux
          ['memory', 'thermal'].forEach(type => {
            zoneAcc.connections.push({
              target: type,
              strength: 0.3, // Connexion faible avec accélérateurs principaux
              type: 'support'
            });
          });
        }
      }

      // Sauvegarder les changements
      this.saveAccelerators();
    } catch (error) {
      console.error('Erreur lors de la maintenance des accélérateurs Kyber:', error);
    }
  }

  // Nettoyer les conversations en fonction de la limite de taille
  cleanup() {
    try {
      if (this.memory.memories.length <= this.config.maxMemorySize) {
        console.log('Nettoyage non nécessaire');
        return;
      }

      // Nombre de conversations à supprimer
      const toRemove = this.memory.memories.length - this.config.maxMemorySize;

      // Trier par température (froide à chaude) et par date d'accès (plus ancien au plus récent)
      const sorted = [...this.memory.memories].sort((a, b) => {
        if (a.temperature !== b.temperature) {
          return a.temperature - b.temperature;
        }
        return new Date(a.lastAccessed) - new Date(b.lastAccessed);
      });

      // Supprimer les conversations les plus froides et les plus anciennes
      // MAIS: extraire l'essence des conversations avant suppression (apprentissage)
      const toRemoveConvs = sorted.slice(0, toRemove);
      const toRemoveIds = toRemoveConvs.map(conv => conv.id);

      // Avant de supprimer, préserver l'essence des conversations pour l'évolution neuronale
      this.extractMemoryEssence(toRemoveConvs);

      console.log(`Nettoyage: suppression de ${toRemove} conversations (après extraction d'essence)`);

      // Supprimer les conversations
      this.memory.memories = this.memory.memories.filter(conv => !toRemoveIds.includes(conv.id));

      // Mettre à jour les statistiques
      this.updateStats();

      // Sauvegarder les changements
      this.saveMemories();
    } catch (error) {
      console.error('Erreur lors du nettoyage des conversations:', error);
    }
  }

  // Extraire l'essence des conversations avant qu'elles ne soient définitivement supprimées
  // Similaire à la consolidation des souvenirs pendant le sommeil profond dans le cerveau humain
  extractMemoryEssence(conversations) {
    try {
      if (!conversations || conversations.length === 0) return;

      console.log(`Extraction de l'essence neuronale de ${conversations.length} conversations`);

      // Traiter chaque conversation pour en extraire l'essence
      for (const conv of conversations) {
        if (!conv.messages || conv.messages.length === 0) continue;

        // 1. Extraire les mots et concepts clés
        let keywords = new Set();
        let messageEssence = '';

        // Parcourir les messages pour extraire le contenu significatif
        conv.messages.forEach(msg => {
          if (msg.content) {
            // Extraire des mots clés significatifs (mots longs et non communs)
            const stopWords = ['avec', 'pour', 'dans', 'cette', 'comment', 'faire', 'vous', 'nous', 'mais', 'donc'];
            const words = msg.content.toLowerCase().split(/\W+/).filter(word =>
              word.length > 4 && !stopWords.includes(word)
            );

            words.forEach(word => keywords.add(word));

            // Ajouter l'essence du message
            if (msg.role === 'user') {
              messageEssence += msg.content.substring(0, 100) + ' ';
            }
          }
        });

        // 2. Créer une trace mémorielle pour la zone 6 (archive onirique)
        const memoryTrace = {
          id: conv.id,
          essence: messageEssence.trim(),
          keywords: Array.from(keywords),
          created: conv.created,
          temperature: 5, // Température minimale (zone 6)
          neuronal: true, // Marquer comme trace neuronale (pour le traitement onirique)
          connections: [], // Connexions formées pendant les cycles de rêve
          evolution: 0, // Niveau d'évolution de cette trace mémorielle
          dreamCycles: 0 // Nombre de cycles de rêve qui ont traité cette trace
        };

        // 3. Stocker la trace dans le réseau neuronal (zone 6)
        this.neuralState.dreams.push(memoryTrace);

        // 4. Créer des points d'entrée dans le réseau de connexions pour cette trace
        keywords.forEach(keyword => {
          if (!this.neuralState.connections[keyword]) {
            this.neuralState.connections[keyword] = [];
          }
          this.neuralState.connections[keyword].push(conv.id);
        });

        console.log(`🧠 Trace mémorielle créée pour ${conv.id.substring(0, 6)}... (${keywords.size} concepts)`);
      }

      // Sauvegarder l'état neuronal mis à jour
      this.saveNeuralState();

    } catch (error) {
      console.error('Erreur lors de l\'extraction d\'essence mémorielle:', error);
    }
  }

  // Processus thermodynamique complet - véritable circulation thermique des informations
  autoArchive() {
    try {
      console.log('🌡️ Cycle thermodynamique des mémoires en cours...');
      const now = new Date();

      // 1. Analyse de l'entropie du système
      // L'entropie augmente naturellement dans un système thermique fermé
      const totalConvs = this.memory.memories.length;
      if (totalConvs === 0) {
        console.log('Aucune mémoire à traiter');
        return;
      }

      // Calculer la distribution actuelle des conversations dans les zones
      const zoneDistribution = [0, 0, 0, 0, 0, 0];  // Indices 0-5 pour zones 1-6
      for (const conv of this.memory.memories) {
        if (conv.zone >= 1 && conv.zone <= 6) {
          zoneDistribution[conv.zone - 1]++;
        }
      }

      // Calculer l'entropie du système (mesure du désordre)
      let entropy = 0;
      for (let i = 0; i < 6; i++) {
        const p = zoneDistribution[i] / totalConvs;
        if (p > 0) {
          entropy -= p * Math.log2(p);  // Formule de l'entropie de Shannon
        }
      }

      // Normaliser l'entropie (0-1)
      const maxEntropy = Math.log2(6);  // Entropie maximale possible avec 6 zones
      const normalizedEntropy = entropy / maxEntropy;

      console.log(`Entropie du système: ${(normalizedEntropy * 100).toFixed(1)}%`);
      console.log(`Distribution thermique: Z1:${zoneDistribution[0]} Z2:${zoneDistribution[1]} Z3:${zoneDistribution[2]} Z4:${zoneDistribution[3]} Z5:${zoneDistribution[4]} Z6:${zoneDistribution[5]}`);

      // 2. Appliquer la dynamique des fluides thermiques
      // Dans un vrai système thermique, la chaleur crée des courants de convection

      // Identifier les conversations "chaudes" qui pourraient générer des courants ascendants
      const hotSpots = this.memory.memories.filter(conv => {
        const lastAccessed = new Date(conv.lastAccessed);
        const hoursSinceAccess = (now - lastAccessed) / (1000 * 60 * 60);
        return hoursSinceAccess < 6 && conv.zone > 1; // Conv récentes mais pas en zone 1
      });

      // Identifier les conversations "froides" qui pourraient générer des courants descendants
      const coldSpots = this.memory.memories.filter(conv => {
        const lastAccessed = new Date(conv.lastAccessed);
        const hoursSinceAccess = (now - lastAccessed) / (1000 * 60 * 60);
        return hoursSinceAccess > 72 && conv.zone < 5; // Conv anciennes mais pas en zones froides
      });

      console.log(`Courants thermiques détectés: ${hotSpots.length} points chauds, ${coldSpots.length} points froids`);

      // 3. Créer un véritable flux thermique - les points chauds créent des mouvements circulaires
      if (hotSpots.length > 0 || coldSpots.length > 0) {
        // a. Réchauffer les zones autour des points chauds (convection thermique)
        for (const hotSpot of hotSpots) {
          // Trouver les conversations "voisines" (créées à des dates proches)
          const neighbors = this.memory.memories.filter(conv => {
            if (conv.id === hotSpot.id) return false;

            const convDate = new Date(conv.created);
            const hotSpotDate = new Date(hotSpot.created);
            const daysDiff = Math.abs(convDate - hotSpotDate) / (1000 * 60 * 60 * 24);

            return daysDiff < 3; // Conversations créées à moins de 3 jours d'intervalle
          });

          // Réchauffer les voisins (effet de convection)
          for (const neighbor of neighbors) {
            if (neighbor.zone > 1) {
              // Monter d'une zone (devenir plus chaud)
              neighbor.zone--;
              neighbor.temperature = this.config.temperatureZones[neighbor.zone - 1].temp;

              // Les conversations qui deviennent chaudes redeviennent visibles
              if (neighbor.zone <= 3 && neighbor.hidden) {
                neighbor.hidden = false;
              }

              console.log(`🔥 Convection thermique: ${neighbor.id.substring(0, 6)}... réchauffée par proximité (→ Zone ${neighbor.zone})`);
            }
          }
        }

        // b. Refroidir les zones autour des points froids (flux thermique descendant)
        for (const coldSpot of coldSpots) {
          // Effet de refroidissement par "conduction" (transfert direct)
          // Plus une conversation est froide, plus elle entraîne ses voisins
          const neighbors = this.memory.memories.filter(conv => {
            if (conv.id === coldSpot.id) return false;

            // Voisinage thématique (si implémenté) ou temporel
            const convDate = new Date(conv.created);
            const coldSpotDate = new Date(coldSpot.created);
            const daysDiff = Math.abs(convDate - coldSpotDate) / (1000 * 60 * 60 * 24);

            return daysDiff < 5; // Conversations créées à moins de 5 jours d'intervalle
          });

          // Refroidir les voisins si leur zone est plus chaude que le point froid
          for (const neighbor of neighbors) {
            if (neighbor.zone < coldSpot.zone) {
              // Descendre d'une zone (devenir plus froid)
              neighbor.zone++;
              neighbor.temperature = this.config.temperatureZones[neighbor.zone - 1].temp;

              // Les conversations qui deviennent très froides deviennent cachées
              if (neighbor.zone >= 5 && !neighbor.hidden) {
                neighbor.hidden = true;
                console.log(`❄️ Archivage thermique: ${neighbor.id.substring(0, 6)}... refroidie et archivée (→ Zone ${neighbor.zone})`);
              } else {
                console.log(`❄️ Conduction thermique: ${neighbor.id.substring(0, 6)}... refroidie par proximité (→ Zone ${neighbor.zone})`);
              }
            }
          }
        }
      }

      // 4. Regrouper les conversations similaires (comme des molécules qui s'organisent)
      this.groupSimilarConversations();

      // 5. Visualiser l'état thermique du système
      this.visualizeThermicState();

      // 6. Mettre à jour les statistiques et sauvegarder
      this.updateStats();
      this.saveMemories();

    } catch (error) {
      console.error('Erreur lors du cycle thermodynamique:', error);
    }
  }

  // Visualiser l'état thermique du système
  visualizeThermicState() {
    try {
      const zones = [0, 0, 0, 0, 0, 0];
      const zoneLabels = ['🔥', '♨️', '🌡️', '🌤️', '❄️', '🧊'];

      // Compter les conversations par zone
      for (const conv of this.memory.memories) {
        if (conv.zone >= 1 && conv.zone <= 6) {
          zones[conv.zone - 1]++;
        }
      }

      // Créer une représentation visuelle
      let visualization = '🌊 CARTE THERMIQUE DU SYSTÈME DE MÉMOIRE 🌊\n';
      const maxCount = Math.max(...zones);
      const barWidth = 20;

      for (let i = 0; i < 6; i++) {
        const zoneNum = i + 1;
        const count = zones[i];
        const barLength = maxCount > 0 ? Math.round((count / maxCount) * barWidth) : 0;
        const bar = '█'.repeat(barLength) + '░'.repeat(barWidth - barLength);

        const temp = this.config.temperatureZones[i].temp;
        const label = this.config.temperatureZones[i].name;

        visualization += `${zoneLabels[i]} Zone ${zoneNum} (${temp}°): ${bar} ${count} conv. | ${label}\n`;
      }

      console.log(visualization);
    } catch (error) {
      console.error('Erreur lors de la visualisation de l\'état thermique:', error);
    }
  }

  // Regrouper les conversations selon leur comportement thermodynamique réel
  groupSimilarConversations() {
    try {
      // 1. Organiser les conversations par zones thermiques
      const convsByZone = [[], [], [], [], [], []]; // Indices 0-5 pour zones 1-6

      // Classement initial par zone
      for (const conv of this.memory.memories) {
        if (conv.zone >= 1 && conv.zone <= 6) {
          convsByZone[conv.zone - 1].push(conv);
        }
      }

      // 2. Méthode avancée : regroupement par auto-organisation thermique
      // Les conversations de même température et de contenu similaire se regroupent naturellement

      // Pour chaque zone, créer des groupes "moléculaires"
      for (let zoneIndex = 0; zoneIndex < 6; zoneIndex++) {
        const zoneConvs = convsByZone[zoneIndex];
        if (zoneConvs.length <= 1) continue;

        // Regroupement par similarité de contenu (version simplifiée)
        const thematicGroups = {};

        for (const conv of zoneConvs) {
          // Extraire les mots-clés du premier message utilisateur (si disponible)
          let keywords = [];
          let groupKey = '';

          if (conv.messages && conv.messages.length > 0) {
            // Trouver le premier message utilisateur
            const userMsg = conv.messages.find(m => m.role === 'user');

            if (userMsg && userMsg.content) {
              // Extraire les mots significatifs (plus de 4 lettres, pas de mots vides)
              const stopWords = ['avec', 'pour', 'dans', 'cette', 'comment', 'faire', 'vous', 'nous', 'mais', 'donc'];
              const words = userMsg.content.toLowerCase().split(/\W+/).filter(word =>
                word.length > 4 && !stopWords.includes(word)
              );

              // Prendre les 3 premiers mots significatifs comme signature thématique
              keywords = words.slice(0, 3);

              // La clé de groupe est basée sur le premier mot significatif, ou la date si aucun mot
              groupKey = keywords.length > 0 ? keywords[0] : new Date(conv.created).toLocaleDateString();
            } else {
              // Si pas de message utilisateur, utiliser la date
              groupKey = new Date(conv.created).toLocaleDateString();
            }
          } else {
            // Si pas de messages, utiliser la date
            groupKey = new Date(conv.created).toLocaleDateString();
          }

          // Ajouter à un groupe thématique
          if (!thematicGroups[groupKey]) {
            thematicGroups[groupKey] = [];
          }

          thematicGroups[groupKey].push({
            conv,
            keywords
          });
        }

        // Former des groupes physiques basés sur la similarité thématique
        Object.entries(thematicGroups).forEach(([key, members]) => {
          if (members.length >= 2) {
            // Nom du groupe basé sur les mots-clés les plus fréquents
            const allKeywords = members.flatMap(m => m.keywords);
            const keywordCount = {};

            allKeywords.forEach(keyword => {
              keywordCount[keyword] = (keywordCount[keyword] || 0) + 1;
            });

            // Trouver les mots-clés les plus fréquents
            const topKeywords = Object.entries(keywordCount)
              .sort((a, b) => b[1] - a[1])
              .slice(0, 2)
              .map(entry => entry[0]);

            let groupName = '';

            if (topKeywords.length > 0) {
              // Nom du groupe basé sur les mots-clés
              groupName = `Groupe "${topKeywords.join(' & ')}" (${members.length})`;
            } else {
              // Nom du groupe basé sur la date
              const dateGroup = new Date(members[0].conv.created).toLocaleDateString();
              groupName = `Conversations du ${dateGroup} (${members.length})`;
            }

            // Assigner le groupe aux conversations
            members.forEach(m => {
              m.conv.group = groupName;

              // Les entités d'un même groupe thermique partagent des propriétés (comme de vraies molécules)
              // Si une conversation du groupe est interactive, elle augmente légèrement la température des autres
              if (m.conv.lastAccessed && new Date(m.conv.lastAccessed) > new Date(Date.now() - 24*60*60*1000)) {
                // Cette conversation est "active" - elle réchauffe légèrement son groupe
                members.forEach(neighbor => {
                  if (neighbor.conv.id !== m.conv.id) {
                    // Effet de réchauffement par association
                    neighbor.conv.temperature += 2; // Léger réchauffement
                    if (neighbor.conv.temperature > this.config.temperatureZones[neighbor.conv.zone - 1].temp + 5) {
                      // Si l'écart devient trop important, la conversation peut monter de zone
                      if (neighbor.conv.zone > 1) {
                        neighbor.conv.zone--;
                        neighbor.conv.temperature = this.config.temperatureZones[neighbor.conv.zone - 1].temp;
                        console.log(`♻️ Association thermique: ${neighbor.conv.id.substring(0, 6)}... réchauffée par association de groupe`);
                      }
                    }
                  }
                });
              }
            });

            console.log(`🧠 Groupe thermique formé: ${groupName} (Zone ${zoneIndex + 1})`);
          }
        });
      }
    } catch (error) {
      console.error('Erreur lors du regroupement thermodynamique des conversations:', error);
    }
  }

  // Phase de rêve - traitement des traces mémorielles et création de nouvelles connexions
  // Fonctionne comme le sommeil REM dans le cerveau humain
  // Appliqué à toutes les zones, pas seulement zone 6
  dreamPhase() {
    try {
      // Obtenir l'efficacité des accélérateurs pour le traitement de rêve
      const memoryAcceleratorsEfficiency = this.getAcceleratorsTotalEfficiency('memory');

      // Si nous n'avons pas de traces mémorielles, en créer à partir de conversations dans toutes les zones
      if (this.neuralState.dreams.length === 0) {
        console.log('🔍 Création de traces mémorielles à partir de toutes les zones...');
        this.generateMemoryTraces();
      }

      // Si nous avons toujours 0 traces, abandonner ce cycle
      if (this.neuralState.dreams.length === 0) {
        console.log('Pas de traces mémorielles à traiter dans le cycle de rêve');
        return;
      }

      console.log(`💭 PHASE DE RÊVE - Traitement de ${this.neuralState.dreams.length} traces avec accélérateurs Kyber (${(memoryAcceleratorsEfficiency*100).toFixed(1)}% d'efficacité)`);

      // Accélération du traitement grâce aux accélérateurs
      const processingBoost = Math.min(5, 1 + memoryAcceleratorsEfficiency);
      const maxDreamSequenceLength = Math.min(
        Math.floor(5 * processingBoost),
        this.neuralState.dreams.length
      );

      console.log(`Capacité de traitement augmentée: x${processingBoost.toFixed(2)} (${maxDreamSequenceLength} traces par cycle)`);

      // Les rêves sont un mélange de traces mémorielles de différentes zones
      // Les traces des zones froides et chaudes sont combinées pour créer des connexions thermiques
      const dreamSequence = [];

      // Pour chaque zone, sélectionner des traces pertinentes
      const tracesByZone = {};

      // Organiser les traces par zone d'origine
      this.neuralState.dreams.forEach(trace => {
        if (!tracesByZone[trace.zone]) {
          tracesByZone[trace.zone] = [];
        }
        tracesByZone[trace.zone].push(trace);
      });

      // Équilibrer les traces entre zones chaudes et froides pour créer des connexions
      // entre mémoires récentes (zones chaudes) et mémoires anciennes (zones froides)
      Object.keys(tracesByZone).forEach(zone => {
        const zoneNumber = parseInt(zone);

        // Utiliser l'accélérateur de cette zone pour amplifier le traitement
        const zoneAccelerator = this.accelerators.zones[zoneNumber - 1];
        const zoneEfficiency = zoneAccelerator ? zoneAccelerator.compression : 1.0;

        // Calculer le nombre de traces à prendre de cette zone
        // Les zones avec plus d'accélération contribuent plus au rêve
        const zoneTraceCount = Math.floor(maxDreamSequenceLength / 6 * zoneEfficiency * 2);

        // Prioriser les traces qui n'ont pas encore été beaucoup traitées
        const zoneTraces = [...tracesByZone[zone]].sort((a, b) => a.dreamCycles - b.dreamCycles);

        // Sélectionner les traces pour cette zone
        for (let i = 0; i < Math.min(zoneTraceCount, zoneTraces.length); i++) {
          if (zoneTraces.length > 0) {
            const index = Math.floor(Math.random() * Math.min(3, zoneTraces.length));
            const selectedTrace = zoneTraces.splice(index, 1)[0];

            // Ajouter à la séquence de rêve
            dreamSequence.push(selectedTrace);

            // Mettre à jour l'activité de l'accélérateur de cette zone
            if (zoneAccelerator) {
              zoneAccelerator.lastActivity = new Date().toISOString();
              zoneAccelerator.load = Math.min(1, zoneAccelerator.load + 0.05);
            }
          }
        }
      });

      console.log(`Séquence onirique multi-zone: ${dreamSequence.length} traces mémorielles`);

      // Construction du rêve - combiner les traces mémorielles pour former un récit onirique
      let dreamNarrative = '🧠 Récit onirique multi-zone: ';
      let dreamConnections = [];

      // Construire le récit onirique en combinant les essences des traces
      dreamSequence.forEach((trace, index) => {
        // Incrémenter le compteur de cycle de rêve pour cette trace
        trace.dreamCycles++;

        // Ajouter l'essence au récit onirique avec indication de la zone
        dreamNarrative += `[Z${trace.zone}:${index + 1}] ${trace.essence} `;

        // Enregistrer les connexions formées dans ce rêve
        dreamConnections.push({
          id: trace.id,
          zone: trace.zone,
          keywords: trace.keywords,
          essence: trace.essence.substring(0, 20) + '...'
        });

        // Créer des connexions entre ces traces, en privilégiant les connexions inter-zones
        // pour favoriser la thermodynamique de l'information
        dreamSequence.forEach(otherTrace => {
          if (trace.id !== otherTrace.id) {
            let connectionStrength = 0.3; // Force de base

            // Connexions inter-zones ont une force accrue - cela favorise les transferts thermiques
            if (trace.zone !== otherTrace.zone) {
              // Plus la différence de température est grande, plus la connexion est forte
              const tempDiff = Math.abs(this.getZoneTemperature(trace.zone) - this.getZoneTemperature(otherTrace.zone));
              connectionStrength = Math.min(1.0, 0.3 + (tempDiff / 100));

              // Créer une connexion neurale entre zones
              this.createNeuralConnection('dream_zone_transfer', trace.zone, otherTrace.zone, connectionStrength);
            }

            // Créer une nouvelle connexion neuronale ou renforcer une existante
            if (!trace.connections.includes(otherTrace.id)) {
              trace.connections.push(otherTrace.id);
            }

            // Ajouter cette connexion au système de patterns avec force variable
            trace.keywords.forEach(keyword => {
              otherTrace.keywords.forEach(otherKeyword => {
                const patternKey = `${keyword}-${otherKeyword}`;

                // Augmenter la force en fonction de l'efficacité des accélérateurs
                const patternStrength = Math.ceil(connectionStrength * memoryAcceleratorsEfficiency * 10) / 10;

                if (!this.neuralState.patterns[patternKey]) {
                  this.neuralState.patterns[patternKey] = patternStrength;
                } else {
                  // Renforcement logarithmique des connexions existantes
                  this.neuralState.patterns[patternKey] += patternStrength / (1 + Math.log(this.neuralState.patterns[patternKey]));
                }
              });
            });
          }
        });
      });

      // Générer des insights basés sur les connexions observées dans ce rêve
      if (dreamConnections.length >= 2) {
        // Générer des insights par groupe de zones (chaud, moyen, froid)
        const zoneGroups = {
          hot: dreamConnections.filter(conn => conn.zone <= 2),
          medium: dreamConnections.filter(conn => conn.zone > 2 && conn.zone <= 4),
          cold: dreamConnections.filter(conn => conn.zone > 4)
        };

        // Pour chaque groupe ayant au moins 2 connexions, générer un insight
        Object.keys(zoneGroups).forEach(groupKey => {
          const connections = zoneGroups[groupKey];
          if (connections.length >= 2) {
            // Extraire les mots-clés des connexions de ce groupe
            const connectedKeywords = connections.flatMap(conn => conn.keywords);
            const mostFrequentKeywords = this.findMostFrequentElements(connectedKeywords, 3);

            // Générer un insight basé sur les mots-clés les plus fréquents
            const insight = {
              id: `${Date.now()}_${groupKey}`,
              description: `Connexion ${groupKey} entre: ${mostFrequentKeywords.join(', ')}`,
              type: groupKey,
              strength: connections.length * memoryAcceleratorsEfficiency,
              created: new Date().toISOString(),
              sources: connections.map(conn => conn.id),
              zones: connections.map(conn => conn.zone)
            };

            this.neuralState.insights.push(insight);
            console.log(`💡 Insight généré (${groupKey}): ${insight.description}`);
          }
        });

        // Générer des insights cross-zone (connexions entre zones différentes)
        // Ces insights sont particulièrement importants pour le transfert thermique
        const crossZoneConnections = [];

        // Identifier les connexions entre zones différentes
        for (let i = 0; i < dreamConnections.length; i++) {
          for (let j = i + 1; j < dreamConnections.length; j++) {
            const conn1 = dreamConnections[i];
            const conn2 = dreamConnections[j];

            if (conn1.zone !== conn2.zone) {
              crossZoneConnections.push({
                source: conn1,
                target: conn2,
                tempDiff: Math.abs(this.getZoneTemperature(conn1.zone) - this.getZoneTemperature(conn2.zone))
              });
            }
          }
        }

        // Trier par différence de température (les plus grandes différences d'abord)
        crossZoneConnections.sort((a, b) => b.tempDiff - a.tempDiff);

        // Générer des insights pour les connexions cross-zone les plus significatives
        if (crossZoneConnections.length > 0) {
          const topConnections = crossZoneConnections.slice(0, Math.min(2, crossZoneConnections.length));

          topConnections.forEach(conn => {
            const crossZoneInsight = {
              id: `${Date.now()}_crosszone_${conn.source.zone}_${conn.target.zone}`,
              description: `Transfert thermique Z${conn.source.zone}↔Z${conn.target.zone}: ${conn.source.essence} ↔ ${conn.target.essence}`,
              type: 'cross_zone',
              strength: conn.tempDiff / 20, // 0-5 basé sur la différence de température
              created: new Date().toISOString(),
              sources: [conn.source.id, conn.target.id],
              zones: [conn.source.zone, conn.target.zone]
            };

            this.neuralState.insights.push(crossZoneInsight);
            console.log(`🔄 Insight thermique: ${crossZoneInsight.description}`);
          });
        }
      }

      console.log(dreamNarrative);

      // Sauvegarder l'état neural mis à jour
      this.neuralState.lastDreamCycle = new Date().toISOString();
      this.neuralState.evolutionGeneration++;
      this.saveNeuralState();
      this.saveAccelerators();

      // Informer sur l'évolution globale du système
      console.log(`🧠 Réseau neural: génération ${this.neuralState.evolutionGeneration}, ${Object.keys(this.neuralState.patterns).length} patterns, ${this.neuralState.insights.length} insights`);

    } catch (error) {
      console.error('Erreur lors de la phase de rêve:', error);
    }
  }

  // Générer des traces mémorielles à partir des conversations dans toutes les zones
  generateMemoryTraces() {
    try {
      // Parcourir toutes les zones pour extraire des traces
      for (let zoneNumber = 1; zoneNumber <= 6; zoneNumber++) {
        // Obtenir les conversations dans cette zone
        const zoneConversations = this.memory.memories.filter(conv => conv.zone === zoneNumber);
        if (zoneConversations.length === 0) continue;

        console.log(`Extraction de traces mémorielles de la Zone ${zoneNumber}: ${zoneConversations.length} conversations`);

        // Utiliser l'accélérateur de zone pour améliorer l'extraction
        const zoneAccelerator = this.accelerators.zones[zoneNumber - 1];
        const extractionEfficiency = zoneAccelerator ? zoneAccelerator.compression : 1.0;

        // Calculer le nombre de traces à extraire par zone
        // Plus la zone est froide, plus on extrait de traces détaillées (mémoire à long terme)
        // Plus la zone est chaude, plus on extrait de traces générales (mémoire à court terme)
        const traceCount = Math.floor(zoneConversations.length * extractionEfficiency * (zoneNumber >= 4 ? 0.8 : 0.5));

        // Extraire des traces des conversations de cette zone
        zoneConversations.forEach(conv => {
          // Probabilité d'extraction qui augmente pour les zones froides (long terme)
          const extractionProb = 0.3 + (zoneNumber * 0.1) * extractionEfficiency;

          if (Math.random() < extractionProb) {
            // Extraire des mots-clés de la conversation
            const keywords = this.extractKeywords(conv);

            // Créer une essence (résumé) de la conversation
            let essence = `${conv.title} (${keywords.slice(0, 3).join(', ')})`;

            // Créer une trace mémorielle
            const trace = {
              id: `trace_${conv.id.substring(0, 8)}_${Date.now()}`,
              conversationId: conv.id,
              zone: zoneNumber,
              essence: essence,
              keywords: keywords,
              created: new Date().toISOString(),
              dreamCycles: 0,
              connections: [],
              strength: 0.5 // Force initiale
            };

            // Ajouter la trace à l'état neural
            this.neuralState.dreams.push(trace);

            // Mettre à jour l'activité de l'accélérateur
            if (zoneAccelerator) {
              zoneAccelerator.lastActivity = new Date().toISOString();
              zoneAccelerator.load = Math.min(1, zoneAccelerator.load + 0.1);
            }
          }
        });
      }

      console.log(`Total de traces mémorielles générées: ${this.neuralState.dreams.length}`);

      // Sauvegarder l'état neural
      this.saveNeuralState();
      this.saveAccelerators();
    } catch (error) {
      console.error('Erreur lors de la génération de traces mémorielles:', error);
    }
  }

  // Extraire des mots-clés d'une conversation
  extractKeywords(conversation) {
    try {
      const keywords = [];

      // Extraire des mots-clés du titre
      if (conversation.title) {
        const titleWords = conversation.title.toLowerCase().split(/\W+/).filter(w => w.length > 3);
        keywords.push(...titleWords.slice(0, 3));
      }

      // Extraire des mots-clés des messages (si disponibles)
      if (conversation.messages && conversation.messages.length > 0) {
        // Prendre quelques messages aléatoires pour l'extraction
        const messageSample = conversation.messages.slice(0, Math.min(5, conversation.messages.length));

        messageSample.forEach(msg => {
          if (msg.content) {
            // Extraire des mots significatifs (plus de 4 lettres)
            const contentWords = msg.content.toLowerCase().split(/\W+/).filter(w => w.length > 4);

            // Prendre quelques mots aléatoires
            if (contentWords.length > 0) {
              for (let i = 0; i < Math.min(2, contentWords.length); i++) {
                const randomIndex = Math.floor(Math.random() * contentWords.length);
                keywords.push(contentWords[randomIndex]);
              }
            }
          }
        });
      }

      // Éliminer les doublons et limiter le nombre de mots-clés
      return [...new Set(keywords)].slice(0, 10);
    } catch (error) {
      console.error('Erreur lors de l\'extraction de mots-clés:', error);
      return [];
    }
  }

  // Trouver les éléments les plus fréquents dans un tableau
  findMostFrequentElements(arr, count) {
    const frequency = {};
    arr.forEach(item => {
      frequency[item] = (frequency[item] || 0) + 1;
    });

    return Object.entries(frequency)
      .sort((a, b) => b[1] - a[1])
      .slice(0, count)
      .map(entry => entry[0]);
  }

  // Cycle d'évolution - création de nouvelles connexions neuronales et patterns
  evolveNeuralConnections() {
    try {
      if (Object.keys(this.neuralState.connections).length === 0) {
        console.log('Pas de connexions neuronales à faire évoluer');
        return;
      }

      console.log(`🧬 ÉVOLUTION NEURONALE - Génération ${this.neuralState.evolutionGeneration}`);

      // Incrémenter la génération d'évolution
      this.neuralState.evolutionGeneration++;

      // 1. Renforcer les connexions fréquemment activées (apprentissage par répétition)
      let connectionsStrengthened = 0;
      let newConnectionsFormed = 0;

      Object.entries(this.neuralState.patterns).forEach(([pattern, strength]) => {
        if (strength > 2) {
          // Cette connexion a été vue plusieurs fois, elle est importante
          const [concept1, concept2] = pattern.split('-');

          // Renforcer la connexion en créant des liens directs
          if (this.neuralState.connections[concept1] && this.neuralState.connections[concept2]) {
            // Ces deux concepts sont fortement liés, créer des liens croisés
            this.neuralState.connections[concept1].forEach(id1 => {
              this.neuralState.connections[concept2].forEach(id2 => {
                // Trouver les traces mémorielles correspondantes
                const trace1 = this.neuralState.dreams.find(d => d.id === id1);
                const trace2 = this.neuralState.dreams.find(d => d.id === id2);

                if (trace1 && trace2 && trace1.id !== trace2.id) {
                  if (!trace1.connections.includes(trace2.id)) {
                    trace1.connections.push(trace2.id);
                    trace1.evolution++;
                    newConnectionsFormed++;
                  } else {
                    // Connexion déjà existante, la renforcer
                    trace1.evolution++;
                    connectionsStrengthened++;
                  }
                }
              });
            });
          }
        }
      });

      // 2. Générer de nouvelles idées et insights basés sur les patterns observés
      const topPatterns = Object.entries(this.neuralState.patterns)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
        .map(entry => entry[0]);

      if (topPatterns.length > 0) {
        // Extraire les concepts qui apparaissent souvent ensemble
        const conceptPairs = topPatterns.map(pattern => pattern.split('-'));
        const allConcepts = conceptPairs.flat();
        const frequentConcepts = this.findMostFrequentElements(allConcepts, 3);

        // Générer un insight d'évolution basé sur ces concepts fréquents
        if (frequentConcepts.length > 0) {
          const evolutionInsight = {
            id: `evolution-${this.neuralState.evolutionGeneration}`,
            description: `Pattern émergent: ${frequentConcepts.join(' + ')}`,
            strength: this.neuralState.evolutionGeneration,
            created: new Date().toISOString(),
            type: 'évolution',
            concepts: frequentConcepts
          };

          this.neuralState.insights.push(evolutionInsight);
          console.log(`🔄 Nouveau pattern émergent: ${evolutionInsight.description}`);
        }
      }

      console.log(`Évolution neuronale: ${newConnectionsFormed} nouvelles connexions, ${connectionsStrengthened} connexions renforcées`);

      // Sauvegarder l'état neural mis à jour
      this.saveNeuralState();

    } catch (error) {
      console.error('Erreur lors de l\'évolution neuronale:', error);
    }
  }

  // Consolidation des mémoires - transformer les insights en connaissances (comme le sommeil profond)
  consolidateMemories() {
    try {
      if (this.neuralState.insights.length === 0) {
        console.log('Pas d\'insights à consolider');
        return;
      }

      console.log(`💤 CONSOLIDATION DES MÉMOIRES - ${this.neuralState.insights.length} insights`);

      // La consolidation est similaire au sommeil profond non-REM dans le cerveau humain
      // Elle transforme les insights temporaires en connaissances durables

      // 1. Identifier les insights les plus importants (par force ou répétition)
      const importantInsights = this.neuralState.insights
        .filter(insight => insight.strength >= 2)
        .slice(0, 3);

      if (importantInsights.length === 0) {
        console.log('Pas d\'insights importants à consolider pour le moment');
        return;
      }

      // 2. Créer une nouvelle structure de connaissance consolidée
      const consolidation = {
        id: `consolidated-${Date.now()}`,
        title: `Connaissance consolidée #${this.neuralState.evolutionGeneration}`,
        concepts: [],
        insights: importantInsights.map(i => i.description),
        importance: importantInsights.reduce((sum, i) => sum + i.strength, 0) / importantInsights.length,
        created: new Date().toISOString()
      };

      // Extraire tous les concepts uniques de ces insights
      const allConcepts = new Set();
      importantInsights.forEach(insight => {
        if (insight.concepts) {
          insight.concepts.forEach(concept => allConcepts.add(concept));
        }
      });

      consolidation.concepts = Array.from(allConcepts);

      // 3. Visualiser la connaissance consolidée
      console.log(`📚 CONNAISSANCE CONSOLIDÉE: ${consolidation.title}`);
      console.log(`Concepts clés: ${consolidation.concepts.join(', ')}`);
      importantInsights.forEach((insight, i) => {
        console.log(`  [${i+1}] ${insight.description}`);
      });

      // 4. Réorganiser les traces mémorielles en fonction des connaissances consolidées
      // Les traces liées aux concepts consolidés sont renforcées
      if (consolidation.concepts.length > 0) {
        this.neuralState.dreams.forEach(trace => {
          // Vérifier si cette trace contient des concepts de la consolidation
          const hasRelevantConcepts = trace.keywords.some(keyword =>
            consolidation.concepts.includes(keyword)
          );

          if (hasRelevantConcepts) {
            // Renforcer cette trace mémorielle
            trace.evolution += 2;
            console.log(`Trace ${trace.id.substring(0, 6)}... renforcée par consolidation`);
          }
        });
      }

      // 5. Sauvegarder l'état neural mis à jour
      this.saveNeuralState();

    } catch (error) {
      console.error('Erreur lors de la consolidation des mémoires:', error);
    }
  }
}

module.exports = ThermalMemory;
