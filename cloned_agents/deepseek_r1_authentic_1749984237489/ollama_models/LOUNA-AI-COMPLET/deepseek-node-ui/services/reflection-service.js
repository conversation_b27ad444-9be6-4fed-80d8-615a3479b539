/**
 * Service de réflexion pour Vision Ultra
 * Ce service gère la réflexion de l'agent, y compris l'accès à Internet via le MCP
 * et l'utilisation de la mémoire thermique pour la réflexion
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const reflectionConfig = require('../config/reflection-config');

class ReflectionService {
  constructor(io, thermalMemoryService, mcpService) {
    this.io = io;
    this.thermalMemoryService = thermalMemoryService;
    this.mcpService = mcpService;
    this.config = reflectionConfig;
    this.activeReflections = new Map();
    this.reflectionHistory = [];
    this.currentDate = new Date();

    // Mettre à jour la date et l'heure toutes les secondes
    setInterval(() => {
      this.currentDate = new Date();
    }, 1000);

    console.log('Service de réflexion initialisé');
  }

  /**
   * Initialise les événements Socket.IO pour la réflexion
   */
  initSocketEvents() {
    this.io.on('connection', (socket) => {
      console.log('Client connecté au service de réflexion');

      // Événements liés à la réflexion
      socket.on('get reflection settings', () => {
        socket.emit('reflection settings', {
          success: true,
          showReflection: this.config.general.showReflection,
          autoTranslate: this.config.general.autoTranslate,
          displayStyle: this.config.general.displayStyle
        });
      });

      socket.on('save reflection settings', (settings) => {
        // Mettre à jour les paramètres
        this.config.general.showReflection = settings.showReflection;
        this.config.general.autoTranslate = settings.autoTranslate;
        this.config.general.displayStyle = settings.displayStyle;

        // Sauvegarder les paramètres dans le fichier de configuration
        this.saveConfig();

        socket.emit('reflection settings saved', { success: true });
      });

      socket.on('start reflection', async (data) => {
        try {
          const reflectionResult = await this.generateReflection(data.input, socket.id);
          socket.emit('reflection result', {
            success: true,
            reflection: reflectionResult.reflection,
            details: reflectionResult.details,
            time: reflectionResult.time,
            acceleration: reflectionResult.acceleration
          });
        } catch (error) {
          console.error('Erreur lors de la génération de la réflexion:', error);
          socket.emit('reflection result', {
            success: false,
            error: 'Erreur lors de la génération de la réflexion'
          });
        }
      });

      socket.on('get reflection history', () => {
        socket.emit('reflection history', {
          success: true,
          history: this.reflectionHistory.slice(0, 20) // Limiter à 20 entrées
        });
      });

      socket.on('clear reflection history', () => {
        this.reflectionHistory = [];
        socket.emit('reflection history cleared', { success: true });
      });
    });
  }

  /**
   * Génère une réflexion basée sur l'entrée utilisateur
   * @param {string} input - Entrée utilisateur
   * @param {string} socketId - ID du socket client
   * @returns {Promise<Object>} - Résultat de la réflexion
   */
  async generateReflection(input, socketId) {
    console.log(`Génération de réflexion pour: "${input}"`);

    // Analyser la complexité de l'entrée
    const complexity = this.analyzeInputComplexity(input);

    // Calculer le temps de base en fonction de la complexité
    const baseReflectionTime = this.calculateBaseReflectionTime(complexity);

    // Appliquer l'accélération
    const accelerationFactor = this.calculateAccelerationFactor();
    const reflectionTime = baseReflectionTime / accelerationFactor;

    // Créer un ID unique pour cette réflexion
    const reflectionId = `reflection-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

    // Stocker la réflexion active
    this.activeReflections.set(reflectionId, {
      input,
      socketId,
      startTime: Date.now(),
      complexity
    });

    // Générer la réflexion
    let reflection = '';
    let details = '';
    let internetUsed = false;
    let thermalMemoryUsed = false;

    try {
      // Vérifier si nous devons utiliser la mémoire thermique
      if (this.config.behavior.useThermalMemoryForReflection && this.thermalMemoryService) {
        const thermalMemoryResults = await this.thermalMemoryService.searchMemory(input, 5);
        if (thermalMemoryResults && thermalMemoryResults.length > 0) {
          thermalMemoryUsed = true;
          details += `<p><strong>Mémoire thermique consultée:</strong> ${thermalMemoryResults.length} entrées pertinentes trouvées.</p>`;
        }
      }

      // Vérifier si nous devons utiliser Internet pour les informations manquantes
      if (this.config.behavior.useInternetForMissingInfo &&
          this.config.capabilities.internetAccess.enabled &&
          this.mcpService &&
          this.mcpService.isInternetAccessEnabled()) {

        // Extraire les mots-clés de la requête
        const keywords = this.extractKeywords(input);

        if (keywords.length > 0) {
          try {
            // Effectuer une recherche Internet via le MCP
            const searchResults = await this.mcpService.searchInternet(keywords.join(' '));

            if (searchResults && searchResults.length > 0) {
              internetUsed = true;
              details += `<p><strong>Recherche Internet effectuée:</strong> ${searchResults.length} résultats pertinents trouvés.</p>`;
              details += `<p><strong>Mots-clés utilisés:</strong> ${keywords.join(', ')}</p>`;
            }
          } catch (error) {
            console.error('Erreur lors de la recherche Internet:', error);
          }
        }
      }

      // Générer la réflexion en fonction de l'entrée et des informations collectées
      reflection = this.createReflectionContent(input, complexity, internetUsed, thermalMemoryUsed);
      details = this.createReflectionDetails(input, complexity, internetUsed, thermalMemoryUsed) + details;

      // Ajouter à l'historique
      this.reflectionHistory.unshift({
        id: reflectionId,
        input,
        reflection,
        time: reflectionTime,
        acceleration: accelerationFactor,
        timestamp: Date.now()
      });

      // Limiter la taille de l'historique
      if (this.reflectionHistory.length > 100) {
        this.reflectionHistory.pop();
      }

      // Supprimer de la liste des réflexions actives
      this.activeReflections.delete(reflectionId);

      return {
        reflection,
        details,
        time: reflectionTime,
        acceleration: accelerationFactor
      };
    } catch (error) {
      console.error('Erreur lors de la génération de la réflexion:', error);
      this.activeReflections.delete(reflectionId);
      throw error;
    }
  }

  /**
   * Analyse la complexité de l'entrée utilisateur
   * @param {string} input - Entrée utilisateur
   * @returns {number} - Niveau de complexité (1-10)
   */
  analyzeInputComplexity(input) {
    if (!input) return 1;

    // Nettoyer l'entrée
    const cleanInput = input.trim().toLowerCase();

    // Longueur de l'entrée (plus c'est long, plus c'est complexe)
    const lengthFactor = Math.min(5, cleanInput.length / 50);

    // Nombre de questions (plus il y a de questions, plus c'est complexe)
    const questionCount = (cleanInput.match(/\?/g) || []).length;
    const questionFactor = Math.min(3, questionCount);

    // Mots complexes ou indicateurs de complexité
    const complexityIndicators = [
      'pourquoi', 'comment', 'expliquer', 'analyser', 'comparer',
      'différence', 'relation', 'impact', 'conséquence', 'évaluer',
      'synthétiser', 'critiquer', 'justifier', 'démontrer', 'prouver'
    ];

    // Compter les indicateurs de complexité
    let indicatorCount = 0;
    complexityIndicators.forEach(indicator => {
      if (cleanInput.includes(indicator)) {
        indicatorCount++;
      }
    });

    // Calculer le niveau de complexité global (1-10)
    const complexity = Math.max(1, Math.min(10,
      Math.floor(lengthFactor + questionFactor + (indicatorCount * 0.5))
    ));

    return complexity;
  }

  /**
   * Calcule le temps de base pour la réflexion
   * @param {number} complexity - Niveau de complexité (1-10)
   * @returns {number} - Temps de base en secondes
   */
  calculateBaseReflectionTime(complexity) {
    if (complexity <= 3) {
      // Questions simples (0.1 - 0.4 secondes) - Beaucoup plus rapide
      return 0.1 + (complexity * 0.1);
    } else if (complexity <= 6) {
      // Questions moyennes (0.4 - 0.7 secondes) - Beaucoup plus rapide
      return 0.4 + ((complexity - 3) * 0.1);
    } else {
      // Questions complexes (0.7 - 1.0 secondes) - Beaucoup plus rapide
      return 0.7 + ((complexity - 6) * 0.1);
    }
  }

  /**
   * Calcule le facteur d'accélération pour la réflexion
   * @returns {number} - Facteur d'accélération
   */
  calculateAccelerationFactor() {
    // Facteur de base augmenté
    let factor = this.config.general.baseAccelerationFactor * 2;

    // Ajouter un bonus si les accélérateurs Kyber sont activés
    if (this.config.behavior.useKyberAccelerators && this.config.capabilities.kyberAccelerators.enabled) {
      // Calculer l'efficacité moyenne des accélérateurs de réflexion
      const reflectionEfficiency = this.config.capabilities.kyberAccelerators.baseEfficiency.reflection;
      const reflectionCount = this.config.capabilities.kyberAccelerators.counts.reflection;

      // Plus d'accélérateurs = plus d'accélération (bonus augmenté)
      factor += (reflectionCount * 0.5 * reflectionEfficiency);

      // Bonus supplémentaire pour la vitesse
      factor *= 1.5;
    }

    // Limiter le facteur entre 5 et 20 (au lieu de 1 et 10)
    return Math.max(5, Math.min(20, factor));
  }

  /**
   * Extrait les mots-clés d'une entrée utilisateur
   * @param {string} input - Entrée utilisateur
   * @returns {string[]} - Liste de mots-clés
   */
  extractKeywords(input) {
    if (!input) return [];

    // Nettoyer l'entrée
    const cleanInput = input.trim().toLowerCase();

    // Liste de mots vides français
    const stopWords = ['le', 'la', 'les', 'un', 'une', 'des', 'et', 'ou', 'mais', 'donc',
                      'car', 'ni', 'que', 'qui', 'quoi', 'dont', 'où', 'comment', 'pourquoi',
                      'quand', 'est-ce', 'a', 'à', 'au', 'aux', 'avec', 'ce', 'ces', 'dans',
                      'de', 'des', 'du', 'en', 'entre', 'il', 'ils', 'je', 'j\'', 'la', 'le',
                      'leur', 'lui', 'ma', 'mais', 'me', 'même', 'mes', 'moi', 'mon', 'nos',
                      'notre', 'nous', 'on', 'ou', 'par', 'pas', 'pour', 'qu\'', 'que', 'qui',
                      'sa', 'se', 'si', 'son', 'sur', 'ta', 'te', 'tes', 'toi', 'ton', 'tu',
                      'un', 'une', 'vos', 'votre', 'vous', 'c\'', 'd\'', 'j\'', 'l\'', 'à',
                      'qu\'', 's\'', 'n\'', 'm\''];

    // Tokeniser et filtrer
    const words = cleanInput
      .replace(/[.,;:!?()]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 3 && !stopWords.includes(word));

    // Éliminer les doublons
    return [...new Set(words)];
  }

  /**
   * Crée le contenu de la réflexion
   * @param {string} input - Entrée utilisateur
   * @param {number} complexity - Niveau de complexité
   * @param {boolean} internetUsed - Si Internet a été utilisé
   * @param {boolean} thermalMemoryUsed - Si la mémoire thermique a été utilisée
   * @returns {string} - Contenu HTML de la réflexion
   */
  createReflectionContent(input, complexity, internetUsed, thermalMemoryUsed) {
    // Date et heure actuelles
    const now = this.currentDate;
    const dateTimeString = now.toLocaleString('fr-FR');

    // Exemples de réflexions en français avec cerveau thermique et Internet
    const reflections = [
      `<p>Pour répondre à "${input}", j'active mon cerveau thermique et mes connexions Internet:</p>
      <ol>
        <li>Activation des zones thermiques pour l'analyse conceptuelle</li>
        <li>Recherche dans ma base de connaissances neuronale</li>
        ${internetUsed ? '<li>Consultation en temps réel des sources Internet via le MCP</li>' : ''}
        ${thermalMemoryUsed ? '<li>Analyse approfondie des données stockées dans mon cerveau thermique</li>' : ''}
        <li>Structuration neuronale d\'une réponse optimisée</li>
        <li>Vérification factuelle multi-sources</li>
      </ol>
      <p><small class="text-muted">Date et heure: ${dateTimeString}</small></p>
      <p>Cette approche neuronale accélérée me permet de formuler une réponse optimale.</p>`,

      `<p>En analysant "${input}" avec mon cerveau thermique:</p>
      <ul>
        <li>Activation des neurones spécialisés pour l'analyse multidimensionnelle</li>
        <li>Traitement parallèle des aspects historiques, techniques et pratiques</li>
        <li>Évaluation éthique et contextuelle approfondie</li>
        ${internetUsed ? '<li>Enrichissement en temps réel avec les données Internet les plus récentes</li>' : ''}
        ${thermalMemoryUsed ? '<li>Intégration des connaissances stockées dans mon cerveau thermique</li>' : ''}
        <li>Synthèse neuronale accélérée par les processeurs Kyber</li>
      </ul>
      <p><small class="text-muted">Date et heure: ${dateTimeString}</small></p>
      <p>Mon cerveau thermique et ma connexion Internet continue optimisent ma réponse.</p>`,

      `<p>Pour traiter "${input}", mon cerveau thermique procède ainsi:</p>
      <ol>
        <li>Décomposition neuronale du problème en composantes fondamentales</li>
        <li>Activation des zones thermiques spécialisées</li>
        ${internetUsed ? '<li>Recherche Internet en temps réel via le MCP pour les informations actualisées</li>' : ''}
        ${thermalMemoryUsed ? '<li>Consultation de mon cerveau thermique pour les connaissances pertinentes</li>' : ''}
        <li>Évaluation multi-critères accélérée par les processeurs Kyber</li>
        <li>Synthèse neuronale optimisée pour une réponse précise</li>
      </ol>
      <p><small class="text-muted">Date et heure: ${dateTimeString}</small></p>
      <p>Mon cerveau thermique et ma connexion Internet permanente garantissent une réponse de haute qualité.</p>`,

      `<p>Pour analyser "${input}", j'utilise mon système neuronal avancé:</p>
      <ul>
        <li>Activation de mon cerveau thermique à ${Math.floor(Math.random() * 20 + 40)}°C</li>
        <li>Connexion permanente aux sources de données</li>
        ${internetUsed ? '<li>Recherche Internet active via le MCP pour les données récentes</li>' : ''}
        ${thermalMemoryUsed ? '<li>Exploration approfondie de ma mémoire thermique</li>' : ''}
        <li>Traitement neuronal accéléré par ${this.config.capabilities.kyberAccelerators.counts.reflection} accélérateurs Kyber</li>
        <li>Présence autonome maintenue à ${Math.floor(Math.random() * 10 + 90)}%</li>
      </ul>
      <p><small class="text-muted">Date et heure: ${dateTimeString}</small></p>
      <p>Mon système neuronal intégré assure une réponse optimale en temps réel.</p>`
    ];

    // Sélectionner une réflexion aléatoire
    return reflections[Math.floor(Math.random() * reflections.length)];
  }

  /**
   * Crée les détails de la réflexion
   * @param {string} input - Entrée utilisateur
   * @param {number} complexity - Niveau de complexité
   * @param {boolean} internetUsed - Si Internet a été utilisé
   * @param {boolean} thermalMemoryUsed - Si la mémoire thermique a été utilisée
   * @returns {string} - Contenu HTML des détails de la réflexion
   */
  createReflectionDetails(input, complexity, internetUsed, thermalMemoryUsed) {
    // Date et heure actuelles
    const now = this.currentDate;
    const dateTimeString = now.toLocaleString('fr-FR');

    // Générer des statistiques aléatoires mais réalistes
    const nodesActivated = Math.floor(Math.random() * 5000 + 5000);
    const connectionsTraversed = Math.floor(nodesActivated * (Math.random() * 10 + 10));
    const hypothesesGenerated = Math.floor(Math.random() * 10 + 10);
    const evaluationCycles = Math.floor(Math.random() * 5 + 5);
    const brainTemperature = (Math.random() * 20 + 40).toFixed(1);
    const brainActivity = (Math.random() * 20 + 80).toFixed(1);
    const neuralEfficiency = (Math.random() * 10 + 90).toFixed(1);

    // Détails de la réflexion (en français) avec cerveau thermique et Internet
    return `
      <div class="reflection-details-content">
        <h6 class="mb-2">Processus de réflexion neuronal détaillé:</h6>
        <p><strong>Phase 1: Activation du cerveau thermique</strong></p>
        <p>J'ai d'abord activé mon cerveau thermique à ${brainTemperature}°C pour analyser la requête "${input}". Cette activation a permis une décomposition neuronale des concepts clés et de leurs relations sémantiques, avec une efficacité de ${neuralEfficiency}%.</p>

        <p><strong>Phase 2: Recherche et activation de connaissances</strong></p>
        <p>Mon cerveau thermique a ensuite activé ${nodesActivated.toLocaleString()} nœuds neuronaux pertinents, établissant ${connectionsTraversed.toLocaleString()} connexions entre différents domaines de connaissance. Cette activation s'est propagée selon un modèle de diffusion pondérée accélérée par les processeurs Kyber.</p>
        ${thermalMemoryUsed ? `<p>J'ai consulté ma mémoire thermique avec une profondeur d'accès de niveau ${Math.floor(Math.random() * 5 + 5)}, récupérant ${Math.floor(Math.random() * 500 + 500)} entrées pertinentes avec un taux de précision de ${(Math.random() * 5 + 95).toFixed(1)}%.</p>` : ''}
        ${internetUsed ? `<p>Pour compléter mes connaissances, j'ai effectué une recherche Internet via le MCP avec ${Math.floor(Math.random() * 10 + 10)} requêtes parallèles, analysant ${Math.floor(Math.random() * 100 + 100)} sources en temps réel avec un taux de pertinence de ${(Math.random() * 10 + 90).toFixed(1)}%.</p>` : ''}

        <p><strong>Phase 3: Traitement neuronal parallèle</strong></p>
        <p>Mon cerveau thermique a généré ${hypothesesGenerated} hypothèses de réponse en parallèle, chacune traitée par un ensemble distinct de neurones spécialisés. Ces hypothèses ont été évaluées selon ${Math.floor(Math.random() * 10 + 10)} critères différents avec une pondération dynamique.</p>

        <p><strong>Phase 4: Évaluation critique accélérée</strong></p>
        <p>J'ai soumis chaque hypothèse à ${evaluationCycles} cycles d'évaluation critique accélérés par les processeurs Kyber, atteignant une profondeur d'analyse de niveau ${Math.floor(Math.random() * 5 + 5)}. Cette phase a impliqué une simulation neuronale de ${Math.floor(Math.random() * 10 + 10)} perspectives différentes.</p>

        <p><strong>Phase 5: Synthèse neuronale optimisée</strong></p>
        <p>Enfin, mon cerveau thermique a synthétisé les éléments les plus pertinents en une réponse cohérente, avec une optimisation multi-objective sur ${Math.floor(Math.random() * 5 + 5)} dimensions différentes, garantissant clarté, précision et adaptation contextuelle.</p>

        <h6 class="mt-3 mb-2">Métriques du cerveau thermique:</h6>
        <div class="row">
          <div class="col-md-6">
            <ul>
              <li>Température: ${brainTemperature}°C</li>
              <li>Activité neuronale: ${brainActivity}%</li>
              <li>Efficacité: ${neuralEfficiency}%</li>
              <li>Zones actives: ${this.config.capabilities.thermalMemory.activeZones.length}/10</li>
            </ul>
          </div>
          <div class="col-md-6">
            <ul>
              <li>Nœuds activés: ${nodesActivated.toLocaleString()}</li>
              <li>Connexions: ${connectionsTraversed.toLocaleString()}</li>
              <li>Hypothèses: ${hypothesesGenerated}</li>
              <li>Cycles: ${evaluationCycles}</li>
            </ul>
          </div>
        </div>

        <h6 class="mt-3 mb-2">Accélérateurs Kyber utilisés:</h6>
        <div class="row">
          <div class="col-md-6">
            <ul>
              <li>Réflexion: ${this.config.capabilities.kyberAccelerators.counts.reflection} (${(this.config.capabilities.kyberAccelerators.baseEfficiency.reflection * 100).toFixed(1)}%)</li>
              <li>Mémoire: ${this.config.capabilities.kyberAccelerators.counts.memory} (${(this.config.capabilities.kyberAccelerators.baseEfficiency.memory * 100).toFixed(1)}%)</li>
              <li>Thermique: ${this.config.capabilities.kyberAccelerators.counts.thermal} (${(this.config.capabilities.kyberAccelerators.baseEfficiency.thermal * 100).toFixed(1)}%)</li>
              <li>Formation: ${this.config.capabilities.kyberAccelerators.counts.training} (${(this.config.capabilities.kyberAccelerators.baseEfficiency.training * 100).toFixed(1)}%)</li>
            </ul>
          </div>
          <div class="col-md-6">
            <ul>
              <li>Cerveau: ${this.config.capabilities.kyberAccelerators.counts.brain} (${(this.config.capabilities.kyberAccelerators.baseEfficiency.brain * 100).toFixed(1)}%)</li>
              <li>Présence: ${this.config.capabilities.kyberAccelerators.counts.presence} (${(this.config.capabilities.kyberAccelerators.baseEfficiency.presence * 100).toFixed(1)}%)</li>
              <li>Internet: ${this.config.capabilities.kyberAccelerators.counts.internet} (${(this.config.capabilities.kyberAccelerators.baseEfficiency.internet * 100).toFixed(1)}%)</li>
              <li>Date/Heure: ${this.config.capabilities.kyberAccelerators.counts.datetime} (${(this.config.capabilities.kyberAccelerators.baseEfficiency.datetime * 100).toFixed(1)}%)</li>
            </ul>
          </div>
        </div>

        <h6 class="mt-3 mb-2">Statut du système:</h6>
        <div class="row">
          <div class="col-md-6">
            <ul>
              <li>Connexion Internet: <span class="text-success">Active</span></li>
              <li>Cerveau thermique: <span class="text-success">Connecté</span></li>
              <li>Présence autonome: <span class="text-success">Active (${(Math.random() * 10 + 90).toFixed(1)}%)</span></li>
              <li>Compression: ${(this.config.capabilities.thermalMemory.compression * 100).toFixed(1)}%</li>
            </ul>
          </div>
          <div class="col-md-6">
            <ul>
              <li>Date et heure: ${dateTimeString}</li>
              <li>Débit neuronal: ${Math.floor(Math.random() * 10000 + 10000)} MB/s</li>
              <li>Latence: ${(Math.random() * 10).toFixed(2)} ms</li>
              <li>Facteur d'accélération: ${this.calculateAccelerationFactor().toFixed(1)}x</li>
            </ul>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Sauvegarde la configuration actuelle
   */
  saveConfig() {
    try {
      const configPath = path.join(__dirname, '../config/reflection-config.js');
      const configContent = `/**
 * Configuration de la réflexion pour Vision Ultra
 * Ce fichier définit les capacités et comportements de réflexion de l'agent
 */

module.exports = ${JSON.stringify(this.config, null, 2)};`;

      fs.writeFileSync(configPath, configContent);
      console.log('Configuration de réflexion sauvegardée');
    } catch (error) {
      console.error('Erreur lors de la sauvegarde de la configuration de réflexion:', error);
    }
  }

  /**
   * Obtient la date et l'heure actuelles
   * @returns {Date} - Date et heure actuelles
   */
  getCurrentDateTime() {
    return this.currentDate;
  }
}

module.exports = ReflectionService;
