/**
 * Service d'agent pour Vision Ultra
 * Version temporaire pour permettre le démarrage de Luna
 */

class AgentService {
  constructor(io) {
    this.io = io;
    this.mcpService = null;
    this.isConnected = false;
    this.config = {
      useDeepSeek: false,
      model: 'mistral:7b',
      temperature: 0.7,
      maxTokens: 2048
    };
    
    console.log('Service d\'agent créé avec le modèle', this.config.model);
  }
  
  /**
   * Définit le service MCP
   * @param {Object} mcpService - Service MCP
   */
  setMCPService(mcpService) {
    this.mcpService = mcpService;
    console.log('Service MCP associé au service d\'agent');
  }
  
  /**
   * Initialise les gestionnaires d'événements Socket.IO
   */
  initSocketEvents() {
    if (!this.io) {
      console.error('Socket.IO non disponible pour l\'agent');
      return;
    }
    
    this.io.on('connection', (socket) => {
      console.log('Client connecté au service d\'agent');
      
      // Envoyer l'état initial
      socket.emit('agent:status', {
        active: true,
        model: this.config.model,
        temperature: this.config.temperature
      });
      
      // Gérer les messages envoyés à l'agent
      socket.on('agent:send', async (data) => {
        try {
          const { message, options = {} } = data;
          
          if (!message) {
            socket.emit('agent:error', {
              error: 'Message vide'
            });
            return;
          }
          
          // Indiquer que l'agent commence à traiter
          socket.emit('agent:thinking', { active: true });
          
          // Simuler un temps de traitement
          setTimeout(() => {
            // Réponse simulée basée sur l'intégration avec Mistral via Ollama
            const response = {
              role: 'assistant',
              content: 'Je suis Vision Ultra, basé sur le modèle Mistral 7B. ' +
                'Je vous réponds via le service d\'agent qui est maintenant opérationnel. ' +
                'Votre message original était: "' + message + '"'
            };
            
            // Indiquer que l'agent a terminé de traiter
            socket.emit('agent:thinking', { active: false });
            
            // Envoyer la réponse
            socket.emit('agent:response', {
              success: true,
              response
            });
          }, 1000);
        } catch (error) {
          console.error('Erreur lors du traitement du message par l\'agent:', error);
          socket.emit('agent:error', {
            error: error.message || 'Erreur lors du traitement du message'
          });
        }
      });
      
      // Gérer les modifications de configuration
      socket.on('agent:config', (config) => {
        if (config.model) {
          this.config.model = config.model;
        }
        if (config.temperature !== undefined) {
          this.config.temperature = Math.min(Math.max(config.temperature, 0), 1);
        }
        if (config.maxTokens !== undefined) {
          this.config.maxTokens = config.maxTokens;
        }
        
        socket.emit('agent:config:updated', this.config);
      });
    });
    
    console.log('Gestionnaires d\'événements Socket.IO initialisés pour l\'agent');
  }
}

module.exports = AgentService;