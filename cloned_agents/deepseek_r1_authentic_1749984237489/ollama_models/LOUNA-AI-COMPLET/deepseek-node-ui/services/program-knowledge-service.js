/**
 * Service de connaissance du programme pour Vision Ultra
 * Ce service permet à l'agent de connaître son programme complet via son cerveau thermique
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const reflectionConfig = require('../config/reflection-config');

class ProgramKnowledgeService {
  constructor(io, thermalMemoryService) {
    this.io = io;
    this.thermalMemoryService = thermalMemoryService;
    this.config = reflectionConfig;
    this.programKnowledge = {
      modules: [],
      interfaces: [],
      services: [],
      capabilities: [],
      routes: [],
      configurations: [],
      components: [],
      assets: [],
      styles: [],
      scripts: [],
      models: [],
      plugins: [],
      systemStatus: {},
      missingElements: [],
      incompleteElements: []
    };
    this.lastScanTime = null;
    this.isScanning = false;

    // Initialiser la connaissance du programme
    this.initProgramKnowledge();

    // Planifier une analyse périodique des éléments manquants et incomplets
    setInterval(() => {
      this.analyzeMissingAndIncompleteElements();
    }, 60 * 60 * 1000); // Toutes les heures

    console.log('Service de connaissance du programme initialisé');
  }

  /**
   * Initialise les événements Socket.IO pour la connaissance du programme
   */
  initSocketEvents() {
    this.io.on('connection', (socket) => {
      console.log('Client connecté au service de connaissance du programme');

      // Événements liés à la connaissance du programme
      socket.on('get program knowledge', () => {
        socket.emit('program knowledge', {
          success: true,
          programKnowledge: this.programKnowledge,
          lastScanTime: this.lastScanTime
        });
      });

      socket.on('scan program', async () => {
        if (this.isScanning) {
          socket.emit('scan program status', {
            success: false,
            error: 'Scan déjà en cours'
          });
          return;
        }

        try {
          this.isScanning = true;
          socket.emit('scan program status', {
            success: true,
            status: 'started'
          });

          await this.scanProgram();

          socket.emit('scan program status', {
            success: true,
            status: 'completed',
            programKnowledge: this.programKnowledge,
            lastScanTime: this.lastScanTime
          });
        } catch (error) {
          console.error('Erreur lors du scan du programme:', error);
          socket.emit('scan program status', {
            success: false,
            error: 'Erreur lors du scan du programme'
          });
        } finally {
          this.isScanning = false;
        }
      });
    });
  }

  /**
   * Initialise la connaissance du programme
   */
  async initProgramKnowledge() {
    try {
      await this.scanProgram();

      // Planifier un scan périodique (toutes les 6 heures)
      setInterval(() => {
        this.scanProgram();
      }, 6 * 60 * 60 * 1000);
    } catch (error) {
      console.error('Erreur lors de l\'initialisation de la connaissance du programme:', error);
    }
  }

  /**
   * Scanne le programme pour construire la connaissance
   */
  async scanProgram() {
    console.log('Scan du programme en cours...');

    try {
      // Scan des modules
      await this.scanModules();

      // Scan des interfaces
      await this.scanInterfaces();

      // Scan des services
      await this.scanServices();

      // Scan des capacités
      await this.scanCapabilities();

      // Scan des routes
      await this.scanRoutes();

      // Scan des configurations
      await this.scanConfigurations();

      // Scan des composants
      await this.scanComponents();

      // Scan des assets
      await this.scanAssets();

      // Scan des styles
      await this.scanStyles();

      // Scan des scripts
      await this.scanScripts();

      // Scan des modèles
      await this.scanModels();

      // Scan des plugins
      await this.scanPlugins();

      // Scan du statut du système
      await this.scanSystemStatus();

      // Mettre à jour le temps de scan
      this.lastScanTime = new Date();

      // Stocker la connaissance dans la mémoire thermique
      if (this.thermalMemoryService) {
        await this.storeInThermalMemory();
      }

      console.log('Scan du programme terminé');
    } catch (error) {
      console.error('Erreur lors du scan du programme:', error);
      throw error;
    }
  }

  /**
   * Scanne les modules du programme
   */
  async scanModules() {
    const modules = [];

    // Lister les fichiers package.json
    const packageJsonFiles = await this.findFiles('package.json');

    for (const packageJsonFile of packageJsonFiles) {
      try {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonFile, 'utf8'));

        modules.push({
          name: packageJson.name || path.basename(path.dirname(packageJsonFile)),
          version: packageJson.version || '0.0.0',
          description: packageJson.description || '',
          dependencies: packageJson.dependencies || {},
          devDependencies: packageJson.devDependencies || {},
          path: path.dirname(packageJsonFile)
        });
      } catch (error) {
        console.error(`Erreur lors de la lecture de ${packageJsonFile}:`, error);
      }
    }

    this.programKnowledge.modules = modules;
  }

  /**
   * Scanne les interfaces du programme
   */
  async scanInterfaces() {
    const interfaces = [];

    // Lister les fichiers EJS (interfaces)
    const ejsFiles = await this.findFiles('*.ejs');

    for (const ejsFile of ejsFiles) {
      try {
        const content = fs.readFileSync(ejsFile, 'utf8');
        const name = path.basename(ejsFile, '.ejs');

        // Extraire le titre de l'interface
        const titleMatch = content.match(/<title>(.*?)<\/title>/);
        const title = titleMatch ? titleMatch[1] : name;

        interfaces.push({
          name,
          title,
          path: ejsFile,
          size: content.length,
          lastModified: fs.statSync(ejsFile).mtime
        });
      } catch (error) {
        console.error(`Erreur lors de la lecture de ${ejsFile}:`, error);
      }
    }

    this.programKnowledge.interfaces = interfaces;
  }

  /**
   * Scanne les services du programme
   */
  async scanServices() {
    const services = [];

    // Lister les fichiers de service
    const serviceFiles = await this.findFiles('services/*.js');

    for (const serviceFile of serviceFiles) {
      try {
        const content = fs.readFileSync(serviceFile, 'utf8');
        const name = path.basename(serviceFile, '.js');

        // Extraire la classe du service
        const classMatch = content.match(/class\s+(\w+)/);
        const className = classMatch ? classMatch[1] : name;

        // Extraire les méthodes du service
        const methodMatches = content.match(/(\w+)\s*\([^)]*\)\s*{/g) || [];
        const methods = methodMatches.map(match => {
          const methodName = match.match(/(\w+)\s*\(/)[1];
          return methodName !== 'constructor' ? methodName : null;
        }).filter(Boolean);

        services.push({
          name,
          className,
          methods,
          path: serviceFile,
          size: content.length,
          lastModified: fs.statSync(serviceFile).mtime
        });
      } catch (error) {
        console.error(`Erreur lors de la lecture de ${serviceFile}:`, error);
      }
    }

    this.programKnowledge.services = services;
  }

  /**
   * Scanne les capacités du programme
   */
  async scanCapabilities() {
    // Utiliser les capacités définies dans la configuration de réflexion
    const capabilities = [];

    if (this.config && this.config.capabilities) {
      for (const [key, value] of Object.entries(this.config.capabilities)) {
        if (typeof value === 'object' && value.enabled) {
          capabilities.push({
            name: key,
            enabled: value.enabled,
            details: value
          });
        }
      }
    }

    this.programKnowledge.capabilities = capabilities;
  }

  /**
   * Scanne les routes du programme
   */
  async scanRoutes() {
    const routes = [];

    // Lister les fichiers de route
    const routeFiles = await this.findFiles('routes/*.js');

    for (const routeFile of routeFiles) {
      try {
        const content = fs.readFileSync(routeFile, 'utf8');
        const name = path.basename(routeFile, '.js');

        // Extraire les routes
        const routeMatches = content.match(/router\.(get|post|put|delete|patch)\s*\(\s*['"]([^'"]+)['"]/g) || [];
        const routePaths = routeMatches.map(match => {
          const parts = match.match(/router\.(get|post|put|delete|patch)\s*\(\s*['"]([^'"]+)['"]/);
          return parts ? { method: parts[1].toUpperCase(), path: parts[2] } : null;
        }).filter(Boolean);

        routes.push({
          name,
          paths: routePaths,
          file: routeFile,
          lastModified: fs.statSync(routeFile).mtime
        });
      } catch (error) {
        console.error(`Erreur lors de la lecture de ${routeFile}:`, error);
      }
    }

    this.programKnowledge.routes = routes;
  }

  /**
   * Scanne les configurations du programme
   */
  async scanConfigurations() {
    const configurations = [];

    // Lister les fichiers de configuration
    const configFiles = await this.findFiles('config/*.js');

    for (const configFile of configFiles) {
      try {
        const content = fs.readFileSync(configFile, 'utf8');
        const name = path.basename(configFile, '.js');

        configurations.push({
          name,
          path: configFile,
          size: content.length,
          lastModified: fs.statSync(configFile).mtime
        });
      } catch (error) {
        console.error(`Erreur lors de la lecture de ${configFile}:`, error);
      }
    }

    this.programKnowledge.configurations = configurations;
  }

  /**
   * Trouve des fichiers correspondant à un motif
   * @param {string} pattern - Motif de recherche
   * @returns {Promise<string[]>} - Liste des fichiers trouvés
   */
  async findFiles(pattern) {
    return new Promise((resolve, reject) => {
      const rootDir = path.resolve(__dirname, '..');

      exec(`find ${rootDir} -name "${pattern}" -not -path "*/node_modules/*" -not -path "*/\\.*"`, (error, stdout, stderr) => {
        if (error) {
          reject(error);
          return;
        }

        const files = stdout.trim().split('\n').filter(Boolean);
        resolve(files);
      });
    });
  }

  /**
   * Scanne les composants du programme
   */
  async scanComponents() {
    const components = [];

    // Lister les fichiers de composants
    const componentFiles = await this.findFiles('components/*.js');

    for (const componentFile of componentFiles) {
      try {
        const content = fs.readFileSync(componentFile, 'utf8');
        const name = path.basename(componentFile, '.js');

        // Extraire la classe du composant
        const classMatch = content.match(/class\s+(\w+)/);
        const className = classMatch ? classMatch[1] : name;

        components.push({
          name,
          className,
          path: componentFile,
          size: content.length,
          lastModified: fs.statSync(componentFile).mtime
        });
      } catch (error) {
        console.error(`Erreur lors de la lecture de ${componentFile}:`, error);
      }
    }

    this.programKnowledge.components = components;
  }

  /**
   * Scanne les assets du programme
   */
  async scanAssets() {
    const assets = [];

    // Lister les fichiers d'assets
    const assetFiles = await this.findFiles('public/assets/**/*.*');

    for (const assetFile of assetFiles) {
      try {
        const stats = fs.statSync(assetFile);
        const name = path.basename(assetFile);
        const extension = path.extname(assetFile).toLowerCase();

        // Déterminer le type d'asset
        let type = 'unknown';
        if (['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp'].includes(extension)) {
          type = 'image';
        } else if (['.mp3', '.wav', '.ogg'].includes(extension)) {
          type = 'audio';
        } else if (['.mp4', '.webm', '.avi'].includes(extension)) {
          type = 'video';
        } else if (['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'].includes(extension)) {
          type = 'document';
        } else if (['.json', '.xml', '.csv'].includes(extension)) {
          type = 'data';
        }

        assets.push({
          name,
          type,
          extension,
          path: assetFile,
          size: stats.size,
          lastModified: stats.mtime
        });
      } catch (error) {
        console.error(`Erreur lors de la lecture de ${assetFile}:`, error);
      }
    }

    this.programKnowledge.assets = assets;
  }

  /**
   * Scanne les styles du programme
   */
  async scanStyles() {
    const styles = [];

    // Lister les fichiers CSS
    const cssFiles = await this.findFiles('public/css/*.css');

    for (const cssFile of cssFiles) {
      try {
        const content = fs.readFileSync(cssFile, 'utf8');
        const name = path.basename(cssFile, '.css');

        // Extraire les sélecteurs CSS
        const selectorMatches = content.match(/([.#][\w-]+|\w+)(?:\s*[{,])/g) || [];
        const selectors = selectorMatches.map(match => match.trim().replace(/[{,]$/, ''));

        styles.push({
          name,
          path: cssFile,
          size: content.length,
          selectors: selectors.slice(0, 50), // Limiter à 50 sélecteurs
          selectorCount: selectors.length,
          lastModified: fs.statSync(cssFile).mtime
        });
      } catch (error) {
        console.error(`Erreur lors de la lecture de ${cssFile}:`, error);
      }
    }

    this.programKnowledge.styles = styles;
  }

  /**
   * Scanne les scripts du programme
   */
  async scanScripts() {
    const scripts = [];

    // Lister les fichiers JavaScript
    const jsFiles = await this.findFiles('public/js/*.js');

    for (const jsFile of jsFiles) {
      try {
        const content = fs.readFileSync(jsFile, 'utf8');
        const name = path.basename(jsFile, '.js');

        // Extraire les fonctions
        const functionMatches = content.match(/function\s+(\w+)\s*\([^)]*\)/g) || [];
        const functions = functionMatches.map(match => {
          const nameMatch = match.match(/function\s+(\w+)/);
          return nameMatch ? nameMatch[1] : null;
        }).filter(Boolean);

        // Extraire les événements
        const eventMatches = content.match(/addEventListener\(\s*['"](\w+)['"]/g) || [];
        const events = eventMatches.map(match => {
          const eventMatch = match.match(/addEventListener\(\s*['"](\w+)['"]/);
          return eventMatch ? eventMatch[1] : null;
        }).filter(Boolean);

        scripts.push({
          name,
          path: jsFile,
          size: content.length,
          functions: functions.slice(0, 20), // Limiter à 20 fonctions
          functionCount: functions.length,
          events: events.slice(0, 20), // Limiter à 20 événements
          eventCount: events.length,
          lastModified: fs.statSync(jsFile).mtime
        });
      } catch (error) {
        console.error(`Erreur lors de la lecture de ${jsFile}:`, error);
      }
    }

    this.programKnowledge.scripts = scripts;
  }

  /**
   * Scanne les modèles du programme
   */
  async scanModels() {
    const models = [];

    // Lister les fichiers de modèles
    const modelFiles = await this.findFiles('models/*.js');

    for (const modelFile of modelFiles) {
      try {
        const content = fs.readFileSync(modelFile, 'utf8');
        const name = path.basename(modelFile, '.js');

        // Extraire la classe du modèle
        const classMatch = content.match(/class\s+(\w+)/);
        const className = classMatch ? classMatch[1] : name;

        // Extraire les propriétés du modèle
        const propertyMatches = content.match(/this\.(\w+)\s*=/g) || [];
        const properties = propertyMatches.map(match => {
          const propertyMatch = match.match(/this\.(\w+)\s*=/);
          return propertyMatch ? propertyMatch[1] : null;
        }).filter(Boolean);

        models.push({
          name,
          className,
          path: modelFile,
          size: content.length,
          properties: properties.slice(0, 20), // Limiter à 20 propriétés
          propertyCount: properties.length,
          lastModified: fs.statSync(modelFile).mtime
        });
      } catch (error) {
        console.error(`Erreur lors de la lecture de ${modelFile}:`, error);
      }
    }

    this.programKnowledge.models = models;
  }

  /**
   * Scanne les plugins du programme
   */
  async scanPlugins() {
    const plugins = [];

    // Lister les fichiers de plugins
    const pluginFiles = await this.findFiles('plugins/*.js');

    for (const pluginFile of pluginFiles) {
      try {
        const content = fs.readFileSync(pluginFile, 'utf8');
        const name = path.basename(pluginFile, '.js');

        plugins.push({
          name,
          path: pluginFile,
          size: content.length,
          lastModified: fs.statSync(pluginFile).mtime
        });
      } catch (error) {
        console.error(`Erreur lors de la lecture de ${pluginFile}:`, error);
      }
    }

    this.programKnowledge.plugins = plugins;
  }

  /**
   * Scanne le statut du système
   */
  async scanSystemStatus() {
    try {
      // Récupérer les informations sur le système
      const systemStatus = {
        timestamp: new Date(),
        memory: {},
        cpu: {},
        disk: {},
        network: {},
        processes: {},
        uptime: 0
      };

      // Récupérer l'uptime du système
      systemStatus.uptime = Math.floor(process.uptime());

      // Récupérer les informations sur la mémoire
      systemStatus.memory = {
        total: process.memoryUsage().heapTotal,
        used: process.memoryUsage().heapUsed,
        rss: process.memoryUsage().rss,
        external: process.memoryUsage().external
      };

      // Récupérer les informations sur le CPU
      systemStatus.cpu = {
        cores: require('os').cpus().length,
        model: require('os').cpus()[0].model,
        speed: require('os').cpus()[0].speed
      };

      // Récupérer les informations sur le disque
      const rootDir = path.resolve(__dirname, '../');
      await new Promise((resolve, reject) => {
        exec(`du -sh ${rootDir}`, (error, stdout, stderr) => {
          if (error) {
            console.error(`Erreur lors de la récupération des informations sur le disque:`, error);
            systemStatus.disk.size = 'Unknown';
          } else {
            const match = stdout.match(/^(\S+)/);
            systemStatus.disk.size = match ? match[1] : 'Unknown';
          }
          resolve();
        });
      });

      // Récupérer les informations sur les processus
      await new Promise((resolve, reject) => {
        exec('ps -eo pid,ppid,cmd | grep node', (error, stdout, stderr) => {
          if (error && error.code !== 1) {
            console.error(`Erreur lors de la récupération des informations sur les processus:`, error);
            systemStatus.processes.count = 0;
          } else {
            const lines = stdout.trim().split('\n');
            systemStatus.processes.count = lines.length;
            systemStatus.processes.list = lines.map(line => {
              const parts = line.trim().split(/\s+/);
              return {
                pid: parts[0],
                ppid: parts[1],
                command: parts.slice(2).join(' ')
              };
            }).slice(0, 10); // Limiter à 10 processus
          }
          resolve();
        });
      });

      this.programKnowledge.systemStatus = systemStatus;
    } catch (error) {
      console.error('Erreur lors du scan du statut du système:', error);
      this.programKnowledge.systemStatus = {
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * Stocke la connaissance du programme dans la mémoire thermique
   */
  async storeInThermalMemory() {
    if (!this.thermalMemoryService) {
      console.warn('Service de mémoire thermique non disponible pour stocker la connaissance du programme');
      return;
    }

    try {
      // Stocker la connaissance globale du programme
      await this.thermalMemoryService.storeMemory({
        type: 'program_knowledge',
        content: 'Connaissance globale du programme Vision Ultra',
        data: {
          summary: `Vision Ultra comprend ${this.programKnowledge.modules.length} modules, ${this.programKnowledge.interfaces.length} interfaces, ${this.programKnowledge.services.length} services, ${this.programKnowledge.capabilities.length} capacités, ${this.programKnowledge.routes.length} routes, ${this.programKnowledge.configurations.length} configurations, ${this.programKnowledge.components.length} composants, ${this.programKnowledge.assets.length} assets, ${this.programKnowledge.styles.length} styles, ${this.programKnowledge.scripts.length} scripts, ${this.programKnowledge.models.length} modèles et ${this.programKnowledge.plugins.length} plugins.`,
          lastScanTime: this.lastScanTime
        }
      });

      // Stocker les modules
      for (const module of this.programKnowledge.modules) {
        await this.thermalMemoryService.storeMemory({
          type: 'program_module',
          content: `Module ${module.name}`,
          data: module
        });
      }

      // Stocker les interfaces
      for (const interfaceItem of this.programKnowledge.interfaces) {
        await this.thermalMemoryService.storeMemory({
          type: 'program_interface',
          content: `Interface ${interfaceItem.name}`,
          data: interfaceItem
        });
      }

      // Stocker les services
      for (const service of this.programKnowledge.services) {
        await this.thermalMemoryService.storeMemory({
          type: 'program_service',
          content: `Service ${service.name}`,
          data: service
        });
      }

      // Stocker les capacités
      for (const capability of this.programKnowledge.capabilities) {
        await this.thermalMemoryService.storeMemory({
          type: 'program_capability',
          content: `Capacité ${capability.name}`,
          data: capability
        });
      }

      // Stocker les routes
      for (const route of this.programKnowledge.routes) {
        await this.thermalMemoryService.storeMemory({
          type: 'program_route',
          content: `Route ${route.name}`,
          data: route
        });
      }

      // Stocker les configurations
      for (const configuration of this.programKnowledge.configurations) {
        await this.thermalMemoryService.storeMemory({
          type: 'program_configuration',
          content: `Configuration ${configuration.name}`,
          data: configuration
        });
      }

      // Stocker les composants
      for (const component of this.programKnowledge.components) {
        await this.thermalMemoryService.storeMemory({
          type: 'program_component',
          content: `Composant ${component.name}`,
          data: component
        });
      }

      // Stocker les assets
      for (const asset of this.programKnowledge.assets.slice(0, 50)) { // Limiter à 50 assets
        await this.thermalMemoryService.storeMemory({
          type: 'program_asset',
          content: `Asset ${asset.name}`,
          data: asset
        });
      }

      // Stocker les styles
      for (const style of this.programKnowledge.styles) {
        await this.thermalMemoryService.storeMemory({
          type: 'program_style',
          content: `Style ${style.name}`,
          data: style
        });
      }

      // Stocker les scripts
      for (const script of this.programKnowledge.scripts) {
        await this.thermalMemoryService.storeMemory({
          type: 'program_script',
          content: `Script ${script.name}`,
          data: script
        });
      }

      // Stocker les modèles
      for (const model of this.programKnowledge.models) {
        await this.thermalMemoryService.storeMemory({
          type: 'program_model',
          content: `Modèle ${model.name}`,
          data: model
        });
      }

      // Stocker les plugins
      for (const plugin of this.programKnowledge.plugins) {
        await this.thermalMemoryService.storeMemory({
          type: 'program_plugin',
          content: `Plugin ${plugin.name}`,
          data: plugin
        });
      }

      // Stocker le statut du système
      await this.thermalMemoryService.storeMemory({
        type: 'program_system_status',
        content: `Statut du système Vision Ultra`,
        data: this.programKnowledge.systemStatus
      });

      console.log('Connaissance du programme stockée dans la mémoire thermique');
    } catch (error) {
      console.error('Erreur lors du stockage de la connaissance du programme dans la mémoire thermique:', error);
    }
  }

  /**
   * Analyse les éléments manquants et incomplets
   */
  async analyzeMissingAndIncompleteElements() {
    console.log('Analyse des éléments manquants et incomplets...');

    try {
      // Réinitialiser les listes
      this.programKnowledge.missingElements = [];
      this.programKnowledge.incompleteElements = [];

      // Vérifier les interfaces
      await this.analyzeInterfaces();

      // Vérifier les services
      await this.analyzeServices();

      // Vérifier les routes
      await this.analyzeRoutes();

      // Vérifier les styles
      await this.analyzeStyles();

      // Vérifier les scripts
      await this.analyzeScripts();

      // Stocker les résultats dans la mémoire thermique
      if (this.thermalMemoryService) {
        await this.thermalMemoryService.storeMemory({
          type: 'program_analysis',
          content: 'Analyse des éléments manquants et incomplets',
          data: {
            missingElements: this.programKnowledge.missingElements,
            incompleteElements: this.programKnowledge.incompleteElements,
            timestamp: new Date()
          }
        });
      }

      // Émettre un événement pour informer les clients
      this.io.emit('program analysis', {
        success: true,
        missingElements: this.programKnowledge.missingElements,
        incompleteElements: this.programKnowledge.incompleteElements,
        timestamp: new Date()
      });

      console.log(`Analyse terminée: ${this.programKnowledge.missingElements.length} éléments manquants, ${this.programKnowledge.incompleteElements.length} éléments incomplets`);
    } catch (error) {
      console.error('Erreur lors de l\'analyse des éléments manquants et incomplets:', error);
    }
  }

  /**
   * Analyse les interfaces pour détecter les éléments manquants et incomplets
   */
  async analyzeInterfaces() {
    // Liste des interfaces attendues
    const expectedInterfaces = [
      'luna-dashboard', 'luna-chat', 'luna-memory', 'luna-brain', 'luna-cognitive-fixed',
      'luna-settings', 'luna-mcp', 'luna-training', 'luna-code', 'luna-security',
      'luna-internet-page', 'luna-vpn', 'luna-connectivity', 'luna-antivirus',
      'luna-accelerators-updated', 'luna-media', 'luna-backup', 'luna-monitor',
      'luna-thermal', 'luna-reflection', 'luna-program-knowledge'
    ];

    // Vérifier les interfaces manquantes
    const existingInterfaces = this.programKnowledge.interfaces.map(i => i.name);
    const missingInterfaces = expectedInterfaces.filter(i => !existingInterfaces.includes(i));

    // Ajouter les interfaces manquantes à la liste
    missingInterfaces.forEach(name => {
      this.programKnowledge.missingElements.push({
        type: 'interface',
        name,
        reason: 'Interface manquante',
        priority: 'high'
      });
    });

    // Vérifier les interfaces incomplètes (taille < 1000 octets)
    const incompleteInterfaces = this.programKnowledge.interfaces.filter(i => i.size < 1000);

    // Ajouter les interfaces incomplètes à la liste
    incompleteInterfaces.forEach(i => {
      this.programKnowledge.incompleteElements.push({
        type: 'interface',
        name: i.name,
        reason: `Interface potentiellement incomplète (taille: ${i.size} octets)`,
        priority: 'medium'
      });
    });
  }

  /**
   * Analyse les services pour détecter les éléments manquants et incomplets
   */
  async analyzeServices() {
    // Liste des services attendus
    const expectedServices = [
      'thermal-memory-service', 'mcp-service', 'reflection-service', 'program-knowledge-service',
      'media-generator-service', 'brain-presence-service', 'security-service'
    ];

    // Vérifier les services manquants
    const existingServices = this.programKnowledge.services.map(s => s.name);
    const missingServices = expectedServices.filter(s => !existingServices.includes(s));

    // Ajouter les services manquants à la liste
    missingServices.forEach(name => {
      this.programKnowledge.missingElements.push({
        type: 'service',
        name,
        reason: 'Service manquant',
        priority: 'high'
      });
    });

    // Vérifier les services incomplets (moins de 3 méthodes)
    const incompleteServices = this.programKnowledge.services.filter(s => s.methods.length < 3);

    // Ajouter les services incomplets à la liste
    incompleteServices.forEach(s => {
      this.programKnowledge.incompleteElements.push({
        type: 'service',
        name: s.name,
        reason: `Service potentiellement incomplet (${s.methods.length} méthodes)`,
        priority: 'medium'
      });
    });
  }

  /**
   * Analyse les routes pour détecter les éléments manquants et incomplets
   */
  async analyzeRoutes() {
    // Liste des routes attendues
    const expectedRoutes = [
      '/luna', '/luna/chat', '/luna/memory', '/luna/brain', '/luna/cognitive',
      '/luna/settings', '/luna/mcp', '/luna/training', '/luna/code', '/luna/security',
      '/luna/internet', '/luna/dashboard', '/luna/vpn', '/luna/connectivity', '/luna/antivirus',
      '/luna/accelerators', '/luna/media', '/luna/backup', '/luna/monitor',
      '/luna/thermal', '/luna/reflection', '/luna/program-knowledge'
    ];

    // Collecter toutes les routes existantes
    const existingRoutes = [];
    this.programKnowledge.routes.forEach(r => {
      r.paths.forEach(p => {
        existingRoutes.push(p.path);
      });
    });

    // Vérifier les routes manquantes
    const missingRoutes = expectedRoutes.filter(r => !existingRoutes.some(er => er === r || er.startsWith(r + '/')));

    // Ajouter les routes manquantes à la liste
    missingRoutes.forEach(path => {
      this.programKnowledge.missingElements.push({
        type: 'route',
        name: path,
        reason: 'Route manquante',
        priority: 'high'
      });
    });
  }

  /**
   * Analyse les styles pour détecter les éléments manquants et incomplets
   */
  async analyzeStyles() {
    // Liste des styles attendus
    const expectedStyles = [
      'luna-dashboard', 'luna-chat', 'luna-memory', 'luna-brain', 'luna-cognitive',
      'luna-settings', 'luna-mcp', 'luna-training', 'luna-code', 'luna-security',
      'luna-internet', 'luna-vpn', 'luna-connectivity', 'luna-antivirus',
      'luna-accelerators', 'luna-media', 'luna-backup', 'luna-monitor',
      'luna-thermal', 'luna-reflection', 'luna-program-knowledge'
    ];

    // Vérifier les styles manquants
    const existingStyles = this.programKnowledge.styles.map(s => s.name);
    const missingStyles = expectedStyles.filter(s => !existingStyles.includes(s));

    // Ajouter les styles manquants à la liste
    missingStyles.forEach(name => {
      this.programKnowledge.missingElements.push({
        type: 'style',
        name: name + '.css',
        reason: 'Style manquant',
        priority: 'medium'
      });
    });

    // Vérifier les styles incomplets (moins de 10 sélecteurs)
    const incompleteStyles = this.programKnowledge.styles.filter(s => s.selectorCount < 10);

    // Ajouter les styles incomplets à la liste
    incompleteStyles.forEach(s => {
      this.programKnowledge.incompleteElements.push({
        type: 'style',
        name: s.name + '.css',
        reason: `Style potentiellement incomplet (${s.selectorCount} sélecteurs)`,
        priority: 'low'
      });
    });
  }

  /**
   * Analyse les scripts pour détecter les éléments manquants et incomplets
   */
  async analyzeScripts() {
    // Liste des scripts attendus
    const expectedScripts = [
      'luna-dashboard', 'luna-chat', 'luna-memory', 'luna-brain', 'luna-cognitive',
      'luna-settings', 'luna-mcp', 'luna-training', 'luna-code', 'luna-security',
      'luna-internet', 'luna-vpn', 'luna-connectivity', 'luna-antivirus',
      'luna-accelerators', 'luna-media', 'luna-backup', 'luna-monitor',
      'luna-thermal', 'luna-reflection', 'luna-program-knowledge'
    ];

    // Vérifier les scripts manquants
    const existingScripts = this.programKnowledge.scripts.map(s => s.name);
    const missingScripts = expectedScripts.filter(s => !existingScripts.includes(s));

    // Ajouter les scripts manquants à la liste
    missingScripts.forEach(name => {
      this.programKnowledge.missingElements.push({
        type: 'script',
        name: name + '.js',
        reason: 'Script manquant',
        priority: 'medium'
      });
    });

    // Vérifier les scripts incomplets (moins de 5 fonctions)
    const incompleteScripts = this.programKnowledge.scripts.filter(s => s.functionCount < 5);

    // Ajouter les scripts incomplets à la liste
    incompleteScripts.forEach(s => {
      this.programKnowledge.incompleteElements.push({
        type: 'script',
        name: s.name + '.js',
        reason: `Script potentiellement incomplet (${s.functionCount} fonctions)`,
        priority: 'low'
      });
    });
  }

  /**
   * Obtient la connaissance du programme
   * @returns {Object} - Connaissance du programme
   */
  getProgramKnowledge() {
    return {
      programKnowledge: this.programKnowledge,
      lastScanTime: this.lastScanTime
    };
  }
}

module.exports = ProgramKnowledgeService;
