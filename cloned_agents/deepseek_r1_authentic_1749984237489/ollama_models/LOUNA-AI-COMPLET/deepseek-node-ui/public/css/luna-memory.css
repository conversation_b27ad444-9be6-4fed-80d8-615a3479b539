/* Styles pour la page de mémoire thermique */

:root {
  --luna-primary: #9c89b8;
  --luna-secondary: #f0a6ca;
  --luna-accent: #b8bedd;
  --luna-dark: #1a1a2e;
  --luna-light: #edf2fb;
  --luna-gradient: linear-gradient(135deg, #9c89b8, #f0a6ca);
  --zone1-color: #ff5252;
  --zone2-color: #ff9800;
  --zone3-color: #29b6f6;
  --zone4-color: #5c6bc0;
  --zone5-color: #78909c;
  --zone6-color: #424242;
}

/* Styles pour les zones de mémoire */
.memory-zones-container {
  margin-bottom: 2rem;
}

.memory-zone-header {
  margin-bottom: 1.5rem;
}

.memory-zones {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.memory-zone {
  background: rgba(26, 26, 46, 0.7);
  border: 1px solid rgba(184, 190, 221, 0.2);
  border-radius: 10px;
  padding: 1rem;
  transition: all 0.3s ease;
  cursor: pointer;
}

.memory-zone:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.memory-zone.active {
  border-color: var(--luna-secondary);
  box-shadow: 0 0 15px rgba(240, 166, 202, 0.3);
}

.zone-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(184, 190, 221, 0.2);
}

.zone-header h5 {
  margin: 0;
  font-weight: 600;
}

.zone-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.5s ease;
}

.memory-zone.active .zone-content {
  max-height: 500px;
}

/* Styles pour les éléments de mémoire */
.memory-item {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 0.75rem;
  margin-bottom: 0.75rem;
  transition: all 0.3s ease;
}

.memory-item:hover {
  background: rgba(0, 0, 0, 0.3);
}

.memory-item-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.memory-timestamp {
  color: var(--luna-accent);
  font-size: 0.8rem;
}

.memory-title {
  font-weight: 600;
}

.memory-item-content {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
}

.memory-item-content p {
  margin-bottom: 0.5rem;
}

.memory-item-content p:last-child {
  margin-bottom: 0;
}

/* Message pour les zones vides */
.empty-zone-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  color: rgba(255, 255, 255, 0.5);
  text-align: center;
}

.empty-zone-message i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

/* Styles pour les animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.memory-item {
  animation: fadeIn 0.5s ease forwards;
}

/* Styles pour le graphique */
canvas {
  margin-bottom: 1rem;
}

/* Styles pour les boutons */
.btn-luna {
  background: var(--luna-gradient);
  border: none;
  color: white;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-luna:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-luna-outline {
  background: transparent;
  border: 1px solid var(--luna-secondary);
  color: var(--luna-secondary);
}

.btn-luna-outline:hover {
  background: rgba(240, 166, 202, 0.1);
}

/* Animation de rotation pour les icônes de chargement */
.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Styles pour les transitions entre zones */
.memory-zone[data-zone="1"] {
  border-left: 4px solid var(--zone1-color);
}

.memory-zone[data-zone="2"] {
  border-left: 4px solid var(--zone2-color);
}

.memory-zone[data-zone="3"] {
  border-left: 4px solid var(--zone3-color);
}

.memory-zone[data-zone="4"] {
  border-left: 4px solid var(--zone4-color);
}

.memory-zone[data-zone="5"] {
  border-left: 4px solid var(--zone5-color);
}

.memory-zone[data-zone="6"] {
  border-left: 4px solid var(--zone6-color);
}

/* Transition en douceur entre les zones (effet de feuilles qui tombent) */
@keyframes leafFall {
  0% { opacity: 0; transform: translateY(-20px) rotate(-5deg); }
  100% { opacity: 1; transform: translateY(0) rotate(0); }
}

.memory-item {
  animation: leafFall 0.6s ease forwards;
  animation-delay: calc(var(--item-index, 0) * 0.1s);
}

/* Styles pour le pied de page fixe */
.footer-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 40px;
  background-color: #1a1a2e;
  border-top: 1px solid #2d2d4a;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  color: #edf2fb;
}
