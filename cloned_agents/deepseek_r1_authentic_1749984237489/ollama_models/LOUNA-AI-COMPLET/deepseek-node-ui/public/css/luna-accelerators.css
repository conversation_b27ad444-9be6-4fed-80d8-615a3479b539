/**
 * Styles pour l'interface des accélérateurs Luna
 */

/* Tableau des accélérateurs */
.accelerator-table {
  color: var(--luna-light);
  border-collapse: separate;
  border-spacing: 0;
}

.accelerator-table thead th {
  background-color: rgba(0, 0, 0, 0.3);
  border-bottom: 2px solid rgba(156, 137, 184, 0.3);
  padding: 0.75rem 1rem;
  font-weight: 500;
}

.accelerator-table tbody td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid rgba(156, 137, 184, 0.1);
  vertical-align: middle;
}

.accelerator-row {
  transition: background-color 0.2s ease;
}

.accelerator-row:hover {
  background-color: rgba(156, 137, 184, 0.1);
}

/* Badges pour les types d'accélérateurs */
.badge {
  padding: 0.5em 0.75em;
  font-weight: 500;
  border-radius: 6px;
}

.badge.bg-primary {
  background: linear-gradient(135deg, #9c89b8, #b8bedd) !important;
}

.badge.bg-info {
  background: linear-gradient(135deg, #b8bedd, #f0a6ca) !important;
}

.badge.bg-warning {
  background: linear-gradient(135deg, #f0a6ca, #9c89b8) !important;
}

.badge.bg-success {
  background: linear-gradient(135deg, #9c89b8, #f0a6ca) !important;
}

/* Barres de progression */
.progress {
  background-color: rgba(0, 0, 0, 0.3);
  height: 8px;
  margin-bottom: 0;
}

.progress-bar {
  background: linear-gradient(to right, #9c89b8, #f0a6ca);
}

/* Indicateurs de statut */
.status-indicator {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 5px;
}

.status-active {
  background-color: #4caf50;
  box-shadow: 0 0 5px #4caf50;
}

.status-inactive {
  background-color: #f44336;
  box-shadow: 0 0 5px #f44336;
}

.status-warning {
  background-color: #ff9800;
  box-shadow: 0 0 5px #ff9800;
}

/* Boutons d'action */
.toggle-accelerator-btn, .optimize-accelerator-btn {
  width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-accelerator-btn:hover {
  background-color: rgba(156, 137, 184, 0.2);
  border-color: rgba(156, 137, 184, 0.5);
}

.optimize-accelerator-btn:hover {
  background-color: rgba(240, 166, 202, 0.2);
  border-color: rgba(240, 166, 202, 0.5);
}

/* Animation pour les boutons d'optimisation */
@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

.optimize-accelerator-btn:hover i, #optimize-all-btn:hover i {
  animation: pulse 1s infinite;
}

/* Styles pour les sliders personnalisés */
.form-range::-webkit-slider-thumb {
  background: linear-gradient(135deg, #9c89b8, #f0a6ca);
}

.form-range::-moz-range-thumb {
  background: linear-gradient(135deg, #9c89b8, #f0a6ca);
}

.form-range::-ms-thumb {
  background: linear-gradient(135deg, #9c89b8, #f0a6ca);
}

/* Animation de rotation pour les icônes de chargement */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spin {
  animation: spin 1s linear infinite;
}

/* Styles pour les graphiques */
#accelerator-types-chart {
  max-height: 200px;
}

/* Styles pour la hiérarchie des accélérateurs */
.accelerators-hierarchy {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Chef d'orchestre principal */
.master-conductor {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 2rem;
}

.conductor-card {
  background: rgba(26, 26, 46, 0.8);
  border: 1px solid rgba(156, 137, 184, 0.3);
  border-radius: 12px;
  padding: 1.25rem;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.conductor-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  border-color: var(--luna-secondary);
}

.conductor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(156, 137, 184, 0.2);
}

.conductor-header h4, .conductor-header h5 {
  margin: 0;
  font-weight: 600;
}

.conductor-status {
  display: flex;
  align-items: center;
}

.conductor-body {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.conductor-stats {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.stat-item span:first-child {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
}

.stat-item span:last-child {
  font-weight: 600;
  font-size: 1.1rem;
}

.conductor-meter {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.meter-label {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
  width: 80px;
}

.meter-bar {
  flex-grow: 1;
  height: 8px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  overflow: hidden;
}

.meter-fill {
  height: 100%;
  background: linear-gradient(to right, #9c89b8, #f0a6ca);
  border-radius: 4px;
  transition: width 0.5s ease;
}

.meter-value {
  font-size: 0.85rem;
  font-weight: 600;
  width: 40px;
  text-align: right;
}

/* Connexions entre les chefs d'orchestre */
.conductor-connections {
  position: relative;
  width: 100%;
  height: 80px;
  margin-top: 1rem;
}

.connection-line {
  position: absolute;
  height: 2px;
  background: linear-gradient(to right, rgba(156, 137, 184, 0.8), rgba(240, 166, 202, 0.8));
  transform-origin: left center;
  box-shadow: 0 0 10px rgba(240, 166, 202, 0.5);
}

/* Animation pour les connexions */
@keyframes pulse-line {
  0% { opacity: 0.5; }
  50% { opacity: 1; }
  100% { opacity: 0.5; }
}

.connection-line::after {
  content: '';
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(240, 166, 202, 1);
  right: -4px;
  top: -3px;
  box-shadow: 0 0 10px rgba(240, 166, 202, 0.8);
  animation: pulse 2s infinite;
}

/* Chefs d'orchestre de zone */
.zone-conductors {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.zone-conductor {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Groupes d'accélérateurs */
.accelerator-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1rem;
  position: relative;
  z-index: 1;
}

.accelerator-item, .accelerator {
  background: rgba(26, 26, 46, 0.6);
  border: 1px solid rgba(156, 137, 184, 0.2);
  border-radius: 8px;
  padding: 0.75rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.accelerator-item:hover, .accelerator:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  border-color: var(--luna-secondary);
}

/* Effet de lueur pour les accélérateurs */
.accelerator::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  background: linear-gradient(45deg, var(--luna-primary), var(--luna-secondary));
  border-radius: 12px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.accelerator:hover::before {
  opacity: 0.3;
}

/* Animation pour les accélérateurs actifs */
@keyframes accelerator-active {
  0% { box-shadow: 0 0 5px rgba(156, 137, 184, 0.5); }
  50% { box-shadow: 0 0 15px rgba(240, 166, 202, 0.7); }
  100% { box-shadow: 0 0 5px rgba(156, 137, 184, 0.5); }
}

.accelerator.active {
  animation: accelerator-active 2s infinite;
}

.accelerator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.accelerator-name {
  font-size: 0.85rem;
  font-weight: 600;
}

.accelerator-body {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.accelerator-efficiency {
  font-size: 1.1rem;
  font-weight: 700;
  text-align: center;
  margin: 0.25rem 0;
}

.accelerator-bar, .accelerator-meter {
  height: 6px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
  overflow: hidden;
}

.accelerator-bar-fill, .accelerator-fill {
  height: 100%;
  background: linear-gradient(to right, #9c89b8, #f0a6ca);
  border-radius: 3px;
  transition: width 0.5s ease;
}

/* Animation pour les accélérateurs actifs */
@keyframes accelerator-pulse {
  0% { opacity: 0.8; }
  50% { opacity: 1; }
  100% { opacity: 0.8; }
}

.accelerator-fill.active {
  animation: accelerator-pulse 1.5s infinite;
}

/* Flux de données */
.data-flow-container {
  position: relative;
  height: 150px;
}

/* Animation pour les particules de données */
@keyframes flow-particle {
  0% { transform: translateX(0) translateY(0); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateX(100%) translateY(0); opacity: 0; }
}

@keyframes connection-flow {
  0% { transform: translateX(0); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateX(100%); opacity: 0; }
}

.data-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: rgba(240, 166, 202, 0.8);
  box-shadow: 0 0 5px rgba(240, 166, 202, 0.5);
  animation: flow-particle 3s linear infinite;
}

.connection-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: rgba(156, 137, 184, 0.9);
  box-shadow: 0 0 8px rgba(156, 137, 184, 0.7);
  animation: connection-flow 1.5s linear forwards;
}

/* Animation pour les connexions actives */
@keyframes connection-pulse {
  0% { opacity: 0.5; }
  50% { opacity: 1; }
  100% { opacity: 0.5; }
}

.active-connection {
  animation: connection-pulse 1s infinite;
  box-shadow: 0 0 10px rgba(156, 137, 184, 0.7);
}

/* Styles pour le pied de page fixe */
.footer-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 40px;
  background-color: #1a1a2e;
  border-top: 1px solid #2d2d4a;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  color: #edf2fb;
}
