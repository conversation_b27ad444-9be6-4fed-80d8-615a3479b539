:root {
  --primary-color: #6c5ce7;
  --secondary-color: #a29bfe;
  --dark-bg: #1a1a2e;
  --darker-bg: #16213e;
  --light-text: #f1f1f1;
  --muted-text: #a0a0a0;
  --border-color: #2d3748;
  --user-message-bg: #2d3748;
  --bot-message-bg: #3a3f58;
  --code-bg: #2d2d2d;
}

body {
  background-color: var(--dark-bg);
  color: var(--light-text);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}

/* Sidebar styles */
.sidebar {
  background-color: var(--darker-bg);
  height: 100vh;
  overflow-y: auto;
  border-right: 1px solid var(--border-color);
}

.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: var(--darker-bg);
}

.sidebar::-webkit-scrollbar-thumb {
  background-color: var(--border-color);
  border-radius: 3px;
}

.conversations-list {
  margin-top: 20px;
}

.conversations-list .list-group-item {
  background-color: transparent;
  border-color: var(--border-color);
  color: var(--light-text);
  cursor: pointer;
  transition: background-color 0.2s;
}

.conversations-list .list-group-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.conversations-list .list-group-item.active {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

/* Main content styles */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 56px); /* Hauteur de la navbar */
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.chat-header {
  background-color: var(--darker-bg);
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: var(--dark-bg);
  display: flex;
  flex-direction: column;
}

.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: var(--dark-bg);
}

.chat-messages::-webkit-scrollbar-thumb {
  background-color: var(--border-color);
  border-radius: 3px;
}

.chat-input {
  background-color: var(--darker-bg);
  padding: 15px;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

/* Message styles */
.message {
  margin-bottom: 20px;
  max-width: 85%;
}

.message-user {
  align-self: flex-end;
  background-color: var(--user-message-bg);
  border-radius: 18px 18px 0 18px;
  margin-left: auto;
}

.message-bot {
  align-self: flex-start;
  background-color: var(--bot-message-bg);
  border-radius: 18px 18px 18px 0;
}

.message-content {
  padding: 12px 16px;
}

.message-time {
  font-size: 0.75rem;
  color: var(--muted-text);
  text-align: right;
  margin-top: 4px;
}

/* Markdown content styling */
.markdown-content pre {
  background-color: var(--code-bg);
  border-radius: 6px;
  padding: 12px;
  overflow-x: auto;
  margin: 10px 0;
}

.markdown-content code {
  font-family: 'Fira Code', 'Courier New', monospace;
  font-size: 0.9em;
}

.markdown-content p {
  margin-bottom: 10px;
}

.markdown-content ul, .markdown-content ol {
  padding-left: 20px;
  margin-bottom: 10px;
}

.markdown-content h1, .markdown-content h2, .markdown-content h3,
.markdown-content h4, .markdown-content h5, .markdown-content h6 {
  margin-top: 16px;
  margin-bottom: 8px;
}

.markdown-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 16px 0;
}

.markdown-content th, .markdown-content td {
  border: 1px solid var(--border-color);
  padding: 8px;
}

.markdown-content th {
  background-color: var(--darker-bg);
}

/* Loading indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  margin: 10px 0;
}

.typing-indicator span {
  height: 8px;
  width: 8px;
  background-color: var(--muted-text);
  border-radius: 50%;
  display: inline-block;
  margin-right: 5px;
  animation: typing 1s infinite;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
  100% { transform: translateY(0); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    top: 0;
    left: -100%;
    width: 80%;
    z-index: 1000;
    transition: left 0.3s ease;
  }

  .sidebar.show {
    left: 0;
  }

  .main-content {
    width: 100%;
  }

  .message {
    max-width: 90%;
  }
}
