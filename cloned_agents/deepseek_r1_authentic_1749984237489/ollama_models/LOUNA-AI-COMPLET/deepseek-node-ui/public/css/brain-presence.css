/**
 * Styles pour l'interface de présence du cerveau
 */

/* Conteneur principal */
.brain-presence-container {
  position: relative;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 20px;
  transition: all 0.3s ease;
  overflow: hidden;
}

/* Mode compact */
.brain-presence-container.compact {
  height: 80px;
}

/* Mode étendu */
.brain-presence-container.expanded {
  height: 300px;
}

/* En-tête de présence */
.brain-presence-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.brain-presence-title {
  display: flex;
  align-items: center;
  font-size: 1.1rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

.brain-presence-title i {
  margin-right: 8px;
  font-size: 1.2rem;
}

.brain-presence-controls {
  display: flex;
  gap: 8px;
}

.brain-presence-controls button {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.brain-presence-controls button:hover {
  color: rgba(255, 255, 255, 1);
  background-color: rgba(255, 255, 255, 0.1);
}

/* Indicateur de présence */
.brain-presence-indicator {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 10px;
  transition: all 0.5s ease;
}

.presence-active {
  background-color: #4CAF50;
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.7);
  animation: pulse 2s infinite;
}

.presence-inactive {
  background-color: #F44336;
  box-shadow: 0 0 5px rgba(244, 67, 54, 0.5);
}

/* Animation de pulsation */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Indicateur de statut */
.status-active {
  color: #4CAF50;
}

.status-inactive {
  color: #F44336;
}

/* Contenu de présence */
.brain-presence-content {
  display: flex;
  flex-direction: column;
  height: calc(100% - 40px);
  overflow: hidden;
}

/* Informations d'activité */
.brain-activity-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.activity-level {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
}

.activity-progress {
  height: 6px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  overflow: hidden;
  margin-top: 5px;
}

.activity-progress .progress-bar {
  height: 100%;
  border-radius: 3px;
  transition: width 0.5s ease;
}

/* Animation du cerveau */
.brain-animation-container {
  position: relative;
  height: 150px;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 10px;
  box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.5);
}

.brain-animation {
  position: relative;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 80%;
}

.brain-animation svg {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  height: 80%;
  transition: transform 0.5s ease, opacity 0.5s ease;
}

.brain-neuron {
  position: absolute;
  border-radius: 50%;
  animation: neuron-pulse 1.5s ease-out;
  opacity: 0;
  z-index: 2;
}

.brain-neuron.fade-out {
  animation: neuron-fade-out 0.5s ease-out forwards;
}

@keyframes neuron-pulse {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  20% {
    transform: scale(1.2);
    opacity: 1;
  }
  80% {
    transform: scale(1);
    opacity: 0.8;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.6;
  }
}

@keyframes neuron-fade-out {
  0% {
    opacity: 0.6;
    transform: scale(0.8);
  }
  100% {
    opacity: 0;
    transform: scale(0);
  }
}

/* Connexions neuronales */
.brain-connection {
  position: absolute;
  background-color: rgba(156, 137, 184, 0.4);
  transform-origin: 0 0;
  z-index: 1;
  height: 1px;
  animation: connection-appear 0.8s ease-out;
  box-shadow: 0 0 5px rgba(156, 137, 184, 0.6);
}

.brain-connection.fade-out {
  animation: connection-fade-out 0.3s ease-out forwards;
}

@keyframes connection-appear {
  0% {
    opacity: 0;
    height: 0;
  }
  50% {
    height: 2px;
  }
  100% {
    opacity: 0.8;
    height: 1px;
  }
}

@keyframes connection-fade-out {
  0% {
    opacity: 0.8;
  }
  100% {
    opacity: 0;
  }
}

/* Effet de pulsation du cerveau */
.brain-pulse {
  animation: brain-pulse 2s infinite alternate;
}

@keyframes brain-pulse {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }
  100% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

/* Statistiques avancées */
.brain-stats-container {
  margin-top: 10px;
  margin-bottom: 10px;
}

.stats-card {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 10px;
  height: 100%;
  margin-bottom: 10px;
}

.stats-title {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 5px;
}

.stats-content {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.stats-item {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
}

.stats-label {
  color: rgba(255, 255, 255, 0.6);
}

.stats-value {
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

/* Graphiques */
.brain-charts {
  display: flex;
  gap: 15px;
  margin-top: 10px;
  height: 120px;
}

.chart-container {
  flex: 1;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 10px;
  height: 100%;
}

.chart-title {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 5px;
}

/* Pensées */
.thought-container {
  margin-top: 10px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 10px;
  height: 120px;
  overflow: hidden;
}

.thought-title {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.thought-item {
  background-color: rgba(156, 137, 184, 0.1);
  border-left: 3px solid rgba(156, 137, 184, 0.7);
  border-radius: 0 4px 4px 0;
  padding: 8px 10px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.thought-header {
  display: flex;
  justify-content: space-between;
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 3px;
}

.thought-content {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
}

/* Types de pensées */
.thought-analyse {
  border-left-color: #4CAF50;
}

.thought-réflexion {
  border-left-color: #2196F3;
}

.thought-observation {
  border-left-color: #9C89B8;
}

.thought-question {
  border-left-color: #FF9800;
}

.thought-idée {
  border-left-color: #E91E63;
}

.thought-souvenir {
  border-left-color: #673AB7;
}

.thought-association {
  border-left-color: #00BCD4;
}

/* Animation d'apparition des pensées */
.thought-appear {
  animation: thought-appear 0.5s ease-out;
}

@keyframes thought-appear {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Historique des pensées */
.thought-history-container {
  display: none;
  height: 100%;
  overflow-y: auto;
}

.history-item {
  margin-bottom: 5px;
  padding: 5px 8px;
}

/* Barre de défilement personnalisée */
.thought-history-container::-webkit-scrollbar {
  width: 6px;
}

.thought-history-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.thought-history-container::-webkit-scrollbar-thumb {
  background: rgba(156, 137, 184, 0.5);
  border-radius: 3px;
}

.thought-history-container::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 137, 184, 0.7);
}

/* Responsive */
@media (max-width: 768px) {
  .brain-charts {
    flex-direction: column;
    height: auto;
    gap: 10px;
  }

  .chart-container {
    height: 100px;
  }

  .brain-presence-container.expanded {
    height: 500px;
  }
}
