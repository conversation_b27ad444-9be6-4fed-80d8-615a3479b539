/**
 * Luna Code - Styles pour l'éditeur de code
 */

/* Conteneur principal de l'éditeur */
.code-editor-container {
  display: flex;
  flex-direction: column;
  height: 75vh;
  border-radius: 10px;
  overflow: hidden;
  background-color: rgba(26, 26, 46, 0.8);
  border: 1px solid rgba(156, 137, 184, 0.3);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Barre d'outils de l'éditeur */
.code-editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  background: linear-gradient(90deg, rgba(156, 137, 184, 0.2), rgba(240, 166, 202, 0.2));
  border-bottom: 1px solid rgba(156, 137, 184, 0.3);
}

.code-editor-toolbar .left-tools,
.code-editor-toolbar .right-tools {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Zone de l'éditeur */
.code-editor {
  flex-grow: 1;
  overflow: auto;
  font-family: 'Fira Code', 'Consolas', monospace;
  font-size: 14px;
  line-height: 1.5;
  padding: 1rem;
  background-color: rgba(26, 26, 46, 0.95);
  color: #e0e0e0;
  resize: none;
  border: none;
  outline: none;
  tab-size: 4;
}

/* Barre de statut */
.code-editor-statusbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem 1rem;
  background: rgba(26, 26, 46, 0.9);
  border-top: 1px solid rgba(156, 137, 184, 0.3);
  font-size: 12px;
  color: #aaa;
}

/* Styles pour la coloration syntaxique */
.code-block {
  position: relative;
  margin: 1rem 0;
  border-radius: 8px;
  overflow: hidden;
}

.code-block-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  background: rgba(156, 137, 184, 0.2);
  border-bottom: 1px solid rgba(156, 137, 184, 0.3);
}

.code-block-content {
  padding: 1rem;
  background-color: rgba(26, 26, 46, 0.95);
  overflow-x: auto;
  max-height: 500px;
  overflow-y: auto;
}

.code-block-footer {
  display: flex;
  justify-content: flex-end;
  padding: 0.5rem 1rem;
  background: rgba(26, 26, 46, 0.9);
  border-top: 1px solid rgba(156, 137, 184, 0.3);
}

/* Bouton de copie */
.copy-btn {
  background: transparent;
  border: 1px solid rgba(156, 137, 184, 0.5);
  color: rgba(156, 137, 184, 0.8);
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.copy-btn:hover {
  background: rgba(156, 137, 184, 0.2);
  color: rgba(240, 166, 202, 1);
}

.copy-btn.copied {
  background: rgba(156, 137, 184, 0.3);
  color: #4caf50;
}

/* Coloration syntaxique pour différents langages */
/* JavaScript */
.language-js .keyword { color: #c792ea; }
.language-js .string { color: #c3e88d; }
.language-js .number { color: #f78c6c; }
.language-js .comment { color: #676e95; font-style: italic; }
.language-js .function { color: #82aaff; }
.language-js .operator { color: #89ddff; }
.language-js .variable { color: #eeffff; }
.language-js .property { color: #80cbc4; }

/* Python */
.language-python .keyword { color: #c792ea; }
.language-python .string { color: #c3e88d; }
.language-python .number { color: #f78c6c; }
.language-python .comment { color: #676e95; font-style: italic; }
.language-python .function { color: #82aaff; }
.language-python .operator { color: #89ddff; }
.language-python .variable { color: #eeffff; }
.language-python .class { color: #ffcb6b; }

/* HTML */
.language-html .tag { color: #f07178; }
.language-html .attr-name { color: #ffcb6b; }
.language-html .attr-value { color: #c3e88d; }
.language-html .comment { color: #676e95; font-style: italic; }
.language-html .doctype { color: #546e7a; font-style: italic; }

/* CSS */
.language-css .selector { color: #f07178; }
.language-css .property { color: #80cbc4; }
.language-css .value { color: #c3e88d; }
.language-css .comment { color: #676e95; font-style: italic; }
.language-css .unit { color: #f78c6c; }

/* Ligne de code surlignée */
.highlighted-line {
  background-color: rgba(156, 137, 184, 0.1);
  display: block;
  margin: 0 -1rem;
  padding: 0 1rem;
}

/* Numéros de ligne */
.line-numbers {
  counter-reset: line;
  padding-right: 1rem;
  border-right: 1px solid rgba(156, 137, 184, 0.2);
  margin-right: 1rem;
  color: #676e95;
  user-select: none;
}

.line-numbers span {
  counter-increment: line;
  display: block;
  line-height: 1.5;
}

.line-numbers span::before {
  content: counter(line);
  display: inline-block;
  width: 2rem;
  text-align: right;
  padding-right: 0.5rem;
}

/* Animation pour le chargement */
@keyframes pulse {
  0% { opacity: 0.5; }
  50% { opacity: 1; }
  100% { opacity: 0.5; }
}

.code-loading {
  animation: pulse 1.5s infinite;
  text-align: center;
  padding: 2rem;
  color: rgba(156, 137, 184, 0.8);
}

/* Styles pour les onglets de fichiers */
.code-tabs {
  display: flex;
  background-color: rgba(26, 26, 46, 0.9);
  border-bottom: 1px solid rgba(156, 137, 184, 0.3);
  overflow-x: auto;
  white-space: nowrap;
}

.code-tab {
  padding: 0.5rem 1rem;
  background-color: rgba(26, 26, 46, 0.7);
  border-right: 1px solid rgba(156, 137, 184, 0.2);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.code-tab:hover {
  background-color: rgba(156, 137, 184, 0.1);
}

.code-tab.active {
  background-color: rgba(156, 137, 184, 0.2);
  border-bottom: 2px solid rgba(240, 166, 202, 0.8);
}

.code-tab .close-tab {
  margin-left: 0.5rem;
  opacity: 0.5;
  transition: all 0.2s ease;
}

.code-tab:hover .close-tab {
  opacity: 1;
}

/* Styles pour le menu contextuel */
.context-menu {
  position: absolute;
  background-color: rgba(26, 26, 46, 0.95);
  border: 1px solid rgba(156, 137, 184, 0.3);
  border-radius: 4px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  min-width: 150px;
}

.context-menu-item {
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.context-menu-item:hover {
  background-color: rgba(156, 137, 184, 0.2);
}

.context-menu-separator {
  height: 1px;
  background-color: rgba(156, 137, 184, 0.3);
  margin: 0.25rem 0;
}

/* Styles pour les suggestions de code */
.code-suggestions {
  position: absolute;
  background-color: rgba(26, 26, 46, 0.95);
  border: 1px solid rgba(156, 137, 184, 0.3);
  border-radius: 4px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
  min-width: 200px;
}

.suggestion-item {
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.suggestion-item:hover,
.suggestion-item.selected {
  background-color: rgba(156, 137, 184, 0.2);
}

.suggestion-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.suggestion-text {
  flex-grow: 1;
}

.suggestion-type {
  font-size: 12px;
  color: #676e95;
}

/* Styles pour les erreurs et avertissements */
.code-error {
  text-decoration: wavy underline #f07178;
}

.code-warning {
  text-decoration: wavy underline #ffcb6b;
}

.error-tooltip {
  position: absolute;
  background-color: rgba(26, 26, 46, 0.95);
  border: 1px solid #f07178;
  border-radius: 4px;
  padding: 0.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  max-width: 300px;
  font-size: 12px;
}

.warning-tooltip {
  position: absolute;
  background-color: rgba(26, 26, 46, 0.95);
  border: 1px solid #ffcb6b;
  border-radius: 4px;
  padding: 0.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  max-width: 300px;
  font-size: 12px;
}

/* Styles pour l'historique des versions */
.versions-list {
  max-height: 200px;
  overflow-y: auto;
  margin-bottom: 10px;
}

.version-item {
  padding: 8px 10px;
  border-bottom: 1px solid rgba(156, 137, 184, 0.2);
  cursor: pointer;
  transition: all 0.2s ease;
}

.version-item:hover {
  background-color: rgba(156, 137, 184, 0.1);
}

.version-item.active {
  background-color: rgba(156, 137, 184, 0.2);
  border-left: 3px solid var(--luna-secondary);
}

.version-item .version-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 3px;
}

.version-item .version-time {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
}

.version-item .version-message {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.version-item .version-size {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.5);
}