/* Styles pour le tableau de bord Luna */

/* Carte de statistiques */
.stat-card {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  padding: 1rem;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  background: rgba(0, 0, 0, 0.3);
}

.stat-icon {
  font-size: 2rem;
  margin-right: 1rem;
  color: var(--luna-secondary);
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(240, 166, 202, 0.1);
  border-radius: 10px;
}

.stat-info {
  flex-grow: 1;
}

.stat-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.3rem;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.progress {
  height: 6px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
  overflow: hidden;
}

.progress-bar {
  background: var(--luna-gradient);
  border-radius: 3px;
  transition: width 0.5s ease;
}

/* Conteneur de graphique */
.chart-container {
  position: relative;
  height: 300px;
  margin-top: 1rem;
}

/* Liste d'activités */
.activity-list {
  max-height: 300px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  padding: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-item:hover {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}

.activity-icon {
  font-size: 1.2rem;
  margin-right: 1rem;
  color: var(--luna-secondary);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(240, 166, 202, 0.1);
  border-radius: 8px;
  flex-shrink: 0;
}

.activity-content {
  flex-grow: 1;
}

.activity-title {
  font-weight: 600;
  margin-bottom: 0.3rem;
}

.activity-description {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.3rem;
}

.activity-time {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.5);
}

/* Grille d'accès rapide */
.quick-access-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  margin-top: 1rem;
}

.quick-access-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  padding: 1rem;
  text-decoration: none;
  color: var(--luna-light);
  transition: all 0.3s ease;
}

.quick-access-item:hover {
  transform: translateY(-5px);
  background: rgba(156, 137, 184, 0.2);
  color: white;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.quick-access-item i {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--luna-secondary);
}

.quick-access-item span {
  font-size: 0.8rem;
  text-align: center;
}

/* Liste de statut du système */
.system-status-list {
  margin-top: 1rem;
}

.system-status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.system-status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
}

.status-value {
  display: flex;
  align-items: center;
}

/* Informations sur le modèle */
.model-info {
  margin-top: 1rem;
}

.model-name {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  padding: 0.5rem 1rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  display: inline-block;
}

.model-details {
  margin-bottom: 1rem;
}

.model-detail {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.detail-label {
  color: rgba(255, 255, 255, 0.7);
}

.detail-value {
  font-weight: 500;
}

.model-actions {
  display: flex;
  justify-content: flex-end;
}

/* Animation de rotation pour l'icône d'actualisation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spin {
  animation: spin 1s linear infinite;
}

/* Styles pour la barre de défilement */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(156, 137, 184, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 137, 184, 0.7);
}

/* Styles responsifs */
@media (max-width: 768px) {
  .quick-access-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stat-card {
    margin-bottom: 1rem;
  }
  
  .chart-container {
    height: 200px;
  }
}
