/* Styles pour la page de connaissance du programme */

.program-knowledge-container {
  background-color: rgba(30, 30, 40, 0.7);
  border-radius: 10px;
  padding: 15px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Style pour l'affichage de la date et de l'heure */
.datetime-display {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  padding: 5px 10px;
  font-size: 0.9rem;
  color: var(--luna-accent);
  display: inline-block;
}

.nav-tabs {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-tabs .nav-link {
  color: rgba(255, 255, 255, 0.7);
  border: none;
  border-bottom: 2px solid transparent;
  background-color: transparent;
  transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
  color: rgba(255, 255, 255, 0.9);
  border-bottom: 2px solid rgba(255, 255, 255, 0.3);
  background-color: rgba(255, 255, 255, 0.05);
}

.nav-tabs .nav-link.active {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.1);
  border-bottom: 2px solid #9c89b8;
}

.card {
  background-color: rgba(30, 30, 40, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}

.card-header {
  background-color: rgba(20, 20, 30, 0.7);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.progress {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  overflow: hidden;
}

.progress-bar {
  background-image: linear-gradient(to right, #9c89b8, #f0a6ca);
  border-radius: 10px;
}



.btn-luna {
  background-image: linear-gradient(to right, #9c89b8, #f0a6ca);
  border: none;
  color: #fff;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-luna:hover {
  background-image: linear-gradient(to right, #8a77a6, #de94b8);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-luna:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Styles pour les listes */
.list-group {
  border-radius: 10px;
  overflow: hidden;
}

.list-group-item {
  background-color: rgba(30, 30, 40, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
}

.list-group-item:hover {
  background-color: rgba(40, 40, 50, 0.7);
}

/* Styles pour les badges */
.badge {
  font-weight: 500;
  padding: 5px 8px;
  border-radius: 4px;
}

.badge-primary {
  background-color: #9c89b8;
  color: #fff;
}

.badge-secondary {
  background-color: #f0a6ca;
  color: #fff;
}

.badge-success {
  background-color: #6a994e;
  color: #fff;
}

.badge-danger {
  background-color: #bc4749;
  color: #fff;
}

.badge-warning {
  background-color: #f2cc8f;
  color: #333;
}

.badge-info {
  background-color: #84a9c0;
  color: #fff;
}

/* Styles pour les toasts */
.toast {
  background-color: rgba(30, 30, 40, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.toast-header {
  background-color: rgba(20, 20, 30, 0.9);
  color: #fff;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.toast-body {
  color: rgba(255, 255, 255, 0.9);
}

/* Styles pour les tableaux */
.table {
  color: rgba(255, 255, 255, 0.9);
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 10px;
  overflow: hidden;
}

.table thead th {
  background-color: rgba(20, 20, 30, 0.7);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: #fff;
  font-weight: 500;
}

.table tbody tr {
  background-color: rgba(30, 30, 40, 0.7);
  transition: all 0.3s ease;
}

.table tbody tr:hover {
  background-color: rgba(40, 40, 50, 0.7);
}

.table td, .table th {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 12px 15px;
}

/* Styles pour les spinners */
.spinner-border {
  color: #9c89b8;
}

/* Styles pour les tooltips */
.tooltip-inner {
  background-color: rgba(20, 20, 30, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  padding: 8px 12px;
  max-width: 300px;
}

.bs-tooltip-auto[x-placement^=top] .arrow::before,
.bs-tooltip-top .arrow::before {
  border-top-color: rgba(20, 20, 30, 0.9);
}

.bs-tooltip-auto[x-placement^=bottom] .arrow::before,
.bs-tooltip-bottom .arrow::before {
  border-bottom-color: rgba(20, 20, 30, 0.9);
}

.bs-tooltip-auto[x-placement^=left] .arrow::before,
.bs-tooltip-left .arrow::before {
  border-left-color: rgba(20, 20, 30, 0.9);
}

.bs-tooltip-auto[x-placement^=right] .arrow::before,
.bs-tooltip-right .arrow::before {
  border-right-color: rgba(20, 20, 30, 0.9);
}
