/**
 * Styles pour la visualisation de la mémoire thermique
 */

.brain-visualization {
  height: 500px;
  position: relative;
  background-color: #1a1a1a;
  border-radius: 8px;
  overflow: hidden;
}

.brain-region {
  position: absolute;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  text-shadow: 0 0 3px rgba(0, 0, 0, 0.7);
}

.brain-region:hover {
  transform: scale(1.05);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.brain-connection {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.2);
  transform-origin: 0 0;
  z-index: 0;
}

.brain-region.instant {
  background-color: rgba(23, 162, 184, 0.7);
}

.brain-region.short-term {
  background-color: rgba(40, 167, 69, 0.7);
}

.brain-region.working {
  background-color: rgba(0, 123, 255, 0.7);
}

.brain-region.medium-term {
  background-color: rgba(255, 193, 7, 0.7);
}

.brain-region.long-term {
  background-color: rgba(220, 53, 69, 0.7);
}

.brain-region.dream {
  background-color: rgba(138, 43, 226, 0.7);
}

.brain-region.kyber {
  background-color: rgba(255, 255, 255, 0.7);
  color: #000;
}

.memory-item {
  background-color: #2a2a2a;
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 10px;
  border-left: 4px solid #007bff;
}

.memory-item.instant {
  border-left-color: #17a2b8;
}

.memory-item.short-term {
  border-left-color: #28a745;
}

.memory-item.working {
  border-left-color: #007bff;
}

.memory-item.medium-term {
  border-left-color: #ffc107;
}

.memory-item.long-term {
  border-left-color: #dc3545;
}

.memory-item.dream {
  border-left-color: #8a2be2;
}

.memory-item-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.memory-item-content {
  font-size: 0.9rem;
}

.memory-item-timestamp {
  font-size: 0.8rem;
  color: #aaa;
}

.memory-item-importance {
  font-size: 0.8rem;
  padding: 2px 6px;
  border-radius: 10px;
  background-color: #343a40;
}

/* Styles pour l'accélérateur Kyber */
.bg-purple {
  background-color: #8a2be2;
}

#kyber-controls .form-range::-webkit-slider-thumb {
  background: #8a2be2;
}

#kyber-controls .form-range::-moz-range-thumb {
  background: #8a2be2;
}

.kyber-pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(138, 43, 226, 0.7);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(138, 43, 226, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(138, 43, 226, 0);
  }
}

/* Animation pour les connexions neuronales */
.neural-activity {
  animation: neural-pulse 1.5s ease-in-out;
}

@keyframes neural-pulse {
  0% {
    opacity: 0.2;
    height: 100%;
  }
  50% {
    opacity: 0.8;
    height: 100%;
  }
  100% {
    opacity: 0.2;
    height: 100%;
  }
}
