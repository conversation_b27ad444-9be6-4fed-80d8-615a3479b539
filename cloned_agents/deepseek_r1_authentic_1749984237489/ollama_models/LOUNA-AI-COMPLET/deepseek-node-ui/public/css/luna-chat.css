/**
 * Styles pour l'interface de chat Luna
 * Améliore l'affichage des messages sous forme de bulles
 */

/* Conteneur de conversation */
#conversation-container {
  height: 65vh;
  overflow-y: auto;
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 137, 184, 0.5) rgba(0, 0, 0, 0.1);
}

#conversation-container::-webkit-scrollbar {
  width: 8px;
}

#conversation-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

#conversation-container::-webkit-scrollbar-thumb {
  background-color: rgba(156, 137, 184, 0.5);
  border-radius: 4px;
}

/* Messages */
.message {
  display: flex;
  margin-bottom: 1.5rem;
  position: relative;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Messages de l'utilisateur */
.user-message {
  justify-content: flex-end;
  padding-left: 15%;
}

.user-bubble {
  background: linear-gradient(135deg, rgba(156, 137, 184, 0.2), rgba(184, 190, 221, 0.2));
  color: var(--luna-light);
  border-radius: 18px 18px 3px 18px;
  margin-right: 10px;
  border-right: 3px solid rgba(156, 137, 184, 0.5);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  order: -1;
}

.user-avatar {
  background: linear-gradient(135deg, #9c89b8, #b8bedd);
  color: var(--luna-dark);
  font-size: 1.2rem;
}

/* Messages de l'agent */
.agent-message {
  justify-content: flex-start;
  padding-right: 15%;
}

.agent-bubble {
  background: linear-gradient(135deg, rgba(184, 190, 221, 0.2), rgba(240, 166, 202, 0.2));
  color: var(--luna-light);
  border-radius: 18px 18px 18px 3px;
  margin-left: 10px;
  border-left: 3px solid rgba(240, 166, 202, 0.5);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.agent-avatar {
  background: linear-gradient(135deg, #b8bedd, #f0a6ca);
  color: var(--luna-dark);
  font-size: 1.2rem;
}

/* Messages système */
.system-message {
  justify-content: center;
  padding-left: 10%;
  padding-right: 10%;
  margin-bottom: 1rem;
}

.system-bubble {
  background: rgba(0, 0, 0, 0.3);
  color: #aaa;
  font-style: italic;
  border-radius: 12px;
  margin-left: 10px;
  max-width: 90%;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.system-avatar {
  background: rgba(0, 0, 0, 0.3);
  color: #aaa;
  font-size: 1rem;
}

/* Messages de réflexion */
.thinking-message {
  justify-content: flex-start;
  padding-right: 15%;
  margin-bottom: 0.5rem;
}

.thinking-avatar {
  background: linear-gradient(135deg, #f0a6ca, #9c89b8);
  color: var(--luna-dark);
  font-size: 1rem;
}

.thinking-bubble {
  background: rgba(156, 137, 184, 0.1);
  color: var(--luna-light);
  border-radius: 18px 18px 18px 3px;
  margin-left: 10px;
  border-left: 3px solid #9c89b8;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.thinking-header {
  padding: 0.5rem 1rem;
  border-bottom: 1px solid rgba(156, 137, 184, 0.2);
}

.thinking-content {
  padding: 0.75rem 1rem;
}

.thinking-text {
  font-style: italic;
  color: #ccc;
}

/* Éléments communs */
.message-bubble {
  flex-grow: 1;
  max-width: 85%;
  word-wrap: break-word;
  position: relative;
}

.message-content {
  padding: 0.75rem 1rem;
  overflow-wrap: break-word;
}

.message-content p {
  margin-bottom: 0.5rem;
}

.message-content p:last-child {
  margin-bottom: 0;
}

.message-avatar {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.message-timestamp {
  font-size: 0.7rem;
  opacity: 0.7;
  text-align: right;
  margin-top: 0.2rem;
  padding-right: 0.5rem;
}

/* Bouton de copie */
.copy-button {
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: rgba(0, 0, 0, 0.3);
  color: rgba(255, 255, 255, 0.7);
  border: none;
  border-radius: 4px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s ease, background-color 0.2s ease;
}

.message-bubble:hover .copy-button {
  opacity: 1;
}

.copy-button:hover {
  background-color: rgba(0, 0, 0, 0.5);
  color: rgba(255, 255, 255, 0.9);
}

.copy-button.copied {
  background-color: rgba(40, 167, 69, 0.5);
  color: white;
}

/* Animation des points de suspension pour l'indication de frappe */
.typing-dots {
  display: flex;
  justify-content: center;
  padding: 0.5rem;
}

.typing-dots span {
  width: 8px;
  height: 8px;
  margin: 0 3px;
  background-color: var(--luna-light);
  border-radius: 50%;
  display: inline-block;
  animation: typing-dot 1.5s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: 0s; }
.typing-dots span:nth-child(2) { animation-delay: 0.3s; }
.typing-dots span:nth-child(3) { animation-delay: 0.6s; }

@keyframes typing-dot {
  0%, 60%, 100% { transform: translateY(0); }
  30% { transform: translateY(-5px); }
}

/* Zone de saisie */
.input-group {
  margin-top: 1rem;
}

#user-input {
  border-radius: 0;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--luna-accent);
  color: white;
}

#user-input:focus {
  box-shadow: 0 0 0 0.25rem rgba(240, 166, 202, 0.25);
}

/* Sélecteur de prompts */
#prompt-selector {
  margin-top: 1rem;
  display: none;
}

.quick-prompt-item button {
  text-align: left;
  transition: all 0.2s ease;
}

.quick-prompt-item button:hover {
  background-color: rgba(156, 137, 184, 0.2);
  transform: translateX(5px);
}

/* Styles pour les boutons de périphériques */
#toggleMicrophoneBtn.btn-danger,
#toggleVisionBtn.btn-danger,
#testSpeechBtn.btn-danger {
  background-color: #dc3545 !important;
  border-color: #dc3545 !important;
  color: white !important;
  box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
}

/* Styles pour le pied de page fixe */
.footer-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 40px;
  background-color: #1a1a2e;
  border-top: 1px solid rgba(184, 190, 221, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  color: #edf2fb;
}
