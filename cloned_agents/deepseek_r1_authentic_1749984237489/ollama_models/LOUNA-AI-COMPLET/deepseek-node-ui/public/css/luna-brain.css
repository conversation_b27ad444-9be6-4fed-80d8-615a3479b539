/**
 * Styles pour l'interface de visualisation du cerveau Luna
 */

/* Conteneur principal de visualisation */
#brain-container {
  position: relative;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

.brain-view {
  width: 100%;
  height: 100%;
  display: none;
}

.brain-view.active {
  display: block;
}

/* Styles pour la vue 2D */
.brain-zones {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 15px;
  height: 100%;
  padding: 10px;
}

.brain-zone {
  position: relative;
  border-radius: 10px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.brain-zone:hover {
  transform: scale(1.02);
  box-shadow: 0 0 15px rgba(156, 137, 184, 0.3);
}

.zone-label {
  text-align: center;
  font-weight: 500;
  margin-bottom: 10px;
  z-index: 2;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.zone-nodes {
  flex-grow: 1;
  position: relative;
  overflow: hidden;
}

/* Couleurs spécifiques pour chaque zone */
.zone-1 {
  background: linear-gradient(135deg, rgba(240, 166, 202, 0.2), rgba(156, 137, 184, 0.2));
  border: 1px solid rgba(240, 166, 202, 0.3);
}

.zone-2 {
  background: linear-gradient(135deg, rgba(240, 166, 202, 0.15), rgba(156, 137, 184, 0.15));
  border: 1px solid rgba(240, 166, 202, 0.25);
}

.zone-3 {
  background: linear-gradient(135deg, rgba(184, 190, 221, 0.15), rgba(156, 137, 184, 0.15));
  border: 1px solid rgba(184, 190, 221, 0.25);
}

.zone-4 {
  background: linear-gradient(135deg, rgba(184, 190, 221, 0.1), rgba(156, 137, 184, 0.1));
  border: 1px solid rgba(184, 190, 221, 0.2);
}

.zone-5 {
  background: linear-gradient(135deg, rgba(156, 137, 184, 0.1), rgba(184, 190, 221, 0.1));
  border: 1px solid rgba(156, 137, 184, 0.2);
}

.zone-6 {
  background: linear-gradient(135deg, rgba(156, 137, 184, 0.05), rgba(184, 190, 221, 0.05));
  border: 1px solid rgba(156, 137, 184, 0.15);
}

/* Nœuds de mémoire */
.memory-node {
  position: absolute;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 1;
}

.memory-node:hover {
  transform: scale(1.5);
  z-index: 10;
}

.memory-node::after {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border-radius: 50%;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
  opacity: 0;
  transition: all 0.2s ease;
}

.memory-node:hover::after {
  opacity: 1;
}

/* Couleurs des nœuds par zone */
.memory-node.zone-1-node {
  background: rgba(240, 166, 202, 0.9);
  box-shadow: 0 0 8px rgba(240, 166, 202, 0.7);
}

.memory-node.zone-2-node {
  background: rgba(240, 166, 202, 0.7);
  box-shadow: 0 0 6px rgba(240, 166, 202, 0.5);
}

.memory-node.zone-3-node {
  background: rgba(184, 190, 221, 0.7);
  box-shadow: 0 0 6px rgba(184, 190, 221, 0.5);
}

.memory-node.zone-4-node {
  background: rgba(184, 190, 221, 0.6);
  box-shadow: 0 0 5px rgba(184, 190, 221, 0.4);
}

.memory-node.zone-5-node {
  background: rgba(156, 137, 184, 0.6);
  box-shadow: 0 0 5px rgba(156, 137, 184, 0.4);
}

.memory-node.zone-6-node {
  background: rgba(156, 137, 184, 0.5);
  box-shadow: 0 0 4px rgba(156, 137, 184, 0.3);
}

/* Connexions entre les nœuds */
.memory-connection {
  position: absolute;
  height: 1px;
  background: linear-gradient(to right, rgba(240, 166, 202, 0.5), rgba(156, 137, 184, 0.5));
  transform-origin: 0 0;
  z-index: 0;
  opacity: 0.3;
  pointer-events: none;
}

.memory-connection.active {
  opacity: 0.8;
  height: 2px;
  box-shadow: 0 0 5px rgba(240, 166, 202, 0.5);
}

/* Animation de pulsation pour les nœuds actifs */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.3); }
  100% { transform: scale(1); }
}

.memory-node.active {
  animation: pulse 2s infinite;
  z-index: 10;
}

/* Légende des zones */
.memory-zones-legend {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-top: 10px;
  font-size: 0.8rem;
}

.zone-legend {
  padding: 4px 8px;
  border-radius: 4px;
  text-align: center;
}

.zone-1-legend {
  background: rgba(240, 166, 202, 0.2);
  border: 1px solid rgba(240, 166, 202, 0.3);
}

.zone-2-legend {
  background: rgba(240, 166, 202, 0.15);
  border: 1px solid rgba(240, 166, 202, 0.25);
}

.zone-3-legend {
  background: rgba(184, 190, 221, 0.15);
  border: 1px solid rgba(184, 190, 221, 0.25);
}

.zone-4-legend {
  background: rgba(184, 190, 221, 0.1);
  border: 1px solid rgba(184, 190, 221, 0.2);
}

.zone-5-legend {
  background: rgba(156, 137, 184, 0.1);
  border: 1px solid rgba(156, 137, 184, 0.2);
}

.zone-6-legend {
  background: rgba(156, 137, 184, 0.05);
  border: 1px solid rgba(156, 137, 184, 0.15);
}

/* Styles pour la modal de détail de mémoire */
.memory-detail-header {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 15px;
}

.memory-connections-container {
  max-height: 200px;
  overflow-y: auto;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 10px;
}

.memory-connection-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid rgba(156, 137, 184, 0.1);
}

.memory-connection-item:last-child {
  border-bottom: none;
}

/* Animation de rotation pour les icônes de chargement */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spin {
  animation: spin 1s linear infinite;
}
