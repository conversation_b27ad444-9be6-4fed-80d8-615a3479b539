/**
 * Luna Reflection Enhanced - Styles pour l'interface de réflexion améliorée
 */

/* Conteneur principal avec hauteur limitée */
.reflection-container {
  height: calc(100vh - 150px);
  max-height: calc(100vh - 150px);
  overflow-y: auto;
  padding-bottom: 20px;
}

/* Carte de réflexion */
.reflection-card {
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  background-color: rgba(0, 0, 0, 0.2);
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.reflection-card:hover {
  box-shadow: 0 0 15px rgba(156, 137, 184, 0.3);
}

/* Contenu de la réflexion */
.reflection-content {
  padding: 15px;
  border-radius: 8px;
  background-color: rgba(156, 137, 184, 0.05);
  border-left: 3px solid var(--luna-accent);
}

/* Détails de la réflexion */
.reflection-details {
  padding: 15px;
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.1);
  margin-top: 15px;
  font-size: 0.9rem;
}

/* Historique de réflexion */
.reflection-history-container {
  max-height: 500px;
  overflow-y: auto;
}

/* Options de style d'affichage */
.display-style-options {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.display-style-option {
  padding: 8px 15px;
  border-radius: 5px;
  background-color: rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: all 0.2s ease;
}

.display-style-option:hover {
  background-color: rgba(156, 137, 184, 0.2);
}

.display-style-option.active {
  background-color: var(--luna-accent);
  color: white;
}

/* Formulaire de réflexion */
.reflection-form-container {
  margin-bottom: 20px;
}

.reflection-input {
  background-color: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--luna-light);
  border-radius: 8px;
  padding: 10px;
  resize: none;
}

.reflection-input:focus {
  background-color: rgba(0, 0, 0, 0.3);
  border-color: var(--luna-accent);
  box-shadow: 0 0 0 0.25rem rgba(156, 137, 184, 0.25);
}

/* Paramètres de réflexion */
.reflection-settings {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 20px;
}

.settings-section {
  margin-bottom: 15px;
}

.settings-section:last-child {
  margin-bottom: 0;
}

/* Interrupteurs */
.form-switch .form-check-input {
  width: 3em;
  height: 1.5em;
}

.form-switch .form-check-input:checked {
  background-color: var(--luna-accent);
  border-color: var(--luna-accent);
}

/* Indicateurs de statut */
.status-indicator {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 5px;
}

.status-indicator.active {
  background-color: #28a745;
  box-shadow: 0 0 5px #28a745;
}

.status-indicator.inactive {
  background-color: #dc3545;
  box-shadow: 0 0 5px #dc3545;
}

/* Barre de progression */
.progress {
  background-color: rgba(0, 0, 0, 0.2);
}

.progress-bar {
  background-color: var(--luna-accent);
}

/* Animations */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(156, 137, 184, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(156, 137, 184, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(156, 137, 184, 0);
  }
}

.pulse {
  animation: pulse 2s infinite;
}

/* Métriques de réflexion */
.reflection-metrics {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  font-size: 0.8rem;
  color: var(--luna-light-muted);
}

/* Date et heure */
.datetime-display {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: var(--luna-accent);
  padding: 5px 10px;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 5px;
  display: inline-block;
}

/* Toast */
.toast-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1050;
}

.toast {
  background-color: rgba(33, 37, 41, 0.9);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.toast-header {
  background-color: rgba(33, 37, 41, 0.95);
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive */
@media (max-width: 768px) {
  .reflection-container {
    height: auto;
    max-height: none;
  }
  
  .display-style-options {
    flex-direction: column;
    gap: 5px;
  }
}
