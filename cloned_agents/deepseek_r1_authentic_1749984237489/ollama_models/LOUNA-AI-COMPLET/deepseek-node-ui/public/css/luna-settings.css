/* Styles pour la page de paramètres Luna */

:root {
  --luna-primary: #9c89b8;
  --luna-secondary: #f0a6ca;
  --luna-accent: #b8bedd;
  --luna-dark: #1a1a2e;
  --luna-light: #edf2fb;
  --luna-gradient: linear-gradient(135deg, #9c89b8, #f0a6ca);
}

/* Styles pour les onglets */
.nav-tabs {
  border-bottom: 1px solid rgba(184, 190, 221, 0.2);
}

.nav-tabs .nav-link {
  color: rgba(255, 255, 255, 0.7);
  border: none;
  border-bottom: 2px solid transparent;
  background-color: transparent;
  padding: 0.75rem 1rem;
  margin-right: 0.5rem;
  transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
  color: white;
  border-bottom-color: rgba(240, 166, 202, 0.5);
  background-color: rgba(156, 137, 184, 0.1);
}

.nav-tabs .nav-link.active {
  color: white;
  border-bottom-color: var(--luna-secondary);
  background-color: rgba(156, 137, 184, 0.2);
}

/* Styles pour les formulaires */
.form-control, .form-select {
  background-color: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(184, 190, 221, 0.3);
  color: var(--luna-light);
  transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
  background-color: rgba(0, 0, 0, 0.4);
  border-color: var(--luna-secondary);
  box-shadow: 0 0 0 0.25rem rgba(240, 166, 202, 0.25);
  color: white;
}

.form-text {
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.form-label {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

/* Styles pour les interrupteurs */
.form-check-input {
  background-color: rgba(0, 0, 0, 0.3);
  border-color: rgba(184, 190, 221, 0.5);
  width: 2.5rem;
  height: 1.25rem;
  margin-top: 0.25rem;
}

.form-check-input:checked {
  background-color: var(--luna-secondary);
  border-color: var(--luna-secondary);
}

.form-check-input:focus {
  border-color: var(--luna-secondary);
  box-shadow: 0 0 0 0.25rem rgba(240, 166, 202, 0.25);
}

.form-check-label {
  font-weight: 500;
}

/* Styles pour les curseurs */
.form-range {
  height: 0.5rem;
}

.form-range::-webkit-slider-thumb {
  background: var(--luna-secondary);
}

.form-range::-moz-range-thumb {
  background: var(--luna-secondary);
}

.form-range::-webkit-slider-runnable-track {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 0.25rem;
}

.form-range::-moz-range-track {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 0.25rem;
}

/* Styles pour les sections */
h4 {
  color: var(--luna-secondary);
  font-weight: 600;
  margin-bottom: 1.5rem;
}

h5 {
  color: var(--luna-accent);
  font-weight: 500;
  font-size: 1.1rem;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(184, 190, 221, 0.2);
}

/* Styles pour le journal des modifications */
.settings-log {
  max-height: 200px;
  overflow-y: auto;
  font-size: 0.85rem;
}

.log-entry {
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(184, 190, 221, 0.1);
  display: flex;
  flex-direction: column;
}

.log-entry:last-child {
  border-bottom: none;
}

.log-time {
  color: var(--luna-accent);
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
}

.log-message {
  color: rgba(255, 255, 255, 0.8);
}

/* Styles pour les boutons */
.btn-luna {
  background: var(--luna-gradient);
  border: none;
  color: white;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-luna:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-luna-outline {
  background: transparent;
  border: 1px solid var(--luna-accent);
  color: var(--luna-accent);
}

.btn-luna-outline:hover {
  background: rgba(184, 190, 221, 0.1);
  color: white;
}

/* Styles pour les alertes */
.alert {
  background-color: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  color: rgba(255, 255, 255, 0.9);
}

/* Animation de transition entre les onglets */
.tab-pane {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Styles pour les informations système */
#system-version, #system-model, #system-memory, #system-accelerators, #system-last-update {
  font-weight: 600;
  color: var(--luna-secondary);
}

/* Styles pour les notifications */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: rgba(26, 26, 46, 0.9);
  border-left: 4px solid var(--luna-primary);
  color: white;
  padding: 15px;
  border-radius: 5px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  z-index: 9999;
  transform: translateX(120%);
  transition: transform 0.3s ease;
  max-width: 300px;
}

.notification.show {
  transform: translateX(0);
}

.notification-success {
  border-left-color: #4caf50;
}

.notification-error {
  border-left-color: #f44336;
}

.notification-info {
  border-left-color: var(--luna-accent);
}

.notification-warning {
  border-left-color: #ff9800;
}
