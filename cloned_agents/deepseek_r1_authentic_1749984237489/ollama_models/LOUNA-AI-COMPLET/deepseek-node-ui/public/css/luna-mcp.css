/**
 * Styles pour l'interface MCP (Master Control Program)
 */

/* Terminal MCP */
#mcp-terminal {
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  height: 300px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  color: #0f0;
  text-shadow: 0 0 5px #0f0;
  padding: 1rem;
  border: 1px solid #333;
  cursor: text;
}

.mcp-line {
  margin-bottom: 0.5rem;
  white-space: pre-wrap;
  word-break: break-word;
}

.mcp-error {
  color: #f55;
  text-shadow: 0 0 5px #f55;
}

.mcp-success {
  color: #5f5;
  text-shadow: 0 0 5px #5f5;
}

.mcp-command {
  color: #5ff;
  text-shadow: 0 0 5px #5ff;
  font-weight: bold;
}

/* Journal d'activité */
#mcp-activity-log {
  max-height: 200px;
  overflow-y: auto;
  font-size: 0.8rem;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 0.5rem;
}

.log-entry {
  margin-bottom: 0.3rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 0.3rem;
}

.log-time {
  color: #9c89b8;
  font-weight: bold;
  margin-right: 0.5rem;
}

/* Barres de progression */
.progress {
  background-color: rgba(0, 0, 0, 0.3);
  height: 8px;
  margin-bottom: 1rem;
}

.progress-bar {
  background: linear-gradient(to right, #9c89b8, #f0a6ca);
}

/* Commandes rapides */
.mcp-command-btn {
  text-align: left;
  transition: all 0.2s ease;
}

.mcp-command-btn:hover {
  background-color: rgba(156, 137, 184, 0.2);
  transform: translateX(5px);
}

/* Switches */
.form-check-input:checked {
  background-color: #9c89b8;
  border-color: #9c89b8;
}

.form-check-input:focus {
  border-color: #f0a6ca;
  box-shadow: 0 0 0 0.25rem rgba(156, 137, 184, 0.25);
}

/* Animation de clignotement pour le prompt */
@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

#mcp-prompt::after {
  content: '█';
  animation: blink 1s infinite;
}

/* Styles pour le pied de page fixe */
.footer-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 40px;
  background-color: #1a1a2e;
  border-top: 1px solid rgba(184, 190, 221, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  color: #edf2fb;
}
