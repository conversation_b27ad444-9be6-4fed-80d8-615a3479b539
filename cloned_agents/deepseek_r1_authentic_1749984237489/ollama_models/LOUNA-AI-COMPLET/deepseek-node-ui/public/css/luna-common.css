/* Styles communs pour toutes les pages Luna */

:root {
  --luna-primary: #9c89b8;
  --luna-secondary: #f0a6ca;
  --luna-accent: #b8bedd;
  --luna-dark: #1a1a2e;
  --luna-light: #edf2fb;
  --luna-gradient: linear-gradient(135deg, #9c89b8, #f0a6ca);
}

/* Transitions fluides entre les zones */
.zone-transition {
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  animation-fill-mode: both;
}

/* Animation de feuilles qui tombent */
@keyframes leafFall {
  0% {
    opacity: 0;
    transform: translateY(-20px) rotate(-5deg);
  }
  50% {
    opacity: 0.8;
    transform: translateY(5px) rotate(2deg);
  }
  100% {
    opacity: 1;
    transform: translateY(0) rotate(0);
  }
}

.leaf-fall {
  animation: leafFall 0.8s ease-out forwards;
  animation-delay: calc(var(--item-index, 0) * 0.1s);
}

/* Animation d'eau qui coule */
@keyframes waterFlow {
  0% {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
    filter: blur(2px);
  }
  50% {
    opacity: 0.7;
    transform: translateY(-5px) scale(0.98);
    filter: blur(1px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

.water-flow {
  animation: waterFlow 0.6s ease-out forwards;
  animation-delay: calc(var(--item-index, 0) * 0.08s);
}

/* Animation de fondu enchaîné */
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.5s ease-out forwards;
  animation-delay: calc(var(--item-index, 0) * 0.05s);
}

/* Styles pour les bulles de conversation */
.message {
  position: relative;
  margin-bottom: 1rem;
  padding: 1rem;
  border-radius: 15px;
  max-width: 85%;
  transition: all 0.3s ease;
  animation: fadeInUp 0.5s ease-out forwards;
}

.user-message {
  background-color: rgba(156, 137, 184, 0.2);
  border: 1px solid rgba(156, 137, 184, 0.3);
  margin-left: auto;
  border-top-right-radius: 5px;
}

.agent-message {
  background-color: rgba(240, 166, 202, 0.2);
  border: 1px solid rgba(240, 166, 202, 0.3);
  margin-right: auto;
  border-top-left-radius: 5px;
}

.system-message {
  background-color: rgba(184, 190, 221, 0.2);
  border: 1px solid rgba(184, 190, 221, 0.3);
  margin: 0 auto;
  max-width: 90%;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
}

/* Styles pour les avatars dans les messages */
.message-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  flex-shrink: 0;
}

.user-message .message-avatar {
  background: linear-gradient(135deg, #9c89b8, #7c6a99);
}

.agent-message .message-avatar {
  background: linear-gradient(135deg, #f0a6ca, #d08aab);
}

.system-message .message-avatar {
  background: linear-gradient(135deg, #b8bedd, #9a9fbf);
}

/* Styles pour les notifications */
.notification {
  background: rgba(26, 26, 46, 0.9);
  border-left: 4px solid var(--luna-primary);
  color: var(--luna-light);
  padding: 15px;
  margin-bottom: 10px;
  border-radius: 5px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  transform: translateX(120%);
  transition: transform 0.3s ease;
}

.notification.show {
  transform: translateX(0);
}

.notification-success {
  border-left-color: #4caf50;
}

.notification-error {
  border-left-color: #f44336;
}

.notification-info {
  border-left-color: var(--luna-accent);
}

.notification-warning {
  border-left-color: #ff9800;
}

/* Animation de pulsation pour les indicateurs actifs */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(76, 175, 80, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
  }
}

.pulse {
  animation: pulse 2s infinite;
}

/* Styles pour les boutons */
.btn-luna {
  background: var(--luna-gradient);
  border: none;
  border-radius: 25px;
  font-weight: 600;
  padding: 0.5rem 1.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.btn-luna:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0,0,0,0.2);
}

.btn-luna-outline {
  background: transparent;
  border: 2px solid var(--luna-accent);
  color: var(--luna-accent);
}

.btn-luna-outline:hover {
  background: rgba(184, 190, 221, 0.1);
}

/* Animation de rotation pour les icônes de chargement */
.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Styles pour les cartes */
.luna-card {
  background: rgba(26, 26, 46, 0.7);
  border: 1px solid rgba(184, 190, 221, 0.2);
  border-radius: 15px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 10px 25px rgba(0,0,0,0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.luna-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0,0,0,0.25);
  border-color: var(--luna-secondary);
}

/* Styles pour les indicateurs de statut */
.status-indicator {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
  transition: all 0.5s ease;
}

.status-active {
  background-color: #4caf50;
  box-shadow: 0 0 10px #4caf50;
  animation: pulse 2s infinite;
}

.status-inactive {
  background-color: #f44336;
}

/* Styles pour les barres de progression */
.progress {
  background-color: rgba(0, 0, 0, 0.2);
  height: 8px;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.progress-bar {
  transition: width 1s ease;
}
