/* Styles pour corriger les problèmes d'affichage de l'interface cognitive */

/* Limiter la hauteur du conteneur principal */
.cognitive-container {
  max-height: calc(100vh - 150px);
  overflow-y: auto;
  padding-bottom: 60px;
  position: relative;
  margin-bottom: 40px;
}

/* Assurer que les cartes ont une hauteur fixe */
.card {
  margin-bottom: 20px;
  height: auto !important;
  overflow: hidden;
}

/* Empêcher le débordement des conteneurs */
.container-fluid {
  overflow-x: hidden;
}

/* Assurer que les graphiques restent dans leurs conteneurs */
canvas {
  max-width: 100%;
  height: auto !important;
}

/* Styles pour les éléments de visualisation */
.neural-activity-container {
  height: 200px;
  max-height: 200px;
  overflow: hidden;
}

.video-container, 
.audio-container,
.video-integration-container {
  max-height: 250px;
  overflow: hidden;
}

/* Assurer que les résultats d'analyse restent dans leurs conteneurs */
#videoProcessingResults,
#audioProcessingResults,
#textProcessingResults,
#injectionResults,
#videoIntegrationResults {
  max-height: 200px;
  overflow-y: auto;
}

/* Styles pour les formulaires */
textarea {
  max-height: 120px;
}

/* Styles pour les éléments de résultat */
.result-item {
  margin-bottom: 8px;
  display: flex;
  justify-content: space-between;
}

.result-label {
  font-weight: 500;
  color: #555;
}

.result-value {
  font-weight: 400;
}

.numeric-value {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #0d6efd;
}

/* Styles pour les graphiques */
#neuralActivityChart,
#audioWaveform {
  width: 100% !important;
  max-height: 180px !important;
}

/* Styles pour les placeholders */
#videoPlaceholder,
#audioPlaceholder,
#videoIntegrationPlaceholder {
  height: 240px;
  max-height: 240px;
  overflow: hidden;
}

/* Styles pour les boutons */
.btn {
  white-space: nowrap;
}

/* Styles pour les alertes */
.alert {
  margin-bottom: 10px;
  padding: 8px 12px;
}

/* Styles pour les badges */
.badge {
  font-size: 0.8rem;
  padding: 5px 8px;
}

/* Styles pour les progress bars */
.progress {
  height: 8px;
  margin-bottom: 10px;
}

/* Styles pour les sélecteurs */
.form-select {
  height: 38px;
}

/* Styles pour les ranges */
.form-range {
  padding: 0;
}

/* Styles pour les cartes d'information */
.card-body {
  padding: 15px;
}

.card-header {
  padding: 10px 15px;
}

/* Styles pour les colonnes */
.col-md-6 {
  margin-bottom: 20px;
}

/* Styles pour les rangées */
.row {
  margin-bottom: 20px;
}

/* Styles pour le pied de page fixe */
.footer-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 40px;
  background-color: #1a1a2e;
  border-top: 1px solid #2d2d4a;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  color: #edf2fb;
}

/* Assurer que le dernier élément a une marge suffisante pour le défilement */
.row:last-child {
  margin-bottom: 60px;
}

/* Styles pour les gradients */
.bg-gradient-primary {
  background: linear-gradient(135deg, #0d6efd, #0a58ca);
}

.bg-gradient-info {
  background: linear-gradient(135deg, #0dcaf0, #0aa2c0);
}

.bg-gradient-success {
  background: linear-gradient(135deg, #198754, #146c43);
}

.bg-gradient-warning {
  background: linear-gradient(135deg, #ffc107, #cc9a06);
}

.bg-gradient-danger {
  background: linear-gradient(135deg, #dc3545, #b02a37);
}

/* Styles pour les conteneurs de résultats */
.bg-light {
  background-color: rgba(255, 255, 255, 0.1) !important;
  color: #edf2fb;
}

/* Styles pour les textes */
.text-muted {
  color: rgba(255, 255, 255, 0.6) !important;
}

/* Styles pour les icônes */
.bi {
  margin-right: 5px;
}

/* Styles pour les inputs */
.form-control {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #edf2fb;
}

.form-control:focus {
  background-color: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  color: #edf2fb;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-control::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

/* Styles pour les labels */
.form-label {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

/* Styles pour les sélecteurs */
.form-select {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #edf2fb;
}

.form-select:focus {
  background-color: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  color: #edf2fb;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Styles pour les ranges */
.form-range::-webkit-slider-thumb {
  background-color: #0d6efd;
}

.form-range::-moz-range-thumb {
  background-color: #0d6efd;
}

.form-range::-webkit-slider-runnable-track {
  background-color: rgba(255, 255, 255, 0.2);
}

.form-range::-moz-range-track {
  background-color: rgba(255, 255, 255, 0.2);
}
