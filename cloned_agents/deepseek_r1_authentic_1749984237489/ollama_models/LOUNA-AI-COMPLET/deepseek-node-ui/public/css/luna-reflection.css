/**
 * Styles pour l'interface de réflexion Luna
 */

/* Zone de réflexion */
#reflection-area {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  padding: 1.5rem;
}

.reflection-header {
  margin-bottom: 2rem;
  border-bottom: 1px solid rgba(156, 137, 184, 0.3);
  padding-bottom: 1rem;
}

.reflection-examples {
  margin-top: 2rem;
}

.reflection-example {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  margin-bottom: 1.5rem;
  overflow: hidden;
  border: 1px solid rgba(156, 137, 184, 0.2);
}

.reflection-example-header {
  background-color: rgba(156, 137, 184, 0.1);
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  border-bottom: 1px solid rgba(156, 137, 184, 0.2);
}

.reflection-example-content {
  padding: 1rem;
}

.reflection-thinking {
  background-color: rgba(156, 137, 184, 0.1);
  border-left: 3px solid #9c89b8;
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 0 10px 10px 0;
}

.reflection-response {
  background-color: rgba(240, 166, 202, 0.1);
  border-left: 3px solid #f0a6ca;
  padding: 1rem;
  border-radius: 0 10px 10px 0;
}

/* Accélérateurs */
.progress {
  background-color: rgba(0, 0, 0, 0.3);
  height: 8px;
  margin-bottom: 1rem;
}

.progress-bar {
  background: linear-gradient(to right, #9c89b8, #f0a6ca);
}

/* Animation pour les accélérateurs */
@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spin {
  animation: spin 1s linear infinite;
}

#optimize-accelerators-btn:hover .bi-lightning-charge {
  animation: pulse 1s infinite;
}

/* Styles pour les badges */
.badge {
  padding: 0.5em 0.75em;
  font-weight: 500;
  border-radius: 6px;
}

.badge.bg-primary {
  background: linear-gradient(135deg, #9c89b8, #b8bedd) !important;
}

/* Styles pour les toggles */
.form-check-input:checked {
  background-color: #9c89b8;
  border-color: #9c89b8;
}

.form-check-input:focus {
  border-color: #f0a6ca;
  box-shadow: 0 0 0 0.25rem rgba(156, 137, 184, 0.25);
}

/* Styles pour les boutons */
.btn-luna:hover {
  background: linear-gradient(135deg, #9c89b8, #f0a6ca);
  border-color: transparent;
}

/* Styles pour les messages de réflexion dans le chat */
.thinking-message {
  justify-content: flex-start;
  padding-right: 15%;
  margin-bottom: 0.5rem;
}

.thinking-avatar {
  background: linear-gradient(135deg, #f0a6ca, #9c89b8);
  color: var(--luna-dark);
  font-size: 1rem;
}

.thinking-bubble {
  background: rgba(156, 137, 184, 0.1);
  color: var(--luna-light);
  border-radius: 18px 18px 18px 3px;
  margin-left: 10px;
  border-left: 3px solid #9c89b8;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.thinking-header {
  padding: 0.5rem 1rem;
  border-bottom: 1px solid rgba(156, 137, 184, 0.2);
}

.thinking-content {
  padding: 0.75rem 1rem;
}

.thinking-text {
  font-style: italic;
  color: #ccc;
}
