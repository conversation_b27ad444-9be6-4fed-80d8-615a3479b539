/**
 * Luna MCP - Master Control Program
 * Permet de contrôler les fonctionnalités avancées du système
 */

/**
 * Met à jour la date et l'heure dans l'interface
 */
function updateDateTime() {
  const now = new Date();

  // Mettre à jour l'élément current-datetime
  const datetimeElement = document.getElementById('current-datetime');
  if (datetimeElement) {
    datetimeElement.textContent = now.toLocaleString('fr-FR');
  }
}

// Initialiser la connexion Socket.IO
const socket = io();

// État global du MCP
const mcpState = {
  active: false,
  internetAccess: true,
  securityLevel: 'medium',
  cpuUsage: 0,
  memoryUsage: 0,
  networkUsage: 0,
  temperature: 0,
  accelerators: {
    memory: { count: 3, efficiency: 0.6 },
    thermal: { count: 3, efficiency: 0.7 },
    reflection: { count: 3, efficiency: 0.9 }
  },
  lastCommand: '',
  commandHistory: []
};

// Événements Socket.IO
socket.on('connect', () => {
  console.log('🔌 Connecté au serveur Luna');
  addTerminalLine('Connexion au serveur établie');
  loadMcpStatus();
});

socket.on('disconnect', () => {
  console.log('🔌 Déconnecté du serveur Luna');
  addTerminalLine('Connexion au serveur perdue. Tentative de reconnexion...', 'error');
});

// Recevoir les mises à jour du statut MCP
socket.on('mcp status', (data) => {
  if (data.success) {
    mcpState.active = data.active;
    mcpState.internetAccess = data.internetAccess;
    mcpState.securityLevel = data.securityLevel;

    // Mettre à jour l'interface
    updateMcpUI();

    addTerminalLine(`Statut MCP mis à jour: ${mcpState.active ? 'Actif' : 'Inactif'}`);
  } else {
    addTerminalLine('Erreur lors de la récupération du statut MCP', 'error');
  }
});

// Recevoir les mises à jour des métriques système
socket.on('system metrics', (data) => {
  if (data.success) {
    mcpState.cpuUsage = data.cpuUsage;
    mcpState.memoryUsage = data.memoryUsage;
    mcpState.networkUsage = data.networkUsage;
    mcpState.temperature = data.temperature;

    // Mettre à jour l'interface
    updateMetricsUI();
  }
});

// Recevoir les mises à jour des accélérateurs
socket.on('accelerators status', (data) => {
  if (data.success) {
    mcpState.accelerators = data.accelerators;

    // Mettre à jour l'interface
    updateAcceleratorsUI();
  }
});

// Recevoir les réponses aux commandes MCP
socket.on('mcp command response', (data) => {
  if (data.success) {
    addTerminalLine(data.response);
  } else {
    addTerminalLine(`Erreur: ${data.error}`, 'error');
  }
});

// Fonctions principales

// Charger le statut MCP depuis le serveur
function loadMcpStatus() {
  socket.emit('get mcp status');

  // Demander également les métriques système
  socket.emit('get system metrics');

  // Et le statut des accélérateurs
  socket.emit('get accelerators status');
}

// Mettre à jour l'interface MCP
function updateMcpUI() {
  // Mettre à jour le bouton de statut MCP
  const mcpStatusBtn = $('#mcp-status-btn');
  const mcpStatusText = $('#mcp-status-text');

  if (mcpState.active) {
    mcpStatusBtn.removeClass('btn-luna').addClass('btn-danger');
    mcpStatusText.text('Désactiver MCP');
  } else {
    mcpStatusBtn.removeClass('btn-danger').addClass('btn-luna');
    mcpStatusText.text('Activer MCP');
  }

  // Mettre à jour les toggles
  $('#mcp-mode-toggle').prop('checked', mcpState.active);
  $('#mcp-mode-status').text(mcpState.active ? 'Activé' : 'Désactivé');

  $('#internet-access-toggle').prop('checked', mcpState.internetAccess);
  $('#internet-access-status').text(mcpState.internetAccess ? 'Activé' : 'Désactivé');

  $('#security-level').val(mcpState.securityLevel);
}

// Mettre à jour l'interface des métriques
function updateMetricsUI() {
  // CPU
  $('#mcp-cpu-usage').text(`${mcpState.cpuUsage}%`);
  $('#mcp-cpu-bar').css('width', `${mcpState.cpuUsage}%`).attr('aria-valuenow', mcpState.cpuUsage);

  // Mémoire
  $('#mcp-memory-usage').text(`${mcpState.memoryUsage} MB`);
  const memoryPercentage = Math.min(100, (mcpState.memoryUsage / 8192) * 100);
  $('#mcp-memory-bar').css('width', `${memoryPercentage}%`).attr('aria-valuenow', memoryPercentage);

  // Réseau
  $('#mcp-network-usage').text(`${mcpState.networkUsage} Mbps`);
  const networkPercentage = Math.min(100, (mcpState.networkUsage / 100) * 100);
  $('#mcp-network-bar').css('width', `${networkPercentage}%`).attr('aria-valuenow', networkPercentage);

  // Température
  $('#mcp-temp-value').text(`${mcpState.temperature}°C`);
  const tempPercentage = Math.min(100, (mcpState.temperature / 100) * 100);
  $('#mcp-temp-bar').css('width', `${tempPercentage}%`).attr('aria-valuenow', tempPercentage);
}

// Mettre à jour l'interface des accélérateurs
function updateAcceleratorsUI() {
  // Mémoire
  $('#acc-memory-count').text(mcpState.accelerators.memory.count);
  const memoryEfficiency = mcpState.accelerators.memory.efficiency * 100;
  $('#acc-memory-bar').css('width', `${memoryEfficiency}%`).attr('aria-valuenow', memoryEfficiency);

  // Thermique
  $('#acc-thermal-count').text(mcpState.accelerators.thermal.count);
  const thermalEfficiency = mcpState.accelerators.thermal.efficiency * 100;
  $('#acc-thermal-bar').css('width', `${thermalEfficiency}%`).attr('aria-valuenow', thermalEfficiency);

  // Réflexion
  $('#acc-reflection-count').text(mcpState.accelerators.reflection.count);
  const reflectionEfficiency = mcpState.accelerators.reflection.efficiency * 100;
  $('#acc-reflection-bar').css('width', `${reflectionEfficiency}%`).attr('aria-valuenow', reflectionEfficiency);
}

// Ajouter une ligne au terminal MCP
function addTerminalLine(text, type = 'info') {
  const terminal = $('#mcp-terminal');
  const prompt = $('#mcp-prompt');

  // Créer une nouvelle ligne
  const line = $('<div class="mcp-line"></div>');

  // Ajouter une classe en fonction du type
  if (type === 'error') {
    line.addClass('mcp-error');
    text = `ERREUR: ${text}`;
  } else if (type === 'command') {
    line.addClass('mcp-command');
    text = `> ${text}`;
  } else if (type === 'success') {
    line.addClass('mcp-success');
  }

  line.text(text);

  // Insérer la ligne avant le prompt
  prompt.before(line);

  // Faire défiler vers le bas
  terminal.scrollTop(terminal[0].scrollHeight);

  // Ajouter au journal d'activité si c'est une information importante
  if (type !== 'command') {
    addActivityLog(text);
  }
}

// Ajouter une entrée au journal d'activité
function addActivityLog(text) {
  const log = $('#mcp-activity-log');
  const now = new Date();
  const timeString = now.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit', second: '2-digit' });

  const logEntry = $(`<div class="log-entry"><span class="log-time">${timeString}</span> ${text}</div>`);
  log.append(logEntry);

  // Limiter le nombre d'entrées (garder les 50 dernières)
  const entries = log.find('.log-entry');
  if (entries.length > 50) {
    entries.slice(0, entries.length - 50).remove();
  }

  // Faire défiler vers le bas
  log.scrollTop(log[0].scrollHeight);
}

// Exécuter une commande MCP
function executeCommand(command) {
  // Ajouter la commande à l'historique
  mcpState.commandHistory.unshift(command);
  if (mcpState.commandHistory.length > 20) {
    mcpState.commandHistory.pop();
  }

  // Afficher la commande dans le terminal
  addTerminalLine(command, 'command');

  // Traiter les commandes spéciales
  if (command === 'clear') {
    // Effacer le terminal
    const terminal = $('#mcp-terminal');
    terminal.find('.mcp-line').not('#mcp-prompt').remove();
    return;
  }

  // Envoyer la commande au serveur
  socket.emit('mcp command', { command });
}

// Initialisation au chargement de la page
$(document).ready(function() {
  // Initialiser la date et l'heure
  updateDateTime();
  // Mettre à jour la date et l'heure toutes les secondes
  setInterval(updateDateTime, 1000);

  // Gestionnaire pour le bouton de statut MCP
  $('#mcp-status-btn').on('click', function() {
    const newState = !mcpState.active;
    socket.emit('set mcp status', { active: newState });
  });

  // Gestionnaire pour le toggle MCP
  $('#mcp-mode-toggle').on('change', function() {
    const newState = $(this).is(':checked');
    socket.emit('set mcp status', { active: newState });
  });

  // Gestionnaire pour le toggle d'accès Internet
  $('#internet-access-toggle').on('change', function() {
    const newState = $(this).is(':checked');
    socket.emit('set mcp status', { internetAccess: newState });

    // Mettre à jour l'état local immédiatement pour une réponse plus rapide
    mcpState.internetAccess = newState;
    $('#internet-access-status').text(newState ? 'Activé' : 'Désactivé');

    addTerminalLine(`Accès Internet ${newState ? 'activé' : 'désactivé'}`);
  });

  // Gestionnaire pour le niveau de sécurité
  $('#security-level').on('change', function() {
    const newLevel = $(this).val();
    socket.emit('set mcp status', { securityLevel: newLevel });

    // Mettre à jour l'état local immédiatement
    mcpState.securityLevel = newLevel;

    addTerminalLine(`Niveau de sécurité défini sur: ${newLevel.toUpperCase()}`);
  });

  // Gestionnaire pour les boutons de commande rapide
  $('.mcp-command-btn').on('click', function() {
    const command = $(this).data('command');
    executeCommand(command);
  });

  // Gestionnaire pour l'entrée de commande dans le terminal
  $('#mcp-terminal').on('click', function() {
    // Simuler un prompt de commande
    const command = prompt('Entrez une commande MCP:');
    if (command && command.trim()) {
      executeCommand(command.trim());
    }
  });

  // Simuler des mises à jour périodiques des métriques
  setInterval(() => {
    // Simuler des fluctuations aléatoires
    mcpState.cpuUsage = Math.floor(20 + Math.random() * 30);
    mcpState.memoryUsage = Math.floor(2000 + Math.random() * 2000);
    mcpState.networkUsage = Math.floor(5 + Math.random() * 20);
    mcpState.temperature = Math.floor(50 + Math.random() * 20);

    updateMetricsUI();
  }, 3000);
});
