/**
 * Brain Presence UI - Interface utilisateur pour la présence autonome du cerveau
 * Affiche l'activité cérébrale en temps réel et les pensées générées
 */

// Initialiser la connexion Socket.IO
const socket = io();

// État global de la présence
const presenceState = {
  isActive: false,
  activityLevel: 0,
  lastActivityTime: null,
  currentThought: null,
  thoughtHistory: [],
  zoneActivity: {
    zone1: 0,
    zone2: 0,
    zone3: 0,
    zone4: 0,
    zone5: 0,
    zone6: 0
  },
  // Statistiques des accélérateurs
  acceleratorEfficiency: 100,  // Efficacité des accélérateurs en pourcentage
  acceleratorCount: 0,         // Nombre d'accélérateurs actifs
  acceleratorLoad: 0,          // Charge des accélérateurs en pourcentage
  // Statistiques neuronales
  neuronCount: 1000,           // Nombre de neurones actifs
  connectionCount: 5000,       // Nombre de connexions actives
  iqEstimate: 100,             // Estimation de l'IQ basée sur l'activité
  learningRate: 50,            // Taux d'apprentissage actuel
  thoughtCount: 0,             // Nombre total de pensées générées
  // Paramètres d'interface
  animationActive: true,
  displayMode: 'compact',      // 'compact' ou 'expanded'
  thoughtDisplayMode: 'latest', // 'latest' ou 'history'
  autoScroll: true
};

// Référence aux éléments d'animation
let brainAnimation = null;
let activityChart = null;
let zoneActivityChart = null;

// Événements Socket.IO
socket.on('connect', () => {
  console.log('🔌 Connecté au serveur Luna');
  updateConnectionStatus(true);

  // Demander l'état initial de la présence
  socket.emit('get brain presence');
});

socket.on('disconnect', () => {
  console.log('🔌 Déconnecté du serveur Luna');
  updateConnectionStatus(false);
});

// Recevoir les mises à jour de présence
socket.on('brain presence update', (data) => {
  updatePresenceState(data);
});

// Recevoir une nouvelle pensée
socket.on('brain thought', (thought) => {
  addThought(thought);
});

// Recevoir une mise à jour d'activité
socket.on('brain activity', (activity) => {
  updateActivityLevel(activity);
});

// Fonctions principales

/**
 * Met à jour l'état de connexion
 * @param {boolean} connected - État de connexion
 */
function updateConnectionStatus(connected) {
  const statusElement = document.getElementById('brain-presence-status');

  if (statusElement) {
    statusElement.className = connected ? 'status-active' : 'status-inactive';
    statusElement.innerHTML = connected ?
      '<i class="bi bi-broadcast"></i> Connecté' :
      '<i class="bi bi-broadcast-pin"></i> Déconnecté';
  }
}

/**
 * Met à jour l'état de présence
 * @param {Object} data - Données de présence
 */
function updatePresenceState(data) {
  // Mettre à jour l'état global
  presenceState.isActive = data.isActive;
  presenceState.activityLevel = data.activityLevel;
  presenceState.lastActivityTime = data.lastActivityTime;
  presenceState.zoneActivity = data.zoneActivity || presenceState.zoneActivity;

  // Mettre à jour les statistiques des accélérateurs
  if (data.acceleratorEfficiency !== undefined) {
    presenceState.acceleratorEfficiency = data.acceleratorEfficiency;
  }
  if (data.acceleratorCount !== undefined) {
    presenceState.acceleratorCount = data.acceleratorCount;
  }
  if (data.acceleratorLoad !== undefined) {
    presenceState.acceleratorLoad = data.acceleratorLoad;
  }

  // Mettre à jour les statistiques neuronales
  if (data.neuronCount !== undefined) {
    presenceState.neuronCount = data.neuronCount;
  }
  if (data.connectionCount !== undefined) {
    presenceState.connectionCount = data.connectionCount;
  }
  if (data.iqEstimate !== undefined) {
    presenceState.iqEstimate = data.iqEstimate;
  }
  if (data.learningRate !== undefined) {
    presenceState.learningRate = data.learningRate;
  }
  if (data.thoughtCount !== undefined) {
    presenceState.thoughtCount = data.thoughtCount;
  }

  // Mettre à jour la pensée courante si nouvelle
  if (data.currentThought && (!presenceState.currentThought || data.currentThought.id !== presenceState.currentThought.id)) {
    presenceState.currentThought = data.currentThought;
    addThought(data.currentThought);
  }

  // Mettre à jour l'interface
  updatePresenceUI();

  // Mettre à jour les statistiques avancées
  updateAdvancedStats();
}

/**
 * Met à jour les statistiques avancées dans l'interface
 */
function updateAdvancedStats() {
  // Mettre à jour les statistiques des accélérateurs
  const acceleratorEfficiencyElement = document.getElementById('accelerator-efficiency');
  if (acceleratorEfficiencyElement) {
    acceleratorEfficiencyElement.textContent = `${Math.round(presenceState.acceleratorEfficiency)}%`;
  }

  const acceleratorCountElement = document.getElementById('accelerator-count');
  if (acceleratorCountElement) {
    acceleratorCountElement.textContent = presenceState.acceleratorCount;
  }

  const acceleratorLoadElement = document.getElementById('accelerator-load');
  if (acceleratorLoadElement) {
    acceleratorLoadElement.textContent = `${Math.round(presenceState.acceleratorLoad)}%`;
  }

  // Mettre à jour les statistiques neuronales
  const neuronCountElement = document.getElementById('neuron-count');
  if (neuronCountElement) {
    neuronCountElement.textContent = presenceState.neuronCount.toLocaleString();
  }

  const connectionCountElement = document.getElementById('connection-count');
  if (connectionCountElement) {
    connectionCountElement.textContent = presenceState.connectionCount.toLocaleString();
  }

  const iqEstimateElement = document.getElementById('iq-estimate');
  if (iqEstimateElement) {
    iqEstimateElement.textContent = presenceState.iqEstimate;
  }

  const learningRateElement = document.getElementById('learning-rate');
  if (learningRateElement) {
    learningRateElement.textContent = `${Math.round(presenceState.learningRate)}%`;
  }

  const thoughtCountElement = document.getElementById('thought-count');
  if (thoughtCountElement) {
    thoughtCountElement.textContent = presenceState.thoughtCount;
  }
}

/**
 * Met à jour le niveau d'activité
 * @param {Object} activity - Données d'activité
 */
function updateActivityLevel(activity) {
  presenceState.activityLevel = activity.level;
  presenceState.zoneActivity = activity.zoneActivity || presenceState.zoneActivity;

  // Mettre à jour les statistiques des accélérateurs
  if (activity.acceleratorEfficiency !== undefined) {
    presenceState.acceleratorEfficiency = activity.acceleratorEfficiency;
  }
  if (activity.acceleratorCount !== undefined) {
    presenceState.acceleratorCount = activity.acceleratorCount;
  }
  if (activity.acceleratorLoad !== undefined) {
    presenceState.acceleratorLoad = activity.acceleratorLoad;
  }

  // Mettre à jour les statistiques neuronales
  if (activity.neuronCount !== undefined) {
    presenceState.neuronCount = activity.neuronCount;
  }
  if (activity.connectionCount !== undefined) {
    presenceState.connectionCount = activity.connectionCount;
  }
  if (activity.iqEstimate !== undefined) {
    presenceState.iqEstimate = activity.iqEstimate;
  }
  if (activity.learningRate !== undefined) {
    presenceState.learningRate = activity.learningRate;
  }

  // Mettre à jour l'interface
  updateActivityUI();

  // Mettre à jour les statistiques avancées
  updateAdvancedStats();
}

/**
 * Ajoute une pensée à l'historique
 * @param {Object} thought - Pensée à ajouter
 */
function addThought(thought) {
  // Ajouter à l'historique
  presenceState.thoughtHistory.unshift(thought);

  // Limiter la taille de l'historique
  if (presenceState.thoughtHistory.length > 20) {
    presenceState.thoughtHistory.pop();
  }

  // Mettre à jour l'interface
  updateThoughtUI();
}

/**
 * Met à jour l'interface de présence
 */
function updatePresenceUI() {
  // Mettre à jour l'indicateur de présence
  const presenceIndicator = document.getElementById('brain-presence-indicator');

  if (presenceIndicator) {
    presenceIndicator.className = presenceState.isActive ? 'presence-active' : 'presence-inactive';

    // Animer l'indicateur en fonction du niveau d'activité
    const pulseScale = 1 + (presenceState.activityLevel / 200);
    presenceIndicator.style.transform = `scale(${pulseScale})`;

    // Ajuster l'opacité en fonction du niveau d'activité
    const opacity = 0.5 + (presenceState.activityLevel / 200);
    presenceIndicator.style.opacity = opacity;
  }

  // Mettre à jour le niveau d'activité
  const activityLevelElement = document.getElementById('brain-activity-level');

  if (activityLevelElement) {
    activityLevelElement.textContent = `${Math.round(presenceState.activityLevel)}%`;

    // Mettre à jour la barre de progression
    const progressBar = document.getElementById('brain-activity-progress');

    if (progressBar) {
      progressBar.style.width = `${presenceState.activityLevel}%`;

      // Changer la couleur en fonction du niveau d'activité
      if (presenceState.activityLevel < 30) {
        progressBar.className = 'progress-bar bg-info';
      } else if (presenceState.activityLevel < 70) {
        progressBar.className = 'progress-bar bg-success';
      } else {
        progressBar.className = 'progress-bar bg-warning';
      }
    }
  }

  // Mettre à jour les graphiques si disponibles
  updateCharts();
}

/**
 * Met à jour l'interface d'activité
 */
function updateActivityUI() {
  // Mettre à jour le niveau d'activité
  const activityLevelElement = document.getElementById('brain-activity-level');

  if (activityLevelElement) {
    activityLevelElement.textContent = `${Math.round(presenceState.activityLevel)}%`;

    // Mettre à jour la barre de progression
    const progressBar = document.getElementById('brain-activity-progress');

    if (progressBar) {
      progressBar.style.width = `${presenceState.activityLevel}%`;
    }
  }

  // Mettre à jour l'activité des zones
  Object.keys(presenceState.zoneActivity).forEach(zone => {
    const zoneElement = document.getElementById(`brain-${zone}-activity`);

    if (zoneElement) {
      const activity = presenceState.zoneActivity[zone];
      zoneElement.textContent = `${Math.round(activity)}%`;

      // Mettre à jour la barre de progression
      const progressBar = document.getElementById(`brain-${zone}-progress`);

      if (progressBar) {
        progressBar.style.width = `${activity}%`;
      }
    }
  });

  // Mettre à jour les graphiques si disponibles
  updateCharts();

  // Animer le cerveau si l'animation est active
  if (presenceState.animationActive) {
    animateBrain();
  }
}

/**
 * Met à jour l'interface des pensées
 */
function updateThoughtUI() {
  // Mettre à jour la pensée actuelle
  const currentThoughtElement = document.getElementById('current-thought');

  if (currentThoughtElement && presenceState.currentThought) {
    // Créer un élément de pensée
    const thoughtElement = createThoughtElement(presenceState.currentThought);

    // Remplacer le contenu
    currentThoughtElement.innerHTML = '';
    currentThoughtElement.appendChild(thoughtElement);

    // Ajouter une animation d'apparition
    thoughtElement.classList.add('thought-appear');
  }

  // Mettre à jour l'historique des pensées si en mode historique
  if (presenceState.thoughtDisplayMode === 'history') {
    const thoughtHistoryElement = document.getElementById('thought-history');

    if (thoughtHistoryElement) {
      // Vider l'historique
      thoughtHistoryElement.innerHTML = '';

      // Ajouter chaque pensée
      presenceState.thoughtHistory.forEach(thought => {
        const thoughtElement = createThoughtElement(thought, true);
        thoughtHistoryElement.appendChild(thoughtElement);
      });

      // Faire défiler vers le haut si auto-scroll activé
      if (presenceState.autoScroll) {
        thoughtHistoryElement.scrollTop = 0;
      }
    }
  }
}

/**
 * Crée un élément HTML pour une pensée
 * @param {Object} thought - Pensée à afficher
 * @param {boolean} isHistoryItem - Indique si c'est un élément d'historique
 * @returns {HTMLElement} - Élément HTML
 */
function createThoughtElement(thought, isHistoryItem = false) {
  const thoughtElement = document.createElement('div');
  thoughtElement.className = `thought-item thought-${thought.type}${isHistoryItem ? ' history-item' : ''}`;

  // Formater la date
  const date = new Date(thought.timestamp);
  const formattedTime = `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`;

  // Créer le contenu
  thoughtElement.innerHTML = `
    <div class="thought-header">
      <span class="thought-type">${thought.type}</span>
      <span class="thought-time">${formattedTime}</span>
    </div>
    <div class="thought-content">${thought.content}</div>
  `;

  return thoughtElement;
}

/**
 * Met à jour les graphiques
 */
function updateCharts() {
  // Mettre à jour le graphique d'activité si disponible
  if (activityChart) {
    // Ajouter une nouvelle donnée
    activityChart.data.labels.push('');
    activityChart.data.datasets[0].data.push(presenceState.activityLevel);

    // Limiter le nombre de points
    if (activityChart.data.labels.length > 50) {
      activityChart.data.labels.shift();
      activityChart.data.datasets[0].data.shift();
    }

    activityChart.update();
  }

  // Mettre à jour le graphique d'activité des zones si disponible
  if (zoneActivityChart) {
    zoneActivityChart.data.datasets[0].data = [
      presenceState.zoneActivity.zone1,
      presenceState.zoneActivity.zone2,
      presenceState.zoneActivity.zone3,
      presenceState.zoneActivity.zone4,
      presenceState.zoneActivity.zone5,
      presenceState.zoneActivity.zone6
    ];

    zoneActivityChart.update();
  }
}

/**
 * Anime le cerveau
 */
function animateBrain() {
  const brainElement = document.getElementById('brain-animation');

  if (!brainElement) return;

  // Nombre de neurones basé sur le niveau d'activité
  const neuronCount = Math.floor(presenceState.activityLevel / 8) + 1;

  // Créer des "neurones" qui s'allument
  for (let i = 0; i < neuronCount; i++) {
    createNeuron(brainElement);
  }

  // Créer des connexions entre neurones existants
  if (Math.random() < 0.3 && presenceState.activityLevel > 30) {
    createConnection(brainElement);
  }

  // Faire pulser le cerveau en fonction de l'activité
  const pulseScale = 1 + (presenceState.activityLevel / 500);
  const brainOutline = brainElement.querySelector('svg');
  if (brainOutline) {
    brainOutline.style.transform = `scale(${pulseScale})`;
    brainOutline.style.opacity = 0.5 + (presenceState.activityLevel / 200);
  }
}

/**
 * Crée un neurone dans l'animation du cerveau
 * @param {HTMLElement} brainElement - Élément contenant l'animation du cerveau
 */
function createNeuron(brainElement) {
  // Créer un neurone
  const neuron = document.createElement('div');
  neuron.className = 'brain-neuron';

  // Position aléatoire, mais plus concentrée au centre
  const centerBias = 0.5; // 0 = uniforme, 1 = tout au centre
  const randomX = Math.random();
  const randomY = Math.random();
  const left = 50 + (randomX - 0.5) * 100 * (1 - centerBias);
  const top = 50 + (randomY - 0.5) * 100 * (1 - centerBias);

  neuron.style.left = `${left}%`;
  neuron.style.top = `${top}%`;

  // Taille basée sur l'activité
  const baseSize = 3 + Math.random() * 5;
  const activityFactor = presenceState.activityLevel / 100;
  const size = baseSize * (1 + activityFactor * 0.5);
  neuron.style.width = `${size}px`;
  neuron.style.height = `${size}px`;

  // Couleur basée sur la zone et l'activité
  // Déterminer quelle zone est la plus active
  let mostActiveZone = 1;
  let highestActivity = 0;

  Object.keys(presenceState.zoneActivity).forEach(zone => {
    const zoneNumber = parseInt(zone.replace('zone', ''));
    if (presenceState.zoneActivity[zone] > highestActivity) {
      highestActivity = presenceState.zoneActivity[zone];
      mostActiveZone = zoneNumber;
    }
  });

  // Couleur basée sur la zone (1=rouge, 6=bleu)
  let hue;
  if (Math.random() < 0.7) {
    // 70% de chance d'utiliser la couleur de la zone la plus active
    hue = 360 - (mostActiveZone * 40);
  } else {
    // 30% de chance d'utiliser une couleur aléatoire
    hue = 360 - (Math.floor(Math.random() * 6) + 1) * 40;
  }

  // Ajuster la luminosité en fonction de l'activité
  const lightness = 60 + (presenceState.activityLevel / 5);
  neuron.style.backgroundColor = `hsl(${hue}, 100%, ${lightness}%)`;

  // Ajouter une ombre pour un effet de brillance
  neuron.style.boxShadow = `0 0 ${size * 2}px hsl(${hue}, 100%, 70%)`;

  // Ajouter au cerveau
  brainElement.appendChild(neuron);

  // Stocker la position pour les connexions
  neuron.dataset.x = left;
  neuron.dataset.y = top;

  // Supprimer après l'animation
  setTimeout(() => {
    neuron.classList.add('fade-out');
    setTimeout(() => {
      neuron.remove();
    }, 500);
  }, 1000 + Math.random() * 1000);
}

/**
 * Crée une connexion entre deux neurones
 * @param {HTMLElement} brainElement - Élément contenant l'animation du cerveau
 */
function createConnection(brainElement) {
  // Obtenir tous les neurones actuels
  const neurons = brainElement.querySelectorAll('.brain-neuron');

  if (neurons.length < 2) return;

  // Sélectionner deux neurones aléatoires
  const neuron1 = neurons[Math.floor(Math.random() * neurons.length)];
  let neuron2;
  do {
    neuron2 = neurons[Math.floor(Math.random() * neurons.length)];
  } while (neuron1 === neuron2);

  // Obtenir les positions
  const x1 = parseFloat(neuron1.dataset.x);
  const y1 = parseFloat(neuron1.dataset.y);
  const x2 = parseFloat(neuron2.dataset.x);
  const y2 = parseFloat(neuron2.dataset.y);

  // Calculer la distance
  const dx = x2 - x1;
  const dy = y2 - y1;
  const distance = Math.sqrt(dx * dx + dy * dy);

  // Ne créer une connexion que si les neurones sont assez proches
  if (distance > 40) return;

  // Créer la connexion
  const connection = document.createElement('div');
  connection.className = 'brain-connection';

  // Positionner la connexion
  connection.style.left = `${x1}%`;
  connection.style.top = `${y1}%`;

  // Calculer la longueur et l'angle
  const length = distance;
  const angle = Math.atan2(dy, dx) * (180 / Math.PI);

  // Appliquer la transformation
  connection.style.width = `${length}%`;
  connection.style.transform = `rotate(${angle}deg)`;

  // Couleur basée sur l'activité
  const hue = 240 - Math.floor(presenceState.activityLevel / 2);
  connection.style.backgroundColor = `hsla(${hue}, 100%, 70%, 0.4)`;

  // Ajouter au cerveau
  brainElement.appendChild(connection);

  // Supprimer après l'animation
  setTimeout(() => {
    connection.classList.add('fade-out');
    setTimeout(() => {
      connection.remove();
    }, 300);
  }, 800 + Math.random() * 500);
}

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
  // Initialiser les graphiques
  initCharts();

  // Configurer les gestionnaires d'événements
  setupEventHandlers();

  // Démarrer l'animation du cerveau
  startBrainAnimation();
});

/**
 * Initialise les graphiques
 */
function initCharts() {
  // Graphique d'activité
  const activityChartElement = document.getElementById('activity-chart');

  if (activityChartElement) {
    activityChart = new Chart(activityChartElement, {
      type: 'line',
      data: {
        labels: Array(20).fill(''),
        datasets: [{
          label: 'Niveau d\'activité',
          data: Array(20).fill(0),
          borderColor: 'rgba(156, 137, 184, 1)',
          backgroundColor: 'rgba(156, 137, 184, 0.2)',
          borderWidth: 2,
          tension: 0.4,
          fill: true
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            max: 100
          }
        },
        plugins: {
          legend: {
            display: false
          }
        },
        animation: {
          duration: 500
        }
      }
    });
  }

  // Graphique d'activité des zones
  const zoneActivityChartElement = document.getElementById('zone-activity-chart');

  if (zoneActivityChartElement) {
    zoneActivityChart = new Chart(zoneActivityChartElement, {
      type: 'bar',
      data: {
        labels: ['Zone 1', 'Zone 2', 'Zone 3', 'Zone 4', 'Zone 5', 'Zone 6'],
        datasets: [{
          label: 'Activité des zones',
          data: [0, 0, 0, 0, 0, 0],
          backgroundColor: [
            'rgba(255, 99, 132, 0.7)',
            'rgba(255, 159, 64, 0.7)',
            'rgba(255, 205, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(54, 162, 235, 0.7)',
            'rgba(153, 102, 255, 0.7)'
          ],
          borderColor: [
            'rgb(255, 99, 132)',
            'rgb(255, 159, 64)',
            'rgb(255, 205, 86)',
            'rgb(75, 192, 192)',
            'rgb(54, 162, 235)',
            'rgb(153, 102, 255)'
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            max: 100
          }
        },
        plugins: {
          legend: {
            display: false
          }
        },
        animation: {
          duration: 500
        }
      }
    });
  }
}

/**
 * Configure les gestionnaires d'événements
 */
function setupEventHandlers() {
  // Bouton pour basculer l'animation
  const toggleAnimationBtn = document.getElementById('toggle-animation-btn');

  if (toggleAnimationBtn) {
    toggleAnimationBtn.addEventListener('click', function() {
      presenceState.animationActive = !presenceState.animationActive;
      this.innerHTML = presenceState.animationActive ?
        '<i class="bi bi-pause-circle"></i>' :
        '<i class="bi bi-play-circle"></i>';
    });
  }

  // Bouton pour basculer le mode d'affichage
  const toggleDisplayModeBtn = document.getElementById('toggle-display-mode-btn');

  if (toggleDisplayModeBtn) {
    toggleDisplayModeBtn.addEventListener('click', function() {
      presenceState.displayMode = presenceState.displayMode === 'compact' ? 'expanded' : 'compact';

      const presenceContainer = document.getElementById('brain-presence-container');

      if (presenceContainer) {
        presenceContainer.className = `brain-presence-container ${presenceState.displayMode}`;
      }

      this.innerHTML = presenceState.displayMode === 'expanded' ?
        '<i class="bi bi-arrows-angle-contract"></i>' :
        '<i class="bi bi-arrows-angle-expand"></i>';
    });
  }

  // Bouton pour basculer le mode d'affichage des pensées
  const toggleThoughtModeBtn = document.getElementById('toggle-thought-mode-btn');

  if (toggleThoughtModeBtn) {
    toggleThoughtModeBtn.addEventListener('click', function() {
      presenceState.thoughtDisplayMode = presenceState.thoughtDisplayMode === 'latest' ? 'history' : 'latest';

      const currentThoughtContainer = document.getElementById('current-thought-container');
      const thoughtHistoryContainer = document.getElementById('thought-history-container');

      if (currentThoughtContainer && thoughtHistoryContainer) {
        if (presenceState.thoughtDisplayMode === 'latest') {
          currentThoughtContainer.style.display = 'block';
          thoughtHistoryContainer.style.display = 'none';
        } else {
          currentThoughtContainer.style.display = 'none';
          thoughtHistoryContainer.style.display = 'block';

          // Mettre à jour l'historique
          updateThoughtUI();
        }
      }

      this.innerHTML = presenceState.thoughtDisplayMode === 'history' ?
        '<i class="bi bi-chat"></i>' :
        '<i class="bi bi-chat-text"></i>';
    });
  }
}

/**
 * Démarre l'animation du cerveau
 */
function startBrainAnimation() {
  // Animation simple pour le moment
  setInterval(() => {
    if (presenceState.isActive && presenceState.animationActive) {
      animateBrain();
    }
  }, 500);
}
