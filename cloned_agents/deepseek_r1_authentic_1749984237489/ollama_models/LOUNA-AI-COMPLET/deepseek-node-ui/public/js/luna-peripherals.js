/**
 * Luna Peripherals - Gestion des périphériques (caméra, microphone, haut-parleur)
 * Ce script gère l'activation et la désactivation des périphériques d'entrée/sortie
 */

// État global des périphériques
const peripheralsState = {
  microphone: {
    active: false,
    stream: null,
    recognizer: null,
    visualizer: null
  },
  camera: {
    active: false,
    stream: null,
    container: null,
    processor: null
  },
  speaker: {
    active: false,
    synthesis: null,
    volume: 1.0
  }
};

// Connexion Socket.io
let socket;

// Initialisation au chargement du document
document.addEventListener('DOMContentLoaded', function() {
  // Initialiser la connexion Socket.io
  socket = io();

  // Initialiser les gestionnaires d'événements
  initializeEventHandlers();

  console.log('Luna Peripherals Interface initialized');
});

/**
 * Initialise les gestionnaires d'événements
 */
function initializeEventHandlers() {
  // Gestionnaire pour le bouton du microphone
  const microphoneBtn = document.getElementById('toggleMicrophoneBtn');
  if (microphoneBtn) {
    microphoneBtn.addEventListener('click', function() {
      toggleMicrophone();
    });
  }

  // Gestionnaire pour le bouton de la caméra
  const cameraBtn = document.getElementById('toggleVisionBtn');
  if (cameraBtn) {
    cameraBtn.addEventListener('click', function() {
      toggleCamera();
    });
  }

  // Gestionnaire pour le bouton de synthèse vocale
  const speechBtn = document.getElementById('testSpeechBtn');
  if (speechBtn) {
    speechBtn.addEventListener('click', function() {
      toggleSpeech();
    });
  }
}

/**
 * Active ou désactive le microphone
 */
function toggleMicrophone() {
  if (peripheralsState.microphone.active) {
    stopMicrophone();
  } else {
    startMicrophone();
  }
}

/**
 * Active le microphone
 */
function startMicrophone() {
  // Vérifier si le navigateur supporte l'API MediaDevices
  if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
    showNotification('Votre navigateur ne supporte pas l\'accès au microphone', 'error');
    return;
  }

  // Mettre à jour l'interface
  const statusIndicator = document.querySelector('#microphoneStatus .status-indicator');
  if (statusIndicator) {
    statusIndicator.classList.remove('status-inactive');
    statusIndicator.classList.add('status-active');

    // Ajouter un effet lumineux pour indiquer que le micro est actif
    statusIndicator.style.boxShadow = '0 0 10px #4caf50';
    statusIndicator.style.backgroundColor = '#4caf50';
  }

  // Mettre à jour tous les boutons de microphone
  const micButtons = document.querySelectorAll('#toggleMicrophoneBtn, #mic-btn');
  micButtons.forEach(button => {
    if (button) {
      button.classList.remove('btn-luna', 'btn-luna-outline');
      button.classList.add('btn-success');
      button.innerHTML = '<i class="bi bi-mic"></i>';
      button.setAttribute('title', 'Désactiver le microphone');
      button.style.backgroundColor = '#28a745'; // Vert pour indiquer qu'il est actif
      button.style.borderColor = '#28a745';
    }
  });

  // Afficher un message
  updateSystemDashboard('Activation du microphone...');

  // Demander l'accès au microphone
  navigator.mediaDevices.getUserMedia({
    audio: {
      echoCancellation: true,
      noiseSuppression: true,
      autoGainControl: true
    }
  })
  .then(function(stream) {
    // Stocker le flux audio
    peripheralsState.microphone.stream = stream;
    peripheralsState.microphone.active = true;

    // Créer un visualiseur audio
    createAudioVisualizer(stream);

    // Initialiser la reconnaissance vocale si disponible
    initSpeechRecognition();

    // Informer le serveur
    socket.emit('peripheral status', {
      type: 'microphone',
      active: true
    });

    // Afficher un message de succès
    updateSystemDashboard('Microphone activé avec succès');
    showNotification('Microphone activé', 'success');
  })
  .catch(function(error) {
    console.error('Erreur lors de l\'accès au microphone:', error);
    updateSystemDashboard(`Erreur d'accès au microphone: ${error.message}`);
    showNotification('Impossible d\'accéder au microphone', 'error');

    // Réinitialiser l'interface
    statusIndicator.classList.remove('status-active');
    statusIndicator.classList.add('status-inactive');
  });
}

/**
 * Désactive le microphone
 */
function stopMicrophone() {
  // Arrêter le flux audio
  if (peripheralsState.microphone.stream) {
    peripheralsState.microphone.stream.getTracks().forEach(track => track.stop());
    peripheralsState.microphone.stream = null;
  }

  // Arrêter la reconnaissance vocale
  if (peripheralsState.microphone.recognizer) {
    peripheralsState.microphone.recognizer.stop();
    peripheralsState.microphone.recognizer = null;
  }

  // Supprimer le visualiseur
  if (peripheralsState.microphone.visualizer) {
    const container = document.getElementById('audio-level');
    if (container) {
      container.remove();
    }
    peripheralsState.microphone.visualizer = null;
  }

  // Mettre à jour l'état
  peripheralsState.microphone.active = false;

  // Mettre à jour l'interface
  const statusIndicator = document.querySelector('#microphoneStatus .status-indicator');
  if (statusIndicator) {
    statusIndicator.classList.remove('status-active');
    statusIndicator.classList.add('status-inactive');

    // Supprimer l'effet lumineux
    statusIndicator.style.boxShadow = 'none';
    statusIndicator.style.backgroundColor = '';
  }

  // Mettre à jour tous les boutons de microphone
  const micButtons = document.querySelectorAll('#toggleMicrophoneBtn, #mic-btn');
  micButtons.forEach(button => {
    if (button) {
      button.classList.remove('btn-success', 'btn-danger');
      if (button.id === 'toggleMicrophoneBtn') {
        button.classList.add('btn-luna');
      } else {
        button.classList.add('btn-luna-outline');
      }
      button.innerHTML = '<i class="bi bi-mic"></i>';
      button.setAttribute('title', 'Activer le microphone');
      button.style.backgroundColor = ''; // Réinitialiser la couleur
      button.style.borderColor = '';
    }
  });

  // Informer le serveur
  socket.emit('peripheral status', {
    type: 'microphone',
    active: false
  });

  // Afficher un message
  updateSystemDashboard('Microphone désactivé');
  showNotification('Microphone désactivé', 'info');
}

/**
 * Crée un visualiseur audio pour le microphone
 * @param {MediaStream} stream - Flux audio du microphone
 */
function createAudioVisualizer(stream) {
  try {
    // Créer un contexte audio
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const source = audioContext.createMediaStreamSource(stream);
    const analyser = audioContext.createAnalyser();

    // Configurer l'analyseur
    analyser.fftSize = 256;
    analyser.smoothingTimeConstant = 0.8;
    source.connect(analyser);

    // Créer un conteneur pour le visualiseur
    const container = document.createElement('div');
    container.id = 'audio-level';
    container.className = 'message system-message';
    container.innerHTML = `
      <div class="message-content">
        <p>Microphone actif - Niveau sonore:</p>
        <div class="audio-meter" style="height: 20px; background: rgba(0,0,0,0.3); border-radius: 10px; overflow: hidden; margin-top: 5px;">
          <div id="audio-level-bar" style="height: 100%; width: 0%; background: linear-gradient(to right, #9c89b8, #f0a6ca); transition: width 0.1s;"></div>
        </div>
      </div>
    `;

    // Ajouter au DOM
    const conversationContainer = document.getElementById('conversation-container');
    if (conversationContainer) {
      conversationContainer.appendChild(container);
    }

    // Stocker l'analyseur
    peripheralsState.microphone.visualizer = analyser;

    // Mettre à jour le niveau audio en temps réel
    const dataArray = new Uint8Array(analyser.frequencyBinCount);
    function updateAudioLevel() {
      if (!peripheralsState.microphone.active) return;

      analyser.getByteFrequencyData(dataArray);

      // Calculer le niveau moyen
      let sum = 0;
      for (let i = 0; i < dataArray.length; i++) {
        sum += dataArray[i];
      }
      const average = sum / dataArray.length;
      const level = Math.min(100, Math.max(0, average * 100 / 256));

      // Mettre à jour la barre de niveau
      const levelBar = document.getElementById('audio-level-bar');
      if (levelBar) {
        levelBar.style.width = level + '%';
      }

      // Continuer l'animation
      requestAnimationFrame(updateAudioLevel);
    }

    // Démarrer l'animation
    updateAudioLevel();
  } catch (error) {
    console.error('Erreur lors de la création du visualiseur audio:', error);
  }
}

/**
 * Initialise la reconnaissance vocale
 */
function initSpeechRecognition() {
  // Vérifier si le navigateur supporte l'API SpeechRecognition
  const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
  if (!SpeechRecognition) {
    console.log('La reconnaissance vocale n\'est pas supportée par ce navigateur');
    return;
  }

  try {
    // Créer un objet de reconnaissance vocale
    const recognition = new SpeechRecognition();
    recognition.lang = 'fr-FR';
    recognition.continuous = true;
    recognition.interimResults = true;

    // Gérer les résultats
    recognition.onresult = function(event) {
      const result = event.results[event.results.length - 1];
      const transcript = result[0].transcript;

      if (result.isFinal) {
        // Envoyer le texte reconnu au serveur
        socket.emit('speech recognition', { text: transcript });

        // Si le texte est suffisamment long, l'envoyer comme message
        if (transcript.length > 5) {
          const inputField = document.getElementById('user-input');
          if (inputField) {
            inputField.value = transcript;
            // Simuler un clic sur le bouton d'envoi
            const sendButton = document.getElementById('send-button');
            if (sendButton) {
              sendButton.click();
            }
          }
        }
      }
    };

    // Gérer les erreurs
    recognition.onerror = function(event) {
      console.error('Erreur de reconnaissance vocale:', event.error);
    };

    // Redémarrer automatiquement la reconnaissance
    recognition.onend = function() {
      if (peripheralsState.microphone.active) {
        recognition.start();
      }
    };

    // Démarrer la reconnaissance
    recognition.start();

    // Stocker l'objet de reconnaissance
    peripheralsState.microphone.recognizer = recognition;
  } catch (error) {
    console.error('Erreur lors de l\'initialisation de la reconnaissance vocale:', error);
  }
}

/**
 * Active ou désactive la caméra
 */
function toggleCamera() {
  if (peripheralsState.camera.active) {
    stopCamera();
  } else {
    startCamera();
  }
}

/**
 * Active la caméra
 */
function startCamera() {
  // Vérifier si le navigateur supporte l'API MediaDevices
  if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
    showNotification('Votre navigateur ne supporte pas l\'accès à la caméra', 'error');
    return;
  }

  // Mettre à jour l'interface
  const statusIndicator = document.querySelector('#visionStatus .status-indicator');
  if (statusIndicator) {
    statusIndicator.classList.remove('status-inactive');
    statusIndicator.classList.add('status-active');

    // Ajouter un effet lumineux pour indiquer que la caméra est active
    statusIndicator.style.boxShadow = '0 0 10px #4caf50';
    statusIndicator.style.backgroundColor = '#4caf50';
  }

  // Mettre à jour tous les boutons de caméra
  const cameraButtons = document.querySelectorAll('#toggleVisionBtn, #camera-btn');
  cameraButtons.forEach(button => {
    if (button) {
      button.classList.remove('btn-luna', 'btn-luna-outline');
      button.classList.add('btn-success');
      button.innerHTML = '<i class="bi bi-camera"></i>';
      button.setAttribute('title', 'Désactiver la caméra');
      button.style.backgroundColor = '#28a745'; // Vert pour indiquer qu'il est actif
      button.style.borderColor = '#28a745';
    }
  });

  // Afficher un message
  updateSystemDashboard('Activation de la caméra...');

  // Demander l'accès à la caméra
  navigator.mediaDevices.getUserMedia({
    video: {
      width: { ideal: 640 },
      height: { ideal: 480 },
      frameRate: { ideal: 30 }
    }
  })
  .then(function(stream) {
    // Stocker le flux vidéo
    peripheralsState.camera.stream = stream;
    peripheralsState.camera.active = true;

    // Créer un conteneur pour la vidéo
    createVideoContainer(stream);

    // Informer le serveur
    socket.emit('peripheral status', {
      type: 'camera',
      active: true
    });

    // Afficher un message de succès
    updateSystemDashboard('Caméra activée avec succès');
    showNotification('Caméra activée', 'success');
  })
  .catch(function(error) {
    console.error('Erreur lors de l\'accès à la caméra:', error);
    updateSystemDashboard(`Erreur d'accès à la caméra: ${error.message}`);
    showNotification('Impossible d\'accéder à la caméra', 'error');

    // Réinitialiser l'interface
    statusIndicator.classList.remove('status-active');
    statusIndicator.classList.add('status-inactive');
  });
}

/**
 * Crée un conteneur pour afficher la vidéo de la caméra
 * @param {MediaStream} stream - Flux vidéo de la caméra
 */
function createVideoContainer(stream) {
  // Créer un conteneur pour la vidéo
  let videoContainer = document.getElementById('video-container');
  if (!videoContainer) {
    videoContainer = document.createElement('div');
    videoContainer.id = 'video-container';
    videoContainer.style.position = 'fixed';
    videoContainer.style.bottom = '80px';
    videoContainer.style.right = '20px';
    videoContainer.style.zIndex = '1000';
    videoContainer.style.transition = 'opacity 0.3s ease';
    videoContainer.style.opacity = '0';

    // Créer l'élément vidéo
    videoContainer.innerHTML = `
      <div style="position: relative; width: 320px; height: 240px; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 8px rgba(0,0,0,0.2);">
        <video id="video-preview" autoplay playsinline style="width: 100%; height: 100%; object-fit: cover;"></video>
        <div style="position: absolute; top: 10px; right: 10px; background: rgba(0,0,0,0.5); color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; cursor: pointer;" onclick="toggleCamera()">
          <i class="bi bi-x"></i>
        </div>
      </div>
    `;

    // Ajouter au DOM
    document.body.appendChild(videoContainer);
  } else {
    // Réinitialiser le conteneur existant
    videoContainer.style.display = 'block';
  }

  // Stocker le conteneur
  peripheralsState.camera.container = videoContainer;

  // Connecter le flux vidéo à l'élément vidéo
  const videoElement = document.getElementById('video-preview');
  if (videoElement) {
    videoElement.srcObject = stream;

    // Afficher le conteneur avec une animation fluide
    setTimeout(() => {
      videoContainer.style.opacity = '1';
    }, 100);
  }
}

/**
 * Désactive la caméra
 */
function stopCamera() {
  // Arrêter le flux vidéo
  if (peripheralsState.camera.stream) {
    peripheralsState.camera.stream.getTracks().forEach(track => track.stop());
    peripheralsState.camera.stream = null;
  }

  // Masquer le conteneur vidéo
  if (peripheralsState.camera.container) {
    peripheralsState.camera.container.style.opacity = '0';
    setTimeout(() => {
      peripheralsState.camera.container.style.display = 'none';
    }, 300);
  }

  // Mettre à jour l'état
  peripheralsState.camera.active = false;

  // Mettre à jour l'interface
  const statusIndicator = document.querySelector('#visionStatus .status-indicator');
  if (statusIndicator) {
    statusIndicator.classList.remove('status-active');
    statusIndicator.classList.add('status-inactive');

    // Supprimer l'effet lumineux
    statusIndicator.style.boxShadow = 'none';
    statusIndicator.style.backgroundColor = '';
  }

  // Mettre à jour tous les boutons de caméra
  const cameraButtons = document.querySelectorAll('#toggleVisionBtn, #camera-btn');
  cameraButtons.forEach(button => {
    if (button) {
      button.classList.remove('btn-success', 'btn-danger');
      if (button.id === 'toggleVisionBtn') {
        button.classList.add('btn-luna');
      } else {
        button.classList.add('btn-luna-outline');
      }
      button.innerHTML = '<i class="bi bi-camera"></i>';
      button.setAttribute('title', 'Activer la caméra');
      button.style.backgroundColor = ''; // Réinitialiser la couleur
      button.style.borderColor = '';
    }
  });

  // Informer le serveur
  socket.emit('peripheral status', {
    type: 'camera',
    active: false
  });

  // Afficher un message
  updateSystemDashboard('Caméra désactivée');
  showNotification('Caméra désactivée', 'info');
}

/**
 * Active ou désactive la synthèse vocale
 */
function toggleSpeech() {
  if (peripheralsState.speaker.active) {
    stopSpeech();
  } else {
    startSpeech();
  }
}

/**
 * Active la synthèse vocale
 */
function startSpeech() {
  // Vérifier si le navigateur supporte l'API SpeechSynthesis
  if (!window.speechSynthesis) {
    showNotification('Votre navigateur ne supporte pas la synthèse vocale', 'error');
    return;
  }

  // Mettre à jour l'interface
  const statusIndicator = document.querySelector('#speechStatus .status-indicator');
  if (statusIndicator) {
    statusIndicator.classList.remove('status-inactive');
    statusIndicator.classList.add('status-active');

    // Ajouter un effet lumineux pour indiquer que la synthèse vocale est active
    statusIndicator.style.boxShadow = '0 0 10px #4caf50';
    statusIndicator.style.backgroundColor = '#4caf50';
  }

  // Mettre à jour tous les boutons de synthèse vocale
  const speechButtons = document.querySelectorAll('#testSpeechBtn, #speaker-btn');
  speechButtons.forEach(button => {
    if (button) {
      button.classList.remove('btn-luna', 'btn-luna-outline');
      button.classList.add('btn-success');
      button.innerHTML = '<i class="bi bi-volume-up"></i>';
      button.setAttribute('title', 'Désactiver la synthèse vocale');
      button.style.backgroundColor = '#28a745'; // Vert pour indiquer qu'il est actif
      button.style.borderColor = '#28a745';
    }
  });

  // Mettre à jour l'état
  peripheralsState.speaker.active = true;

  // Informer le serveur
  socket.emit('peripheral status', {
    type: 'speech',
    active: true
  });

  // Tester la synthèse vocale
  const message = 'La synthèse vocale est maintenant activée. Je vais désormais vous parler.';
  speakText(message);

  // Afficher un message
  updateSystemDashboard('Synthèse vocale activée');
  showNotification('Synthèse vocale activée', 'success');
}

/**
 * Désactive la synthèse vocale
 */
function stopSpeech() {
  // Arrêter toute synthèse vocale en cours
  if (window.speechSynthesis) {
    window.speechSynthesis.cancel();
  }

  // Mettre à jour l'état
  peripheralsState.speaker.active = false;

  // Mettre à jour l'interface
  const statusIndicator = document.querySelector('#speechStatus .status-indicator');
  if (statusIndicator) {
    statusIndicator.classList.remove('status-active');
    statusIndicator.classList.add('status-inactive');

    // Supprimer l'effet lumineux
    statusIndicator.style.boxShadow = 'none';
    statusIndicator.style.backgroundColor = '';
  }

  // Mettre à jour tous les boutons de synthèse vocale
  const speechButtons = document.querySelectorAll('#testSpeechBtn, #speaker-btn');
  speechButtons.forEach(button => {
    if (button) {
      button.classList.remove('btn-success', 'btn-danger');
      if (button.id === 'testSpeechBtn') {
        button.classList.add('btn-luna');
        button.innerHTML = '<i class="bi bi-chat-quote"></i>';
      } else {
        button.classList.add('btn-luna-outline');
        button.innerHTML = '<i class="bi bi-volume-up"></i>';
      }
      button.setAttribute('title', 'Activer la synthèse vocale');
      button.style.backgroundColor = ''; // Réinitialiser la couleur
      button.style.borderColor = '';
    }
  });

  // Informer le serveur
  socket.emit('peripheral status', {
    type: 'speech',
    active: false
  });

  // Afficher un message
  updateSystemDashboard('Synthèse vocale désactivée');
  showNotification('Synthèse vocale désactivée', 'info');
}

/**
 * Fait parler le texte via la synthèse vocale
 * @param {string} text - Texte à prononcer
 */
function speakText(text) {
  // Vérifier si la synthèse vocale est activée
  if (!peripheralsState.speaker.active || !window.speechSynthesis) {
    return;
  }

  // Créer un objet d'énoncé
  const utterance = new SpeechSynthesisUtterance(text);

  // Configurer la voix
  utterance.lang = 'fr-FR';
  utterance.volume = peripheralsState.speaker.volume;
  utterance.rate = 1.0;
  utterance.pitch = 1.0;

  // Essayer de trouver une voix féminine en français
  const voices = window.speechSynthesis.getVoices();
  const frenchVoices = voices.filter(voice => voice.lang.includes('fr'));
  const femaleVoices = frenchVoices.filter(voice => voice.name.includes('female') || voice.name.includes('Amélie') || voice.name.includes('Audrey'));

  if (femaleVoices.length > 0) {
    utterance.voice = femaleVoices[0];
  } else if (frenchVoices.length > 0) {
    utterance.voice = frenchVoices[0];
  }

  // Prononcer le texte
  window.speechSynthesis.speak(utterance);
}

/**
 * Met à jour le tableau de bord système avec un message
 * @param {string} message - Message à afficher
 */
function updateSystemDashboard(message) {
  // Trouver ou créer le conteneur de messages système
  let systemMessagesContainer = document.getElementById('system-messages');

  if (!systemMessagesContainer) {
    // Si le conteneur n'existe pas, le créer dans la carte MCP
    const mcpCard = document.querySelector('.luna-card h4 i.bi-cpu')?.closest('.luna-card');

    if (mcpCard) {
      // Créer une nouvelle section pour les messages système
      const systemSection = document.createElement('div');
      systemSection.className = 'mt-3 pt-2 border-top border-secondary';
      systemSection.innerHTML = `
        <h6 class="mb-2"><i class="bi bi-info-circle me-2"></i> Messages système</h6>
        <div id="system-messages" class="small" style="max-height: 150px; overflow-y: auto; background: rgba(0,0,0,0.2); border-radius: 5px; padding: 8px;"></div>
      `;

      mcpCard.appendChild(systemSection);
      systemMessagesContainer = document.getElementById('system-messages');
    }
  }

  if (systemMessagesContainer) {
    // Ajouter le message au conteneur
    const messageElement = document.createElement('div');
    messageElement.className = 'system-message-item mb-1';

    // Formater le message avec l'heure
    const timestamp = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    messageElement.innerHTML = `
      <small class="text-muted">${timestamp}</small>
      <span class="ms-1">${message}</span>
    `;

    // Ajouter au début pour que les messages les plus récents soient en haut
    systemMessagesContainer.insertBefore(messageElement, systemMessagesContainer.firstChild);

    // Limiter le nombre de messages (garder les 20 derniers)
    const messages = systemMessagesContainer.querySelectorAll('.system-message-item');
    if (messages.length > 20) {
      for (let i = 20; i < messages.length; i++) {
        systemMessagesContainer.removeChild(messages[i]);
      }
    }
  } else {
    // Si on ne peut pas afficher dans l'interface, au moins logger dans la console
    console.log('Message système:', message);
  }
}

/**
 * Affiche une notification
 * @param {string} message - Message à afficher
 * @param {string} type - Type de notification (info, success, warning, error)
 */
function showNotification(message, type = 'info') {
  // Vérifier si la fonction existe dans le contexte global
  if (typeof window.showNotification === 'function') {
    window.showNotification(message, type);
    return;
  }

  // Créer l'élément de notification s'il n'existe pas
  let notificationArea = document.getElementById('notification-area');
  if (!notificationArea) {
    notificationArea = document.createElement('div');
    notificationArea.id = 'notification-area';
    notificationArea.style.position = 'fixed';
    notificationArea.style.top = '20px';
    notificationArea.style.right = '20px';
    notificationArea.style.zIndex = '9999';
    notificationArea.style.width = '300px';
    document.body.appendChild(notificationArea);
  }

  // Créer la notification
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  notification.innerHTML = message;
  notificationArea.appendChild(notification);

  // Afficher avec animation
  setTimeout(() => {
    notification.classList.add('show');

    // Masquer après 3 secondes
    setTimeout(() => {
      notification.classList.remove('show');
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }, 100);
}