/**
 * Visualisation de la mémoire thermique pour DeepSeek r1
 */

document.addEventListener('DOMContentLoaded', () => {
  console.log('Memory visualization initialized');

  // Éléments DOM
  const brainVisualization = document.getElementById('brain-visualization');
  
  // Variables globales
  let socket = null;
  let memoryData = null;
  let brainRegions = [];
  let brainConnections = [];
  let kyberEnabled = false;
  let kyberLocked = false;
  
  // Initialiser Socket.io
  initSocket();
  
  // Configurer les écouteurs d'événements
  setupEventListeners();
  
  /**
   * Initialise la connexion Socket.io
   */
  function initSocket() {
    try {
      console.log('Initializing socket connection...');
      socket = io({
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
        timeout: 10000
      });
      
      socket.on('connect', () => {
        console.log('Socket connected successfully');
        updateOllamaStatus(true);
      });
      
      socket.on('memory update', (data) => {
        console.log('Received memory update:', data);
        memoryData = data;
        updateMemoryVisualization();
        updateMemoryStats();
      });
      
      socket.on('brain state', (data) => {
        console.log('Received brain state:', data);
        updateBrainState(data);
      });
      
      socket.on('ollama status', (data) => {
        console.log('Received Ollama status:', data);
        updateOllamaStatus(data.isRunning);
      });
      
      socket.on('connect_error', (error) => {
        console.error('Socket connection error:', error);
        updateOllamaStatus(false);
      });
      
      socket.on('disconnect', (reason) => {
        console.log('Socket disconnected:', reason);
        updateOllamaStatus(false);
      });
    } catch (error) {
      console.error('Error initializing socket:', error);
    }
  }
  
  /**
   * Configure les écouteurs d'événements
   */
  function setupEventListeners() {
    // Contrôles Kyber
    const kyberEnabledCheckbox = document.getElementById('kyber-enabled');
    const kyberFactorSlider = document.getElementById('kyber-factor');
    const kyberTemperatureSlider = document.getElementById('kyber-temperature');
    const kyberStabilitySlider = document.getElementById('kyber-stability');
    const saveKyberBtn = document.getElementById('save-kyber-btn');
    const lockKyberBtn = document.getElementById('lock-kyber-btn');
    
    if (kyberEnabledCheckbox) {
      kyberEnabledCheckbox.addEventListener('change', () => {
        kyberEnabled = kyberEnabledCheckbox.checked;
        updateKyberUI();
      });
    }
    
    if (kyberFactorSlider) {
      kyberFactorSlider.addEventListener('input', () => {
        document.getElementById('kyber-factor-value').textContent = kyberFactorSlider.value;
      });
    }
    
    if (kyberTemperatureSlider) {
      kyberTemperatureSlider.addEventListener('input', () => {
        document.getElementById('kyber-temperature-value').textContent = kyberTemperatureSlider.value;
      });
    }
    
    if (kyberStabilitySlider) {
      kyberStabilitySlider.addEventListener('input', () => {
        document.getElementById('kyber-stability-value').textContent = kyberStabilitySlider.value;
      });
    }
    
    if (saveKyberBtn) {
      saveKyberBtn.addEventListener('click', saveKyberSettings);
    }
    
    if (lockKyberBtn) {
      lockKyberBtn.addEventListener('click', toggleKyberLock);
    }
  }
  
  /**
   * Met à jour la visualisation de la mémoire
   */
  function updateMemoryVisualization() {
    if (!brainVisualization || !memoryData) return;
    
    // Supprimer le message de chargement
    const loadingElement = brainVisualization.querySelector('.brain-loading');
    if (loadingElement) {
      loadingElement.remove();
    }
    
    // Créer les régions du cerveau si elles n'existent pas
    if (brainRegions.length === 0) {
      createBrainRegions();
    }
    
    // Mettre à jour les connexions
    updateBrainConnections();
    
    // Animer les régions en fonction de l'activité
    animateBrainActivity();
  }
  
  /**
   * Crée les régions du cerveau
   */
  function createBrainRegions() {
    const regions = [
      { id: 'instant', name: 'Instant Memory', x: 50, y: 50, size: 80 },
      { id: 'short-term', name: 'Short-Term', x: 200, y: 100, size: 70 },
      { id: 'working', name: 'Working Memory', x: 350, y: 150, size: 90 },
      { id: 'medium-term', name: 'Medium-Term', x: 150, y: 250, size: 75 },
      { id: 'long-term', name: 'Long-Term', x: 300, y: 350, size: 100 },
      { id: 'dream', name: 'Dream Memory', x: 450, y: 300, size: 65 },
      { id: 'kyber', name: 'Kyber', x: 250, y: 200, size: 60 }
    ];
    
    regions.forEach(region => {
      const regionElement = document.createElement('div');
      regionElement.className = `brain-region ${region.id}`;
      regionElement.id = `brain-${region.id}`;
      regionElement.style.left = `${region.x}px`;
      regionElement.style.top = `${region.y}px`;
      regionElement.style.width = `${region.size}px`;
      regionElement.style.height = `${region.size}px`;
      regionElement.textContent = region.name;
      regionElement.dataset.region = region.id;
      
      regionElement.addEventListener('click', () => showMemoryDetails(region.id));
      
      brainVisualization.appendChild(regionElement);
      brainRegions.push(regionElement);
    });
    
    // Créer les connexions entre les régions
    createBrainConnections();
  }
  
  /**
   * Crée les connexions entre les régions du cerveau
   */
  function createBrainConnections() {
    const connections = [
      { from: 'instant', to: 'short-term' },
      { from: 'instant', to: 'working' },
      { from: 'short-term', to: 'working' },
      { from: 'short-term', to: 'medium-term' },
      { from: 'working', to: 'medium-term' },
      { from: 'medium-term', to: 'long-term' },
      { from: 'long-term', to: 'dream' },
      { from: 'kyber', to: 'instant' },
      { from: 'kyber', to: 'working' },
      { from: 'kyber', to: 'long-term' }
    ];
    
    connections.forEach(connection => {
      const fromElement = document.getElementById(`brain-${connection.from}`);
      const toElement = document.getElementById(`brain-${connection.to}`);
      
      if (fromElement && toElement) {
        const fromRect = fromElement.getBoundingClientRect();
        const toRect = toElement.getBoundingClientRect();
        
        const fromX = fromElement.offsetLeft + fromElement.offsetWidth / 2;
        const fromY = fromElement.offsetTop + fromElement.offsetHeight / 2;
        const toX = toElement.offsetLeft + toElement.offsetWidth / 2;
        const toY = toElement.offsetTop + toElement.offsetHeight / 2;
        
        const length = Math.sqrt(Math.pow(toX - fromX, 2) + Math.pow(toY - fromY, 2));
        const angle = Math.atan2(toY - fromY, toX - fromX) * 180 / Math.PI;
        
        const connectionElement = document.createElement('div');
        connectionElement.className = 'brain-connection';
        connectionElement.style.width = `${length}px`;
        connectionElement.style.height = '2px';
        connectionElement.style.left = `${fromX}px`;
        connectionElement.style.top = `${fromY}px`;
        connectionElement.style.transform = `rotate(${angle}deg)`;
        connectionElement.dataset.from = connection.from;
        connectionElement.dataset.to = connection.to;
        
        brainVisualization.appendChild(connectionElement);
        brainConnections.push(connectionElement);
      }
    });
  }
  
  /**
   * Met à jour les connexions du cerveau
   */
  function updateBrainConnections() {
    brainConnections.forEach(connection => {
      const fromRegion = connection.dataset.from;
      const toRegion = connection.dataset.to;
      
      // Simuler l'activité neuronale
      if (Math.random() < 0.1) {
        connection.classList.add('neural-activity');
        setTimeout(() => {
          connection.classList.remove('neural-activity');
        }, 1500);
      }
    });
  }
  
  /**
   * Anime l'activité du cerveau
   */
  function animateBrainActivity() {
    brainRegions.forEach(region => {
      const regionId = region.dataset.region;
      
      // Animer en fonction de l'activité
      if (memoryData && memoryData[regionId]) {
        const activity = memoryData[regionId].length || 0;
        const scale = 1 + Math.min(activity / 10, 0.3);
        region.style.transform = `scale(${scale})`;
        
        // Ajouter un effet de pulsation pour les régions actives
        if (activity > 0) {
          region.classList.add('active');
        } else {
          region.classList.remove('active');
        }
      }
      
      // Effet spécial pour Kyber
      if (regionId === 'kyber' && kyberEnabled) {
        region.classList.add('kyber-pulse');
      } else if (regionId === 'kyber') {
        region.classList.remove('kyber-pulse');
      }
    });
  }
  
  /**
   * Met à jour les statistiques de mémoire
   */
  function updateMemoryStats() {
    if (!memoryData) return;
    
    updateMemoryStat('instant-memory', memoryData.instantMemory);
    updateMemoryStat('short-term', memoryData.shortTerm);
    updateMemoryStat('working-memory', memoryData.workingMemory);
    updateMemoryStat('medium-term', memoryData.mediumTerm);
    updateMemoryStat('long-term', memoryData.longTerm);
    updateMemoryStat('dream-memory', memoryData.dreamMemory);
  }
  
  /**
   * Met à jour une statistique de mémoire spécifique
   */
  function updateMemoryStat(id, data) {
    const bar = document.getElementById(`${id}-bar`);
    const count = document.getElementById(`${id}-count`);
    
    if (bar && count && data) {
      const items = Array.isArray(data) ? data.length : 0;
      const percentage = Math.min(items * 10, 100);
      
      bar.style.width = `${percentage}%`;
      count.textContent = `${items} élément${items > 1 ? 's' : ''}`;
    }
  }
  
  /**
   * Met à jour l'état du cerveau
   */
  function updateBrainState(state) {
    if (!state) return;
    
    // Mettre à jour l'état de Kyber
    if (state.kyber) {
      kyberEnabled = state.kyber.enabled || false;
      kyberLocked = state.kyber.locked || false;
      
      updateKyberUI();
    }
  }
  
  /**
   * Met à jour l'interface utilisateur de Kyber
   */
  function updateKyberUI() {
    const kyberEnabledCheckbox = document.getElementById('kyber-enabled');
    const kyberFactorSlider = document.getElementById('kyber-factor');
    const kyberTemperatureSlider = document.getElementById('kyber-temperature');
    const kyberStabilitySlider = document.getElementById('kyber-stability');
    const lockKyberBtn = document.getElementById('lock-kyber-btn');
    
    if (kyberEnabledCheckbox) {
      kyberEnabledCheckbox.checked = kyberEnabled;
    }
    
    if (lockKyberBtn) {
      if (kyberLocked) {
        lockKyberBtn.innerHTML = '<i class="bi bi-lock me-2"></i>Déverrouiller';
      } else {
        lockKyberBtn.innerHTML = '<i class="bi bi-unlock me-2"></i>Verrouiller';
      }
    }
    
    // Désactiver les contrôles si Kyber est verrouillé
    if (kyberFactorSlider) kyberFactorSlider.disabled = kyberLocked;
    if (kyberTemperatureSlider) kyberTemperatureSlider.disabled = kyberLocked;
    if (kyberStabilitySlider) kyberStabilitySlider.disabled = kyberLocked;
  }
  
  /**
   * Sauvegarde les paramètres de Kyber
   */
  function saveKyberSettings() {
    const kyberEnabledCheckbox = document.getElementById('kyber-enabled');
    const kyberFactorSlider = document.getElementById('kyber-factor');
    const kyberTemperatureSlider = document.getElementById('kyber-temperature');
    const kyberStabilitySlider = document.getElementById('kyber-stability');
    
    const settings = {
      enabled: kyberEnabledCheckbox ? kyberEnabledCheckbox.checked : false,
      factor: kyberFactorSlider ? parseFloat(kyberFactorSlider.value) : 1.0,
      temperature: kyberTemperatureSlider ? parseFloat(kyberTemperatureSlider.value) : 0.7,
      stability: kyberStabilitySlider ? parseFloat(kyberStabilitySlider.value) : 0.5,
      locked: kyberLocked
    };
    
    console.log('Saving Kyber settings:', settings);
    
    if (socket && socket.connected) {
      socket.emit('update kyber', settings);
    }
  }
  
  /**
   * Verrouille/déverrouille l'accélérateur Kyber
   */
  function toggleKyberLock() {
    kyberLocked = !kyberLocked;
    updateKyberUI();
    saveKyberSettings();
  }
  
  /**
   * Affiche les détails de la mémoire
   */
  function showMemoryDetails(regionId) {
    console.log(`Showing memory details for ${regionId}`);
    
    // Implémenter l'affichage des détails de la mémoire
    // Cette fonction pourrait ouvrir une modal ou mettre à jour une section de la page
  }
  
  /**
   * Met à jour le statut d'Ollama
   */
  function updateOllamaStatus(isRunning) {
    const statusElement = document.getElementById('ollama-status-nav');
    
    if (statusElement) {
      if (isRunning) {
        statusElement.innerHTML = '<div class="d-flex align-items-center"><div class="bg-success rounded-circle me-2" style="width: 10px; height: 10px;"></div><small class="text-light">Ollama connecté</small></div>';
      } else {
        statusElement.innerHTML = '<div class="d-flex align-items-center"><div class="bg-danger rounded-circle me-2" style="width: 10px; height: 10px;"></div><small class="text-light">Ollama déconnecté</small></div>';
      }
    }
  }
});
