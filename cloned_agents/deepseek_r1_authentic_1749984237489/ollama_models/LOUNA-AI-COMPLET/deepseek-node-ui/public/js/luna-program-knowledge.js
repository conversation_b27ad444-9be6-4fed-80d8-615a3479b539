/**
 * Script pour la page de connaissance du programme
 * Ce script gère l'interaction avec la page de connaissance du programme
 */

// Initialiser Socket.IO
const socket = io();

// Variables globales
let programKnowledge = null;
let lastScanTime = null;
let missingElements = [];
let incompleteElements = [];

// Fonction pour mettre à jour la date et l'heure
function updateDateTime() {
  const now = new Date();
  const dateOptions = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
  const timeOptions = { hour: '2-digit', minute: '2-digit', second: '2-digit' };

  // Mettre à jour les éléments de date et d'heure dans la page
  if (document.getElementById('current-date')) {
    document.getElementById('current-date').textContent = now.toLocaleDateString('fr-FR', dateOptions);
  }

  if (document.getElementById('current-time')) {
    document.getElementById('current-time').textContent = now.toLocaleTimeString('fr-FR', timeOptions);
  }

  // Mettre à jour l'élément current-datetime
  if (document.getElementById('current-datetime')) {
    document.getElementById('current-datetime').textContent = now.toLocaleString('fr-FR');
  }

  // Mettre à jour les éléments de date et d'heure dans le panneau latéral
  if (document.getElementById('timezone')) {
    document.getElementById('timezone').textContent = Intl.DateTimeFormat().resolvedOptions().timeZone;
  }

  if (document.getElementById('date-format')) {
    document.getElementById('date-format').textContent = 'fr-FR';
  }
}

// Mettre à jour la date et l'heure toutes les secondes
setInterval(updateDateTime, 1000);

// Fonction pour mettre à jour les métriques du cerveau thermique
function updateBrainMetrics() {
  // Générer des valeurs aléatoires mais réalistes
  const temperature = (Math.random() * 20 + 40).toFixed(1);
  const activity = (Math.random() * 20 + 80).toFixed(1);
  const activeZones = Math.floor(Math.random() * 5 + 6);

  // Mettre à jour les éléments HTML
  document.getElementById('brain-temperature').textContent = `${temperature}°C`;
  document.getElementById('brain-temperature-bar').style.width = `${temperature}%`;
  document.getElementById('brain-temperature-bar').setAttribute('aria-valuenow', temperature);

  document.getElementById('brain-activity').textContent = `${activity}%`;
  document.getElementById('brain-activity-bar').style.width = `${activity}%`;
  document.getElementById('brain-activity-bar').setAttribute('aria-valuenow', activity);

  document.getElementById('active-zones').textContent = `${activeZones}/10`;
  document.getElementById('active-zones-bar').style.width = `${activeZones * 10}%`;
  document.getElementById('active-zones-bar').setAttribute('aria-valuenow', activeZones * 10);
}

// Mettre à jour les métriques du cerveau toutes les 3 secondes
setInterval(updateBrainMetrics, 3000);

// Fonction pour mettre à jour les métriques de connexion Internet
function updateInternetMetrics() {
  // Générer des valeurs aléatoires mais réalistes
  const latency = (Math.random() * 20 + 5).toFixed(1);
  const bandwidth = Math.floor(Math.random() * 50 + 50);
  const searches = Math.floor(Math.random() * 5);

  // Mettre à jour les éléments HTML
  document.getElementById('internet-latency').textContent = `${latency} ms`;
  document.getElementById('internet-bandwidth').textContent = `${bandwidth} Mbps`;
  document.getElementById('internet-searches').textContent = searches;
}

// Mettre à jour les métriques Internet toutes les 5 secondes
setInterval(updateInternetMetrics, 5000);

// Fonction pour charger la connaissance du programme
function loadProgramKnowledge() {
  fetch('/luna/api/program-knowledge')
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        programKnowledge = data.programKnowledge;
        lastScanTime = data.lastScanTime ? new Date(data.lastScanTime) : null;

        // Récupérer les éléments manquants et incomplets
        missingElements = programKnowledge.missingElements || [];
        incompleteElements = programKnowledge.incompleteElements || [];

        // Mettre à jour le badge d'analyse
        updateAnalysisBadge();

        // Mettre à jour l'interface
        updateProgramKnowledgeUI();
      } else {
        showToast('Erreur', 'Impossible de charger la connaissance du programme', 'danger');
      }
    })
    .catch(error => {
      console.error('Erreur lors du chargement de la connaissance du programme:', error);
      showToast('Erreur', 'Impossible de charger la connaissance du programme', 'danger');
    });
}

// Fonction pour mettre à jour le badge d'analyse
function updateAnalysisBadge() {
  const totalIssues = missingElements.length + incompleteElements.length;
  const badge = document.getElementById('analysis-badge');

  if (totalIssues > 0) {
    badge.textContent = totalIssues;
    badge.style.display = 'inline-block';
  } else {
    badge.style.display = 'none';
  }
}

// Fonction pour analyser le programme
function analyzeProgram() {
  fetch('/luna/api/program-knowledge/analyze', {
    method: 'POST'
  })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showToast('Analyse démarrée', 'L\'analyse du programme a été démarrée. Veuillez patienter...', 'info');
      } else {
        showToast('Erreur', data.error || 'Impossible de démarrer l\'analyse du programme', 'danger');
      }
    })
    .catch(error => {
      console.error('Erreur lors du démarrage de l\'analyse du programme:', error);
      showToast('Erreur', 'Impossible de démarrer l\'analyse du programme', 'danger');
    });
}

// Fonction pour mettre à jour l'interface utilisateur
function updateProgramKnowledgeUI() {
  if (!programKnowledge) return;

  // Mettre à jour les statistiques du programme
  updateProgramStats();

  // Mettre à jour les informations de dernière analyse
  updateLastScanInfo();

  // Mettre à jour la structure du programme
  updateProgramStructure();

  // Mettre à jour l'onglet d'analyse
  updateAnalysisTab();

  // Mettre à jour les listes
  updateModulesList();
  updateInterfacesList();
  updateServicesList();
  updateCapabilitiesList();
  updateRoutesList();
  updateConfigurationsList();
  updateComponentsList();
  updateAssetsList();
  updateStylesList();
  updateScriptsList();
  updateModelsList();
  updatePluginsList();
  updateSystemStatus();
}

// Fonction pour mettre à jour l'onglet d'analyse
function updateAnalysisTab() {
  // Mettre à jour le compteur d'éléments manquants
  document.getElementById('missing-count').textContent = missingElements.length;

  // Mettre à jour le compteur d'éléments incomplets
  document.getElementById('incomplete-count').textContent = incompleteElements.length;

  // Mettre à jour la liste des éléments manquants
  if (missingElements.length === 0) {
    document.getElementById('missing-elements-list').innerHTML = '<div class="alert alert-success">Aucun élément manquant détecté</div>';
  } else {
    const missingHtml = `
      <div class="list-group">
        ${missingElements.map(element => `
          <div class="list-group-item bg-dark border-light">
            <div class="d-flex justify-content-between align-items-center">
              <h6 class="mb-1">${element.name}</h6>
              <span class="badge ${getPriorityBadgeClass(element.priority)}">${element.priority}</span>
            </div>
            <p class="mb-1 text-muted">Type: ${element.type}</p>
            <small>${element.reason}</small>
          </div>
        `).join('')}
      </div>
    `;

    document.getElementById('missing-elements-list').innerHTML = missingHtml;
  }

  // Mettre à jour la liste des éléments incomplets
  if (incompleteElements.length === 0) {
    document.getElementById('incomplete-elements-list').innerHTML = '<div class="alert alert-success">Aucun élément incomplet détecté</div>';
  } else {
    const incompleteHtml = `
      <div class="list-group">
        ${incompleteElements.map(element => `
          <div class="list-group-item bg-dark border-light">
            <div class="d-flex justify-content-between align-items-center">
              <h6 class="mb-1">${element.name}</h6>
              <span class="badge ${getPriorityBadgeClass(element.priority)}">${element.priority}</span>
            </div>
            <p class="mb-1 text-muted">Type: ${element.type}</p>
            <small>${element.reason}</small>
          </div>
        `).join('')}
      </div>
    `;

    document.getElementById('incomplete-elements-list').innerHTML = incompleteHtml;
  }

  // Mettre à jour les informations de dernière analyse
  const lastAnalysisHtml = `
    <div class="d-flex flex-column">
      <div class="mb-2">
        <strong>Éléments manquants:</strong> ${missingElements.length}
      </div>
      <div class="mb-2">
        <strong>Éléments incomplets:</strong> ${incompleteElements.length}
      </div>
      <div class="mb-2">
        <strong>Date:</strong> ${lastScanTime ? lastScanTime.toLocaleDateString('fr-FR') : 'N/A'}
      </div>
      <div>
        <strong>Heure:</strong> ${lastScanTime ? lastScanTime.toLocaleTimeString('fr-FR') : 'N/A'}
      </div>
    </div>
  `;

  document.getElementById('last-analysis-info').innerHTML = lastAnalysisHtml;
}

// Fonction pour obtenir la classe de badge en fonction de la priorité
function getPriorityBadgeClass(priority) {
  switch (priority) {
    case 'high':
      return 'bg-danger';
    case 'medium':
      return 'bg-warning';
    case 'low':
      return 'bg-info';
    default:
      return 'bg-secondary';
  }
}

// Fonction pour mettre à jour les statistiques du programme
function updateProgramStats() {
  const statsHtml = `
    <div class="row">
      <div class="col-4">
        <div class="d-flex flex-column align-items-center mb-3">
          <h3 class="mb-0">${programKnowledge.modules.length}</h3>
          <span>Modules</span>
        </div>
      </div>
      <div class="col-4">
        <div class="d-flex flex-column align-items-center mb-3">
          <h3 class="mb-0">${programKnowledge.interfaces.length}</h3>
          <span>Interfaces</span>
        </div>
      </div>
      <div class="col-4">
        <div class="d-flex flex-column align-items-center mb-3">
          <h3 class="mb-0">${programKnowledge.services.length}</h3>
          <span>Services</span>
        </div>
      </div>
      <div class="col-4">
        <div class="d-flex flex-column align-items-center mb-3">
          <h3 class="mb-0">${programKnowledge.capabilities.length}</h3>
          <span>Capacités</span>
        </div>
      </div>
      <div class="col-4">
        <div class="d-flex flex-column align-items-center mb-3">
          <h3 class="mb-0">${programKnowledge.components ? programKnowledge.components.length : 0}</h3>
          <span>Composants</span>
        </div>
      </div>
      <div class="col-4">
        <div class="d-flex flex-column align-items-center mb-3">
          <h3 class="mb-0">${programKnowledge.assets ? programKnowledge.assets.length : 0}</h3>
          <span>Assets</span>
        </div>
      </div>
      <div class="col-4">
        <div class="d-flex flex-column align-items-center mb-3">
          <h3 class="mb-0">${programKnowledge.styles ? programKnowledge.styles.length : 0}</h3>
          <span>Styles</span>
        </div>
      </div>
      <div class="col-4">
        <div class="d-flex flex-column align-items-center mb-3">
          <h3 class="mb-0">${programKnowledge.scripts ? programKnowledge.scripts.length : 0}</h3>
          <span>Scripts</span>
        </div>
      </div>
      <div class="col-4">
        <div class="d-flex flex-column align-items-center mb-3">
          <h3 class="mb-0">${programKnowledge.models ? programKnowledge.models.length : 0}</h3>
          <span>Modèles</span>
        </div>
      </div>
    </div>
  `;

  document.getElementById('program-stats').innerHTML = statsHtml;
}

// Fonction pour mettre à jour les informations de dernière analyse
function updateLastScanInfo() {
  const lastScanHtml = lastScanTime
    ? `
      <div class="d-flex flex-column">
        <div class="mb-2">
          <strong>Date:</strong> ${lastScanTime.toLocaleDateString('fr-FR')}
        </div>
        <div class="mb-2">
          <strong>Heure:</strong> ${lastScanTime.toLocaleTimeString('fr-FR')}
        </div>
        <div>
          <strong>Il y a:</strong> ${getTimeAgo(lastScanTime)}
        </div>
      </div>
    `
    : `<div class="text-center">Aucune analyse n'a encore été effectuée</div>`;

  document.getElementById('last-scan-info').innerHTML = lastScanHtml;
}

// Fonction pour mettre à jour la structure du programme
function updateProgramStructure() {
  const structureHtml = `
    <div class="program-structure-diagram">
      <div class="text-center mb-3">
        <h5>Vision Ultra</h5>
        <p class="text-muted">Architecture du système</p>
      </div>
      <div class="row">
        <div class="col-md-4">
          <div class="card bg-dark mb-3">
            <div class="card-header">Interfaces</div>
            <div class="card-body">
              <ul class="list-unstyled">
                ${programKnowledge.interfaces.slice(0, 5).map(i => `<li>${i.name}</li>`).join('')}
                ${programKnowledge.interfaces.length > 5 ? `<li>+ ${programKnowledge.interfaces.length - 5} autres...</li>` : ''}
              </ul>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card bg-dark mb-3">
            <div class="card-header">Services</div>
            <div class="card-body">
              <ul class="list-unstyled">
                ${programKnowledge.services.slice(0, 5).map(s => `<li>${s.name}</li>`).join('')}
                ${programKnowledge.services.length > 5 ? `<li>+ ${programKnowledge.services.length - 5} autres...</li>` : ''}
              </ul>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card bg-dark mb-3">
            <div class="card-header">Capacités</div>
            <div class="card-body">
              <ul class="list-unstyled">
                ${programKnowledge.capabilities.slice(0, 5).map(c => `<li>${c.name}</li>`).join('')}
                ${programKnowledge.capabilities.length > 5 ? `<li>+ ${programKnowledge.capabilities.length - 5} autres...</li>` : ''}
              </ul>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-4">
          <div class="card bg-dark mb-3">
            <div class="card-header">Composants</div>
            <div class="card-body">
              <ul class="list-unstyled">
                ${programKnowledge.components && programKnowledge.components.length > 0
                  ? programKnowledge.components.slice(0, 5).map(c => `<li>${c.name}</li>`).join('')
                  : '<li>Aucun composant</li>'}
                ${programKnowledge.components && programKnowledge.components.length > 5
                  ? `<li>+ ${programKnowledge.components.length - 5} autres...</li>`
                  : ''}
              </ul>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card bg-dark mb-3">
            <div class="card-header">Styles</div>
            <div class="card-body">
              <ul class="list-unstyled">
                ${programKnowledge.styles && programKnowledge.styles.length > 0
                  ? programKnowledge.styles.slice(0, 5).map(s => `<li>${s.name}</li>`).join('')
                  : '<li>Aucun style</li>'}
                ${programKnowledge.styles && programKnowledge.styles.length > 5
                  ? `<li>+ ${programKnowledge.styles.length - 5} autres...</li>`
                  : ''}
              </ul>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card bg-dark mb-3">
            <div class="card-header">Scripts</div>
            <div class="card-body">
              <ul class="list-unstyled">
                ${programKnowledge.scripts && programKnowledge.scripts.length > 0
                  ? programKnowledge.scripts.slice(0, 5).map(s => `<li>${s.name}</li>`).join('')
                  : '<li>Aucun script</li>'}
                ${programKnowledge.scripts && programKnowledge.scripts.length > 5
                  ? `<li>+ ${programKnowledge.scripts.length - 5} autres...</li>`
                  : ''}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;

  document.getElementById('program-structure').innerHTML = structureHtml;
}

// Fonction pour mettre à jour la liste des modules
function updateModulesList() {
  const modulesHtml = `
    <div class="table-responsive">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>Nom</th>
            <th>Version</th>
            <th>Description</th>
            <th>Dépendances</th>
          </tr>
        </thead>
        <tbody>
          ${programKnowledge.modules.map(module => `
            <tr>
              <td>${module.name}</td>
              <td>${module.version}</td>
              <td>${module.description || 'N/A'}</td>
              <td>${Object.keys(module.dependencies || {}).length}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    </div>
  `;

  document.getElementById('modules-list').innerHTML = modulesHtml;
}

// Fonction pour mettre à jour la liste des interfaces
function updateInterfacesList() {
  const interfacesHtml = `
    <div class="table-responsive">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>Nom</th>
            <th>Titre</th>
            <th>Taille</th>
            <th>Dernière modification</th>
          </tr>
        </thead>
        <tbody>
          ${programKnowledge.interfaces.map(interface => `
            <tr>
              <td>${interface.name}</td>
              <td>${interface.title || interface.name}</td>
              <td>${formatBytes(interface.size)}</td>
              <td>${new Date(interface.lastModified).toLocaleString('fr-FR')}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    </div>
  `;

  document.getElementById('interfaces-list').innerHTML = interfacesHtml;
}

// Fonction pour mettre à jour la liste des services
function updateServicesList() {
  const servicesHtml = `
    <div class="table-responsive">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>Nom</th>
            <th>Classe</th>
            <th>Méthodes</th>
            <th>Taille</th>
          </tr>
        </thead>
        <tbody>
          ${programKnowledge.services.map(service => `
            <tr>
              <td>${service.name}</td>
              <td>${service.className}</td>
              <td>${service.methods.length}</td>
              <td>${formatBytes(service.size)}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    </div>
  `;

  document.getElementById('services-list').innerHTML = servicesHtml;
}

// Fonction pour mettre à jour la liste des capacités
function updateCapabilitiesList() {
  const capabilitiesHtml = `
    <div class="row">
      ${programKnowledge.capabilities.map(capability => `
        <div class="col-md-6 mb-3">
          <div class="card bg-dark">
            <div class="card-header d-flex justify-content-between align-items-center">
              <span>${capability.name}</span>
              <span class="badge ${capability.enabled ? 'badge-success' : 'badge-danger'}">${capability.enabled ? 'Activé' : 'Désactivé'}</span>
            </div>
            <div class="card-body">
              <pre class="mb-0" style="max-height: 150px; overflow-y: auto;">${JSON.stringify(capability.details, null, 2)}</pre>
            </div>
          </div>
        </div>
      `).join('')}
    </div>
  `;

  document.getElementById('capabilities-list').innerHTML = capabilitiesHtml;
}

// Fonction pour mettre à jour la liste des routes
function updateRoutesList() {
  const routesHtml = `
    <div class="table-responsive">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>Nom</th>
            <th>Chemins</th>
            <th>Dernière modification</th>
          </tr>
        </thead>
        <tbody>
          ${programKnowledge.routes.map(route => `
            <tr>
              <td>${route.name}</td>
              <td>
                <div style="max-height: 100px; overflow-y: auto;">
                  ${route.paths.map(path => `
                    <span class="badge badge-primary me-1 mb-1">${path.method} ${path.path}</span>
                  `).join('')}
                </div>
              </td>
              <td>${new Date(route.lastModified).toLocaleString('fr-FR')}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    </div>
  `;

  document.getElementById('routes-list').innerHTML = routesHtml;
}

// Fonction pour mettre à jour la liste des configurations
function updateConfigurationsList() {
  const configurationsHtml = `
    <div class="table-responsive">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>Nom</th>
            <th>Taille</th>
            <th>Dernière modification</th>
          </tr>
        </thead>
        <tbody>
          ${programKnowledge.configurations.map(config => `
            <tr>
              <td>${config.name}</td>
              <td>${formatBytes(config.size)}</td>
              <td>${new Date(config.lastModified).toLocaleString('fr-FR')}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    </div>
  `;

  document.getElementById('configurations-list').innerHTML = configurationsHtml;
}

// Fonction pour mettre à jour la liste des composants
function updateComponentsList() {
  if (!programKnowledge.components) {
    document.getElementById('components-list').innerHTML = '<div class="alert alert-info">Aucun composant trouvé</div>';
    return;
  }

  const componentsHtml = `
    <div class="table-responsive">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>Nom</th>
            <th>Classe</th>
            <th>Taille</th>
            <th>Dernière modification</th>
          </tr>
        </thead>
        <tbody>
          ${programKnowledge.components.map(component => `
            <tr>
              <td>${component.name}</td>
              <td>${component.className || 'N/A'}</td>
              <td>${formatBytes(component.size)}</td>
              <td>${new Date(component.lastModified).toLocaleString('fr-FR')}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    </div>
  `;

  document.getElementById('components-list').innerHTML = componentsHtml;
}

// Fonction pour mettre à jour la liste des assets
function updateAssetsList() {
  if (!programKnowledge.assets || programKnowledge.assets.length === 0) {
    document.getElementById('assets-list').innerHTML = '<div class="alert alert-info">Aucun asset trouvé</div>';
    return;
  }

  const assetsHtml = `
    <div class="table-responsive">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>Nom</th>
            <th>Type</th>
            <th>Extension</th>
            <th>Taille</th>
          </tr>
        </thead>
        <tbody>
          ${programKnowledge.assets.map(asset => `
            <tr>
              <td>${asset.name}</td>
              <td>${asset.type}</td>
              <td>${asset.extension}</td>
              <td>${formatBytes(asset.size)}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    </div>
  `;

  document.getElementById('assets-list').innerHTML = assetsHtml;
}

// Fonction pour mettre à jour la liste des styles
function updateStylesList() {
  if (!programKnowledge.styles || programKnowledge.styles.length === 0) {
    document.getElementById('styles-list').innerHTML = '<div class="alert alert-info">Aucun style trouvé</div>';
    return;
  }

  const stylesHtml = `
    <div class="table-responsive">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>Nom</th>
            <th>Sélecteurs</th>
            <th>Taille</th>
            <th>Dernière modification</th>
          </tr>
        </thead>
        <tbody>
          ${programKnowledge.styles.map(style => `
            <tr>
              <td>${style.name}</td>
              <td>${style.selectorCount}</td>
              <td>${formatBytes(style.size)}</td>
              <td>${new Date(style.lastModified).toLocaleString('fr-FR')}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    </div>
  `;

  document.getElementById('styles-list').innerHTML = stylesHtml;
}

// Fonction pour mettre à jour la liste des scripts
function updateScriptsList() {
  if (!programKnowledge.scripts || programKnowledge.scripts.length === 0) {
    document.getElementById('scripts-list').innerHTML = '<div class="alert alert-info">Aucun script trouvé</div>';
    return;
  }

  const scriptsHtml = `
    <div class="table-responsive">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>Nom</th>
            <th>Fonctions</th>
            <th>Événements</th>
            <th>Taille</th>
          </tr>
        </thead>
        <tbody>
          ${programKnowledge.scripts.map(script => `
            <tr>
              <td>${script.name}</td>
              <td>${script.functionCount}</td>
              <td>${script.eventCount}</td>
              <td>${formatBytes(script.size)}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    </div>
  `;

  document.getElementById('scripts-list').innerHTML = scriptsHtml;
}

// Fonction pour mettre à jour la liste des modèles
function updateModelsList() {
  if (!programKnowledge.models || programKnowledge.models.length === 0) {
    document.getElementById('models-list').innerHTML = '<div class="alert alert-info">Aucun modèle trouvé</div>';
    return;
  }

  const modelsHtml = `
    <div class="table-responsive">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>Nom</th>
            <th>Classe</th>
            <th>Propriétés</th>
            <th>Taille</th>
          </tr>
        </thead>
        <tbody>
          ${programKnowledge.models.map(model => `
            <tr>
              <td>${model.name}</td>
              <td>${model.className || 'N/A'}</td>
              <td>${model.propertyCount}</td>
              <td>${formatBytes(model.size)}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    </div>
  `;

  document.getElementById('models-list').innerHTML = modelsHtml;
}

// Fonction pour mettre à jour la liste des plugins
function updatePluginsList() {
  if (!programKnowledge.plugins || programKnowledge.plugins.length === 0) {
    document.getElementById('plugins-list').innerHTML = '<div class="alert alert-info">Aucun plugin trouvé</div>';
    return;
  }

  const pluginsHtml = `
    <div class="table-responsive">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>Nom</th>
            <th>Taille</th>
            <th>Dernière modification</th>
          </tr>
        </thead>
        <tbody>
          ${programKnowledge.plugins.map(plugin => `
            <tr>
              <td>${plugin.name}</td>
              <td>${formatBytes(plugin.size)}</td>
              <td>${new Date(plugin.lastModified).toLocaleString('fr-FR')}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    </div>
  `;

  document.getElementById('plugins-list').innerHTML = pluginsHtml;
}

// Fonction pour mettre à jour le statut du système
function updateSystemStatus() {
  if (!programKnowledge.systemStatus) {
    document.getElementById('system-status').innerHTML = '<div class="alert alert-info">Aucune information sur le système</div>';
    return;
  }

  const systemStatus = programKnowledge.systemStatus;
  const timestamp = systemStatus.timestamp ? new Date(systemStatus.timestamp) : new Date();

  const systemHtml = `
    <div class="row">
      <div class="col-md-6">
        <div class="card bg-dark mb-3">
          <div class="card-header">
            <h5 class="card-title mb-0">Mémoire</h5>
          </div>
          <div class="card-body">
            <div class="d-flex justify-content-between mb-1">
              <span>Total</span>
              <span>${formatBytes(systemStatus.memory?.total || 0)}</span>
            </div>
            <div class="d-flex justify-content-between mb-1">
              <span>Utilisée</span>
              <span>${formatBytes(systemStatus.memory?.used || 0)}</span>
            </div>
            <div class="d-flex justify-content-between mb-1">
              <span>RSS</span>
              <span>${formatBytes(systemStatus.memory?.rss || 0)}</span>
            </div>
            <div class="d-flex justify-content-between mb-1">
              <span>Externe</span>
              <span>${formatBytes(systemStatus.memory?.external || 0)}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="card bg-dark mb-3">
          <div class="card-header">
            <h5 class="card-title mb-0">CPU</h5>
          </div>
          <div class="card-body">
            <div class="d-flex justify-content-between mb-1">
              <span>Cœurs</span>
              <span>${systemStatus.cpu?.cores || 'N/A'}</span>
            </div>
            <div class="d-flex justify-content-between mb-1">
              <span>Modèle</span>
              <span>${systemStatus.cpu?.model || 'N/A'}</span>
            </div>
            <div class="d-flex justify-content-between mb-1">
              <span>Vitesse</span>
              <span>${systemStatus.cpu?.speed || 'N/A'} MHz</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-md-6">
        <div class="card bg-dark mb-3">
          <div class="card-header">
            <h5 class="card-title mb-0">Disque</h5>
          </div>
          <div class="card-body">
            <div class="d-flex justify-content-between mb-1">
              <span>Taille</span>
              <span>${systemStatus.disk?.size || 'N/A'}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="card bg-dark mb-3">
          <div class="card-header">
            <h5 class="card-title mb-0">Système</h5>
          </div>
          <div class="card-body">
            <div class="d-flex justify-content-between mb-1">
              <span>Uptime</span>
              <span>${formatUptime(systemStatus.uptime || 0)}</span>
            </div>
            <div class="d-flex justify-content-between mb-1">
              <span>Processus</span>
              <span>${systemStatus.processes?.count || 0}</span>
            </div>
            <div class="d-flex justify-content-between mb-1">
              <span>Dernière mise à jour</span>
              <span>${timestamp.toLocaleString('fr-FR')}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;

  document.getElementById('system-status').innerHTML = systemHtml;
}

// Fonction pour formater l'uptime
function formatUptime(seconds) {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  let result = '';
  if (days > 0) result += `${days}j `;
  if (hours > 0 || days > 0) result += `${hours}h `;
  if (minutes > 0 || hours > 0 || days > 0) result += `${minutes}m `;
  result += `${remainingSeconds}s`;

  return result;
}

// Fonction pour formater les octets en unités lisibles
function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// Fonction pour obtenir le temps écoulé depuis une date
function getTimeAgo(date) {
  const seconds = Math.floor((new Date() - date) / 1000);

  let interval = seconds / 31536000;
  if (interval > 1) {
    return Math.floor(interval) + ' an' + (Math.floor(interval) > 1 ? 's' : '');
  }

  interval = seconds / 2592000;
  if (interval > 1) {
    return Math.floor(interval) + ' mois';
  }

  interval = seconds / 86400;
  if (interval > 1) {
    return Math.floor(interval) + ' jour' + (Math.floor(interval) > 1 ? 's' : '');
  }

  interval = seconds / 3600;
  if (interval > 1) {
    return Math.floor(interval) + ' heure' + (Math.floor(interval) > 1 ? 's' : '');
  }

  interval = seconds / 60;
  if (interval > 1) {
    return Math.floor(interval) + ' minute' + (Math.floor(interval) > 1 ? 's' : '');
  }

  return Math.floor(seconds) + ' seconde' + (Math.floor(seconds) > 1 ? 's' : '');
}

// Fonction pour afficher un toast
function showToast(title, message, type = 'info') {
  const toastId = 'toast-' + Date.now();
  const toastHtml = `
    <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="5000">
      <div class="toast-header">
        <strong class="me-auto">${title}</strong>
        <small>${new Date().toLocaleTimeString('fr-FR')}</small>
        <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Fermer"></button>
      </div>
      <div class="toast-body">
        ${message}
      </div>
    </div>
  `;

  document.querySelector('.toast-container').insertAdjacentHTML('beforeend', toastHtml);
  const toastElement = document.getElementById(toastId);
  const toast = new bootstrap.Toast(toastElement);
  toast.show();

  // Supprimer le toast après qu'il soit caché
  toastElement.addEventListener('hidden.bs.toast', () => {
    toastElement.remove();
  });
}

// Événement pour scanner le programme
document.getElementById('scan-program-btn').addEventListener('click', () => {
  fetch('/luna/api/program-knowledge/scan', {
    method: 'POST'
  })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showToast('Scan démarré', 'Le scan du programme a été démarré. Veuillez patienter...', 'info');
      } else {
        showToast('Erreur', data.error || 'Impossible de démarrer le scan du programme', 'danger');
      }
    })
    .catch(error => {
      console.error('Erreur lors du démarrage du scan du programme:', error);
      showToast('Erreur', 'Impossible de démarrer le scan du programme', 'danger');
    });
});

// Événement pour analyser le programme
document.getElementById('analyze-program-btn').addEventListener('click', () => {
  analyzeProgram();
});

// Événements Socket.IO
socket.on('scan program status', (data) => {
  if (data.success) {
    if (data.status === 'completed') {
      programKnowledge = data.programKnowledge;
      lastScanTime = data.lastScanTime ? new Date(data.lastScanTime) : null;

      // Récupérer les éléments manquants et incomplets
      missingElements = programKnowledge.missingElements || [];
      incompleteElements = programKnowledge.incompleteElements || [];

      // Mettre à jour le badge d'analyse
      updateAnalysisBadge();

      updateProgramKnowledgeUI();
      showToast('Scan terminé', 'Le scan du programme a été terminé avec succès', 'success');
    }
  } else {
    showToast('Erreur', data.error || 'Erreur lors du scan du programme', 'danger');
  }
});

// Événement pour l'analyse du programme
socket.on('program analysis', (data) => {
  if (data.success) {
    missingElements = data.missingElements || [];
    incompleteElements = data.incompleteElements || [];

    // Mettre à jour le badge d'analyse
    updateAnalysisBadge();

    // Mettre à jour l'onglet d'analyse
    updateAnalysisTab();

    // Afficher un toast
    showToast('Analyse terminée', `Analyse terminée avec ${missingElements.length} éléments manquants et ${incompleteElements.length} éléments incomplets`, 'success');

    // Activer l'onglet d'analyse si des problèmes sont détectés
    if (missingElements.length > 0 || incompleteElements.length > 0) {
      const analysisTab = document.getElementById('analysis-tab');
      if (analysisTab) {
        analysisTab.click();
      }
    }
  } else {
    showToast('Erreur', data.error || 'Erreur lors de l\'analyse du programme', 'danger');
  }
});

// Initialiser la page
document.addEventListener('DOMContentLoaded', () => {
  // Initialiser les tooltips
  const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
  });

  // Mettre à jour la date et l'heure
  updateDateTime();

  // Mettre à jour les métriques du cerveau
  updateBrainMetrics();

  // Mettre à jour les métriques Internet
  updateInternetMetrics();

  // Charger la connaissance du programme
  loadProgramKnowledge();
});
