/**
 * Luna Accelerators - Gestion des accélérateurs Kyber pour l'interface Luna
 * Permet de configurer et optimiser les accélérateurs de traitement
 */

/**
 * Met à jour la date et l'heure dans l'interface
 */
function updateDateTime() {
  const now = new Date();

  // Mettre à jour l'élément current-datetime
  const datetimeElement = document.getElementById('current-datetime');
  if (datetimeElement) {
    datetimeElement.textContent = now.toLocaleString('fr-FR');
  }
}

// Initialiser la connexion Socket.IO (si pas déjà initialisée par luna-accelerators-hierarchy.js)
let socket;
if (typeof socket === 'undefined') {
  socket = io();
}

// État global des accélérateurs
const acceleratorsState = {
  accelerators: [],
  stats: {
    avgEfficiency: 0.84,
    totalThroughput: 3.02,
    cpuUsage: 42
  },
  distribution: {
    memory: 3,
    thermal: 3,
    reflection: 3,
    zones: 6
  }
};

// Référence au graphique de répartition
let typesChart = null;

// Événements Socket.IO
socket.on('connect', () => {
  console.log('🔌 Connecté au serveur Luna');
  loadAccelerators();
});

socket.on('disconnect', () => {
  console.log('🔌 Déconnecté du serveur Luna');
  showNotification('Connexion au serveur perdue. Tentative de reconnexion...', 'error');
});

// Recevoir la liste des accélérateurs
socket.on('accelerators list', (data) => {
  if (data.success) {
    acceleratorsState.accelerators = data.accelerators;

    // Mettre à jour l'interface
    updateAcceleratorsUI();
  }
});

// Recevoir les statistiques des accélérateurs
socket.on('accelerators stats', (data) => {
  if (data.success) {
    acceleratorsState.stats = data.stats;
    acceleratorsState.distribution = data.distribution;

    // Mettre à jour l'interface
    updateStatsUI();
    updateDistributionChart();
  }
});

// Recevoir la confirmation d'ajout d'accélérateur
socket.on('accelerator added', (data) => {
  if (data.success) {
    showNotification(`Accélérateur ${data.accelerator.id} ajouté avec succès`, 'success');

    // Recharger la liste des accélérateurs
    loadAccelerators();
  } else {
    showNotification(`Erreur lors de l'ajout de l'accélérateur: ${data.error}`, 'error');
  }
});

// Recevoir la confirmation de modification d'accélérateur
socket.on('accelerator updated', (data) => {
  if (data.success) {
    showNotification(`Accélérateur ${data.accelerator.id} mis à jour avec succès`, 'success');

    // Recharger la liste des accélérateurs
    loadAccelerators();
  } else {
    showNotification(`Erreur lors de la mise à jour de l'accélérateur: ${data.error}`, 'error');
  }
});

// Fonctions principales

// Charger la liste des accélérateurs
function loadAccelerators() {
  socket.emit('get accelerators list');
  socket.emit('get accelerators stats');
}

// Mettre à jour l'interface des accélérateurs
function updateAcceleratorsUI() {
  const container = $('#accelerators-list');

  // Vider le conteneur
  container.empty();

  // Ajouter chaque accélérateur
  acceleratorsState.accelerators.forEach(acc => {
    // Déterminer la classe de badge en fonction du type
    let badgeClass = 'bg-primary';
    if (acc.type === 'thermal') badgeClass = 'bg-info';
    else if (acc.type === 'reflection') badgeClass = 'bg-warning';
    else if (acc.type === 'zone') badgeClass = 'bg-success';

    // Créer la ligne du tableau
    const row = $(`
      <tr class="accelerator-row" data-id="${acc.id}">
        <td>${acc.id}</td>
        <td><span class="badge ${badgeClass}">${capitalizeFirstLetter(acc.type)}</span></td>
        <td><span class="status-indicator ${acc.active ? 'status-active' : 'status-inactive'}"></span> ${acc.active ? 'Actif' : 'Inactif'}</td>
        <td>
          <div class="progress">
            <div class="progress-bar" role="progressbar" style="width: ${Math.round(acc.efficiency * 100)}%;" aria-valuenow="${Math.round(acc.efficiency * 100)}" aria-valuemin="0" aria-valuemax="100">${Math.round(acc.efficiency * 100)}%</div>
          </div>
        </td>
        <td>${acc.throughput} MB/s</td>
        <td>
          <button class="btn btn-sm btn-outline-secondary me-1 toggle-accelerator-btn" data-id="${acc.id}">
            <i class="bi bi-power"></i>
          </button>
          <button class="btn btn-sm btn-outline-info me-1 optimize-accelerator-btn" data-id="${acc.id}">
            <i class="bi bi-lightning-charge"></i>
          </button>
        </td>
      </tr>
    `);

    // Ajouter la ligne au conteneur
    container.append(row);
  });

  // Ajouter les gestionnaires d'événements
  $('.toggle-accelerator-btn').off('click').on('click', function() {
    const accId = $(this).data('id');
    toggleAccelerator(accId);
  });

  $('.optimize-accelerator-btn').off('click').on('click', function() {
    const accId = $(this).data('id');
    optimizeAccelerator(accId);
  });
}

// Mettre à jour l'interface des statistiques
function updateStatsUI() {
  // Mettre à jour l'efficacité moyenne
  const avgEfficiency = Math.round(acceleratorsState.stats.avgEfficiency * 100);
  $('#avg-efficiency').text(`${avgEfficiency}%`);
  $('#avg-efficiency-bar').css('width', `${avgEfficiency}%`).attr('aria-valuenow', avgEfficiency);

  // Mettre à jour le débit total
  $('#total-throughput').text(`${acceleratorsState.stats.totalThroughput.toFixed(2)} GB/s`);
  const throughputPercentage = Math.min(100, (acceleratorsState.stats.totalThroughput / 4) * 100);
  $('#total-throughput-bar').css('width', `${throughputPercentage}%`).attr('aria-valuenow', throughputPercentage);

  // Mettre à jour l'utilisation CPU
  $('#cpu-usage').text(`${acceleratorsState.stats.cpuUsage}%`);
  $('#cpu-usage-bar').css('width', `${acceleratorsState.stats.cpuUsage}%`).attr('aria-valuenow', acceleratorsState.stats.cpuUsage);
}

// Mettre à jour le graphique de répartition
function updateDistributionChart() {
  const ctx = document.getElementById('accelerator-types-chart').getContext('2d');

  // Détruire le graphique existant s'il existe
  if (typesChart) {
    typesChart.destroy();
  }

  // Créer un nouveau graphique
  typesChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
      labels: ['Mémoire', 'Thermique', 'Réflexion', 'Zones'],
      datasets: [{
        data: [
          acceleratorsState.distribution.memory,
          acceleratorsState.distribution.thermal,
          acceleratorsState.distribution.reflection,
          acceleratorsState.distribution.zones
        ],
        backgroundColor: [
          'rgba(156, 137, 184, 0.8)',
          'rgba(184, 190, 221, 0.8)',
          'rgba(240, 166, 202, 0.8)',
          'rgba(156, 137, 184, 0.5)'
        ],
        borderColor: [
          'rgba(156, 137, 184, 1)',
          'rgba(184, 190, 221, 1)',
          'rgba(240, 166, 202, 1)',
          'rgba(156, 137, 184, 0.8)'
        ],
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              const label = context.label || '';
              const value = context.raw || 0;
              return `${label}: ${value} accélérateurs`;
            }
          }
        }
      },
      cutout: '70%'
    }
  });
}

// Activer/désactiver un accélérateur
function toggleAccelerator(accId) {
  // Trouver l'accélérateur dans la liste
  const acc = acceleratorsState.accelerators.find(a => a.id === accId);
  if (!acc) return;

  // Inverser l'état actif
  const newState = !acc.active;

  // Mettre à jour l'interface immédiatement pour une réponse plus rapide
  const row = $(`.accelerator-row[data-id="${accId}"]`);
  const statusIndicator = row.find('.status-indicator');
  const statusText = statusIndicator.parent().contents().last();

  if (newState) {
    statusIndicator.removeClass('status-inactive').addClass('status-active');
    statusText.replaceWith(' Actif');
  } else {
    statusIndicator.removeClass('status-active').addClass('status-inactive');
    statusText.replaceWith(' Inactif');
  }

  // Envoyer la mise à jour au serveur
  socket.emit('update accelerator', {
    id: accId,
    active: newState
  });
}

// Optimiser un accélérateur
function optimizeAccelerator(accId) {
  // Trouver l'accélérateur dans la liste
  const acc = acceleratorsState.accelerators.find(a => a.id === accId);
  if (!acc) return;

  // Désactiver le bouton pendant l'optimisation
  const button = $(`.optimize-accelerator-btn[data-id="${accId}"]`);
  button.prop('disabled', true).html('<i class="bi bi-arrow-repeat spin"></i>');

  // Envoyer la demande d'optimisation
  socket.emit('optimize accelerator', { id: accId });

  // Simuler un délai d'optimisation
  setTimeout(() => {
    // Réactiver le bouton
    button.prop('disabled', false).html('<i class="bi bi-lightning-charge"></i>');

    // Recharger les accélérateurs pour voir les changements
    loadAccelerators();
  }, 2000);
}

// Optimiser tous les accélérateurs
function optimizeAllAccelerators() {
  // Désactiver le bouton pendant l'optimisation
  $('#optimize-all-btn').prop('disabled', true).html('<i class="bi bi-arrow-repeat spin me-1"></i> Optimisation en cours...');

  // Envoyer la demande d'optimisation
  socket.emit('optimize all accelerators');

  // Simuler un délai d'optimisation
  setTimeout(() => {
    // Réactiver le bouton
    $('#optimize-all-btn').prop('disabled', false).html('<i class="bi bi-lightning-charge me-1"></i> Optimiser tous les accélérateurs');

    // Recharger les accélérateurs pour voir les changements
    loadAccelerators();

    // Afficher une notification
    showNotification('Tous les accélérateurs ont été optimisés avec succès', 'success');
  }, 3000);
}

// Équilibrer la charge des accélérateurs
function balanceAcceleratorsLoad() {
  // Désactiver le bouton pendant l'équilibrage
  $('#balance-load-btn').prop('disabled', true).html('<i class="bi bi-arrow-repeat spin me-1"></i> Équilibrage en cours...');

  // Envoyer la demande d'équilibrage
  socket.emit('balance accelerators load');

  // Simuler un délai d'équilibrage
  setTimeout(() => {
    // Réactiver le bouton
    $('#balance-load-btn').prop('disabled', false).html('<i class="bi bi-arrow-repeat me-1"></i> Équilibrer la charge');

    // Recharger les accélérateurs pour voir les changements
    loadAccelerators();

    // Afficher une notification
    showNotification('Charge des accélérateurs équilibrée avec succès', 'success');
  }, 2500);
}

// Générer un rapport sur les accélérateurs
function generateAcceleratorsReport() {
  // Désactiver le bouton pendant la génération
  $('#generate-report-btn').prop('disabled', true).html('<i class="bi bi-arrow-repeat spin me-1"></i> Génération en cours...');

  // Envoyer la demande de génération
  socket.emit('generate accelerators report');

  // Simuler un délai de génération
  setTimeout(() => {
    // Réactiver le bouton
    $('#generate-report-btn').prop('disabled', false).html('<i class="bi bi-file-earmark-text me-1"></i> Générer un rapport');

    // Afficher une notification
    showNotification('Rapport généré avec succès', 'success');

    // Simuler un téléchargement
    const blob = new Blob(['Rapport des accélérateurs Kyber'], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'rapport-accelerateurs.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, 2000);
}

// Afficher une notification
function showNotification(message, type = 'info') {
  // Créer l'élément de notification s'il n'existe pas
  let notificationArea = $('#notification-area');
  if (notificationArea.length === 0) {
    $('body').append('<div id="notification-area"></div>');
    notificationArea = $('#notification-area');
  }

  // Créer la notification
  const notification = $(`<div class="notification notification-${type}">${message}</div>`);
  notificationArea.append(notification);

  // Afficher avec animation
  setTimeout(() => {
    notification.addClass('show');

    // Masquer après 3 secondes
    setTimeout(() => {
      notification.removeClass('show');
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }, 100);
}

// Fonction utilitaire pour capitaliser la première lettre
function capitalizeFirstLetter(string) {
  return string.charAt(0).toUpperCase() + string.slice(1);
}

// Initialisation au chargement de la page
$(document).ready(function() {
  // Initialiser la date et l'heure
  updateDateTime();
  // Mettre à jour la date et l'heure toutes les secondes
  setInterval(updateDateTime, 1000);

  // Gestionnaire pour le bouton d'actualisation
  $('#refresh-accelerators-btn').on('click', loadAccelerators);

  // Gestionnaire pour le bouton d'ajout
  $('#add-accelerator-btn').on('click', function() {
    $('#add-accelerator-modal').modal('show');
  });

  // Gestionnaire pour le type d'accélérateur
  $('#accelerator-type').on('change', function() {
    if ($(this).val() === 'zone') {
      $('#zone-number-container').show();
    } else {
      $('#zone-number-container').hide();
    }
  });

  // Gestionnaire pour le préréglage
  $('#accelerator-preset').on('change', function() {
    if ($(this).val() === 'custom') {
      $('#custom-settings').show();
    } else {
      $('#custom-settings').hide();
    }
  });

  // Gestionnaires pour les sliders personnalisés
  $('#custom-compression').on('input', function() {
    $('#compression-value').text(`${$(this).val()}%`);
  });

  $('#custom-throughput').on('input', function() {
    $('#throughput-value').text(`${$(this).val()} MB/s`);
  });

  // Gestionnaire pour l'ajout d'accélérateur
  $('#confirm-add-accelerator').on('click', function() {
    const type = $('#accelerator-type').val();
    const preset = $('#accelerator-preset').val();

    let zoneNumber = null;
    if (type === 'zone') {
      zoneNumber = parseInt($('#zone-number').val());
    }

    let compression = null;
    let throughput = null;
    if (preset === 'custom') {
      compression = parseInt($('#custom-compression').val()) / 100;
      throughput = parseInt($('#custom-throughput').val());
    }

    // Envoyer la demande d'ajout
    socket.emit('add accelerator', {
      type,
      preset,
      zoneNumber,
      compression,
      throughput
    });

    // Fermer la modal
    $('#add-accelerator-modal').modal('hide');
  });

  // Gestionnaires pour les actions globales
  $('#optimize-all-btn').on('click', optimizeAllAccelerators);
  $('#balance-load-btn').on('click', balanceAcceleratorsLoad);
  $('#generate-report-btn').on('click', generateAcceleratorsReport);

  // Charger les accélérateurs au démarrage
  loadAccelerators();
});
