/**
 * Luna Code Persistence - Gestion de la persistance des fichiers de code
 * Permet de sauvegarder, charger et gérer les fichiers de code de manière persistante
 */

// État global des projets
const projectsState = {
  projects: [],
  activeProject: null,
  autoSaveEnabled: true,
  autoSaveInterval: null,
  autoSaveDelay: 5000, // 5 secondes
  lastSaveTime: null,
  pendingChanges: false
};

// Initialiser la connexion Socket.IO
let socket;
if (typeof io !== 'undefined') {
  socket = io();
}

// Initialisation
$(document).ready(function() {
  // Charger les projets au démarrage
  loadProjects();

  // Configurer l'auto-sauvegarde
  setupAutoSave();

  // Configurer les événements Socket.IO
  setupSocketEvents();

  // Configurer les événements de l'interface utilisateur
  setupUIEvents();
});

// Charger tous les projets
function loadProjects() {
  if (socket) {
    socket.emit('get code projects');
  } else {
    console.error('Socket.IO non disponible pour charger les projets');
    showNotification('Impossible de charger les projets (pas de connexion)', 'error');
  }
}

// Charger le projet actif
function loadActiveProject() {
  if (socket) {
    socket.emit('get active project');
  } else {
    console.error('Socket.IO non disponible pour charger le projet actif');
    showNotification('Impossible de charger le projet actif (pas de connexion)', 'error');
  }
}

// Configurer l'auto-sauvegarde
function setupAutoSave() {
  // Vérifier si l'auto-sauvegarde est activée
  const autoSaveEnabled = $('#auto-save').prop('checked');
  projectsState.autoSaveEnabled = autoSaveEnabled;

  // Configurer l'intervalle d'auto-sauvegarde
  if (projectsState.autoSaveEnabled) {
    projectsState.autoSaveInterval = setInterval(() => {
      if (projectsState.pendingChanges && projectsState.activeProject) {
        saveCurrentFile();
      }
    }, projectsState.autoSaveDelay);
  } else if (projectsState.autoSaveInterval) {
    clearInterval(projectsState.autoSaveInterval);
    projectsState.autoSaveInterval = null;
  }
}

// Configurer les événements Socket.IO
function setupSocketEvents() {
  if (!socket) return;

  // Événement pour recevoir tous les projets
  socket.on('code projects', (data) => {
    if (data.projects) {
      projectsState.projects = data.projects;
      updateProjectsList();
    }
  });

  // Événement pour recevoir le projet actif
  socket.on('active project', (data) => {
    if (data.project) {
      projectsState.activeProject = data.project;
      updateProjectView();
    }
  });

  // Événement pour la création d'un projet
  socket.on('project created', (data) => {
    if (data.project) {
      projectsState.projects.push(data.project);
      updateProjectsList();
    }
  });

  // Événement pour la sauvegarde d'un fichier
  socket.on('file saved', (data) => {
    if (data.projectId === projectsState.activeProject.id) {
      // Mettre à jour le fichier dans le projet actif
      const fileIndex = projectsState.activeProject.files.findIndex(f => f.name === data.file.name);

      if (fileIndex >= 0) {
        projectsState.activeProject.files[fileIndex] = data.file;
      } else {
        projectsState.activeProject.files.push(data.file);
      }

      // Mettre à jour l'interface
      updateFileTabs();
      showNotification('Fichier sauvegardé avec succès', 'success');

      // Réinitialiser l'état des modifications
      projectsState.pendingChanges = false;
      projectsState.lastSaveTime = new Date();
    }
  });

  // Événement pour la création d'un fichier
  socket.on('file created', (data) => {
    if (data.projectId === projectsState.activeProject.id) {
      projectsState.activeProject.files.push(data.file);
      updateFileTabs();
      showNotification('Fichier créé avec succès', 'success');
    }
  });

  // Événement pour la suppression d'un fichier
  socket.on('file deleted', (data) => {
    if (data.projectId === projectsState.activeProject.id) {
      // Supprimer le fichier du projet actif
      projectsState.activeProject.files = projectsState.activeProject.files.filter(f => f.name !== data.fileName);

      // Si le fichier supprimé est le fichier actuel, charger un autre fichier
      if (codeEditorState.currentFile === data.fileName) {
        if (projectsState.activeProject.files.length > 0) {
          codeEditorState.currentFile = projectsState.activeProject.files[0].name;
          loadFile(codeEditorState.currentFile);
        } else {
          // Créer un fichier par défaut si aucun fichier n'est disponible
          createNewFile('main.js', '// Nouveau fichier JavaScript');
        }
      }

      updateFileTabs();
      showNotification('Fichier supprimé avec succès', 'success');
    }
  });

  // Événement pour les erreurs
  socket.on('code error', (data) => {
    console.error('Erreur de code:', data.error);
    showNotification(`Erreur: ${data.error}`, 'error');
  });
}

// Configurer les événements de l'interface utilisateur
function setupUIEvents() {
  // Événement pour la création d'un nouveau projet
  $('#new-project-btn').on('click', () => {
    // Afficher une boîte de dialogue pour saisir le nom du projet
    const projectName = prompt('Nom du nouveau projet:');

    if (projectName) {
      createNewProject(projectName);
    }
  });

  // Événement pour la création d'un nouveau fichier
  $('#new-file-btn').on('click', () => {
    // Afficher une boîte de dialogue pour saisir le nom du fichier
    const fileName = prompt('Nom du nouveau fichier:');

    if (fileName) {
      createNewFile(fileName, '');
    }
  });

  // Événement pour la suppression d'un fichier
  $(document).on('click', '.close-tab', function(e) {
    e.stopPropagation();
    const fileName = $(this).parent().data('file');

    if (confirm(`Voulez-vous vraiment supprimer le fichier "${fileName}" ?`)) {
      deleteFile(fileName);
    }
  });

  // Événement pour changer de fichier
  $(document).on('click', '.code-tab:not(.add-tab)', function() {
    const fileName = $(this).data('file');

    // Sauvegarder le fichier actuel avant de changer
    if (projectsState.pendingChanges) {
      saveCurrentFile();
    }

    // Charger le nouveau fichier
    codeEditorState.currentFile = fileName;
    loadFile(fileName);

    // Charger les versions du fichier
    loadFileVersions(fileName);
  });

  // Événement pour l'ajout d'un nouvel onglet
  $(document).on('click', '.add-tab', function() {
    // Afficher une boîte de dialogue pour saisir le nom du fichier
    const fileName = prompt('Nom du nouveau fichier:');

    if (fileName) {
      createNewFile(fileName, '');
    }
  });

  // Événement pour la sauvegarde du fichier
  $('#save-code-btn').on('click', () => {
    saveCurrentFile();

    // Recharger les versions du fichier après la sauvegarde
    setTimeout(() => {
      loadFileVersions(codeEditorState.currentFile);
    }, 500);
  });

  // Événement pour l'auto-sauvegarde
  $('#auto-save').on('change', function() {
    projectsState.autoSaveEnabled = $(this).prop('checked');
    setupAutoSave();
  });

  // Événement pour détecter les modifications dans l'éditeur
  $('#code-editor').on('input', function() {
    // Marquer le fichier comme modifié
    codeEditorState.files[codeEditorState.currentFile].modified = true;
    projectsState.pendingChanges = true;

    // Mettre à jour les onglets pour afficher l'indicateur de modification
    updateFileTabs();
  });

  // Événement pour actualiser les versions
  $('#refresh-versions-btn').on('click', () => {
    loadFileVersions(codeEditorState.currentFile);
  });

  // Événement pour sélectionner une version
  $(document).on('click', '.version-item', function() {
    // Désélectionner toutes les versions
    $('.version-item').removeClass('active');

    // Sélectionner la version cliquée
    $(this).addClass('active');

    // Activer le bouton de restauration
    $('#restore-version-btn').prop('disabled', false);

    // Stocker l'ID de la version sélectionnée
    const versionId = $(this).data('version-id');
    projectsState.selectedVersionId = versionId;

    // Prévisualiser la version
    previewVersion(versionId);
  });

  // Événement pour restaurer une version
  $('#restore-version-btn').on('click', () => {
    if (projectsState.selectedVersionId) {
      restoreVersion(projectsState.selectedVersionId);
    }
  });
}

// Mettre à jour la liste des projets
function updateProjectsList() {
  const projectsList = $('.projects-list');

  if (projectsList.length === 0) return;

  projectsList.empty();

  projectsState.projects.forEach(project => {
    const isActive = projectsState.activeProject && project.id === projectsState.activeProject.id;
    const projectItem = $(`
      <li class="project-item ${isActive ? 'active' : ''}">
        <div class="d-flex align-items-center">
          <i class="bi bi-folder${isActive ? '-fill' : ''} me-2"></i>
          <span>${project.name}</span>
        </div>
      </li>
    `);

    // Événement pour sélectionner un projet
    projectItem.on('click', () => {
      // Sauvegarder le fichier actuel avant de changer de projet
      if (projectsState.pendingChanges) {
        saveCurrentFile();
      }

      // Définir le projet actif
      if (socket) {
        socket.emit('set active project', { projectId: project.id });
      }
    });

    projectsList.append(projectItem);
  });
}

// Mettre à jour la vue du projet
function updateProjectView() {
  if (!projectsState.activeProject) return;

  // Mettre à jour le titre du projet
  $('.project-title').text(projectsState.activeProject.name);

  // Mettre à jour les fichiers dans l'éditeur
  codeEditorState.files = {};

  // Charger les fichiers du projet
  projectsState.activeProject.files.forEach(file => {
    codeEditorState.files[file.name] = {
      content: '',
      language: file.language || getLanguageFromFileName(file.name),
      modified: false
    };
  });

  // Définir le fichier actuel
  if (projectsState.activeProject.files.length > 0) {
    codeEditorState.currentFile = projectsState.activeProject.files[0].name;
    loadFile(codeEditorState.currentFile);
  }

  // Mettre à jour les onglets
  updateFileTabs();
}

// Charger un fichier
function loadFile(fileName) {
  if (!projectsState.activeProject) return;

  // Vérifier si le fichier existe dans le projet
  const fileExists = projectsState.activeProject.files.some(f => f.name === fileName);

  if (!fileExists) {
    console.error(`Fichier non trouvé: ${fileName}`);
    showNotification(`Fichier non trouvé: ${fileName}`, 'error');
    return;
  }

  // Charger le contenu du fichier depuis le serveur
  if (socket) {
    socket.emit('load file', {
      projectId: projectsState.activeProject.id,
      fileName: fileName
    });

    // Événement pour recevoir le contenu du fichier
    socket.once('file loaded', (data) => {
      if (data.success) {
        // Mettre à jour le contenu du fichier
        codeEditorState.files[fileName].content = data.content;
        codeEditorState.files[fileName].language = data.language || getLanguageFromFileName(fileName);
        codeEditorState.files[fileName].modified = false;

        // Mettre à jour l'éditeur
        updateEditor();
      } else {
        console.error(`Erreur lors du chargement du fichier ${fileName}:`, data.error);
        showNotification(`Erreur lors du chargement du fichier: ${data.error}`, 'error');
      }
    });
  }
}

// Sauvegarder le fichier actuel
function saveCurrentFile() {
  if (!projectsState.activeProject) return;

  const fileName = codeEditorState.currentFile;
  const content = $('#code-editor').val();

  // Mettre à jour le contenu du fichier
  codeEditorState.files[fileName].content = content;

  // Sauvegarder le fichier sur le serveur
  if (socket) {
    socket.emit('save file', {
      projectId: projectsState.activeProject.id,
      fileName: fileName,
      content: content
    });
  }
}

// Créer un nouveau projet
function createNewProject(name, description = '') {
  if (socket) {
    socket.emit('create project', {
      name: name,
      description: description
    });
  }
}

// Créer un nouveau fichier
function createNewFile(fileName, content = '') {
  if (!projectsState.activeProject) return;

  // Vérifier si le fichier existe déjà
  const fileExists = projectsState.activeProject.files.some(f => f.name === fileName);

  if (fileExists) {
    showNotification(`Le fichier ${fileName} existe déjà`, 'warning');
    return;
  }

  // Créer le fichier sur le serveur
  if (socket) {
    socket.emit('create file', {
      projectId: projectsState.activeProject.id,
      fileName: fileName,
      content: content
    });

    // Ajouter le fichier à l'état local
    codeEditorState.files[fileName] = {
      content: content,
      language: getLanguageFromFileName(fileName),
      modified: false
    };

    // Définir le nouveau fichier comme fichier actuel
    codeEditorState.currentFile = fileName;

    // Mettre à jour l'éditeur
    updateEditor();
  }
}

// Supprimer un fichier
function deleteFile(fileName) {
  if (!projectsState.activeProject) return;

  // Supprimer le fichier sur le serveur
  if (socket) {
    socket.emit('delete file', {
      projectId: projectsState.activeProject.id,
      fileName: fileName
    });
  }
}

// Obtenir le langage à partir du nom de fichier
function getLanguageFromFileName(fileName) {
  const extension = fileName.split('.').pop().toLowerCase();

  switch (extension) {
    case 'js':
      return 'javascript';
    case 'html':
      return 'html';
    case 'css':
      return 'css';
    case 'py':
      return 'python';
    case 'json':
      return 'json';
    case 'md':
      return 'markdown';
    default:
      return 'text';
  }
}

// Charger les versions d'un fichier
function loadFileVersions(fileName) {
  if (!projectsState.activeProject) return;

  // Afficher un indicateur de chargement
  $('#versions-list').html(`
    <div class="text-center py-3">
      <div class="spinner-border text-light" role="status">
        <span class="visually-hidden">Chargement...</span>
      </div>
      <p class="mt-2">Chargement de l'historique...</p>
    </div>
  `);

  // Désactiver le bouton de restauration
  $('#restore-version-btn').prop('disabled', true);

  // Réinitialiser la version sélectionnée
  projectsState.selectedVersionId = null;

  // Charger les versions depuis le serveur
  if (socket) {
    socket.emit('get file versions', {
      projectId: projectsState.activeProject.id,
      fileName: fileName
    });

    // Événement pour recevoir les versions
    socket.once('file versions', (data) => {
      if (data.versions && data.versions.length > 0) {
        // Trier les versions par date (la plus récente en premier)
        const versions = data.versions.sort((a, b) => {
          return new Date(b.timestamp) - new Date(a.timestamp);
        });

        // Mettre à jour la liste des versions
        updateVersionsList(versions);
      } else {
        // Aucune version disponible
        $('#versions-list').html(`
          <div class="text-center py-3">
            <i class="bi bi-clock-history text-muted" style="font-size: 2rem;"></i>
            <p class="mt-2">Aucune version disponible</p>
          </div>
        `);
      }
    });
  }
}

// Mettre à jour la liste des versions
function updateVersionsList(versions) {
  const versionsList = $('#versions-list');
  versionsList.empty();

  versions.forEach(version => {
    // Formater la date
    const date = new Date(version.timestamp);
    const formattedDate = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();

    // Formater la taille
    const formattedSize = formatFileSize(version.size);

    // Créer l'élément de version
    const versionItem = $(`
      <div class="version-item" data-version-id="${version.id}">
        <div class="version-header">
          <span class="version-time">${formattedDate}</span>
          <span class="version-size">${formattedSize}</span>
        </div>
        <div class="version-message">${version.message}</div>
      </div>
    `);

    versionsList.append(versionItem);
  });
}

// Prévisualiser une version
function previewVersion(versionId) {
  if (!projectsState.activeProject || !codeEditorState.currentFile) return;

  // Sauvegarder le contenu actuel si nécessaire
  if (projectsState.pendingChanges) {
    const currentContent = $('#code-editor').val();
    projectsState.currentContent = currentContent;
  }

  // Charger le contenu de la version
  if (socket) {
    socket.emit('get version content', {
      projectId: projectsState.activeProject.id,
      fileName: codeEditorState.currentFile,
      versionId: versionId
    });

    // Événement pour recevoir le contenu de la version
    socket.once('version content', (data) => {
      if (data.content) {
        // Mettre à jour l'éditeur avec le contenu de la version
        $('#code-editor').val(data.content);

        // Mettre à jour les numéros de ligne
        updateLineNumbers();

        // Afficher une notification
        showNotification('Prévisualisation de la version', 'info');
      }
    });
  }
}

// Restaurer une version
function restoreVersion(versionId) {
  if (!projectsState.activeProject || !codeEditorState.currentFile) return;

  if (confirm('Voulez-vous vraiment restaurer cette version ? Les modifications non sauvegardées seront perdues.')) {
    if (socket) {
      socket.emit('restore version', {
        projectId: projectsState.activeProject.id,
        fileName: codeEditorState.currentFile,
        versionId: versionId
      });

      // Événement pour recevoir la confirmation de restauration
      socket.once('version restored', (data) => {
        // Recharger le fichier
        loadFile(codeEditorState.currentFile);

        // Recharger les versions
        loadFileVersions(codeEditorState.currentFile);

        // Afficher une notification
        showNotification('Version restaurée avec succès', 'success');
      });
    }
  }
}

// Formater la taille d'un fichier
function formatFileSize(bytes) {
  if (bytes < 1024) {
    return bytes + ' o';
  } else if (bytes < 1024 * 1024) {
    return (bytes / 1024).toFixed(1) + ' Ko';
  } else {
    return (bytes / (1024 * 1024)).toFixed(1) + ' Mo';
  }
}

// Afficher une notification
function showNotification(message, type = 'info') {
  // Vérifier si la fonction existe déjà dans le contexte global
  if (typeof window.showNotification === 'function') {
    window.showNotification(message, type);
    return;
  }

  // Créer un conteneur de notifications s'il n'existe pas
  let notificationContainer = $('#notification-container');

  if (notificationContainer.length === 0) {
    notificationContainer = $('<div id="notification-container"></div>');
    $('body').append(notificationContainer);

    // Ajouter du style au conteneur
    notificationContainer.css({
      position: 'fixed',
      top: '20px',
      right: '20px',
      zIndex: 9999,
      maxWidth: '300px'
    });
  }

  // Créer la notification
  const notification = $(`
    <div class="notification notification-${type}">
      <div class="notification-content">
        <i class="bi bi-${getIconForType(type)} me-2"></i>
        <span>${message}</span>
      </div>
      <button class="notification-close">
        <i class="bi bi-x"></i>
      </button>
    </div>
  `);

  // Ajouter du style à la notification
  notification.css({
    backgroundColor: getColorForType(type),
    color: '#fff',
    padding: '10px 15px',
    borderRadius: '5px',
    marginBottom: '10px',
    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.2)',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    opacity: 0,
    transform: 'translateY(-20px)',
    transition: 'opacity 0.3s, transform 0.3s'
  });

  // Ajouter la notification au conteneur
  notificationContainer.append(notification);

  // Animer l'apparition
  setTimeout(() => {
    notification.css({
      opacity: 1,
      transform: 'translateY(0)'
    });
  }, 10);

  // Fermer la notification au clic sur le bouton
  notification.find('.notification-close').on('click', () => {
    closeNotification(notification);
  });

  // Fermer automatiquement après 5 secondes
  setTimeout(() => {
    closeNotification(notification);
  }, 5000);

  // Fonction pour fermer la notification
  function closeNotification(notification) {
    notification.css({
      opacity: 0,
      transform: 'translateY(-20px)'
    });

    setTimeout(() => {
      notification.remove();
    }, 300);
  }

  // Fonction pour obtenir l'icône en fonction du type
  function getIconForType(type) {
    switch (type) {
      case 'success':
        return 'check-circle';
      case 'error':
        return 'exclamation-circle';
      case 'warning':
        return 'exclamation-triangle';
      case 'info':
      default:
        return 'info-circle';
    }
  }

  // Fonction pour obtenir la couleur en fonction du type
  function getColorForType(type) {
    switch (type) {
      case 'success':
        return '#28a745';
      case 'error':
        return '#dc3545';
      case 'warning':
        return '#ffc107';
      case 'info':
      default:
        return '#17a2b8';
    }
  }
}
