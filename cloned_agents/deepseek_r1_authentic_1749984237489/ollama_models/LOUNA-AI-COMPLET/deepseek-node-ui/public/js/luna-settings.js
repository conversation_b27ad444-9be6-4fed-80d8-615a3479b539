/**
 * Luna Settings - Gestion des paramètres de l'application
 * Ce script gère l'interface des paramètres et leur persistance
 */

// Initialiser la connexion Socket.IO
const socket = io();

// État global des paramètres
const settingsState = {
  general: {
    model: 'claude-3-opus',
    temperature: 0.7,
    maxTokens: 4096,
    camera: true,
    microphone: true,
    speaker: true
  },
  memory: {
    capacity: 1000,
    retention: 0.85,
    thermalTransfer: 0.3,
    contextWindow: 10,
    semanticSearch: true
  },
  accelerators: {
    count: 16,
    cascadeDepth: 3,
    masterPower: 87,
    allocation: {
      reflection: 25,
      memory: 25,
      processing: 25,
      creativity: 25
    }
  },
  interface: {
    theme: 'default',
    fontSize: 'medium',
    animations: true,
    bubbleStyle: true,
    timestamps: true,
    systemMessages: false
  },
  security: {
    vpn: {
      enabled: false,
      server: 'fr',
      autoconnect: false,
      killswitch: false,
      adblock: true,
      antitracking: true,
      connected: false,
      connectionTime: null,
      ip: 'Non masquée',
      location: 'Guadeloupe',
      protection: 0
    },
    firewall: {
      enabled: true,
      level: 'medium'
    },
    antivirus: {
      enabled: true,
      scanFrequency: 'weekly',
      lastScan: null
    }
  },
  advanced: {
    logLevel: 'info',
    debugMode: false,
    experimentalFeatures: false,
    apiUrl: 'https://api.anthropic.com/v1',
    timeout: 30000,
    useProxy: false,
    proxyUrl: ''
  },
  system: {
    version: '1.0.0',
    lastUpdate: '01/06/2023 14:30'
  }
};

// Événements Socket.IO
socket.on('connect', () => {
  console.log('🔌 Connecté au serveur Luna');
  loadSettings();
});

socket.on('disconnect', () => {
  console.log('🔌 Déconnecté du serveur Luna');
  showNotification('Connexion au serveur perdue. Tentative de reconnexion...', 'error');
});

// Recevoir les paramètres
socket.on('settings data', (data) => {
  if (data.success) {
    // Fusionner les paramètres reçus avec l'état actuel
    Object.assign(settingsState, data.settings);

    // Mettre à jour l'interface
    updateSettingsUI();

    showNotification('Paramètres chargés avec succès', 'success');
  } else {
    showNotification(`Erreur lors du chargement des paramètres: ${data.error}`, 'error');
  }
});

// Confirmation de sauvegarde
socket.on('settings saved', (data) => {
  if (data.success) {
    showNotification('Paramètres enregistrés avec succès', 'success');

    // Mettre à jour la date de dernière mise à jour
    const now = new Date();
    settingsState.system.lastUpdate = now.toLocaleString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    // Ajouter une entrée au journal
    addLogEntry('Paramètres mis à jour');

    // Mettre à jour l'interface
    updateSystemInfo();
  } else {
    showNotification(`Erreur lors de l'enregistrement des paramètres: ${data.error}`, 'error');
  }
});

// Fonctions principales

// Charger les paramètres
function loadSettings() {
  socket.emit('get settings');
}

// Sauvegarder les paramètres
function saveSettings() {
  // Collecter les valeurs des formulaires
  collectFormValues();

  // Envoyer les paramètres au serveur
  socket.emit('save settings', { settings: settingsState });
}

// Collecter les valeurs des formulaires
function collectFormValues() {
  // Paramètres généraux
  settingsState.general.model = $('#model-select').val();
  settingsState.general.temperature = parseFloat($('#temperature-range').val());
  settingsState.general.maxTokens = parseInt($('#max-tokens-input').val());
  settingsState.general.camera = $('#camera-toggle').is(':checked');
  settingsState.general.microphone = $('#microphone-toggle').is(':checked');
  settingsState.general.speaker = $('#speaker-toggle').is(':checked');

  // Paramètres de mémoire
  settingsState.memory.capacity = parseInt($('#memory-capacity-input').val());
  settingsState.memory.retention = parseFloat($('#memory-retention-range').val());
  settingsState.memory.thermalTransfer = parseFloat($('#thermal-transfer-range').val());
  settingsState.memory.contextWindow = parseInt($('#context-window-input').val());
  settingsState.memory.semanticSearch = $('#semantic-search-toggle').is(':checked');

  // Paramètres des accélérateurs
  settingsState.accelerators.count = parseInt($('#accelerator-count-input').val());
  settingsState.accelerators.cascadeDepth = parseInt($('#cascade-depth-input').val());
  settingsState.accelerators.masterPower = parseInt($('#master-power-range').val());
  settingsState.accelerators.allocation.reflection = parseInt($('#reflection-allocation-range').val());
  settingsState.accelerators.allocation.memory = parseInt($('#memory-allocation-range').val());
  settingsState.accelerators.allocation.processing = parseInt($('#processing-allocation-range').val());
  settingsState.accelerators.allocation.creativity = parseInt($('#creativity-allocation-range').val());

  // Paramètres d'interface
  settingsState.interface.theme = $('#theme-select').val();
  settingsState.interface.fontSize = $('#font-size-select').val();
  settingsState.interface.animations = $('#animations-toggle').is(':checked');
  settingsState.interface.bubbleStyle = $('#bubble-style-toggle').is(':checked');
  settingsState.interface.timestamps = $('#timestamps-toggle').is(':checked');
  settingsState.interface.systemMessages = $('#system-messages-toggle').is(':checked');

  // Paramètres de sécurité
  settingsState.security.vpn.enabled = $('#vpn-toggle').is(':checked');
  settingsState.security.vpn.server = $('#vpn-server-select').val();
  settingsState.security.vpn.autoconnect = $('#vpn-autoconnect-toggle').is(':checked');
  settingsState.security.vpn.killswitch = $('#vpn-killswitch-toggle').is(':checked');
  settingsState.security.vpn.adblock = $('#vpn-adblock-toggle').is(':checked');
  settingsState.security.vpn.antitracking = $('#vpn-antitracking-toggle').is(':checked');

  settingsState.security.firewall.enabled = $('#firewall-toggle').is(':checked');
  settingsState.security.firewall.level = $('#firewall-level-select').val();

  settingsState.security.antivirus.enabled = $('#antivirus-toggle').is(':checked');
  settingsState.security.antivirus.scanFrequency = $('#scan-frequency-select').val();

  // Paramètres avancés
  settingsState.advanced.logLevel = $('#log-level-select').val();
  settingsState.advanced.debugMode = $('#debug-mode-toggle').is(':checked');
  settingsState.advanced.experimentalFeatures = $('#experimental-features-toggle').is(':checked');
  settingsState.advanced.apiUrl = $('#api-url-input').val();
  settingsState.advanced.timeout = parseInt($('#timeout-input').val());
  settingsState.advanced.useProxy = $('#proxy-toggle').is(':checked');
  settingsState.advanced.proxyUrl = $('#proxy-url-input').val();
}

// Mettre à jour l'interface des paramètres
function updateSettingsUI() {
  // Paramètres généraux
  $('#model-select').val(settingsState.general.model);
  $('#temperature-range').val(settingsState.general.temperature);
  $('#temperature-value').text(settingsState.general.temperature);
  $('#max-tokens-input').val(settingsState.general.maxTokens);
  $('#camera-toggle').prop('checked', settingsState.general.camera);
  $('#microphone-toggle').prop('checked', settingsState.general.microphone);
  $('#speaker-toggle').prop('checked', settingsState.general.speaker);

  // Paramètres de mémoire
  $('#memory-capacity-input').val(settingsState.memory.capacity);
  $('#memory-retention-range').val(settingsState.memory.retention);
  $('#memory-retention-value').text(settingsState.memory.retention);
  $('#thermal-transfer-range').val(settingsState.memory.thermalTransfer);
  $('#thermal-transfer-value').text(settingsState.memory.thermalTransfer);
  $('#context-window-input').val(settingsState.memory.contextWindow);
  $('#semantic-search-toggle').prop('checked', settingsState.memory.semanticSearch);

  // Paramètres des accélérateurs
  $('#accelerator-count-input').val(settingsState.accelerators.count);
  $('#cascade-depth-input').val(settingsState.accelerators.cascadeDepth);
  $('#master-power-range').val(settingsState.accelerators.masterPower);
  $('#master-power-value').text(`${settingsState.accelerators.masterPower}%`);
  $('#reflection-allocation-range').val(settingsState.accelerators.allocation.reflection);
  $('#reflection-allocation-value').text(`${settingsState.accelerators.allocation.reflection}%`);
  $('#memory-allocation-range').val(settingsState.accelerators.allocation.memory);
  $('#memory-allocation-value').text(`${settingsState.accelerators.allocation.memory}%`);
  $('#processing-allocation-range').val(settingsState.accelerators.allocation.processing);
  $('#processing-allocation-value').text(`${settingsState.accelerators.allocation.processing}%`);
  $('#creativity-allocation-range').val(settingsState.accelerators.allocation.creativity);
  $('#creativity-allocation-value').text(`${settingsState.accelerators.allocation.creativity}%`);
  updateTotalAllocation();

  // Paramètres d'interface
  $('#theme-select').val(settingsState.interface.theme);
  $('#font-size-select').val(settingsState.interface.fontSize);
  $('#animations-toggle').prop('checked', settingsState.interface.animations);
  $('#bubble-style-toggle').prop('checked', settingsState.interface.bubbleStyle);
  $('#timestamps-toggle').prop('checked', settingsState.interface.timestamps);
  $('#system-messages-toggle').prop('checked', settingsState.interface.systemMessages);

  // Paramètres de sécurité
  $('#vpn-toggle').prop('checked', settingsState.security.vpn.enabled);
  $('#vpn-server-select').val(settingsState.security.vpn.server);
  $('#vpn-autoconnect-toggle').prop('checked', settingsState.security.vpn.autoconnect);
  $('#vpn-killswitch-toggle').prop('checked', settingsState.security.vpn.killswitch);
  $('#vpn-adblock-toggle').prop('checked', settingsState.security.vpn.adblock);
  $('#vpn-antitracking-toggle').prop('checked', settingsState.security.vpn.antitracking);

  // Afficher/masquer les paramètres VPN
  if (settingsState.security.vpn.enabled) {
    $('#vpn-settings').show();
  } else {
    $('#vpn-settings').hide();
  }

  // Mettre à jour l'état de connexion VPN
  updateVPNStatus();

  $('#firewall-toggle').prop('checked', settingsState.security.firewall.enabled);
  $('#firewall-level-select').val(settingsState.security.firewall.level);

  $('#antivirus-toggle').prop('checked', settingsState.security.antivirus.enabled);
  $('#scan-frequency-select').val(settingsState.security.antivirus.scanFrequency);

  // Paramètres avancés
  $('#log-level-select').val(settingsState.advanced.logLevel);
  $('#debug-mode-toggle').prop('checked', settingsState.advanced.debugMode);
  $('#experimental-features-toggle').prop('checked', settingsState.advanced.experimentalFeatures);
  $('#api-url-input').val(settingsState.advanced.apiUrl);
  $('#timeout-input').val(settingsState.advanced.timeout);
  $('#proxy-toggle').prop('checked', settingsState.advanced.useProxy);
  $('#proxy-url-input').val(settingsState.advanced.proxyUrl);

  // Afficher/masquer les paramètres du proxy
  if (settingsState.advanced.useProxy) {
    $('#proxy-settings').show();
  } else {
    $('#proxy-settings').hide();
  }

  // Mettre à jour les informations système
  updateSystemInfo();
}

// Mettre à jour les informations système
function updateSystemInfo() {
  $('#system-version').text(settingsState.system.version);
  $('#system-model').text(settingsState.general.model);
  $('#system-memory').text(`${settingsState.memory.capacity}/1000`);
  $('#system-accelerators').text(`${settingsState.accelerators.count} actifs`);
  $('#system-last-update').text(settingsState.system.lastUpdate);
}

// Mettre à jour le total d'allocation
function updateTotalAllocation() {
  const reflection = parseInt($('#reflection-allocation-range').val());
  const memory = parseInt($('#memory-allocation-range').val());
  const processing = parseInt($('#processing-allocation-range').val());
  const creativity = parseInt($('#creativity-allocation-range').val());

  const total = reflection + memory + processing + creativity;
  $('#total-allocation').text(total);

  // Mettre en évidence si le total n'est pas égal à 100%
  if (total !== 100) {
    $('#total-allocation').addClass('text-danger');
  } else {
    $('#total-allocation').removeClass('text-danger');
  }
}

// Réinitialiser les paramètres
function resetSettings() {
  if (confirm('Êtes-vous sûr de vouloir réinitialiser tous les paramètres ? Cette action est irréversible.')) {
    // Réinitialiser les paramètres aux valeurs par défaut
    settingsState.general = {
      model: 'claude-3-opus',
      temperature: 0.7,
      maxTokens: 4096,
      camera: true,
      microphone: true,
      speaker: true
    };

    settingsState.memory = {
      capacity: 1000,
      retention: 0.85,
      thermalTransfer: 0.3,
      contextWindow: 10,
      semanticSearch: true
    };

    settingsState.accelerators = {
      count: 16,
      cascadeDepth: 3,
      masterPower: 87,
      allocation: {
        reflection: 25,
        memory: 25,
        processing: 25,
        creativity: 25
      }
    };

    settingsState.interface = {
      theme: 'default',
      fontSize: 'medium',
      animations: true,
      bubbleStyle: true,
      timestamps: true,
      systemMessages: false
    };

    settingsState.advanced = {
      logLevel: 'info',
      debugMode: false,
      experimentalFeatures: false,
      apiUrl: 'https://api.anthropic.com/v1',
      timeout: 30000,
      useProxy: false,
      proxyUrl: ''
    };

    // Mettre à jour l'interface
    updateSettingsUI();

    // Ajouter une entrée au journal
    addLogEntry('Paramètres réinitialisés aux valeurs par défaut');

    showNotification('Paramètres réinitialisés aux valeurs par défaut', 'success');
  }
}

// Exporter les paramètres
function exportSettings() {
  // Collecter les valeurs des formulaires
  collectFormValues();

  // Créer un objet Blob avec les paramètres
  const data = JSON.stringify(settingsState, null, 2);
  const blob = new Blob([data], { type: 'application/json' });
  const url = URL.createObjectURL(blob);

  // Créer un lien de téléchargement
  const a = document.createElement('a');
  a.href = url;
  a.download = `luna-settings-${new Date().toISOString().slice(0, 10)}.json`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);

  // Ajouter une entrée au journal
  addLogEntry('Paramètres exportés');

  showNotification('Paramètres exportés avec succès', 'success');
}

// Importer des paramètres
function importSettings() {
  // Créer un élément input de type file
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.json';

  // Gérer l'événement de changement
  input.onchange = function(event) {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();

      reader.onload = function(e) {
        try {
          const importedSettings = JSON.parse(e.target.result);

          // Fusionner les paramètres importés avec l'état actuel
          Object.assign(settingsState, importedSettings);

          // Mettre à jour l'interface
          updateSettingsUI();

          // Ajouter une entrée au journal
          addLogEntry('Paramètres importés');

          showNotification('Paramètres importés avec succès', 'success');
        } catch (error) {
          showNotification(`Erreur lors de l'importation des paramètres: ${error.message}`, 'error');
        }
      };

      reader.readAsText(file);
    }
  };

  // Déclencher le clic sur l'élément input
  input.click();
}

// Ajouter une entrée au journal
function addLogEntry(message) {
  const now = new Date();
  const timeString = now.toLocaleString('fr-FR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });

  const logEntry = $(`
    <div class="log-entry">
      <span class="log-time">${timeString}</span>
      <span class="log-message">${message}</span>
    </div>
  `);

  // Ajouter au début du journal
  $('#settings-log').prepend(logEntry);
}

// Afficher une notification
function showNotification(message, type = 'info') {
  // Créer l'élément de notification s'il n'existe pas
  let notificationArea = $('#notification-area');
  if (notificationArea.length === 0) {
    $('body').append('<div id="notification-area"></div>');
    notificationArea = $('#notification-area');
  }

  // Créer la notification
  const notification = $(`<div class="notification notification-${type}">${message}</div>`);
  notificationArea.append(notification);

  // Afficher avec animation
  setTimeout(() => {
    notification.addClass('show');

    // Masquer après 3 secondes
    setTimeout(() => {
      notification.removeClass('show');
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }, 100);
}

// Initialisation au chargement de la page
$(document).ready(function() {
  // Gestionnaire pour le bouton de sauvegarde
  $('#save-settings-btn').on('click', saveSettings);

  // Gestionnaire pour le bouton de réinitialisation
  $('#reset-settings-btn').on('click', resetSettings);

  // Gestionnaire pour le bouton d'exportation
  $('#export-settings-btn').on('click', exportSettings);

  // Gestionnaire pour le bouton d'importation
  $('#import-settings-btn').on('click', importSettings);

  // Gestionnaire pour le curseur de température
  $('#temperature-range').on('input', function() {
    $('#temperature-value').text($(this).val());
  });

  // Gestionnaire pour le curseur de rétention mémoire
  $('#memory-retention-range').on('input', function() {
    $('#memory-retention-value').text($(this).val());
  });

  // Gestionnaire pour le curseur de transfert thermique
  $('#thermal-transfer-range').on('input', function() {
    $('#thermal-transfer-value').text($(this).val());
  });

  // Gestionnaire pour le curseur de puissance principale
  $('#master-power-range').on('input', function() {
    $('#master-power-value').text(`${$(this).val()}%`);
  });

  // Gestionnaires pour les curseurs d'allocation
  $('#reflection-allocation-range, #memory-allocation-range, #processing-allocation-range, #creativity-allocation-range').on('input', function() {
    const id = $(this).attr('id');
    const value = $(this).val();
    $(`#${id.replace('range', 'value')}`).text(`${value}%`);
    updateTotalAllocation();
  });

  // Gestionnaire pour le toggle de proxy
  $('#proxy-toggle').on('change', function() {
    if ($(this).is(':checked')) {
      $('#proxy-settings').show();
    } else {
      $('#proxy-settings').hide();
    }
  });

  // Gestionnaire pour le toggle VPN
  $('#vpn-toggle').on('change', function() {
    if ($(this).is(':checked')) {
      $('#vpn-settings').slideDown();
    } else {
      // Si le VPN est connecté, demander confirmation
      if (settingsState.security.vpn.connected) {
        if (confirm('Le VPN est actuellement connecté. Êtes-vous sûr de vouloir le désactiver ?')) {
          disconnectVPN();
          $('#vpn-settings').slideUp();
        } else {
          $(this).prop('checked', true);
        }
      } else {
        $('#vpn-settings').slideUp();
      }
    }
  });

  // Gestionnaire pour le bouton de connexion VPN
  $('#vpn-connect-btn').on('click', function() {
    if (settingsState.security.vpn.connected) {
      disconnectVPN();
    } else {
      connectVPN();
    }
  });

  // Gestionnaire pour le bouton rapide VPN dans les actions rapides
  $('#vpn-quick-toggle-btn').on('click', function() {
    if (settingsState.security.vpn.connected) {
      disconnectVPN();
      $(this).html('<i class="bi bi-shield-check me-1"></i> Activer VPN');
      $(this).removeClass('btn-danger').addClass('btn-luna');
    } else {
      connectVPN();
      $(this).html('<i class="bi bi-shield-x me-1"></i> Désactiver VPN');
      $(this).removeClass('btn-luna').addClass('btn-danger');
    }

    // Activer l'onglet de sécurité pour montrer les paramètres VPN
    $('#security-tab').tab('show');
  });

  // Gestionnaire pour le bouton rapide Antivirus dans les actions rapides
  $('#antivirus-quick-toggle-btn').on('click', function() {
    if (settingsState.security.antivirus.enabled) {
      // Désactiver l'antivirus
      settingsState.security.antivirus.enabled = false;
      $(this).html('<i class="bi bi-virus me-1"></i> Activer Antivirus');
      $(this).removeClass('btn-success').addClass('btn-danger');
      $('#antivirus-toggle').prop('checked', false);

      // Mettre à jour le bouton dans la barre de navigation
      $('#antivirus-nav-status').text('ANTIVIRUS OFF');
      $('#antivirus-nav-toggle-btn').removeClass('btn-success').addClass('btn-danger');

      // Ajouter une entrée au journal
      addLogEntry('Antivirus désactivé');

      // Afficher une notification
      showNotification('Antivirus désactivé', 'warning');
    } else {
      // Activer l'antivirus
      settingsState.security.antivirus.enabled = true;
      $(this).html('<i class="bi bi-virus me-1"></i> Antivirus Actif');
      $(this).removeClass('btn-danger').addClass('btn-success');
      $('#antivirus-toggle').prop('checked', true);

      // Mettre à jour le bouton dans la barre de navigation
      $('#antivirus-nav-status').text('ANTIVIRUS ON');
      $('#antivirus-nav-toggle-btn').removeClass('btn-danger').addClass('btn-success');

      // Ajouter une entrée au journal
      addLogEntry('Antivirus activé');

      // Afficher une notification
      showNotification('Antivirus activé et protection en temps réel activée', 'success');
    }

    // Activer l'onglet de sécurité pour montrer les paramètres antivirus
    $('#security-tab').tab('show');
  });

  // Gestionnaire pour le bouton VPN dans la barre de navigation
  $('#vpn-nav-toggle-btn').on('click', function() {
    if (settingsState.security.vpn.connected) {
      disconnectVPN();
    } else {
      connectVPN();
    }

    // Rediriger vers la page des paramètres, onglet sécurité
    if (window.location.pathname !== '/luna/settings') {
      window.location.href = '/luna/settings#security';
    } else {
      $('#security-tab').tab('show');
    }
  });

  // Gestionnaire pour le bouton Antivirus dans la barre de navigation
  $('#antivirus-nav-toggle-btn').on('click', function() {
    if (settingsState.security.antivirus.enabled) {
      // Désactiver l'antivirus
      settingsState.security.antivirus.enabled = false;
      $(this).removeClass('btn-success').addClass('btn-danger');
      $('#antivirus-nav-status').text('ANTIVIRUS OFF');
      $('#antivirus-toggle').prop('checked', false);
      $('#antivirus-quick-toggle-btn').html('<i class="bi bi-virus me-1"></i> Activer Antivirus');
      $('#antivirus-quick-toggle-btn').removeClass('btn-success').addClass('btn-danger');

      // Ajouter une entrée au journal
      addLogEntry('Antivirus désactivé');

      // Afficher une notification
      showNotification('Antivirus désactivé', 'warning');
    } else {
      // Activer l'antivirus
      settingsState.security.antivirus.enabled = true;
      $(this).removeClass('btn-danger').addClass('btn-success');
      $('#antivirus-nav-status').text('ANTIVIRUS ON');
      $('#antivirus-toggle').prop('checked', true);
      $('#antivirus-quick-toggle-btn').html('<i class="bi bi-virus me-1"></i> Antivirus Actif');
      $('#antivirus-quick-toggle-btn').removeClass('btn-danger').addClass('btn-success');

      // Ajouter une entrée au journal
      addLogEntry('Antivirus activé');

      // Afficher une notification
      showNotification('Antivirus activé et protection en temps réel activée', 'success');
    }

    // Rediriger vers la page des paramètres, onglet sécurité
    if (window.location.pathname !== '/luna/settings') {
      window.location.href = '/luna/settings#security';
    } else {
      $('#security-tab').tab('show');
    }
  });

  // Gestionnaire pour le bouton d'analyse antivirus
  $('#scan-now-btn').on('click', function() {
    $(this).html('<i class="bi bi-search me-2 spin"></i> Analyse en cours...');
    $(this).prop('disabled', true);

    // Simuler une analyse
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      if (progress <= 100) {
        $(this).html(`<i class="bi bi-search me-2 spin"></i> Analyse en cours... ${progress}%`);
      } else {
        clearInterval(interval);
        $(this).html('<i class="bi bi-check-lg me-2"></i> Analyse terminée');

        // Mettre à jour la date de dernière analyse
        settingsState.security.antivirus.lastScan = new Date();

        // Ajouter une entrée au journal
        addLogEntry('Analyse antivirus terminée - Aucune menace détectée');

        // Restaurer le bouton après 3 secondes
        setTimeout(() => {
          $(this).html('<i class="bi bi-search me-2"></i> Analyser maintenant');
          $(this).prop('disabled', false);
        }, 3000);
      }
    }, 500);
  });

  // Ajouter des styles pour l'animation de rotation
  $('<style>')
    .text(`
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .spin {
        animation: spin 1s linear infinite;
      }
    `)
    .appendTo('head');

  // Initialiser l'état des boutons antivirus
  initAntivirusButtons();

  // Charger les paramètres au démarrage
  loadSettings();
});

// Fonction pour initialiser l'état des boutons antivirus
function initAntivirusButtons() {
  if (settingsState.security.antivirus.enabled) {
    // Antivirus activé
    $('#antivirus-nav-toggle-btn').removeClass('btn-danger').addClass('btn-success');
    $('#antivirus-nav-status').text('ANTIVIRUS ON');
    $('#antivirus-quick-toggle-btn').html('<i class="bi bi-virus me-1"></i> Antivirus Actif');
    $('#antivirus-quick-toggle-btn').removeClass('btn-danger').addClass('btn-success');
  } else {
    // Antivirus désactivé
    $('#antivirus-nav-toggle-btn').removeClass('btn-success').addClass('btn-danger');
    $('#antivirus-nav-status').text('ANTIVIRUS OFF');
    $('#antivirus-quick-toggle-btn').html('<i class="bi bi-virus me-1"></i> Activer Antivirus');
    $('#antivirus-quick-toggle-btn').removeClass('btn-success').addClass('btn-danger');
  }
}

// Fonction pour connecter le VPN
function connectVPN() {
  $('#vpn-connect-btn').html('<i class="bi bi-hourglass-split me-2"></i> Connexion en cours...');
  $('#vpn-connect-btn').prop('disabled', true);

  // Simuler la connexion
  setTimeout(() => {
    settingsState.security.vpn.connected = true;
    settingsState.security.vpn.connectionTime = new Date();

    // Mettre à jour l'interface
    updateVPNStatus();

    // Mettre à jour les informations VPN
    const server = $('#vpn-server-select option:selected').text().split(' ')[0];
    settingsState.security.vpn.ip = '192.168.1.XXX (masquée)';
    settingsState.security.vpn.location = server;
    settingsState.security.vpn.protection = 100;

    // Démarrer l'intervalle pour mettre à jour le temps de connexion
    startVPNUptimeInterval();

    // Ajouter une entrée au journal
    addLogEntry(`VPN connecté au serveur ${server}`);

    showNotification(`VPN connecté au serveur ${server}`, 'success');
  }, 2000);
}

// Fonction pour déconnecter le VPN
function disconnectVPN() {
  $('#vpn-connect-btn').html('<i class="bi bi-hourglass-split me-2"></i> Déconnexion en cours...');
  $('#vpn-connect-btn').prop('disabled', true);

  // Simuler la déconnexion
  setTimeout(() => {
    settingsState.security.vpn.connected = false;
    settingsState.security.vpn.connectionTime = null;

    // Mettre à jour l'interface
    updateVPNStatus();

    // Mettre à jour les informations VPN
    settingsState.security.vpn.ip = 'Non masquée';
    settingsState.security.vpn.location = 'Guadeloupe';
    settingsState.security.vpn.protection = 0;

    // Arrêter l'intervalle
    stopVPNUptimeInterval();

    // Ajouter une entrée au journal
    addLogEntry('VPN déconnecté');

    showNotification('VPN déconnecté', 'info');
  }, 1500);
}

// Fonction pour mettre à jour l'état du VPN dans l'interface
function updateVPNStatus() {
  if (settingsState.security.vpn.connected) {
    // Mettre à jour le bouton principal VPN
    $('#vpn-status-badge').removeClass('bg-danger').addClass('bg-success').text('Connecté');
    $('#vpn-connect-btn').html('<i class="bi bi-shield-x me-2"></i> Déconnecter le VPN');
    $('#vpn-connect-btn').removeClass('btn-luna').addClass('btn-danger');

    // Mettre à jour le bouton rapide VPN
    $('#vpn-quick-toggle-btn').html('<i class="bi bi-shield-x me-1"></i> Désactiver VPN');
    $('#vpn-quick-toggle-btn').removeClass('btn-luna').addClass('btn-danger');

    // Mettre à jour le bouton VPN dans la barre de navigation
    $('#vpn-nav-toggle-btn').removeClass('btn-luna').addClass('btn-success');
    $('#vpn-nav-status').text('VPN ON');

    // Ajouter une classe active à l'élément de menu VPN
    $('a.nav-link[href="/luna/vpn"]').addClass('active-vpn');
  } else {
    // Mettre à jour le bouton principal VPN
    $('#vpn-status-badge').removeClass('bg-success').addClass('bg-danger').text('Déconnecté');
    $('#vpn-connect-btn').html('<i class="bi bi-shield-check me-2"></i> Connecter le VPN');
    $('#vpn-connect-btn').removeClass('btn-danger').addClass('btn-luna');

    // Mettre à jour le bouton rapide VPN
    $('#vpn-quick-toggle-btn').html('<i class="bi bi-shield-check me-1"></i> Activer VPN');
    $('#vpn-quick-toggle-btn').removeClass('btn-danger').addClass('btn-luna');

    // Mettre à jour le bouton VPN dans la barre de navigation
    $('#vpn-nav-toggle-btn').removeClass('btn-success').addClass('btn-luna');
    $('#vpn-nav-status').text('VPN OFF');

    // Supprimer la classe active de l'élément de menu VPN
    $('a.nav-link[href="/luna/vpn"]').removeClass('active-vpn');
  }

  $('#vpn-ip').text(settingsState.security.vpn.ip);
  $('#vpn-location').text(settingsState.security.vpn.location);
  $('#vpn-protection').text(`${settingsState.security.vpn.protection}%`);

  if (settingsState.security.vpn.connectionTime) {
    updateVPNUptime();
  } else {
    $('#vpn-uptime').text('00:00:00');
  }
}

// Variables pour l'intervalle de mise à jour du temps de connexion VPN
let vpnUptimeInterval = null;

// Fonction pour démarrer l'intervalle de mise à jour du temps de connexion VPN
function startVPNUptimeInterval() {
  // Arrêter l'intervalle existant s'il y en a un
  stopVPNUptimeInterval();

  // Démarrer un nouvel intervalle
  vpnUptimeInterval = setInterval(updateVPNUptime, 1000);
}

// Fonction pour arrêter l'intervalle de mise à jour du temps de connexion VPN
function stopVPNUptimeInterval() {
  if (vpnUptimeInterval) {
    clearInterval(vpnUptimeInterval);
    vpnUptimeInterval = null;
  }
}

// Fonction pour mettre à jour le temps de connexion VPN
function updateVPNUptime() {
  if (!settingsState.security.vpn.connectionTime) return;

  const now = new Date();
  const diff = now - settingsState.security.vpn.connectionTime;

  // Convertir en heures, minutes, secondes
  const hours = Math.floor(diff / 3600000).toString().padStart(2, '0');
  const minutes = Math.floor((diff % 3600000) / 60000).toString().padStart(2, '0');
  const seconds = Math.floor((diff % 60000) / 1000).toString().padStart(2, '0');

  $('#vpn-uptime').text(`${hours}:${minutes}:${seconds}`);
}
