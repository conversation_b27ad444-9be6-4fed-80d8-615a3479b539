/**
 * Script pour l'interface d'analyse de livres
 */

document.addEventListener('DOMContentLoaded', () => {
  // Éléments DOM
  const booksListElement = document.getElementById('books-list');
  const recentBooksElement = document.getElementById('recent-books');
  const homeView = document.getElementById('home-view');
  const bookDetailView = document.getElementById('book-detail-view');
  const bookLoading = document.getElementById('book-loading');
  const bookContent = document.getElementById('book-content');
  const pageTitle = document.getElementById('page-title');
  const refreshBtn = document.getElementById('refresh-btn');
  const uploadBtn = document.getElementById('upload-btn');
  const deleteBookBtn = document.getElementById('delete-book-btn');
  const runMpcBtn = document.getElementById('run-mpc-btn');
  const mpcLoading = document.getElementById('mpc-loading');
  
  // Éléments du formulaire de téléchargement
  const uploadModal = new bootstrap.Modal(document.getElementById('upload-modal'));
  const uploadForm = document.getElementById('upload-form');
  const bookFileInput = document.getElementById('book-file');
  const modalUploadBtn = document.getElementById('modal-upload-btn');
  const modalProgressContainer = document.querySelector('#upload-modal .progress-container');
  const modalProgressBar = document.querySelector('#upload-modal .progress-bar');
  const modalProgressText = document.getElementById('modal-progress-text');
  
  // Éléments de la zone de glisser-déposer
  const dropArea = document.getElementById('drop-area');
  const fileInput = document.getElementById('file-input');
  const progressContainer = document.querySelector('#home-view .progress-container');
  const progressBar = document.querySelector('#home-view .progress-bar');
  const progressText = document.getElementById('progress-text');
  
  // Variables globales
  let currentBookId = null;
  let wordFrequencyChart = null;
  
  // Initialisation
  loadBooksList();
  loadRecentBooks();
  setupEventListeners();
  
  /**
   * Configurer les écouteurs d'événements
   */
  function setupEventListeners() {
    // Bouton de rafraîchissement
    refreshBtn.addEventListener('click', () => {
      if (currentBookId) {
        loadBookDetails(currentBookId);
      } else {
        loadBooksList();
        loadRecentBooks();
      }
    });
    
    // Bouton de téléchargement
    uploadBtn.addEventListener('click', () => {
      uploadModal.show();
    });
    
    // Bouton de téléchargement dans le modal
    modalUploadBtn.addEventListener('click', () => {
      const file = bookFileInput.files[0];
      if (file) {
        uploadBook(file, true);
      } else {
        alert('Veuillez sélectionner un fichier');
      }
    });
    
    // Bouton de suppression d'un livre
    deleteBookBtn.addEventListener('click', () => {
      if (currentBookId && confirm('Êtes-vous sûr de vouloir supprimer cette analyse ?')) {
        deleteBook(currentBookId);
      }
    });
    
    // Bouton d'exécution de MPC
    runMpcBtn.addEventListener('click', () => {
      if (currentBookId) {
        runMPC(currentBookId);
      }
    });
    
    // Zone de glisser-déposer
    dropArea.addEventListener('click', () => {
      fileInput.click();
    });
    
    fileInput.addEventListener('change', () => {
      const file = fileInput.files[0];
      if (file) {
        uploadBook(file, false);
      }
    });
    
    dropArea.addEventListener('dragover', (e) => {
      e.preventDefault();
      dropArea.classList.add('dragover');
    });
    
    dropArea.addEventListener('dragleave', () => {
      dropArea.classList.remove('dragover');
    });
    
    dropArea.addEventListener('drop', (e) => {
      e.preventDefault();
      dropArea.classList.remove('dragover');
      
      const file = e.dataTransfer.files[0];
      if (file) {
        fileInput.files = e.dataTransfer.files;
        uploadBook(file, false);
      }
    });
  }
  
  /**
   * Charger la liste des livres analysés
   */
  async function loadBooksList() {
    try {
      booksListElement.innerHTML = `
        <div class="text-center p-3">
          <div class="spinner-border spinner-border-sm text-light" role="status"></div>
          <div class="mt-2">Chargement des livres...</div>
        </div>
      `;
      
      const response = await fetch('/api/books/list');
      const data = await response.json();
      
      if (data.success) {
        if (data.analyses.length === 0) {
          booksListElement.innerHTML = `
            <div class="text-center p-3 text-muted">
              <i class="bi bi-inbox fs-4"></i>
              <div class="mt-2">Aucun livre analysé</div>
            </div>
          `;
          return;
        }
        
        let html = '';
        data.analyses.forEach(analysis => {
          html += `
            <a href="#" class="list-group-item list-group-item-action bg-dark text-light border-secondary book-item" data-book-id="${analysis.bookId}">
              <div class="d-flex justify-content-between align-items-start">
                <div>
                  <div class="fw-bold">${analysis.fileName}</div>
                  <div class="small text-muted">${formatDate(analysis.analysisDate)}</div>
                </div>
                <span class="badge bg-secondary">${analysis.fileType.toUpperCase()}</span>
              </div>
            </a>
          `;
        });
        
        booksListElement.innerHTML = html;
        
        // Ajouter les écouteurs d'événements pour les éléments de la liste
        document.querySelectorAll('.book-item').forEach(item => {
          item.addEventListener('click', (e) => {
            e.preventDefault();
            const bookId = item.getAttribute('data-book-id');
            loadBookDetails(bookId);
          });
        });
      } else {
        booksListElement.innerHTML = `
          <div class="text-center p-3 text-danger">
            <i class="bi bi-exclamation-triangle fs-4"></i>
            <div class="mt-2">Erreur lors du chargement des livres</div>
          </div>
        `;
      }
    } catch (error) {
      console.error('Error loading books list:', error);
      booksListElement.innerHTML = `
        <div class="text-center p-3 text-danger">
          <i class="bi bi-exclamation-triangle fs-4"></i>
          <div class="mt-2">Erreur lors du chargement des livres</div>
        </div>
      `;
    }
  }
  
  /**
   * Charger les livres récemment analysés
   */
  async function loadRecentBooks() {
    try {
      recentBooksElement.innerHTML = `
        <div class="text-center p-5">
          <div class="spinner-border text-light" role="status"></div>
          <div class="mt-3">Chargement des livres récents...</div>
        </div>
      `;
      
      const response = await fetch('/api/books/list');
      const data = await response.json();
      
      if (data.success) {
        if (data.analyses.length === 0) {
          recentBooksElement.innerHTML = `
            <div class="col-12 text-center p-5 text-muted">
              <i class="bi bi-inbox fs-1"></i>
              <div class="mt-3">Aucun livre analysé</div>
              <p class="mt-3">Téléchargez un livre pour commencer l'analyse</p>
            </div>
          `;
          return;
        }
        
        let html = '';
        // Limiter à 6 livres récents
        const recentAnalyses = data.analyses.slice(0, 6);
        
        recentAnalyses.forEach(analysis => {
          html += `
            <div class="col-md-6 col-lg-4 mb-4">
              <div class="card bg-dark border-secondary book-card h-100">
                <div class="card-body">
                  <h5 class="card-title">${analysis.fileName}</h5>
                  <div class="d-flex justify-content-between mb-3">
                    <span class="badge bg-secondary">${analysis.fileType.toUpperCase()}</span>
                    <small class="text-muted">${formatFileSize(analysis.fileSize)}</small>
                  </div>
                  <p class="card-text small">${analysis.summary}</p>
                </div>
                <div class="card-footer bg-dark border-secondary">
                  <button class="btn btn-outline-primary btn-sm w-100 view-book-btn" data-book-id="${analysis.bookId}">
                    <i class="bi bi-eye me-1"></i>Voir l'analyse
                  </button>
                </div>
              </div>
            </div>
          `;
        });
        
        recentBooksElement.innerHTML = html;
        
        // Ajouter les écouteurs d'événements pour les boutons de visualisation
        document.querySelectorAll('.view-book-btn').forEach(button => {
          button.addEventListener('click', () => {
            const bookId = button.getAttribute('data-book-id');
            loadBookDetails(bookId);
          });
        });
      } else {
        recentBooksElement.innerHTML = `
          <div class="col-12 text-center p-5 text-danger">
            <i class="bi bi-exclamation-triangle fs-1"></i>
            <div class="mt-3">Erreur lors du chargement des livres récents</div>
          </div>
        `;
      }
    } catch (error) {
      console.error('Error loading recent books:', error);
      recentBooksElement.innerHTML = `
        <div class="col-12 text-center p-5 text-danger">
          <i class="bi bi-exclamation-triangle fs-1"></i>
          <div class="mt-3">Erreur lors du chargement des livres récents</div>
        </div>
      `;
    }
  }
  
  /**
   * Charger les détails d'un livre
   * @param {string} bookId - ID du livre
   */
  async function loadBookDetails(bookId) {
    try {
      // Mettre à jour l'interface
      homeView.classList.add('d-none');
      bookDetailView.classList.remove('d-none');
      bookLoading.classList.remove('d-none');
      bookContent.classList.add('d-none');
      currentBookId = bookId;
      
      // Mettre à jour le titre de la page
      pageTitle.textContent = 'Analyse de Livre';
      
      // Charger les détails du livre
      const response = await fetch(`/api/books/analysis/${bookId}`);
      const data = await response.json();
      
      if (data.success) {
        const analysis = data.analysis;
        
        // Mettre à jour les informations du livre
        document.getElementById('book-title').textContent = analysis.fileName;
        document.getElementById('book-meta').textContent = `${analysis.fileType.toUpperCase()} • ${formatFileSize(analysis.fileSize)} • Analysé le ${formatDate(analysis.analysisDate)}`;
        document.getElementById('book-summary').textContent = analysis.summary;
        
        // Mettre à jour les statistiques
        document.getElementById('word-count').textContent = formatNumber(analysis.textAnalysis.statistics.wordCount);
        document.getElementById('char-count').textContent = formatNumber(analysis.textAnalysis.statistics.characterCount);
        document.getElementById('sentence-count').textContent = formatNumber(analysis.textAnalysis.statistics.sentenceCount);
        document.getElementById('paragraph-count').textContent = formatNumber(analysis.textAnalysis.statistics.paragraphCount);
        document.getElementById('avg-word-length').textContent = analysis.textAnalysis.statistics.averageWordLength.toFixed(2);
        document.getElementById('sentiment').textContent = analysis.textAnalysis.sentiment.interpretation;
        
        // Créer le graphique de fréquence des mots
        createWordFrequencyChart(analysis.textAnalysis.topWords.slice(0, 15));
        
        // Mettre à jour l'analyse MPC
        updateMPCResults(analysis.mpcResults);
        
        // Afficher le contenu
        bookLoading.classList.add('d-none');
        bookContent.classList.remove('d-none');
      } else {
        alert('Erreur lors du chargement des détails du livre');
        navigateToHome();
      }
    } catch (error) {
      console.error('Error loading book details:', error);
      alert('Erreur lors du chargement des détails du livre');
      navigateToHome();
    }
  }
  
  /**
   * Mettre à jour les résultats de l'analyse MPC
   * @param {Object} mpcResults - Résultats de l'analyse MPC
   */
  function updateMPCResults(mpcResults) {
    const predictedThemesElement = document.getElementById('predicted-themes');
    const recurringPatternsElement = document.getElementById('recurring-patterns');
    
    if (!mpcResults) {
      predictedThemesElement.innerHTML = `
        <div class="text-muted">Aucune analyse MPC n'a encore été effectuée.</div>
      `;
      recurringPatternsElement.innerHTML = `
        <div class="text-muted">Aucune analyse MPC n'a encore été effectuée.</div>
      `;
      return;
    }
    
    // Mettre à jour les thèmes prédits
    if (mpcResults.predictedThemes && mpcResults.predictedThemes.length > 0) {
      let themesHtml = '<div class="row">';
      
      mpcResults.predictedThemes.forEach(theme => {
        themesHtml += `
          <div class="col-md-6 mb-3">
            <div class="card bg-dark border-secondary">
              <div class="card-body">
                <h6 class="card-title">${theme.mainTerm}</h6>
                <div class="tag-cloud">
                  ${theme.relatedTerms.map(term => `
                    <span class="tag">${term.term} (${term.count})</span>
                  `).join('')}
                </div>
              </div>
            </div>
          </div>
        `;
      });
      
      themesHtml += '</div>';
      predictedThemesElement.innerHTML = themesHtml;
    } else {
      predictedThemesElement.innerHTML = `
        <div class="text-muted">Aucun thème prédit n'a été trouvé.</div>
      `;
    }
    
    // Mettre à jour les motifs récurrents
    if (mpcResults.recurringPatterns && mpcResults.recurringPatterns.length > 0) {
      let patternsHtml = '<div class="list-group">';
      
      mpcResults.recurringPatterns.forEach(pattern => {
        patternsHtml += `
          <div class="list-group-item bg-dark border-secondary">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <div class="fw-bold">"${pattern.pattern}"</div>
                <div class="small text-muted">${pattern.length} mots</div>
              </div>
              <span class="badge bg-primary rounded-pill">${pattern.count} occurrences</span>
            </div>
          </div>
        `;
      });
      
      patternsHtml += '</div>';
      recurringPatternsElement.innerHTML = patternsHtml;
    } else {
      recurringPatternsElement.innerHTML = `
        <div class="text-muted">Aucun motif récurrent n'a été trouvé.</div>
      `;
    }
  }
  
  /**
   * Créer le graphique de fréquence des mots
   * @param {Array} topWords - Liste des mots les plus fréquents
   */
  function createWordFrequencyChart(topWords) {
    // Détruire le graphique existant s'il y en a un
    if (wordFrequencyChart) {
      wordFrequencyChart.destroy();
    }
    
    const ctx = document.getElementById('word-frequency-chart').getContext('2d');
    
    const labels = topWords.map(word => word[0]);
    const data = topWords.map(word => word[1]);
    
    wordFrequencyChart = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: labels,
        datasets: [{
          label: 'Fréquence',
          data: data,
          backgroundColor: 'rgba(13, 110, 253, 0.7)',
          borderColor: 'rgba(13, 110, 253, 1)',
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              color: 'rgba(255, 255, 255, 0.1)'
            },
            ticks: {
              color: 'rgba(255, 255, 255, 0.7)'
            }
          },
          x: {
            grid: {
              color: 'rgba(255, 255, 255, 0.1)'
            },
            ticks: {
              color: 'rgba(255, 255, 255, 0.7)'
            }
          }
        },
        plugins: {
          legend: {
            labels: {
              color: 'rgba(255, 255, 255, 0.7)'
            }
          }
        }
      }
    });
  }
  
  /**
   * Télécharger un livre
   * @param {File} file - Fichier à télécharger
   * @param {boolean} fromModal - Indique si le téléchargement vient du modal
   */
  async function uploadBook(file, fromModal) {
    try {
      // Vérifier le type de fichier
      const fileType = file.name.split('.').pop().toLowerCase();
      if (!['pdf', 'epub', 'txt'].includes(fileType)) {
        alert('Type de fichier non pris en charge. Seuls les fichiers PDF, EPUB et TXT sont acceptés.');
        return;
      }
      
      // Vérifier la taille du fichier (max 50 Mo)
      if (file.size > 50 * 1024 * 1024) {
        alert('Le fichier est trop volumineux. La taille maximale est de 50 Mo.');
        return;
      }
      
      // Préparer le formulaire
      const formData = new FormData();
      formData.append('book', file);
      
      // Afficher la barre de progression
      if (fromModal) {
        modalProgressContainer.style.display = 'block';
        modalProgressBar.style.width = '0%';
        modalProgressText.textContent = 'Préparation...';
      } else {
        progressContainer.style.display = 'block';
        progressBar.style.width = '0%';
        progressText.textContent = 'Préparation...';
      }
      
      // Simuler la progression (car fetch ne fournit pas de progression pour les téléchargements)
      let progress = 0;
      const progressInterval = setInterval(() => {
        progress += 5;
        if (progress > 90) {
          clearInterval(progressInterval);
        }
        
        if (fromModal) {
          modalProgressBar.style.width = `${progress}%`;
          modalProgressText.textContent = `Téléchargement en cours... ${progress}%`;
        } else {
          progressBar.style.width = `${progress}%`;
          progressText.textContent = `Téléchargement en cours... ${progress}%`;
        }
      }, 500);
      
      // Envoyer le fichier
      const response = await fetch('/api/books/upload', {
        method: 'POST',
        body: formData
      });
      
      // Arrêter la simulation de progression
      clearInterval(progressInterval);
      
      const data = await response.json();
      
      if (data.success) {
        // Mettre à jour la progression à 100%
        if (fromModal) {
          modalProgressBar.style.width = '100%';
          modalProgressText.textContent = 'Téléchargement terminé !';
          
          // Fermer le modal après un court délai
          setTimeout(() => {
            uploadModal.hide();
            modalProgressContainer.style.display = 'none';
            uploadForm.reset();
            
            // Charger les détails du livre
            loadBookDetails(data.bookId);
          }, 1000);
        } else {
          progressBar.style.width = '100%';
          progressText.textContent = 'Téléchargement terminé !';
          
          // Masquer la barre de progression après un court délai
          setTimeout(() => {
            progressContainer.style.display = 'none';
            fileInput.value = '';
            
            // Charger les détails du livre
            loadBookDetails(data.bookId);
          }, 1000);
        }
        
        // Rafraîchir la liste des livres
        loadBooksList();
      } else {
        alert(`Erreur lors du téléchargement du livre: ${data.error}`);
        
        if (fromModal) {
          modalProgressContainer.style.display = 'none';
        } else {
          progressContainer.style.display = 'none';
        }
      }
    } catch (error) {
      console.error('Error uploading book:', error);
      alert('Erreur lors du téléchargement du livre');
      
      if (fromModal) {
        modalProgressContainer.style.display = 'none';
      } else {
        progressContainer.style.display = 'none';
      }
    }
  }
  
  /**
   * Supprimer un livre
   * @param {string} bookId - ID du livre à supprimer
   */
  async function deleteBook(bookId) {
    try {
      const response = await fetch(`/api/books/${bookId}`, {
        method: 'DELETE'
      });
      
      const data = await response.json();
      
      if (data.success) {
        alert('Analyse supprimée avec succès');
        navigateToHome();
        loadBooksList();
        loadRecentBooks();
      } else {
        alert(`Erreur lors de la suppression de l'analyse: ${data.error}`);
      }
    } catch (error) {
      console.error('Error deleting book:', error);
      alert('Erreur lors de la suppression de l\'analyse');
    }
  }
  
  /**
   * Exécuter l'analyse MPC sur un livre
   * @param {string} bookId - ID du livre
   */
  async function runMPC(bookId) {
    try {
      // Afficher l'indicateur de chargement
      mpcLoading.classList.remove('d-none');
      runMpcBtn.disabled = true;
      
      const response = await fetch(`/api/books/mpc/${bookId}`, {
        method: 'POST'
      });
      
      const data = await response.json();
      
      // Masquer l'indicateur de chargement
      mpcLoading.classList.add('d-none');
      runMpcBtn.disabled = false;
      
      if (data.success) {
        // Mettre à jour les résultats MPC
        updateMPCResults(data.mpcResults);
      } else {
        alert(`Erreur lors de l'exécution de MPC: ${data.error}`);
      }
    } catch (error) {
      console.error('Error running MPC:', error);
      alert('Erreur lors de l\'exécution de MPC');
      mpcLoading.classList.add('d-none');
      runMpcBtn.disabled = false;
    }
  }
  
  /**
   * Naviguer vers la page d'accueil
   */
  function navigateToHome() {
    homeView.classList.remove('d-none');
    bookDetailView.classList.add('d-none');
    pageTitle.textContent = 'Analyse de Livres';
    currentBookId = null;
  }
  
  /**
   * Formater une date
   * @param {string} dateString - Chaîne de date ISO
   * @returns {string} - Date formatée
   */
  function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
  
  /**
   * Formater la taille d'un fichier
   * @param {number} bytes - Taille en octets
   * @returns {string} - Taille formatée
   */
  function formatFileSize(bytes) {
    if (bytes < 1024) {
      return bytes + ' o';
    } else if (bytes < 1024 * 1024) {
      return (bytes / 1024).toFixed(1) + ' Ko';
    } else if (bytes < 1024 * 1024 * 1024) {
      return (bytes / (1024 * 1024)).toFixed(1) + ' Mo';
    } else {
      return (bytes / (1024 * 1024 * 1024)).toFixed(1) + ' Go';
    }
  }
  
  /**
   * Formater un nombre avec des séparateurs de milliers
   * @param {number} number - Nombre à formater
   * @returns {string} - Nombre formaté
   */
  function formatNumber(number) {
    return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
  }
});
