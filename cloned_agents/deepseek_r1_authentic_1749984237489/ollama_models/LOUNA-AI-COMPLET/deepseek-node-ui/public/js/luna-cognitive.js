/**
 * Luna - Système Cognitif
 *
 * Ce script gère les fonctionnalités du système cognitif de Luna,
 * y compris le traitement vidéo TLX, le traitement audio, l'analyse de texte,
 * et l'injection directe de connaissances dans le cerveau.
 */

/**
 * Met à jour la date et l'heure dans l'interface
 */
function updateDateTime() {
  const now = new Date();
  const dateOptions = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
  const timeOptions = { hour: '2-digit', minute: '2-digit', second: '2-digit' };

  // Mettre à jour l'élément current-datetime
  const datetimeElement = document.getElementById('current-datetime');
  if (datetimeElement) {
    datetimeElement.textContent = now.toLocaleString('fr-FR');
  }
}

document.addEventListener('DOMContentLoaded', function() {
  // Initialiser le graphique d'activité neuronale
  initNeuralActivityChart();

  // Initialiser les gestionnaires d'événements
  initVideoProcessing();
  initAudioProcessing();
  initTextProcessing();
  initKnowledgeInjection();
  initVideoIntegration();
  initLearningSystem();

  // Initialiser la date et l'heure
  updateDateTime();
  // Mettre à jour la date et l'heure toutes les secondes
  setInterval(updateDateTime, 1000);

  // Mettre à jour l'affichage de l'importance
  document.getElementById('importanceRange').addEventListener('input', function() {
    document.getElementById('importanceValue').textContent = this.value;
  });

  // Mettre à jour l'affichage de la profondeur d'apprentissage
  const learningDepthRange = document.getElementById('learningDepthRange');
  if (learningDepthRange) {
    learningDepthRange.addEventListener('input', function() {
      document.getElementById('learningDepthValue').textContent = this.value;
    });
  }

  // Connexion WebSocket
  const socket = io();

  // Écouter les mises à jour du système cognitif
  socket.on('cognitive_system_update', function(data) {
    updateSystemStatus(data);
  });

  // Écouter les mises à jour d'apprentissage
  socket.on('learning_update', function(data) {
    updateLearningProgress(data);
  });
});

/**
 * Initialise le graphique d'activité neuronale
 */
function initNeuralActivityChart() {
  // Approche complètement différente pour éviter l'accumulation de lignes
  // Utiliser un canvas et le dessiner manuellement

  const canvas = document.getElementById('neuralActivityChart');
  const ctx = canvas.getContext('2d');

  // S'assurer que le canvas a la bonne taille
  function resizeCanvas() {
    const container = canvas.parentElement;
    canvas.width = container.clientWidth;
    canvas.height = 180;
  }

  // Appeler une fois au démarrage
  resizeCanvas();

  // Écouter les changements de taille de fenêtre
  window.addEventListener('resize', resizeCanvas);

  // Générer des données aléatoires pour l'activité neuronale
  const dataPoints = Array.from({length: 10}, () => Math.random() * 100);

  // Fonction pour dessiner le graphique
  function drawChart() {
    // Effacer le canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Dessiner l'arrière-plan
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Calculer les dimensions
    const padding = 20;
    const chartWidth = canvas.width - (padding * 2);
    const chartHeight = canvas.height - (padding * 2);

    // Dessiner les axes
    ctx.beginPath();
    ctx.strokeStyle = '#dee2e6';
    ctx.lineWidth = 1;

    // Axe Y
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, canvas.height - padding);

    // Axe X
    ctx.moveTo(padding, canvas.height - padding);
    ctx.lineTo(canvas.width - padding, canvas.height - padding);

    ctx.stroke();

    // Dessiner la ligne de données
    if (dataPoints.length > 1) {
      ctx.beginPath();
      ctx.strokeStyle = 'rgba(13, 110, 253, 1)';
      ctx.lineWidth = 2;
      ctx.lineJoin = 'round';

      // Calculer l'espacement entre les points
      const pointSpacing = chartWidth / (dataPoints.length - 1);

      // Dessiner la ligne
      dataPoints.forEach((value, index) => {
        const x = padding + (index * pointSpacing);
        const y = padding + chartHeight - (value / 100 * chartHeight);

        if (index === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });

      ctx.stroke();

      // Dessiner la zone sous la ligne
      ctx.lineTo(padding + ((dataPoints.length - 1) * pointSpacing), canvas.height - padding);
      ctx.lineTo(padding, canvas.height - padding);
      ctx.closePath();
      ctx.fillStyle = 'rgba(13, 110, 253, 0.2)';
      ctx.fill();

      // Dessiner les points
      dataPoints.forEach((value, index) => {
        const x = padding + (index * pointSpacing);
        const y = padding + chartHeight - (value / 100 * chartHeight);

        ctx.beginPath();
        ctx.arc(x, y, 3, 0, Math.PI * 2);
        ctx.fillStyle = 'rgba(13, 110, 253, 1)';
        ctx.strokeStyle = '#fff';
        ctx.lineWidth = 1;
        ctx.fill();
        ctx.stroke();
      });
    }

    // Dessiner les étiquettes de l'axe X
    ctx.fillStyle = '#6c757d';
    ctx.font = '10px Arial';
    ctx.textAlign = 'center';

    const pointSpacing = chartWidth / (dataPoints.length - 1);
    for (let i = 0; i < dataPoints.length; i++) {
      const x = padding + (i * pointSpacing);
      const y = canvas.height - (padding / 2);
      ctx.fillText(`T-${10-i}`, x, y);
    }

    // Dessiner les étiquettes de l'axe Y
    ctx.textAlign = 'right';
    ctx.textBaseline = 'middle';

    for (let i = 0; i <= 5; i++) {
      const value = i * 20;
      const x = padding - 5;
      const y = padding + chartHeight - (value / 100 * chartHeight);
      ctx.fillText(value.toString(), x, y);
    }
  }

  // Dessiner le graphique initial
  drawChart();

  // Mettre à jour le graphique toutes les 2 secondes
  setInterval(() => {
    // Ajouter une nouvelle valeur et supprimer la plus ancienne
    dataPoints.shift();
    const newValue = Math.random() * 100;
    dataPoints.push(newValue);

    // Redessiner le graphique
    drawChart();

    // Mettre à jour l'intensité affichée
    const intensityElement = document.getElementById('neuralActivityValue');
    if (intensityElement) {
      intensityElement.textContent = newValue.toFixed(1);
    } else {
      // Fallback si l'élément spécifique n'est pas trouvé
      const firstNumericValue = document.querySelector('.numeric-value');
      if (firstNumericValue) {
        firstNumericValue.textContent = newValue.toFixed(1);
      }
    }
  }, 2000);
}

/**
 * Initialise le traitement vidéo
 */
function initVideoProcessing() {
  const startVideoBtn = document.getElementById('startVideoBtn');
  const processVideoBtn = document.getElementById('processVideoBtn');
  const videoElement = document.getElementById('videoElement');
  const videoPlaceholder = document.getElementById('videoPlaceholder');
  const videoProcessingResults = document.getElementById('videoProcessingResults');

  let videoStream = null;

  // Gestionnaire pour le bouton d'activation de la caméra
  startVideoBtn.addEventListener('click', async function() {
    try {
      if (videoStream) {
        // Arrêter la caméra
        videoStream.getTracks().forEach(track => track.stop());
        videoStream = null;
        videoElement.style.display = 'none';
        videoPlaceholder.style.display = 'flex';
        processVideoBtn.disabled = true;
        startVideoBtn.innerHTML = '<i class="bi bi-camera-video"></i> Activer la caméra';
        startVideoBtn.classList.remove('btn-danger');
        startVideoBtn.classList.add('btn-primary');
      } else {
        // Activer la caméra
        videoStream = await navigator.mediaDevices.getUserMedia({ video: true });
        videoElement.srcObject = videoStream;
        videoElement.style.display = 'block';
        videoPlaceholder.style.display = 'none';
        processVideoBtn.disabled = false;
        startVideoBtn.innerHTML = '<i class="bi bi-camera-video-off"></i> Désactiver la caméra';
        startVideoBtn.classList.remove('btn-primary');
        startVideoBtn.classList.add('btn-danger');
      }
    } catch (error) {
      console.error('Erreur lors de l\'accès à la caméra:', error);
      alert('Impossible d\'accéder à la caméra. Veuillez vérifier les permissions.');
    }
  });

  // Gestionnaire pour le bouton de traitement vidéo
  processVideoBtn.addEventListener('click', function() {
    if (!videoStream) return;

    // Simuler le traitement vidéo avec l'accélérateur TLX
    processVideoBtn.disabled = true;
    processVideoBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Traitement...';

    // Simuler un délai de traitement
    setTimeout(() => {
      // Afficher les résultats
      document.getElementById('detectedObjects').textContent = 'Personne, Chaise, Bureau, Ordinateur';
      document.getElementById('detectedFaces').textContent = '1 (confiance: 98%)';
      document.getElementById('environment').textContent = 'Intérieur, bien éclairé';
      document.getElementById('frameRate').textContent = '180';
      document.getElementById('resolution').textContent = '4K';
      document.getElementById('processingTime').textContent = '12.5';

      videoProcessingResults.style.display = 'block';

      // Réactiver le bouton
      processVideoBtn.disabled = false;
      processVideoBtn.innerHTML = '<i class="bi bi-cpu"></i> Traiter avec TLX';
    }, 1500);
  });
}

/**
 * Initialise le traitement audio
 */
function initAudioProcessing() {
  const startAudioBtn = document.getElementById('startAudioBtn');
  const processAudioBtn = document.getElementById('processAudioBtn');
  const audioWaveform = document.getElementById('audioWaveform');
  const audioPlaceholder = document.getElementById('audioPlaceholder');
  const audioProcessingResults = document.getElementById('audioProcessingResults');

  let audioStream = null;
  let audioContext = null;
  let analyser = null;
  let dataArray = null;
  let animationFrame = null;

  // Gestionnaire pour le bouton d'activation du microphone
  startAudioBtn.addEventListener('click', async function() {
    try {
      if (audioStream) {
        // Arrêter le microphone
        audioStream.getTracks().forEach(track => track.stop());
        audioStream = null;
        audioWaveform.style.display = 'none';
        audioPlaceholder.style.display = 'block';
        processAudioBtn.disabled = true;
        startAudioBtn.innerHTML = '<i class="bi bi-mic"></i> Activer le microphone';
        startAudioBtn.classList.remove('btn-danger');
        startAudioBtn.classList.add('btn-primary');

        // Arrêter l'animation
        if (animationFrame) {
          cancelAnimationFrame(animationFrame);
          animationFrame = null;
        }
      } else {
        // Activer le microphone
        audioStream = await navigator.mediaDevices.getUserMedia({ audio: true });
        audioWaveform.style.display = 'block';
        audioPlaceholder.style.display = 'none';
        processAudioBtn.disabled = false;
        startAudioBtn.innerHTML = '<i class="bi bi-mic-mute"></i> Désactiver le microphone';
        startAudioBtn.classList.remove('btn-primary');
        startAudioBtn.classList.add('btn-danger');

        // Initialiser l'analyseur audio
        audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const source = audioContext.createMediaStreamSource(audioStream);
        analyser = audioContext.createAnalyser();
        analyser.fftSize = 256;
        source.connect(analyser);

        // Créer le tableau de données
        const bufferLength = analyser.frequencyBinCount;
        dataArray = new Uint8Array(bufferLength);

        // Dessiner la forme d'onde
        const canvasCtx = audioWaveform.getContext('2d');

        function draw() {
          animationFrame = requestAnimationFrame(draw);
          analyser.getByteTimeDomainData(dataArray);

          canvasCtx.fillStyle = 'rgb(240, 240, 240)';
          canvasCtx.fillRect(0, 0, audioWaveform.width, audioWaveform.height);

          canvasCtx.lineWidth = 2;
          canvasCtx.strokeStyle = 'rgb(0, 123, 255)';
          canvasCtx.beginPath();

          const sliceWidth = audioWaveform.width / bufferLength;
          let x = 0;

          for (let i = 0; i < bufferLength; i++) {
            const v = dataArray[i] / 128.0;
            const y = v * audioWaveform.height / 2;

            if (i === 0) {
              canvasCtx.moveTo(x, y);
            } else {
              canvasCtx.lineTo(x, y);
            }

            x += sliceWidth;
          }

          canvasCtx.lineTo(audioWaveform.width, audioWaveform.height / 2);
          canvasCtx.stroke();
        }

        draw();
      }
    } catch (error) {
      console.error('Erreur lors de l\'accès au microphone:', error);
      alert('Impossible d\'accéder au microphone. Veuillez vérifier les permissions.');
    }
  });

  // Gestionnaire pour le bouton de traitement audio
  processAudioBtn.addEventListener('click', function() {
    if (!audioStream) return;

    // Simuler le traitement audio
    processAudioBtn.disabled = true;
    processAudioBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Analyse...';

    // Simuler un délai de traitement
    setTimeout(() => {
      // Afficher les résultats
      document.getElementById('speechDetected').textContent = 'Oui';
      document.getElementById('detectedLanguage').textContent = 'Français';
      document.getElementById('audioEmotion').textContent = 'Neutre';
      document.getElementById('audioLevel').textContent = '65';
      document.getElementById('audioClarity').textContent = 'Bonne';
      document.getElementById('audioProcessingTime').textContent = '8.2';

      audioProcessingResults.style.display = 'block';

      // Réactiver le bouton
      processAudioBtn.disabled = false;
      processAudioBtn.innerHTML = '<i class="bi bi-soundwave"></i> Analyser l\'audio';
    }, 1000);
  });
}

/**
 * Initialise le traitement de texte
 */
function initTextProcessing() {
  const processTextBtn = document.getElementById('processTextBtn');
  const textInput = document.getElementById('textInput');
  const textProcessingResults = document.getElementById('textProcessingResults');

  processTextBtn.addEventListener('click', function() {
    const text = textInput.value.trim();
    if (!text) {
      alert('Veuillez entrer du texte à analyser.');
      return;
    }

    // Simuler le traitement de texte
    processTextBtn.disabled = true;
    processTextBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Analyse...';

    // Simuler un délai de traitement
    setTimeout(() => {
      // Extraire des entités et mots-clés basés sur le texte réel
      const entities = extractEntities(text);
      const keywords = extractKeywords(text);
      const sentiment = analyzeSentiment(text);
      const language = detectLanguage(text);
      const complexity = calculateComplexity(text);

      // Afficher les résultats
      document.getElementById('textEntities').textContent = entities.join(', ');
      document.getElementById('textKeywords').textContent = keywords.join(', ');
      document.getElementById('textSentiment').textContent = sentiment;
      document.getElementById('textLanguage').textContent = language;
      document.getElementById('textComplexity').textContent = complexity;
      document.getElementById('textProcessingTime').textContent = (Math.random() * 10 + 5).toFixed(1);

      textProcessingResults.style.display = 'block';

      // Réactiver le bouton
      processTextBtn.disabled = false;
      processTextBtn.innerHTML = '<i class="bi bi-braces"></i> Analyser le texte';
    }, 1200);
  });
}

/**
 * Initialise l'injection de connaissances
 */
function initKnowledgeInjection() {
  const injectKnowledgeBtn = document.getElementById('injectKnowledgeBtn');
  const knowledgeInput = document.getElementById('knowledgeInput');
  const domainSelect = document.getElementById('domainSelect');
  const importanceRange = document.getElementById('importanceRange');
  const injectionResults = document.getElementById('injectionResults');

  injectKnowledgeBtn.addEventListener('click', function() {
    const knowledge = knowledgeInput.value.trim();
    if (!knowledge) {
      alert('Veuillez entrer une connaissance à injecter.');
      return;
    }

    const domain = domainSelect.value;
    const importance = importanceRange.value;

    // Simuler l'injection de connaissance
    injectKnowledgeBtn.disabled = true;
    injectKnowledgeBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Injection...';

    // Préparer les données pour l'injection
    const knowledgeData = {
      content: knowledge,
      domain: domain,
      importance: parseInt(importance),
      timestamp: new Date().toISOString()
    };

    // Simuler un appel API pour l'injection
    setTimeout(() => {
      // Extraire des mots-clés basés sur le texte réel
      const keywords = extractKeywords(knowledge);

      // Calculer des valeurs basées sur le contenu et l'importance
      const neuralConnections = Math.floor(knowledge.length / 5) + parseInt(importance) * 2;
      const integrationTime = Math.floor(knowledge.length * 1.5) + Math.random() * 50;

      // Déterminer la zone de stockage en fonction du domaine
      let storageZone = '';
      switch(domain) {
        case 'science':
          storageZone = 'Zone 1 (Scientifique)';
          break;
        case 'technologie':
          storageZone = 'Zone 2 (Technologique)';
          break;
        case 'art':
          storageZone = 'Zone 3 (Artistique)';
          break;
        case 'histoire':
          storageZone = 'Zone 4 (Historique)';
          break;
        case 'philosophie':
          storageZone = 'Zone 5 (Philosophique)';
          break;
        case 'mathematiques':
          storageZone = 'Zone 7 (Mathématique)';
          break;
        default:
          storageZone = 'Zone 6 (Archive)';
      }

      // Afficher les résultats
      document.getElementById('neuralConnections').textContent = neuralConnections;
      document.getElementById('extractedKeywords').textContent = keywords.join(', ');
      document.getElementById('storageZone').textContent = storageZone;
      document.getElementById('integrationTime').textContent = Math.floor(integrationTime);

      injectionResults.style.display = 'block';

      // Réactiver le bouton
      injectKnowledgeBtn.disabled = false;
      injectKnowledgeBtn.innerHTML = '<i class="bi bi-lightning"></i> Injecter au cerveau';
    }, 1500);
  });
}

/**
 * Met à jour l'état du système cognitif
 */
function updateSystemStatus(data) {
  // Mettre à jour les indicateurs d'état du système
  // Cette fonction serait appelée par les mises à jour WebSocket
}

// Fonctions utilitaires pour l'analyse de texte

function extractEntities(text) {
  // Analyse simple pour extraire des entités
  const entities = [];
  const words = text.split(/\s+/);

  // Rechercher des mots commençant par une majuscule (noms propres potentiels)
  words.forEach(word => {
    if (/^[A-Z][a-z]{2,}/.test(word)) {
      entities.push(word.replace(/[.,;:!?]$/, ''));
    }
  });

  return [...new Set(entities)].slice(0, 5); // Éliminer les doublons et limiter à 5
}

function extractKeywords(text) {
  // Liste de mots vides français
  const stopWords = ['le', 'la', 'les', 'un', 'une', 'des', 'et', 'ou', 'mais', 'donc', 'car', 'ni', 'que', 'qui', 'quoi', 'dont', 'où', 'comment', 'pourquoi', 'quand'];

  // Tokeniser et filtrer
  const words = text.toLowerCase()
    .replace(/[.,;:!?()]/g, '')
    .split(/\s+/)
    .filter(word => word.length > 3 && !stopWords.includes(word));

  // Compter les occurrences
  const wordCounts = {};
  words.forEach(word => {
    wordCounts[word] = (wordCounts[word] || 0) + 1;
  });

  // Trier par fréquence et prendre les 5 premiers
  return Object.entries(wordCounts)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 5)
    .map(entry => entry[0]);
}

function analyzeSentiment(text) {
  // Mots positifs et négatifs simples
  const positiveWords = ['bon', 'bien', 'super', 'excellent', 'génial', 'heureux', 'content', 'aime', 'positif', 'réussi'];
  const negativeWords = ['mauvais', 'mal', 'terrible', 'horrible', 'triste', 'déteste', 'négatif', 'échec', 'problème', 'difficile'];

  const lowerText = text.toLowerCase();
  let positiveCount = 0;
  let negativeCount = 0;

  positiveWords.forEach(word => {
    if (lowerText.includes(word)) positiveCount++;
  });

  negativeWords.forEach(word => {
    if (lowerText.includes(word)) negativeCount++;
  });

  if (positiveCount > negativeCount) return 'Positif';
  if (negativeCount > positiveCount) return 'Négatif';
  return 'Neutre';
}

function detectLanguage(text) {
  // Détection très basique basée sur des mots fréquents
  const frenchWords = ['le', 'la', 'les', 'un', 'une', 'des', 'et', 'ou', 'mais', 'donc', 'car', 'est', 'sont', 'pour', 'dans', 'avec', 'sur'];
  const englishWords = ['the', 'a', 'an', 'and', 'or', 'but', 'is', 'are', 'for', 'in', 'with', 'on', 'at', 'to', 'of'];

  const words = text.toLowerCase().split(/\s+/);
  let frenchCount = 0;
  let englishCount = 0;

  words.forEach(word => {
    if (frenchWords.includes(word)) frenchCount++;
    if (englishWords.includes(word)) englishCount++;
  });

  if (frenchCount > englishCount) return 'Français';
  if (englishCount > frenchCount) return 'Anglais';
  return 'Indéterminé';
}

function calculateComplexity(text) {
  // Calculer la complexité basée sur la longueur des phrases et des mots
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
  const words = text.split(/\s+/).filter(w => w.length > 0);

  if (sentences.length === 0 || words.length === 0) return 'Faible';

  const avgWordsPerSentence = words.length / sentences.length;
  const avgWordLength = words.join('').length / words.length;

  const complexityScore = (avgWordsPerSentence * 0.5) + (avgWordLength * 0.5);

  if (complexityScore > 12) return 'Élevée';
  if (complexityScore > 8) return 'Moyenne';
  return 'Faible';
}

/**
 * Initialise l'intégration vidéo
 */
function initVideoIntegration() {
  const videoUrlInput = document.getElementById('videoUrlInput');
  const loadVideoBtn = document.getElementById('loadVideoBtn');
  const extractVideoInfoBtn = document.getElementById('extractVideoInfoBtn');
  const learnFromVideoBtn = document.getElementById('learnFromVideoBtn');
  const videoIntegrationPlaceholder = document.getElementById('videoIntegrationPlaceholder');
  const videoIntegrationPlayer = document.getElementById('videoIntegrationPlayer');
  const videoIntegrationResults = document.getElementById('videoIntegrationResults');

  let player = null;
  let videoId = null;

  // Vérifier que tous les éléments sont présents
  if (!videoUrlInput || !loadVideoBtn || !extractVideoInfoBtn || !learnFromVideoBtn ||
      !videoIntegrationPlaceholder || !videoIntegrationPlayer || !videoIntegrationResults) {
    console.error('Éléments manquants pour l\'intégration vidéo');
    return;
  }

  // Gestionnaire pour le bouton de chargement de vidéo
  loadVideoBtn.addEventListener('click', function() {
    const url = videoUrlInput.value.trim();
    if (!url) {
      alert('Veuillez entrer une URL de vidéo valide.');
      return;
    }

    // Extraire l'ID de la vidéo YouTube
    videoId = extractYouTubeId(url);
    if (!videoId) {
      alert('URL YouTube invalide. Veuillez entrer une URL au format https://www.youtube.com/watch?v=ID_VIDEO');
      return;
    }

    // Charger la vidéo
    loadYouTubeVideo(videoId);
  });

  // Gestionnaire pour le bouton d'extraction d'informations
  extractVideoInfoBtn.addEventListener('click', function() {
    if (!videoId) return;

    // Désactiver le bouton pendant l'extraction
    extractVideoInfoBtn.disabled = true;
    extractVideoInfoBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Extraction...';

    // Simuler l'extraction d'informations
    setTimeout(() => {
      // Obtenir les informations de la vidéo
      const videoInfo = getVideoInfo(videoId);

      // Afficher les résultats
      document.getElementById('videoTitle').textContent = videoInfo.title;
      document.getElementById('videoDuration').textContent = videoInfo.duration;
      document.getElementById('videoCategory').textContent = videoInfo.category;
      document.getElementById('videoConcepts').textContent = videoInfo.concepts.join(', ');
      document.getElementById('videoKnowledge').textContent = videoInfo.knowledge;
      document.getElementById('videoAnalysisTime').textContent = Math.floor(Math.random() * 1000 + 500);

      // Afficher les résultats
      videoIntegrationResults.style.display = 'block';

      // Réactiver le bouton
      extractVideoInfoBtn.disabled = false;
      extractVideoInfoBtn.innerHTML = '<i class="bi bi-info-circle"></i> Extraire informations';

      // Activer le bouton d'apprentissage
      learnFromVideoBtn.disabled = false;
    }, 2000);
  });

  // Gestionnaire pour le bouton d'apprentissage à partir de la vidéo
  learnFromVideoBtn.addEventListener('click', function() {
    if (!videoId) return;

    // Désactiver le bouton pendant l'apprentissage
    learnFromVideoBtn.disabled = true;
    learnFromVideoBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Apprentissage...';

    // Sélectionner automatiquement la source d'apprentissage "vidéo"
    const learningSourceSelect = document.getElementById('learningSourceSelect');
    if (learningSourceSelect) {
      learningSourceSelect.value = 'video';
    }

    // Simuler l'apprentissage
    simulateLearningProcess('Vidéo YouTube: ' + document.getElementById('videoTitle').textContent);

    // Réactiver le bouton après un délai
    setTimeout(() => {
      learnFromVideoBtn.disabled = false;
      learnFromVideoBtn.innerHTML = '<i class="bi bi-mortarboard"></i> Apprendre de la vidéo';
    }, 10000); // 10 secondes
  });

  // Fonction pour extraire l'ID d'une vidéo YouTube
  function extractYouTubeId(url) {
    const regExp = /^.*((youtu.be\/)|(v\/)|(\/u\/\w\/)|(embed\/)|(watch\?))\??v?=?([^#&?]*).*/;
    const match = url.match(regExp);
    return (match && match[7].length === 11) ? match[7] : null;
  }

  // Fonction pour charger une vidéo YouTube
  function loadYouTubeVideo(id) {
    // Masquer le placeholder et afficher le lecteur
    videoIntegrationPlaceholder.style.display = 'none';
    videoIntegrationPlayer.style.display = 'block';

    // Si l'API YouTube est chargée
    if (typeof YT !== 'undefined' && YT.Player) {
      createYouTubePlayer(id);
    } else {
      // Sinon, attendre que l'API soit chargée
      window.onYouTubeIframeAPIReady = function() {
        createYouTubePlayer(id);
      };
    }
  }

  // Fonction pour créer le lecteur YouTube
  function createYouTubePlayer(id) {
    if (player) {
      player.loadVideoById(id);
    } else {
      player = new YT.Player(videoIntegrationPlayer, {
        height: '240',
        width: '100%',
        videoId: id,
        playerVars: {
          'playsinline': 1,
          'modestbranding': 1,
          'rel': 0
        },
        events: {
          'onReady': onPlayerReady,
          'onStateChange': onPlayerStateChange
        }
      });
    }
  }

  // Fonction appelée lorsque le lecteur est prêt
  function onPlayerReady(event) {
    // Activer le bouton d'extraction d'informations
    extractVideoInfoBtn.disabled = false;
  }

  // Fonction appelée lorsque l'état du lecteur change
  function onPlayerStateChange(event) {
    // Rien à faire pour l'instant
  }

  // Fonction pour obtenir des informations sur une vidéo (simulé)
  function getVideoInfo(id) {
    // Simuler des informations sur la vidéo
    const titles = [
      'Introduction à l\'Intelligence Artificielle',
      'Comprendre le Deep Learning',
      'Les bases du Machine Learning',
      'Réseaux de neurones expliqués',
      'L\'avenir de l\'IA'
    ];

    const categories = [
      'Éducation',
      'Science & Technologie',
      'Formation',
      'Tutoriel',
      'Conférence'
    ];

    const conceptsList = [
      ['Intelligence Artificielle', 'Apprentissage automatique', 'Algorithmes'],
      ['Réseaux de neurones', 'Backpropagation', 'Gradient descent'],
      ['Classification', 'Régression', 'Clustering'],
      ['Perceptron', 'Couches cachées', 'Fonction d\'activation'],
      ['AGI', 'Singularité technologique', 'Éthique de l\'IA']
    ];

    // Générer un index aléatoire
    const index = Math.floor(Math.random() * titles.length);

    return {
      title: titles[index],
      duration: `${Math.floor(Math.random() * 30) + 5}:${Math.floor(Math.random() * 60).toString().padStart(2, '0')}`,
      category: categories[index],
      concepts: conceptsList[index],
      knowledge: Math.floor(Math.random() * 50) + 10
    };
  }
}

/**
 * Initialise le système d'apprentissage
 */
function initLearningSystem() {
  const learningSourceSelect = document.getElementById('learningSourceSelect');
  const learningTopicInput = document.getElementById('learningTopicInput');
  const learningDepthRange = document.getElementById('learningDepthRange');
  const startLearningBtn = document.getElementById('startLearningBtn');
  const learningResults = document.getElementById('learningResults');
  const learningStats = document.getElementById('learningStats');

  // Vérifier que tous les éléments sont présents
  if (!learningSourceSelect || !learningTopicInput || !learningDepthRange ||
      !startLearningBtn || !learningResults || !learningStats) {
    console.error('Éléments manquants pour le système d\'apprentissage');
    return;
  }

  // Gestionnaire pour le bouton de démarrage d'apprentissage
  startLearningBtn.addEventListener('click', function() {
    const topic = learningTopicInput.value.trim();
    if (!topic) {
      alert('Veuillez entrer un sujet d\'apprentissage.');
      return;
    }

    // Démarrer le processus d'apprentissage
    simulateLearningProcess(topic);
  });
}

/**
 * Simule un processus d'apprentissage
 * @param {string} topic - Le sujet d'apprentissage
 */
function simulateLearningProcess(topic) {
  const learningResults = document.getElementById('learningResults');
  const learningStats = document.getElementById('learningStats');
  const learningProgressBar = document.getElementById('learningProgressBar');
  const learningStatus = document.getElementById('learningStatus');
  const learningProgressValue = document.getElementById('learningProgressValue');
  const startLearningBtn = document.getElementById('startLearningBtn');
  const learningSource = document.getElementById('learningSourceSelect').value;
  const learningDepth = parseInt(document.getElementById('learningDepthRange').value);

  // Afficher les résultats et masquer les statistiques
  learningResults.style.display = 'block';
  learningStats.style.display = 'none';

  // Désactiver le bouton pendant l'apprentissage
  startLearningBtn.disabled = true;

  // Initialiser la barre de progression
  learningProgressBar.style.width = '0%';
  learningProgressBar.setAttribute('aria-valuenow', 0);
  learningStatus.textContent = 'Initialisation...';
  learningProgressValue.textContent = '0%';

  // Simuler les étapes d'apprentissage
  const steps = [
    { name: 'Recherche de sources', duration: 1000 },
    { name: 'Analyse des concepts', duration: 1500 },
    { name: 'Extraction de connaissances', duration: 2000 },
    { name: 'Création de connexions neuronales', duration: 1500 },
    { name: 'Intégration dans la mémoire', duration: 2000 },
    { name: 'Optimisation des connexions', duration: 1000 },
    { name: 'Finalisation', duration: 1000 }
  ];

  let currentStep = 0;
  let totalDuration = steps.reduce((sum, step) => sum + step.duration, 0);
  let elapsedTime = 0;

  // Fonction pour mettre à jour la progression
  function updateProgress() {
    if (currentStep >= steps.length) {
      // Apprentissage terminé
      learningProgressBar.style.width = '100%';
      learningProgressBar.setAttribute('aria-valuenow', 100);
      learningStatus.textContent = 'Apprentissage terminé';
      learningProgressValue.textContent = '100%';

      // Afficher les statistiques
      showLearningStats(topic, learningSource, learningDepth);

      // Réactiver le bouton
      startLearningBtn.disabled = false;

      return;
    }

    // Mettre à jour l'étape actuelle
    const step = steps[currentStep];
    learningStatus.textContent = step.name;

    // Calculer la progression
    elapsedTime += 100; // 100ms par mise à jour
    const stepProgress = Math.min(1, elapsedTime / step.duration);
    const overallProgress = ((currentStep + stepProgress) / steps.length) * 100;

    // Mettre à jour la barre de progression
    learningProgressBar.style.width = `${overallProgress}%`;
    learningProgressBar.setAttribute('aria-valuenow', overallProgress);
    learningProgressValue.textContent = `${Math.round(overallProgress)}%`;

    // Passer à l'étape suivante si l'étape actuelle est terminée
    if (stepProgress >= 1) {
      currentStep++;
      elapsedTime = 0;
    }

    // Continuer la mise à jour
    setTimeout(updateProgress, 100);
  }

  // Démarrer la mise à jour de la progression
  updateProgress();
}

/**
 * Affiche les statistiques d'apprentissage
 * @param {string} topic - Le sujet d'apprentissage
 * @param {string} source - La source d'apprentissage
 * @param {number} depth - La profondeur d'apprentissage
 */
function showLearningStats(topic, source, depth) {
  const learningStats = document.getElementById('learningStats');
  const conceptsLearned = document.getElementById('conceptsLearned');
  const connectionsCreated = document.getElementById('connectionsCreated');
  const iqGain = document.getElementById('iqGain');
  const learningTime = document.getElementById('learningTime');

  // Calculer les statistiques en fonction de la profondeur et de la source
  const concepts = Math.floor((depth * 2) + Math.random() * 10);
  const connections = Math.floor((concepts * 3) + Math.random() * 20);
  const iq = (depth / 10) + (Math.random() * 0.5);
  const time = Math.floor(10 + (depth * 1.5));

  // Afficher les statistiques
  conceptsLearned.textContent = concepts;
  connectionsCreated.textContent = connections;
  iqGain.textContent = iq.toFixed(2);
  learningTime.textContent = time;

  // Afficher le conteneur de statistiques
  learningStats.style.display = 'block';

  // Mettre à jour les statistiques globales
  updateLearningLevel(concepts);
}

/**
 * Met à jour le niveau d'apprentissage
 * @param {number} concepts - Le nombre de concepts appris
 */
function updateLearningLevel(concepts) {
  const learningLevel = document.getElementById('learningLevel');
  const learningLevelBar = learningLevel.parentElement.querySelector('.progress-bar');

  if (learningLevel && learningLevelBar) {
    // Récupérer le niveau actuel
    let currentLevel = parseInt(learningLevel.textContent);

    // Calculer le nouveau niveau (1 niveau tous les 100 concepts)
    const levelProgress = (concepts / 100);
    if (levelProgress >= 1) {
      currentLevel += Math.floor(levelProgress);
      learningLevel.textContent = currentLevel;
    }

    // Mettre à jour la barre de progression
    const progress = (levelProgress % 1) * 100;
    learningLevelBar.style.width = `${progress}%`;
    learningLevelBar.setAttribute('aria-valuenow', progress);
  }
}

/**
 * Met à jour la progression de l'apprentissage (appelé par les événements WebSocket)
 * @param {Object} data - Les données de mise à jour
 */
function updateLearningProgress(data) {
  // Cette fonction serait appelée par les mises à jour WebSocket
  // Pour l'instant, elle ne fait rien car nous simulons l'apprentissage
}
