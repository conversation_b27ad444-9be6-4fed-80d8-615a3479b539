/**
 * Luna Memory - Gestion de la mémoire thermique
 * Ce script gère l'interface de visualisation et de contrôle de la mémoire thermique
 */

/**
 * Met à jour la date et l'heure dans l'interface
 */
function updateDateTime() {
  const now = new Date();

  // Mettre à jour l'élément current-datetime
  const datetimeElement = document.getElementById('current-datetime');
  if (datetimeElement) {
    datetimeElement.textContent = now.toLocaleString('fr-FR');
  }
}

// État global de la mémoire
const memoryState = {
  zones: [
    { id: 1, name: 'Récente', temperature: 100, color: '#ff5252', conversations: 34 },
    { id: 2, name: '<PERSON><PERSON>', temperature: 80, color: '#ff9800', conversations: 0 },
    { id: 3, name: 'Ti<PERSON><PERSON>', temperature: 60, color: '#29b6f6', conversations: 0 },
    { id: 4, name: '<PERSON><PERSON><PERSON><PERSON>', temperature: 40, color: '#5c6bc0', conversations: 0 },
    { id: 5, name: '<PERSON><PERSON><PERSON>', temperature: 20, color: '#78909c', conversations: 0 },
    { id: 6, name: 'Archive', temperature: 5, color: '#424242', conversations: 0 }
  ],
  totalCapacity: 1000,
  usedCapacity: 34,
  acceleratorEfficiency: 166.5,
  thermalFlowData: {
    hotPoints: 3,
    coldPoints: 2,
    transfers: 12
  }
};

// Connexion Socket.io
let socket;

// Initialisation au chargement du document
document.addEventListener('DOMContentLoaded', function() {
  // Initialiser la connexion Socket.io
  initializeSocket();

  // Initialiser les gestionnaires d'événements
  initializeEventHandlers();

  // Initialiser les zones de mémoire
  initializeMemoryZones();

  // Initialiser les animations de transition
  initializeTransitionAnimations();

  // Initialiser la date et l'heure
  updateDateTime();
  // Mettre à jour la date et l'heure toutes les secondes
  setInterval(updateDateTime, 1000);

  console.log('Luna Memory Interface initialized');
});

/**
 * Initialise la connexion Socket.io
 */
function initializeSocket() {
  socket = io();

  // Écouter les mises à jour de l'état de la mémoire
  socket.on('memory state update', function(data) {
    updateMemoryState(data);
  });

  // Écouter les événements de transfert thermique
  socket.on('thermal transfer', function(data) {
    animateThermalTransfer(data.fromZone, data.toZone);
  });

  // Écouter les événements de création de connexion neurale
  socket.on('neural connection', function(data) {
    showNeuralConnection(data.fromZone, data.toZone, data.strength);
  });
}

/**
 * Initialise les gestionnaires d'événements
 */
function initializeEventHandlers() {
  // Gestionnaire pour le bouton de rafraîchissement
  document.getElementById('refresh-memory-btn').addEventListener('click', function() {
    refreshMemoryData();
  });

  // Gestionnaire pour le bouton d'optimisation
  document.getElementById('optimize-memory-btn').addEventListener('click', function() {
    optimizeMemory();
  });

  // Gestionnaire pour le bouton d'effacement
  document.getElementById('clear-memory-btn').addEventListener('click', function() {
    clearMemory();
  });

  // Gestionnaire pour les zones de mémoire
  document.querySelectorAll('.memory-zone').forEach(function(zone) {
    zone.addEventListener('click', function() {
      const zoneId = this.getAttribute('data-zone');
      selectMemoryZone(zoneId);
    });
  });
}

/**
 * Initialise les zones de mémoire
 */
function initializeMemoryZones() {
  // Mettre à jour les badges de conversation pour chaque zone
  memoryState.zones.forEach(function(zone) {
    const zoneElement = document.querySelector(`.memory-zone[data-zone="${zone.id}"]`);
    if (zoneElement) {
      const badge = zoneElement.querySelector('.badge');
      if (badge) {
        badge.textContent = `${zone.conversations} conversations`;
      }
    }
  });

  // Activer la première zone par défaut
  selectMemoryZone(1);
}

/**
 * Initialise les animations de transition entre zones
 */
function initializeTransitionAnimations() {
  // Ajouter des indices aux éléments de mémoire pour les animations décalées
  document.querySelectorAll('.memory-item').forEach(function(item, index) {
    item.style.setProperty('--item-index', index);
  });
}

/**
 * Sélectionne une zone de mémoire
 * @param {number} zoneId - ID de la zone à sélectionner
 */
function selectMemoryZone(zoneId) {
  // Désactiver toutes les zones
  document.querySelectorAll('.memory-zone').forEach(function(zone) {
    zone.classList.remove('active');
  });

  // Activer la zone sélectionnée
  const selectedZone = document.querySelector(`.memory-zone[data-zone="${zoneId}"]`);
  if (selectedZone) {
    selectedZone.classList.add('active');
  }
}

/**
 * Rafraîchit les données de mémoire
 */
function refreshMemoryData() {
  // Désactiver le bouton pendant le rafraîchissement
  const refreshBtn = document.getElementById('refresh-memory-btn');
  refreshBtn.disabled = true;
  refreshBtn.innerHTML = '<i class="bi bi-arrow-repeat spin me-1"></i> Rafraîchissement...';

  // Simuler une requête au serveur
  setTimeout(function() {
    // Réactiver le bouton
    refreshBtn.disabled = false;
    refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i> Rafraîchir les données';

    // Mettre à jour les données (simulé)
    updateMemoryState({
      zones: [
        { id: 1, conversations: 36 },
        { id: 2, conversations: 2 },
        { id: 3, conversations: 0 },
        { id: 4, conversations: 0 },
        { id: 5, conversations: 0 },
        { id: 6, conversations: 0 }
      ],
      usedCapacity: 38,
      acceleratorEfficiency: 168.2,
      thermalFlowData: {
        hotPoints: 4,
        coldPoints: 2,
        transfers: 15
      }
    });

    // Afficher une notification
    showNotification('Données de mémoire rafraîchies', 'success');
  }, 1000);
}

/**
 * Optimise la mémoire
 */
function optimizeMemory() {
  // Désactiver le bouton pendant l'optimisation
  const optimizeBtn = document.getElementById('optimize-memory-btn');
  optimizeBtn.disabled = true;
  optimizeBtn.innerHTML = '<i class="bi bi-lightning-charge-fill spin me-1"></i> Optimisation...';

  // Simuler une requête au serveur
  setTimeout(function() {
    // Réactiver le bouton
    optimizeBtn.disabled = false;
    optimizeBtn.innerHTML = '<i class="bi bi-lightning-charge me-1"></i> Optimiser la mémoire';

    // Mettre à jour les données (simulé)
    updateMemoryState({
      acceleratorEfficiency: 175.8,
      thermalFlowData: {
        hotPoints: 5,
        coldPoints: 1,
        transfers: 18
      }
    });

    // Afficher une notification
    showNotification('Mémoire optimisée avec succès', 'success');

    // Simuler une animation de connexion neurale
    showNeuralConnection(1, 3, 45);
  }, 2000);
}

/**
 * Efface la mémoire
 */
function clearMemory() {
  // Demander confirmation
  if (confirm('Êtes-vous sûr de vouloir effacer toute la mémoire thermique ? Cette action est irréversible.')) {
    // Désactiver le bouton pendant l'effacement
    const clearBtn = document.getElementById('clear-memory-btn');
    clearBtn.disabled = true;
    clearBtn.innerHTML = '<i class="bi bi-trash-fill spin me-1"></i> Effacement...';

    // Simuler une requête au serveur
    setTimeout(function() {
      // Réactiver le bouton
      clearBtn.disabled = false;
      clearBtn.innerHTML = '<i class="bi bi-trash me-1"></i> Effacer la mémoire';

      // Mettre à jour les données (simulé)
      updateMemoryState({
        zones: [
          { id: 1, conversations: 0 },
          { id: 2, conversations: 0 },
          { id: 3, conversations: 0 },
          { id: 4, conversations: 0 },
          { id: 5, conversations: 0 },
          { id: 6, conversations: 0 }
        ],
        usedCapacity: 0
      });

      // Vider le contenu des zones
      document.querySelectorAll('.zone-content').forEach(function(content) {
        content.innerHTML = `
          <div class="empty-zone-message">
            <i class="bi bi-info-circle"></i>
            <p>Aucune conversation dans cette zone</p>
          </div>
        `;
      });

      // Afficher une notification
      showNotification('Mémoire thermique effacée', 'warning');
    }, 1500);
  }
}

/**
 * Met à jour l'état de la mémoire
 * @param {Object} data - Nouvelles données d'état
 */
function updateMemoryState(data) {
  // Mettre à jour les zones
  if (data.zones) {
    data.zones.forEach(function(zoneUpdate) {
      const zoneIndex = memoryState.zones.findIndex(z => z.id === zoneUpdate.id);
      if (zoneIndex !== -1) {
        // Mettre à jour le nombre de conversations
        memoryState.zones[zoneIndex].conversations = zoneUpdate.conversations;

        // Mettre à jour l'affichage
        const zoneElement = document.querySelector(`.memory-zone[data-zone="${zoneUpdate.id}"]`);
        if (zoneElement) {
          const badge = zoneElement.querySelector('.badge');
          if (badge) {
            badge.textContent = `${zoneUpdate.conversations} conversations`;
          }
        }
      }
    });
  }

  // Mettre à jour la capacité utilisée
  if (data.usedCapacity !== undefined) {
    memoryState.usedCapacity = data.usedCapacity;
    document.getElementById('memory-usage').textContent = `${data.usedCapacity}/${memoryState.totalCapacity}`;

    // Mettre à jour la barre de progression
    const usagePercentage = (data.usedCapacity / memoryState.totalCapacity) * 100;
    document.querySelector('#memory-usage').nextElementSibling.querySelector('.progress-bar').style.width = `${usagePercentage}%`;
  }

  // Mettre à jour l'efficacité des accélérateurs
  if (data.acceleratorEfficiency !== undefined) {
    memoryState.acceleratorEfficiency = data.acceleratorEfficiency;
    document.getElementById('accelerator-efficiency').textContent = `${data.acceleratorEfficiency.toFixed(1)}%`;

    // Mettre à jour la barre de progression
    const efficiencyPercentage = Math.min(100, data.acceleratorEfficiency / 2);
    document.querySelector('#accelerator-efficiency').nextElementSibling.querySelector('.progress-bar').style.width = `${efficiencyPercentage}%`;
  }

  // Mettre à jour les données de flux thermique
  if (data.thermalFlowData) {
    memoryState.thermalFlowData = data.thermalFlowData;
    document.getElementById('hot-points').textContent = data.thermalFlowData.hotPoints;
    document.getElementById('cold-points').textContent = data.thermalFlowData.coldPoints;
    document.getElementById('thermal-transfers').textContent = data.thermalFlowData.transfers;
  }
}

/**
 * Anime un transfert thermique entre deux zones
 * @param {number} fromZone - ID de la zone source
 * @param {number} toZone - ID de la zone destination
 */
function animateThermalTransfer(fromZone, toZone) {
  // Trouver les éléments de zone
  const fromElement = document.querySelector(`.memory-zone[data-zone="${fromZone}"]`);
  const toElement = document.querySelector(`.memory-zone[data-zone="${toZone}"]`);

  if (fromElement && toElement) {
    // Créer un élément d'animation
    const transferElement = document.createElement('div');
    transferElement.className = 'thermal-transfer';
    transferElement.style.position = 'absolute';
    transferElement.style.width = '10px';
    transferElement.style.height = '10px';
    transferElement.style.borderRadius = '50%';
    transferElement.style.background = 'radial-gradient(circle, rgba(240,166,202,1) 0%, rgba(156,137,184,1) 100%)';
    transferElement.style.boxShadow = '0 0 10px rgba(240,166,202,0.8)';
    transferElement.style.zIndex = '1000';

    // Ajouter au conteneur
    document.getElementById('memory-container').appendChild(transferElement);

    // Calculer les positions
    const fromRect = fromElement.getBoundingClientRect();
    const toRect = toElement.getBoundingClientRect();
    const containerRect = document.getElementById('memory-container').getBoundingClientRect();

    // Position initiale
    transferElement.style.left = `${fromRect.left - containerRect.left + fromRect.width / 2}px`;
    transferElement.style.top = `${fromRect.top - containerRect.top + fromRect.height / 2}px`;

    // Animer vers la destination
    setTimeout(() => {
      transferElement.style.transition = 'all 1s cubic-bezier(0.25, 0.1, 0.25, 1)';
      transferElement.style.left = `${toRect.left - containerRect.left + toRect.width / 2}px`;
      transferElement.style.top = `${toRect.top - containerRect.top + toRect.height / 2}px`;

      // Supprimer après l'animation
      setTimeout(() => {
        transferElement.remove();
      }, 1000);
    }, 10);
  }
}

/**
 * Affiche une connexion neurale entre deux zones
 * @param {number} fromZone - ID de la zone source
 * @param {number} toZone - ID de la zone destination
 * @param {number} strength - Force de la connexion (0-100)
 */
function showNeuralConnection(fromZone, toZone, strength) {
  // Afficher une notification
  showNotification(`Connexion neurale créée: Zone ${fromZone} ↔ Zone ${toZone} (force: ${strength}%)`, 'info');

  // Animer un transfert dans les deux sens
  animateThermalTransfer(fromZone, toZone);
  setTimeout(() => {
    animateThermalTransfer(toZone, fromZone);
  }, 500);
}

/**
 * Affiche une notification
 * @param {string} message - Message à afficher
 * @param {string} type - Type de notification (info, success, warning, error)
 */
function showNotification(message, type = 'info') {
  // Créer l'élément de notification s'il n'existe pas
  let notificationArea = document.getElementById('notification-area');
  if (!notificationArea) {
    notificationArea = document.createElement('div');
    notificationArea.id = 'notification-area';
    notificationArea.style.position = 'fixed';
    notificationArea.style.top = '20px';
    notificationArea.style.right = '20px';
    notificationArea.style.zIndex = '9999';
    notificationArea.style.width = '300px';
    document.body.appendChild(notificationArea);
  }

  // Créer la notification
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  notification.innerHTML = message;
  notificationArea.appendChild(notification);

  // Afficher avec animation
  setTimeout(() => {
    notification.classList.add('show');

    // Masquer après 3 secondes
    setTimeout(() => {
      notification.classList.remove('show');
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }, 100);
}
