/**
 * Correctif pour le template luna-memory.ejs
 * Ce script définit la variable memoryStats qui manque dans le template
 */

// Définir memoryStats si non défini
window.addEventListener('DOMContentLoaded', () => {
    // Vérifier si memoryStats est déjà défini
    if (typeof window.memoryStats === 'undefined') {
        // Définir memoryStats avec des valeurs par défaut
        window.memoryStats = {
            zones: [
                { name: 'Récente', count: 150, percentage: 15 },
                { name: '<PERSON><PERSON>', count: 250, percentage: 25 },
                { name: '<PERSON>i<PERSON><PERSON>', count: 200, percentage: 20 },
                { name: '<PERSON><PERSON><PERSON><PERSON>', count: 150, percentage: 15 },
                { name: '<PERSON><PERSON><PERSON>', count: 150, percentage: 15 },
                { name: 'Archive', count: 100, percentage: 10 }
            ],
            total: 1000
        };
        
        console.log('Variable memoryStats définie avec des valeurs par défaut');
    }
});
