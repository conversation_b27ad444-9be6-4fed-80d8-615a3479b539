document.addEventListener('DOMContentLoaded', () => {
  // Éléments DOM
  const chatForm = document.getElementById('chat-form');
  const messageInput = document.getElementById('message-input');
  const chatMessages = document.getElementById('chat-messages');
  const ollamaStatus = document.getElementById('ollama-status');
  const modelSelector = document.getElementById('model-selector');
  const refreshModelsBtn = document.getElementById('refresh-models-btn');
  const downloadModelBtn = document.getElementById('download-model-btn');
  const saveSettingsBtn = document.getElementById('save-settings-btn');
  const temperatureSlider = document.getElementById('temperature');
  const temperatureValue = document.getElementById('temperature-value');
  const maxTokensSlider = document.getElementById('max-tokens');
  const maxTokensValue = document.getElementById('max-tokens-value');
  const newChatBtn = document.getElementById('new-chat-btn');
  const clearChatBtn = document.getElementById('clear-chat-btn');
  const exportChatBtn = document.getElementById('export-chat-btn');
  const conversationsList = document.getElementById('conversations-list');
  const currentChatTitle = document.getElementById('current-chat-title');

  // Éléments du modal de téléchargement
  const downloadModelModal = new bootstrap.Modal(document.getElementById('download-model-modal'));
  const modelButtons = document.querySelectorAll('.list-group-item[data-model]');
  const customModelNameInput = document.getElementById('custom-model-name');
  const confirmDownloadBtn = document.getElementById('confirm-download-btn');

  // Variables globales
  let socket;
  let currentConversationId = generateId();
  let conversations = loadConversations();
  let messageHistory = [];
  let isWaitingForResponse = false;
  let isOllamaRunning = false;
  let selectedModel = '';
  let availableModels = [];
  let useMemory = true; // Utiliser la mémoire thermique par défaut
  let brainState = null; // État du cerveau pour la mémoire thermique

  // Initialisation
  initializeSocket();
  updateConversationsList();
  setupEventListeners();
  checkOllamaStatus();

  // Fonctions
  function initializeSocket() {
    // Initialiser Socket.io avec reconnexion automatique et optimisé contre les erreurs 502
    socket = io({
      reconnectionAttempts: Infinity,        // Essayer indéfiniment
      reconnectionDelay: 1000,              // Délai initial
      reconnectionDelayMax: 5000,           // Maximum de 5 secondes entre les tentatives
      timeout: 20000,                       // Augmenter le timeout
      transports: ['websocket', 'polling'],  // Essayer websocket d'abord, puis fallback sur polling
      forceNew: true,                       // Forcer une nouvelle connexion pour éviter les conflits
      autoConnect: true                     // Se connecter automatiquement
    });

    socket.on('connect', () => {
      console.log('Connected to server');
      // Vérifier le statut d'Ollama après la connexion
      checkOllamaStatus();
    });

    // Recevoir le statut d'Ollama
    socket.on('ollama status', (data) => {
      console.log('Received Ollama status:', data);
      isOllamaRunning = data.isRunning;
      updateOllamaStatus();
    });

    // Recevoir la configuration
    socket.on('config', (config) => {
      console.log('Received config:', config);
      selectedModel = config.selectedModel || '';
      availableModels = config.availableModels || [];

      // Mettre à jour l'interface utilisateur
      temperatureSlider.value = config.temperature || 0.7;
      temperatureValue.textContent = temperatureSlider.value;

      maxTokensSlider.value = config.maxTokens || 1000;
      maxTokensValue.textContent = maxTokensSlider.value;

      updateModelSelector();
    });

    // Recevoir l'état du cerveau pour la mémoire thermique
    socket.on('brain state', (state) => {
      console.log('Received brain state:', state);
      brainState = state;
    });

    // Recevoir les données de mémoire
    socket.on('memory update', (data) => {
      console.log('Received memory update');
    });

    socket.on('chat response', (data) => {
      console.log('Received chat response:', data);
      isWaitingForResponse = false;
      removeTypingIndicator();

      if (data.error) {
        displayErrorMessage(data.error);
        return;
      }

      if (data.message) {
        const botMessage = { role: 'assistant', content: data.message.content };
        messageHistory.push(botMessage);
        displayMessage(botMessage.content, 'bot');
        saveConversation();
      }
    });

    socket.on('connect_error', (error) => {
      console.error('Connection error:', error);
      displayErrorMessage('Erreur de connexion au serveur: ' + error.message);
    });

    socket.on('disconnect', (reason) => {
      console.log('Disconnected from server:', reason);
      if (reason === 'io server disconnect') {
        // Le serveur a forcé la déconnexion, essayer de se reconnecter
        socket.connect();
      }
    });

    socket.on('error', (error) => {
      console.error('Socket error:', error);
      displayErrorMessage('Erreur de socket: ' + error.message);
    });
  }

  function setupEventListeners() {
    // Formulaire de chat
    chatForm.addEventListener('submit', (e) => {
      e.preventDefault();
      sendMessage();
    });

    // Ajouter un événement pour la touche Entrée dans le champ de message
    messageInput.addEventListener('keydown', (e) => {
      // Envoyer le message avec Entrée (sans Shift)
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        sendMessage();
      }
    });

    // Ajouter un événement pour le bouton d'envoi
    const sendButton = document.getElementById('send-button');
    if (sendButton) {
      sendButton.addEventListener('click', () => {
        sendMessage();
      });
    }

    // Rafraîchir la liste des modèles
    refreshModelsBtn.addEventListener('click', () => {
      fetchAvailableModels();
    });

    // Ouvrir le modal de téléchargement
    downloadModelBtn.addEventListener('click', () => {
      downloadModelModal.show();
    });

    // Sélectionner un modèle dans le modal
    modelButtons.forEach(button => {
      button.addEventListener('click', () => {
        modelButtons.forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');
        customModelNameInput.value = button.dataset.model;
      });
    });

    // Télécharger le modèle sélectionné
    confirmDownloadBtn.addEventListener('click', () => {
      const modelName = customModelNameInput.value.trim();
      if (modelName) {
        downloadModel(modelName);
        downloadModelModal.hide();
      } else {
        alert('Veuillez sélectionner ou entrer un nom de modèle');
      }
    });

    // Sauvegarder les paramètres
    saveSettingsBtn.addEventListener('click', () => {
      saveSettings();
    });

    // Sliders pour les paramètres
    temperatureSlider.addEventListener('input', () => {
      temperatureValue.textContent = temperatureSlider.value;
    });

    maxTokensSlider.addEventListener('input', () => {
      maxTokensValue.textContent = maxTokensSlider.value;
    });

    // Option de mémoire thermique
    const useMemoryCheckbox = document.getElementById('use-memory');
    if (useMemoryCheckbox) {
      useMemoryCheckbox.addEventListener('change', () => {
        useMemory = useMemoryCheckbox.checked;
        localStorage.setItem('use_memory', useMemory);
      });

      // Charger la valeur sauvegardée
      const savedUseMemory = localStorage.getItem('use_memory');
      if (savedUseMemory !== null) {
        useMemory = savedUseMemory === 'true';
        useMemoryCheckbox.checked = useMemory;
      }
    }

    // Boutons d'action
    newChatBtn.addEventListener('click', startNewConversation);
    clearChatBtn.addEventListener('click', clearCurrentConversation);
    exportChatBtn.addEventListener('click', exportConversation);

    // Sélection de modèle
    modelSelector.addEventListener('change', () => {
      selectedModel = modelSelector.value;
      saveSettings();
    });
  }

  // Vérifier si Ollama est en cours d'exécution
  async function checkOllamaStatus() {
    try {
      const response = await fetch('/api/check-ollama');
      const data = await response.json();

      isOllamaRunning = data.isOllamaRunning;
      updateOllamaStatus();

      if (isOllamaRunning) {
        fetchAvailableModels();
      }
    } catch (error) {
      console.error('Error checking Ollama status:', error);
      isOllamaRunning = false;
      updateOllamaStatus();
    }
  }

  // Mettre à jour l'affichage du statut d'Ollama
  function updateOllamaStatus() {
    // Mettre à jour le statut dans la sidebar
    if (ollamaStatus) {
      if (isOllamaRunning) {
        ollamaStatus.className = 'alert alert-success mb-3';
        ollamaStatus.innerHTML = '<i class="bi bi-check-circle me-2"></i>Ollama est en cours d\'exécution';
      } else {
        ollamaStatus.className = 'alert alert-danger mb-3';
        ollamaStatus.innerHTML = `
          <i class="bi bi-exclamation-triangle me-2"></i>
          <div>Ollama n'est pas en cours d'exécution</div>
          <div class="mt-2">
            <button class="btn btn-sm btn-outline-light" onclick="checkOllamaStatus()">
              <i class="bi bi-arrow-clockwise me-1"></i>Réessayer
            </button>
          </div>
        `;
      }
    }

    // Mettre à jour le statut dans la barre de navigation
    const ollamaStatusNav = document.getElementById('ollama-status-nav');
    if (ollamaStatusNav) {
      if (isOllamaRunning) {
        ollamaStatusNav.innerHTML = '<div class="d-flex align-items-center"><i class="bi bi-check-circle-fill text-success me-1"></i><small>Ollama actif</small></div>';
      } else {
        ollamaStatusNav.innerHTML = '<div class="d-flex align-items-center"><i class="bi bi-exclamation-triangle-fill text-danger me-1"></i><small>Ollama inactif</small></div>';
      }
    }

    // Mettre à jour le message de bienvenue
    updateWelcomeMessage(isOllamaRunning);
  }

  // Récupérer les modèles disponibles
  async function fetchAvailableModels() {
    if (!isOllamaRunning) {
      return;
    }

    try {
      modelSelector.innerHTML = '<option value="">Chargement des modèles...</option>';

      const response = await fetch('/api/models');
      const data = await response.json();

      if (data.success) {
        availableModels = data.models || [];
        updateModelSelector();
      } else {
        modelSelector.innerHTML = '<option value="">Erreur lors du chargement des modèles</option>';
      }
    } catch (error) {
      console.error('Error fetching models:', error);
      modelSelector.innerHTML = '<option value="">Erreur lors du chargement des modèles</option>';
    }
  }

  // Mettre à jour le sélecteur de modèles
  function updateModelSelector() {
    modelSelector.innerHTML = '';

    if (availableModels.length === 0) {
      modelSelector.innerHTML = '<option value="">Aucun modèle disponible</option>';
      return;
    }

    availableModels.forEach(model => {
      const option = document.createElement('option');
      option.value = model.name;
      option.textContent = model.name;
      option.selected = model.name === selectedModel;
      modelSelector.appendChild(option);
    });

    if (!selectedModel && availableModels.length > 0) {
      selectedModel = availableModels[0].name;
    }
  }

  // Télécharger un modèle
  async function downloadModel(modelName) {
    try {
      // Afficher un message de chargement
      const loadingAlert = document.createElement('div');
      loadingAlert.className = 'alert alert-info';
      loadingAlert.innerHTML = `
        <div class="d-flex align-items-center">
          <div class="spinner-border spinner-border-sm me-2" role="status"></div>
          <div>Téléchargement du modèle ${modelName} en cours...</div>
        </div>
      `;
      chatMessages.insertAdjacentElement('afterbegin', loadingAlert);

      const response = await fetch('/api/pull-model', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ modelName })
      });

      const data = await response.json();

      // Supprimer le message de chargement
      loadingAlert.remove();

      if (data.success) {
        // Afficher un message de succès
        const successAlert = document.createElement('div');
        successAlert.className = 'alert alert-success';
        successAlert.innerHTML = `<i class="bi bi-check-circle me-2"></i>${data.message}`;
        chatMessages.insertAdjacentElement('afterbegin', successAlert);

        // Mettre à jour la liste des modèles
        fetchAvailableModels();

        // Supprimer le message après 3 secondes
        setTimeout(() => {
          successAlert.remove();
        }, 3000);
      } else {
        // Afficher un message d'erreur
        displayErrorMessage(data.error || 'Échec du téléchargement du modèle');
      }
    } catch (error) {
      console.error('Error downloading model:', error);
      displayErrorMessage('Erreur lors du téléchargement du modèle');
    }
  }

  // Sauvegarder les paramètres
  async function saveSettings() {
    try {
      const response = await fetch('/api/save-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          selectedModel,
          temperature: parseFloat(temperatureSlider.value),
          maxTokens: parseInt(maxTokensSlider.value),
          useMemory: useMemory
        })
      });

      const data = await response.json();

      if (data.success) {
        // Afficher un message de succès
        const successAlert = document.createElement('div');
        successAlert.className = 'alert alert-success';
        successAlert.innerHTML = '<i class="bi bi-check-circle me-2"></i>Paramètres sauvegardés avec succès';
        chatMessages.insertAdjacentElement('afterbegin', successAlert);

        // Supprimer le message après 3 secondes
        setTimeout(() => {
          successAlert.remove();
        }, 3000);
      } else {
        // Afficher un message d'erreur
        displayErrorMessage(data.error || 'Échec de la sauvegarde des paramètres');
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      displayErrorMessage('Erreur lors de la sauvegarde des paramètres');
    }
  }

  // Mettre à jour le message de bienvenue
  function updateWelcomeMessage(isOllamaRunning) {
    const welcomeMessage = document.querySelector('.welcome-message');
    if (welcomeMessage) {
      const alertElement = welcomeMessage.querySelector('.alert');
      if (alertElement) {
        if (isOllamaRunning) {
          alertElement.className = 'alert alert-success';
          alertElement.innerHTML = '<i class="bi bi-check-circle me-2"></i>Ollama est en cours d\'exécution. Vous pouvez commencer à discuter!';
        } else {
          alertElement.className = 'alert alert-danger';
          alertElement.innerHTML = '<i class="bi bi-exclamation-triangle me-2"></i>Ollama n\'est pas en cours d\'exécution. Veuillez démarrer Ollama pour utiliser cette interface.';
        }
      }
    }
  }

  function sendMessage() {
    const message = messageInput.value.trim();
    if (!message || isWaitingForResponse) {
      console.log('Message vide ou en attente de réponse');
      return;
    }

    // Vérifier si Ollama est en cours d'exécution
    if (!isOllamaRunning) {
      displayErrorMessage('Ollama n\'est pas en cours d\'exécution. Veuillez démarrer Ollama pour utiliser cette interface.');
      return;
    }

    // Vérifier si un modèle est sélectionné
    if (!selectedModel) {
      displayErrorMessage('Aucun modèle sélectionné. Veuillez sélectionner un modèle.');
      return;
    }

    console.log('Envoi du message:', message);

    // Afficher le message de l'utilisateur
    displayMessage(message, 'user');

    // Ajouter le message à l'historique
    const userMessage = { role: 'user', content: message };
    messageHistory.push(userMessage);

    // Effacer l'input et afficher l'indicateur de frappe
    messageInput.value = '';
    messageInput.style.height = 'auto'; // Réinitialiser la hauteur si c'est un textarea auto-resize
    displayTypingIndicator();
    isWaitingForResponse = true;

    try {
      // Envoyer le message au serveur
      console.log('Émission du message via socket.io');
      socket.emit('chat message', {
        message,
        history: messageHistory,
        modelName: selectedModel,
        temperature: parseFloat(temperatureSlider.value),
        maxTokens: parseInt(maxTokensSlider.value),
        useMemory: useMemory // Ajouter l'option de mémoire thermique
      });

      console.log('Message émis avec succès');
    } catch (error) {
      console.error('Erreur lors de l\'envoi du message:', error);
      displayErrorMessage('Erreur lors de l\'envoi du message: ' + error.message);
      isWaitingForResponse = false;
      removeTypingIndicator();
    }

    // Sauvegarder la conversation
    saveConversation();

    // Faire défiler vers le bas
    chatMessages.scrollTop = chatMessages.scrollHeight;
  }

  function displayMessage(content, sender) {
    // Supprimer le message de bienvenue s'il est présent
    const welcomeMessage = document.querySelector('.welcome-message');
    if (welcomeMessage) {
      welcomeMessage.remove();
    }

    const messageDiv = document.createElement('div');
    messageDiv.className = `message message-${sender} mb-3`;

    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';

    if (sender === 'bot') {
      // Utiliser marked.js pour convertir le markdown en HTML
      contentDiv.innerHTML = marked.parse(content);
      contentDiv.classList.add('markdown-content');

      // Appliquer highlight.js aux blocs de code
      contentDiv.querySelectorAll('pre code').forEach((block) => {
        hljs.highlightElement(block);
      });
    } else {
      contentDiv.textContent = content;
    }

    const timeDiv = document.createElement('div');
    timeDiv.className = 'message-time';
    timeDiv.textContent = new Date().toLocaleTimeString();

    messageDiv.appendChild(contentDiv);
    messageDiv.appendChild(timeDiv);
    chatMessages.appendChild(messageDiv);

    // Faire défiler vers le bas
    chatMessages.scrollTop = chatMessages.scrollHeight;
  }

  function displayErrorMessage(error) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'alert alert-danger mt-3';
    errorDiv.innerHTML = `<i class="bi bi-exclamation-triangle me-2"></i>${error}`;
    chatMessages.appendChild(errorDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
  }

  function displayTypingIndicator() {
    const indicatorDiv = document.createElement('div');
    indicatorDiv.className = 'typing-indicator';
    indicatorDiv.innerHTML = '<span></span><span></span><span></span>';
    chatMessages.appendChild(indicatorDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
  }

  function removeTypingIndicator() {
    const indicator = document.querySelector('.typing-indicator');
    if (indicator) {
      indicator.remove();
    }
  }

  function startNewConversation() {
    // Sauvegarder la conversation actuelle si elle contient des messages
    if (messageHistory.length > 0) {
      saveConversation();
    }

    // Réinitialiser
    currentConversationId = generateId();
    messageHistory = [];
    chatMessages.innerHTML = `
      <div class="welcome-message text-center my-5">
        <i class="bi bi-robot display-1 mb-3"></i>
        <h2>Nouvelle conversation</h2>
        <p class="lead">Commencez à discuter avec l'agent DeepSeek r1 en utilisant Ollama en local.</p>
        <div class="alert ${isOllamaRunning ? 'alert-success' : 'alert-danger'}">
          <i class="bi bi-${isOllamaRunning ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
          ${isOllamaRunning ? 'Ollama est en cours d\'exécution. Vous pouvez commencer à discuter!' : 'Ollama n\'est pas en cours d\'exécution. Veuillez démarrer Ollama pour utiliser cette interface.'}
        </div>
      </div>
    `;
    currentChatTitle.textContent = 'Nouvelle conversation';

    // Mettre à jour la liste des conversations
    updateConversationsList();
  }

  function clearCurrentConversation() {
    if (confirm('Êtes-vous sûr de vouloir effacer cette conversation ?')) {
      startNewConversation();
    }
  }

  function exportConversation() {
    if (messageHistory.length === 0) {
      alert('Aucun message à exporter');
      return;
    }

    // Formater la conversation
    let conversationText = '# Conversation DeepSeek r1\n\n';
    messageHistory.forEach(msg => {
      const role = msg.role === 'user' ? 'Vous' : 'DeepSeek r1';
      conversationText += `## ${role}\n\n${msg.content}\n\n`;
    });

    // Créer un blob et un lien de téléchargement
    const blob = new Blob([conversationText], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `deepseek-conversation-${new Date().toISOString().slice(0, 10)}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  function saveConversation() {
    if (messageHistory.length === 0) return;

    // Générer un titre pour la conversation
    let title = 'Nouvelle conversation';
    if (messageHistory.length > 0 && messageHistory[0].content) {
      title = messageHistory[0].content.substring(0, 30) + (messageHistory[0].content.length > 30 ? '...' : '');
    }

    // Mettre à jour la conversation actuelle
    conversations[currentConversationId] = {
      id: currentConversationId,
      title,
      messages: messageHistory,
      lastUpdated: new Date().toISOString()
    };

    // Sauvegarder dans le localStorage
    localStorage.setItem('deepseek_conversations', JSON.stringify(conversations));

    // Mettre à jour la liste des conversations
    updateConversationsList();

    // Mettre à jour le titre de la conversation actuelle
    currentChatTitle.textContent = title;
  }

  function loadConversation(id) {
    if (!conversations[id]) return;

    currentConversationId = id;
    messageHistory = conversations[id].messages;
    currentChatTitle.textContent = conversations[id].title;

    // Afficher les messages
    chatMessages.innerHTML = '';
    messageHistory.forEach(msg => {
      displayMessage(msg.content, msg.role === 'user' ? 'user' : 'bot');
    });

    // Mettre à jour la liste des conversations
    updateConversationsList();
  }

  function updateConversationsList() {
    conversationsList.innerHTML = '';

    // Trier les conversations par date de mise à jour (la plus récente en premier)
    const sortedConversations = Object.values(conversations).sort((a, b) => {
      return new Date(b.lastUpdated) - new Date(a.lastUpdated);
    });

    sortedConversations.forEach(conv => {
      const li = document.createElement('li');
      li.className = `list-group-item ${conv.id === currentConversationId ? 'active' : ''}`;
      li.textContent = conv.title;
      li.addEventListener('click', () => loadConversation(conv.id));
      conversationsList.appendChild(li);
    });
  }

  function loadConversations() {
    const saved = localStorage.getItem('deepseek_conversations');
    return saved ? JSON.parse(saved) : {};
  }

  function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }

  // Exposer certaines fonctions globalement pour les appels depuis le HTML
  window.checkOllamaStatus = checkOllamaStatus;
});
