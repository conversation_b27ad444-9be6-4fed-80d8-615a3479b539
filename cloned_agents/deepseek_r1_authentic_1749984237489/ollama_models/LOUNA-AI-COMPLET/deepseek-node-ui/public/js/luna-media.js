/**
 * JavaScript pour la page de génération multimédia de Luna
 * Gère la génération d'images, de vidéos, de musique et de code
 * 
 * Cré<PERSON> par <PERSON>, Sainte-Anne, Guadeloupe (97180)
 */

// État global
const mediaState = {
  activeJobs: new Map(),
  currentTab: 'image',
  galleryData: {
    image: [],
    video: [],
    music: [],
    code: []
  }
};

// Initialisation au chargement de la page
$(document).ready(function() {
  console.log('Initialisation de l\'interface de génération multimédia');
  
  // Charger les galeries
  loadGalleries();
  
  // Configurer les événements des onglets
  setupTabEvents();
  
  // Configurer les formulaires
  setupForms();
  
  // Configurer les événements socket
  setupSocketEvents();
  
  // Mettre à jour la liste des tâches actives
  updateActiveJobsList();
});

// Charger les galeries d'éléments générés
function loadGalleries() {
  // Charger toutes les galeries
  $.ajax({
    url: '/luna/api/media/files',
    method: 'GET',
    success: function(response) {
      if (response.success) {
        mediaState.galleryData = response.files;
        
        // Mettre à jour chaque galerie
        updateGallery('image');
        updateGallery('video');
        updateGallery('music');
        updateGallery('code');
      } else {
        console.error('Erreur lors du chargement des galeries:', response.error);
      }
    },
    error: function(xhr, status, error) {
      console.error('Erreur lors du chargement des galeries:', error);
    }
  });
}

// Mettre à jour une galerie spécifique
function updateGallery(type) {
  const files = mediaState.galleryData[type] || [];
  const galleryId = `#${type}GalleryContent`;
  
  if (files.length === 0) {
    $(galleryId).html('<div class="col-12 text-center"><p class="text-muted">Aucun élément généré</p></div>');
    return;
  }
  
  let html = '';
  
  files.forEach(file => {
    if (type === 'image') {
      html += `
        <div class="col-md-4 mb-3">
          <div class="card">
            <img src="${file.path}" class="card-img-top" alt="Image générée">
            <div class="card-body">
              <h6 class="card-title">${file.name}</h6>
              <p class="card-text text-muted small">${new Date(file.created).toLocaleString()}</p>
            </div>
          </div>
        </div>
      `;
    } else if (type === 'video') {
      html += `
        <div class="col-md-6 mb-3">
          <div class="card">
            <div class="card-body">
              <video controls class="w-100">
                <source src="${file.path}" type="video/mp4">
                Votre navigateur ne supporte pas la lecture de vidéos.
              </video>
              <h6 class="card-title mt-2">${file.name}</h6>
              <p class="card-text text-muted small">${new Date(file.created).toLocaleString()}</p>
            </div>
          </div>
        </div>
      `;
    } else if (type === 'music') {
      html += `
        <div class="col-md-6 mb-3">
          <div class="card">
            <div class="card-body">
              <h6 class="card-title">${file.name}</h6>
              <audio controls class="w-100">
                <source src="${file.path}" type="audio/mpeg">
                Votre navigateur ne supporte pas la lecture audio.
              </audio>
              <p class="card-text text-muted small">${new Date(file.created).toLocaleString()}</p>
            </div>
          </div>
        </div>
      `;
    } else if (type === 'code') {
      html += `
        <div class="col-md-6 mb-3">
          <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
              <span>${file.name}</span>
              <a href="${file.path}" class="btn btn-sm btn-luna-outline" target="_blank">
                <i class="bi bi-box-arrow-up-right"></i> Ouvrir
              </a>
            </div>
            <div class="card-body">
              <p class="card-text text-muted small">${new Date(file.created).toLocaleString()}</p>
            </div>
          </div>
        </div>
      `;
    }
  });
  
  $(galleryId).html(html);
}

// Configurer les événements des onglets
function setupTabEvents() {
  $('#mediaTypeTabs button').on('click', function(e) {
    const tabId = $(this).attr('id');
    mediaState.currentTab = tabId.replace('-tab', '');
  });
}

// Configurer les formulaires
function setupForms() {
  // Formulaire de génération d'image
  $('#imageGenerationForm').on('submit', function(e) {
    e.preventDefault();
    
    const prompt = $('#imagePrompt').val().trim();
    if (!prompt) {
      showNotification('Veuillez entrer une description pour l\'image', 'warning');
      return;
    }
    
    const options = {
      width: parseInt($('#imageWidth').val()),
      height: parseInt($('#imageHeight').val())
    };
    
    // Afficher la barre de progression
    $('#imageGenerationProgress').removeClass('d-none');
    $('#imageGenerationProgress .progress-bar').css('width', '0%').text('0%');
    
    // Envoyer la requête via socket
    socket.emit('generate image', { prompt, options });
  });
  
  // Formulaire de génération de vidéo
  $('#videoGenerationForm').on('submit', function(e) {
    e.preventDefault();
    
    const prompt = $('#videoPrompt').val().trim();
    if (!prompt) {
      showNotification('Veuillez entrer une description pour la vidéo', 'warning');
      return;
    }
    
    const options = {
      duration: parseInt($('#videoDuration').val()),
      resolution: $('#videoResolution').val()
    };
    
    // Afficher la barre de progression
    $('#videoGenerationProgress').removeClass('d-none');
    $('#videoGenerationProgress .progress-bar').css('width', '0%').text('0%');
    
    // Envoyer la requête via socket
    socket.emit('generate video', { prompt, options });
  });
  
  // Formulaire de génération de musique
  $('#musicGenerationForm').on('submit', function(e) {
    e.preventDefault();
    
    const prompt = $('#musicPrompt').val().trim();
    if (!prompt) {
      showNotification('Veuillez entrer une description pour la musique', 'warning');
      return;
    }
    
    const options = {
      duration: parseInt($('#musicDuration').val()),
      genre: $('#musicGenre').val()
    };
    
    // Afficher la barre de progression
    $('#musicGenerationProgress').removeClass('d-none');
    $('#musicGenerationProgress .progress-bar').css('width', '0%').text('0%');
    
    // Envoyer la requête via API
    $.ajax({
      url: '/luna/api/media/music',
      method: 'POST',
      data: JSON.stringify({ prompt, options }),
      contentType: 'application/json',
      success: function(response) {
        if (response.success) {
          handleMusicGenerationResult(response.result);
        } else {
          showNotification('Erreur lors de la génération de musique: ' + response.error, 'error');
          $('#musicGenerationProgress').addClass('d-none');
        }
      },
      error: function(xhr, status, error) {
        showNotification('Erreur lors de la génération de musique: ' + error, 'error');
        $('#musicGenerationProgress').addClass('d-none');
      }
    });
  });
  
  // Formulaire de génération de code
  $('#codeGenerationForm').on('submit', function(e) {
    e.preventDefault();
    
    const prompt = $('#codePrompt').val().trim();
    if (!prompt) {
      showNotification('Veuillez entrer une description pour le code', 'warning');
      return;
    }
    
    const options = {
      language: $('#codeLanguage').val(),
      complexity: $('#codeComplexity').val()
    };
    
    // Afficher la barre de progression
    $('#codeGenerationProgress').removeClass('d-none');
    $('#codeGenerationProgress .progress-bar').css('width', '0%').text('0%');
    
    // Envoyer la requête via API
    $.ajax({
      url: '/luna/api/media/code',
      method: 'POST',
      data: JSON.stringify({ prompt, options }),
      contentType: 'application/json',
      success: function(response) {
        if (response.success) {
          handleCodeGenerationResult(response.result);
        } else {
          showNotification('Erreur lors de la génération de code: ' + response.error, 'error');
          $('#codeGenerationProgress').addClass('d-none');
        }
      },
      error: function(xhr, status, error) {
        showNotification('Erreur lors de la génération de code: ' + error, 'error');
        $('#codeGenerationProgress').addClass('d-none');
      }
    });
  });
  
  // Bouton de copie de code
  $('#copyCodeBtn').on('click', function() {
    const code = $('#generatedCode').text();
    navigator.clipboard.writeText(code).then(function() {
      showNotification('Code copié dans le presse-papiers', 'success');
    }, function(err) {
      showNotification('Erreur lors de la copie du code: ' + err, 'error');
    });
  });
}

// Configurer les événements socket
function setupSocketEvents() {
  // Événements de création de tâche
  socket.on('media:job:created', function(job) {
    mediaState.activeJobs.set(job.id, job);
    updateActiveJobsList();
  });
  
  // Événements de progression de tâche
  socket.on('media:job:progress', function(update) {
    if (mediaState.activeJobs.has(update.id)) {
      const job = mediaState.activeJobs.get(update.id);
      job.progress = update.progress;
      job.message = update.message;
      
      updateActiveJobsList();
      updateProgressBar(update.id, update.type, update.progress, update.message);
    }
  });
  
  // Événements de complétion de tâche
  socket.on('media:job:completed', function(job) {
    mediaState.activeJobs.delete(job.id);
    updateActiveJobsList();
    
    // Recharger la galerie correspondante
    loadGalleries();
  });
  
  // Événements d'échec de tâche
  socket.on('media:job:failed', function(job) {
    mediaState.activeJobs.delete(job.id);
    updateActiveJobsList();
    
    showNotification(`Erreur lors de la génération: ${job.error}`, 'error');
    
    // Masquer la barre de progression correspondante
    hideProgressBar(job.type);
  });
  
  // Résultats de génération d'image
  socket.on('generate image result', function(data) {
    if (data.success) {
      handleImageGenerationResult(data.result);
    } else {
      showNotification('Erreur lors de la génération d\'image: ' + data.error, 'error');
      $('#imageGenerationProgress').addClass('d-none');
    }
  });
  
  // Résultats de génération de vidéo
  socket.on('generate video result', function(data) {
    if (data.success) {
      handleVideoGenerationResult(data.result);
    } else {
      showNotification('Erreur lors de la génération de vidéo: ' + data.error, 'error');
      $('#videoGenerationProgress').addClass('d-none');
    }
  });
}

// Mettre à jour la liste des tâches actives
function updateActiveJobsList() {
  const jobsList = $('#activeJobsList');
  
  if (mediaState.activeJobs.size === 0) {
    jobsList.html('<p class="text-center text-muted">Aucune tâche en cours</p>');
    return;
  }
  
  let html = '<div class="list-group">';
  
  mediaState.activeJobs.forEach(job => {
    const progressPercent = Math.round(job.progress * 100);
    const jobType = job.type.charAt(0).toUpperCase() + job.type.slice(1);
    const elapsedTime = Math.round((Date.now() - job.startTime) / 1000);
    
    html += `
      <div class="list-group-item">
        <div class="d-flex w-100 justify-content-between">
          <h6 class="mb-1">${jobType}: ${job.prompt.substring(0, 50)}${job.prompt.length > 50 ? '...' : ''}</h6>
          <small>${elapsedTime}s</small>
        </div>
        <p class="mb-1">${job.message}</p>
        <div class="progress">
          <div class="progress-bar" role="progressbar" style="width: ${progressPercent}%" aria-valuenow="${progressPercent}" aria-valuemin="0" aria-valuemax="100">${progressPercent}%</div>
        </div>
      </div>
    `;
  });
  
  html += '</div>';
  jobsList.html(html);
}

// Mettre à jour une barre de progression
function updateProgressBar(jobId, type, progress, message) {
  const progressBar = $(`#${type}GenerationProgress`);
  const progressBarInner = progressBar.find('.progress-bar');
  
  const percent = Math.round(progress * 100);
  progressBarInner.css('width', `${percent}%`);
  progressBarInner.text(`${percent}% - ${message}`);
}

// Masquer une barre de progression
function hideProgressBar(type) {
  $(`#${type}GenerationProgress`).addClass('d-none');
}

// Gérer le résultat de génération d'image
function handleImageGenerationResult(result) {
  // Masquer la barre de progression
  $('#imageGenerationProgress').addClass('d-none');
  
  // Afficher l'image générée
  $('#generatedImage').attr('src', result.path);
  $('#imagePromptDisplay').text(result.prompt);
  $('#imageResult').removeClass('d-none');
  
  // Recharger la galerie
  loadGalleries();
  
  showNotification('Image générée avec succès', 'success');
}

// Gérer le résultat de génération de vidéo
function handleVideoGenerationResult(result) {
  // Masquer la barre de progression
  $('#videoGenerationProgress').addClass('d-none');
  
  // Afficher la vidéo générée
  $('#generatedVideo source').attr('src', result.path);
  $('#generatedVideo')[0].load(); // Recharger la vidéo
  $('#videoPromptDisplay').text(result.prompt);
  $('#videoResult').removeClass('d-none');
  
  // Recharger la galerie
  loadGalleries();
  
  showNotification('Vidéo générée avec succès', 'success');
}

// Gérer le résultat de génération de musique
function handleMusicGenerationResult(result) {
  // Masquer la barre de progression
  $('#musicGenerationProgress').addClass('d-none');
  
  // Afficher la musique générée
  $('#generatedMusic source').attr('src', result.path);
  $('#generatedMusic')[0].load(); // Recharger l'audio
  $('#musicPromptDisplay').text(result.prompt);
  $('#musicResult').removeClass('d-none');
  
  // Recharger la galerie
  loadGalleries();
  
  showNotification('Musique générée avec succès', 'success');
}

// Gérer le résultat de génération de code
function handleCodeGenerationResult(result) {
  // Masquer la barre de progression
  $('#codeGenerationProgress').addClass('d-none');
  
  // Afficher le code généré
  $('#generatedCode').text(result.code);
  $('#codePromptDisplay').text(result.prompt);
  $('#codeLanguageDisplay').text($('#codeLanguage option:selected').text());
  $('#codeResult').removeClass('d-none');
  
  // Appliquer la coloration syntaxique si Prism est disponible
  if (typeof Prism !== 'undefined') {
    Prism.highlightElement(document.getElementById('generatedCode'));
  }
  
  // Recharger la galerie
  loadGalleries();
  
  showNotification('Code généré avec succès', 'success');
}
