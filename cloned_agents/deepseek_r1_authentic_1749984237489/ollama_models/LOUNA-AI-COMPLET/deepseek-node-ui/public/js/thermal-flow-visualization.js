/**
 * Visualisation du flux thermique pour Luna
 * Ce script crée une visualisation fluide du flux thermique entre les zones de mémoire
 * avec des transitions douces comme l'eau
 */

class ThermalFlowVisualization {
  constructor(canvasId, options = {}) {
    this.canvas = document.getElementById(canvasId);
    if (!this.canvas) {
      console.error(`Canvas with id ${canvasId} not found`);
      return;
    }
    
    this.ctx = this.canvas.getContext('2d');
    this.width = this.canvas.width;
    this.height = this.canvas.height;
    
    // Options par défaut
    this.options = {
      zoneCount: 6,
      flowSpeed: 2,
      particleCount: 50,
      particleSize: 3,
      waveHeight: 20,
      waveSpeed: 0.05,
      colors: [
        '#ff5252', // Zone 1 - Rouge (Récente)
        '#ff9800', // Zone 2 - Orange (Chaude)
        '#29b6f6', // Zone 3 - Bleu clair (Tiède)
        '#5c6bc0', // Zone 4 - Bleu (Fraîche)
        '#78909c', // Zone 5 - Gris (Froide)
        '#424242'  // Zone 6 - <PERSON><PERSON> foncé (Archive)
      ],
      backgroundColor: 'rgba(26, 26, 46, 0.3)',
      ...options
    };
    
    // État du flux thermique
    this.state = {
      zones: Array(this.options.zoneCount).fill().map((_, i) => ({
        temperature: 100 - (i * (100 / (this.options.zoneCount - 1))),
        activity: i === 0 ? 100 : 0,
        particles: []
      })),
      particles: [],
      waveOffset: 0,
      hotPoints: 0,
      coldPoints: 0,
      transfers: 0
    };
    
    // Initialiser les particules
    this.initParticles();
    
    // Démarrer l'animation
    this.animate();
    
    // Redimensionner le canvas lors du redimensionnement de la fenêtre
    window.addEventListener('resize', this.resize.bind(this));
    this.resize();
  }
  
  /**
   * Initialise les particules pour l'animation du flux
   */
  initParticles() {
    this.state.particles = [];
    
    for (let i = 0; i < this.options.particleCount; i++) {
      this.state.particles.push({
        x: Math.random() * this.width,
        y: Math.random() * this.height,
        size: Math.random() * this.options.particleSize + 1,
        speedX: (Math.random() - 0.5) * this.options.flowSpeed,
        speedY: (Math.random() - 0.5) * this.options.flowSpeed,
        zoneIndex: Math.floor(Math.random() * this.options.zoneCount),
        opacity: Math.random() * 0.5 + 0.3
      });
    }
  }
  
  /**
   * Redimensionne le canvas pour s'adapter à la taille du conteneur
   */
  resize() {
    const container = this.canvas.parentElement;
    if (container) {
      this.canvas.width = container.clientWidth;
      this.canvas.height = 200; // Hauteur fixe
      this.width = this.canvas.width;
      this.height = this.canvas.height;
    }
  }
  
  /**
   * Met à jour l'état du flux thermique
   * @param {Object} data - Nouvelles données pour le flux thermique
   */
  updateState(data) {
    if (data.zones) {
      data.zones.forEach((zone, index) => {
        if (index < this.state.zones.length) {
          this.state.zones[index].activity = zone.activity || 0;
          
          // Ajouter des particules en fonction de l'activité
          if (zone.activity > 20) {
            this.addParticlesForZone(index, Math.floor(zone.activity / 10));
          }
        }
      });
    }
    
    if (data.hotPoints !== undefined) {
      this.state.hotPoints = data.hotPoints;
    }
    
    if (data.coldPoints !== undefined) {
      this.state.coldPoints = data.coldPoints;
    }
    
    if (data.transfers !== undefined) {
      this.state.transfers = data.transfers;
    }
    
    // Mettre à jour les éléments DOM
    this.updateDOMElements();
  }
  
  /**
   * Met à jour les éléments DOM avec les statistiques
   */
  updateDOMElements() {
    const hotPointsElement = document.getElementById('hot-points');
    const coldPointsElement = document.getElementById('cold-points');
    const transfersElement = document.getElementById('thermal-transfers');
    
    if (hotPointsElement) {
      hotPointsElement.textContent = this.state.hotPoints;
    }
    
    if (coldPointsElement) {
      coldPointsElement.textContent = this.state.coldPoints;
    }
    
    if (transfersElement) {
      transfersElement.textContent = this.state.transfers;
    }
  }
  
  /**
   * Ajoute des particules pour une zone spécifique
   * @param {number} zoneIndex - Index de la zone
   * @param {number} count - Nombre de particules à ajouter
   */
  addParticlesForZone(zoneIndex, count) {
    const zoneWidth = this.width / this.options.zoneCount;
    const zoneX = zoneIndex * zoneWidth;
    
    for (let i = 0; i < count; i++) {
      this.state.particles.push({
        x: zoneX + Math.random() * zoneWidth,
        y: Math.random() * this.height,
        size: Math.random() * this.options.particleSize + 1,
        speedX: (Math.random() - 0.5) * this.options.flowSpeed * 2,
        speedY: (Math.random() - 0.5) * this.options.flowSpeed * 2,
        zoneIndex: zoneIndex,
        opacity: Math.random() * 0.5 + 0.5
      });
    }
    
    // Limiter le nombre total de particules
    if (this.state.particles.length > this.options.particleCount * 2) {
      this.state.particles = this.state.particles.slice(-this.options.particleCount * 2);
    }
  }
  
  /**
   * Anime le flux thermique
   */
  animate() {
    this.update();
    this.draw();
    requestAnimationFrame(this.animate.bind(this));
  }
  
  /**
   * Met à jour l'état de l'animation
   */
  update() {
    // Mettre à jour le décalage des vagues
    this.state.waveOffset += this.options.waveSpeed;
    
    // Mettre à jour les particules
    this.state.particles.forEach(particle => {
      particle.x += particle.speedX;
      particle.y += particle.speedY;
      
      // Rebondir sur les bords
      if (particle.x < 0 || particle.x > this.width) {
        particle.speedX *= -1;
      }
      
      if (particle.y < 0 || particle.y > this.height) {
        particle.speedY *= -1;
      }
      
      // Déterminer la zone actuelle de la particule
      const zoneWidth = this.width / this.options.zoneCount;
      const currentZoneIndex = Math.min(
        this.options.zoneCount - 1,
        Math.max(0, Math.floor(particle.x / zoneWidth))
      );
      
      // Changer la zone si nécessaire
      if (currentZoneIndex !== particle.zoneIndex) {
        // Simuler un transfert entre zones
        if (Math.random() < 0.1) {
          this.state.transfers++;
          this.updateDOMElements();
        }
        
        particle.zoneIndex = currentZoneIndex;
      }
    });
  }
  
  /**
   * Dessine le flux thermique
   */
  draw() {
    // Effacer le canvas
    this.ctx.fillStyle = this.options.backgroundColor;
    this.ctx.fillRect(0, 0, this.width, this.height);
    
    // Dessiner les zones
    this.drawZones();
    
    // Dessiner les vagues
    this.drawWaves();
    
    // Dessiner les particules
    this.drawParticles();
  }
  
  /**
   * Dessine les zones de température
   */
  drawZones() {
    const zoneWidth = this.width / this.options.zoneCount;
    
    this.state.zones.forEach((zone, index) => {
      const x = index * zoneWidth;
      const color = this.options.colors[index];
      
      // Dessiner le fond de la zone
      this.ctx.fillStyle = this.hexToRgba(color, 0.1);
      this.ctx.fillRect(x, 0, zoneWidth, this.height);
      
      // Dessiner le nom de la zone
      this.ctx.fillStyle = this.hexToRgba(color, 0.8);
      this.ctx.font = '12px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(`Zone ${index + 1}`, x + zoneWidth / 2, 20);
      
      // Dessiner la température
      this.ctx.fillStyle = '#ffffff';
      this.ctx.font = '10px Arial';
      this.ctx.fillText(`${zone.temperature.toFixed(0)}°C`, x + zoneWidth / 2, 40);
    });
  }
  
  /**
   * Dessine les vagues entre les zones
   */
  drawWaves() {
    const zoneWidth = this.width / this.options.zoneCount;
    
    this.ctx.beginPath();
    this.ctx.moveTo(0, this.height);
    
    for (let i = 0; i <= this.options.zoneCount; i++) {
      const x = i * zoneWidth;
      
      // Dessiner une vague à la frontière de chaque zone
      if (i > 0 && i < this.options.zoneCount) {
        const waveHeight = this.options.waveHeight * (1 + Math.sin(this.state.waveOffset + i) * 0.5);
        
        // Dessiner une courbe de Bézier pour la vague
        this.ctx.bezierCurveTo(
          x - zoneWidth / 4, this.height - waveHeight,
          x - zoneWidth / 8, this.height - waveHeight * 1.5,
          x, this.height - waveHeight
        );
        
        this.ctx.bezierCurveTo(
          x + zoneWidth / 8, this.height - waveHeight * 0.5,
          x + zoneWidth / 4, this.height,
          x + zoneWidth / 2, this.height
        );
      } else {
        this.ctx.lineTo(x, this.height);
      }
    }
    
    // Remplir avec un dégradé
    const gradient = this.ctx.createLinearGradient(0, 0, this.width, 0);
    this.options.colors.forEach((color, index) => {
      gradient.addColorStop(index / (this.options.zoneCount - 1), this.hexToRgba(color, 0.3));
    });
    
    this.ctx.fillStyle = gradient;
    this.ctx.fill();
  }
  
  /**
   * Dessine les particules
   */
  drawParticles() {
    this.state.particles.forEach(particle => {
      const color = this.options.colors[particle.zoneIndex];
      
      this.ctx.beginPath();
      this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
      this.ctx.fillStyle = this.hexToRgba(color, particle.opacity);
      this.ctx.fill();
    });
  }
  
  /**
   * Convertit une couleur hexadécimale en rgba
   * @param {string} hex - Couleur hexadécimale
   * @param {number} alpha - Valeur alpha (0-1)
   * @returns {string} - Couleur rgba
   */
  hexToRgba(hex, alpha) {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  }
  
  /**
   * Simule un transfert thermique entre deux zones
   * @param {number} fromZone - Index de la zone source
   * @param {number} toZone - Index de la zone destination
   */
  simulateTransfer(fromZone, toZone) {
    const zoneWidth = this.width / this.options.zoneCount;
    const fromX = (fromZone + 0.5) * zoneWidth;
    const toX = (toZone + 0.5) * zoneWidth;
    
    // Ajouter des particules pour visualiser le transfert
    for (let i = 0; i < 10; i++) {
      const progress = i / 10;
      const x = fromX + (toX - fromX) * progress;
      const y = this.height / 2 + Math.sin(progress * Math.PI) * 50;
      
      this.state.particles.push({
        x: x,
        y: y,
        size: Math.random() * this.options.particleSize * 2 + 2,
        speedX: (toX - fromX) / 50,
        speedY: (Math.random() - 0.5) * 2,
        zoneIndex: fromZone,
        opacity: 0.8
      });
    }
    
    // Incrémenter le compteur de transferts
    this.state.transfers++;
    this.updateDOMElements();
  }
}

// Initialiser la visualisation lorsque le document est chargé
document.addEventListener('DOMContentLoaded', () => {
  // Vérifier si le canvas existe
  const canvas = document.getElementById('thermal-flow-canvas');
  if (canvas) {
    // Créer l'instance de visualisation
    window.thermalFlowVisualization = new ThermalFlowVisualization('thermal-flow-canvas');
    
    // Simuler des mises à jour périodiques
    setInterval(() => {
      const randomZoneActivity = Array(6).fill().map((_, i) => ({
        activity: i === 0 ? 100 : Math.random() * 20
      }));
      
      window.thermalFlowVisualization.updateState({
        zones: randomZoneActivity,
        hotPoints: Math.floor(Math.random() * 5) + 1,
        coldPoints: Math.floor(Math.random() * 3) + 1,
        transfers: window.thermalFlowVisualization.state.transfers + Math.floor(Math.random() * 2)
      });
      
      // Simuler un transfert aléatoire
      if (Math.random() < 0.3) {
        const fromZone = Math.floor(Math.random() * 3);
        const toZone = Math.floor(Math.random() * 3) + 3;
        window.thermalFlowVisualization.simulateTransfer(fromZone, toZone);
      }
    }, 3000);
  }
});
