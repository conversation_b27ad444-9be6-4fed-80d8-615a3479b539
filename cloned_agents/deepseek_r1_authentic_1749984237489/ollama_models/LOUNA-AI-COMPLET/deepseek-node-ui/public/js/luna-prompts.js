/**
 * Luna Prompts - Gestion des prompts pour l'interface Luna
 * Permet <PERSON>, é<PERSON>er, organiser et utiliser des prompts prédéfinis
 */

// Initialiser la connexion Socket.IO
const socket = io();

// État global des prompts
const promptsState = {
  prompts: [],
  categories: ['creative', 'technical', 'business', 'personal'],
  customCategories: [],
  activeCategory: 'all',
  lastModified: null
};

// Événements Socket.IO
socket.on('connect', () => {
  console.log('🔌 Connecté au serveur Luna');
  loadPrompts();
});

socket.on('disconnect', () => {
  console.log('🔌 Déconnecté du serveur Luna');
  showNotification('Connexion au serveur perdue. Tentative de reconnexion...', 'error');
});

// Charger les prompts depuis le serveur
socket.on('prompts loaded', (data) => {
  if (data.success) {
    promptsState.prompts = data.prompts || [];
    promptsState.customCategories = data.customCategories || [];
    promptsState.lastModified = data.lastModified;
    
    // Mettre à jour l'interface
    updatePromptsUI();
    updateCategoriesUI();
    updateStats();
    
    console.log(`${promptsState.prompts.length} prompts chargés`);
  } else {
    console.error('Erreur lors du chargement des prompts:', data.error);
    showNotification('Erreur lors du chargement des prompts', 'error');
  }
});

// Confirmation de sauvegarde
socket.on('prompt saved', (data) => {
  if (data.success) {
    // Fermer la modal
    $('#prompt-modal').modal('hide');
    
    // Recharger les prompts
    loadPrompts();
    
    showNotification('Prompt enregistré avec succès', 'success');
  } else {
    console.error('Erreur lors de la sauvegarde du prompt:', data.error);
    showNotification('Erreur lors de la sauvegarde', 'error');
  }
});

// Confirmation de suppression
socket.on('prompt deleted', (data) => {
  if (data.success) {
    // Recharger les prompts
    loadPrompts();
    
    showNotification('Prompt supprimé avec succès', 'success');
  } else {
    console.error('Erreur lors de la suppression du prompt:', data.error);
    showNotification('Erreur lors de la suppression', 'error');
  }
});

// Fonctions principales

// Charger les prompts depuis le serveur
function loadPrompts() {
  socket.emit('load prompts');
}

// Mettre à jour l'interface des prompts
function updatePromptsUI() {
  const container = $('#prompts-container');
  const noPromptsMessage = $('#no-prompts-message');
  
  // Vider le conteneur
  container.find('.prompt-card').remove();
  
  // Filtrer les prompts selon la catégorie active
  let filteredPrompts = promptsState.prompts;
  if (promptsState.activeCategory !== 'all') {
    filteredPrompts = promptsState.prompts.filter(p => p.category === promptsState.activeCategory);
  }
  
  // Afficher ou masquer le message "aucun prompt"
  if (filteredPrompts.length === 0) {
    noPromptsMessage.show();
  } else {
    noPromptsMessage.hide();
    
    // Ajouter chaque prompt au conteneur
    filteredPrompts.forEach(prompt => {
      const promptCard = createPromptCard(prompt);
      container.append(promptCard);
    });
  }
}

// Créer une carte pour un prompt
function createPromptCard(prompt) {
  const card = $(`
    <div class="prompt-card mb-3" data-id="${prompt.id}">
      <div class="card bg-dark">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">
            ${prompt.favorite ? '<i class="bi bi-star-fill text-warning me-2"></i>' : ''}
            ${prompt.title}
          </h5>
          <div class="btn-group">
            <button class="btn btn-sm btn-outline-secondary edit-prompt-btn">
              <i class="bi bi-pencil"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger delete-prompt-btn">
              <i class="bi bi-trash"></i>
            </button>
            <button class="btn btn-sm btn-outline-primary use-prompt-btn">
              <i class="bi bi-lightning"></i>
            </button>
          </div>
        </div>
        <div class="card-body">
          <div class="prompt-preview mb-2">${truncateText(prompt.content, 150)}</div>
          ${prompt.description ? `<div class="prompt-description text-muted small">${prompt.description}</div>` : ''}
        </div>
        <div class="card-footer d-flex justify-content-between">
          <span class="badge bg-secondary">${getCategoryLabel(prompt.category)}</span>
          <small class="text-muted">Créé le ${formatDate(prompt.created)}</small>
        </div>
      </div>
    </div>
  `);
  
  // Ajouter les gestionnaires d'événements
  card.find('.edit-prompt-btn').on('click', () => openEditPromptModal(prompt));
  card.find('.delete-prompt-btn').on('click', () => confirmDeletePrompt(prompt));
  card.find('.use-prompt-btn').on('click', () => usePrompt(prompt));
  
  return card;
}

// Mettre à jour l'interface des catégories
function updateCategoriesUI() {
  const categoriesList = $('#prompt-categories');
  
  // Conserver les catégories par défaut
  const defaultCategories = ['all', 'creative', 'technical', 'business', 'personal'];
  
  // Supprimer les catégories personnalisées existantes
  categoriesList.find('a').not(function() {
    return defaultCategories.includes($(this).data('category'));
  }).remove();
  
  // Ajouter les catégories personnalisées
  promptsState.customCategories.forEach(category => {
    if (!defaultCategories.includes(category)) {
      categoriesList.append(`
        <a href="#" class="list-group-item list-group-item-action" data-category="${category}">
          ${getCategoryLabel(category)}
        </a>
      `);
    }
  });
  
  // Mettre à jour la catégorie active
  categoriesList.find('a').removeClass('active');
  categoriesList.find(`a[data-category="${promptsState.activeCategory}"]`).addClass('active');
  
  // Ajouter les gestionnaires d'événements
  categoriesList.find('a').off('click').on('click', function(e) {
    e.preventDefault();
    promptsState.activeCategory = $(this).data('category');
    updatePromptsUI();
    updateCategoriesUI();
  });
}

// Mettre à jour les statistiques
function updateStats() {
  $('#total-prompts-count').text(promptsState.prompts.length);
  
  const usedCount = promptsState.prompts.filter(p => p.usageCount && p.usageCount > 0).length;
  $('#used-prompts-count').text(usedCount);
  
  $('#last-modified-date').text(promptsState.lastModified ? formatDate(promptsState.lastModified) : '-');
}

// Ouvrir la modal pour créer un nouveau prompt
function openNewPromptModal() {
  // Réinitialiser le formulaire
  $('#prompt-form')[0].reset();
  $('#prompt-id').val('');
  $('#promptModalLabel').text('Nouveau Prompt');
  
  // Afficher la modal
  $('#prompt-modal').modal('show');
}

// Ouvrir la modal pour éditer un prompt existant
function openEditPromptModal(prompt) {
  // Remplir le formulaire avec les données du prompt
  $('#prompt-id').val(prompt.id);
  $('#prompt-title').val(prompt.title);
  $('#prompt-category').val(prompt.category);
  $('#prompt-content').val(prompt.content);
  $('#prompt-description').val(prompt.description || '');
  $('#prompt-favorite').prop('checked', prompt.favorite || false);
  
  // Mettre à jour le titre de la modal
  $('#promptModalLabel').text('Modifier le Prompt');
  
  // Afficher la modal
  $('#prompt-modal').modal('show');
}

// Sauvegarder un prompt (nouveau ou existant)
function savePrompt() {
  const promptData = {
    id: $('#prompt-id').val() || generateId(),
    title: $('#prompt-title').val(),
    category: $('#prompt-category').val(),
    content: $('#prompt-content').val(),
    description: $('#prompt-description').val(),
    favorite: $('#prompt-favorite').is(':checked'),
    created: new Date().toISOString(),
    usageCount: 0
  };
  
  // Envoyer au serveur
  socket.emit('save prompt', promptData);
}

// Confirmer la suppression d'un prompt
function confirmDeletePrompt(prompt) {
  if (confirm(`Êtes-vous sûr de vouloir supprimer le prompt "${prompt.title}" ?`)) {
    socket.emit('delete prompt', { id: prompt.id });
  }
}

// Utiliser un prompt
function usePrompt(prompt) {
  // Incrémenter le compteur d'utilisation
  prompt.usageCount = (prompt.usageCount || 0) + 1;
  socket.emit('save prompt', prompt);
  
  // Rediriger vers l'interface de chat avec le prompt
  window.location.href = `/luna?prompt=${encodeURIComponent(prompt.content)}`;
}

// Fonctions utilitaires

// Générer un ID unique
function generateId() {
  return 'prompt_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
}

// Tronquer un texte à une longueur maximale
function truncateText(text, maxLength) {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
}

// Formater une date
function formatDate(dateString) {
  const date = new Date(dateString);
  return date.toLocaleDateString('fr-FR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
}

// Obtenir le libellé d'une catégorie
function getCategoryLabel(category) {
  const labels = {
    'all': 'Tous',
    'creative': 'Créativité',
    'technical': 'Technique',
    'business': 'Business',
    'personal': 'Personnel'
  };
  
  return labels[category] || category;
}

// Afficher une notification
function showNotification(message, type = 'info') {
  // Créer l'élément de notification s'il n'existe pas
  let notificationArea = $('#notification-area');
  if (notificationArea.length === 0) {
    $('body').append('<div id="notification-area"></div>');
    notificationArea = $('#notification-area');
  }
  
  // Créer la notification
  const notification = $(`<div class="notification notification-${type}">${message}</div>`);
  notificationArea.append(notification);
  
  // Afficher avec animation
  setTimeout(() => {
    notification.addClass('show');
    
    // Masquer après 3 secondes
    setTimeout(() => {
      notification.removeClass('show');
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }, 100);
}

// Initialisation au chargement de la page
$(document).ready(function() {
  // Gestionnaires d'événements pour les boutons
  $('#new-prompt-btn').on('click', openNewPromptModal);
  $('#save-prompt-btn').on('click', savePrompt);
  
  // Gestionnaire pour l'ajout de catégorie
  $('#add-category-btn').on('click', function() {
    $('#category-modal').modal('show');
  });
  
  // Gestionnaire pour la sauvegarde de catégorie
  $('#save-category-btn').on('click', function() {
    const categoryName = $('#category-name').val().trim();
    if (categoryName) {
      // Ajouter la catégorie si elle n'existe pas déjà
      if (!promptsState.customCategories.includes(categoryName)) {
        promptsState.customCategories.push(categoryName);
        socket.emit('save categories', { categories: promptsState.customCategories });
        updateCategoriesUI();
      }
      
      // Fermer la modal
      $('#category-modal').modal('hide');
      $('#category-name').val('');
    }
  });
  
  // Gestionnaires pour l'importation/exportation
  $('#import-prompts-btn').on('click', function() {
    // À implémenter
    alert('Fonctionnalité d\'importation à venir');
  });
  
  $('#export-prompts-btn').on('click', function() {
    // À implémenter
    alert('Fonctionnalité d\'exportation à venir');
  });
  
  // Charger les prompts au démarrage
  loadPrompts();
});
