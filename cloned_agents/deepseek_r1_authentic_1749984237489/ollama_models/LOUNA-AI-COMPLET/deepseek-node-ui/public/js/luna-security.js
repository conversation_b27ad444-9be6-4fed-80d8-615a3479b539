/**
 * Luna Security - Gestion des fonctionnalités de sécurité
 * Ce script gère le VPN et l'antivirus dans l'interface Luna
 */

/**
 * Met à jour la date et l'heure dans l'interface
 */
function updateDateTime() {
  const now = new Date();

  // Mettre à jour l'élément current-datetime
  const datetimeElement = document.getElementById('current-datetime');
  if (datetimeElement) {
    datetimeElement.textContent = now.toLocaleString('fr-FR');
  }
}

// Initialiser la connexion Socket.IO
const securitySocket = io();

// Variables globales
let vpnConnected = false;
let vpnConnectionStartTime = null;
let vpnUptimeInterval = null;
let antivirusEnabled = true;

// Initialisation au chargement de la page
$(document).ready(function() {
  console.log('Module de sécurité Luna chargé');

  // Initialiser la date et l'heure
  updateDateTime();
  // Mettre à jour la date et l'heure toutes les secondes
  setInterval(updateDateTime, 1000);

  // Initialiser les boutons VPN
  initVPNButtons();

  // Initialiser les boutons antivirus
  initAntivirusButtons();

  // Gestionnaire pour le bouton VPN dans la barre de navigation
  $('#vpn-nav-toggle-btn').on('click', function() {
    if (vpnConnected) {
      disconnectVPN();
    } else {
      connectVPN();
    }

    // Rediriger vers la page des paramètres si on n'y est pas déjà
    if (window.location.pathname !== '/luna/settings') {
      window.location.href = '/luna/settings#security';
    } else {
      $('#security-tab').tab('show');
    }
  });

  // Gestionnaire pour le bouton antivirus dans la barre de navigation
  $('#antivirus-nav-toggle-btn').on('click', function() {
    if (antivirusEnabled) {
      disableAntivirus();
    } else {
      enableAntivirus();
    }

    // Rediriger vers la page des paramètres si on n'y est pas déjà
    if (window.location.pathname !== '/luna/settings') {
      window.location.href = '/luna/settings#security';
    } else {
      $('#security-tab').tab('show');
    }
  });

  // Gestionnaire pour le bouton rapide VPN dans les actions rapides
  $('#vpn-quick-toggle-btn').on('click', function() {
    if (vpnConnected) {
      disconnectVPN();
    } else {
      connectVPN();
    }

    // Activer l'onglet de sécurité pour montrer les paramètres VPN
    $('#security-tab').tab('show');
  });

  // Gestionnaire pour le bouton rapide antivirus dans les actions rapides
  $('#antivirus-quick-toggle-btn').on('click', function() {
    if (antivirusEnabled) {
      disableAntivirus();
    } else {
      enableAntivirus();
    }

    // Activer l'onglet de sécurité pour montrer les paramètres antivirus
    $('#security-tab').tab('show');
  });

  // Gestionnaire pour le bouton d'analyse antivirus
  $('#scan-now-btn').on('click', function() {
    startAntivirusScan();
  });
});

// Fonction pour initialiser les boutons VPN
function initVPNButtons() {
  if (vpnConnected) {
    // VPN connecté
    $('#vpn-status-badge').removeClass('bg-danger').addClass('bg-success').text('Connecté');
    $('#vpn-connect-btn').html('<i class="bi bi-shield-x me-2"></i> Déconnecter le VPN');
    $('#vpn-connect-btn').removeClass('btn-luna').addClass('btn-danger');

    $('#vpn-quick-toggle-btn').html('<i class="bi bi-shield-x me-1"></i> Désactiver VPN');
    $('#vpn-quick-toggle-btn').removeClass('btn-luna').addClass('btn-danger');

    $('#vpn-nav-toggle-btn').removeClass('btn-luna').addClass('btn-success');
    $('#vpn-nav-status').text('VPN ON');

    $('a.nav-link[href="/luna/vpn"]').addClass('active-vpn');
  } else {
    // VPN déconnecté
    $('#vpn-status-badge').removeClass('bg-success').addClass('bg-danger').text('Déconnecté');
    $('#vpn-connect-btn').html('<i class="bi bi-shield-check me-2"></i> Connecter le VPN');
    $('#vpn-connect-btn').removeClass('btn-danger').addClass('btn-luna');

    $('#vpn-quick-toggle-btn').html('<i class="bi bi-shield-check me-1"></i> Activer VPN');
    $('#vpn-quick-toggle-btn').removeClass('btn-danger').addClass('btn-luna');

    $('#vpn-nav-toggle-btn').removeClass('btn-success').addClass('btn-luna');
    $('#vpn-nav-status').text('VPN OFF');

    $('a.nav-link[href="/luna/vpn"]').removeClass('active-vpn');
  }
}

// Fonction pour initialiser les boutons antivirus
function initAntivirusButtons() {
  if (antivirusEnabled) {
    // Antivirus activé
    $('#antivirus-toggle').prop('checked', true);
    $('#antivirus-quick-toggle-btn').html('<i class="bi bi-virus me-1"></i> Antivirus Actif');
    $('#antivirus-quick-toggle-btn').removeClass('btn-danger').addClass('btn-success');

    $('#antivirus-nav-toggle-btn').removeClass('btn-danger').addClass('btn-success');
    $('#antivirus-nav-status').text('ANTIVIRUS ON');
  } else {
    // Antivirus désactivé
    $('#antivirus-toggle').prop('checked', false);
    $('#antivirus-quick-toggle-btn').html('<i class="bi bi-virus me-1"></i> Activer Antivirus');
    $('#antivirus-quick-toggle-btn').removeClass('btn-success').addClass('btn-danger');

    $('#antivirus-nav-toggle-btn').removeClass('btn-success').addClass('btn-danger');
    $('#antivirus-nav-status').text('ANTIVIRUS OFF');
  }
}

// Fonction pour connecter le VPN
function connectVPN() {
  $('#vpn-connect-btn').html('<i class="bi bi-hourglass-split me-2"></i> Connexion en cours...');
  $('#vpn-connect-btn').prop('disabled', true);
  $('#vpn-quick-toggle-btn').html('<i class="bi bi-hourglass-split me-1"></i> Connexion...');
  $('#vpn-quick-toggle-btn').prop('disabled', true);

  // Simuler la connexion
  setTimeout(() => {
    vpnConnected = true;
    vpnConnectionStartTime = new Date();

    // Mettre à jour l'interface
    initVPNButtons();

    // Mettre à jour les informations VPN
    const server = $('#vpn-server-select option:selected').text().split(' ')[0];
    $('#vpn-ip').text('192.168.1.XXX (masquée)');
    $('#vpn-location').text(server);
    $('#vpn-protection').text('100%');

    // Démarrer l'intervalle pour mettre à jour le temps de connexion
    startVPNUptimeInterval();

    // Réactiver les boutons
    $('#vpn-connect-btn').prop('disabled', false);
    $('#vpn-quick-toggle-btn').prop('disabled', false);

    // Ajouter une entrée au journal
    addLogEntry(`VPN connecté au serveur ${server}`);

    // Afficher une notification
    showNotification(`VPN connecté au serveur ${server}`, 'success');
  }, 2000);
}

// Fonction pour déconnecter le VPN
function disconnectVPN() {
  $('#vpn-connect-btn').html('<i class="bi bi-hourglass-split me-2"></i> Déconnexion en cours...');
  $('#vpn-connect-btn').prop('disabled', true);
  $('#vpn-quick-toggle-btn').html('<i class="bi bi-hourglass-split me-1"></i> Déconnexion...');
  $('#vpn-quick-toggle-btn').prop('disabled', true);

  // Simuler la déconnexion
  setTimeout(() => {
    vpnConnected = false;
    vpnConnectionStartTime = null;

    // Mettre à jour l'interface
    initVPNButtons();

    // Mettre à jour les informations VPN
    $('#vpn-ip').text('Non masquée');
    $('#vpn-location').text('Guadeloupe');
    $('#vpn-protection').text('0%');
    $('#vpn-uptime').text('00:00:00');

    // Arrêter l'intervalle
    stopVPNUptimeInterval();

    // Réactiver les boutons
    $('#vpn-connect-btn').prop('disabled', false);
    $('#vpn-quick-toggle-btn').prop('disabled', false);

    // Ajouter une entrée au journal
    addLogEntry('VPN déconnecté');

    // Afficher une notification
    showNotification('VPN déconnecté', 'info');
  }, 1500);
}

// Fonction pour activer l'antivirus
function enableAntivirus() {
  antivirusEnabled = true;

  // Mettre à jour l'interface
  initAntivirusButtons();

  // Ajouter une entrée au journal
  addLogEntry('Antivirus activé');

  // Afficher une notification
  showNotification('Antivirus activé et protection en temps réel activée', 'success');
}

// Fonction pour désactiver l'antivirus
function disableAntivirus() {
  antivirusEnabled = false;

  // Mettre à jour l'interface
  initAntivirusButtons();

  // Ajouter une entrée au journal
  addLogEntry('Antivirus désactivé');

  // Afficher une notification
  showNotification('Antivirus désactivé', 'warning');
}

// Fonction pour démarrer une analyse antivirus
function startAntivirusScan() {
  $('#scan-now-btn').html('<i class="bi bi-search me-2 spin"></i> Analyse en cours...');
  $('#scan-now-btn').prop('disabled', true);

  // Simuler une analyse
  let progress = 0;
  const interval = setInterval(() => {
    progress += 10;
    if (progress <= 100) {
      $('#scan-now-btn').html(`<i class="bi bi-search me-2 spin"></i> Analyse en cours... ${progress}%`);
    } else {
      clearInterval(interval);
      $('#scan-now-btn').html('<i class="bi bi-check-lg me-2"></i> Analyse terminée');

      // Ajouter une entrée au journal
      addLogEntry('Analyse antivirus terminée - Aucune menace détectée');

      // Afficher une notification
      showNotification('Analyse antivirus terminée - Aucune menace détectée', 'success');

      // Restaurer le bouton après 3 secondes
      setTimeout(() => {
        $('#scan-now-btn').html('<i class="bi bi-search me-2"></i> Analyser maintenant');
        $('#scan-now-btn').prop('disabled', false);
      }, 3000);
    }
  }, 500);
}

// Variables pour l'intervalle de mise à jour du temps de connexion VPN
let vpnUptimeInterval = null;

// Fonction pour démarrer l'intervalle de mise à jour du temps de connexion VPN
function startVPNUptimeInterval() {
  // Arrêter l'intervalle existant s'il y en a un
  stopVPNUptimeInterval();

  // Démarrer un nouvel intervalle
  vpnUptimeInterval = setInterval(updateVPNUptime, 1000);
}

// Fonction pour arrêter l'intervalle de mise à jour du temps de connexion VPN
function stopVPNUptimeInterval() {
  if (vpnUptimeInterval) {
    clearInterval(vpnUptimeInterval);
    vpnUptimeInterval = null;
  }
}

// Fonction pour mettre à jour le temps de connexion VPN
function updateVPNUptime() {
  if (!vpnConnectionStartTime) return;

  const now = new Date();
  const diff = now - vpnConnectionStartTime;

  // Convertir en heures, minutes, secondes
  const hours = Math.floor(diff / 3600000).toString().padStart(2, '0');
  const minutes = Math.floor((diff % 3600000) / 60000).toString().padStart(2, '0');
  const seconds = Math.floor((diff % 60000) / 1000).toString().padStart(2, '0');

  $('#vpn-uptime').text(`${hours}:${minutes}:${seconds}`);
}

// Fonction pour ajouter une entrée au journal
function addLogEntry(message) {
  const now = new Date();
  const timeString = now.toLocaleString('fr-FR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });

  const logEntry = $(`
    <div class="log-entry">
      <span class="log-time">${timeString}</span>
      <span class="log-message">${message}</span>
    </div>
  `);

  // Ajouter au début du journal
  $('#settings-log').prepend(logEntry);
}

// Fonction pour afficher une notification
function showNotification(message, type = 'info') {
  // Créer l'élément de notification s'il n'existe pas
  let notificationArea = $('#notification-area');
  if (notificationArea.length === 0) {
    $('body').append('<div id="notification-area"></div>');
    notificationArea = $('#notification-area');
  }

  // Créer la notification
  const notification = $(`<div class="notification notification-${type}">${message}</div>`);
  notificationArea.append(notification);

  // Afficher avec animation
  setTimeout(() => {
    notification.addClass('show');

    // Masquer après 3 secondes
    setTimeout(() => {
      notification.removeClass('show');
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }, 100);
}

// Ajouter des styles pour l'animation de rotation
$('<style>')
  .text(`
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .spin {
      animation: spin 1s linear infinite;
    }

    #notification-area {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 9999;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
    }

    .notification {
      background-color: #333;
      color: white;
      padding: 12px 20px;
      margin-bottom: 10px;
      border-radius: 5px;
      box-shadow: 0 3px 10px rgba(0,0,0,0.2);
      transform: translateX(120%);
      transition: transform 0.3s ease;
      max-width: 350px;
    }

    .notification.show {
      transform: translateX(0);
    }

    .notification-success {
      background-color: #4caf50;
    }

    .notification-error {
      background-color: #f44336;
    }

    .notification-warning {
      background-color: #ff9800;
    }

    .notification-info {
      background-color: #2196f3;
    }
  `)
  .appendTo('head');
