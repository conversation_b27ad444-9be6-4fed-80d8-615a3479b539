/**
 * Script pour l'interface de mémoire thermique
 */

document.addEventListener('DOMContentLoaded', () => {
  // Éléments DOM
  const refreshBtn = document.getElementById('refresh-btn');
  const exportMemoryBtn = document.getElementById('export-memory-btn');
  const forceCycleBtn = document.getElementById('force-cycle-btn');
  const generateDreamBtn = document.getElementById('generate-dream-btn');
  const clearMemoryBtn = document.getElementById('clear-memory-btn');
  const memorySearchInput = document.getElementById('memory-search-input');
  const memorySearchBtn = document.getElementById('memory-search-btn');
  const memorySearchResults = document.getElementById('memory-search-results');
  const searchResultsList = document.getElementById('search-results-list');

  // Éléments du modal de détail
  const memoryDetailModal = new bootstrap.Modal(document.getElementById('memory-detail-modal'));
  const memoryDetailKey = document.getElementById('memory-detail-key');
  const memoryDetailData = document.getElementById('memory-detail-data');
  const memoryDetailTemperature = document.getElementById('memory-detail-temperature');
  const memoryDetailCategory = document.getElementById('memory-detail-category');
  const memoryDetailSource = document.getElementById('memory-detail-source');
  const memoryDetailTimestamp = document.getElementById('memory-detail-timestamp');
  const memoryDetailLastAccessed = document.getElementById('memory-detail-last-accessed');
  const memoryDetailAccessCount = document.getElementById('memory-detail-access-count');
  const deleteMemoryItemBtn = document.getElementById('delete-memory-item-btn');

  // Éléments de statistiques
  const totalEntriesElement = document.getElementById('total-entries');
  const totalEntriesBarElement = document.getElementById('total-entries-bar');
  const averageTemperatureElement = document.getElementById('average-temperature');
  const averageTemperatureBarElement = document.getElementById('average-temperature-bar');
  const cyclesPerformedElement = document.getElementById('cycles-performed');
  const lastCycleTimeElement = document.getElementById('last-cycle-time');

  // Éléments de comptage
  const instantCountElement = document.getElementById('instant-count');
  const shortTermCountElement = document.getElementById('short-term-count');
  const workingMemoryCountElement = document.getElementById('working-memory-count');
  const mediumTermCountElement = document.getElementById('medium-term-count');
  const longTermCountElement = document.getElementById('long-term-count');
  const dreamMemoryCountElement = document.getElementById('dream-memory-count');

  // Éléments Kyber
  const boostFactorElement = document.getElementById('boost-factor');
  const boostFactorBarElement = document.getElementById('boost-factor-bar');
  const boostFactorSlider = document.getElementById('boost-factor-slider');
  const kyberTemperatureElement = document.getElementById('kyber-temperature');
  const kyberTemperatureBarElement = document.getElementById('kyber-temperature-bar');
  const temperatureSlider = document.getElementById('temperature-slider');
  const stabilityElement = document.getElementById('stability');
  const stabilityBarElement = document.getElementById('stability-bar');
  const stabilitySlider = document.getElementById('stability-slider');
  const kyberEnabledCheckbox = document.getElementById('kyber-enabled');
  const saveKyberConfigBtn = document.getElementById('save-kyber-config-btn');
  const resetKyberConfigBtn = document.getElementById('reset-kyber-config-btn');
  const lockKyberBtn = document.getElementById('lock-kyber-btn');

  // Éléments de visualisation du cerveau
  const brainVisualization = document.getElementById('brain-visualization');

  // Éléments des niveaux de mémoire
  const instantMemoryItems = document.getElementById('instant-memory-items');
  const shortTermItems = document.getElementById('short-term-items');
  const workingMemoryItems = document.getElementById('working-memory-items');
  const mediumTermItems = document.getElementById('medium-term-items');
  const longTermItems = document.getElementById('long-term-items');
  const dreamMemoryItems = document.getElementById('dream-memory-items');

  // Variables globales
  let socket;
  let memoryData = {};
  let currentMemoryItemId = null;
  let brainState = null;

  // Initialisation
  initSocket();
  setupEventListeners();
  initBrainVisualization();
  loadMemoryData();
  loadKyberState();

  /**
   * Initialise la connexion socket
   */
  function initSocket() {
    socket = io();

    socket.on('connect', () => {
      console.log('Connected to server');
    });

    socket.on('memory update', (data) => {
      console.log('Memory update received:', data);
      memoryData = data;
      updateMemoryDisplay();
    });

    socket.on('brain state', (state) => {
      console.log('Brain state received:', state);
      brainState = state;
      updateBrainVisualization();
    });

    socket.on('connect_error', (error) => {
      console.error('Connection error:', error);
    });
  }

  /**
   * Configure les écouteurs d'événements
   */
  function setupEventListeners() {
    // Bouton de rafraîchissement
    refreshBtn.addEventListener('click', () => {
      loadMemoryData();
    });

    // Bouton d'exportation
    exportMemoryBtn.addEventListener('click', () => {
      exportMemory();
    });

    // Bouton de cycle forcé
    forceCycleBtn.addEventListener('click', () => {
      forceCycle();
    });

    // Bouton de génération de rêve
    generateDreamBtn.addEventListener('click', () => {
      generateDream();
    });

    // Bouton de suppression de la mémoire
    clearMemoryBtn.addEventListener('click', () => {
      if (confirm('Êtes-vous sûr de vouloir effacer toute la mémoire ? Cette action est irréversible.')) {
        clearMemory();
      }
    });

    // Recherche dans la mémoire
    memorySearchBtn.addEventListener('click', () => {
      searchMemory();
    });

    memorySearchInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        searchMemory();
      }
    });

    // Bouton de suppression d'un élément de mémoire
    deleteMemoryItemBtn.addEventListener('click', () => {
      if (currentMemoryItemId && confirm('Êtes-vous sûr de vouloir supprimer cet élément de mémoire ?')) {
        deleteMemoryItem(currentMemoryItemId);
        memoryDetailModal.hide();
      }
    });

    // Contrôles de l'accélérateur Kyber
    if (kyberEnabledCheckbox) {
      kyberEnabledCheckbox.addEventListener('change', () => {
        const enabled = kyberEnabledCheckbox.checked;
        if (enabled) {
          enableKyberAccelerator();
        } else {
          disableKyberAccelerator();
        }
      });
    }

    if (boostFactorSlider) {
      boostFactorSlider.addEventListener('input', () => {
        boostFactorElement.textContent = `${boostFactorSlider.value}x`;
        boostFactorBarElement.style.width = `${(boostFactorSlider.value - 1.0) * 100}%`;
      });
    }

    if (temperatureSlider) {
      temperatureSlider.addEventListener('input', () => {
        kyberTemperatureElement.textContent = temperatureSlider.value;
        kyberTemperatureBarElement.style.width = `${temperatureSlider.value * 100}%`;
      });
    }

    if (stabilitySlider) {
      stabilitySlider.addEventListener('input', () => {
        stabilityElement.textContent = stabilitySlider.value;
        stabilityBarElement.style.width = `${stabilitySlider.value * 100}%`;
      });
    }

    if (saveKyberConfigBtn) {
      saveKyberConfigBtn.addEventListener('click', () => {
        saveKyberConfig();
      });
    }

    if (resetKyberConfigBtn) {
      resetKyberConfigBtn.addEventListener('click', () => {
        resetKyberConfig();
      });
    }

    if (lockKyberBtn) {
      lockKyberBtn.addEventListener('click', () => {
        const isLocked = lockKyberBtn.innerHTML.includes('Déverrouiller');
        lockKyberAccelerator(!isLocked);
      });
    }
  }

  /**
   * Initialise la visualisation du cerveau
   */
  function initBrainVisualization() {
    // Créer les régions du cerveau
    const regions = [
      { id: 'frontal', name: 'Frontal (Mémoire instantanée)', color: '#dc3545', x: 30, y: 30, size: 100 },
      { id: 'parietal', name: 'Pariétal (Mémoire à court terme)', color: '#0d6efd', x: 70, y: 30, size: 90 },
      { id: 'temporal', name: 'Temporal (Mémoire de travail)', color: '#ffc107', x: 20, y: 60, size: 80 },
      { id: 'occipital', name: 'Occipital (Mémoire à moyen terme)', color: '#198754', x: 70, y: 70, size: 70 },
      { id: 'limbic', name: 'Limbique (Mémoire à long terme)', color: '#6f42c1', x: 50, y: 50, size: 60 },
      { id: 'creative', name: 'Créatif (Mémoire des rêves)', color: '#fd7e14', x: 40, y: 20, size: 50 }
    ];

    // Vider le conteneur
    brainVisualization.innerHTML = '';

    // Ajouter les régions au DOM
    regions.forEach(region => {
      const element = document.createElement('div');
      element.id = `brain-${region.id}`;
      element.className = 'brain-region';
      element.style.backgroundColor = region.color;
      element.style.left = `${region.x}%`;
      element.style.top = `${region.y}%`;
      element.style.width = `${region.size}px`;
      element.style.height = `${region.size}px`;
      element.setAttribute('data-bs-toggle', 'tooltip');
      element.setAttribute('data-bs-placement', 'top');
      element.setAttribute('title', `${region.name}: 0%`);
      element.setAttribute('data-region', region.id);

      // Ajouter un événement de clic pour afficher les détails
      element.addEventListener('click', () => {
        showRegionDetails(region.id);
      });

      brainVisualization.appendChild(element);
    });

    // Créer les connexions entre les régions
    createBrainConnections(regions);

    // Ajouter le panneau d'information
    const infoPanel = document.createElement('div');
    infoPanel.className = 'brain-info-panel';
    infoPanel.id = 'brain-info-panel';
    infoPanel.innerHTML = 'Température CPU: 36.5°C | Activité: 0%';
    brainVisualization.appendChild(infoPanel);

    // Initialiser les tooltips
    const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    tooltips.forEach(tooltip => {
      new bootstrap.Tooltip(tooltip);
    });
  }

  /**
   * Crée les connexions entre les régions du cerveau
   * @param {Array} regions - Régions du cerveau
   */
  function createBrainConnections(regions) {
    // Définir les connexions entre les régions
    const connections = [
      { from: 'frontal', to: 'parietal' },
      { from: 'frontal', to: 'temporal' },
      { from: 'frontal', to: 'limbic' },
      { from: 'frontal', to: 'creative' },
      { from: 'parietal', to: 'occipital' },
      { from: 'parietal', to: 'temporal' },
      { from: 'temporal', to: 'limbic' },
      { from: 'temporal', to: 'occipital' },
      { from: 'occipital', to: 'limbic' },
      { from: 'limbic', to: 'creative' }
    ];

    // Créer les connexions
    connections.forEach(connection => {
      const fromRegion = regions.find(r => r.id === connection.from);
      const toRegion = regions.find(r => r.id === connection.to);

      if (fromRegion && toRegion) {
        // Calculer les coordonnées
        const fromX = fromRegion.x;
        const fromY = fromRegion.y;
        const toX = toRegion.x;
        const toY = toRegion.y;

        // Calculer la longueur et l'angle
        const length = Math.sqrt(Math.pow(toX - fromX, 2) + Math.pow(toY - fromY, 2));
        const angle = Math.atan2(toY - fromY, toX - fromX) * 180 / Math.PI;

        // Créer l'élément de connexion
        const connectionElement = document.createElement('div');
        connectionElement.className = 'brain-connection';
        connectionElement.id = `connection-${connection.from}-${connection.to}`;
        connectionElement.style.width = `${length}%`;
        connectionElement.style.left = `${fromX}%`;
        connectionElement.style.top = `${fromY}%`;
        connectionElement.style.transform = `rotate(${angle}deg)`;

        brainVisualization.appendChild(connectionElement);
      }
    });
  }

  /**
   * Affiche les détails d'une région du cerveau
   * @param {string} regionId - ID de la région
   */
  function showRegionDetails(regionId) {
    if (!brainState || !brainState.zones) return;

    const zone = brainState.zones[regionId];
    if (!zone) return;

    // Déterminer le niveau de mémoire correspondant
    let memoryLevel = '';
    let memoryItems = [];

    switch (regionId) {
      case 'frontal':
        memoryLevel = 'instantMemory';
        memoryItems = memoryData.instantMemory ? Object.values(memoryData.instantMemory) : [];
        break;
      case 'parietal':
        memoryLevel = 'shortTerm';
        memoryItems = memoryData.shortTerm ? Object.values(memoryData.shortTerm) : [];
        break;
      case 'temporal':
        memoryLevel = 'workingMemory';
        memoryItems = memoryData.workingMemory ? Object.values(memoryData.workingMemory) : [];
        break;
      case 'occipital':
        memoryLevel = 'mediumTerm';
        memoryItems = memoryData.mediumTerm ? Object.values(memoryData.mediumTerm) : [];
        break;
      case 'limbic':
        memoryLevel = 'longTerm';
        memoryItems = memoryData.longTerm ? Object.values(memoryData.longTerm) : [];
        break;
      case 'creative':
        memoryLevel = 'dreamMemory';
        memoryItems = memoryData.dreamMemory ? Object.values(memoryData.dreamMemory) : [];
        break;
    }

    // Faire défiler jusqu'au niveau de mémoire correspondant
    const element = document.getElementById(memoryLevel);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }

    // Mettre en évidence le niveau de mémoire
    highlightMemoryLevel(memoryLevel);
  }

  /**
   * Met en évidence un niveau de mémoire
   * @param {string} memoryLevel - Niveau de mémoire
   */
  function highlightMemoryLevel(memoryLevel) {
    // Réinitialiser toutes les mises en évidence
    document.querySelectorAll('.memory-level').forEach(level => {
      level.classList.remove('bg-dark');
    });

    // Mettre en évidence le niveau sélectionné
    const element = document.getElementById(memoryLevel);
    if (element) {
      element.classList.add('bg-dark');

      // Retirer la mise en évidence après 3 secondes
      setTimeout(() => {
        element.classList.remove('bg-dark');
      }, 3000);
    }
  }

  /**
   * Met à jour la visualisation du cerveau
   */
  function updateBrainVisualization() {
    if (!brainState) return;

    // Mettre à jour le panneau d'information
    const infoPanel = document.getElementById('brain-info-panel');
    if (infoPanel && brainState.cpuInfo) {
      infoPanel.innerHTML = `Température CPU: ${brainState.cpuInfo.temperature}°C | Activité: ${brainState.cpuInfo.usage}% | Mémoire: ${Math.round(brainState.cpuInfo.memory.used / 1024)}GB/${Math.round(brainState.cpuInfo.memory.total / 1024)}GB`;
    }

    // Mettre à jour les régions du cerveau
    Object.entries(brainState.zones).forEach(([region, data]) => {
      const element = document.getElementById(`brain-${region}`);
      if (element) {
        // Mettre à jour la taille en fonction de l'activité
        const baseSize = parseInt(element.style.width);
        const newSize = baseSize * (0.8 + (data.activity / 100) * 0.4);
        element.style.width = `${newSize}px`;
        element.style.height = `${newSize}px`;

        // Mettre à jour l'opacité en fonction de la température
        const opacity = 0.5 + (data.temperature - 36) / 2;
        element.style.opacity = opacity;

        // Mettre à jour le tooltip
        const tooltip = bootstrap.Tooltip.getInstance(element);
        if (tooltip) {
          tooltip.setContent({
            '.tooltip-inner': `${region.charAt(0).toUpperCase() + region.slice(1)}: ${data.activity}% (${data.temperature}°C)<br>Entrées: ${data.entries || 0}`
          });
        }

        // Ajouter des particules de flux de mémoire si l'activité est élevée
        if (data.activity > 70 && Math.random() > 0.7) {
          createMemoryFlow(region);
        }
      }
    });

    // Mettre à jour les connexions
    updateBrainConnections();

    // Mettre à jour les statistiques Kyber
    updateKyberDisplay(brainState.kyber);
  }

  /**
   * Met à jour les connexions entre les régions du cerveau
   */
  function updateBrainConnections() {
    if (!brainState) return;

    // Définir les connexions entre les régions
    const connections = [
      { from: 'frontal', to: 'parietal' },
      { from: 'frontal', to: 'temporal' },
      { from: 'frontal', to: 'limbic' },
      { from: 'frontal', to: 'creative' },
      { from: 'parietal', to: 'occipital' },
      { from: 'parietal', to: 'temporal' },
      { from: 'temporal', to: 'limbic' },
      { from: 'temporal', to: 'occipital' },
      { from: 'occipital', to: 'limbic' },
      { from: 'limbic', to: 'creative' }
    ];

    // Mettre à jour les connexions
    connections.forEach(connection => {
      const connectionElement = document.getElementById(`connection-${connection.from}-${connection.to}`);
      if (connectionElement && brainState.zones[connection.from] && brainState.zones[connection.to]) {
        // Calculer l'activité moyenne des deux régions
        const fromActivity = brainState.zones[connection.from].activity;
        const toActivity = brainState.zones[connection.to].activity;
        const avgActivity = (fromActivity + toActivity) / 2;

        // Mettre à jour l'opacité en fonction de l'activité
        const opacity = 0.1 + (avgActivity / 100) * 0.4;
        connectionElement.style.opacity = opacity;

        // Mettre à jour la couleur en fonction de l'activité
        const hue = 200 + (avgActivity / 100) * 160; // De bleu à rouge
        connectionElement.style.backgroundColor = `hsla(${hue}, 80%, 60%, ${opacity})`;

        // Mettre à jour l'épaisseur en fonction de l'activité
        const thickness = 1 + (avgActivity / 100) * 3;
        connectionElement.style.height = `${thickness}px`;

        // Ajouter des particules de flux si l'activité est élevée
        if (avgActivity > 60 && Math.random() > 0.9) {
          createConnectionFlow(connection.from, connection.to);
        }
      }
    });
  }

  /**
   * Crée un flux de mémoire dans une région du cerveau
   * @param {string} regionId - ID de la région
   */
  function createMemoryFlow(regionId) {
    const region = document.getElementById(`brain-${regionId}`);
    if (!region) return;

    // Créer l'élément de flux
    const flow = document.createElement('div');
    flow.className = 'memory-flow';
    flow.style.backgroundColor = region.style.backgroundColor;

    // Positionner l'élément au centre de la région
    const regionRect = region.getBoundingClientRect();
    const brainRect = brainVisualization.getBoundingClientRect();

    const x = (regionRect.left + regionRect.width / 2 - brainRect.left) / brainRect.width * 100;
    const y = (regionRect.top + regionRect.height / 2 - brainRect.top) / brainRect.height * 100;

    flow.style.left = `${x}%`;
    flow.style.top = `${y}%`;

    // Ajouter l'élément au DOM
    brainVisualization.appendChild(flow);

    // Supprimer l'élément après l'animation
    setTimeout(() => {
      if (flow.parentNode) {
        flow.parentNode.removeChild(flow);
      }
    }, 5000);
  }

  /**
   * Crée un flux de mémoire entre deux régions du cerveau
   * @param {string} fromRegionId - ID de la région source
   * @param {string} toRegionId - ID de la région cible
   */
  function createConnectionFlow(fromRegionId, toRegionId) {
    const fromRegion = document.getElementById(`brain-${fromRegionId}`);
    const toRegion = document.getElementById(`brain-${toRegionId}`);
    if (!fromRegion || !toRegion) return;

    // Créer l'élément de flux
    const flow = document.createElement('div');
    flow.className = 'memory-flow';

    // Mélanger les couleurs des deux régions
    const fromColor = fromRegion.style.backgroundColor;
    const toColor = toRegion.style.backgroundColor;
    flow.style.backgroundColor = fromColor;

    // Positionner l'élément au début de la connexion
    const fromRect = fromRegion.getBoundingClientRect();
    const brainRect = brainVisualization.getBoundingClientRect();

    const x = (fromRect.left + fromRect.width / 2 - brainRect.left) / brainRect.width * 100;
    const y = (fromRect.top + fromRect.height / 2 - brainRect.top) / brainRect.height * 100;

    flow.style.left = `${x}%`;
    flow.style.top = `${y}%`;

    // Ajouter l'élément au DOM
    brainVisualization.appendChild(flow);

    // Animer le déplacement vers la région cible
    const toRect = toRegion.getBoundingClientRect();
    const toX = (toRect.left + toRect.width / 2 - brainRect.left) / brainRect.width * 100;
    const toY = (toRect.top + toRect.height / 2 - brainRect.top) / brainRect.height * 100;

    // Animer le déplacement
    flow.style.transition = 'left 2s linear, top 2s linear, background-color 2s linear';

    // Déclencher l'animation au prochain cycle
    setTimeout(() => {
      flow.style.left = `${toX}%`;
      flow.style.top = `${toY}%`;
      flow.style.backgroundColor = toColor;
    }, 10);

    // Supprimer l'élément après l'animation
    setTimeout(() => {
      if (flow.parentNode) {
        flow.parentNode.removeChild(flow);
      }
    }, 2100);
  }

  /**
   * Met à jour l'affichage de l'accélérateur Kyber
   * @param {Object} kyber - État de l'accélérateur Kyber
   */
  function updateKyberDisplay(kyber) {
    if (!kyber) return;

    // Mettre à jour les valeurs affichées
    boostFactorElement.textContent = `${kyber.boostFactor || 1.5}x`;
    boostFactorBarElement.style.width = `${((kyber.boostFactor || 1.5) - 1.0) * 100}%`;

    kyberTemperatureElement.textContent = kyber.temperature || 0.6;
    kyberTemperatureBarElement.style.width = `${(kyber.temperature || 0.6) * 100}%`;

    stabilityElement.textContent = kyber.stability || 0.9;
    stabilityBarElement.style.width = `${(kyber.stability || 0.9) * 100}%`;

    // Mettre à jour les sliders
    if (boostFactorSlider) boostFactorSlider.value = kyber.boostFactor || 1.5;
    if (temperatureSlider) temperatureSlider.value = kyber.temperature || 0.6;
    if (stabilitySlider) stabilitySlider.value = kyber.stability || 0.9;

    // Mettre à jour la case à cocher d'activation
    if (kyberEnabledCheckbox) kyberEnabledCheckbox.checked = kyber.enabled !== false;

    // Mettre à jour le bouton de verrouillage
    if (lockKyberBtn) {
      if (kyber.locked) {
        lockKyberBtn.innerHTML = '<i class="bi bi-unlock me-1"></i>Déverrouiller';
        lockKyberBtn.classList.remove('btn-outline-danger');
        lockKyberBtn.classList.add('btn-outline-warning');

        // Désactiver les contrôles
        if (boostFactorSlider) boostFactorSlider.disabled = true;
        if (temperatureSlider) temperatureSlider.disabled = true;
        if (stabilitySlider) stabilitySlider.disabled = true;
        if (kyberEnabledCheckbox) kyberEnabledCheckbox.disabled = true;
        if (saveKyberConfigBtn) saveKyberConfigBtn.disabled = true;
        if (resetKyberConfigBtn) resetKyberConfigBtn.disabled = true;
      } else {
        lockKyberBtn.innerHTML = '<i class="bi bi-lock me-1"></i>Verrouiller';
        lockKyberBtn.classList.remove('btn-outline-warning');
        lockKyberBtn.classList.add('btn-outline-danger');

        // Activer les contrôles
        if (boostFactorSlider) boostFactorSlider.disabled = false;
        if (temperatureSlider) temperatureSlider.disabled = false;
        if (stabilitySlider) stabilitySlider.disabled = false;
        if (kyberEnabledCheckbox) kyberEnabledCheckbox.disabled = false;
        if (saveKyberConfigBtn) saveKyberConfigBtn.disabled = false;
        if (resetKyberConfigBtn) resetKyberConfigBtn.disabled = false;
      }
    }
  }

  /**
   * Charge les données de mémoire
   */
  async function loadMemoryData() {
    try {
      const response = await fetch('/api/memory/data');
      const data = await response.json();

      if (data.success) {
        memoryData = data.memory;
        updateMemoryDisplay();
      } else {
        showError('Erreur lors du chargement des données de mémoire');
      }
    } catch (error) {
      console.error('Error loading memory data:', error);
      showError('Erreur lors du chargement des données de mémoire');
    }
  }

  /**
   * Met à jour l'affichage de la mémoire
   */
  function updateMemoryDisplay() {
    if (!memoryData) return;

    // Mettre à jour les statistiques
    updateStats(memoryData.stats);

    // Mettre à jour les niveaux de mémoire
    updateMemoryLevel(instantMemoryItems, memoryData.instantMemory, instantCountElement);
    updateMemoryLevel(shortTermItems, memoryData.shortTerm, shortTermCountElement);
    updateMemoryLevel(workingMemoryItems, memoryData.workingMemory, workingMemoryCountElement);
    updateMemoryLevel(mediumTermItems, memoryData.mediumTerm, mediumTermCountElement);
    updateMemoryLevel(longTermItems, memoryData.longTerm, longTermCountElement);
    updateMemoryLevel(dreamMemoryItems, memoryData.dreamMemory, dreamMemoryCountElement);
  }

  /**
   * Met à jour les statistiques
   * @param {Object} stats - Statistiques de la mémoire
   */
  function updateStats(stats) {
    if (!stats) return;

    // Mettre à jour les compteurs
    totalEntriesElement.textContent = stats.totalEntries;
    totalEntriesBarElement.style.width = `${Math.min(100, stats.totalEntries / 10)}%`;

    averageTemperatureElement.textContent = stats.averageTemperature.toFixed(2);
    averageTemperatureBarElement.style.width = `${stats.averageTemperature * 100}%`;

    cyclesPerformedElement.textContent = stats.cyclesPerformed;

    if (stats.lastCycleTime) {
      lastCycleTimeElement.textContent = formatDate(stats.lastCycleTime);
    } else {
      lastCycleTimeElement.textContent = '-';
    }
  }

  /**
   * Met à jour un niveau de mémoire
   * @param {HTMLElement} container - Conteneur HTML
   * @param {Object} items - Éléments de mémoire
   * @param {HTMLElement} countElement - Élément de comptage
   */
  function updateMemoryLevel(container, items, countElement) {
    if (!container || !items) return;

    // Mettre à jour le compteur
    const count = Object.keys(items).length;
    countElement.textContent = count;

    // Si aucun élément, afficher un message
    if (count === 0) {
      container.innerHTML = `
        <div class="text-center p-5 text-muted">
          <i class="bi bi-hourglass fs-1"></i>
          <div class="mt-3">Aucune entrée dans ce niveau de mémoire</div>
        </div>
      `;
      return;
    }

    // Générer le HTML pour chaque élément
    let html = '';

    Object.entries(items).forEach(([id, item]) => {
      // Déterminer la couleur en fonction de la température
      let colorClass = 'bg-primary';
      if (item.temperature > 0.8) colorClass = 'bg-danger';
      else if (item.temperature > 0.6) colorClass = 'bg-warning';
      else if (item.temperature > 0.4) colorClass = 'bg-info';
      else if (item.temperature > 0.2) colorClass = 'bg-success';

      html += `
        <div class="col-md-6 col-lg-4 mb-4">
          <div class="card bg-dark border-secondary memory-card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
              <span class="badge ${colorClass}">${item.temperature.toFixed(2)}</span>
              <small class="text-muted">${formatDate(item.timestamp)}</small>
            </div>
            <div class="card-body">
              <h6 class="card-title">${truncateText(item.key, 50)}</h6>
              <p class="card-text small">${truncateText(formatData(item.data), 100)}</p>
            </div>
            <div class="card-footer bg-dark border-secondary">
              <button class="btn btn-outline-primary btn-sm w-100 memory-item" data-id="${id}">
                <i class="bi bi-eye me-1"></i>Voir les détails
              </button>
            </div>
          </div>
        </div>
      `;
    });

    container.innerHTML = html;

    // Ajouter les écouteurs d'événements pour les éléments de mémoire
    document.querySelectorAll('.memory-item').forEach(item => {
      item.addEventListener('click', () => {
        const id = item.getAttribute('data-id');
        showMemoryDetail(id);
      });
    });
  }

  /**
   * Affiche les détails d'un élément de mémoire
   * @param {string} id - ID de l'élément de mémoire
   */
  function showMemoryDetail(id) {
    // Trouver l'élément dans tous les niveaux de mémoire
    const item = findMemoryItem(id);

    if (!item) {
      alert('Élément de mémoire non trouvé');
      return;
    }

    // Mettre à jour le modal
    memoryDetailKey.textContent = item.key;
    memoryDetailData.textContent = formatData(item.data);
    memoryDetailTemperature.textContent = item.temperature.toFixed(2);
    memoryDetailCategory.textContent = item.category || 'Non spécifié';
    memoryDetailSource.textContent = item.source || 'Non spécifié';
    memoryDetailTimestamp.textContent = formatDate(item.timestamp);
    memoryDetailLastAccessed.textContent = formatDate(item.lastAccessed);
    memoryDetailAccessCount.textContent = item.accessCount || 0;

    // Stocker l'ID de l'élément courant
    currentMemoryItemId = id;

    // Afficher le modal
    memoryDetailModal.show();
  }

  /**
   * Trouve un élément de mémoire dans tous les niveaux
   * @param {string} id - ID de l'élément de mémoire
   * @returns {Object|null} - Élément de mémoire ou null si non trouvé
   */
  function findMemoryItem(id) {
    if (!memoryData) return null;

    return memoryData.instantMemory?.[id] ||
           memoryData.shortTerm?.[id] ||
           memoryData.workingMemory?.[id] ||
           memoryData.mediumTerm?.[id] ||
           memoryData.longTerm?.[id] ||
           memoryData.dreamMemory?.[id] ||
           null;
  }

  /**
   * Recherche dans la mémoire
   */
  async function searchMemory() {
    const query = memorySearchInput.value.trim();

    if (!query) {
      alert('Veuillez entrer un terme de recherche');
      return;
    }

    try {
      const response = await fetch(`/api/memory/search?query=${encodeURIComponent(query)}`);
      const data = await response.json();

      if (data.success) {
        displaySearchResults(data.results);
      } else {
        alert('Erreur lors de la recherche dans la mémoire');
      }
    } catch (error) {
      console.error('Error searching memory:', error);
      alert('Erreur lors de la recherche dans la mémoire');
    }
  }

  /**
   * Affiche les résultats de recherche
   * @param {Array} results - Résultats de la recherche
   */
  function displaySearchResults(results) {
    memorySearchResults.classList.remove('d-none');

    if (!results || results.length === 0) {
      searchResultsList.innerHTML = `
        <div class="text-center p-3 text-muted">
          <i class="bi bi-search fs-4"></i>
          <div class="mt-2">Aucun résultat trouvé</div>
        </div>
      `;
      return;
    }

    let html = '';

    results.forEach(item => {
      html += `
        <button class="list-group-item list-group-item-action bg-dark text-light border-secondary memory-item" data-id="${item.id}">
          <div class="d-flex justify-content-between align-items-start">
            <div>
              <div class="fw-bold">${truncateText(item.key, 50)}</div>
              <div class="small">${truncateText(formatData(item.data), 100)}</div>
            </div>
            <span class="badge bg-primary">${item.temperature.toFixed(2)}</span>
          </div>
        </button>
      `;
    });

    searchResultsList.innerHTML = html;

    // Ajouter les écouteurs d'événements pour les résultats
    searchResultsList.querySelectorAll('.memory-item').forEach(item => {
      item.addEventListener('click', () => {
        const id = item.getAttribute('data-id');
        showMemoryDetail(id);
      });
    });
  }

  /**
   * Force un cycle de mémoire
   */
  async function forceCycle() {
    try {
      const response = await fetch('/api/memory/cycle', { method: 'POST' });
      const data = await response.json();

      if (data.success) {
        alert('Cycle de mémoire exécuté avec succès');
        loadMemoryData();
      } else {
        alert('Erreur lors de l\'exécution du cycle de mémoire');
      }
    } catch (error) {
      console.error('Error forcing memory cycle:', error);
      alert('Erreur lors de l\'exécution du cycle de mémoire');
    }
  }

  /**
   * Génère un rêve
   */
  async function generateDream() {
    try {
      const response = await fetch('/api/memory/dream', { method: 'POST' });
      const data = await response.json();

      if (data.success) {
        alert('Rêve généré avec succès');
        loadMemoryData();
      } else {
        alert('Erreur lors de la génération du rêve');
      }
    } catch (error) {
      console.error('Error generating dream:', error);
      alert('Erreur lors de la génération du rêve');
    }
  }

  /**
   * Efface toute la mémoire
   */
  async function clearMemory() {
    try {
      const response = await fetch('/api/memory/clear', { method: 'POST' });
      const data = await response.json();

      if (data.success) {
        alert('Mémoire effacée avec succès');
        loadMemoryData();
      } else {
        alert('Erreur lors de l\'effacement de la mémoire');
      }
    } catch (error) {
      console.error('Error clearing memory:', error);
      alert('Erreur lors de l\'effacement de la mémoire');
    }
  }

  /**
   * Supprime un élément de mémoire
   * @param {string} id - ID de l'élément à supprimer
   */
  async function deleteMemoryItem(id) {
    try {
      const response = await fetch(`/api/memory/item/${id}`, { method: 'DELETE' });
      const data = await response.json();

      if (data.success) {
        alert('Élément de mémoire supprimé avec succès');
        loadMemoryData();
      } else {
        alert('Erreur lors de la suppression de l\'élément de mémoire');
      }
    } catch (error) {
      console.error('Error deleting memory item:', error);
      alert('Erreur lors de la suppression de l\'élément de mémoire');
    }
  }

  /**
   * Exporte la mémoire
   */
  function exportMemory() {
    if (!memoryData) {
      alert('Aucune donnée de mémoire à exporter');
      return;
    }

    // Créer un blob et un lien de téléchargement
    const blob = new Blob([JSON.stringify(memoryData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `thermal-memory-export-${new Date().toISOString().slice(0, 10)}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  /**
   * Active l'accélérateur Kyber
   */
  async function enableKyberAccelerator() {
    try {
      const response = await fetch('/api/memory/kyber/enable', { method: 'POST' });
      const data = await response.json();

      if (data.success) {
        console.log('Accélérateur Kyber activé');
        loadKyberState();
      } else {
        alert('Erreur lors de l\'activation de l\'accélérateur Kyber');
      }
    } catch (error) {
      console.error('Error enabling Kyber accelerator:', error);
      alert('Erreur lors de l\'activation de l\'accélérateur Kyber');
    }
  }

  /**
   * Désactive l'accélérateur Kyber
   */
  async function disableKyberAccelerator() {
    try {
      const response = await fetch('/api/memory/kyber/disable', { method: 'POST' });
      const data = await response.json();

      if (data.success) {
        console.log('Accélérateur Kyber désactivé');
        loadKyberState();
      } else {
        alert('Erreur lors de la désactivation de l\'accélérateur Kyber');
      }
    } catch (error) {
      console.error('Error disabling Kyber accelerator:', error);
      alert('Erreur lors de la désactivation de l\'accélérateur Kyber');
    }
  }

  /**
   * Sauvegarde la configuration de l'accélérateur Kyber
   */
  async function saveKyberConfig() {
    try {
      const config = {
        boostFactor: parseFloat(boostFactorSlider.value),
        temperature: parseFloat(temperatureSlider.value),
        stability: parseFloat(stabilitySlider.value),
        enabled: kyberEnabledCheckbox.checked
      };

      const response = await fetch('/api/memory/kyber/configure', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
      });

      const data = await response.json();

      if (data.success) {
        alert('Configuration de l\'accélérateur Kyber sauvegardée');
        updateKyberDisplay(data.kyberState);
      } else {
        alert('Erreur lors de la sauvegarde de la configuration de l\'accélérateur Kyber');
      }
    } catch (error) {
      console.error('Error saving Kyber config:', error);
      alert('Erreur lors de la sauvegarde de la configuration de l\'accélérateur Kyber');
    }
  }

  /**
   * Réinitialise la configuration de l'accélérateur Kyber
   */
  function resetKyberConfig() {
    boostFactorSlider.value = 1.5;
    temperatureSlider.value = 0.6;
    stabilitySlider.value = 0.9;
    kyberEnabledCheckbox.checked = true;

    boostFactorElement.textContent = '1.5x';
    boostFactorBarElement.style.width = '50%';

    kyberTemperatureElement.textContent = '0.6';
    kyberTemperatureBarElement.style.width = '60%';

    stabilityElement.textContent = '0.9';
    stabilityBarElement.style.width = '90%';
  }

  /**
   * Verrouille ou déverrouille l'accélérateur Kyber
   * @param {boolean} locked - État de verrouillage
   */
  async function lockKyberAccelerator(locked) {
    try {
      const response = await fetch('/api/memory/kyber/lock', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ locked })
      });

      const data = await response.json();

      if (data.success) {
        console.log(`Accélérateur Kyber ${locked ? 'verrouillé' : 'déverrouillé'}`);
        updateKyberDisplay(data.kyberState);
      } else {
        alert('Erreur lors du verrouillage/déverrouillage de l\'accélérateur Kyber');
      }
    } catch (error) {
      console.error('Error locking/unlocking Kyber accelerator:', error);
      alert('Erreur lors du verrouillage/déverrouillage de l\'accélérateur Kyber');
    }
  }

  /**
   * Charge l'état de l'accélérateur Kyber
   */
  async function loadKyberState() {
    try {
      const response = await fetch('/api/memory/kyber');
      const data = await response.json();

      if (data.success) {
        updateKyberDisplay(data.kyberState);
      } else {
        console.error('Error loading Kyber state:', data.error);
      }
    } catch (error) {
      console.error('Error loading Kyber state:', error);
    }
  }

  /**
   * Affiche une erreur
   * @param {string} message - Message d'erreur
   */
  function showError(message) {
    alert(message);
  }

  /**
   * Formate une date
   * @param {number|string} timestamp - Timestamp ou chaîne de date
   * @returns {string} - Date formatée
   */
  function formatDate(timestamp) {
    if (!timestamp) return '-';

    const date = new Date(timestamp);
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }

  /**
   * Formate des données pour l'affichage
   * @param {any} data - Données à formater
   * @returns {string} - Données formatées
   */
  function formatData(data) {
    if (!data) return 'Aucune donnée';

    if (typeof data === 'object') {
      if (data.question && data.answer) {
        return `Q: ${data.question}\nR: ${data.answer}`;
      } else if (data.content) {
        return data.content;
      } else {
        return JSON.stringify(data);
      }
    }

    return String(data);
  }

  /**
   * Tronque un texte
   * @param {string} text - Texte à tronquer
   * @param {number} maxLength - Longueur maximale
   * @returns {string} - Texte tronqué
   */
  function truncateText(text, maxLength) {
    if (!text) return '';

    if (text.length <= maxLength) {
      return text;
    }

    return text.substring(0, maxLength) + '...';
  }
});
