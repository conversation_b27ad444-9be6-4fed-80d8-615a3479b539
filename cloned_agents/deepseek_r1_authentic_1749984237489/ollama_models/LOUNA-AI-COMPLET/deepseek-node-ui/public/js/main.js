document.addEventListener('DOMContentLoaded', () => {
  // Éléments DOM
  const chatForm = document.getElementById('chat-form');
  const messageInput = document.getElementById('message-input');
  const chatMessages = document.getElementById('chat-messages');
  const apiKeyInput = document.getElementById('api-key');
  const toggleApiKeyBtn = document.getElementById('toggle-api-key');
  const saveApiKeyBtn = document.getElementById('save-api-key');
  const testApiKeyBtn = document.getElementById('test-api-key');
  const apiKeyStatus = document.getElementById('api-key-status');
  const temperatureSlider = document.getElementById('temperature');
  const temperatureValue = document.getElementById('temperature-value');
  const maxTokensSlider = document.getElementById('max-tokens');
  const maxTokensValue = document.getElementById('max-tokens-value');
  const newChatBtn = document.getElementById('new-chat-btn');
  const clearChatBtn = document.getElementById('clear-chat-btn');
  const exportChatBtn = document.getElementById('export-chat-btn');
  const conversationsList = document.getElementById('conversations-list');
  const currentChatTitle = document.getElementById('current-chat-title');

  // Variables globales
  let socket;
  let currentConversationId = generateId();
  let conversations = loadConversations();
  let messageHistory = [];
  let isWaitingForResponse = false;
  let serverHasApiKey = false;
  let apiKeyPreview = '';

  // Initialisation
  initializeSocket();
  updateConversationsList();
  setupEventListeners();
  checkApiKeyStatus();

  // Fonctions
  function initializeSocket() {
    socket = io();

    socket.on('connect', () => {
      console.log('Connected to server');
    });

    socket.on('chat response', (data) => {
      isWaitingForResponse = false;
      removeTypingIndicator();

      if (data.error) {
        displayErrorMessage(data.error);
        return;
      }

      if (data.choices && data.choices.length > 0) {
        const botMessage = data.choices[0].message;
        messageHistory.push(botMessage);
        displayMessage(botMessage.content, 'bot');
        saveConversation();
      }
    });

    socket.on('connect_error', (error) => {
      console.error('Connection error:', error);
      displayErrorMessage('Erreur de connexion au serveur');
    });
  }

  // Fonction pour sauvegarder la clé API
  async function saveApiKey() {
    const apiKey = apiKeyInput.value.trim();

    if (!apiKey) {
      displayErrorMessage('Veuillez entrer une clé API valide');
      return;
    }

    // Afficher un indicateur de chargement
    apiKeyStatus.innerHTML = `
      <div class="alert alert-info">
        <div class="d-flex align-items-center">
          <div class="spinner-border spinner-border-sm me-2" role="status"></div>
          <div>Sauvegarde de la clé API en cours...</div>
        </div>
      </div>
    `;

    try {
      const response = await fetch('/api/save-api-key', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ apiKey })
      });

      const data = await response.json();

      if (response.ok && data.success) {
        serverHasApiKey = true;
        apiKeyPreview = data.apiKeyPreview;

        apiKeyStatus.innerHTML = `
          <div class="alert alert-success">
            <i class="bi bi-check-circle me-2"></i>
            Clé API sauvegardée avec succès: ${apiKeyPreview}
          </div>
        `;

        // Mettre à jour le message de bienvenue
        updateWelcomeMessage(true);
      } else {
        apiKeyStatus.innerHTML = `
          <div class="alert alert-danger">
            <i class="bi bi-exclamation-triangle me-2"></i>
            Erreur: ${data.error || 'Échec de la sauvegarde de la clé API'}
          </div>
        `;
      }
    } catch (error) {
      console.error('Error saving API key:', error);
      apiKeyStatus.innerHTML = `
        <div class="alert alert-danger">
          <i class="bi bi-exclamation-triangle me-2"></i>
          Erreur: ${error.message || 'Échec de la sauvegarde de la clé API'}
        </div>
      `;
    }
  }

  // Fonction pour tester la clé API
  async function testApiKey() {
    apiKeyStatus.innerHTML = `
      <div class="alert alert-info">
        <div class="d-flex align-items-center">
          <div class="spinner-border spinner-border-sm me-2" role="status"></div>
          <div>Test de la clé API en cours...</div>
        </div>
      </div>
    `;

    try {
      const response = await fetch('/api/test-api-key');
      const data = await response.json();

      if (response.ok && data.success) {
        apiKeyStatus.innerHTML = `
          <div class="alert alert-success">
            <i class="bi bi-check-circle me-2"></i>
            La clé API est valide!
            ${data.models && data.models.data ? `<div class="mt-2"><small>Modèles disponibles: ${data.models.data.map(m => m.id).join(', ')}</small></div>` : ''}
          </div>
        `;
      } else {
        apiKeyStatus.innerHTML = `
          <div class="alert alert-danger">
            <i class="bi bi-exclamation-triangle me-2"></i>
            Erreur: ${data.error || 'Échec du test de la clé API'}
            ${data.details ? `<div class="mt-2"><small>${JSON.stringify(data.details)}</small></div>` : ''}
          </div>
        `;
      }
    } catch (error) {
      console.error('Error testing API key:', error);
      apiKeyStatus.innerHTML = `
        <div class="alert alert-danger">
          <i class="bi bi-exclamation-triangle me-2"></i>
          Erreur: ${error.message || 'Échec du test de la clé API'}
        </div>
      `;
    }
  }

  // Vérifier le statut de la clé API
  async function checkApiKeyStatus() {
    try {
      const response = await fetch('/api/config');
      const data = await response.json();

      if (response.ok) {
        serverHasApiKey = data.hasApiKey;
        apiKeyPreview = data.apiKeyPreview;

        if (serverHasApiKey) {
          apiKeyStatus.innerHTML = `
            <div class="alert alert-success">
              <i class="bi bi-check-circle me-2"></i>
              Clé API configurée: ${apiKeyPreview}
            </div>
          `;

          // Mettre à jour le message de bienvenue
          updateWelcomeMessage(true);
        } else {
          apiKeyStatus.innerHTML = `
            <div class="alert alert-warning">
              <i class="bi bi-exclamation-triangle me-2"></i>
              Aucune clé API configurée. Veuillez sauvegarder votre clé.
            </div>
          `;
        }
      }
    } catch (error) {
      console.error('Error checking API key status:', error);
    }
  }

  // Mettre à jour le message de bienvenue
  function updateWelcomeMessage(hasApiKey) {
    const welcomeMessage = document.querySelector('.welcome-message');
    if (welcomeMessage) {
      const alertElement = welcomeMessage.querySelector('.alert');
      if (alertElement) {
        if (hasApiKey) {
          alertElement.className = 'alert alert-success';
          alertElement.innerHTML = '<i class="bi bi-check-circle me-2"></i>Clé API configurée. Vous pouvez commencer à discuter!';
        } else {
          alertElement.className = 'alert alert-warning';
          alertElement.innerHTML = '<i class="bi bi-exclamation-triangle me-2"></i>Vous devez configurer votre clé API DeepSeek pour utiliser cette interface.';
        }
      }
    }
  }

  function setupEventListeners() {
    // Formulaire de chat
    chatForm.addEventListener('submit', (e) => {
      e.preventDefault();
      sendMessage();
    });

    // Afficher/masquer la clé API
    toggleApiKeyBtn.addEventListener('click', () => {
      if (apiKeyInput.type === 'password') {
        apiKeyInput.type = 'text';
        toggleApiKeyBtn.innerHTML = '<i class="bi bi-eye-slash"></i>';
      } else {
        apiKeyInput.type = 'password';
        toggleApiKeyBtn.innerHTML = '<i class="bi bi-eye"></i>';
      }
    });

    // Gérer la sauvegarde de la clé API
    saveApiKeyBtn.addEventListener('click', () => {
      saveApiKey();
    });

    // Gérer le test de la clé API
    testApiKeyBtn.addEventListener('click', () => {
      testApiKey();
    });

    // Sliders pour les paramètres
    temperatureSlider.addEventListener('input', () => {
      temperatureValue.textContent = temperatureSlider.value;
    });

    maxTokensSlider.addEventListener('input', () => {
      maxTokensValue.textContent = maxTokensSlider.value;
    });

    // Boutons d'action
    newChatBtn.addEventListener('click', startNewConversation);
    clearChatBtn.addEventListener('click', clearCurrentConversation);
    exportChatBtn.addEventListener('click', exportConversation);
  }

  function sendMessage() {
    const message = messageInput.value.trim();
    if (!message || isWaitingForResponse) return;

    // Vérifier si la clé API est configurée sur le serveur
    if (!serverHasApiKey) {
      displayErrorMessage('Veuillez sauvegarder votre clé API DeepSeek sur le serveur');
      return;
    }

    // Afficher le message de l'utilisateur
    displayMessage(message, 'user');

    // Ajouter le message à l'historique
    const userMessage = { role: 'user', content: message };
    messageHistory.push(userMessage);

    // Effacer l'input et afficher l'indicateur de frappe
    messageInput.value = '';
    displayTypingIndicator();
    isWaitingForResponse = true;

    // Envoyer le message au serveur
    socket.emit('chat message', {
      message,
      history: messageHistory,
      temperature: parseFloat(temperatureSlider.value),
      maxTokens: parseInt(maxTokensSlider.value)
    });

    // Sauvegarder la conversation
    saveConversation();
  }

  function displayMessage(content, sender) {
    // Supprimer le message de bienvenue s'il est présent
    const welcomeMessage = document.querySelector('.welcome-message');
    if (welcomeMessage) {
      welcomeMessage.remove();
    }

    const messageDiv = document.createElement('div');
    messageDiv.className = `message message-${sender} mb-3`;

    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';

    if (sender === 'bot') {
      // Utiliser marked.js pour convertir le markdown en HTML
      contentDiv.innerHTML = marked.parse(content);
      contentDiv.classList.add('markdown-content');

      // Appliquer highlight.js aux blocs de code
      contentDiv.querySelectorAll('pre code').forEach((block) => {
        hljs.highlightElement(block);
      });
    } else {
      contentDiv.textContent = content;
    }

    const timeDiv = document.createElement('div');
    timeDiv.className = 'message-time';
    timeDiv.textContent = new Date().toLocaleTimeString();

    messageDiv.appendChild(contentDiv);
    messageDiv.appendChild(timeDiv);
    chatMessages.appendChild(messageDiv);

    // Faire défiler vers le bas
    chatMessages.scrollTop = chatMessages.scrollHeight;
  }

  function displayErrorMessage(error) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'alert alert-danger mt-3';
    errorDiv.innerHTML = `<i class="bi bi-exclamation-triangle me-2"></i>${error}`;
    chatMessages.appendChild(errorDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
  }

  function displayTypingIndicator() {
    const indicatorDiv = document.createElement('div');
    indicatorDiv.className = 'typing-indicator';
    indicatorDiv.innerHTML = '<span></span><span></span><span></span>';
    chatMessages.appendChild(indicatorDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
  }

  function removeTypingIndicator() {
    const indicator = document.querySelector('.typing-indicator');
    if (indicator) {
      indicator.remove();
    }
  }

  function startNewConversation() {
    // Sauvegarder la conversation actuelle si elle contient des messages
    if (messageHistory.length > 0) {
      saveConversation();
    }

    // Réinitialiser
    currentConversationId = generateId();
    messageHistory = [];
    chatMessages.innerHTML = `
      <div class="welcome-message text-center my-5">
        <i class="bi bi-robot display-1 mb-3"></i>
        <h2>Nouvelle conversation</h2>
        <p class="lead">Commencez à discuter avec l'agent DeepSeek r1 en entrant votre message ci-dessous.</p>
      </div>
    `;
    currentChatTitle.textContent = 'Nouvelle conversation';

    // Mettre à jour la liste des conversations
    updateConversationsList();
  }

  function clearCurrentConversation() {
    if (confirm('Êtes-vous sûr de vouloir effacer cette conversation ?')) {
      startNewConversation();
    }
  }

  function exportConversation() {
    if (messageHistory.length === 0) {
      alert('Aucun message à exporter');
      return;
    }

    // Formater la conversation
    let conversationText = '# Conversation DeepSeek r1\n\n';
    messageHistory.forEach(msg => {
      const role = msg.role === 'user' ? 'Vous' : 'DeepSeek r1';
      conversationText += `## ${role}\n\n${msg.content}\n\n`;
    });

    // Créer un blob et un lien de téléchargement
    const blob = new Blob([conversationText], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `deepseek-conversation-${new Date().toISOString().slice(0, 10)}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  function saveConversation() {
    if (messageHistory.length === 0) return;

    // Générer un titre pour la conversation
    let title = 'Nouvelle conversation';
    if (messageHistory.length > 0 && messageHistory[0].content) {
      title = messageHistory[0].content.substring(0, 30) + (messageHistory[0].content.length > 30 ? '...' : '');
    }

    // Mettre à jour la conversation actuelle
    conversations[currentConversationId] = {
      id: currentConversationId,
      title,
      messages: messageHistory,
      lastUpdated: new Date().toISOString()
    };

    // Sauvegarder dans le localStorage
    localStorage.setItem('deepseek_conversations', JSON.stringify(conversations));

    // Mettre à jour la liste des conversations
    updateConversationsList();

    // Mettre à jour le titre de la conversation actuelle
    currentChatTitle.textContent = title;
  }

  function loadConversation(id) {
    if (!conversations[id]) return;

    currentConversationId = id;
    messageHistory = conversations[id].messages;
    currentChatTitle.textContent = conversations[id].title;

    // Afficher les messages
    chatMessages.innerHTML = '';
    messageHistory.forEach(msg => {
      displayMessage(msg.content, msg.role === 'user' ? 'user' : 'bot');
    });

    // Mettre à jour la liste des conversations
    updateConversationsList();
  }

  function updateConversationsList() {
    conversationsList.innerHTML = '';

    // Trier les conversations par date de mise à jour (la plus récente en premier)
    const sortedConversations = Object.values(conversations).sort((a, b) => {
      return new Date(b.lastUpdated) - new Date(a.lastUpdated);
    });

    sortedConversations.forEach(conv => {
      const li = document.createElement('li');
      li.className = `list-group-item ${conv.id === currentConversationId ? 'active' : ''}`;
      li.textContent = conv.title;
      li.addEventListener('click', () => loadConversation(conv.id));
      conversationsList.appendChild(li);
    });
  }

  function loadConversations() {
    const saved = localStorage.getItem('deepseek_conversations');
    return saved ? JSON.parse(saved) : {};
  }

  // Cette fonction n'est plus nécessaire car nous utilisons checkApiKeyStatus() à la place

  function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }
});
