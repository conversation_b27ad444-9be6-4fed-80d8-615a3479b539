/**
 * Service de gestion des accélérateurs Kyber pour la réflexion
 * Ce service permet d'optimiser les performances de réflexion en utilisant
 * les accélérateurs Kyber spécifiques à la réflexion.
 */

class ReflectionAcceleratorsService {
  constructor() {
    this.socket = io();
    this.accelerators = [];
    this.stats = {
      efficiency: 0,
      throughput: 0,
      temperature: 0,
      load: 0
    };
    this.isInitialized = false;
    this.callbacks = {
      onUpdate: [],
      onOptimize: [],
      onError: []
    };

    this.init();
  }

  /**
   * Initialise le service d'accélérateurs
   */
  init() {
    if (this.isInitialized) return;

    // Configurer les écouteurs d'événements Socket.IO
    this.setupSocketListeners();

    // Demander les données initiales
    this.requestAcceleratorsData();

    // Configurer la mise à jour périodique
    setInterval(() => {
      this.requestAcceleratorsData();
    }, 10000); // Mise à jour toutes les 10 secondes

    this.isInitialized = true;
    console.log('Service d\'accélérateurs de réflexion initialisé');
  }

  /**
   * Configure les écouteurs d'événements Socket.IO
   */
  setupSocketListeners() {
    // Réception des données des accélérateurs
    this.socket.on('reflection accelerators data', (data) => {
      if (data.success) {
        this.accelerators = data.accelerators || [];
        this.stats = data.stats || this.stats;
        this.notifyUpdate();
      }
    });

    // Réception du résultat de l'optimisation
    this.socket.on('reflection accelerators optimized', (data) => {
      if (data.success) {
        this.accelerators = data.accelerators || this.accelerators;
        this.stats = data.stats || this.stats;
        this.notifyOptimize(data);
      }
    });

    // Gestion des erreurs
    this.socket.on('reflection accelerators error', (error) => {
      console.error('Erreur des accélérateurs de réflexion:', error);
      this.notifyError(error);
    });
  }

  /**
   * Demande les données des accélérateurs au serveur
   */
  requestAcceleratorsData() {
    this.socket.emit('get reflection accelerators');
  }

  /**
   * Optimise les accélérateurs de réflexion
   * @returns {Promise} Une promesse résolue lorsque l'optimisation est terminée
   */
  optimizeAccelerators() {
    return new Promise((resolve, reject) => {
      // Envoyer la demande d'optimisation
      this.socket.emit('optimize reflection accelerators');

      // Configurer un écouteur temporaire pour la réponse
      const onOptimize = (data) => {
        resolve(data);
      };

      const onError = (error) => {
        reject(error);
      };

      // Ajouter les écouteurs temporaires
      this.callbacks.onOptimize.push(onOptimize);
      this.callbacks.onError.push(onError);

      // Configurer un timeout pour éviter de bloquer indéfiniment
      setTimeout(() => {
        // Supprimer les écouteurs temporaires
        this.callbacks.onOptimize = this.callbacks.onOptimize.filter(cb => cb !== onOptimize);
        this.callbacks.onError = this.callbacks.onError.filter(cb => cb !== onError);

        // Rejeter la promesse si aucune réponse n'a été reçue
        reject(new Error('Timeout lors de l\'optimisation des accélérateurs'));
      }, 10000); // 10 secondes de timeout
    });
  }

  /**
   * Calcule l'accélération actuelle basée sur l'efficacité des accélérateurs
   * @returns {number} Le facteur d'accélération (1.0 = pas d'accélération)
   */
  calculateAccelerationFactor() {
    if (!this.accelerators || this.accelerators.length === 0) {
      return 3.0; // Accélération de base améliorée même sans accélérateurs
    }

    // Calculer l'efficacité moyenne des accélérateurs actifs
    const activeAccelerators = this.accelerators.filter(acc => acc.active);
    if (activeAccelerators.length === 0) return 3.0; // Accélération de base améliorée

    // Vérifier si les accélérateurs sont en mode série
    const isSerialMode = activeAccelerators.some(acc => acc.serialMode);

    if (isSerialMode) {
      // Pour les accélérateurs en série, l'effet est multiplicatif
      // Trier par position dans la chaîne
      const sortedAccelerators = [...activeAccelerators].sort((a, b) =>
        (a.serialPosition || 0) - (b.serialPosition || 0));

      // Calculer l'efficacité en série
      let serialFactor = 3.0; // Facteur de base amélioré

      // Chaque accélérateur dans la chaîne contribue de manière multiplicative
      for (let i = 0; i < sortedAccelerators.length; i++) {
        const acc = sortedAccelerators[i];
        // L'effet est plus fort pour les premiers accélérateurs dans la chaîne
        const positionFactor = Math.pow(0.9, i); // 1, 0.9, 0.81, 0.729, ...

        // Ajouter la contribution de cet accélérateur
        serialFactor *= (1 + (acc.efficiency * positionFactor * 0.5));
      }

      // Appliquer un multiplicateur supplémentaire basé sur la longueur de la chaîne
      const chainMultiplier = 1 + (Math.log2(sortedAccelerators.length) / 1.0);

      // Limiter le facteur d'accélération à une plage raisonnable (3.0 - 20.0)
      return Math.max(3.0, Math.min(20.0, serialFactor * chainMultiplier));
    } else {
      // Pour les accélérateurs en parallèle, utiliser la méthode standard
      // L'efficacité augmente avec le nombre d'accélérateurs en cascade
      const baseEfficiency = activeAccelerators.reduce((sum, acc) => sum + acc.efficiency, 0) / activeAccelerators.length;

      // Effet de cascade: plus d'accélérateurs = effet multiplicateur (amélioré)
      const cascadeMultiplier = 1 + (Math.log2(activeAccelerators.length + 1) / 1.5);

      // Convertir l'efficacité en facteur d'accélération (3.0 - 12.0) - plage augmentée
      return Math.max(3.0, Math.min(12.0, baseEfficiency * cascadeMultiplier * 12));
    }
  }

  /**
   * Simule le temps de réflexion avec accélération
   * @param {number} baseTime - Le temps de base sans accélération (en secondes)
   * @param {number} complexity - La complexité de la réflexion (1-10, optionnel)
   * @returns {Object} Objet contenant le temps accéléré et le facteur d'accélération
   */
  simulateReflectionTime(baseTime, complexity = 5) {
    // Obtenir le facteur d'accélération actuel
    const accelerationFactor = this.calculateAccelerationFactor();

    // Réduire drastiquement le temps de base pour les questions simples
    let adjustedBaseTime = baseTime;
    if (complexity <= 3) {
      // Pour les questions très simples, réduire considérablement le temps de base
      adjustedBaseTime = baseTime * 0.3;
    } else if (complexity <= 5) {
      // Pour les questions de complexité moyenne, réduire modérément
      adjustedBaseTime = baseTime * 0.5;
    } else if (complexity <= 7) {
      // Pour les questions assez complexes, réduire légèrement
      adjustedBaseTime = baseTime * 0.7;
    }

    // Appliquer l'accélération avec un effet plus fort
    const acceleratedTime = adjustedBaseTime / (accelerationFactor * 1.5);

    // Garantir un temps minimum (0.2 seconde pour les questions simples, 0.5 pour les complexes)
    const minTime = complexity <= 3 ? 0.2 : 0.5;
    const finalTime = Math.max(minTime, acceleratedTime);

    return {
      baseTime,
      adjustedBaseTime,
      acceleratedTime: finalTime,
      accelerationFactor: accelerationFactor * 1.5, // Facteur d'accélération amélioré
      complexity
    };
  }

  /**
   * S'abonne aux mises à jour des accélérateurs
   * @param {Function} callback - Fonction appelée lors des mises à jour
   */
  onUpdate(callback) {
    if (typeof callback === 'function') {
      this.callbacks.onUpdate.push(callback);
    }
  }

  /**
   * Notifie tous les abonnés d'une mise à jour
   */
  notifyUpdate() {
    this.callbacks.onUpdate.forEach(callback => {
      try {
        callback({
          accelerators: this.accelerators,
          stats: this.stats
        });
      } catch (error) {
        console.error('Erreur lors de la notification de mise à jour:', error);
      }
    });
  }

  /**
   * Notifie tous les abonnés d'une optimisation
   * @param {Object} data - Données d'optimisation
   */
  notifyOptimize(data) {
    this.callbacks.onOptimize.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error('Erreur lors de la notification d\'optimisation:', error);
      }
    });
  }

  /**
   * Notifie tous les abonnés d'une erreur
   * @param {Object} error - Erreur survenue
   */
  notifyError(error) {
    this.callbacks.onError.forEach(callback => {
      try {
        callback(error);
      } catch (err) {
        console.error('Erreur lors de la notification d\'erreur:', err);
      }
    });
  }
}

// Créer une instance unique du service
const reflectionAcceleratorsService = new ReflectionAcceleratorsService();

// Exporter le service
window.reflectionAcceleratorsService = reflectionAcceleratorsService;
