/**
 * Luna Training - Gestion de la formation de l'IA
 */

document.addEventListener('DOMContentLoaded', function() {
  // Initialiser les variables
  let trainingInProgress = false;
  let currentSessionId = null;
  let socket = io();
  let autoTrainingEnabled = false;
  let autoTrainingInterval = null;

  // Éléments DOM
  const startTrainingBtn = document.getElementById('startTrainingBtn');
  const stopTrainingBtn = document.getElementById('stopTrainingBtn');
  const trainingStatus = document.getElementById('trainingStatus');
  const trainingProgress = document.getElementById('trainingProgress');
  const trainingProgressBar = document.getElementById('trainingProgressBar');
  const sessionsContainer = document.getElementById('sessionsContainer');
  const diceElement = document.getElementById('dice');
  const launchTrainingButton = document.getElementById('launchTrainingButton');
  const autoTrainingButton = document.getElementById('autoTrainingButton');
  const internetAccessToggle = document.getElementById('internetAccessToggle');

  // Éléments pour les statistiques
  const iqValue = document.getElementById('iqValue');
  const neuronsValue = document.getElementById('neuronsValue');
  const learningSpeedValue = document.getElementById('learningSpeedValue');
  const evolutionTimeValue = document.getElementById('evolutionTimeValue');

  // Éléments pour les barres de progression
  const evolutionProgressBar = document.getElementById('evolutionProgressBar');
  const evolutionProgressValue = document.getElementById('evolutionProgressValue');
  const learningProgressBar = document.getElementById('learningProgressBar');
  const learningProgressValue = document.getElementById('learningProgressValue');
  const iqProgressBar = document.getElementById('iqProgressBar');
  const iqProgressValue = document.getElementById('iqProgressValue');

  // Charger les statistiques de l'IA au chargement de la page
  loadAiStats();

  // Événements de clic sur les boutons
  if (startTrainingBtn) {
    startTrainingBtn.addEventListener('click', startTraining);
  }

  if (stopTrainingBtn) {
    stopTrainingBtn.addEventListener('click', stopTraining);
  }

  if (launchTrainingButton) {
    launchTrainingButton.addEventListener('click', startTraining);
  }

  if (autoTrainingButton) {
    autoTrainingButton.addEventListener('click', toggleAutoTraining);
  }

  const refreshButton = document.getElementById('refreshButton');
  if (refreshButton) {
    refreshButton.addEventListener('click', function() {
      loadAiStats();
      loadTrainingSessions();
    });
  }

  // Fonction pour charger les statistiques de l'IA
  function loadAiStats() {
    fetch('/api/ai/stats')
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          updateStatsDisplay(data.stats);
        } else {
          console.error('Erreur lors du chargement des statistiques:', data.error);
        }
      })
      .catch(error => {
        console.error('Erreur lors de la requête de statistiques:', error);
      });
  }

  // Fonction pour mettre à jour l'affichage des statistiques
  function updateStatsDisplay(stats) {
    // Mettre à jour les valeurs des statistiques
    if (iqValue) iqValue.textContent = Math.round(stats.iq);
    if (neuronsValue) neuronsValue.textContent = stats.neurons.toLocaleString();
    if (learningSpeedValue) learningSpeedValue.textContent = `${stats.learningSpeed}%`;
    if (evolutionTimeValue) evolutionTimeValue.textContent = `${stats.evolutionTime}h`;

    // Mettre à jour les barres de progression
    if (evolutionProgressBar) {
      evolutionProgressBar.style.width = `${stats.levelProgress}%`;
      evolutionProgressValue.textContent = `${stats.levelProgress}%`;
    }

    if (learningProgressBar) {
      learningProgressBar.style.width = `${stats.learningSpeed}%`;
      learningProgressValue.textContent = `${stats.learningSpeed}%`;
    }

    if (iqProgressBar) {
      // Calculer le pourcentage de QI (base 100 = 0%, 200 = 100%)
      const iqPercentage = Math.min(100, Math.max(0, (stats.iq - 100) / 100 * 100));
      iqProgressBar.style.width = `${iqPercentage}%`;
      iqProgressValue.textContent = `${Math.round(iqPercentage)}%`;
    }

    // Mettre à jour l'historique des évolutions si disponible
    if (stats.history && stats.history.length > 0) {
      const historyContainer = document.getElementById('evolutionHistory');
      if (historyContainer) {
        historyContainer.innerHTML = '';

        stats.history.slice(0, 5).forEach(entry => {
          const historyItem = document.createElement('div');
          historyItem.className = 'history-item';
          historyItem.innerHTML = `
            <span class="history-date">${formatDate(entry.date)}</span>
            <span class="history-sessions">${entry.sessions} session(s)</span>
            <span class="history-gain">+${entry.iqGain.toFixed(1)} QI</span>
          `;
          historyContainer.appendChild(historyItem);
        });
      }
    }
  }

  // Fonction pour démarrer une session de formation
  function startTraining() {
    if (trainingInProgress) return;

    // Nombre de questions pour la session (entre 3 et 10)
    const questionCount = Math.floor(Math.random() * 8) + 3;

    // Vérifier si l'accès Internet est activé
    const useInternet = internetAccessToggle ? internetAccessToggle.checked : true;

    // Animer le dé
    if (diceElement) {
      rollDice(questionCount);
    }

    // Mettre à jour l'interface
    trainingInProgress = true;
    currentSessionId = generateSessionId();

    if (startTrainingBtn) startTrainingBtn.disabled = true;
    if (stopTrainingBtn) stopTrainingBtn.disabled = false;
    if (launchTrainingButton) launchTrainingButton.disabled = true;
    if (autoTrainingButton) autoTrainingButton.textContent = 'Arrêter Auto-Formation';

    // Afficher le conteneur de progression
    const progressContainer = document.getElementById('trainingProgressContainer');
    if (progressContainer) {
      progressContainer.style.display = 'block';
    }

    // Mettre à jour les informations de progression
    const trainingStage = document.getElementById('trainingStage');
    const internetBadge = document.getElementById('internetBadge');
    const trainingProgressValue = document.getElementById('trainingProgressValue');

    if (trainingStage) trainingStage.textContent = 'Préparation...';
    if (internetBadge) internetBadge.style.display = useInternet ? 'inline-block' : 'none';
    if (trainingProgressValue) trainingProgressValue.textContent = '0%';
    if (trainingProgressBar) trainingProgressBar.style.width = '0%';

    // Appeler l'API pour démarrer la formation
    fetch('/api/training', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        questionCount: questionCount,
        useInternet: useInternet
      }),
    })
    .then(response => response.json())
    .then(data => {
      if (!data.success) {
        console.error('Erreur lors du démarrage de la formation:', data.error);
        stopTraining();
      }
    })
    .catch(error => {
      console.error('Erreur lors de la requête de formation:', error);
      stopTraining();
    });
  }

  // Fonction pour activer/désactiver l'auto-formation
  function toggleAutoTraining() {
    if (autoTrainingEnabled) {
      // Désactiver l'auto-formation
      autoTrainingEnabled = false;
      if (autoTrainingButton) {
        autoTrainingButton.textContent = 'Auto-Formation';
        autoTrainingButton.style.backgroundColor = '#38a169';
      }

      // Arrêter l'intervalle
      if (autoTrainingInterval) {
        clearInterval(autoTrainingInterval);
        autoTrainingInterval = null;
      }

      // Arrêter la formation en cours si nécessaire
      if (trainingInProgress) {
        stopTraining();
      }
    } else {
      // Activer l'auto-formation
      autoTrainingEnabled = true;
      if (autoTrainingButton) {
        autoTrainingButton.textContent = 'Arrêter Auto-Formation';
        autoTrainingButton.style.backgroundColor = '#e53e3e';
      }

      // Démarrer la première session
      if (!trainingInProgress) {
        startTraining();
      }

      // Configurer l'intervalle pour les sessions suivantes
      autoTrainingInterval = setInterval(() => {
        // Vérifier si une formation est en cours
        if (!trainingInProgress) {
          // Démarrer une nouvelle session
          startTraining();
        }
      }, 60000); // Vérifier toutes les minutes
    }
  }

  // Fonction pour arrêter la formation
  function stopTraining() {
    trainingInProgress = false;

    if (startTrainingBtn) startTrainingBtn.disabled = false;
    if (stopTrainingBtn) stopTrainingBtn.disabled = true;
    if (launchTrainingButton) launchTrainingButton.disabled = false;

    // Masquer le conteneur de progression
    const progressContainer = document.getElementById('trainingProgressContainer');
    if (progressContainer) {
      progressContainer.style.display = 'none';
    }

    // Ne pas changer le bouton d'auto-formation si l'auto-formation est activée
    if (!autoTrainingEnabled && autoTrainingButton) {
      autoTrainingButton.textContent = 'Auto-Formation';
      autoTrainingButton.style.backgroundColor = '#38a169';
    }

    // Réinitialiser l'ID de session
    currentSessionId = null;

    // Recharger les statistiques après la formation
    setTimeout(() => {
      loadAiStats();
      loadTrainingSessions();
    }, 1000);
  }

  // Fonction pour charger les sessions de formation
  function loadTrainingSessions() {
    fetch('/api/training/sessions')
      .then(response => response.json())
      .then(data => {
        if (data.success && data.sessions) {
          updateSessionsDisplay(data.sessions);
        } else {
          console.error('Erreur lors du chargement des sessions:', data.error);
        }
      })
      .catch(error => {
        console.error('Erreur lors de la requête de sessions:', error);
      });
  }

  // Fonction pour mettre à jour l'affichage des sessions
  function updateSessionsDisplay(sessions) {
    if (!sessionsContainer) return;

    // Vider le conteneur
    sessionsContainer.innerHTML = '';

    if (sessions.length === 0) {
      sessionsContainer.innerHTML = '<div class="no-sessions">Aucune session de formation disponible</div>';
      return;
    }

    // Ajouter les sessions
    sessions.forEach(session => {
      addSessionToHistory(session);
    });
  }

  // Fonction pour simuler la progression de la formation
  function simulateTrainingProgress(questionCount) {
    let progress = 0;
    const interval = setInterval(() => {
      if (!trainingInProgress) {
        clearInterval(interval);
        return;
      }

      progress += 100 / (questionCount * 5); // 5 étapes par question

      if (progress >= 100) {
        progress = 100;
        clearInterval(interval);

        // Terminer la formation après un court délai
        setTimeout(() => {
          if (trainingInProgress) {
            stopTraining();
          }
        }, 1000);
      }

      // Mettre à jour la barre de progression
      if (trainingProgressBar) {
        trainingProgressBar.style.width = `${progress}%`;
      }
    }, 1000);
  }

  // Fonction pour animer le dé
  function rollDice(result) {
    if (!diceElement) return;

    diceElement.classList.add('rolling');
    diceElement.textContent = '?';

    setTimeout(() => {
      diceElement.classList.remove('rolling');
      diceElement.textContent = result;
    }, 1500);
  }

  // Fonction pour générer un ID de session
  function generateSessionId() {
    return Math.random().toString(36).substring(2, 10);
  }

  // Fonction pour formater une date
  function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  }

  // Écouter les événements socket pour les mises à jour de formation
  socket.on('training progress', (data) => {
    // Mettre à jour la progression
    if (trainingProgressBar && data.progress) {
      trainingProgressBar.style.width = `${data.progress}%`;
    }

    // Mettre à jour les informations de progression
    const trainingStage = document.getElementById('trainingStage');
    const trainingProgressValue = document.getElementById('trainingProgressValue');
    const trainingElapsedTime = document.getElementById('trainingElapsedTime');
    const trainingRemainingTime = document.getElementById('trainingRemainingTime');
    const trainingAcceleratorInfo = document.getElementById('trainingAcceleratorInfo');
    const trainingAcceleratorValue = document.getElementById('trainingAcceleratorValue');
    const internetBadge = document.getElementById('internetBadge');

    if (trainingProgressValue) {
      trainingProgressValue.textContent = `${data.progress}%`;
    }

    if (trainingStage) {
      let stageText = 'Préparation...';

      switch (data.stage) {
        case 'generating_question':
          stageText = 'Génération de question';
          break;
        case 'internet_research':
          stageText = 'Recherche Internet';
          break;
        case 'generating_response':
          stageText = 'Génération de réponse';
          break;
        case 'generating_feedback':
          stageText = 'Génération de feedback';
          break;
        case 'storing_memory':
          stageText = 'Stockage en mémoire';
          break;
      }

      trainingStage.textContent = stageText;
    }

    if (internetBadge && data.internetEnabled) {
      internetBadge.style.display = 'inline-block';
    }

    if (trainingElapsedTime && data.elapsedTime) {
      trainingElapsedTime.textContent = formatTime(data.elapsedTime);
    }

    if (trainingRemainingTime && data.estimatedTimeRemaining) {
      trainingRemainingTime.textContent = formatTime(data.estimatedTimeRemaining);
    }

    if (trainingAcceleratorInfo && trainingAcceleratorValue && data.accelerated) {
      trainingAcceleratorInfo.style.display = 'flex';
      trainingAcceleratorValue.textContent = `x${data.speedMultiplier.toFixed(1)}`;
    }

    // Ajouter une nouvelle session à l'historique si terminée
    if (data.status === 'completed' && data.session) {
      addSessionToHistory(data.session);

      // Marquer la formation comme terminée
      trainingInProgress = false;

      // Si l'auto-formation est activée, ne pas appeler stopTraining()
      // pour éviter de désactiver l'auto-formation
      if (!autoTrainingEnabled) {
        stopTraining();
      } else {
        // Réactiver le bouton de lancement manuel
        if (launchTrainingButton) {
          launchTrainingButton.disabled = false;
        }
      }
    }
  });

  // Écouter l'événement de fin de formation
  socket.on('training complete', (data) => {
    // Recharger les statistiques
    loadAiStats();

    // Si l'auto-formation est activée, ne rien faire de plus
    if (autoTrainingEnabled) {
      return;
    }

    // Sinon, arrêter la formation
    trainingInProgress = false;
    stopTraining();
  });

  // Fonction pour formater le temps en minutes et secondes
  function formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    } else {
      return `${remainingSeconds}s`;
    }
  }

  // Fonction pour ajouter une session à l'historique
  function addSessionToHistory(session) {
    if (!sessionsContainer) return;

    const sessionElement = document.createElement('div');
    sessionElement.className = 'session-item';
    sessionElement.innerHTML = `
      <div class="session-header">
        <span>Session #${session.id.substring(0, 8)}</span>
        <span>${new Date(session.timestamp).toLocaleString()}</span>
      </div>
      <div class="session-question">${session.question}</div>
      <div class="session-response">${session.response}</div>
      <div class="session-feedback">
        <div class="feedback-title">Feedback d'apprentissage:</div>
        ${session.feedback}
      </div>
    `;

    // Ajouter au début de la liste
    if (sessionsContainer.firstChild) {
      sessionsContainer.insertBefore(sessionElement, sessionsContainer.firstChild);
    } else {
      sessionsContainer.appendChild(sessionElement);
    }

    // Supprimer le message "Aucune session"
    const noSessions = document.querySelector('.no-sessions');
    if (noSessions) {
      noSessions.remove();
    }
  }
});
