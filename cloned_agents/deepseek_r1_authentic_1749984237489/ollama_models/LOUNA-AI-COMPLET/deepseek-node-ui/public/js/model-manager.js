/**
 * Gestionnaire de modèles pour l'interface DeepSeek r1
 * 
 * Ce script ajoute des fonctionnalités avancées pour gérer les modèles Ollama :
 * - Barre de progression pour le téléchargement des modèles
 * - Affichage de la taille des modèles en Go
 * - Gestion des modèles existants (suppression)
 */

// Tailles des modèles DeepSeek r1 en Go
const MODEL_SIZES = {
  'deepseek-r1:1.5b': 1.1,
  'deepseek-r1:7b': 4.7,
  'deepseek-r1:8b': 4.9,
  'deepseek-r1:14b': 9.0,
  'deepseek-r1:32b': 20.0,
  'deepseek-r1:70b': 43.0,
  'deepseek-r1:671b': 404.0
};

// État global du téléchargement
let downloadState = {
  isDownloading: false,
  modelName: '',
  progress: 0,
  totalSize: 0,
  downloadedSize: 0,
  speed: 0,
  remainingTime: 0
};

// Éléments DOM
let progressBarContainer;
let progressBar;
let progressText;
let modelsList;
let deleteModelButtons;

// Initialisation du gestionnaire de modèles
function initModelManager() {
  // Créer les éléments DOM pour la barre de progression
  createProgressBar();
  
  // Ajouter un bouton pour gérer les modèles
  addManageModelsButton();
  
  // Intercepter les téléchargements de modèles
  interceptModelDownloads();
}

// Créer la barre de progression
function createProgressBar() {
  // Créer le conteneur de la barre de progression
  progressBarContainer = document.createElement('div');
  progressBarContainer.className = 'progress-container mt-3';
  progressBarContainer.style.display = 'none';
  
  // Créer la barre de progression
  const progressBarWrapper = document.createElement('div');
  progressBarWrapper.className = 'progress';
  progressBarWrapper.style.height = '20px';
  
  progressBar = document.createElement('div');
  progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated';
  progressBar.role = 'progressbar';
  progressBar.style.width = '0%';
  progressBar.setAttribute('aria-valuenow', '0');
  progressBar.setAttribute('aria-valuemin', '0');
  progressBar.setAttribute('aria-valuemax', '100');
  
  progressBarWrapper.appendChild(progressBar);
  progressBarContainer.appendChild(progressBarWrapper);
  
  // Créer le texte de la barre de progression
  progressText = document.createElement('div');
  progressText.className = 'text-center mt-1 small';
  progressText.textContent = 'Téléchargement en cours...';
  progressBarContainer.appendChild(progressText);
  
  // Ajouter la barre de progression au DOM
  const modelsContainer = document.getElementById('models-container');
  if (modelsContainer) {
    modelsContainer.appendChild(progressBarContainer);
  }
}

// Ajouter un bouton pour gérer les modèles
function addManageModelsButton() {
  const modelsContainer = document.getElementById('models-container');
  if (!modelsContainer) return;
  
  // Créer le bouton
  const manageButton = document.createElement('button');
  manageButton.className = 'btn btn-outline-danger w-100 mt-2';
  manageButton.innerHTML = '<i class="bi bi-trash me-2"></i>Gérer les modèles';
  manageButton.onclick = showModelsManager;
  
  // Ajouter le bouton au DOM
  modelsContainer.appendChild(manageButton);
}

// Afficher le gestionnaire de modèles
function showModelsManager() {
  // Créer le modal
  const modalHtml = `
    <div class="modal fade" id="manage-models-modal" tabindex="-1" aria-labelledby="manage-models-modal-label" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content bg-dark text-light">
          <div class="modal-header">
            <h5 class="modal-title" id="manage-models-modal-label">Gérer les modèles</h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <p>Sélectionnez les modèles que vous souhaitez supprimer pour libérer de l'espace disque.</p>
            <div id="models-list" class="list-group mb-3">
              <div class="d-flex justify-content-center">
                <div class="spinner-border text-light" role="status">
                  <span class="visually-hidden">Chargement...</span>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
          </div>
        </div>
      </div>
    </div>
  `;
  
  // Ajouter le modal au DOM
  const modalContainer = document.createElement('div');
  modalContainer.innerHTML = modalHtml;
  document.body.appendChild(modalContainer);
  
  // Afficher le modal
  const modal = new bootstrap.Modal(document.getElementById('manage-models-modal'));
  modal.show();
  
  // Charger la liste des modèles
  loadModelsList();
}

// Charger la liste des modèles
async function loadModelsList() {
  try {
    const response = await fetch('/api/models');
    const data = await response.json();
    
    if (data.success) {
      displayModelsList(data.models || []);
    } else {
      displayModelsError('Erreur lors du chargement des modèles');
    }
  } catch (error) {
    console.error('Error loading models:', error);
    displayModelsError('Erreur lors du chargement des modèles');
  }
}

// Afficher la liste des modèles
function displayModelsList(models) {
  modelsList = document.getElementById('models-list');
  if (!modelsList) return;
  
  if (models.length === 0) {
    modelsList.innerHTML = '<div class="alert alert-info">Aucun modèle installé</div>';
    return;
  }
  
  // Trier les modèles par taille (du plus grand au plus petit)
  models.sort((a, b) => {
    const sizeA = MODEL_SIZES[a.name] || 0;
    const sizeB = MODEL_SIZES[b.name] || 0;
    return sizeB - sizeA;
  });
  
  // Calculer l'espace total utilisé
  const totalSpace = models.reduce((total, model) => {
    return total + (MODEL_SIZES[model.name] || 0);
  }, 0);
  
  // Afficher l'espace total utilisé
  const totalSpaceHtml = `
    <div class="alert alert-info mb-3">
      <div class="d-flex justify-content-between align-items-center">
        <span>Espace total utilisé :</span>
        <span><strong>${totalSpace.toFixed(1)} Go</strong></span>
      </div>
    </div>
  `;
  
  // Générer le HTML pour chaque modèle
  let modelsHtml = totalSpaceHtml;
  
  models.forEach(model => {
    const modelSize = MODEL_SIZES[model.name] || 'Inconnu';
    const sizeText = typeof modelSize === 'number' ? `${modelSize.toFixed(1)} Go` : modelSize;
    
    modelsHtml += `
      <div class="list-group-item bg-dark text-light border-secondary">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <strong>${model.name}</strong>
            <div class="small text-muted">Taille: ${sizeText}</div>
          </div>
          <button class="btn btn-outline-danger btn-sm delete-model" data-model="${model.name}">
            <i class="bi bi-trash"></i>
          </button>
        </div>
      </div>
    `;
  });
  
  modelsList.innerHTML = modelsHtml;
  
  // Ajouter les événements pour les boutons de suppression
  deleteModelButtons = document.querySelectorAll('.delete-model');
  deleteModelButtons.forEach(button => {
    button.addEventListener('click', () => {
      const modelName = button.getAttribute('data-model');
      if (confirm(`Êtes-vous sûr de vouloir supprimer le modèle ${modelName} ?`)) {
        deleteModel(modelName);
      }
    });
  });
}

// Afficher une erreur dans la liste des modèles
function displayModelsError(error) {
  const modelsList = document.getElementById('models-list');
  if (!modelsList) return;
  
  modelsList.innerHTML = `<div class="alert alert-danger">${error}</div>`;
}

// Supprimer un modèle
async function deleteModel(modelName) {
  try {
    // Désactiver le bouton de suppression
    const button = document.querySelector(`.delete-model[data-model="${modelName}"]`);
    if (button) {
      button.disabled = true;
      button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';
    }
    
    // Appeler l'API pour supprimer le modèle
    const response = await fetch('/api/delete-model', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ modelName })
    });
    
    const data = await response.json();
    
    if (data.success) {
      // Recharger la liste des modèles
      loadModelsList();
      
      // Afficher un message de succès
      alert(`Le modèle ${modelName} a été supprimé avec succès`);
    } else {
      alert(`Erreur lors de la suppression du modèle ${modelName}: ${data.error}`);
      
      // Réactiver le bouton
      if (button) {
        button.disabled = false;
        button.innerHTML = '<i class="bi bi-trash"></i>';
      }
    }
  } catch (error) {
    console.error('Error deleting model:', error);
    alert(`Erreur lors de la suppression du modèle ${modelName}`);
    
    // Réactiver le bouton
    const button = document.querySelector(`.delete-model[data-model="${modelName}"]`);
    if (button) {
      button.disabled = false;
      button.innerHTML = '<i class="bi bi-trash"></i>';
    }
  }
}

// Intercepter les téléchargements de modèles
function interceptModelDownloads() {
  // Intercepter les clics sur le bouton de téléchargement
  const downloadButtons = document.querySelectorAll('.list-group-item[data-model]');
  downloadButtons.forEach(button => {
    button.addEventListener('click', () => {
      const modelName = button.getAttribute('data-model');
      const modelSize = MODEL_SIZES[modelName] || 0;
      
      // Mettre à jour l'état du téléchargement
      downloadState = {
        isDownloading: true,
        modelName,
        progress: 0,
        totalSize: modelSize,
        downloadedSize: 0,
        speed: 0,
        remainingTime: 0
      };
      
      // Afficher la barre de progression
      showProgressBar();
      
      // Simuler les mises à jour de la barre de progression
      simulateProgressUpdates();
    });
  });
  
  // Intercepter le bouton de confirmation
  const confirmButton = document.getElementById('confirm-download-btn');
  if (confirmButton) {
    confirmButton.addEventListener('click', () => {
      const customModelInput = document.getElementById('custom-model-name');
      if (customModelInput && customModelInput.value) {
        const modelName = customModelInput.value;
        const modelSize = MODEL_SIZES[modelName] || 5; // Taille par défaut si inconnue
        
        // Mettre à jour l'état du téléchargement
        downloadState = {
          isDownloading: true,
          modelName,
          progress: 0,
          totalSize: modelSize,
          downloadedSize: 0,
          speed: 0,
          remainingTime: 0
        };
        
        // Afficher la barre de progression
        showProgressBar();
        
        // Simuler les mises à jour de la barre de progression
        simulateProgressUpdates();
      }
    });
  }
}

// Afficher la barre de progression
function showProgressBar() {
  if (progressBarContainer) {
    progressBarContainer.style.display = 'block';
  }
}

// Mettre à jour la barre de progression
function updateProgressBar(progress, downloadedSize, totalSize, speed, remainingTime) {
  if (progressBar && progressText) {
    // Mettre à jour la barre de progression
    progressBar.style.width = `${progress}%`;
    progressBar.setAttribute('aria-valuenow', progress);
    
    // Mettre à jour le texte
    const downloadedSizeFormatted = (downloadedSize / 1024).toFixed(1);
    const totalSizeFormatted = (totalSize).toFixed(1);
    const speedFormatted = (speed / 1024).toFixed(1);
    
    progressText.innerHTML = `
      Téléchargement de <strong>${downloadState.modelName}</strong><br>
      ${downloadedSizeFormatted} Go / ${totalSizeFormatted} Go (${progress}%) - ${speedFormatted} Mo/s<br>
      Temps restant: ${remainingTime}s
    `;
  }
}

// Simuler les mises à jour de la barre de progression
function simulateProgressUpdates() {
  // Cette fonction est utilisée uniquement pour la démonstration
  // Dans une implémentation réelle, les mises à jour viendraient du serveur
  
  const updateInterval = setInterval(() => {
    if (!downloadState.isDownloading) {
      clearInterval(updateInterval);
      return;
    }
    
    // Simuler une progression
    downloadState.progress += 1;
    downloadState.downloadedSize = (downloadState.progress / 100) * downloadState.totalSize;
    downloadState.speed = 80 * 1024; // 80 Mo/s
    downloadState.remainingTime = Math.round((downloadState.totalSize - downloadState.downloadedSize) * 1024 / downloadState.speed);
    
    // Mettre à jour la barre de progression
    updateProgressBar(
      downloadState.progress,
      downloadState.downloadedSize,
      downloadState.totalSize,
      downloadState.speed,
      downloadState.remainingTime
    );
    
    // Arrêter la simulation lorsque le téléchargement est terminé
    if (downloadState.progress >= 100) {
      downloadState.isDownloading = false;
      clearInterval(updateInterval);
      
      // Masquer la barre de progression après quelques secondes
      setTimeout(() => {
        if (progressBarContainer) {
          progressBarContainer.style.display = 'none';
        }
      }, 3000);
    }
  }, 500);
}

// Initialiser le gestionnaire de modèles lorsque le DOM est chargé
document.addEventListener('DOMContentLoaded', initModelManager);
