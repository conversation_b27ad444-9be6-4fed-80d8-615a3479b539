/**
 * Luna Reflection Enhanced - Interface de réflexion améliorée pour Vision Ultra
 * Ce fichier gère l'interface utilisateur pour la réflexion de l'agent
 */

// Initialiser la connexion Socket.IO
const socket = io();

// État global de la réflexion
const reflectionState = {
  showReflection: true,
  autoTranslate: true,
  displayStyle: 'collapsed',
  history: [],
  currentReflection: null,
  internetAccess: true,
  thermalMemoryAccess: true,
  acceleratorsEnabled: true,
  currentDateTime: new Date()
};

// Événements Socket.IO
socket.on('connect', () => {
  console.log('🔌 Connecté au serveur Luna');
  loadReflectionSettings();
  loadReflectionHistory();
  updateDateTime();
});

socket.on('disconnect', () => {
  console.log('🔌 Déconnecté du serveur Luna');
  showToast('Connexion au serveur perdue. Tentative de reconnexion...', 'error');
});

// Recevoir les paramètres de réflexion
socket.on('reflection settings', (data) => {
  if (data.success) {
    reflectionState.showReflection = data.showReflection;
    reflectionState.autoTranslate = data.autoTranslate;
    reflectionState.displayStyle = data.displayStyle;
    
    // Mettre à jour l'interface
    updateSettingsUI();
  } else {
    showToast('Erreur lors de la récupération des paramètres de réflexion', 'error');
  }
});

// Recevoir l'historique de réflexion
socket.on('reflection history', (data) => {
  if (data.success) {
    reflectionState.history = data.history;
    
    // Mettre à jour l'interface
    updateHistoryUI();
  } else {
    showToast('Erreur lors de la récupération de l\'historique de réflexion', 'error');
  }
});

// Recevoir le résultat de la réflexion
socket.on('reflection result', (data) => {
  if (data.success) {
    reflectionState.currentReflection = {
      reflection: data.reflection,
      details: data.details,
      time: data.time,
      acceleration: data.acceleration,
      timestamp: Date.now()
    };
    
    // Mettre à jour l'interface
    displayReflection(reflectionState.currentReflection);
    
    // Ajouter à l'historique local
    reflectionState.history.unshift(reflectionState.currentReflection);
    
    // Mettre à jour l'interface de l'historique
    updateHistoryUI();
  } else {
    showToast('Erreur lors de la génération de la réflexion', 'error');
  }
});

// Recevoir la date et l'heure actuelles
socket.on('current datetime', (data) => {
  if (data.success) {
    reflectionState.currentDateTime = new Date(data.datetime.iso);
    
    // Mettre à jour l'interface
    updateDateTimeUI();
  }
});

// Fonctions principales

// Charger les paramètres de réflexion
function loadReflectionSettings() {
  socket.emit('get reflection settings');
}

// Charger l'historique de réflexion
function loadReflectionHistory() {
  socket.emit('get reflection history');
}

// Mettre à jour la date et l'heure
function updateDateTime() {
  socket.emit('get current datetime');
  
  // Mettre à jour toutes les secondes
  setInterval(() => {
    socket.emit('get current datetime');
  }, 1000);
}

// Mettre à jour l'interface des paramètres
function updateSettingsUI() {
  $('#show-reflection-toggle').prop('checked', reflectionState.showReflection);
  $('#auto-translate-toggle').prop('checked', reflectionState.autoTranslate);
  
  // Mettre à jour le style d'affichage
  $('.display-style-option').removeClass('active');
  $(`#display-style-${reflectionState.displayStyle}`).addClass('active');
}

// Mettre à jour l'interface de l'historique
function updateHistoryUI() {
  const historyContainer = $('#reflection-history');
  historyContainer.empty();
  
  if (reflectionState.history.length === 0) {
    historyContainer.append('<div class="text-center text-muted">Aucune réflexion dans l\'historique</div>');
    return;
  }
  
  reflectionState.history.forEach((item, index) => {
    const timestamp = new Date(item.timestamp).toLocaleString('fr-FR');
    const card = $(`
      <div class="card mb-2">
        <div class="card-header d-flex justify-content-between align-items-center">
          <span>Réflexion #${index + 1}</span>
          <small class="text-muted">${timestamp}</small>
        </div>
        <div class="card-body">
          <div class="reflection-content">${item.reflection}</div>
          <div class="mt-2">
            <small class="text-muted">Temps: ${item.time.toFixed(2)}s (x${item.acceleration.toFixed(1)})</small>
          </div>
        </div>
        <div class="card-footer">
          <button class="btn btn-sm btn-luna view-details-btn" data-index="${index}">
            <i class="bi bi-info-circle"></i> Détails
          </button>
          <button class="btn btn-sm btn-luna-outline replay-btn" data-index="${index}">
            <i class="bi bi-arrow-repeat"></i> Rejouer
          </button>
        </div>
      </div>
    `);
    
    historyContainer.append(card);
  });
  
  // Ajouter les gestionnaires d'événements
  $('.view-details-btn').on('click', function() {
    const index = $(this).data('index');
    showReflectionDetails(reflectionState.history[index]);
  });
  
  $('.replay-btn').on('click', function() {
    const index = $(this).data('index');
    displayReflection(reflectionState.history[index]);
  });
}

// Mettre à jour l'interface de la date et l'heure
function updateDateTimeUI() {
  const dateTimeString = reflectionState.currentDateTime.toLocaleString('fr-FR');
  $('#current-datetime').text(dateTimeString);
}

// Afficher la réflexion
function displayReflection(reflection) {
  const reflectionContainer = $('#reflection-container');
  
  // Afficher la réflexion
  reflectionContainer.html(`
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <span>Réflexion</span>
        <div>
          <button class="btn btn-sm btn-luna-outline" id="toggle-details-btn">
            <i class="bi bi-info-circle"></i> Détails
          </button>
        </div>
      </div>
      <div class="card-body">
        <div class="reflection-content">${reflection.reflection}</div>
        <div class="reflection-details mt-3" style="display: none;">
          ${reflection.details}
        </div>
        <div class="mt-3">
          <div class="progress" style="height: 5px;">
            <div class="progress-bar" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
          </div>
          <div class="d-flex justify-content-between mt-1">
            <small class="text-muted">Temps: ${reflection.time.toFixed(2)}s</small>
            <small class="text-muted">Accélération: x${reflection.acceleration.toFixed(1)}</small>
          </div>
        </div>
      </div>
    </div>
  `);
  
  // Ajouter le gestionnaire d'événement pour afficher/masquer les détails
  $('#toggle-details-btn').on('click', function() {
    $('.reflection-details').toggle();
  });
}

// Afficher les détails de la réflexion
function showReflectionDetails(reflection) {
  const modal = $('#reflection-details-modal');
  
  // Mettre à jour le contenu du modal
  modal.find('.modal-body').html(reflection.details);
  
  // Afficher le modal
  modal.modal('show');
}

// Générer une réflexion
function generateReflection(input) {
  if (!input) {
    showToast('Veuillez entrer une requête', 'warning');
    return;
  }
  
  // Afficher un indicateur de chargement
  $('#reflection-container').html(`
    <div class="text-center p-4">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Chargement...</span>
      </div>
      <p class="mt-2">Génération de la réflexion en cours...</p>
    </div>
  `);
  
  // Émettre l'événement pour générer la réflexion
  socket.emit('start reflection', { input });
}

// Effacer l'historique de réflexion
function clearReflectionHistory() {
  // Demander confirmation
  if (!confirm('Êtes-vous sûr de vouloir effacer tout l\'historique de réflexion ?')) {
    return;
  }
  
  socket.emit('clear reflection history');
  
  // Vider l'historique local
  reflectionState.history = [];
  
  // Mettre à jour l'interface
  updateHistoryUI();
  
  showToast('Historique de réflexion effacé', 'success');
}

// Sauvegarder les paramètres de réflexion
function saveReflectionSettings() {
  const settings = {
    showReflection: $('#show-reflection-toggle').is(':checked'),
    autoTranslate: $('#auto-translate-toggle').is(':checked'),
    displayStyle: $('.display-style-option.active').data('style')
  };
  
  socket.emit('save reflection settings', settings);
  
  // Mettre à jour l'état local
  reflectionState.showReflection = settings.showReflection;
  reflectionState.autoTranslate = settings.autoTranslate;
  reflectionState.displayStyle = settings.displayStyle;
  
  showToast('Paramètres de réflexion sauvegardés', 'success');
}

// Afficher un toast
function showToast(message, type = 'info') {
  const toast = $(`
    <div class="toast" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="3000">
      <div class="toast-header">
        <strong class="me-auto">Vision Ultra</strong>
        <small>À l'instant</small>
        <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Fermer"></button>
      </div>
      <div class="toast-body ${type === 'error' ? 'text-danger' : type === 'warning' ? 'text-warning' : type === 'success' ? 'text-success' : ''}">
        ${message}
      </div>
    </div>
  `);
  
  $('.toast-container').append(toast);
  const bsToast = new bootstrap.Toast(toast);
  bsToast.show();
  
  // Supprimer le toast après qu'il soit caché
  toast.on('hidden.bs.toast', function() {
    $(this).remove();
  });
}

// Initialisation au chargement de la page
$(document).ready(function() {
  // Gestionnaire pour le formulaire de réflexion
  $('#reflection-form').on('submit', function(e) {
    e.preventDefault();
    const input = $('#reflection-input').val().trim();
    generateReflection(input);
  });
  
  // Gestionnaire pour le bouton d'effacement de l'historique
  $('#clear-history-btn').on('click', function() {
    clearReflectionHistory();
  });
  
  // Gestionnaire pour le bouton de sauvegarde des paramètres
  $('#save-settings-btn').on('click', function() {
    saveReflectionSettings();
  });
  
  // Gestionnaire pour les options de style d'affichage
  $('.display-style-option').on('click', function() {
    $('.display-style-option').removeClass('active');
    $(this).addClass('active');
  });
  
  // Initialiser les tooltips
  $('[data-bs-toggle="tooltip"]').tooltip();
});
