/**
 * Gestionnaire de chat pour l'interface DeepSeek r1
 * Ce script gère l'envoi et la réception de messages
 */

// Attendre que le DOM soit chargé
document.addEventListener('DOMContentLoaded', () => {
  console.log('Chat handler initialized');

  // Éléments DOM
  const chatForm = document.getElementById('chat-form');
  const messageInput = document.getElementById('message-input');
  const sendButton = document.getElementById('send-button');
  const chatMessages = document.getElementById('chat-messages');

  // Variables globales
  let socket = null;
  let isWaitingForResponse = false;
  let messageHistory = [];

  // Initialiser Socket.io
  initSocket();

  // Configurer les écouteurs d'événements
  setupEventListeners();

  /**
   * Initialise la connexion Socket.io
   */
  function initSocket() {
    try {
      console.log('Initializing socket connection...');
      socket = io({
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
        timeout: 10000
      });

      socket.on('connect', () => {
        console.log('Socket connected successfully');
      });

      socket.on('chat response', handleChatResponse);

      socket.on('connect_error', (error) => {
        console.error('Socket connection error:', error);
        displayErrorMessage('Erreur de connexion au serveur: ' + error.message);
      });

      socket.on('disconnect', (reason) => {
        console.log('Socket disconnected:', reason);
      });
    } catch (error) {
      console.error('Error initializing socket:', error);
    }
  }

  /**
   * Configure les écouteurs d'événements
   */
  function setupEventListeners() {
    // Formulaire de chat
    if (chatForm) {
      chatForm.addEventListener('submit', (e) => {
        e.preventDefault();
        sendChatMessage();
      });
    }

    // Champ de message
    if (messageInput) {
      messageInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          sendChatMessage();
        }
      });
    }

    // Bouton d'envoi
    if (sendButton) {
      sendButton.addEventListener('click', () => {
        sendChatMessage();
      });
    }
  }

  /**
   * Envoie un message de chat
   */
  function sendChatMessage() {
    try {
      // Récupérer le message
      const message = messageInput.value.trim();

      // Vérifier si le message est vide ou si on attend déjà une réponse
      if (!message || isWaitingForResponse) {
        console.log('Message vide ou en attente de réponse');
        return;
      }

      console.log('Sending message:', message);

      // Récupérer les paramètres du modèle
      const modelSelector = document.getElementById('model-selector');
      const temperatureSlider = document.getElementById('temperature');
      const maxTokensSlider = document.getElementById('max-tokens');
      const useMemoryCheckbox = document.getElementById('use-memory');

      const modelName = modelSelector ? modelSelector.value : 'deepseek-r1:7b';
      const temperature = temperatureSlider ? parseFloat(temperatureSlider.value) : 0.7;
      const maxTokens = maxTokensSlider ? parseInt(maxTokensSlider.value) : 1000;
      const useMemory = useMemoryCheckbox ? useMemoryCheckbox.checked : true;

      // Afficher le message de l'utilisateur
      displayMessage(message, 'user');

      // Ajouter le message à l'historique
      const userMessage = { role: 'user', content: message };

      // Limiter l'historique à 10 messages pour éviter les problèmes de mémoire
      if (messageHistory.length > 10) {
        messageHistory = messageHistory.slice(messageHistory.length - 10);
      }

      messageHistory.push(userMessage);

      // Effacer l'input et afficher l'indicateur de frappe
      messageInput.value = '';
      messageInput.style.height = 'auto';
      displayTypingIndicator();
      isWaitingForResponse = true;

      // Faire défiler vers le bas
      chatMessages.scrollTop = chatMessages.scrollHeight;

      // Envoyer le message au serveur
      if (socket && socket.connected) {
        console.log('Emitting chat message event');

        // Créer une copie de l'historique pour éviter les problèmes de référence
        const historyCopy = JSON.parse(JSON.stringify(messageHistory));

        // Limiter l'historique envoyé à 5 messages pour éviter les problèmes de taille
        const limitedHistory = historyCopy.slice(-5);

        socket.emit('chat message', {
          message,
          history: limitedHistory,
          modelName,
          temperature,
          maxTokens,
          useMemory
        });

        // Définir un délai de 30 secondes pour la réponse
        setTimeout(() => {
          if (isWaitingForResponse) {
            isWaitingForResponse = false;
            removeTypingIndicator();
            displayErrorMessage('Le serveur met trop de temps à répondre. Veuillez réessayer.');
          }
        }, 30000);
      } else {
        console.error('Socket not connected');
        displayErrorMessage('Erreur: Socket non connecté. Veuillez rafraîchir la page.');
        isWaitingForResponse = false;
        removeTypingIndicator();

        // Tenter de reconnecter le socket
        if (socket) {
          socket.connect();
        } else {
          // Si le socket n'existe pas, initialiser une nouvelle connexion
          initSocket();
        }
      }
    } catch (error) {
      console.error('Error sending message:', error);
      displayErrorMessage('Erreur lors de l\'envoi du message: ' + error.message);
      isWaitingForResponse = false;
      removeTypingIndicator();
    }
  }

  /**
   * Gère la réponse du chat
   * @param {Object} data - Données de réponse
   */
  function handleChatResponse(data) {
    console.log('Received chat response:', data);
    isWaitingForResponse = false;
    removeTypingIndicator();

    if (data.error) {
      displayErrorMessage(data.error);
      return;
    }

    let content = '';

    // Vérifier si la réponse contient un message directement ou dans un objet message
    if (data.message && data.message.content) {
      // Format standard
      content = data.message.content;
    } else if (data.content) {
      // Format alternatif
      content = data.content;
    } else if (typeof data === 'string') {
      // Format texte simple
      content = data;
    } else if (data.response) {
      // Format Ollama generate
      content = data.response;
    } else {
      // Essayer de trouver un message dans l'objet
      console.log('Format de réponse non reconnu, recherche de contenu...');

      // Parcourir l'objet pour trouver un contenu
      for (const key in data) {
        if (typeof data[key] === 'object' && data[key] !== null) {
          if (data[key].content) {
            content = data[key].content;
            break;
          } else if (data[key].text) {
            content = data[key].text;
            break;
          } else if (data[key].response) {
            content = data[key].response;
            break;
          }
        } else if (typeof data[key] === 'string') {
          if (key.includes('content') || key.includes('text') || key.includes('response')) {
            content = data[key];
            break;
          }
        }
      }

      if (!content) {
        // Dernier recours : convertir l'objet en chaîne JSON
        try {
          content = 'Réponse reçue mais format non reconnu. Voici les données brutes :\n\n' + JSON.stringify(data, null, 2);
        } catch (e) {
          content = 'Réponse reçue mais impossible de l\'afficher.';
        }
      }
    }

    // Nettoyer le contenu
    content = content.trim();

    // Supprimer les balises <think> si présentes
    content = content.replace(/<think>[\s\S]*?<\/think>/g, '');

    // Ajouter le message à l'historique
    const botMessage = { role: 'assistant', content };
    messageHistory.push(botMessage);

    // Afficher le message
    displayMessage(content, 'bot');

    // Sauvegarder la conversation si la fonction existe
    if (typeof saveConversation === 'function') {
      saveConversation();
    }

    // Faire défiler vers le bas
    chatMessages.scrollTop = chatMessages.scrollHeight;
  }

  /**
   * Affiche un message dans la zone de chat
   * @param {string} content - Contenu du message
   * @param {string} sender - Expéditeur du message ('user' ou 'bot')
   */
  function displayMessage(content, sender) {
    if (!chatMessages) return;

    // Supprimer le message de bienvenue s'il est présent
    const welcomeMessage = document.querySelector('.welcome-message');
    if (welcomeMessage) {
      welcomeMessage.remove();
    }

    const messageDiv = document.createElement('div');
    messageDiv.className = `message message-${sender} mb-3`;

    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';

    if (sender === 'bot') {
      // Utiliser marked.js pour convertir le markdown en HTML
      contentDiv.innerHTML = marked.parse(content);
      contentDiv.classList.add('markdown-content');

      // Appliquer highlight.js aux blocs de code
      contentDiv.querySelectorAll('pre code').forEach((block) => {
        hljs.highlightElement(block);
      });
    } else {
      contentDiv.textContent = content;
    }

    const timeDiv = document.createElement('div');
    timeDiv.className = 'message-time';
    timeDiv.textContent = new Date().toLocaleTimeString();

    messageDiv.appendChild(contentDiv);
    messageDiv.appendChild(timeDiv);
    chatMessages.appendChild(messageDiv);

    // Faire défiler vers le bas
    chatMessages.scrollTop = chatMessages.scrollHeight;
  }

  /**
   * Affiche un message d'erreur
   * @param {string} error - Message d'erreur
   */
  function displayErrorMessage(error) {
    if (!chatMessages) return;

    const errorDiv = document.createElement('div');
    errorDiv.className = 'alert alert-danger mt-3';
    errorDiv.innerHTML = `<i class="bi bi-exclamation-triangle me-2"></i>${error}`;
    chatMessages.appendChild(errorDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
  }

  /**
   * Affiche l'indicateur de frappe
   */
  function displayTypingIndicator() {
    if (!chatMessages) return;

    const indicatorDiv = document.createElement('div');
    indicatorDiv.className = 'typing-indicator';
    indicatorDiv.innerHTML = '<span></span><span></span><span></span>';
    chatMessages.appendChild(indicatorDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
  }

  /**
   * Supprime l'indicateur de frappe
   */
  function removeTypingIndicator() {
    const indicator = document.querySelector('.typing-indicator');
    if (indicator) {
      indicator.remove();
    }
  }

  // Exposer les fonctions globalement
  window.sendChatMessage = sendChatMessage;
});
