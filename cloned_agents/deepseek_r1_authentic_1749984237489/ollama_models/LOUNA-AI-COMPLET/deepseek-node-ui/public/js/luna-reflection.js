/**
 * Luna Reflection - Gestion de la réflexion pour l'interface Luna
 * Permet de configurer et visualiser le processus de réflexion du modèle
 */

/**
 * Met à jour la date et l'heure dans l'interface
 */
function updateDateTime() {
  const now = new Date();

  // Mettre à jour l'élément current-datetime
  const datetimeElement = document.getElementById('current-datetime');
  if (datetimeElement) {
    datetimeElement.textContent = now.toLocaleString('fr-FR');
  }
}

// Initialiser la connexion Socket.IO
const socket = io();

// État global de la réflexion
const reflectionState = {
  showReflection: true,
  autoTranslate: true,
  displayStyle: 'collapsed',
  accelerators: [
    { id: 1, active: true, efficiency: 0.9 },
    { id: 2, active: true, efficiency: 0.85 },
    { id: 3, active: true, efficiency: 0.95 }
  ],
  stats: {
    avgReflectionTime: 1.2,
    reflectionRatio: 2.4,
    translationEfficiency: 0.92
  }
};

// Événements Socket.IO
socket.on('connect', () => {
  console.log('🔌 Connecté au serveur Luna');
  loadReflectionSettings();
});

socket.on('disconnect', () => {
  console.log('🔌 Déconnecté du serveur Luna');
  showNotification('Connexion au serveur perdue. Tentative de reconnexion...', 'error');
});

// Recevoir les paramètres de réflexion
socket.on('reflection settings', (data) => {
  if (data.success) {
    reflectionState.showReflection = data.showReflection;
    reflectionState.autoTranslate = data.autoTranslate;
    reflectionState.displayStyle = data.displayStyle;

    // Mettre à jour l'interface
    updateReflectionUI();
  }
});

// Recevoir les statistiques des accélérateurs
socket.on('accelerators status', (data) => {
  if (data.success && data.accelerators && data.accelerators.reflection) {
    const reflectionAccelerators = data.accelerators.reflection;

    // Mettre à jour l'état des accélérateurs
    for (let i = 0; i < Math.min(reflectionAccelerators.length, reflectionState.accelerators.length); i++) {
      reflectionState.accelerators[i].active = reflectionAccelerators[i].active;
      reflectionState.accelerators[i].efficiency = reflectionAccelerators[i].efficiency;
    }

    // Mettre à jour l'interface
    updateAcceleratorsUI();
  }
});

// Recevoir les statistiques de réflexion
socket.on('reflection stats', (data) => {
  if (data.success) {
    reflectionState.stats = data.stats;

    // Mettre à jour l'interface
    updateStatsUI();
  }
});

// Fonctions principales

// Charger les paramètres de réflexion
function loadReflectionSettings() {
  socket.emit('get reflection settings');
  socket.emit('get accelerators status');
  socket.emit('get reflection stats');
}

// Mettre à jour l'interface de réflexion
function updateReflectionUI() {
  // Mettre à jour les toggles
  $('#show-reflection-toggle').prop('checked', reflectionState.showReflection);
  $('#auto-translate-toggle').prop('checked', reflectionState.autoTranslate);

  // Mettre à jour le style d'affichage
  $('#reflection-style').val(reflectionState.displayStyle);
}

// Mettre à jour l'interface des accélérateurs
function updateAcceleratorsUI() {
  // Mettre à jour chaque accélérateur
  reflectionState.accelerators.forEach((acc, index) => {
    const accNumber = index + 1;

    // Mettre à jour le statut
    $(`#acc-reflection-${accNumber}-status`).text(acc.active ? 'Actif' : 'Inactif');

    // Mettre à jour la barre de progression
    const efficiency = Math.round(acc.efficiency * 100);
    $(`#acc-reflection-${accNumber}-bar`).css('width', `${efficiency}%`).attr('aria-valuenow', efficiency);

    // Ajouter une classe pour les accélérateurs inactifs
    if (!acc.active) {
      $(`#acc-reflection-${accNumber}-bar`).addClass('inactive');
    } else {
      $(`#acc-reflection-${accNumber}-bar`).removeClass('inactive');
    }
  });
}

// Mettre à jour l'interface des statistiques
function updateStatsUI() {
  $('#avg-reflection-time').text(`${reflectionState.stats.avgReflectionTime.toFixed(1)}s`);
  $('#reflection-ratio').text(`${reflectionState.stats.reflectionRatio.toFixed(1)}:1`);
  $('#translation-efficiency').text(`${Math.round(reflectionState.stats.translationEfficiency * 100)}%`);
}

// Optimiser les accélérateurs
function optimizeAccelerators() {
  // Désactiver le bouton et afficher l'indicateur de chargement
  $('#optimize-accelerators-btn').prop('disabled', true).html('<i class="bi bi-arrow-repeat spin me-1"></i> Optimisation...');

  // Utiliser le service d'accélérateurs pour l'optimisation
  reflectionAcceleratorsService.optimizeAccelerators()
    .then(data => {
      // Mettre à jour l'interface avec les nouvelles données
      updateAcceleratorsUI();

      // Réactiver le bouton
      $('#optimize-accelerators-btn').prop('disabled', false).html('<i class="bi bi-lightning-charge me-1"></i> Optimiser les accélérateurs');

      // Afficher une notification
      showNotification('Accélérateurs de réflexion optimisés avec succès', 'success');

      // Mettre à jour les statistiques d'efficacité
      if (data.stats) {
        $('#global-efficiency').text(`${(data.stats.efficiency * 100).toFixed(1)}%`);
        $('#reflection-accelerators').text(`${(data.stats.efficiency * 100 * 1.05).toFixed(1)}%`);

        // Mettre à jour les barres de progression
        $('.progress-bar.bg-success').css('width', `${data.stats.efficiency * 50}%`);
        $('.progress-bar.bg-info').css('width', `${data.stats.efficiency * 50 * 1.05}%`);
      }
    })
    .catch(error => {
      console.error('Erreur lors de l\'optimisation des accélérateurs:', error);

      // Réactiver le bouton
      $('#optimize-accelerators-btn').prop('disabled', false).html('<i class="bi bi-lightning-charge me-1"></i> Optimiser les accélérateurs');

      // Afficher une notification d'erreur
      showNotification('Erreur lors de l\'optimisation des accélérateurs', 'error');
    });
}

// Sauvegarder les paramètres de réflexion
function saveReflectionSettings() {
  const settings = {
    showReflection: reflectionState.showReflection,
    autoTranslate: reflectionState.autoTranslate,
    displayStyle: reflectionState.displayStyle
  };

  socket.emit('save reflection settings', settings);
}

// Afficher une notification
function showNotification(message, type = 'info') {
  // Créer l'élément de notification s'il n'existe pas
  let notificationArea = $('#notification-area');
  if (notificationArea.length === 0) {
    $('body').append('<div id="notification-area"></div>');
    notificationArea = $('#notification-area');
  }

  // Créer la notification
  const notification = $(`<div class="notification notification-${type}">${message}</div>`);
  notificationArea.append(notification);

  // Afficher avec animation
  setTimeout(() => {
    notification.addClass('show');

    // Masquer après 3 secondes
    setTimeout(() => {
      notification.removeClass('show');
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }, 100);
}

// Initialisation au chargement de la page
$(document).ready(function() {
  // Initialiser la date et l'heure
  updateDateTime();
  // Mettre à jour la date et l'heure toutes les secondes
  setInterval(updateDateTime, 1000);

  // Gestionnaires d'événements pour les toggles
  $('#show-reflection-toggle').on('change', function() {
    reflectionState.showReflection = $(this).is(':checked');
    saveReflectionSettings();
  });

  $('#auto-translate-toggle').on('change', function() {
    reflectionState.autoTranslate = $(this).is(':checked');
    saveReflectionSettings();
  });

  // Gestionnaire pour le style d'affichage
  $('#reflection-style').on('change', function() {
    reflectionState.displayStyle = $(this).val();
    saveReflectionSettings();
  });

  // Gestionnaire pour l'optimisation des accélérateurs
  $('#optimize-accelerators-btn').on('click', optimizeAccelerators);

  // Gestionnaire pour les paramètres de réflexion
  $('#reflection-settings-btn').on('click', function() {
    // Afficher la modale des paramètres avancés
    $('#reflection-advanced-modal').modal('show');
  });

  // Gestionnaire pour le bouton de démarrage de la réflexion
  $('#start-reflection-btn').on('click', startReflection);

  // Gestionnaire pour le bouton d'effacement de la réflexion
  $('#clear-reflection-btn').on('click', clearReflection);

  // Gestionnaire pour le bouton de sauvegarde de la réflexion
  $('#save-reflection-btn').on('click', saveReflection);

  // Gestionnaire pour le bouton de partage de la réflexion
  $('#share-reflection-btn').on('click', shareReflection);

  // Gestionnaire pour le bouton d'effacement de l'historique
  $('#clear-history-btn').on('click', clearHistory);

  // Gestionnaire pour le bouton de rafraîchissement des données thermiques
  $('#refresh-thermal-btn').on('click', updateThermalData);

  // Gestionnaire pour le bouton d'affichage/masquage des détails de la réflexion
  $('#toggle-reflection-details').on('click', toggleReflectionDetails);

  // Initialiser le graphique des accélérateurs
  initAcceleratorsChart();

  // Charger les paramètres au démarrage
  loadReflectionSettings();

  // Simuler des mises à jour périodiques
  setInterval(updateThermalData, 5000);
});

// Fonction pour démarrer la réflexion
function startReflection() {
  const input = document.getElementById('reflection-input').value.trim();

  if (!input) {
    showNotification('Veuillez entrer un texte pour la réflexion', 'error');
    return;
  }

  // Mettre à jour le statut
  updateReflectionStatus(true);

  // Afficher le conteneur de sortie
  document.getElementById('reflection-output-container').style.display = 'block';
  document.getElementById('reflection-details-container').style.display = 'block';

  // Désactiver le bouton de démarrage et activer le bouton d'effacement
  document.getElementById('start-reflection-btn').disabled = true;
  document.getElementById('clear-reflection-btn').disabled = false;

  // Analyser la complexité de la question (valeur de 1 à 10)
  const complexity = analyzeInputComplexity(input);

  // Calculer le temps de base en fonction de la complexité
  // Réduire considérablement le temps de base pour toutes les questions
  let baseReflectionTime;
  if (complexity <= 3) {
    // Questions très simples (0.5 - 1.0 secondes)
    baseReflectionTime = 0.5 + (complexity * 0.15);
  } else if (complexity <= 6) {
    // Questions moyennes (1.0 - 2.0 secondes)
    baseReflectionTime = 1.0 + ((complexity - 3) * 0.3);
  } else {
    // Questions complexes (2.0 - 4.0 secondes)
    baseReflectionTime = 2.0 + ((complexity - 6) * 0.5);
  }

  // Appliquer l'accélération avec le service d'accélérateurs
  const reflectionTimeData = reflectionAcceleratorsService.simulateReflectionTime(baseReflectionTime, complexity);

  const reflectionTime = reflectionTimeData.acceleratedTime;
  const acceleration = reflectionTimeData.accelerationFactor.toFixed(1);

  // Temps minimum pour l'affichage de l'animation (pour éviter les flashs trop rapides)
  const displayTime = Math.max(0.5, reflectionTime);

  // Afficher un indicateur de chargement
  document.getElementById('reflection-output').innerHTML = `
    <div class="text-center">
      <i class="bi bi-arrow-repeat spin fs-3 mb-2"></i>
      <p>Réflexion en cours avec accélération x${acceleration}...</p>
      <small class="text-muted">Temps estimé: ${reflectionTime.toFixed(2)}s (temps de base: ${baseReflectionTime.toFixed(2)}s)</small>
      <div class="mt-2">
        <small class="text-muted">Complexité: ${complexity}/10</small>
        <div class="progress mt-1" style="height: 5px;">
          <div class="progress-bar bg-info" role="progressbar" style="width: ${complexity*10}%"></div>
        </div>
      </div>
    </div>
  `;

  // Simuler le processus de réflexion
  setTimeout(() => {
    generateReflection(input, reflectionTime, acceleration, baseReflectionTime);
  }, displayTime * 1000);
}

// Fonction pour générer la réflexion
function generateReflection(input, time, acceleration, baseTime = null) {
  // Exemples de réflexions en français
  const reflections = [
    `<p>Pour répondre à "${input}", je dois d'abord analyser les concepts clés:</p>
    <ol>
      <li>Identifier les termes principaux et leurs relations</li>
      <li>Rechercher dans ma base de connaissances les informations pertinentes</li>
      <li>Structurer une réponse cohérente et complète</li>
      <li>Vérifier la précision et la pertinence de ma réponse</li>
    </ol>
    <p>Cette approche méthodique me permet de formuler une réponse optimale.</p>`,

    `<p>En analysant "${input}", plusieurs perspectives émergent:</p>
    <ul>
      <li>Perspective historique: comment ce sujet a-t-il évolué dans le temps?</li>
      <li>Perspective technique: quels sont les mécanismes sous-jacents?</li>
      <li>Perspective éthique: quelles considérations morales sont impliquées?</li>
      <li>Perspective pratique: comment appliquer ces connaissances?</li>
    </ul>
    <p>L'intégration de ces perspectives enrichit considérablement ma réponse.</p>`,

    `<p>Pour traiter "${input}", je procède par étapes:</p>
    <ol>
      <li>Décomposition du problème en éléments fondamentaux</li>
      <li>Analyse des relations causales entre ces éléments</li>
      <li>Évaluation des solutions potentielles selon plusieurs critères</li>
      <li>Synthèse d'une réponse optimisée et nuancée</li>
    </ol>
    <p>Cette méthode systématique garantit une réponse de haute qualité.</p>`
  ];

  // Sélectionner une réflexion aléatoire
  const reflection = reflections[Math.floor(Math.random() * reflections.length)];

  // Si le temps de base n'est pas fourni, l'estimer à partir du temps accéléré
  if (baseTime === null) {
    baseTime = time * parseFloat(acceleration);
  }

  // Générer des statistiques aléatoires mais réalistes
  const nodesActivated = Math.floor(Math.random() * 1000 + 800);
  const connectionsTraversed = Math.floor(nodesActivated * (Math.random() * 5 + 5));
  const hypothesesGenerated = Math.floor(Math.random() * 5 + 5);
  const evaluationCycles = Math.floor(Math.random() * 3 + 2);

  // Détails de la réflexion (en français)
  const details = `
    <div class="reflection-details-content">
      <h6 class="mb-2">Processus de réflexion détaillé:</h6>
      <p><strong>Phase 1: Analyse initiale</strong></p>
      <p>J'ai d'abord décomposé la requête "${input}" en identifiant les concepts clés et leurs relations sémantiques. Cette étape implique une analyse linguistique approfondie pour comprendre l'intention et le contexte.</p>

      <p><strong>Phase 2: Recherche et activation de connaissances</strong></p>
      <p>J'ai ensuite activé les nœuds pertinents dans mon réseau de connaissances, établissant des connexions entre différents domaines. Cette activation s'est propagée selon un modèle de diffusion pondérée, privilégiant les informations les plus pertinentes.</p>

      <p><strong>Phase 3: Génération d'hypothèses</strong></p>
      <p>À partir des connaissances activées, j'ai généré plusieurs hypothèses de réponse, chacune représentant une approche différente. Ces hypothèses ont été évaluées selon leur cohérence, leur exhaustivité et leur pertinence.</p>

      <p><strong>Phase 4: Évaluation critique</strong></p>
      <p>J'ai soumis chaque hypothèse à une évaluation critique, identifiant les forces et les faiblesses. Cette phase implique une simulation de différentes perspectives et une anticipation des questions potentielles.</p>

      <p><strong>Phase 5: Synthèse et formulation</strong></p>
      <p>Enfin, j'ai synthétisé les éléments les plus pertinents en une réponse cohérente, en veillant à la clarté, à la précision et à l'adaptation au contexte de la demande.</p>

      <h6 class="mt-3 mb-2">Métriques de performance:</h6>
      <ul>
        <li>Nœuds de connaissance activés: ${nodesActivated.toLocaleString()}</li>
        <li>Connexions traversées: ${connectionsTraversed.toLocaleString()}</li>
        <li>Hypothèses générées: ${hypothesesGenerated}</li>
        <li>Cycles d'évaluation: ${evaluationCycles}</li>
        <li>Temps de réflexion brut: ${baseTime.toFixed(2)}s</li>
        <li>Accélération appliquée: x${acceleration}</li>
        <li>Temps de réflexion net: ${time.toFixed(2)}s</li>
        <li>Gain de temps: ${(baseTime - time).toFixed(2)}s</li>
      </ul>

      <h6 class="mt-3 mb-2">Accélérateurs Kyber utilisés:</h6>
      <div class="row">
        <div class="col-md-6">
          <ul>
            <li>Accélérateurs de réflexion: ${Math.floor(Math.random() * 3 + 3)}</li>
            <li>Efficacité moyenne: ${(Math.random() * 10 + 90).toFixed(1)}%</li>
            <li>Température: ${(Math.random() * 20 + 40).toFixed(1)}°C</li>
          </ul>
        </div>
        <div class="col-md-6">
          <ul>
            <li>Accélérateurs de mémoire: ${Math.floor(Math.random() * 2 + 2)}</li>
            <li>Compression: ${(Math.random() * 10 + 85).toFixed(1)}%</li>
            <li>Débit: ${Math.floor(Math.random() * 1000 + 2000)} MB/s</li>
          </ul>
        </div>
      </div>
    </div>
  `;

  // Mettre à jour l'interface
  document.getElementById('reflection-output').innerHTML = reflection;
  document.getElementById('reflection-details').innerHTML = details;
  document.getElementById('reflection-time').textContent = time.toFixed(2) + 's';
  document.getElementById('reflection-acceleration').textContent = 'x' + acceleration;

  // Activer les boutons de sauvegarde et de partage
  document.getElementById('save-reflection-btn').disabled = false;
  document.getElementById('share-reflection-btn').disabled = false;

  // Mettre à jour le statut
  updateReflectionStatus(false);

  // Ajouter à l'historique
  addToHistory(input, reflection, time, acceleration);

  // Afficher une notification
  showNotification('Réflexion générée avec succès', 'success');

  // Mettre à jour les données des accélérateurs
  updateThermalData();
}

/**
 * Analyse la complexité de l'entrée utilisateur pour ajuster le temps de réflexion
 * @param {string} input - Texte d'entrée à analyser
 * @returns {number} Facteur de complexité (0.5 pour simple, 1.0 pour moyen, 1.5+ pour complexe)
 */
function analyzeInputComplexity(input) {
  if (!input) return 0.5; // Entrée vide ou nulle = simple

  // Nettoyer l'entrée
  const cleanInput = input.trim().toLowerCase();

  // Longueur de l'entrée (plus c'est long, plus c'est complexe)
  // Réduire l'impact de la longueur pour accélérer la réflexion
  const lengthFactor = Math.min(1.2, cleanInput.length / 150);

  // Nombre de questions (plus il y a de questions, plus c'est complexe)
  const questionCount = (cleanInput.match(/\?/g) || []).length;
  const questionFactor = Math.min(1.2, 1 + (questionCount * 0.15));

  // Mots complexes ou indicateurs de complexité
  const complexityIndicators = [
    'pourquoi', 'comment', 'expliquer', 'analyser', 'comparer',
    'différence', 'relation', 'impact', 'conséquence', 'évaluer',
    'synthétiser', 'critiquer', 'justifier', 'démontrer', 'prouver'
  ];

  // Mots simples (questions factuelles)
  const simplicityIndicators = [
    'qui', 'quoi', 'quand', 'où', 'combien', 'est-ce que', 'peux-tu',
    'pouvez-vous', 'liste', 'donne', 'montre', 'affiche', 'quelle'
  ];

  // Compter les indicateurs de complexité
  let indicatorCount = 0;
  complexityIndicators.forEach(indicator => {
    if (cleanInput.includes(indicator)) {
      indicatorCount++;
    }
  });

  // Compter les indicateurs de simplicité
  let simplicityCount = 0;
  simplicityIndicators.forEach(indicator => {
    if (cleanInput.includes(indicator)) {
      simplicityCount++;
    }
  });

  // Ajuster le facteur d'indicateur en soustrayant les indicateurs de simplicité
  const indicatorFactor = Math.min(1.2, Math.max(0.3, 1 + (indicatorCount * 0.1) - (simplicityCount * 0.1)));

  // Calculer le facteur de complexité global (minimum 0.3, maximum 1.5)
  // Réduire la plage pour accélérer la réflexion
  let complexityFactor = Math.max(0.3, Math.min(1.5,
    (lengthFactor + questionFactor + indicatorFactor) / 3
  ));

  // Réduire encore la complexité pour les questions courtes
  if (cleanInput.length < 30) {
    complexityFactor = Math.max(0.3, complexityFactor * 0.6);
  } else if (cleanInput.length < 60) {
    complexityFactor = Math.max(0.3, complexityFactor * 0.8);
  }

  // Convertir le facteur de complexité en valeur entière de 1 à 10 pour la compatibilité avec simulateReflectionTime
  const complexityValue = Math.max(1, Math.min(10, Math.round(complexityFactor * 6.67)));

  console.log(`Analyse de complexité: ${complexityFactor.toFixed(2)} (valeur: ${complexityValue}, longueur: ${lengthFactor.toFixed(2)}, questions: ${questionFactor.toFixed(2)}, indicateurs: ${indicatorFactor.toFixed(2)})`);

  return complexityValue;
}

// Fonction pour effacer la réflexion
function clearReflection() {
  // Réinitialiser l'entrée
  document.getElementById('reflection-input').value = '';

  // Masquer les conteneurs de sortie
  document.getElementById('reflection-output-container').style.display = 'none';
  document.getElementById('reflection-details-container').style.display = 'none';

  // Réinitialiser les boutons
  document.getElementById('start-reflection-btn').disabled = false;
  document.getElementById('clear-reflection-btn').disabled = true;
  document.getElementById('save-reflection-btn').disabled = true;
  document.getElementById('share-reflection-btn').disabled = true;

  // Mettre à jour le statut
  updateReflectionStatus(false);
}

// Fonction pour mettre à jour le statut de la réflexion
function updateReflectionStatus(isActive) {
  const statusIndicator = document.querySelector('#reflection-status .status-indicator');
  const statusText = document.querySelector('#reflection-status span:not(.status-indicator)');

  if (isActive) {
    statusIndicator.classList.remove('status-inactive');
    statusIndicator.classList.add('status-active');
    statusText.textContent = 'Actif';
  } else {
    statusIndicator.classList.remove('status-active');
    statusIndicator.classList.add('status-inactive');
    statusText.textContent = 'Inactif';
  }
}

// Fonction pour afficher/masquer les détails de la réflexion
function toggleReflectionDetails() {
  const detailsElement = document.getElementById('reflection-details');
  const isVisible = detailsElement.style.display !== 'none';
  const toggleText = document.getElementById('reflection-details-text');
  const toggleIcon = document.getElementById('reflection-details-icon');

  if (isVisible) {
    detailsElement.style.display = 'none';
    toggleText.textContent = 'Afficher les détails';
    toggleIcon.classList.remove('bi-chevron-up');
    toggleIcon.classList.add('bi-chevron-down');
  } else {
    detailsElement.style.display = 'block';
    toggleText.textContent = 'Masquer les détails';
    toggleIcon.classList.remove('bi-chevron-down');
    toggleIcon.classList.add('bi-chevron-up');
  }
}

// Fonction pour sauvegarder la réflexion
function saveReflection() {
  // Simuler la sauvegarde
  document.getElementById('save-reflection-btn').disabled = true;
  document.getElementById('save-reflection-btn').innerHTML = '<i class="bi bi-arrow-repeat spin me-1"></i> Sauvegarde...';

  setTimeout(() => {
    document.getElementById('save-reflection-btn').disabled = false;
    document.getElementById('save-reflection-btn').innerHTML = '<i class="bi bi-save me-1"></i> Sauvegarder';

    showNotification('Réflexion sauvegardée avec succès', 'success');
  }, 1000);
}

// Fonction pour partager la réflexion
function shareReflection() {
  // Simuler le partage
  document.getElementById('share-reflection-btn').disabled = true;
  document.getElementById('share-reflection-btn').innerHTML = '<i class="bi bi-arrow-repeat spin me-1"></i> Partage...';

  setTimeout(() => {
    document.getElementById('share-reflection-btn').disabled = false;
    document.getElementById('share-reflection-btn').innerHTML = '<i class="bi bi-share me-1"></i> Partager';

    showNotification('Lien de partage copié dans le presse-papiers', 'success');
  }, 1000);
}

// Fonction pour ajouter une réflexion à l'historique
function addToHistory(input, reflection, time, acceleration) {
  const historyContainer = document.getElementById('reflection-history');
  const timestamp = new Date().toLocaleTimeString();

  // Créer un élément d'historique
  const historyItem = document.createElement('div');
  historyItem.className = 'reflection-history-item mb-3 p-3 rounded';
  historyItem.style.backgroundColor = 'rgba(26, 26, 46, 0.5)';
  historyItem.style.border = '1px solid rgba(184, 190, 221, 0.2)';

  historyItem.innerHTML = `
    <div class="d-flex justify-content-between align-items-center mb-2">
      <h6 class="mb-0">${input.length > 30 ? input.substring(0, 30) + '...' : input}</h6>
      <small class="text-muted">${timestamp}</small>
    </div>
    <div class="reflection-history-content mb-2" style="max-height: 100px; overflow: hidden;">
      ${reflection}
    </div>
    <div class="d-flex justify-content-between align-items-center">
      <div>
        <span class="badge bg-primary me-1">${time.toFixed(2)}s</span>
        <span class="badge bg-success">x${acceleration}</span>
      </div>
      <button class="btn btn-sm btn-luna-outline view-reflection-btn">
        <i class="bi bi-eye me-1"></i> Voir
      </button>
    </div>
  `;

  // Ajouter un gestionnaire d'événements pour le bouton de visualisation
  historyItem.querySelector('.view-reflection-btn').addEventListener('click', function() {
    document.getElementById('reflection-input').value = input;
    document.getElementById('reflection-output').innerHTML = reflection;
    document.getElementById('reflection-output-container').style.display = 'block';
    document.getElementById('reflection-details-container').style.display = 'block';
    document.getElementById('reflection-time').textContent = time.toFixed(2) + 's';
    document.getElementById('reflection-acceleration').textContent = 'x' + acceleration;

    // Activer les boutons
    document.getElementById('clear-reflection-btn').disabled = false;
    document.getElementById('save-reflection-btn').disabled = false;
    document.getElementById('share-reflection-btn').disabled = false;

    // Faire défiler vers le haut
    window.scrollTo({ top: 0, behavior: 'smooth' });
  });

  // Vérifier si l'historique est vide
  if (historyContainer.querySelector('.text-center')) {
    historyContainer.innerHTML = '';
  }

  // Ajouter l'élément à l'historique
  historyContainer.prepend(historyItem);
}

// Fonction pour effacer l'historique
function clearHistory() {
  const historyContainer = document.getElementById('reflection-history');

  // Demander confirmation
  if (confirm('Êtes-vous sûr de vouloir effacer tout l\'historique des réflexions?')) {
    historyContainer.innerHTML = `
      <div class="text-center text-muted py-4">
        <i class="bi bi-clock-history fs-4 mb-2 d-block"></i>
        Aucune réflexion dans l'historique
      </div>
    `;

    showNotification('Historique des réflexions effacé', 'info');
  }
}

// Fonction pour mettre à jour les données thermiques
function updateThermalData() {
  // Simuler la récupération des données
  const cpuTemp = (Math.random() * 5 + 40).toFixed(1);
  const activeZone = Math.floor(Math.random() * 6) + 1;
  const zoneTemp = activeZone === 1 ? '100°' :
                  activeZone === 2 ? '80°' :
                  activeZone === 3 ? '60°' :
                  activeZone === 4 ? '40°' :
                  activeZone === 5 ? '20°' : '5°';
  const systemActivity = (Math.random() * 30 + 70).toFixed(1);

  // Mettre à jour l'interface
  document.getElementById('cpu-temp').textContent = cpuTemp + '°C';
  document.getElementById('active-zone').textContent = `Zone ${activeZone} (${zoneTemp})`;
  document.getElementById('system-activity').textContent = systemActivity + '%';

  // Mettre à jour les barres de progression
  document.querySelector('.progress-bar.bg-info').style.width = (cpuTemp / 85 * 100) + '%';
  document.querySelector('.progress-bar.bg-danger').style.width = (parseInt(zoneTemp) / 100 * 100) + '%';
  document.querySelector('.progress-bar.bg-success').style.width = systemActivity + '%';

  // Mettre à jour les efficacités des accélérateurs
  const globalEfficiency = (Math.random() * 20 + 150).toFixed(1);
  const reflectionAccelerators = (Math.random() * 20 + 160).toFixed(1);
  const memoryAccelerators = (Math.random() * 20 + 130).toFixed(1);
  const thermalAccelerators = (Math.random() * 20 + 120).toFixed(1);

  document.getElementById('global-efficiency').textContent = globalEfficiency + '%';
  document.getElementById('reflection-accelerators').textContent = reflectionAccelerators + '%';
  document.getElementById('memory-accelerators').textContent = memoryAccelerators + '%';
  document.getElementById('thermal-accelerators').textContent = thermalAccelerators + '%';
}

// Fonction pour initialiser le graphique des accélérateurs
function initAcceleratorsChart() {
  const ctx = document.getElementById('accelerators-chart').getContext('2d');

  const acceleratorsChart = new Chart(ctx, {
    type: 'line',
    data: {
      labels: ['0s', '5s', '10s', '15s', '20s', '25s', '30s'],
      datasets: [
        {
          label: 'Réflexion',
          data: [150, 155, 160, 165, 170, 172, 173],
          borderColor: 'rgba(23, 162, 184, 1)',
          backgroundColor: 'rgba(23, 162, 184, 0.1)',
          tension: 0.4,
          fill: true
        },
        {
          label: 'Mémoire',
          data: [130, 135, 138, 140, 142, 141, 142],
          borderColor: 'rgba(0, 123, 255, 1)',
          backgroundColor: 'rgba(0, 123, 255, 0.1)',
          tension: 0.4,
          fill: true
        },
        {
          label: 'Thermique',
          data: [120, 125, 128, 130, 132, 131, 131],
          borderColor: 'rgba(255, 193, 7, 1)',
          backgroundColor: 'rgba(255, 193, 7, 0.1)',
          tension: 0.4,
          fill: true
        }
      ]
    },
    options: {
      responsive: true,
      plugins: {
        legend: {
          position: 'top',
          labels: {
            color: 'rgba(237, 242, 251, 0.7)'
          }
        },
        tooltip: {
          mode: 'index',
          intersect: false
        }
      },
      scales: {
        y: {
          min: 100,
          max: 200,
          ticks: {
            color: 'rgba(237, 242, 251, 0.7)',
            callback: function(value) {
              return value + '%';
            }
          },
          grid: {
            color: 'rgba(255, 255, 255, 0.1)'
          }
        },
        x: {
          ticks: {
            color: 'rgba(237, 242, 251, 0.7)'
          },
          grid: {
            color: 'rgba(255, 255, 255, 0.1)'
          }
        }
      }
    }
  });

  // Mettre à jour le graphique périodiquement
  setInterval(() => {
    // Ajouter de nouvelles données
    const newTime = parseInt(acceleratorsChart.data.labels[acceleratorsChart.data.labels.length - 1]) + 5;
    acceleratorsChart.data.labels.push(newTime + 's');

    // Limiter à 8 points de données
    if (acceleratorsChart.data.labels.length > 8) {
      acceleratorsChart.data.labels.shift();
      acceleratorsChart.data.datasets.forEach(dataset => {
        dataset.data.shift();
      });
    }

    // Ajouter de nouvelles valeurs
    acceleratorsChart.data.datasets[0].data.push(Math.floor(Math.random() * 10 + 165));
    acceleratorsChart.data.datasets[1].data.push(Math.floor(Math.random() * 10 + 135));
    acceleratorsChart.data.datasets[2].data.push(Math.floor(Math.random() * 10 + 125));

    // Mettre à jour le graphique
    acceleratorsChart.update();
  }, 5000);

  return acceleratorsChart;
}