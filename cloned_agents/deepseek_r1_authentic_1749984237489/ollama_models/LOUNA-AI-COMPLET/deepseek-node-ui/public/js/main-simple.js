document.addEventListener('DOMContentLoaded', () => {
  // Éléments DOM
  const chatForm = document.getElementById('chat-form');
  const messageInput = document.getElementById('message-input');
  const chatMessages = document.getElementById('chat-messages');
  const ollamaStatus = document.getElementById('ollama-status');
  const modelSelector = document.getElementById('model-selector');
  const temperatureSlider = document.getElementById('temperature');
  const temperatureValue = document.getElementById('temperature-value');
  const maxTokensSlider = document.getElementById('max-tokens');
  const maxTokensValue = document.getElementById('max-tokens-value');
  const currentChatTitle = document.getElementById('current-chat-title');

  // Variables globales
  let socket;
  let isWaitingForResponse = false;
  let selectedModel = 'deepseek-r1:1.5b';

  // Initialisation
  initializeSocket();
  initializeUI();

  // Fonctions
  function initializeSocket() {
    console.log('Initializing socket connection...');
    
    // Initialiser la connexion socket avec des options robustes
    socket = io({
      reconnectionAttempts: Infinity,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      timeout: 20000,
      transports: ['websocket', 'polling'],
      upgrade: true,
      forceNew: true
    });

    // Événement de connexion réussie
    socket.on('connect', () => {
      console.log('Connected to server successfully');
      displaySystemMessage('Connecté au serveur');
      checkOllamaStatus();
    });

    // Événement de reconnexion
    socket.on('reconnect', (attemptNumber) => {
      console.log(`Reconnected to server after ${attemptNumber} attempts`);
      displaySystemMessage(`Reconnecté au serveur après ${attemptNumber} tentatives`);
      checkOllamaStatus();
    });

    // Événements d'erreur
    socket.on('connect_error', (error) => {
      console.error('Connection error:', error);
      displaySystemMessage(`Erreur de connexion: ${error.message}`);
    });

    socket.on('connect_timeout', () => {
      console.error('Connection timeout');
      displaySystemMessage('Connexion au serveur expirée');
    });

    socket.on('error', (error) => {
      console.error('Socket error:', error);
      displaySystemMessage(`Erreur socket: ${error.message}`);
    });

    socket.on('disconnect', (reason) => {
      console.log('Disconnected from server:', reason);
      displaySystemMessage(`Déconnecté du serveur: ${reason}`);
      
      if (reason === 'io server disconnect') {
        console.log('Attempting to reconnect...');
        socket.connect();
      }
    });

    // Événements spécifiques à l'application
    socket.on('ollama status', (data) => {
      console.log('Received Ollama status:', data);
      updateOllamaStatus(data);
    });

    socket.on('chat response', (data) => {
      console.log('Received chat response:', data);
      handleChatResponse(data);
    });

    socket.on('processing', (isProcessing) => {
      console.log('Processing status:', isProcessing);
      updateProcessingStatus(isProcessing);
    });
  }

  function initializeUI() {
    // Initialiser les contrôles de l'interface
    if (temperatureSlider && temperatureValue) {
      temperatureSlider.addEventListener('input', () => {
        temperatureValue.textContent = temperatureSlider.value;
      });
    }

    if (maxTokensSlider && maxTokensValue) {
      maxTokensSlider.addEventListener('input', () => {
        maxTokensValue.textContent = maxTokensSlider.value;
      });
    }

    // Gestionnaire du formulaire de chat
    if (chatForm) {
      chatForm.addEventListener('submit', (e) => {
        e.preventDefault();
        sendMessage();
      });
    }

    // Affichage de l'état initial
    if (currentChatTitle) {
      currentChatTitle.textContent = 'Nouvelle conversation';
    }

    displaySystemMessage('Application initialisée');
  }

  function checkOllamaStatus() {
    console.log('Checking Ollama status...');
    socket.emit('check ollama');
  }

  function updateOllamaStatus(data) {
    if (!ollamaStatus) return;

    if (data.available) {
      ollamaStatus.innerHTML = `<span class="badge bg-success">Ollama disponible</span>`;
      ollamaStatus.title = `Version: ${data.version || 'Unknown'}`;
    } else {
      ollamaStatus.innerHTML = `<span class="badge bg-danger">Ollama indisponible</span>`;
      ollamaStatus.title = data.error || 'Service non disponible';
    }
  }

  function sendMessage() {
    if (!messageInput || !socket || isWaitingForResponse) return;
    
    const message = messageInput.value.trim();
    if (message === '') return;

    // Afficher le message de l'utilisateur
    displayMessage(message, 'user');
    
    // Préparer et envoyer la requête
    const data = {
      message,
      modelName: selectedModel,
      temperature: temperatureSlider ? temperatureSlider.value : 0.7,
      maxTokens: maxTokensSlider ? maxTokensSlider.value : 1000
    };
    
    console.log('Sending message:', data);
    socket.emit('chat message', data);
    
    // Réinitialiser l'entrée et mettre à jour l'état
    messageInput.value = '';
    isWaitingForResponse = true;
    
    // Afficher l'indicateur de chargement
    displayLoading();
  }

  function handleChatResponse(data) {
    // Supprimer l'indicateur de chargement
    removeLoading();
    
    // Extraire le contenu de la réponse
    let content = '';
    
    if (data.error) {
      content = `Erreur: ${data.error}`;
    } else if (data.message && data.message.content) {
      content = data.message.content;
    } else if (typeof data === 'string') {
      content = data;
    } else {
      content = "Désolé, je n'ai pas pu comprendre la réponse.";
    }
    
    // Afficher la réponse
    displayMessage(content, 'bot');
    
    // Mise à jour de l'état
    isWaitingForResponse = false;
  }

  function updateProcessingStatus(isProcessing) {
    if (isProcessing) {
      displayLoading();
    } else {
      removeLoading();
    }
  }

  function displayMessage(content, sender) {
    if (!chatMessages) return;
    
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}-message`;
    
    // Si c'est un message du bot, traiter le markdown
    if (sender === 'bot') {
      messageDiv.innerHTML = `
        <div class="message-avatar">
          <i class="bi ${sender === 'user' ? 'bi-person-circle' : 'bi-robot'}"></i>
        </div>
        <div class="message-content">
          ${marked.parse(content)}
        </div>
      `;
    } else {
      messageDiv.innerHTML = `
        <div class="message-avatar">
          <i class="bi ${sender === 'user' ? 'bi-person-circle' : 'bi-robot'}"></i>
        </div>
        <div class="message-content">
          <p>${content}</p>
        </div>
      `;
    }
    
    chatMessages.appendChild(messageDiv);
    
    // Appliquer la coloration syntaxique
    messageDiv.querySelectorAll('pre code').forEach((block) => {
      hljs.highlightElement(block);
    });
    
    // Défiler vers le bas
    chatMessages.scrollTop = chatMessages.scrollHeight;
  }

  function displaySystemMessage(text) {
    if (!chatMessages) return;
    
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message system-message';
    messageDiv.innerHTML = `
      <div class="message-content">
        <p><i class="bi bi-info-circle"></i> ${text}</p>
      </div>
    `;
    
    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
  }

  function displayLoading() {
    if (!chatMessages) return;
    
    // Supprimer tout indicateur de chargement existant
    removeLoading();
    
    const loadingDiv = document.createElement('div');
    loadingDiv.className = 'message bot-message loading';
    loadingDiv.id = 'loading-indicator';
    loadingDiv.innerHTML = `
      <div class="message-avatar">
        <i class="bi bi-robot"></i>
      </div>
      <div class="message-content">
        <div class="typing-indicator">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    `;
    
    chatMessages.appendChild(loadingDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
  }

  function removeLoading() {
    const loadingIndicator = document.getElementById('loading-indicator');
    if (loadingIndicator) {
      loadingIndicator.remove();
    }
  }

  // Exposer les fonctions pertinentes globalement
  window.checkOllamaStatus = checkOllamaStatus;
  window.setModel = (modelName) => {
    selectedModel = modelName;
    console.log(`Model set to: ${modelName}`);
    displaySystemMessage(`Modèle sélectionné: ${modelName}`);
  };
});
