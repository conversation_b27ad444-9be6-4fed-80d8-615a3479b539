// Initialiser Socket.IO
const socket = io();

// Variables globales
let programKnowledge = null;
let lastScanTime = null;
let missingElements = [];
let incompleteElements = [];

// Fonction pour mettre à jour la date et l'heure
function updateDateTime() {
  const now = new Date();
  const dateOptions = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
  const timeOptions = { hour: '2-digit', minute: '2-digit', second: '2-digit' };

  // Mettre à jour les éléments de date et d'heure dans la page
  if (document.getElementById('current-date')) {
    document.getElementById('current-date').textContent = now.toLocaleDateString('fr-FR', dateOptions);
  }
  
  if (document.getElementById('current-time')) {
    document.getElementById('current-time').textContent = now.toLocaleTimeString('fr-FR', timeOptions);
  }
  
  // Mettre à jour l'élément current-datetime
  if (document.getElementById('current-datetime')) {
    document.getElementById('current-datetime').textContent = now.toLocaleString('fr-FR');
  }
  
  // Mettre à jour les éléments de date et d'heure dans le panneau latéral
  if (document.getElementById('current-date-panel')) {
    document.getElementById('current-date-panel').textContent = now.toLocaleDateString('fr-FR', dateOptions);
  }
  
  if (document.getElementById('current-time-panel')) {
    document.getElementById('current-time-panel').textContent = now.toLocaleTimeString('fr-FR', timeOptions);
  }
  
  if (document.getElementById('timezone')) {
    document.getElementById('timezone').textContent = Intl.DateTimeFormat().resolvedOptions().timeZone;
  }
  
  if (document.getElementById('date-format')) {
    document.getElementById('date-format').textContent = 'fr-FR';
  }
}

// Fonction pour afficher un toast
function showToast(title, message, type = 'info') {
  const toastContainer = document.querySelector('.toast-container');
  
  const toastElement = document.createElement('div');
  toastElement.className = `toast show bg-${type === 'info' ? 'primary' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'danger'}`;
  toastElement.setAttribute('role', 'alert');
  toastElement.setAttribute('aria-live', 'assertive');
  toastElement.setAttribute('aria-atomic', 'true');
  
  const toastHeader = document.createElement('div');
  toastHeader.className = 'toast-header';
  
  const toastTitle = document.createElement('strong');
  toastTitle.className = 'me-auto';
  toastTitle.textContent = title;
  
  const toastTime = document.createElement('small');
  toastTime.textContent = 'maintenant';
  
  const toastClose = document.createElement('button');
  toastClose.type = 'button';
  toastClose.className = 'btn-close';
  toastClose.setAttribute('data-bs-dismiss', 'toast');
  toastClose.setAttribute('aria-label', 'Fermer');
  
  toastHeader.appendChild(toastTitle);
  toastHeader.appendChild(toastTime);
  toastHeader.appendChild(toastClose);
  
  const toastBody = document.createElement('div');
  toastBody.className = 'toast-body text-white';
  toastBody.textContent = message;
  
  toastElement.appendChild(toastHeader);
  toastElement.appendChild(toastBody);
  
  toastContainer.appendChild(toastElement);
  
  // Supprimer le toast après 5 secondes
  setTimeout(() => {
    toastElement.remove();
  }, 5000);
}

// Fonction pour charger la connaissance du programme
function loadProgramKnowledge() {
  fetch('/luna/api/program-knowledge')
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        programKnowledge = data.programKnowledge;
        lastScanTime = data.lastScanTime ? new Date(data.lastScanTime) : null;
        
        // Récupérer les éléments manquants et incomplets
        missingElements = programKnowledge.missingElements || [];
        incompleteElements = programKnowledge.incompleteElements || [];
        
        // Mettre à jour le badge d'analyse
        updateAnalysisBadge();
        
        // Mettre à jour l'interface
        updateProgramKnowledgeUI();
      } else {
        showToast('Erreur', 'Impossible de charger la connaissance du programme', 'danger');
      }
    })
    .catch(error => {
      console.error('Erreur lors du chargement de la connaissance du programme:', error);
      showToast('Erreur', 'Impossible de charger la connaissance du programme', 'danger');
    });
}

// Fonction pour mettre à jour le badge d'analyse
function updateAnalysisBadge() {
  const totalIssues = missingElements.length + incompleteElements.length;
  const badge = document.getElementById('analysis-badge');
  
  if (badge && totalIssues > 0) {
    badge.textContent = totalIssues;
    badge.style.display = 'inline-block';
  } else if (badge) {
    badge.style.display = 'none';
  }
}

// Fonction pour analyser le programme
function analyzeProgram() {
  fetch('/luna/api/program-knowledge/analyze', {
    method: 'POST'
  })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showToast('Analyse démarrée', 'L\'analyse du programme a été démarrée. Veuillez patienter...', 'info');
      } else {
        showToast('Erreur', data.error || 'Impossible de démarrer l\'analyse du programme', 'danger');
      }
    })
    .catch(error => {
      console.error('Erreur lors du démarrage de l\'analyse du programme:', error);
      showToast('Erreur', 'Impossible de démarrer l\'analyse du programme', 'danger');
    });
}

// Fonction pour mettre à jour l'interface utilisateur
function updateProgramKnowledgeUI() {
  if (!programKnowledge) return;
  
  // Mettre à jour les statistiques du programme
  updateProgramStats();
  
  // Mettre à jour les informations de dernière analyse
  updateLastScanInfo();
}

// Fonction pour mettre à jour les statistiques du programme
function updateProgramStats() {
  const statsContainer = document.getElementById('program-stats');
  if (!statsContainer) return;
  
  const interfacesCount = programKnowledge.interfaces ? programKnowledge.interfaces.length : 0;
  const servicesCount = programKnowledge.services ? programKnowledge.services.length : 0;
  const routesCount = programKnowledge.routes ? programKnowledge.routes.length : 0;
  const stylesCount = programKnowledge.styles ? programKnowledge.styles.length : 0;
  const scriptsCount = programKnowledge.scripts ? programKnowledge.scripts.length : 0;
  
  const statsHtml = `
    <div class="row">
      <div class="col-6 mb-3">
        <div class="d-flex justify-content-between">
          <span>Interfaces:</span>
          <span>${interfacesCount}</span>
        </div>
      </div>
      <div class="col-6 mb-3">
        <div class="d-flex justify-content-between">
          <span>Services:</span>
          <span>${servicesCount}</span>
        </div>
      </div>
      <div class="col-6 mb-3">
        <div class="d-flex justify-content-between">
          <span>Routes:</span>
          <span>${routesCount}</span>
        </div>
      </div>
      <div class="col-6 mb-3">
        <div class="d-flex justify-content-between">
          <span>Styles:</span>
          <span>${stylesCount}</span>
        </div>
      </div>
      <div class="col-6 mb-3">
        <div class="d-flex justify-content-between">
          <span>Scripts:</span>
          <span>${scriptsCount}</span>
        </div>
      </div>
      <div class="col-6 mb-3">
        <div class="d-flex justify-content-between">
          <span>Éléments manquants:</span>
          <span>${missingElements.length}</span>
        </div>
      </div>
      <div class="col-6 mb-3">
        <div class="d-flex justify-content-between">
          <span>Éléments incomplets:</span>
          <span>${incompleteElements.length}</span>
        </div>
      </div>
    </div>
  `;
  
  statsContainer.innerHTML = statsHtml;
}

// Fonction pour mettre à jour les informations de dernière analyse
function updateLastScanInfo() {
  const lastScanContainer = document.getElementById('last-scan-info');
  if (!lastScanContainer) return;
  
  if (lastScanTime) {
    const lastScanHtml = `
      <div class="d-flex flex-column">
        <div class="mb-2">
          <strong>Date:</strong> ${lastScanTime.toLocaleDateString('fr-FR')}
        </div>
        <div class="mb-2">
          <strong>Heure:</strong> ${lastScanTime.toLocaleTimeString('fr-FR')}
        </div>
        <div class="mb-2">
          <strong>Éléments analysés:</strong> ${programKnowledge.interfaces.length + programKnowledge.services.length + programKnowledge.routes.length + programKnowledge.styles.length + programKnowledge.scripts.length}
        </div>
        <div>
          <strong>Problèmes détectés:</strong> ${missingElements.length + incompleteElements.length}
        </div>
      </div>
    `;
    
    lastScanContainer.innerHTML = lastScanHtml;
  } else {
    lastScanContainer.innerHTML = '<div class="alert alert-warning">Aucune analyse n\'a été effectuée</div>';
  }
}

// Événement pour scanner le programme
document.addEventListener('DOMContentLoaded', () => {
  // Mettre à jour la date et l'heure
  updateDateTime();
  setInterval(updateDateTime, 1000);
  
  // Charger la connaissance du programme
  loadProgramKnowledge();
  
  // Événement pour scanner le programme
  const scanButton = document.getElementById('scan-program-btn');
  if (scanButton) {
    scanButton.addEventListener('click', () => {
      fetch('/luna/api/program-knowledge/scan', {
        method: 'POST'
      })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            showToast('Scan démarré', 'Le scan du programme a été démarré. Veuillez patienter...', 'info');
          } else {
            showToast('Erreur', data.error || 'Impossible de démarrer le scan du programme', 'danger');
          }
        })
        .catch(error => {
          console.error('Erreur lors du démarrage du scan du programme:', error);
          showToast('Erreur', 'Impossible de démarrer le scan du programme', 'danger');
        });
    });
  }
  
  // Événement pour analyser le programme
  const analyzeButton = document.getElementById('analyze-program-btn');
  if (analyzeButton) {
    analyzeButton.addEventListener('click', analyzeProgram);
  }
  
  // Événements Socket.IO
  socket.on('scan program status', (data) => {
    if (data.success) {
      if (data.status === 'completed') {
        programKnowledge = data.programKnowledge;
        lastScanTime = data.lastScanTime ? new Date(data.lastScanTime) : null;
        
        // Récupérer les éléments manquants et incomplets
        missingElements = programKnowledge.missingElements || [];
        incompleteElements = programKnowledge.incompleteElements || [];
        
        // Mettre à jour le badge d'analyse
        updateAnalysisBadge();
        
        updateProgramKnowledgeUI();
        showToast('Scan terminé', 'Le scan du programme a été terminé avec succès', 'success');
      }
    } else {
      showToast('Erreur', data.error || 'Erreur lors du scan du programme', 'danger');
    }
  });
  
  // Événement pour l'analyse du programme
  socket.on('program analysis', (data) => {
    if (data.success) {
      missingElements = data.missingElements || [];
      incompleteElements = data.incompleteElements || [];
      
      // Mettre à jour le badge d'analyse
      updateAnalysisBadge();
      
      // Mettre à jour l'interface
      updateProgramKnowledgeUI();
      
      // Afficher un toast
      showToast('Analyse terminée', `Analyse terminée avec ${missingElements.length} éléments manquants et ${incompleteElements.length} éléments incomplets`, 'success');
    } else {
      showToast('Erreur', data.error || 'Erreur lors de l\'analyse du programme', 'danger');
    }
  });
});
