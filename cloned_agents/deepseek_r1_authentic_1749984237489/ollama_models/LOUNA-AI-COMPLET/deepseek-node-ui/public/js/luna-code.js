/**
 * Luna Code - Éditeur de code pour l'interface Luna
 * Permet d'éditer, d'exécuter et de gérer du code dans différents langages
 */

// Initialiser la connexion Socket.IO avec options de reconnexion
let socket;
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 10;
const RECONNECT_DELAY = 2000; // 2 secondes

function initializeSocket() {
  if (typeof io !== 'undefined') {
    // Configuration des options de Socket.IO pour une meilleure stabilité
    socket = io({
      reconnection: true,
      reconnectionAttempts: MAX_RECONNECT_ATTEMPTS,
      reconnectionDelay: RECONNECT_DELAY,
      reconnectionDelayMax: 10000,
      timeout: 20000,
      autoConnect: true
    });

    // Gérer les erreurs de connexion
    socket.on('connect_error', (error) => {
      console.error('Erreur de connexion Socket.IO:', error);
      showNotification('Problème de connexion au serveur. Tentative de reconnexion...', 'warning');
      reconnectAttempts++;

      if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
        showNotification('Impossible de se connecter au serveur après plusieurs tentatives. Veuillez rafraîchir la page.', 'error');
      }
    });

    // Réinitialiser le compteur de tentatives lors d'une connexion réussie
    socket.on('connect', () => {
      reconnectAttempts = 0;
    });
  }
}

// Initialiser la connexion Socket.IO
initializeSocket();

// État global de l'éditeur
const codeEditorState = {
  currentFile: 'main.js',
  files: {
    'main.js': {
      content: '',
      language: 'javascript',
      modified: false
    },
    'styles.css': {
      content: `/* Styles CSS pour l'application */
body {
  font-family: 'Arial', sans-serif;
  background-color: #f5f5f5;
  color: #333;
  margin: 0;
  padding: 20px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.btn {
  padding: 8px 16px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn:hover {
  background-color: #357ae8;
}

.result {
  margin-top: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border-left: 4px solid #4a90e2;
}`,
      language: 'css',
      modified: false
    },
    'index.html': {
      content: `<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Application Luna</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Application Luna</h1>
      <button id="run-btn" class="btn">Exécuter</button>
    </div>

    <div id="app">
      <!-- Le contenu de l'application sera chargé ici -->
    </div>

    <div id="result" class="result">
      <!-- Les résultats seront affichés ici -->
    </div>
  </div>

  <script src="main.js"></script>
</body>
</html>`,
      language: 'html',
      modified: false
    }
  },
  snippets: {
    'function': `/**
 * Description de la fonction
 * @param {type} paramName - Description du paramètre
 * @returns {type} Description de la valeur de retour
 */
function nomDeLaFonction(paramName) {
  // Code de la fonction
  return result;
}`,
    'class': `/**
 * Description de la classe
 */
class NomDeLaClasse {
  /**
   * Constructeur de la classe
   * @param {type} param - Description du paramètre
   */
  constructor(param) {
    this.property = param;
  }

  /**
   * Description de la méthode
   * @param {type} param - Description du paramètre
   * @returns {type} Description de la valeur de retour
   */
  nomDeLaMethode(param) {
    // Code de la méthode
    return result;
  }
}`,
    'fetch': `/**
 * Récupère des données depuis une API
 * @param {string} url - URL de l'API
 * @returns {Promise<Object>} Les données récupérées
 */
async function fetchData(url) {
  try {
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(\`Erreur HTTP: \${response.status}\`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Erreur lors de la récupération des données:", error);
    throw error;
  }
}`,
    'kyber': `/**
 * Classe pour gérer un accélérateur Kyber
 */
class KyberAccelerator {
  /**
   * Crée un nouvel accélérateur Kyber
   * @param {string} name - Nom de l'accélérateur
   * @param {string} type - Type d'accélérateur (memory, thermal, etc.)
   * @param {number} efficiency - Efficacité initiale en pourcentage
   */
  constructor(name, type, efficiency = 100) {
    this.name = name;
    this.type = type;
    this.efficiency = efficiency;
    this.active = true;
    this.temperature = 25; // Température en degrés Celsius
  }

  /**
   * Active l'accélérateur
   * @returns {KyberAccelerator} L'instance actuelle pour chaînage
   */
  activate() {
    this.active = true;
    console.log(\`Accélérateur \${this.name} activé\`);
    return this;
  }

  /**
   * Désactive l'accélérateur
   * @returns {KyberAccelerator} L'instance actuelle pour chaînage
   */
  deactivate() {
    this.active = false;
    console.log(\`Accélérateur \${this.name} désactivé\`);
    return this;
  }

  /**
   * Optimise l'accélérateur
   * @param {number} factor - Facteur d'optimisation
   * @returns {KyberAccelerator} L'instance actuelle pour chaînage
   */
  optimize(factor = 1.1) {
    if (this.active) {
      this.efficiency *= factor;
      this.temperature += 2;
      console.log(\`Accélérateur \${this.name} optimisé: \${this.efficiency.toFixed(1)}%\`);
    } else {
      console.log(\`Impossible d'optimiser l'accélérateur \${this.name} (inactif)\`);
    }
    return this;
  }
}`
  },
  settings: {
    theme: 'dark',
    fontSize: 14,
    autoSave: true,
    indentSize: 2,
    indentType: 'spaces'
  }
};

// Événements Socket.IO
if (socket) {
  socket.on('connect', () => {
    console.log('🔌 Connecté au serveur Luna');
    loadCode();
  });

  socket.on('disconnect', (reason) => {
    console.log(`🔌 Déconnecté du serveur Luna. Raison: ${reason}`);

    // Différentes notifications selon la raison de déconnexion
    if (reason === 'io server disconnect') {
      // Le serveur a forcé la déconnexion
      showNotification('Le serveur a fermé la connexion. Tentative de reconnexion...', 'error');
      // Reconnexion manuelle car le serveur a fermé la connexion
      setTimeout(() => {
        socket.connect();
      }, RECONNECT_DELAY);
    } else if (reason === 'transport close') {
      // Connexion perdue (problème réseau)
      showNotification('Connexion au serveur perdue. Tentative de reconnexion automatique...', 'warning');
    } else {
      // Autres raisons
      showNotification('Connexion au serveur perdue. Tentative de reconnexion...', 'warning');
    }

    // Mettre à jour l'interface pour indiquer la déconnexion
    updateConnectionStatus(false);
  });

  // Événement de reconnexion
  socket.on('reconnect', (attemptNumber) => {
    console.log(`🔌 Reconnecté au serveur Luna après ${attemptNumber} tentative(s)`);
    showNotification('Connexion au serveur rétablie!', 'success');

    // Mettre à jour l'interface pour indiquer la reconnexion
    updateConnectionStatus(true);

    // Recharger les données
    loadCode();
  });

  // Tentative de reconnexion
  socket.on('reconnect_attempt', (attemptNumber) => {
    console.log(`🔌 Tentative de reconnexion ${attemptNumber}/${MAX_RECONNECT_ATTEMPTS}`);
    if (attemptNumber > 1) {
      showNotification(`Tentative de reconnexion ${attemptNumber}/${MAX_RECONNECT_ATTEMPTS}...`, 'info');
    }
  });

  // Échec de reconnexion
  socket.on('reconnect_failed', () => {
    console.log('🔌 Échec de reconnexion après plusieurs tentatives');
    showNotification('Impossible de se reconnecter au serveur. Veuillez rafraîchir la page.', 'error');
  });

  // Chargement du code
  socket.on('code loaded', (data) => {
    if (data.success) {
      if (data.files) {
        codeEditorState.files = data.files;
      }
      updateEditor();
      console.log('Code chargé avec succès');
    } else {
      console.error('Erreur lors du chargement du code:', data.error);
      showNotification('Erreur lors du chargement du code', 'error');
    }
  });

  // Sauvegarde du code
  socket.on('code saved', (data) => {
    if (data.success) {
      codeEditorState.files[codeEditorState.currentFile].modified = false;
      updateFileTabs();
      showNotification('Code sauvegardé avec succès', 'success');
    } else {
      console.error('Erreur lors de la sauvegarde du code:', data.error);
      showNotification('Erreur lors de la sauvegarde', 'error');
    }
  });

  // Exécution du code
  socket.on('code executed', (data) => {
    if (data.success) {
      $('#code-output').html(data.output);
      showNotification('Code exécuté avec succès', 'success');
    } else {
      $('#code-output').html(`<span style="color: #f07178;">Erreur: ${data.error}</span>`);
      showNotification('Erreur lors de l\'exécution', 'error');
    }
  });
}

// Fonctions principales

// Charger le code depuis le serveur
function loadCode() {
  if (socket) {
    socket.emit('load code');
  } else {
    // Utiliser le code par défaut si pas de connexion
    updateEditor();
  }
}

// Mettre à jour l'éditeur avec le fichier actuel
function updateEditor() {
  const file = codeEditorState.files[codeEditorState.currentFile];
  const editor = $('#code-editor');

  // Mettre à jour le contenu
  editor.val(file.content || '');

  // Mettre à jour le sélecteur de langage
  $('#language-selector').val(file.language);
  $('#language-indicator').text(file.language.charAt(0).toUpperCase() + file.language.slice(1));

  // Mettre à jour les onglets
  updateFileTabs();

  // Mettre à jour les numéros de ligne
  updateLineNumbers();

  // Mettre à jour la position du curseur
  updateCursorPosition();
}

// Mettre à jour les onglets de fichiers
function updateFileTabs() {
  const tabs = $('.code-tab').not('.add-tab');

  tabs.removeClass('active');
  tabs.each(function() {
    const filename = $(this).data('file');
    if (filename === codeEditorState.currentFile) {
      $(this).addClass('active');
    }

    // Ajouter un indicateur si le fichier est modifié
    if (codeEditorState.files[filename] && codeEditorState.files[filename].modified) {
      if (!$(this).find('.modified-indicator').length) {
        $(this).append('<span class="modified-indicator ms-1">●</span>');
      }
    } else {
      $(this).find('.modified-indicator').remove();
    }
  });
}

// Mettre à jour les numéros de ligne
function updateLineNumbers() {
  const content = $('#code-editor').val();
  const lines = content.split('\n');
  const lineNumbers = $('#line-numbers');

  lineNumbers.empty();

  for (let i = 0; i < lines.length; i++) {
    lineNumbers.append('<span></span>');
  }
}

// Mettre à jour la position du curseur
function updateCursorPosition() {
  const editor = document.getElementById('code-editor');
  const cursorPosition = editor.selectionStart;
  const content = editor.value.substring(0, cursorPosition);
  const lines = content.split('\n');
  const currentLine = lines.length;
  const currentColumn = lines[lines.length - 1].length + 1;

  $('#cursor-position').text(`Ligne: ${currentLine}, Colonne: ${currentColumn}`);
}

// Sauvegarder le code actuel
function saveCurrentCode() {
  const content = $('#code-editor').val();
  const file = codeEditorState.files[codeEditorState.currentFile];

  file.content = content;
  file.modified = false;

  if (socket) {
    socket.emit('save code', {
      filename: codeEditorState.currentFile,
      content: content,
      language: file.language
    });
  } else {
    showNotification('Code sauvegardé localement', 'success');
    updateFileTabs();
  }
}

// Exécuter le code actuel
function executeCode() {
  const content = $('#code-editor').val();
  const language = codeEditorState.files[codeEditorState.currentFile].language;

  if (socket) {
    socket.emit('execute code', {
      code: content,
      language: language
    });
  } else {
    // Simulation d'exécution locale
    $('#code-output').html(`> Code ${language} exécuté localement\n> Résultat simulé`);
    showNotification('Exécution simulée (pas de connexion au serveur)', 'info');
  }
}

// Formater le code
function formatCode() {
  const content = $('#code-editor').val();
  const language = codeEditorState.files[codeEditorState.currentFile].language;

  // Ici, on pourrait utiliser une bibliothèque comme prettier
  // Pour l'instant, on simule le formatage
  showNotification('Formatage du code effectué', 'success');
}

// Insérer un snippet de code
function insertSnippet(snippetName) {
  const snippet = codeEditorState.snippets[snippetName];
  if (snippet) {
    const editor = document.getElementById('code-editor');
    const start = editor.selectionStart;
    const end = editor.selectionEnd;
    const content = editor.value;

    // Insérer le snippet à la position du curseur
    editor.value = content.substring(0, start) + snippet + content.substring(end);

    // Marquer le fichier comme modifié
    codeEditorState.files[codeEditorState.currentFile].modified = true;
    updateFileTabs();

    // Mettre à jour les numéros de ligne
    updateLineNumbers();

    // Placer le curseur après le snippet
    editor.selectionStart = start + snippet.length;
    editor.selectionEnd = start + snippet.length;
    editor.focus();
  }
}

/**
 * Met à jour l'indicateur de statut de connexion dans l'interface
 * @param {boolean} connected - État de la connexion
 */
function updateConnectionStatus(connected) {
  // Mettre à jour l'indicateur de statut du système
  const statusIndicator = $('#systemStatus .status-indicator');
  const statusText = $('#systemStatus span:not(.status-indicator)');
  const toggleButton = $('#toggleSystem');

  if (connected) {
    statusIndicator.removeClass('status-inactive').addClass('status-active');
    statusText.text('Actif');
    toggleButton.html('<i class="bi bi-power"></i> Désactiver');
    toggleButton.removeClass('btn-success').addClass('btn-danger');

    // Ajouter un indicateur visuel dans la barre de navigation
    $('.connection-status-indicator').removeClass('disconnected').addClass('connected')
      .attr('title', 'Connecté au serveur');
  } else {
    statusIndicator.removeClass('status-active').addClass('status-inactive');
    statusText.text('Déconnecté');
    toggleButton.html('<i class="bi bi-power"></i> Reconnecter');
    toggleButton.removeClass('btn-danger').addClass('btn-success');

    // Ajouter un indicateur visuel dans la barre de navigation
    $('.connection-status-indicator').removeClass('connected').addClass('disconnected')
      .attr('title', 'Déconnecté du serveur');
  }

  // Mettre à jour l'état des boutons qui nécessitent une connexion
  if (connected) {
    $('#run-code-btn, #save-code-btn').prop('disabled', false);
  } else {
    $('#run-code-btn, #save-code-btn').prop('disabled', true);
  }
}

/**
 * Affiche une notification à l'utilisateur
 * @param {string} message - Message à afficher
 * @param {string} type - Type de notification (info, success, warning, error)
 */
function showNotification(message, type = 'info') {
  // Créer l'élément de notification s'il n'existe pas
  let notificationArea = $('#notification-area');
  if (notificationArea.length === 0) {
    $('body').append('<div id="notification-area" style="position: fixed; top: 20px; right: 20px; z-index: 9999; width: 300px;"></div>');
    notificationArea = $('#notification-area');
  }

  // Créer la notification avec un bouton de fermeture
  const notification = $(`
    <div class="alert alert-${type} alert-dismissible fade show" style="margin-bottom: 10px; opacity: 0; transition: opacity 0.3s ease;">
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
  `);

  notificationArea.append(notification);

  // Afficher avec animation
  setTimeout(() => {
    notification.css('opacity', '1');

    // Masquer après 5 secondes (plus long pour les erreurs)
    const displayTime = type === 'error' ? 8000 : 5000;
    setTimeout(() => {
      notification.css('opacity', '0');
      setTimeout(() => notification.remove(), 300);
    }, displayTime);
  }, 100);

  // Gérer le clic sur le bouton de fermeture
  notification.find('.btn-close').on('click', function() {
    notification.css('opacity', '0');
    setTimeout(() => notification.remove(), 300);
  });
}

// Initialisation au chargement de la page
$(document).ready(function() {
  // Charger le code initial
  const initialCode = $('#code-editor').val();
  codeEditorState.files[codeEditorState.currentFile].content = initialCode;

  // Mettre à jour les numéros de ligne
  updateLineNumbers();

  // Gestionnaire d'événements pour l'éditeur
  $('#code-editor').on('input', function() {
    // Marquer le fichier comme modifié
    codeEditorState.files[codeEditorState.currentFile].modified = true;
    updateFileTabs();

    // Mettre à jour les numéros de ligne
    updateLineNumbers();
  });

  $('#code-editor').on('keydown', function(e) {
    // Indentation avec Tab
    if (e.key === 'Tab') {
      e.preventDefault();
      const start = this.selectionStart;
      const end = this.selectionEnd;

      // Insérer des espaces ou une tabulation
      if (codeEditorState.settings.indentType === 'spaces') {
        const spaces = ' '.repeat(codeEditorState.settings.indentSize);
        this.value = this.value.substring(0, start) + spaces + this.value.substring(end);
        this.selectionStart = this.selectionEnd = start + spaces.length;
      } else {
        this.value = this.value.substring(0, start) + '\t' + this.value.substring(end);
        this.selectionStart = this.selectionEnd = start + 1;
      }

      // Marquer le fichier comme modifié
      codeEditorState.files[codeEditorState.currentFile].modified = true;
      updateFileTabs();
    }
  });

  $('#code-editor').on('click keyup', function() {
    // Mettre à jour la position du curseur
    updateCursorPosition();
  });

  // Gestionnaire pour le changement de langage
  $('#language-selector').on('change', function() {
    const language = $(this).val();
    codeEditorState.files[codeEditorState.currentFile].language = language;
    $('#language-indicator').text(language.charAt(0).toUpperCase() + language.slice(1));
  });

  // Gestionnaires pour les boutons
  $('#save-code-btn').on('click', saveCurrentCode);
  $('#run-code-btn').on('click', executeCode);
  $('#format-code-btn').on('click', formatCode);

  // Gestionnaire pour le bouton de copie
  $('#copy-code-btn').on('click', function() {
    const content = $('#code-editor').val();
    navigator.clipboard.writeText(content).then(() => {
      showNotification('Code copié dans le presse-papiers', 'success');
      $(this).html('<i class="bi bi-check"></i> Copié');
      setTimeout(() => {
        $(this).html('<i class="bi bi-clipboard"></i> Copier');
      }, 2000);
    }).catch(err => {
      showNotification('Erreur lors de la copie', 'error');
      console.error('Erreur lors de la copie:', err);
    });
  });

  // Gestionnaire pour les onglets de fichiers
  $('.code-tab').not('.add-tab').on('click', function() {
    const filename = $(this).data('file');
    if (filename !== codeEditorState.currentFile) {
      codeEditorState.currentFile = filename;
      updateEditor();
    }
  });

  // Gestionnaire pour les snippets
  $('.list-group-item[data-snippet]').on('click', function(e) {
    e.preventDefault();
    const snippetName = $(this).data('snippet');
    insertSnippet(snippetName);
  });

  // Gestionnaire pour la taille de police
  $('#font-size').on('input', function() {
    const fontSize = $(this).val();
    $('#code-editor').css('font-size', `${fontSize}px`);
    codeEditorState.settings.fontSize = parseInt(fontSize);
  });

  // Gestionnaire pour le thème
  $('#theme-selector').on('change', function() {
    const theme = $(this).val();
    codeEditorState.settings.theme = theme;

    // Appliquer le thème
    if (theme === 'light') {
      $('#code-editor').css({
        'background-color': '#f5f5f5',
        'color': '#333'
      });
    } else if (theme === 'dark') {
      $('#code-editor').css({
        'background-color': 'rgba(26, 26, 46, 0.95)',
        'color': '#e0e0e0'
      });
    } else if (theme === 'luna') {
      $('#code-editor').css({
        'background-color': 'rgba(26, 26, 46, 0.95)',
        'color': '#f0a6ca'
      });
    }
  });

  // Initialiser l'éditeur
  updateEditor();

  console.log('Éditeur de code Luna initialisé');
});
