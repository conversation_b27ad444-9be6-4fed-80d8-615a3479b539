/**
 * Luna Accelerators Hierarchy - Gestion de la hiérarchie des accélérateurs Kyber
 * Ce script gère la visualisation et le contrôle des chefs d'orchestre et des accélérateurs en cascade
 */

// État global de la hiérarchie des accélérateurs
const hierarchyState = {
  masterConductor: {
    efficiency: 187.5,
    activeAccelerators: 16,
    totalAccelerators: 16,
    temperature: 42.3,
    power: 87
  },
  zoneConductors: {
    reflection: {
      efficiency: 192.3,
      accelerators: 4,
      power: 92,
      acceleratorsList: []
    },
    memory: {
      efficiency: 178.6,
      accelerators: 4,
      power: 85,
      acceleratorsList: []
    },
    processing: {
      efficiency: 201.4,
      accelerators: 4,
      power: 95,
      acceleratorsList: []
    },
    creativity: {
      efficiency: 167.8,
      accelerators: 4,
      power: 78,
      acceleratorsList: []
    }
  },
  dataFlow: {
    throughput: 1.28,
    latency: 12.4,
    efficiency: 187.5,
    history: []
  },
  efficiencyTrends: {
    hour: 2.3,
    day: 8.7,
    week: 15.2
  },
  advancedStats: {
    optimizationCycles: 1284,
    neuralConnections: 8192,
    generation: "v4.2"
  }
};

// Références aux graphiques
let dataFlowChart = null;
let zonePerformanceChart = null;

// Connexion Socket.io
let socket;

// Initialisation au chargement du document
document.addEventListener('DOMContentLoaded', function() {
  // Initialiser la connexion Socket.io (si pas déjà initialisée par luna-accelerators.js)
  if (typeof socket === 'undefined') {
    socket = io();
  }

  // Initialiser les gestionnaires d'événements
  initializeEventHandlers();

  // Initialiser les graphiques
  initializeCharts();

  // Initialiser les accélérateurs
  initializeAccelerators();

  // Initialiser les connexions visuelles
  initializeConnections();

  // Démarrer les animations de flux de données
  startDataFlowAnimation();

  console.log('Luna Accelerators Hierarchy initialized');
});

/**
 * Initialise les gestionnaires d'événements
 */
function initializeEventHandlers() {
  // Gestionnaire pour le bouton d'optimisation
  document.getElementById('optimize-accelerators-btn').addEventListener('click', function() {
    optimizeAllAccelerators();
  });

  // Gestionnaire pour le bouton d'équilibrage
  document.getElementById('balance-load-btn').addEventListener('click', function() {
    balanceAcceleratorsLoad();
  });

  // Gestionnaire pour le bouton de réinitialisation
  document.getElementById('reset-accelerators-btn').addEventListener('click', function() {
    resetAccelerators();
  });

  // Gestionnaires pour les sliders
  document.getElementById('master-power').addEventListener('input', function() {
    const value = this.value;
    document.getElementById('master-power-value').textContent = `${value}%`;
    updateMasterPower(value);
  });

  document.getElementById('thermal-threshold').addEventListener('input', function() {
    const value = this.value;
    document.getElementById('thermal-threshold-value').textContent = `${value}°C`;
    updateThermalThreshold(value);
  });

  document.getElementById('cascade-depth').addEventListener('input', function() {
    const value = this.value;
    document.getElementById('cascade-depth-value').textContent = value;
    updateCascadeDepth(value);
  });
}

/**
 * Initialise les graphiques
 */
function initializeCharts() {
  // Graphique de flux de données
  const dataFlowCtx = document.getElementById('data-flow-chart').getContext('2d');

  // Générer des données aléatoires pour le graphique
  const labels = Array.from({length: 30}, (_, i) => i);
  const data = Array.from({length: 30}, () => Math.random() * 2 + 0.5);

  dataFlowChart = new Chart(dataFlowCtx, {
    type: 'line',
    data: {
      labels: labels,
      datasets: [{
        label: 'Débit de données (GB/s)',
        data: data,
        borderColor: 'rgba(240, 166, 202, 1)',
        backgroundColor: 'rgba(240, 166, 202, 0.2)',
        tension: 0.4,
        fill: true
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      animation: {
        duration: 500
      },
      scales: {
        y: {
          beginAtZero: true,
          max: 3,
          ticks: {
            color: 'rgba(255, 255, 255, 0.7)'
          },
          grid: {
            color: 'rgba(255, 255, 255, 0.1)'
          }
        },
        x: {
          display: false
        }
      },
      plugins: {
        legend: {
          display: false
        }
      }
    }
  });

  // Graphique de performance par zone
  const zonePerformanceCtx = document.getElementById('zone-performance-chart').getContext('2d');

  zonePerformanceChart = new Chart(zonePerformanceCtx, {
    type: 'radar',
    data: {
      labels: ['Réflexion', 'Mémoire', 'Traitement', 'Créativité'],
      datasets: [{
        label: 'Efficacité (%)',
        data: [
          hierarchyState.zoneConductors.reflection.efficiency,
          hierarchyState.zoneConductors.memory.efficiency,
          hierarchyState.zoneConductors.processing.efficiency,
          hierarchyState.zoneConductors.creativity.efficiency
        ],
        backgroundColor: 'rgba(156, 137, 184, 0.2)',
        borderColor: 'rgba(156, 137, 184, 1)',
        pointBackgroundColor: 'rgba(240, 166, 202, 1)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(240, 166, 202, 1)'
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        r: {
          angleLines: {
            color: 'rgba(255, 255, 255, 0.1)'
          },
          grid: {
            color: 'rgba(255, 255, 255, 0.1)'
          },
          pointLabels: {
            color: 'rgba(255, 255, 255, 0.7)'
          },
          ticks: {
            backdropColor: 'transparent',
            color: 'rgba(255, 255, 255, 0.7)'
          },
          min: 0,
          max: 250
        }
      },
      plugins: {
        legend: {
          display: false
        }
      }
    }
  });
}

/**
 * Initialise les accélérateurs pour chaque zone
 */
function initializeAccelerators() {
  // Créer les accélérateurs pour chaque zone
  createZoneAccelerators('reflection', 4);
  createZoneAccelerators('memory', 4);
  createZoneAccelerators('processing', 4);
  createZoneAccelerators('creativity', 4);

  // Mettre à jour les statistiques
  updateStatistics();
}

/**
 * Crée les accélérateurs pour une zone spécifique
 * @param {string} zone - Nom de la zone
 * @param {number} count - Nombre d'accélérateurs à créer
 */
function createZoneAccelerators(zone, count) {
  const container = document.getElementById(`${zone}-accelerators`);
  if (!container) return;

  // Vider le conteneur
  container.innerHTML = '';

  // Créer les accélérateurs
  for (let i = 1; i <= count; i++) {
    // Générer des valeurs aléatoires pour chaque accélérateur
    const efficiency = Math.floor(Math.random() * 30) + 160;
    const power = Math.floor(Math.random() * 20) + 75;

    // Créer l'élément HTML
    const acceleratorElement = document.createElement('div');
    acceleratorElement.className = 'accelerator';
    acceleratorElement.innerHTML = `
      <div class="accelerator-header">
        <div class="accelerator-name">Kyber-${zone.charAt(0).toUpperCase() + zone.slice(1)}-${i}</div>
        <div class="accelerator-status">
          <span class="status-indicator status-active"></span>
        </div>
      </div>
      <div class="accelerator-body">
        <div class="accelerator-efficiency">${efficiency}%</div>
        <div class="accelerator-meter">
          <div class="accelerator-fill" style="width: ${power}%;"></div>
        </div>
        <div class="meter-value">${power}%</div>
      </div>
    `;

    // Ajouter au conteneur
    container.appendChild(acceleratorElement);

    // Stocker dans l'état
    hierarchyState.zoneConductors[zone].acceleratorsList.push({
      id: `kyber-${zone}-${i}`,
      efficiency: efficiency,
      power: power
    });
  }
}

/**
 * Initialise les connexions visuelles entre les chefs d'orchestre
 */
function initializeConnections() {
  // Obtenir les dimensions du conteneur
  const container = document.querySelector('.conductor-connections');
  if (!container) return;

  const width = container.offsetWidth;
  const height = container.offsetHeight;

  // Créer les connexions vers chaque zone
  const zones = ['reflection', 'memory', 'processing', 'creativity'];
  zones.forEach((zone, index) => {
    const line = document.getElementById(`connection-to-${zone}`);
    if (line) {
      // Calculer l'angle de la connexion
      const angle = -45 + (index * 30);
      const length = Math.min(width, height) * 0.8;

      // Positionner la ligne
      line.style.width = `${length}px`;
      line.style.top = `${height / 2}px`;
      line.style.left = `${width / 2}px`;
      line.style.transform = `rotate(${angle}deg)`;

      // Ajouter une animation de pulsation
      line.style.animation = `pulse-line ${1 + index * 0.2}s infinite`;
    }
  });
}

/**
 * Démarre l'animation de flux de données
 */
function startDataFlowAnimation() {
  // Mettre à jour le graphique de flux de données toutes les secondes
  setInterval(updateDataFlowChart, 1000);

  // Mettre à jour l'activité des accélérateurs toutes les 2 secondes
  setInterval(updateAcceleratorsActivity, 2000);

  // Créer des particules de données
  createDataParticles();

  // Créer des particules sur les connexions toutes les 500ms
  setInterval(createConnectionParticles, 500);
}

/**
 * Met à jour l'activité des accélérateurs individuels
 */
function updateAcceleratorsActivity() {
  const zones = ['reflection', 'memory', 'processing', 'creativity'];

  zones.forEach(zone => {
    // Mettre à jour chaque accélérateur dans la zone
    hierarchyState.zoneConductors[zone].acceleratorsList.forEach((acc, index) => {
      // Faire varier légèrement l'efficacité et la puissance
      acc.efficiency = acc.efficiency * (0.98 + Math.random() * 0.04);
      acc.power = acc.power * (0.98 + Math.random() * 0.04);

      // Limiter les valeurs
      acc.efficiency = Math.max(100, Math.min(250, acc.efficiency));
      acc.power = Math.max(50, Math.min(100, acc.power));

      // Mettre à jour l'interface
      const accElement = document.querySelector(`#${zone}-accelerators .accelerator:nth-child(${index + 1})`);
      if (accElement) {
        accElement.querySelector('.accelerator-efficiency').textContent = `${Math.round(acc.efficiency)}%`;
        accElement.querySelector('.accelerator-fill').style.width = `${acc.power}%`;
        accElement.querySelector('.meter-value').textContent = `${Math.round(acc.power)}%`;

        // Ajouter/supprimer la classe active de manière aléatoire pour la barre
        if (Math.random() > 0.5) {
          accElement.querySelector('.accelerator-fill').classList.add('active');
        } else {
          accElement.querySelector('.accelerator-fill').classList.remove('active');
        }

        // Ajouter/supprimer la classe active de manière aléatoire pour l'accélérateur
        if (Math.random() > 0.7) {
          accElement.classList.add('active');
        } else {
          accElement.classList.remove('active');
        }

        // Faire clignoter l'indicateur de statut de manière aléatoire
        const statusIndicator = accElement.querySelector('.status-indicator');
        if (Math.random() > 0.9) {
          statusIndicator.classList.remove('status-active');
          statusIndicator.classList.add('status-inactive');

          // Remettre actif après un court délai
          setTimeout(() => {
            statusIndicator.classList.remove('status-inactive');
            statusIndicator.classList.add('status-active');
          }, 300 + Math.random() * 700);
        }
      }
    });
  });
}

/**
 * Crée des particules sur les connexions entre les chefs d'orchestre
 */
function createConnectionParticles() {
  const connections = document.querySelectorAll('.connection-line');

  connections.forEach(connection => {
    // Créer une particule
    const particle = document.createElement('div');
    particle.className = 'data-particle connection-particle';

    // Positionner aléatoirement le long de la connexion
    const position = Math.random() * 100;
    particle.style.left = `${position}%`;
    particle.style.top = '-3px';

    // Ajouter au conteneur
    connection.appendChild(particle);

    // Supprimer après l'animation
    setTimeout(() => {
      particle.remove();
    }, 2000);
  });
}

/**
 * Crée des particules pour visualiser le flux de données
 */
function createDataParticles() {
  const container = document.querySelector('.data-flow-container');
  if (!container) return;

  // Créer 20 particules
  for (let i = 0; i < 20; i++) {
    const particle = document.createElement('div');
    particle.className = 'data-particle';

    // Positionner aléatoirement
    const top = Math.random() * 100;
    particle.style.top = `${top}%`;

    // Ajouter un délai aléatoire
    const delay = Math.random() * 3;
    particle.style.animationDelay = `${delay}s`;

    // Ajouter au conteneur
    container.appendChild(particle);
  }
}

/**
 * Met à jour le graphique de flux de données
 */
function updateDataFlowChart() {
  // Ajouter une nouvelle valeur
  const newValue = hierarchyState.dataFlow.throughput * (0.9 + Math.random() * 0.2);

  // Mettre à jour les données
  dataFlowChart.data.labels.push(dataFlowChart.data.labels.length);
  dataFlowChart.data.datasets[0].data.push(newValue);

  // Supprimer la première valeur si plus de 30 points
  if (dataFlowChart.data.labels.length > 30) {
    dataFlowChart.data.labels.shift();
    dataFlowChart.data.datasets[0].data.shift();
  }

  // Mettre à jour le graphique
  dataFlowChart.update();

  // Mettre à jour les statistiques de flux
  const latency = hierarchyState.dataFlow.latency * (0.95 + Math.random() * 0.1);
  document.getElementById('data-throughput').textContent = `${newValue.toFixed(2)} GB/s`;
  document.getElementById('data-latency').textContent = `${latency.toFixed(1)} ms`;

  // Stocker dans l'historique
  hierarchyState.dataFlow.throughput = newValue;
  hierarchyState.dataFlow.latency = latency;
  hierarchyState.dataFlow.history.push(newValue);

  // Limiter la taille de l'historique
  if (hierarchyState.dataFlow.history.length > 100) {
    hierarchyState.dataFlow.history.shift();
  }

  // Mettre à jour les barres de progression en temps réel
  updateProgressBars();
}

/**
 * Met à jour les barres de progression en temps réel
 */
function updateProgressBars() {
  // Générer des valeurs aléatoires pour simuler l'activité
  const dataProcessing = Math.floor(70 + Math.random() * 25);
  const neuralSync = Math.floor(85 + Math.random() * 15);
  const thermalTransfer = Math.floor(60 + Math.random() * 30);
  const cascadeOptimization = Math.floor(75 + Math.random() * 20);

  // Mettre à jour les valeurs et les barres
  document.getElementById('data-processing-value').textContent = `${dataProcessing}%`;
  document.getElementById('data-processing-bar').style.width = `${dataProcessing}%`;

  document.getElementById('neural-sync-value').textContent = `${neuralSync}%`;
  document.getElementById('neural-sync-bar').style.width = `${neuralSync}%`;

  document.getElementById('thermal-transfer-value').textContent = `${thermalTransfer}%`;
  document.getElementById('thermal-transfer-bar').style.width = `${thermalTransfer}%`;

  document.getElementById('cascade-optimization-value').textContent = `${cascadeOptimization}%`;
  document.getElementById('cascade-optimization-bar').style.width = `${cascadeOptimization}%`;

  // Mettre à jour les badges
  document.querySelector('.badge.bg-warning').textContent = `Température: ${hierarchyState.masterConductor.temperature.toFixed(1)}°C`;
  document.querySelector('.badge.bg-primary').textContent = `Efficacité: ${hierarchyState.dataFlow.efficiency.toFixed(1)}%`;
}

/**
 * Met à jour les statistiques globales
 */
function updateStatistics() {
  // Mettre à jour les statistiques du chef d'orchestre principal
  document.getElementById('master-efficiency').textContent = `${hierarchyState.masterConductor.efficiency}%`;
  document.getElementById('active-accelerators').textContent = `${hierarchyState.masterConductor.activeAccelerators}/${hierarchyState.masterConductor.totalAccelerators}`;
  document.getElementById('system-temp').textContent = `${hierarchyState.masterConductor.temperature}°C`;

  // Mettre à jour les statistiques des chefs de zone
  document.getElementById('reflection-efficiency').textContent = `${hierarchyState.zoneConductors.reflection.efficiency}%`;
  document.getElementById('reflection-count').textContent = hierarchyState.zoneConductors.reflection.accelerators;

  document.getElementById('memory-efficiency').textContent = `${hierarchyState.zoneConductors.memory.efficiency}%`;
  document.getElementById('memory-count').textContent = hierarchyState.zoneConductors.memory.accelerators;

  document.getElementById('processing-efficiency').textContent = `${hierarchyState.zoneConductors.processing.efficiency}%`;
  document.getElementById('processing-count').textContent = hierarchyState.zoneConductors.processing.accelerators;

  document.getElementById('creativity-efficiency').textContent = `${hierarchyState.zoneConductors.creativity.efficiency}%`;
  document.getElementById('creativity-count').textContent = hierarchyState.zoneConductors.creativity.accelerators;

  // Mettre à jour les tendances d'efficacité
  document.getElementById('efficiency-hour').textContent = `+${hierarchyState.efficiencyTrends.hour}%`;
  document.getElementById('efficiency-day').textContent = `+${hierarchyState.efficiencyTrends.day}%`;
  document.getElementById('efficiency-week').textContent = `+${hierarchyState.efficiencyTrends.week}%`;

  // Mettre à jour l'efficacité du flux de données
  document.getElementById('data-efficiency').textContent = `${hierarchyState.dataFlow.efficiency}%`;
}

/**
 * Optimise tous les accélérateurs
 */
function optimizeAllAccelerators() {
  // Désactiver le bouton pendant l'optimisation
  const button = document.getElementById('optimize-accelerators-btn');
  button.disabled = true;
  button.innerHTML = '<i class="bi bi-arrow-repeat spin me-1"></i> Optimisation en cours...';

  // Simuler l'optimisation
  showNotification('Optimisation de tous les accélérateurs en cours...', 'info');

  // Animer les connexions
  animateConnections();

  // Mettre à jour progressivement les valeurs
  let progress = 0;
  const interval = setInterval(() => {
    progress += 10;

    // Mettre à jour les valeurs d'efficacité
    if (progress <= 100) {
      // Augmenter progressivement l'efficacité
      const factor = progress / 100;
      updateEfficiencyValues(factor);
    }

    // Terminer l'optimisation
    if (progress >= 100) {
      clearInterval(interval);

      // Réactiver le bouton
      button.disabled = false;
      button.innerHTML = '<i class="bi bi-lightning-charge me-1"></i> Optimiser tous les accélérateurs';

      // Afficher une notification
      showNotification('Tous les accélérateurs ont été optimisés avec succès', 'success');

      // Mettre à jour les statistiques finales
      updateStatistics();
    }
  }, 200);
}

/**
 * Met à jour les valeurs d'efficacité pendant l'optimisation
 * @param {number} factor - Facteur de progression (0-1)
 */
function updateEfficiencyValues(factor) {
  // Augmenter l'efficacité du chef d'orchestre principal
  const masterIncrease = 15 * factor;
  hierarchyState.masterConductor.efficiency = Math.min(250, hierarchyState.masterConductor.efficiency + masterIncrease);
  document.getElementById('master-efficiency').textContent = `${hierarchyState.masterConductor.efficiency.toFixed(1)}%`;

  // Augmenter l'efficacité des chefs de zone
  const zones = ['reflection', 'memory', 'processing', 'creativity'];
  zones.forEach(zone => {
    const zoneIncrease = (20 + Math.random() * 10) * factor;
    hierarchyState.zoneConductors[zone].efficiency = Math.min(250, hierarchyState.zoneConductors[zone].efficiency + zoneIncrease);
    document.getElementById(`${zone}-efficiency`).textContent = `${hierarchyState.zoneConductors[zone].efficiency.toFixed(1)}%`;

    // Mettre à jour la barre de progression
    const meterFill = document.querySelector(`.zone-conductor[data-zone="${zone}"] .meter-fill`);
    if (meterFill) {
      const newPower = Math.min(100, hierarchyState.zoneConductors[zone].power + 5 * factor);
      meterFill.style.width = `${newPower}%`;
      document.querySelector(`.zone-conductor[data-zone="${zone}"] .meter-value`).textContent = `${Math.round(newPower)}%`;
      hierarchyState.zoneConductors[zone].power = newPower;
    }

    // Mettre à jour les accélérateurs individuels
    hierarchyState.zoneConductors[zone].acceleratorsList.forEach((acc, index) => {
      const accIncrease = (15 + Math.random() * 15) * factor;
      acc.efficiency = Math.min(250, acc.efficiency + accIncrease);
      acc.power = Math.min(100, acc.power + 5 * factor);

      const accElement = document.querySelector(`#${zone}-accelerators .accelerator:nth-child(${index + 1})`);
      if (accElement) {
        accElement.querySelector('.accelerator-efficiency').textContent = `${Math.round(acc.efficiency)}%`;
        accElement.querySelector('.accelerator-fill').style.width = `${acc.power}%`;
        accElement.querySelector('.meter-value').textContent = `${Math.round(acc.power)}%`;
      }
    });
  });

  // Mettre à jour le graphique de performance par zone
  zonePerformanceChart.data.datasets[0].data = [
    hierarchyState.zoneConductors.reflection.efficiency,
    hierarchyState.zoneConductors.memory.efficiency,
    hierarchyState.zoneConductors.processing.efficiency,
    hierarchyState.zoneConductors.creativity.efficiency
  ];
  zonePerformanceChart.update();

  // Mettre à jour l'efficacité du flux de données
  hierarchyState.dataFlow.efficiency = Math.min(250, hierarchyState.dataFlow.efficiency + 10 * factor);
  document.getElementById('data-efficiency').textContent = `${hierarchyState.dataFlow.efficiency.toFixed(1)}%`;
}

/**
 * Anime les connexions entre les chefs d'orchestre
 */
function animateConnections() {
  const connections = document.querySelectorAll('.connection-line');

  connections.forEach(connection => {
    // Ajouter une classe pour l'animation
    connection.classList.add('active-connection');

    // Créer des particules de données le long de la connexion
    for (let i = 0; i < 5; i++) {
      const particle = document.createElement('div');
      particle.className = 'data-particle connection-particle';

      // Positionner aléatoirement le long de la connexion
      const position = Math.random() * 100;
      particle.style.left = `${position}%`;
      particle.style.top = '-3px';

      // Ajouter un délai aléatoire
      const delay = Math.random() * 0.5;
      particle.style.animationDelay = `${delay}s`;

      // Ajouter au conteneur
      connection.appendChild(particle);

      // Supprimer après l'animation
      setTimeout(() => {
        particle.remove();
      }, 2000);
    }

    // Retirer la classe après l'animation
    setTimeout(() => {
      connection.classList.remove('active-connection');
    }, 2000);
  });
}

/**
 * Équilibre la charge des accélérateurs
 */
function balanceAcceleratorsLoad() {
  // Désactiver le bouton pendant l'équilibrage
  const button = document.getElementById('balance-load-btn');
  button.disabled = true;
  button.innerHTML = '<i class="bi bi-arrow-repeat spin me-1"></i> Équilibrage en cours...';

  // Simuler l'équilibrage
  showNotification('Équilibrage de la charge des accélérateurs en cours...', 'info');

  // Simuler un délai d'équilibrage
  setTimeout(() => {
    // Équilibrer les valeurs d'efficacité
    balanceEfficiencyValues();

    // Réactiver le bouton
    button.disabled = false;
    button.innerHTML = '<i class="bi bi-arrow-repeat me-1"></i> Équilibrer la charge';

    // Afficher une notification
    showNotification('Charge des accélérateurs équilibrée avec succès', 'success');
  }, 2500);
}

/**
 * Équilibre les valeurs d'efficacité entre les zones
 */
function balanceEfficiencyValues() {
  // Calculer l'efficacité moyenne
  const zones = ['reflection', 'memory', 'processing', 'creativity'];
  let totalEfficiency = 0;

  zones.forEach(zone => {
    totalEfficiency += hierarchyState.zoneConductors[zone].efficiency;
  });

  const avgEfficiency = totalEfficiency / zones.length;

  // Équilibrer les valeurs
  zones.forEach(zone => {
    const currentEfficiency = hierarchyState.zoneConductors[zone].efficiency;
    const targetEfficiency = avgEfficiency * (0.9 + Math.random() * 0.2);
    hierarchyState.zoneConductors[zone].efficiency = targetEfficiency;

    // Mettre à jour l'interface
    document.getElementById(`${zone}-efficiency`).textContent = `${targetEfficiency.toFixed(1)}%`;

    // Mettre à jour la barre de progression
    const newPower = 75 + Math.random() * 15;
    const meterFill = document.querySelector(`.zone-conductor[data-zone="${zone}"] .meter-fill`);
    if (meterFill) {
      meterFill.style.width = `${newPower}%`;
      document.querySelector(`.zone-conductor[data-zone="${zone}"] .meter-value`).textContent = `${Math.round(newPower)}%`;
      hierarchyState.zoneConductors[zone].power = newPower;
    }

    // Équilibrer les accélérateurs individuels
    hierarchyState.zoneConductors[zone].acceleratorsList.forEach((acc, index) => {
      acc.efficiency = targetEfficiency * (0.9 + Math.random() * 0.2);
      acc.power = newPower * (0.9 + Math.random() * 0.2);

      const accElement = document.querySelector(`#${zone}-accelerators .accelerator:nth-child(${index + 1})`);
      if (accElement) {
        accElement.querySelector('.accelerator-efficiency').textContent = `${Math.round(acc.efficiency)}%`;
        accElement.querySelector('.accelerator-fill').style.width = `${acc.power}%`;
        accElement.querySelector('.meter-value').textContent = `${Math.round(acc.power)}%`;
      }
    });
  });

  // Mettre à jour le graphique de performance par zone
  zonePerformanceChart.data.datasets[0].data = [
    hierarchyState.zoneConductors.reflection.efficiency,
    hierarchyState.zoneConductors.memory.efficiency,
    hierarchyState.zoneConductors.processing.efficiency,
    hierarchyState.zoneConductors.creativity.efficiency
  ];
  zonePerformanceChart.update();
}

/**
 * Réinitialise tous les accélérateurs
 */
function resetAccelerators() {
  // Demander confirmation
  if (!confirm('Êtes-vous sûr de vouloir réinitialiser tous les accélérateurs ? Cette action réinitialisera toutes les optimisations.')) {
    return;
  }

  // Désactiver le bouton pendant la réinitialisation
  const button = document.getElementById('reset-accelerators-btn');
  button.disabled = true;
  button.innerHTML = '<i class="bi bi-arrow-repeat spin me-1"></i> Réinitialisation...';

  // Simuler la réinitialisation
  showNotification('Réinitialisation des accélérateurs en cours...', 'warning');

  // Simuler un délai de réinitialisation
  setTimeout(() => {
    // Réinitialiser les valeurs
    resetAcceleratorValues();

    // Réactiver le bouton
    button.disabled = false;
    button.innerHTML = '<i class="bi bi-arrow-counterclockwise me-1"></i> Réinitialiser les accélérateurs';

    // Afficher une notification
    showNotification('Tous les accélérateurs ont été réinitialisés', 'success');
  }, 3000);
}

/**
 * Réinitialise les valeurs des accélérateurs
 */
function resetAcceleratorValues() {
  // Réinitialiser le chef d'orchestre principal
  hierarchyState.masterConductor.efficiency = 150;
  document.getElementById('master-efficiency').textContent = `${hierarchyState.masterConductor.efficiency}%`;

  // Réinitialiser les chefs de zone
  const zones = ['reflection', 'memory', 'processing', 'creativity'];
  zones.forEach(zone => {
    // Valeurs de base aléatoires
    const baseEfficiency = 140 + Math.random() * 20;
    const basePower = 60 + Math.random() * 20;

    hierarchyState.zoneConductors[zone].efficiency = baseEfficiency;
    hierarchyState.zoneConductors[zone].power = basePower;

    // Mettre à jour l'interface
    document.getElementById(`${zone}-efficiency`).textContent = `${baseEfficiency.toFixed(1)}%`;

    // Mettre à jour la barre de progression
    const meterFill = document.querySelector(`.zone-conductor[data-zone="${zone}"] .meter-fill`);
    if (meterFill) {
      meterFill.style.width = `${basePower}%`;
      document.querySelector(`.zone-conductor[data-zone="${zone}"] .meter-value`).textContent = `${Math.round(basePower)}%`;
    }

    // Réinitialiser les accélérateurs individuels
    hierarchyState.zoneConductors[zone].acceleratorsList.forEach((acc, index) => {
      acc.efficiency = baseEfficiency * (0.9 + Math.random() * 0.2);
      acc.power = basePower * (0.9 + Math.random() * 0.2);

      const accElement = document.querySelector(`#${zone}-accelerators .accelerator:nth-child(${index + 1})`);
      if (accElement) {
        accElement.querySelector('.accelerator-efficiency').textContent = `${Math.round(acc.efficiency)}%`;
        accElement.querySelector('.accelerator-fill').style.width = `${acc.power}%`;
        accElement.querySelector('.meter-value').textContent = `${Math.round(acc.power)}%`;
      }
    });
  });

  // Mettre à jour le graphique de performance par zone
  zonePerformanceChart.data.datasets[0].data = [
    hierarchyState.zoneConductors.reflection.efficiency,
    hierarchyState.zoneConductors.memory.efficiency,
    hierarchyState.zoneConductors.processing.efficiency,
    hierarchyState.zoneConductors.creativity.efficiency
  ];
  zonePerformanceChart.update();

  // Réinitialiser l'efficacité du flux de données
  hierarchyState.dataFlow.efficiency = 150;
  document.getElementById('data-efficiency').textContent = `${hierarchyState.dataFlow.efficiency}%`;
}

/**
 * Met à jour la puissance du chef d'orchestre principal
 * @param {number} value - Nouvelle valeur de puissance
 */
function updateMasterPower(value) {
  // Mettre à jour la valeur dans l'état
  hierarchyState.masterConductor.power = parseInt(value);

  // Mettre à jour la barre de progression
  const meterFill = document.querySelector('.master-conductor .meter-fill');
  if (meterFill) {
    meterFill.style.width = `${value}%`;
  }

  // Ajuster l'efficacité en fonction de la puissance
  const newEfficiency = 150 + (value - 50) * 1.5;
  hierarchyState.masterConductor.efficiency = newEfficiency;
  document.getElementById('master-efficiency').textContent = `${newEfficiency.toFixed(1)}%`;

  // Ajuster la température
  const newTemp = 35 + (value - 50) * 0.15;
  hierarchyState.masterConductor.temperature = newTemp;
  document.getElementById('system-temp').textContent = `${newTemp.toFixed(1)}°C`;

  // Afficher une notification
  showNotification(`Puissance principale ajustée à ${value}%`, 'info');
}

/**
 * Met à jour le seuil thermique
 * @param {number} value - Nouvelle valeur de seuil thermique
 */
function updateThermalThreshold(value) {
  // Afficher une notification
  showNotification(`Seuil thermique ajusté à ${value}°C`, 'info');
}

/**
 * Met à jour la profondeur de cascade
 * @param {number} value - Nouvelle valeur de profondeur
 */
function updateCascadeDepth(value) {
  // Afficher une notification
  showNotification(`Profondeur de cascade ajustée à ${value}`, 'info');
}

/**
 * Affiche une notification
 * @param {string} message - Message à afficher
 * @param {string} type - Type de notification (info, success, warning, error)
 */
function showNotification(message, type = 'info') {
  // Vérifier si la fonction existe dans le contexte global
  if (typeof window.showNotification === 'function') {
    window.showNotification(message, type);
    return;
  }

  // Créer l'élément de notification s'il n'existe pas
  let notificationArea = document.getElementById('notification-area');
  if (!notificationArea) {
    notificationArea = document.createElement('div');
    notificationArea.id = 'notification-area';
    notificationArea.style.position = 'fixed';
    notificationArea.style.top = '20px';
    notificationArea.style.right = '20px';
    notificationArea.style.zIndex = '9999';
    notificationArea.style.width = '300px';
    document.body.appendChild(notificationArea);
  }

  // Créer la notification
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  notification.innerHTML = message;
  notificationArea.appendChild(notification);

  // Afficher avec animation
  setTimeout(() => {
    notification.classList.add('show');

    // Masquer après 3 secondes
    setTimeout(() => {
      notification.classList.remove('show');
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }, 100);
}