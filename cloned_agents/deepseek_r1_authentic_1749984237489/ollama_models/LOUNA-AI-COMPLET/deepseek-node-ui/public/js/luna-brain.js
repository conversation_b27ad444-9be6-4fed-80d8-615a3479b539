/**
 * Luna Brain - Visualisation du cerveau et de la mémoire thermique
 * Permet de visualiser et interagir avec les mémoires et leurs connexions
 */

// Initialiser la connexion Socket.IO
const socket = io();

// État global du cerveau
const brainState = {
  memories: [],
  connections: [],
  stats: {
    totalMemories: 124,
    totalConnections: 342,
    avgTemperature: 72,
    neuralActivity: 64
  },
  distribution: {
    zone1: 28,
    zone2: 36,
    zone3: 22,
    zone4: 18,
    zone5: 12,
    zone6: 8
  },
  settings: {
    showConnections: true,
    animation: true,
    detailLevel: 'medium',
    view: '2d'
  }
};

// Référence au graphique de distribution
let distributionChart = null;

// Événements Socket.IO
socket.on('connect', () => {
  console.log('🔌 Connecté au serveur Luna');
  loadBrainData();
});

socket.on('disconnect', () => {
  console.log('🔌 Déconnecté du serveur Luna');
  showNotification('Connexion au serveur perdue. Tentative de reconnexion...', 'error');
});

// Recevoir les données du cerveau
socket.on('brain data', (data) => {
  if (data.success) {
    brainState.memories = data.memories || [];
    brainState.connections = data.connections || [];
    brainState.stats = data.stats || brainState.stats;
    brainState.distribution = data.distribution || brainState.distribution;
    
    // Mettre à jour l'interface
    updateBrainUI();
  }
});

// Recevoir les détails d'une mémoire
socket.on('memory details', (data) => {
  if (data.success) {
    showMemoryDetails(data.memory);
  } else {
    showNotification(`Erreur lors de la récupération des détails: ${data.error}`, 'error');
  }
});

// Fonctions principales

// Charger les données du cerveau
function loadBrainData() {
  socket.emit('get brain data', { detailLevel: brainState.settings.detailLevel });
}

// Mettre à jour l'interface du cerveau
function updateBrainUI() {
  // Mettre à jour les statistiques
  updateStatsUI();
  
  // Mettre à jour le graphique de distribution
  updateDistributionChart();
  
  // Mettre à jour la visualisation 2D
  update2DVisualization();
}

// Mettre à jour les statistiques
function updateStatsUI() {
  $('#total-memories').text(brainState.stats.totalMemories);
  $('#total-connections').text(brainState.stats.totalConnections);
  $('#avg-temperature').text(`${brainState.stats.avgTemperature}°C`);
  $('#neural-activity').text(`${brainState.stats.neuralActivity}%`);
}

// Mettre à jour le graphique de distribution
function updateDistributionChart() {
  const ctx = document.getElementById('memory-distribution-chart').getContext('2d');
  
  // Détruire le graphique existant s'il existe
  if (distributionChart) {
    distributionChart.destroy();
  }
  
  // Créer un nouveau graphique
  distributionChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
      labels: ['Zone 1', 'Zone 2', 'Zone 3', 'Zone 4', 'Zone 5', 'Zone 6'],
      datasets: [{
        data: [
          brainState.distribution.zone1,
          brainState.distribution.zone2,
          brainState.distribution.zone3,
          brainState.distribution.zone4,
          brainState.distribution.zone5,
          brainState.distribution.zone6
        ],
        backgroundColor: [
          'rgba(240, 166, 202, 0.8)',
          'rgba(240, 166, 202, 0.6)',
          'rgba(184, 190, 221, 0.6)',
          'rgba(184, 190, 221, 0.5)',
          'rgba(156, 137, 184, 0.5)',
          'rgba(156, 137, 184, 0.4)'
        ],
        borderColor: [
          'rgba(240, 166, 202, 1)',
          'rgba(240, 166, 202, 0.8)',
          'rgba(184, 190, 221, 0.8)',
          'rgba(184, 190, 221, 0.7)',
          'rgba(156, 137, 184, 0.7)',
          'rgba(156, 137, 184, 0.6)'
        ],
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              const label = context.label || '';
              const value = context.raw || 0;
              const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
              const percentage = Math.round((value / total) * 100);
              return `${label}: ${value} mémoires (${percentage}%)`;
            }
          }
        }
      },
      cutout: '70%'
    }
  });
}

// Mettre à jour la visualisation 2D
function update2DVisualization() {
  // Vider les conteneurs de nœuds
  $('.zone-nodes').empty();
  $('#connections-container').empty();
  
  // Ajouter les nœuds de mémoire à chaque zone
  brainState.memories.forEach(memory => {
    const zoneContainer = $(`.zone-${memory.zone} .zone-nodes`);
    
    // Créer le nœud de mémoire
    const node = $(`<div class="memory-node zone-${memory.zone}-node" data-id="${memory.id}"></div>`);
    
    // Positionner aléatoirement le nœud dans sa zone
    const left = Math.random() * 80 + 10; // 10% à 90%
    const top = Math.random() * 80 + 10; // 10% à 90%
    node.css({
      left: `${left}%`,
      top: `${top}%`
    });
    
    // Ajouter une classe active si la mémoire est active
    if (memory.active) {
      node.addClass('active');
    }
    
    // Ajouter le gestionnaire d'événement pour afficher les détails
    node.on('click', function() {
      const memoryId = $(this).data('id');
      socket.emit('get memory details', { id: memoryId });
    });
    
    // Ajouter le nœud à la zone
    zoneContainer.append(node);
  });
  
  // Ajouter les connexions si activées
  if (brainState.settings.showConnections) {
    const connectionsContainer = $('#connections-container');
    
    brainState.connections.forEach(connection => {
      // Trouver les nœuds source et cible
      const sourceNode = $(`.memory-node[data-id="${connection.source}"]`);
      const targetNode = $(`.memory-node[data-id="${connection.target}"]`);
      
      if (sourceNode.length && targetNode.length) {
        // Calculer les positions des nœuds
        const sourceRect = sourceNode[0].getBoundingClientRect();
        const targetRect = targetNode[0].getBoundingClientRect();
        const containerRect = $('#brain-container')[0].getBoundingClientRect();
        
        // Calculer les positions relatives au conteneur
        const sourceX = sourceRect.left - containerRect.left + sourceRect.width / 2;
        const sourceY = sourceRect.top - containerRect.top + sourceRect.height / 2;
        const targetX = targetRect.left - containerRect.left + targetRect.width / 2;
        const targetY = targetRect.top - containerRect.top + targetRect.height / 2;
        
        // Calculer la longueur et l'angle de la connexion
        const length = Math.sqrt(Math.pow(targetX - sourceX, 2) + Math.pow(targetY - sourceY, 2));
        const angle = Math.atan2(targetY - sourceY, targetX - sourceX) * 180 / Math.PI;
        
        // Créer la connexion
        const connectionLine = $(`<div class="memory-connection" data-source="${connection.source}" data-target="${connection.target}"></div>`);
        
        // Positionner et orienter la connexion
        connectionLine.css({
          left: `${sourceX}px`,
          top: `${sourceY}px`,
          width: `${length}px`,
          transform: `rotate(${angle}deg)`
        });
        
        // Ajouter une classe active si la connexion est forte
        if (connection.strength > 0.7) {
          connectionLine.addClass('active');
        }
        
        // Ajouter la connexion au conteneur
        connectionsContainer.append(connectionLine);
      }
    });
  }
  
  // Animer les nœuds si l'animation est activée
  if (brainState.settings.animation) {
    animateNodes();
  }
}

// Animer les nœuds de mémoire
function animateNodes() {
  // Arrêter l'animation précédente si elle existe
  if (brainState.animationInterval) {
    clearInterval(brainState.animationInterval);
  }
  
  // Créer une nouvelle animation
  brainState.animationInterval = setInterval(() => {
    // Activer aléatoirement quelques nœuds
    const nodes = $('.memory-node');
    const activeCount = Math.floor(nodes.length * 0.05); // 5% des nœuds
    
    // Désactiver tous les nœuds
    nodes.removeClass('active');
    
    // Activer aléatoirement quelques nœuds
    for (let i = 0; i < activeCount; i++) {
      const randomIndex = Math.floor(Math.random() * nodes.length);
      $(nodes[randomIndex]).addClass('active');
    }
    
    // Activer les connexions correspondantes
    $('.memory-connection').removeClass('active');
    $('.memory-node.active').each(function() {
      const nodeId = $(this).data('id');
      $(`.memory-connection[data-source="${nodeId}"], .memory-connection[data-target="${nodeId}"]`).addClass('active');
    });
  }, 2000);
}

// Afficher les détails d'une mémoire
function showMemoryDetails(memory) {
  // Mettre à jour les informations de base
  $('#memory-id').text(memory.id);
  $('#memory-zone').text(`${memory.zone} (${getZoneName(memory.zone)})`);
  $('#memory-temp').text(`${memory.temperature}°C`);
  $('#memory-created').text(formatDate(memory.created));
  
  // Mettre à jour le contenu
  $('#memory-content-display').html(memory.content);
  
  // Mettre à jour les connexions
  const connectionsContainer = $('#memory-connections-list');
  connectionsContainer.empty();
  
  $('#memory-connections-count').text(memory.connections.length);
  
  memory.connections.forEach(conn => {
    const connectionItem = $(`
      <div class="memory-connection-item">
        <div class="connection-target">${conn.targetId}</div>
        <div class="connection-strength">Force: ${(conn.strength * 100).toFixed(0)}%</div>
        <div class="connection-zone">Zone ${conn.targetZone}</div>
      </div>
    `);
    
    connectionsContainer.append(connectionItem);
  });
  
  // Afficher la modal
  $('#memory-detail-modal').modal('show');
}

// Obtenir le nom d'une zone à partir de son numéro
function getZoneName(zoneNumber) {
  const zoneNames = {
    1: 'Récente',
    2: 'Chaude',
    3: 'Tiède',
    4: 'Fraîche',
    5: 'Froide',
    6: 'Archive'
  };
  
  return zoneNames[zoneNumber] || 'Inconnue';
}

// Formater une date
function formatDate(dateString) {
  const date = new Date(dateString);
  return date.toLocaleString('fr-FR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

// Afficher une notification
function showNotification(message, type = 'info') {
  // Créer l'élément de notification s'il n'existe pas
  let notificationArea = $('#notification-area');
  if (notificationArea.length === 0) {
    $('body').append('<div id="notification-area"></div>');
    notificationArea = $('#notification-area');
  }
  
  // Créer la notification
  const notification = $(`<div class="notification notification-${type}">${message}</div>`);
  notificationArea.append(notification);
  
  // Afficher avec animation
  setTimeout(() => {
    notification.addClass('show');
    
    // Masquer après 3 secondes
    setTimeout(() => {
      notification.removeClass('show');
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }, 100);
}

// Initialisation au chargement de la page
$(document).ready(function() {
  // Gestionnaires pour les boutons de vue
  $('#view-2d-btn').on('click', function() {
    $(this).addClass('active').removeClass('btn-luna-outline').addClass('btn-luna');
    $('#view-3d-btn').removeClass('active').removeClass('btn-luna').addClass('btn-luna-outline');
    $('#brain-2d-view').addClass('active');
    $('#brain-3d-view').removeClass('active');
    brainState.settings.view = '2d';
  });
  
  $('#view-3d-btn').on('click', function() {
    $(this).addClass('active').removeClass('btn-luna-outline').addClass('btn-luna');
    $('#view-2d-btn').removeClass('active').removeClass('btn-luna').addClass('btn-luna-outline');
    $('#brain-3d-view').addClass('active');
    $('#brain-2d-view').removeClass('active');
    brainState.settings.view = '3d';
  });
  
  // Gestionnaire pour le bouton d'activation 3D
  $('#enable-3d-btn').on('click', function() {
    $(this).prop('disabled', true).html('<i class="bi bi-arrow-repeat spin me-1"></i> Chargement...');
    
    // Simuler un chargement
    setTimeout(() => {
      showNotification('La visualisation 3D sera disponible dans une prochaine mise à jour', 'info');
      $(this).prop('disabled', false).html('<i class="bi bi-gpu-card me-1"></i> Activer le rendu 3D');
    }, 2000);
  });
  
  // Gestionnaires pour les contrôles
  $('#show-connections-toggle').on('change', function() {
    brainState.settings.showConnections = $(this).is(':checked');
    update2DVisualization();
  });
  
  $('#animation-toggle').on('change', function() {
    brainState.settings.animation = $(this).is(':checked');
    
    if (brainState.settings.animation) {
      animateNodes();
    } else if (brainState.animationInterval) {
      clearInterval(brainState.animationInterval);
      $('.memory-node').removeClass('active');
      $('.memory-connection').removeClass('active');
    }
  });
  
  $('#detail-level').on('change', function() {
    brainState.settings.detailLevel = $(this).val();
    loadBrainData();
  });
  
  // Gestionnaire pour le bouton d'actualisation
  $('#refresh-brain-btn').on('click', loadBrainData);
  
  // Gestionnaire pour le bouton d'exportation
  $('#export-brain-btn').on('click', function() {
    $(this).prop('disabled', true).html('<i class="bi bi-arrow-repeat spin me-1"></i> Exportation...');
    
    // Simuler une exportation
    setTimeout(() => {
      // Créer un objet Blob avec les données
      const data = JSON.stringify({
        memories: brainState.memories,
        connections: brainState.connections,
        stats: brainState.stats,
        distribution: brainState.distribution,
        exportDate: new Date().toISOString()
      }, null, 2);
      
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      // Créer un lien de téléchargement
      const a = document.createElement('a');
      a.href = url;
      a.download = `luna-brain-export-${new Date().toISOString().slice(0, 10)}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      // Réactiver le bouton
      $(this).prop('disabled', false).html('<i class="bi bi-download me-1"></i> Exporter');
      
      showNotification('Données du cerveau exportées avec succès', 'success');
    }, 1500);
  });
  
  // Gestionnaires pour les actions de la modal de détail
  $('#delete-memory-btn').on('click', function() {
    const memoryId = $('#memory-id').text();
    
    if (confirm(`Êtes-vous sûr de vouloir supprimer la mémoire ${memoryId} ? Cette action est irréversible.`)) {
      socket.emit('delete memory', { id: memoryId });
      $('#memory-detail-modal').modal('hide');
      
      // Recharger les données après un court délai
      setTimeout(loadBrainData, 500);
    }
  });
  
  $('#promote-memory-btn').on('click', function() {
    const memoryId = $('#memory-id').text();
    socket.emit('promote memory', { id: memoryId });
    $('#memory-detail-modal').modal('hide');
    
    // Recharger les données après un court délai
    setTimeout(loadBrainData, 500);
  });
  
  // Charger les données au démarrage
  loadBrainData();
});
