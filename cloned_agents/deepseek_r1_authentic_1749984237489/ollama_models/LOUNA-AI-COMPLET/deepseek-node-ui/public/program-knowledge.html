<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vision Ultra - Connaissance du Programme</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="/css/luna-program-knowledge.css">
  <style>
    :root {
      --luna-primary: #9c89b8;
      --luna-secondary: #f0a6ca;
      --luna-accent: #b8bedd;
      --luna-dark: #1a1a2e;
      --luna-light: #edf2fb;
      --luna-gradient: linear-gradient(135deg, #9c89b8, #f0a6ca);
      --header-height: 60px;
    }

    html, body {
      font-family: 'Quicksand', sans-serif;
      background-color: var(--luna-dark);
      color: var(--luna-light);
      height: 100%;
      max-height: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden !important;
      margin: 0;
      padding: 0;
    }

    .navbar-luna {
      background: var(--luna-gradient);
      box-shadow: 0 2px 15px rgba(0,0,0,0.2);
    }

    .navbar-brand {
      font-weight: 700;
      font-size: 1.8rem;
    }

    .nav-link {
      font-weight: 500;
      color: var(--luna-dark) !important;
      transition: all 0.3s ease;
      position: relative;
      padding: 0.5rem 1rem;
      margin: 0 0.2rem;
      border-radius: 8px;
    }

    .nav-link:hover {
      color: white !important;
      transform: translateY(-2px);
      background-color: rgba(0, 0, 0, 0.1);
    }

    .nav-link.active {
      color: white !important;
      background-color: rgba(0, 0, 0, 0.2);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      font-weight: 600;
    }

    .luna-container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 2rem;
    }

    .luna-card {
      background: rgba(26, 26, 46, 0.7);
      border: 1px solid rgba(184, 190, 221, 0.2);
      border-radius: 15px;
      padding: 1.5rem;
      margin-bottom: 1.5rem;
      box-shadow: 0 10px 25px rgba(0,0,0,0.2);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
    }

    .status-indicator {
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 8px;
      transition: all 0.5s ease;
    }

    .status-active {
      background-color: #4caf50;
      box-shadow: 0 0 10px #4caf50;
      animation: pulse 2s infinite;
    }

    .status-inactive {
      background-color: #f44336;
    }

    @keyframes pulse {
      0% {
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
      }
      70% {
        box-shadow: 0 0 0 10px rgba(76, 175, 80, 0);
      }
      100% {
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
      }
    }

    .btn-luna {
      background: var(--luna-gradient);
      border: none;
      border-radius: 25px;
      font-weight: 600;
      padding: 0.5rem 1.5rem;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(0,0,0,0.15);
    }

    .btn-luna:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 20px rgba(0,0,0,0.2);
    }

    /* Style pour l'affichage de la date et de l'heure */
    .datetime-display {
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 5px;
      padding: 5px 10px;
      font-size: 0.9rem;
      color: var(--luna-accent);
    }
  </style>
</head>
<body>
  <!-- Barre de navigation -->
  <nav class="navbar navbar-expand-lg navbar-dark navbar-luna">
    <div class="container">
      <a class="navbar-brand" href="/luna">
        <i class="bi bi-eye me-2"></i> Vision Ultra
      </a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarLuna">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarLuna">
        <ul class="navbar-nav">
          <li class="nav-item">
            <a class="nav-link" href="/luna"><i class="bi bi-speedometer2"></i> Accueil</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/luna/prompts"><i class="bi bi-lightning"></i> Prompts</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/luna/reflection"><i class="bi bi-lightbulb"></i> Réflexion</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/luna/memory"><i class="bi bi-hdd"></i> Mémoire</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/luna/brain"><i class="bi bi-diagram-3"></i> Cerveau</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/luna/cognitive"><i class="bi bi-braces"></i> Cognitif</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/luna/accelerators"><i class="bi bi-lightning-charge"></i> Accélérateurs</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/luna/training"><i class="bi bi-graduation-cap"></i> Formation</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/luna/code"><i class="bi bi-code-slash"></i> Code</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/luna/thermal"><i class="bi bi-thermometer-half"></i> Thermique</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/luna/mcp"><i class="bi bi-cpu"></i> MCP</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/luna/security"><i class="bi bi-shield-lock"></i> Sécurité</a>
          </li>
          <li class="nav-item">
            <a class="nav-link active" href="/luna/program-knowledge"><i class="bi bi-info-circle"></i> Connaissance</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/luna/settings"><i class="bi bi-gear"></i> Paramètres</a>
          </li>
        </ul>
      </div>
    </div>
  </nav>

  <div class="luna-container">
    <div class="row align-items-center mb-4">
      <div class="col-md-8">
        <h1 class="mb-0" style="font-weight: 700; letter-spacing: -1px;">Vision Ultra</h1>
        <p class="text-muted">Système cognitif avancé créé par Jean Passave à Sainte-Anne, Guadeloupe (97180)</p>
        <div class="mt-2 d-flex align-items-center">
          <div class="me-3">
            <i class="bi bi-calendar3 me-1" style="color: var(--luna-accent);"></i>
            <span id="current-date" style="font-weight: 500;">Chargement de la date...</span>
          </div>
          <div>
            <i class="bi bi-clock me-1" style="color: var(--luna-accent);"></i>
            <span id="current-time" style="font-weight: 500;">00:00:00</span>
          </div>
        </div>
      </div>
      <div class="col-md-4 text-end">
        <div class="d-flex align-items-center justify-content-end">
          <span class="me-3">Statut du système:</span>
          <span id="systemStatus">
            <span class="status-indicator status-active"></span>
            <span>Actif</span>
          </span>
          <button id="toggleSystem" class="btn btn-danger ms-3">
            <i class="bi bi-power"></i> Désactiver
          </button>
        </div>
      </div>
    </div>

    <!-- Contenu principal -->
    <div class="container-fluid mt-4">
      <div class="row">
        <!-- Panneau principal de connaissance du programme -->
        <div class="col-md-8">
          <div class="luna-card" style="height: 65vh; display: flex; flex-direction: column;">
            <h3 class="mb-3 d-flex align-items-center justify-content-between">
              <div><i class="bi bi-cpu me-2"></i> Connaissance du Programme Vision Ultra</div>
              <div>
                <span id="current-datetime" class="datetime-display me-3"></span>
                <button id="scan-program-btn" class="btn btn-sm btn-luna">
                  <i class="bi bi-search me-1"></i> Scanner le Programme
                </button>
              </div>
            </h3>

            <div class="program-knowledge-container" style="flex-grow: 1; overflow-y: auto; margin-bottom: 1rem; padding: 1rem; border-radius: 10px; background-color: rgba(0,0,0,0.2); background-image: linear-gradient(to bottom, rgba(156, 137, 184, 0.05), rgba(240, 166, 202, 0.05));">
              <!-- Onglets pour les différentes sections -->
              <ul class="nav nav-tabs" id="programKnowledgeTabs" role="tablist">
                <li class="nav-item" role="presentation">
                  <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab" aria-controls="overview" aria-selected="true">Vue d'ensemble</button>
                </li>
                <li class="nav-item" role="presentation">
                  <button class="nav-link" id="analysis-tab" data-bs-toggle="tab" data-bs-target="#analysis" type="button" role="tab" aria-controls="analysis" aria-selected="false">
                    Analyse
                    <span id="analysis-badge" class="badge bg-danger ms-1" style="display: none;">0</span>
                  </button>
                </li>
                <li class="nav-item" role="presentation">
                  <button class="nav-link" id="modules-tab" data-bs-toggle="tab" data-bs-target="#modules" type="button" role="tab" aria-controls="modules" aria-selected="false">Modules</button>
                </li>
                <li class="nav-item" role="presentation">
                  <button class="nav-link" id="interfaces-tab" data-bs-toggle="tab" data-bs-target="#interfaces" type="button" role="tab" aria-controls="interfaces" aria-selected="false">Interfaces</button>
                </li>
                <li class="nav-item" role="presentation">
                  <button class="nav-link" id="services-tab" data-bs-toggle="tab" data-bs-target="#services" type="button" role="tab" aria-controls="services" aria-selected="false">Services</button>
                </li>
              </ul>

              <!-- Contenu des onglets -->
              <div class="tab-content mt-3" id="programKnowledgeTabsContent">
                <!-- Vue d'ensemble -->
                <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
                  <div class="row">
                    <div class="col-md-6">
                      <div class="card bg-dark mb-3">
                        <div class="card-header">
                          <h5 class="card-title mb-0">Statistiques du Programme</h5>
                        </div>
                        <div class="card-body">
                          <div id="program-stats">
                            <div class="text-center">
                              <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Chargement...</span>
                              </div>
                              <p class="mt-2">Chargement des statistiques...</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="card bg-dark mb-3">
                        <div class="card-header">
                          <h5 class="card-title mb-0">Dernière Analyse</h5>
                        </div>
                        <div class="card-body">
                          <div id="last-scan-info">
                            <div class="text-center">
                              <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Chargement...</span>
                              </div>
                              <p class="mt-2">Chargement des informations...</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Panneau latéral avec les statistiques et les informations -->
        <div class="col-md-4">
          <!-- Cerveau thermique -->
          <div class="luna-card mb-3">
            <h4><i class="bi bi-thermometer-half me-2"></i> Cerveau Thermique</h4>
            <div class="mb-3">
              <div class="d-flex justify-content-between mb-1">
                <span>Température</span>
                <span id="brain-temperature">42°C</span>
              </div>
              <div class="progress mb-3" style="height: 8px;">
                <div id="brain-temperature-bar" class="progress-bar" role="progressbar" style="width: 42%;" aria-valuenow="42" aria-valuemin="0" aria-valuemax="100"></div>
              </div>

              <div class="d-flex justify-content-between mb-1">
                <span>Activité neuronale</span>
                <span id="brain-activity">85%</span>
              </div>
              <div class="progress mb-3" style="height: 8px;">
                <div id="brain-activity-bar" class="progress-bar" role="progressbar" style="width: 85%;" aria-valuenow="85" aria-valuemin="0" aria-valuemax="100"></div>
              </div>

              <div class="d-flex justify-content-between mb-1">
                <span>Zones actives</span>
                <span id="active-zones">6/10</span>
              </div>
              <div class="progress" style="height: 8px;">
                <div id="active-zones-bar" class="progress-bar" role="progressbar" style="width: 60%;" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100"></div>
              </div>
            </div>
          </div>

          <!-- Date et heure -->
          <div class="luna-card">
            <h4><i class="bi bi-clock me-2"></i> Date et Heure</h4>
            <div class="mb-3">
              <div class="d-flex justify-content-between mb-1">
                <span>Date actuelle</span>
                <span id="current-date-panel"></span>
              </div>
              <div class="d-flex justify-content-between mb-1">
                <span>Heure actuelle</span>
                <span id="current-time-panel"></span>
              </div>
              <div class="d-flex justify-content-between mb-1">
                <span>Fuseau horaire</span>
                <span id="timezone">Europe/Paris</span>
              </div>
              <div class="d-flex justify-content-between mb-1">
                <span>Format</span>
                <span id="date-format">fr-FR</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Conteneur de toasts -->
  <div class="toast-container"></div>

  <!-- Bibliothèques JavaScript nécessaires -->
  <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>
  <script src="/socket.io/socket.io.js"></script>
  <script src="/js/program-knowledge-standalone.js"></script>
</body>
</html>
