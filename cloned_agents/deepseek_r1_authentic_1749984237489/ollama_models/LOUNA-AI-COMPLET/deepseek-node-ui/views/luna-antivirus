<div class="container-fluid luna-container">
  <div class="row">
    <div class="col-12">
      <div class="card luna-card mb-4">
        <div class="card-header">
          <h5 class="card-title">
            <i class="bi bi-virus"></i> Antivirus Vision Ultra
          </h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="card luna-inner-card mb-4">
                <div class="card-header">
                  <h6 class="card-title">
                    <i class="bi bi-shield-check"></i> État de la protection
                  </h6>
                </div>
                <div class="card-body">
                  <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                      <h5 class="mb-0">Protection en temps réel</h5>
                      <small class="text-muted">Surveille en permanence les menaces potentielles</small>
                    </div>
                    <div class="form-check form-switch">
                      <input class="form-check-input" type="checkbox" id="realTimeProtectionSwitch" checked>
                    </div>
                  </div>
                  
                  <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                      <h5 class="mb-0">Protection Web</h5>
                      <small class="text-muted">Bloque les sites malveillants</small>
                    </div>
                    <div class="form-check form-switch">
                      <input class="form-check-input" type="checkbox" id="webProtectionSwitch" checked>
                    </div>
                  </div>
                  
                  <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                      <h5 class="mb-0">Protection e-mail</h5>
                      <small class="text-muted">Analyse les pièces jointes et les liens</small>
                    </div>
                    <div class="form-check form-switch">
                      <input class="form-check-input" type="checkbox" id="emailProtectionSwitch" checked>
                    </div>
                  </div>
                  
                  <div class="d-flex justify-content-between align-items-center">
                    <div>
                      <h5 class="mb-0">Pare-feu</h5>
                      <small class="text-muted">Contrôle le trafic réseau</small>
                    </div>
                    <div class="form-check form-switch">
                      <input class="form-check-input" type="checkbox" id="firewallSwitch" checked>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="card luna-inner-card">
                <div class="card-header">
                  <h6 class="card-title">
                    <i class="bi bi-gear"></i> Paramètres de l'antivirus
                  </h6>
                </div>
                <div class="card-body">
                  <div class="mb-3">
                    <label for="scanFrequencySelect" class="form-label">Fréquence d'analyse</label>
                    <select class="form-select" id="scanFrequencySelect">
                      <option value="daily">Quotidienne</option>
                      <option value="weekly" selected>Hebdomadaire</option>
                      <option value="monthly">Mensuelle</option>
                    </select>
                  </div>
                  
                  <div class="mb-3">
                    <label for="threatLevelSelect" class="form-label">Niveau de détection des menaces</label>
                    <select class="form-select" id="threatLevelSelect">
                      <option value="low">Faible - Menaces critiques uniquement</option>
                      <option value="medium" selected>Moyen - Équilibré</option>
                      <option value="high">Élevé - Détection agressive</option>
                    </select>
                  </div>
                  
                  <button class="btn btn-primary" id="saveAntivirusSettingsBtn">
                    <i class="bi bi-save"></i> Enregistrer les paramètres
                  </button>
                </div>
              </div>
            </div>
            
            <div class="col-md-6">
              <div class="card luna-inner-card mb-4">
                <div class="card-header">
                  <h6 class="card-title">
                    <i class="bi bi-search"></i> Analyse du système
                  </h6>
                </div>
                <div class="card-body">
                  <div class="mb-3">
                    <label for="scanTypeSelect" class="form-label">Type d'analyse</label>
                    <select class="form-select" id="scanTypeSelect">
                      <option value="quick">Analyse rapide</option>
                      <option value="full">Analyse complète</option>
                      <option value="custom">Analyse personnalisée</option>
                    </select>
                  </div>
                  
                  <div class="mb-3" id="customScanOptions" style="display: none;">
                    <div class="form-check mb-2">
                      <input class="form-check-input" type="checkbox" id="scanSystemFiles" checked>
                      <label class="form-check-label" for="scanSystemFiles">Fichiers système</label>
                    </div>
                    <div class="form-check mb-2">
                      <input class="form-check-input" type="checkbox" id="scanUserFiles" checked>
                      <label class="form-check-label" for="scanUserFiles">Fichiers utilisateur</label>
                    </div>
                    <div class="form-check mb-2">
                      <input class="form-check-input" type="checkbox" id="scanRegistry" checked>
                      <label class="form-check-label" for="scanRegistry">Registre</label>
                    </div>
                    <div class="form-check">
                      <input class="form-check-input" type="checkbox" id="scanRemovableDrives">
                      <label class="form-check-label" for="scanRemovableDrives">Lecteurs amovibles</label>
                    </div>
                  </div>
                  
                  <button class="btn btn-primary w-100" id="startScanBtn">
                    <i class="bi bi-play-fill"></i> Démarrer l'analyse
                  </button>
                  
                  <div class="mt-3" id="scanProgress" style="display: none;">
                    <div class="progress mb-2">
                      <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                    </div>
                    <small class="text-muted" id="scanStatus">Initialisation de l'analyse...</small>
                  </div>
                </div>
              </div>
              
              <div class="card luna-inner-card">
                <div class="card-header">
                  <h6 class="card-title">
                    <i class="bi bi-clock-history"></i> Historique des analyses
                  </h6>
                </div>
                <div class="card-body">
                  <div class="scan-history" id="scanHistory">
                    <div class="scan-item">
                      <div class="d-flex justify-content-between">
                        <span class="scan-date"><%- new Date(Date.now() - 7*24*60*60*1000).toLocaleDateString('fr-FR') %></span>
                        <span class="scan-type">Analyse complète</span>
                      </div>
                      <div class="d-flex justify-content-between align-items-center">
                        <span class="scan-result text-success">Aucune menace détectée</span>
                        <span class="scan-duration">Durée: 45 min</span>
                      </div>
                    </div>
                    <div class="scan-item">
                      <div class="d-flex justify-content-between">
                        <span class="scan-date"><%- new Date(Date.now() - 14*24*60*60*1000).toLocaleDateString('fr-FR') %></span>
                        <span class="scan-type">Analyse rapide</span>
                      </div>
                      <div class="d-flex justify-content-between align-items-center">
                        <span class="scan-result text-success">Aucune menace détectée</span>
                        <span class="scan-duration">Durée: 12 min</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Pied de page fixe -->
<footer class="footer-fixed">
  <small>Vision Ultra - Interface Antivirus - <span id="current-datetime"></span> - &copy; 2024 Jean Passave - Sainte-Anne, Guadeloupe (97180)</small>
</footer>

<style>
  .luna-inner-card {
    background-color: rgba(26, 26, 46, 0.7);
    border: 1px solid rgba(184, 190, 221, 0.2);
  }
  
  .scan-history {
    max-height: 200px;
    overflow-y: auto;
  }
  
  .scan-item {
    padding: 10px;
    border-bottom: 1px solid rgba(184, 190, 221, 0.1);
  }
  
  .scan-date {
    color: #b8bedd;
    font-size: 0.9rem;
  }
  
  .scan-type {
    font-weight: 500;
  }
  
  .scan-result {
    font-size: 0.9rem;
  }
  
  .scan-duration {
    font-size: 0.8rem;
    color: #b8bedd;
  }
  
  /* Styles pour le pied de page fixe */
  .footer-fixed {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 40px;
    background-color: #1a1a2e;
    border-top: 1px solid rgba(184, 190, 221, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    color: #edf2fb;
  }
</style>

<script>
  $(document).ready(function() {
    // Mettre à jour la date et l'heure
    function updateDateTime() {
      const now = new Date();
      $('#current-datetime').text(now.toLocaleString('fr-FR'));
    }
    
    // Mettre à jour la date et l'heure toutes les secondes
    updateDateTime();
    setInterval(updateDateTime, 1000);
    
    // Afficher/masquer les options d'analyse personnalisée
    $('#scanTypeSelect').on('change', function() {
      if ($(this).val() === 'custom') {
        $('#customScanOptions').slideDown();
      } else {
        $('#customScanOptions').slideUp();
      }
    });
    
    // Gérer le bouton de démarrage d'analyse
    $('#startScanBtn').on('click', function() {
      const scanType = $('#scanTypeSelect').val();
      let scanName = '';
      
      switch (scanType) {
        case 'quick':
          scanName = 'Analyse rapide';
          break;
        case 'full':
          scanName = 'Analyse complète';
          break;
        case 'custom':
          scanName = 'Analyse personnalisée';
          break;
      }
      
      // Afficher la barre de progression
      $('#scanProgress').slideDown();
      $('#scanStatus').text(`Initialisation de l'${scanName.toLowerCase()}...`);
      
      // Simuler une analyse
      let progress = 0;
      const interval = setInterval(function() {
        progress += Math.random() * 5;
        if (progress >= 100) {
          progress = 100;
          clearInterval(interval);
          
          // Mettre à jour le statut
          $('#scanStatus').text(`${scanName} terminée. Aucune menace détectée.`);
          
          // Ajouter à l'historique
          const now = new Date();
          const scanDuration = scanType === 'quick' ? '5 min' : (scanType === 'full' ? '45 min' : '25 min');
          
          const scanItemHtml = `
            <div class="scan-item">
              <div class="d-flex justify-content-between">
                <span class="scan-date">${now.toLocaleDateString('fr-FR')}</span>
                <span class="scan-type">${scanName}</span>
              </div>
              <div class="d-flex justify-content-between align-items-center">
                <span class="scan-result text-success">Aucune menace détectée</span>
                <span class="scan-duration">Durée: ${scanDuration}</span>
              </div>
            </div>
          `;
          
          $('#scanHistory').prepend(scanItemHtml);
          
          // Réinitialiser après 3 secondes
          setTimeout(function() {
            $('#scanProgress').slideUp();
            $('.progress-bar').css('width', '0%');
          }, 3000);
        }
        
        // Mettre à jour la barre de progression
        $('.progress-bar').css('width', progress + '%');
        
        // Mettre à jour le statut
        if (progress < 30) {
          $('#scanStatus').text(`Analyse des fichiers système en cours (${Math.round(progress)}%)...`);
        } else if (progress < 60) {
          $('#scanStatus').text(`Analyse des fichiers utilisateur en cours (${Math.round(progress)}%)...`);
        } else if (progress < 90) {
          $('#scanStatus').text(`Analyse du registre en cours (${Math.round(progress)}%)...`);
        } else {
          $('#scanStatus').text(`Finalisation de l'analyse (${Math.round(progress)}%)...`);
        }
      }, 500);
    });
    
    // Gérer le bouton de sauvegarde des paramètres
    $('#saveAntivirusSettingsBtn').on('click', function() {
      // Afficher une notification
      const notification = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
          Paramètres de l'antivirus enregistrés avec succès.
          <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
      `;
      
      $('body').append(`<div class="notification-container">${notification}</div>`);
      
      setTimeout(() => {
        $('.notification-container').fadeOut(500, function() {
          $(this).remove();
        });
      }, 3000);
    });
  });
</script>
