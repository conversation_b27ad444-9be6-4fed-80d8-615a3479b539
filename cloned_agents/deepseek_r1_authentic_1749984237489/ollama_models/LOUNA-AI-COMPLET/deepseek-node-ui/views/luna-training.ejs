<%- include('luna-base') %>
<link rel="stylesheet" href="/css/luna-training.css">

<!-- Contenu principal -->
<div class="row">
  <div class="col-md-12">
    <div class="luna-card">
      <h2><i class="bi bi-graduation-cap"></i> Formation de l'Assistant</h2>
      <p class="text-muted">Utilisez cette page pour lancer des sessions de formation et voir les résultats</p>

      <div class="training-container">
        <div class="training-header">
          <div class="training-title">Formation avec DeepSeek r1</div>
          <div class="training-controls">
            <button id="refreshButton" class="training-button">
              <i class="bi bi-arrow-clockwise"></i> Rafraîchir
            </button>
            <div class="training-option">
              <input type="checkbox" id="internetAccessToggle" checked>
              <label for="internetAccessToggle">Accès Internet</label>
            </div>
            <button id="launchTrainingButton" class="training-button primary">
              <i class="bi bi-dice-5"></i> Lancer Formation
            </button>
            <button id="autoTrainingButton" class="training-button primary" style="background-color: #38a169;">
              <i class="bi bi-robot"></i> Auto-Formation
            </button>
          </div>
        </div>

        <div class="dice-container">
          <div id="dice" class="dice">?</div>
          <div class="dice-label">Nombre de questions aléatoires</div>
        </div>

        <!-- Barre de progression de la formation en cours -->
        <div id="trainingProgressContainer" class="progress-container" style="display: none;">
          <div class="progress-title">
            <span>Formation en cours</span>
            <span id="trainingProgressValue" class="progress-value">0%</span>
          </div>
          <div class="progress-bar-container">
            <div id="trainingProgressBar" class="progress-bar" style="width: 0%"></div>
          </div>
          <div class="training-details">
            <div class="training-detail">
              <span>Étape:</span>
              <span>
                <span id="trainingStage">Préparation...</span>
                <span id="internetBadge" class="internet-badge" style="display: none;">Internet</span>
              </span>
            </div>
            <div class="training-detail">
              <span>Temps écoulé:</span>
              <span id="trainingElapsedTime">0s</span>
            </div>
            <div class="training-detail">
              <span>Temps restant estimé:</span>
              <span id="trainingRemainingTime">Calcul en cours...</span>
            </div>
            <div class="training-detail" id="trainingAcceleratorInfo" style="display: none;">
              <span>Accélération:</span>
              <span id="trainingAcceleratorValue">x1.0</span>
            </div>
          </div>
        </div>

        <div class="training-stats">
          <div class="stat-card">
            <div class="stat-label">Sessions totales</div>
            <div id="totalSessions" class="stat-value">0</div>
          </div>
          <div class="stat-card">
            <div class="stat-label">Sessions aujourd'hui</div>
            <div id="todaySessions" class="stat-value">0</div>
          </div>
          <div class="stat-card">
            <div class="stat-label">Temps moyen de réponse</div>
            <div id="avgResponseTime" class="stat-value">0s</div>
          </div>
        </div>

        <!-- Statistiques d'évolution de l'IA -->
        <div class="evolution-stats">
          <div class="evolution-card">
            <div class="evolution-icon"><i class="bi bi-brain"></i></div>
            <div id="iqValue" class="evolution-value">100</div>
            <div class="evolution-label">Coefficient Intellectuel</div>
          </div>
          <div class="evolution-card">
            <div class="evolution-icon"><i class="bi bi-diagram-3"></i></div>
            <div id="neuronsValue" class="evolution-value">1000</div>
            <div class="evolution-label">Neurones</div>
          </div>
          <div class="evolution-card">
            <div class="evolution-icon"><i class="bi bi-mortarboard"></i></div>
            <div id="evolutionLevelValue" class="evolution-value">1</div>
            <div class="evolution-label">Niveau d'évolution</div>
          </div>
          <div class="evolution-card">
            <div class="evolution-icon"><i class="bi bi-clock"></i></div>
            <div id="evolutionTimeValue" class="evolution-value">0h</div>
            <div class="evolution-label">Temps d'évolution</div>
          </div>
        </div>

        <!-- Barres de progression -->
        <div class="progress-container">
          <div class="progress-title">
            <span>Progression vers le niveau suivant</span>
            <span id="levelProgressValue" class="progress-value">0%</span>
          </div>
          <div class="progress-bar-container">
            <div id="levelProgressBar" class="progress-bar" style="width: 0%"></div>
          </div>

          <div class="progress-title">
            <span>Vitesse d'apprentissage</span>
            <span id="learningSpeedValue" class="progress-value">0%</span>
          </div>
          <div class="progress-bar-container">
            <div id="learningSpeedBar" class="progress-bar learning" style="width: 0%"></div>
          </div>

          <div class="progress-title">
            <span>Évolution du QI</span>
            <span id="iqProgressValue" class="progress-value">100</span>
          </div>
          <div class="progress-bar-container">
            <div id="iqProgressBar" class="progress-bar iq" style="width: 0%"></div>
          </div>
        </div>

        <!-- Historique d'évolution -->
        <div class="evolution-history">
          <div class="history-title">Historique d'évolution récent</div>
          <div id="evolutionHistoryContainer">
            <div class="no-sessions">
              Aucune donnée d'évolution disponible.
            </div>
          </div>
        </div>

        <div class="sessions-container">
          <h3>Sessions récentes</h3>
          <div id="sessionsContainer">
            <div class="no-sessions">
              Aucune session de formation récente. Lancez une formation pour commencer.
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
      const dice = document.getElementById('dice');
      const launchTrainingButton = document.getElementById('launchTrainingButton');
      const refreshButton = document.getElementById('refreshButton');
      const sessionsContainer = document.getElementById('sessionsContainer');
      const totalSessionsElement = document.getElementById('totalSessions');
      const todaySessionsElement = document.getElementById('todaySessions');
      const avgResponseTimeElement = document.getElementById('avgResponseTime');

      // Éléments pour les statistiques d'évolution
      const iqValueElement = document.getElementById('iqValue');
      const neuronsValueElement = document.getElementById('neuronsValue');
      const evolutionLevelValueElement = document.getElementById('evolutionLevelValue');
      const evolutionTimeValueElement = document.getElementById('evolutionTimeValue');
      const levelProgressValueElement = document.getElementById('levelProgressValue');
      const levelProgressBarElement = document.getElementById('levelProgressBar');
      const learningSpeedValueElement = document.getElementById('learningSpeedValue');
      const learningSpeedBarElement = document.getElementById('learningSpeedBar');
      const iqProgressValueElement = document.getElementById('iqProgressValue');
      const iqProgressBarElement = document.getElementById('iqProgressBar');
      const evolutionHistoryContainer = document.getElementById('evolutionHistoryContainer');

      // Éléments pour la barre de progression de formation
      const trainingProgressContainer = document.getElementById('trainingProgressContainer');
      const trainingProgressBar = document.getElementById('trainingProgressBar');
      const trainingProgressValue = document.getElementById('trainingProgressValue');
      const trainingStage = document.getElementById('trainingStage');
      const trainingElapsedTime = document.getElementById('trainingElapsedTime');
      const trainingRemainingTime = document.getElementById('trainingRemainingTime');
      const trainingAcceleratorInfo = document.getElementById('trainingAcceleratorInfo');
      const trainingAcceleratorValue = document.getElementById('trainingAcceleratorValue');

      let sessions = [];
      let aiStats = {};
      let isTraining = false;
      let currentSessionId = null;
      let socket = io();

      // Charger les sessions depuis la mémoire thermique
      function loadSessions() {
        fetch('/api/training/sessions')
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              sessions = data.sessions;
              updateStats();
              renderSessions();

              // Charger les statistiques d'évolution après avoir chargé les sessions
              loadAiStats();
            }
          })
          .catch(error => {
            console.error('Erreur lors du chargement des sessions:', error);
          });
      }

      // Charger les statistiques d'évolution de l'IA
      function loadAiStats() {
        fetch('/api/ai/stats')
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              aiStats = data.stats;
              updateEvolutionStats();
              renderEvolutionHistory();
            }
          })
          .catch(error => {
            console.error('Erreur lors du chargement des statistiques d\'évolution:', error);
          });
      }

      // Mettre à jour les statistiques
      function updateStats() {
        totalSessionsElement.textContent = sessions.length;

        // Calculer les sessions d'aujourd'hui
        const today = new Date().toISOString().split('T')[0];
        const todaySessions = sessions.filter(session => {
          return session.timestamp && session.timestamp.startsWith(today);
        });
        todaySessionsElement.textContent = todaySessions.length;

        // Calculer le temps moyen de réponse (simulé pour l'instant)
        avgResponseTimeElement.textContent = '1.2s';
      }

      // Mettre à jour les statistiques d'évolution
      function updateEvolutionStats() {
        // Mettre à jour les valeurs
        iqValueElement.textContent = Math.round(aiStats.iq);
        neuronsValueElement.textContent = aiStats.neurons.toLocaleString();
        evolutionLevelValueElement.textContent = aiStats.evolutionLevel;
        evolutionTimeValueElement.textContent = aiStats.evolutionTime + 'h';

        // Mettre à jour les barres de progression
        levelProgressValueElement.textContent = aiStats.levelProgress + '%';
        levelProgressBarElement.style.width = aiStats.levelProgress + '%';

        learningSpeedValueElement.textContent = aiStats.learningSpeed + '%';
        learningSpeedBarElement.style.width = aiStats.learningSpeed + '%';

        // Calculer la progression du QI (base 100 = 0%, 200 = 100%)
        const iqProgress = Math.min(100, Math.max(0, (aiStats.iq - 100) / 100 * 100));
        iqProgressValueElement.textContent = Math.round(aiStats.iq);
        iqProgressBarElement.style.width = iqProgress + '%';
      }

      // Afficher l'historique d'évolution
      function renderEvolutionHistory() {
        if (!aiStats.history || aiStats.history.length === 0) {
          evolutionHistoryContainer.innerHTML = `
            <div class="no-sessions">
              Aucune donnée d'évolution disponible.
            </div>
          `;
          return;
        }

        evolutionHistoryContainer.innerHTML = '';

        // Afficher les 7 derniers jours
        aiStats.history.slice(0, 7).forEach(entry => {
          const historyItem = document.createElement('div');
          historyItem.className = 'history-item';

          const date = new Date(entry.date);
          const formattedDate = date.toLocaleDateString('fr-FR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
          });

          historyItem.innerHTML = `
            <span class="history-date">${formattedDate}</span>
            <span class="history-sessions">${entry.sessions} session(s)</span>
            <span class="history-gain">+${entry.iqGain.toFixed(1)} QI</span>
          `;

          evolutionHistoryContainer.appendChild(historyItem);
        });
      }

      // Afficher les sessions
      function renderSessions() {
        if (sessions.length === 0) {
          sessionsContainer.innerHTML = `
            <div class="no-sessions">
              Aucune session de formation récente. Lancez une formation pour commencer.
            </div>
          `;
          return;
        }

        sessionsContainer.innerHTML = '';

        // Trier les sessions par date (les plus récentes d'abord)
        const sortedSessions = [...sessions].sort((a, b) => {
          return new Date(b.timestamp) - new Date(a.timestamp);
        });

        // Afficher les 10 dernières sessions
        sortedSessions.slice(0, 10).forEach(session => {
          const sessionElement = document.createElement('div');
          sessionElement.className = 'session-item';

          const date = new Date(session.timestamp);
          const formattedDate = date.toLocaleDateString('fr-FR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          });

          // Préparer le badge Internet si nécessaire
          const internetBadge = session.internetEnabled
            ? '<span class="internet-badge">Internet</span>'
            : '';

          // Préparer les informations Internet si disponibles
          let internetInfo = '';
          if (session.internetEnabled && session.internetData) {
            internetInfo = `
              <div class="internet-info">
                <div class="internet-info-title">Informations Internet:</div>
                <div class="internet-info-content">
                  <div>Sources: ${session.internetData.searchResults || 0}</div>
                  ${session.internetData.hasWebContent ? '<div>Contenu web: Oui</div>' : ''}
                  ${session.internetData.hasVideoInfo ? '<div>Vidéo: Oui</div>' : ''}
                </div>
              </div>
            `;
          }

          sessionElement.innerHTML = `
            <div class="session-header">
              <span>Session du ${formattedDate}</span>
              <span class="session-id">${session.sessionId} ${internetBadge}</span>
            </div>
            <div class="session-question">${session.question}</div>
            ${internetInfo}
            <div class="session-response">${session.response}</div>
            <div class="feedback-title">Feedback du formateur:</div>
            <div class="session-feedback">${session.feedback}</div>
          `;

          sessionsContainer.appendChild(sessionElement);
        });
      }

      // Lancer une session de formation
      function launchTraining() {
        if (isTraining) return;

        // Animation du dé
        dice.classList.add('rolling');
        dice.textContent = '?';

        // Vérifier si l'accès Internet est activé
        const internetAccessToggle = document.getElementById('internetAccessToggle');
        const useInternet = internetAccessToggle ? internetAccessToggle.checked : true;

        setTimeout(() => {
          // Générer un nombre aléatoire entre 1 et 5
          const diceRoll = Math.floor(Math.random() * 5) + 1;
          dice.textContent = diceRoll;
          dice.classList.remove('rolling');

          // Appel à l'API pour lancer la formation
          fetch('/api/training', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              questionCount: diceRoll,
              useInternet: useInternet
            }),
          })
          .then(response => response.json())
          .then(data => {
            if (!data.success) {
              alert('Erreur lors de la formation: ' + (data.error || 'Erreur inconnue'));
              isTraining = false;
              launchTrainingButton.disabled = false;
            }
            // Le reste de la logique est géré par les événements socket.io
          })
          .catch(error => {
            console.error('Erreur lors de la formation:', error);
            alert('Erreur lors de la formation. Consultez la console pour plus de détails.');
            isTraining = false;
            launchTrainingButton.disabled = false;
          });
        }, 1500);
      }

      // Fonction pour formater le temps en secondes en format lisible
      function formatTime(seconds) {
        if (seconds < 60) {
          return `${seconds}s`;
        } else if (seconds < 3600) {
          const minutes = Math.floor(seconds / 60);
          const remainingSeconds = seconds % 60;
          return `${minutes}m ${remainingSeconds}s`;
        } else {
          const hours = Math.floor(seconds / 3600);
          const minutes = Math.floor((seconds % 3600) / 60);
          return `${hours}h ${minutes}m`;
        }
      }

      // Fonction pour calculer un temps restant plus précis basé sur le temps écoulé
      function calculateRemainingTime(elapsedTime, progress, totalQuestions) {
        if (progress <= 0) return 0;

        // Calculer le temps moyen par pourcentage de progression
        const timePerPercent = elapsedTime / progress;

        // Estimer le temps restant
        return Math.round(timePerPercent * (100 - progress));
      }

      // Fonction pour mettre à jour l'affichage de la progression de formation
      function updateTrainingProgress(data) {
        if (!data) return;

        // Afficher le conteneur de progression
        trainingProgressContainer.style.display = 'block';

        // Mettre à jour la barre de progression
        trainingProgressBar.style.width = `${data.progress}%`;
        trainingProgressValue.textContent = `${data.progress}%`;

        // Mettre à jour les détails de formation
        let stageText = 'Préparation...';
        switch(data.stage) {
          case 'generating_question':
            stageText = 'Génération de question';
            break;
          case 'internet_research':
            stageText = 'Recherche Internet';
            break;
          case 'generating_response':
            stageText = 'Génération de réponse';
            break;
          case 'generating_feedback':
            stageText = 'Génération de feedback';
            break;
          case 'storing_memory':
            stageText = 'Stockage en mémoire';
            break;
          default:
            stageText = 'Traitement en cours';
        }

        // Mettre à jour le texte de l'étape
        trainingStage.textContent = stageText;

        // Ajouter un badge Internet si l'accès Internet est activé
        const internetBadge = document.getElementById('internetBadge');
        if (internetBadge) {
          if (data.internetEnabled && (data.stage === 'internet_research' || data.stage === 'generating_response')) {
            internetBadge.style.display = 'inline-block';
          } else {
            internetBadge.style.display = 'none';
          }
        }
        trainingElapsedTime.textContent = formatTime(data.elapsedTime);

        // Calculer un temps restant plus précis basé sur le temps écoulé réel
        let remainingTime;
        if (data.progress >= 100) {
          remainingTime = 'Terminé';
        } else if (data.progress <= 0) {
          remainingTime = 'Calcul en cours...';
        } else {
          // Utiliser notre fonction améliorée pour calculer le temps restant
          const calculatedRemainingTime = calculateRemainingTime(data.elapsedTime, data.progress, data.totalQuestions);

          // Utiliser le maximum entre le temps calculé et le temps estimé par le serveur
          // pour éviter des fluctuations trop importantes
          const finalRemainingTime = Math.max(
            Math.min(calculatedRemainingTime, data.elapsedTime * 2), // Pas plus du double du temps déjà écoulé
            Math.floor(data.estimatedTimeRemaining / 2) // Au moins la moitié du temps estimé par le serveur
          );

          remainingTime = formatTime(finalRemainingTime);
        }

        trainingRemainingTime.textContent = remainingTime;

        // Afficher les informations d'accélérateur si disponibles
        if (data.accelerated) {
          trainingAcceleratorInfo.style.display = 'flex';
          trainingAcceleratorValue.textContent = `x${data.speedMultiplier.toFixed(1)}`;
        } else {
          trainingAcceleratorInfo.style.display = 'none';
        }
      }

      // Écouter les événements socket pour les mises à jour de formation
      socket.on('training start', (data) => {
        console.log('Formation démarrée:', data);
        currentSessionId = data.sessionId;
        isTraining = true;
        launchTrainingButton.disabled = true;

        // Initialiser la barre de progression
        updateTrainingProgress({
          progress: 0,
          stage: 'preparation',
          elapsedTime: 0,
          estimatedTimeRemaining: data.estimatedTime,
          accelerated: data.accelerated,
          speedMultiplier: data.speedMultiplier
        });
      });

      socket.on('training progress', (data) => {
        console.log('Progression de formation:', data);
        if (data.sessionId === currentSessionId) {
          updateTrainingProgress(data);

          // Si la formation est terminée
          if (data.status === 'completed') {
            setTimeout(() => {
              // Recharger les sessions et les statistiques
              loadSessions();
              loadAiStats();

              // Réinitialiser l'interface
              isTraining = false;
              launchTrainingButton.disabled = false;
              currentSessionId = null;

              // Masquer la barre de progression après un délai
              setTimeout(() => {
                trainingProgressContainer.style.display = 'none';
              }, 3000);
            }, 1000);
          }
        }
      });

      socket.on('training complete', (data) => {
        console.log('Formation terminée:', data);
        if (data.sessionId === currentSessionId) {
          // Afficher un message de succès
          alert(`Formation terminée avec succès! ${data.questionCount} question(s) traitée(s) en ${formatTime(data.totalTime)}.`);
        }
      });

      // Événements
      launchTrainingButton.addEventListener('click', launchTraining);
      refreshButton.addEventListener('click', loadSessions);

      // Charger les sessions au chargement de la page
      loadSessions();
    });
  </script>
</body>
</html>
