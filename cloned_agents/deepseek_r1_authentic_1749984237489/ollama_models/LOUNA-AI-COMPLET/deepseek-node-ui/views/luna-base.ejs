<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vision Ultra - Interface Cognitive Avancée</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <% if (page === 'luna-program-knowledge') { %>
    <link rel="stylesheet" href="/css/luna-program-knowledge.css">
  <% } %>
  <style>
    :root {
      --luna-primary: #9c89b8;
      --luna-secondary: #f0a6ca;
      --luna-accent: #b8bedd;
      --luna-dark: #1a1a2e;
      --luna-light: #edf2fb;
      --luna-gradient: linear-gradient(135deg, #9c89b8, #f0a6ca);
      --header-height: 60px;
    }

    html, body {
      font-family: 'Quicksand', sans-serif;
      background-color: var(--luna-dark);
      color: var(--luna-light);
      height: 100%;
      max-height: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden !important;
      margin: 0;
      padding: 0;
    }

    .navbar-luna {
      background: var(--luna-gradient);
      box-shadow: 0 2px 15px rgba(0,0,0,0.2);
    }

    .navbar-brand {
      font-weight: 700;
      font-size: 1.8rem;
    }

    .nav-link {
      font-weight: 500;
      color: var(--luna-dark) !important;
      transition: all 0.3s ease;
      position: relative;
      padding: 0.5rem 1rem;
      margin: 0 0.2rem;
      border-radius: 8px;
    }

    .nav-link:hover {
      color: white !important;
      transform: translateY(-2px);
      background-color: rgba(0, 0, 0, 0.1);
    }

    .nav-link.active {
      color: white !important;
      background-color: rgba(0, 0, 0, 0.2);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      font-weight: 600;
    }

    .nav-link.active::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 10%;
      width: 80%;
      height: 3px;
      background: white;
      border-radius: 3px;
    }

    .luna-container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 2rem;
    }

    .luna-card {
      background: rgba(26, 26, 46, 0.7);
      border: 1px solid rgba(184, 190, 221, 0.2);
      border-radius: 15px;
      padding: 1.5rem;
      margin-bottom: 1.5rem;
      box-shadow: 0 10px 25px rgba(0,0,0,0.2);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
    }

    .luna-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 35px rgba(0,0,0,0.25);
      border-color: var(--luna-secondary);
    }

    .status-indicator {
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 8px;
      transition: all 0.5s ease;
    }

    .status-active {
      background-color: #4caf50;
      box-shadow: 0 0 10px #4caf50;
      animation: pulse 2s infinite;
    }

    .status-inactive {
      background-color: #f44336;
    }

    @keyframes pulse {
      0% {
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
      }
      70% {
        box-shadow: 0 0 0 10px rgba(76, 175, 80, 0);
      }
      100% {
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
      }
    }

    .btn-luna {
      background: var(--luna-gradient);
      border: none;
      border-radius: 25px;
      font-weight: 600;
      padding: 0.5rem 1.5rem;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(0,0,0,0.15);
    }

    .btn-luna:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 20px rgba(0,0,0,0.2);
    }

    .btn-luna-outline {
      background: transparent;
      border: 2px solid var(--luna-accent);
      color: var(--luna-accent);
    }

    .btn-luna-outline:hover {
      background: var(--luna-accent);
      color: var(--luna-dark);
    }

    /* Styles pour les menus déroulants */
    .dropdown-menu-dark {
      background-color: var(--luna-dark);
      border: 1px solid var(--luna-accent);
      box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    }

    .dropdown-item {
      color: var(--luna-light);
      transition: all 0.2s ease;
      padding: 0.5rem 1rem;
    }

    .dropdown-item:hover {
      background: rgba(156, 137, 184, 0.2);
      transform: translateX(5px);
    }

    .dropdown-item i {
      margin-right: 8px;
      color: var(--luna-accent);
    }

    /* Style pour le menu actif */
    .dropdown-item.active,
    .dropdown-item:active {
      background-color: rgba(240, 166, 202, 0.3);
      color: white;
    }
  </style>
</head>
<body>
  <!-- Barre de navigation -->
  <nav class="navbar navbar-expand-lg navbar-dark navbar-luna">
    <div class="container">
      <a class="navbar-brand" href="/luna">
        <i class="bi bi-eye me-2"></i> Vision Ultra
      </a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarLuna">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarLuna">
        <ul class="navbar-nav me-auto">
          <!-- Menu principal -->
          <li class="nav-item">
            <a class="nav-link" href="/luna"><i class="bi bi-speedometer2"></i> Accueil</a>
          </li>

          <!-- Menu déroulant Interfaces -->
          <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="#" id="interfacesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="bi bi-grid-3x3-gap"></i> Interfaces
            </a>
            <ul class="dropdown-menu dropdown-menu-dark" aria-labelledby="interfacesDropdown">
              <li><a class="dropdown-item" href="/luna/cognitive"><i class="bi bi-braces"></i> Cognitif</a></li>
              <li><a class="dropdown-item" href="/luna/memory"><i class="bi bi-hdd"></i> Mémoire</a></li>
              <li><a class="dropdown-item" href="/luna/reflection"><i class="bi bi-lightbulb"></i> Réflexion</a></li>
              <li><a class="dropdown-item" href="/luna/accelerators"><i class="bi bi-lightning-charge"></i> Accélérateurs</a></li>
              <li><a class="dropdown-item" href="/luna/brain"><i class="bi bi-diagram-3"></i> Cerveau</a></li>
              <li><a class="dropdown-item" href="/luna/training"><i class="bi bi-graduation-cap"></i> Formation</a></li>
              <li><a class="dropdown-item" href="/luna/prompts"><i class="bi bi-lightning"></i> Prompts</a></li>
              <li><a class="dropdown-item" href="/luna/code"><i class="bi bi-code-slash"></i> Code</a></li>
            </ul>
          </li>

          <!-- Menu déroulant Système -->
          <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="#" id="systemDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="bi bi-gear-wide-connected"></i> Système
            </a>
            <ul class="dropdown-menu dropdown-menu-dark" aria-labelledby="systemDropdown">
              <li><a class="dropdown-item" href="/luna/mcp"><i class="bi bi-cpu"></i> MCP</a></li>
              <li><a class="dropdown-item" href="/luna/thermal"><i class="bi bi-thermometer-half"></i> Thermique</a></li>
              <li><a class="dropdown-item" href="/luna/security"><i class="bi bi-shield-lock"></i> Sécurité</a></li>
              <li><a class="dropdown-item" href="/luna/internet"><i class="bi bi-globe"></i> Internet</a></li>
              <li><a class="dropdown-item" href="/luna/connectivity"><i class="bi bi-wifi"></i> Connectivité</a></li>
              <li><a class="dropdown-item" href="/luna/media"><i class="bi bi-film"></i> Multimédia</a></li>
              <li><a class="dropdown-item" href="/luna/backup"><i class="bi bi-archive"></i> Sauvegarde</a></li>
              <li><a class="dropdown-item" href="/luna/settings"><i class="bi bi-gear"></i> Paramètres</a></li>
            </ul>
          </li>
        </ul>

        <!-- Boutons de sécurité à droite -->
        <div class="d-flex">
          <a class="btn btn-primary me-2" href="/luna/security#vpn" style="color: white !important; font-weight: bold; box-shadow: 0 0 10px rgba(0,0,0,0.5);">
            <i class="bi bi-shield-check"></i> VPN
          </a>
          <a class="btn btn-success" href="/luna/security#antivirus" style="color: white !important; font-weight: bold; box-shadow: 0 0 10px rgba(0,0,0,0.5);">
            <i class="bi bi-virus"></i> ANTIVIRUS
          </a>
        </div>
      </div>
    </div>
  </nav>

  <div class="luna-container">
    <div class="row align-items-center mb-4">
      <div class="col-md-8">
        <h1 class="mb-0" style="font-weight: 700; letter-spacing: -1px;">Vision Ultra</h1>
        <p class="text-muted">Système cognitif avancé créé par Jean Passave à Sainte-Anne, Guadeloupe (97180)</p>
        <div class="mt-2 d-flex align-items-center">
          <div class="me-3">
            <i class="bi bi-calendar3 me-1" style="color: var(--luna-accent);"></i>
            <span id="current-date" style="font-weight: 500;">Chargement de la date...</span>
          </div>
          <div>
            <i class="bi bi-clock me-1" style="color: var(--luna-accent);"></i>
            <span id="current-time" style="font-weight: 500;">00:00:00</span>
          </div>
        </div>
      </div>
      <div class="col-md-4 text-end">
        <div class="d-flex align-items-center justify-content-end">
          <span class="me-3">Statut du système:</span>
          <span id="systemStatus">
            <span class="status-indicator status-active"></span>
            <span>Actif</span>
          </span>
          <button id="toggleSystem" class="btn btn-danger ms-3">
            <i class="bi bi-power"></i> Désactiver
          </button>
        </div>
      </div>
    </div>

    <!-- Contenu principal à insérer ici -->
    <% if (typeof content !== 'undefined') { %>
      <!-- Contenu fourni directement depuis le contrôleur -->
      <%- content %>
    <% } else if (typeof page !== 'undefined') { %>
      <!-- Inclusion des pages non-circulaires uniquement -->
      <% if (page !== 'luna-chat') { %>
        <%- include(page + '.ejs') %>
      <% } %>
    <% } else { %>
      <!-- Remarque : Pour l'interface cognitive, nous utilisons une structure différente avec des classes spécifiques -->
    <% } %>
  </div>

  <!-- Bibliothèques JavaScript nécessaires -->
  <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>
  <script src="/socket.io/socket.io.js"></script>
  <script src="/js/luna-security.js"></script>

  <script>
    $(document).ready(function() {
      // Script global pour Luna
      console.log('Interface Luna chargée');

      // Marquer l'onglet actif dans la navigation
      function setActiveNavLink() {
        // Récupérer le chemin actuel
        const currentPath = window.location.pathname;

        // Supprimer toutes les classes actives
        $('.navbar-nav .nav-link').removeClass('active');
        $('.dropdown-item').removeClass('active');

        // Activer le lien d'accueil si nous sommes sur la page d'accueil
        if (currentPath === '/luna') {
          $('.navbar-nav .nav-link[href="/luna"]').addClass('active');
          return;
        }

        // Vérifier les liens directs dans la barre de navigation
        let activeFound = false;
        $('.navbar-nav .nav-link').each(function() {
          const linkPath = $(this).attr('href');
          if (linkPath && linkPath !== '#' && linkPath !== '/luna' && currentPath.startsWith(linkPath)) {
            $(this).addClass('active');
            activeFound = true;
          }
        });

        // Vérifier les éléments de menu déroulant
        $('.dropdown-item').each(function() {
          const linkPath = $(this).attr('href');
          if (linkPath && currentPath.startsWith(linkPath)) {
            $(this).addClass('active');
            // Activer également le menu parent
            const dropdownId = $(this).closest('.dropdown-menu').attr('aria-labelledby');
            $(`#${dropdownId}`).addClass('active');
            activeFound = true;
          }
        });

        // Si aucun lien actif n'a été trouvé, vérifier les fragments d'URL
        if (!activeFound && window.location.hash) {
          const hash = window.location.hash;
          if (hash === '#vpn' || hash === '#antivirus') {
            $('.navbar-nav .dropdown-item[href="/luna/security"]').addClass('active');
            $('#systemDropdown').addClass('active');
          }
        }
      }

      // Appeler la fonction au chargement de la page
      setActiveNavLink();

      // Ajouter un indicateur de statut de connexion dans la barre de navigation
      const navbarBrand = $('.navbar-brand');
      navbarBrand.after('<div class="connection-status-indicator connected" title="Connecté au serveur"></div>');

      // Ajouter des styles pour l'indicateur de connexion
      $('<style>')
        .text(`
          .connection-status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-left: 10px;
            transition: all 0.3s ease;
          }
          .connection-status-indicator.connected {
            background-color: #4caf50;
            box-shadow: 0 0 5px #4caf50;
          }
          .connection-status-indicator.disconnected {
            background-color: #f44336;
            box-shadow: 0 0 5px #f44336;
            animation: pulse 1.5s infinite;
          }
          @keyframes pulse {
            0% { opacity: 0.5; }
            50% { opacity: 1; }
            100% { opacity: 0.5; }
          }
          .nav-link.active-vpn {
            color: #4caf50 !important;
            font-weight: bold;
          }
        `)
        .appendTo('head');

      // Fonctionnalité du bouton d'activation système
      $('#toggleSystem').on('click', function() {
        const isActive = $('#systemStatus .status-indicator').hasClass('status-active');

        if (isActive) {
          // Désactiver le système
          $('#systemStatus .status-indicator').removeClass('status-active').addClass('status-inactive');
          $('#systemStatus span:not(.status-indicator)').text('Inactif');
          $(this).html('<i class="bi bi-power"></i> Activer');

          // Si la connexion Socket.IO est disponible, tenter de reconnecter
          if (typeof socket !== 'undefined' && socket) {
            if (!socket.connected) {
              socket.connect();
            }
          }
        } else {
          // Activer le système
          $('#systemStatus .status-indicator').removeClass('status-inactive').addClass('status-active');
          $('#systemStatus span:not(.status-indicator)').text('Actif');
          $(this).html('<i class="bi bi-power"></i> Désactiver');
        }
      });
    });
  </script>
</body>
</html>
