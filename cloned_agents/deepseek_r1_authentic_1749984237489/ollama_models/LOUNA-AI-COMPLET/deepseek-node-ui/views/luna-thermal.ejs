<div class="row mt-4">
  <!-- Panneau d'état de la température -->
  <div class="col-md-8">
    <!-- Inclusion du module de mode sommeil -->
    <%- include('luna-sleep-mode') %>
    <div class="luna-card">
      <h3 class="mb-3 d-flex align-items-center justify-content-between">
        <div><i class="bi bi-thermometer-half me-2"></i> Régulation Thermique Réelle</div>
        <div>
          <span id="temperature-status" class="me-3">
            <span class="status-indicator status-inactive"></span>
            <span>Chargement...</span>
          </span>
        </div>
      </h3>
      
      <div class="row">
        <div class="col-md-6">
          <!-- Température CPU et Ventilateur -->
          <div class="mb-4">
            <h4>Température Système</h4>
            <div class="d-flex align-items-center mb-3">
              <div style="width: 140px;">
                <canvas id="cpu-gauge" width="120" height="120"></canvas>
              </div>
              <div class="ms-3">
                <div><strong>CPU: </strong><span id="cpu-temp">--</span>°C</div>
                <div><strong>Ventilateur: </strong><span id="fan-speed">--</span> RPM</div>
                <div><strong>Dernière mise à jour: </strong><span id="last-update">--</span></div>
              </div>
            </div>
            
            <div class="d-flex justify-content-between mt-3">
              <button id="trigger-cooling" class="btn btn-luna">
                <i class="bi bi-snow me-1"></i> Déclencher Refroidissement
              </button>
              
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="auto-regulation" checked>
                <label class="form-check-label" for="auto-regulation">Régulation automatique</label>
              </div>
            </div>
          </div>
          
          <!-- Statut du cycle de refroidissement -->
          <div>
            <h4>Cycle de Refroidissement</h4>
            <div class="progress mb-2" style="height: 25px;">
              <div id="cooling-progress" class="progress-bar bg-info" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
            </div>
            <div class="text-muted" id="cooling-status">Aucun cycle de refroidissement en cours</div>
          </div>
        </div>
        
        <div class="col-md-6">
          <!-- Carte thermique de la mémoire -->
          <h4>Mémoire Thermique</h4>
          <div class="thermal-map mb-3">
            <div class="zone-item" data-zone="1">
              <div class="zone-temp">100°</div>
              <div class="zone-bar">
                <div class="zone-fill z1-fill" style="height: 0%;"></div>
              </div>
              <div class="zone-label">Zone 1</div>
              <div class="zone-count">0</div>
            </div>
            <div class="zone-item" data-zone="2">
              <div class="zone-temp">80°</div>
              <div class="zone-bar">
                <div class="zone-fill z2-fill" style="height: 0%;"></div>
              </div>
              <div class="zone-label">Zone 2</div>
              <div class="zone-count">0</div>
            </div>
            <div class="zone-item" data-zone="3">
              <div class="zone-temp">60°</div>
              <div class="zone-bar">
                <div class="zone-fill z3-fill" style="height: 0%;"></div>
              </div>
              <div class="zone-label">Zone 3</div>
              <div class="zone-count">0</div>
            </div>
            <div class="zone-item" data-zone="4">
              <div class="zone-temp">40°</div>
              <div class="zone-bar">
                <div class="zone-fill z4-fill" style="height: 0%;"></div>
              </div>
              <div class="zone-label">Zone 4</div>
              <div class="zone-count">0</div>
            </div>
            <div class="zone-item" data-zone="5">
              <div class="zone-temp">20°</div>
              <div class="zone-bar">
                <div class="zone-fill z5-fill" style="height: 0%;"></div>
              </div>
              <div class="zone-label">Zone 5</div>
              <div class="zone-count">0</div>
            </div>
            <div class="zone-item" data-zone="6">
              <div class="zone-temp">5°</div>
              <div class="zone-bar">
                <div class="zone-fill z6-fill" style="height: 0%;"></div>
              </div>
              <div class="zone-label">Zone 6</div>
              <div class="zone-count">0</div>
            </div>
          </div>
          
          <!-- Zone active -->
          <div>
            <h5>Zone active: <span id="active-zone">--</span></h5>
            <div id="zone-message" class="mt-2 alert alert-info">
              Chargement des données de mémoire thermique...
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Graphique de température -->
    <div class="luna-card mt-3">
      <h3><i class="bi bi-graph-up me-2"></i> Historique de Température</h3>
      <canvas id="temperature-chart" width="100%" height="250"></canvas>
    </div>
  </div>
  
  <!-- Panneau latéral avec les contrôles et le statut d'archivage -->
  <div class="col-md-4">
    <!-- Configuration des accélérateurs Kyber -->
    <div class="luna-card mb-3">
      <h4><i class="bi bi-speedometer2 me-2"></i> Accélérateurs Kyber</h4>
      <div id="kyber-status">Chargement...</div>
      
      <h5 class="mt-3">Efficacité par type</h5>
      <div class="progress-container">
        <label>Mémoire:</label>
        <div class="progress mb-2" style="height: 20px;">
          <div id="memory-efficiency" class="progress-bar bg-primary" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
        </div>
        
        <label>Vidéo:</label>
        <div class="progress mb-2" style="height: 20px;">
          <div id="video-efficiency" class="progress-bar bg-success" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
        </div>
        
        <label>Audio:</label>
        <div class="progress mb-2" style="height: 20px;">
          <div id="audio-efficiency" class="progress-bar bg-info" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
        </div>
        
        <label>Thermique:</label>
        <div class="progress mb-2" style="height: 20px;">
          <div id="thermal-efficiency" class="progress-bar bg-warning" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
        </div>
      </div>
    </div>
    
    <!-- Statut d'archivage -->
    <div class="luna-card">
      <h4><i class="bi bi-archive me-2"></i> Archivage Thermique</h4>
      <div class="mb-3">
        <div class="d-flex align-items-center mb-2">
          <div>
            <div><strong>État: </strong><span id="archive-status">Aucun processus d'archivage en cours</span></div>
            <div><strong>Dernier archivage: </strong><span id="last-archive">--</span></div>
            <div><strong>Zone thermique: </strong><span id="thermal-zone-info">Curseur glissant adapté à la température</span></div>
            <div><strong>Éléments en zone 6: </strong><span id="zone6-count">0</span></div>
          </div>
        </div>
      </div>
      
      <button id="force-archive" class="btn btn-sm btn-luna">
        <i class="bi bi-arrow-down-circle me-1"></i> Forcer l'archivage profond
      </button>
    </div>
  </div>
</div>

<style>
  .thermal-map {
    display: flex;
    justify-content: space-between;
    height: 180px;
    margin-bottom: 1rem;
  }
  
  .zone-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 16%;
  }
  
  .zone-temp {
    font-size: 0.8rem;
    margin-bottom: 5px;
  }
  
  .zone-bar {
    width: 30px;
    height: 120px;
    background-color: rgba(255,255,255,0.1);
    border-radius: 15px;
    overflow: hidden;
    position: relative;
  }
  
  .zone-fill {
    position: absolute;
    bottom: 0;
    width: 100%;
    transition: height 0.5s ease;
  }
  
  .z1-fill { background: linear-gradient(to top, #ff4500, #ff8c00); }
  .z2-fill { background: linear-gradient(to top, #ff8c00, #ffd700); }
  .z3-fill { background: linear-gradient(to top, #ffd700, #adff2f); }
  .z4-fill { background: linear-gradient(to top, #adff2f, #00bfff); }
  .z5-fill { background: linear-gradient(to top, #00bfff, #1e90ff); }
  .z6-fill { background: linear-gradient(to top, #1e90ff, #4b0082); }
  
  .zone-label {
    font-size: 0.8rem;
    margin-top: 5px;
  }
  
  .zone-count {
    font-size: 0.75rem;
    color: rgba(255,255,255,0.7);
  }
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
  $(document).ready(function() {
    // Initialiser les graphiques et jauges
    const cpuGaugeCanvas = document.getElementById('cpu-gauge');
    const cpuGaugeCtx = cpuGaugeCanvas.getContext('2d');
    let cpuGauge = null;
    
    const tempChartCanvas = document.getElementById('temperature-chart');
    const tempChartCtx = tempChartCanvas.getContext('2d');
    let temperatureChart = null;
    
    // Variables d'état
    let coolingInProgress = false;
    let regulationEnabled = true;
    let temperatureData = {
      timestamps: [],
      cpuTemps: [],
      fanSpeeds: []
    };
    
    // Initialiser la jauge de température CPU
    function initCpuGauge() {
      cpuGauge = new Chart(cpuGaugeCtx, {
        type: 'doughnut',
        data: {
          datasets: [{
            data: [0, 100],
            backgroundColor: [
              'rgba(255, 99, 132, 0.8)',
              'rgba(30, 30, 30, 0.2)'
            ],
            borderWidth: 0
          }]
        },
        options: {
          circumference: 270,
          rotation: -135,
          cutout: '70%',
          responsive: true,
          maintainAspectRatio: true,
          animation: {
            animateRotate: true,
            animateScale: true
          },
          plugins: {
            tooltip: {
              enabled: false
            },
            legend: {
              display: false
            }
          }
        }
      });
    }
    
    // Initialiser le graphique d'historique de température
    function initTemperatureChart() {
      temperatureChart = new Chart(tempChartCtx, {
        type: 'line',
        data: {
          labels: [],
          datasets: [
            {
              label: 'Température CPU (°C)',
              data: [],
              borderColor: 'rgba(255, 99, 132, 1)',
              backgroundColor: 'rgba(255, 99, 132, 0.1)',
              borderWidth: 2,
              fill: true,
              tension: 0.4
            },
            {
              label: 'Ventilateur (RPM/100)',
              data: [],
              borderColor: 'rgba(54, 162, 235, 1)',
              backgroundColor: 'rgba(54, 162, 235, 0.0)',
              borderWidth: 1,
              borderDash: [5, 5],
              fill: false,
              tension: 0.2,
              yAxisID: 'y1'
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: true,
          interaction: {
            mode: 'index',
            intersect: false
          },
          scales: {
            x: {
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: 'rgba(255, 255, 255, 0.7)'
              }
            },
            y: {
              min: 30,
              max: 90,
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: 'rgba(255, 255, 255, 0.7)'
              },
              title: {
                display: true,
                text: 'Température (°C)',
                color: 'rgba(255, 255, 255, 0.7)'
              }
            },
            y1: {
              min: 0,
              max: 60,
              position: 'right',
              grid: {
                drawOnChartArea: false
              },
              ticks: {
                color: 'rgba(54, 162, 235, 0.7)',
                callback: function(value) {
                  return value * 100;
                }
              },
              title: {
                display: true,
                text: 'Ventilateur (RPM)',
                color: 'rgba(54, 162, 235, 0.7)'
              }
            }
          },
          plugins: {
            legend: {
              labels: {
                color: 'rgba(255, 255, 255, 0.7)'
              }
            }
          }
        }
      });
    }
    
    // Mettre à jour les données de température et l'interface
    function updateTemperatureData() {
      $.ajax({
        url: '/luna/api/temperature',
        type: 'GET',
        success: function(response) {
          if (response.success && response.data) {
            const data = response.data;
            
            // Mettre à jour la jauge CPU
            if (cpuGauge) {
              cpuGauge.data.datasets[0].data[0] = data.cpu;
              cpuGauge.data.datasets[0].data[1] = 100 - data.cpu;
              
              // Ajuster la couleur en fonction de la température
              let color = 'rgba(0, 255, 0, 0.8)'; // Froid (vert)
              if (data.cpu > 75) {
                color = 'rgba(255, 0, 0, 0.8)'; // Chaud (rouge)
              } else if (data.cpu > 60) {
                color = 'rgba(255, 165, 0, 0.8)'; // Tiède (orange)
              } else if (data.cpu > 45) {
                color = 'rgba(255, 255, 0, 0.8)'; // Normal (jaune)
              }
              cpuGauge.data.datasets[0].backgroundColor[0] = color;
              
              cpuGauge.update();
            }
            
            // Mettre à jour les étiquettes
            $('#cpu-temp').text(data.cpu.toFixed(1));
            $('#fan-speed').text(Math.round(data.fan));
            $('#last-update').text(new Date().toLocaleTimeString());
            
            // Mettre à jour le statut de régulation
            $('#auto-regulation').prop('checked', data.regulationEnabled);
            
            // Mettre à jour le statut du cycle de refroidissement
            if (data.coolingCycleCount > 0) {
              coolingInProgress = true;
              const progress = (data.coolingCycleCount / 3) * 100; // 3 cycles max
              $('#cooling-progress').css('width', progress + '%').attr('aria-valuenow', progress).text(progress.toFixed(0) + '%');
              $('#cooling-status').text(`Cycle de refroidissement #${data.coolingCycleCount} en cours`);
            } else if (coolingInProgress) {
              coolingInProgress = false;
              $('#cooling-progress').css('width', '0%').attr('aria-valuenow', 0).text('0%');
              $('#cooling-status').text('Aucun cycle de refroidissement en cours');
            }
            
            // Détecter le type de module de régulation thermique
            const usingSlidingZones = response.moduleType === 'sliding-zones';
            
            if (usingSlidingZones && data.zones) {
              // Nouveau système de zones thermiques glissantes
              const zones = data.zones;
              const activeZoneIndex = data.systemState.activeZone - 1;
              const activeZone = zones[activeZoneIndex];
              
              // Mettre à jour l'indicateur de zone active
              $('#active-zone').text(`Zone ${activeZone.id} (${activeZone.idealTemp}° virtuel / ${activeZone.realTemp.toFixed(1)}° réel)`);        
              $('#thermal-zone-info').text(`Curseur: ${data.systemState.tempRange.current.min.toFixed(1)}° - ${data.systemState.tempRange.current.max.toFixed(1)}°`);        
              
              // Message de statut adapté aux zones glissantes
              let zoneMessage = '';
              if (data.sleepMode) {
                zoneMessage = `Mode sommeil actif. Les informations descendent vers les zones profondes (Zone ${activeZone.id}, ${activeZone.idealTemp}°)`;
              } else {
                switch(activeZone.id) {
                  case 1:
                    zoneMessage = `Température élevée (${activeZone.realTemp.toFixed(1)}°). Mémoire en zone immédiate ${activeZone.idealTemp}°`;
                    break;
                  case 2:
                    zoneMessage = `Température chaude (${activeZone.realTemp.toFixed(1)}°). Mémoire à court terme ${activeZone.idealTemp}°`;
                    break;
                  case 3:
                    zoneMessage = `Température modérée (${activeZone.realTemp.toFixed(1)}°). Mémoire de travail ${activeZone.idealTemp}°`;
                    break;
                  case 4:
                    zoneMessage = `Température normale (${activeZone.realTemp.toFixed(1)}°). Mémoire à moyen terme ${activeZone.idealTemp}°`;
                    break;
                  case 5:
                    zoneMessage = `Température fraîche (${activeZone.realTemp.toFixed(1)}°). Zone créative/rêve ${activeZone.idealTemp}°`;
                    break;
                  case 6:
                    zoneMessage = `Température basse (${activeZone.realTemp.toFixed(1)}°). Archivage profond ${activeZone.idealTemp}°`;
                    break;
                }
              }
              $('#zone-message').text(zoneMessage);
              
              // Mise à jour de la carte thermique avec les zones glissantes
              zones.forEach(zone => {
                const zoneId = zone.id;
                const activity = zone.activity;
                $(`.z${zoneId}-fill`).css('height', `${activity}%`);
                $(`.zone-item[data-zone="${zoneId}"] .zone-count`).text(zoneId === activeZone.id ? 'ACTIF' : '');
                
                // Mettre à jour les étiquettes de température réelle
                $(`.zone-item[data-zone="${zoneId}"] .zone-temp`).html(`${zone.idealTemp}°<br><small>${zone.realTemp.toFixed(1)}°</small>`);
              });
            } else {
              // Ancien système de régulation thermique
              let activeZone = 1;
              if (data.cpu <= 35) activeZone = 6;
              else if (data.cpu <= 45) activeZone = 5;
              else if (data.cpu <= 55) activeZone = 4;
              else if (data.cpu <= 65) activeZone = 3;
              else if (data.cpu <= 75) activeZone = 2;
              
              $('#active-zone').text(`Zone ${activeZone} (${activeZone === 6 ? '5°' : activeZone === 5 ? '20°' : activeZone === 4 ? '40°' : activeZone === 3 ? '60°' : activeZone === 2 ? '80°' : '100°'})`);
              
              // Message de statut
              let zoneMessage = '';
              switch(activeZone) {
                case 1:
                  zoneMessage = 'CPU très chaud. Les informations sont en mémoire immédiate (Zone 1, 100°)';
                  break;
                case 2:
                  zoneMessage = 'CPU chaud. Les informations circulent en mémoire à court terme (Zone 2, 80°)';
                  break;
                case 3:
                  zoneMessage = 'CPU tiède. Les informations sont traitées en mémoire de travail (Zone 3, 60°)';
                  break;
                case 4:
                  zoneMessage = 'CPU normal. Les informations descendent en mémoire à moyen terme (Zone 4, 40°)';
                  break;
                case 5:
                  zoneMessage = 'CPU frais. La zone créative et de rêve est active (Zone 5, 20°)';
                  break;
                case 6:
                  zoneMessage = 'CPU froid. L\'archivage profond est possible (Zone 6, 5°)';
                  break;
              }
              $('#zone-message').text(zoneMessage);
            }
            
            // Ajouter les données à l'historique pour le graphique
            const timestamp = new Date().toLocaleTimeString();
            
            temperatureData.timestamps.push(timestamp);
            temperatureData.cpuTemps.push(data.cpu);
            temperatureData.fanSpeeds.push(data.fan / 100); // Diviser pour l'échelle
            
            // Garder uniquement les 20 dernières mesures
            if (temperatureData.timestamps.length > 20) {
              temperatureData.timestamps.shift();
              temperatureData.cpuTemps.shift();
              temperatureData.fanSpeeds.shift();
            }
            
            // Mettre à jour le graphique
            if (temperatureChart) {
              temperatureChart.data.labels = temperatureData.timestamps;
              temperatureChart.data.datasets[0].data = temperatureData.cpuTemps;
              temperatureChart.data.datasets[1].data = temperatureData.fanSpeeds;
              temperatureChart.update();
            }
          }
        },
        error: function(error) {
          console.error('Erreur lors de la récupération des données de température:', error);
        }
      });
    }
    
    // Fonction pour mettre à jour l'état des accélérateurs Kyber
    function updateKyberAccelerators() {
      // Simuler des données pour l'instant (à connecter à une API réelle plus tard)
      const memory = Math.random() * 40 + 110; // 110-150%
      const video = Math.random() * 30 + 120; // 120-150%
      const audio = Math.random() * 35 + 115; // 115-150%
      const thermal = Math.random() * 50 + 150; // 150-200%
      
      $('#memory-efficiency').css('width', memory + '%').attr('aria-valuenow', memory).text(memory.toFixed(1) + '%');
      $('#video-efficiency').css('width', video + '%').attr('aria-valuenow', video).text(video.toFixed(1) + '%');
      $('#audio-efficiency').css('width', audio + '%').attr('aria-valuenow', audio).text(audio.toFixed(1) + '%');
      $('#thermal-efficiency').css('width', thermal + '%').attr('aria-valuenow', thermal).text(thermal.toFixed(1) + '%');
      
      $('#kyber-status').html(`
        <div><strong>Accélérateurs actifs: </strong>24</div>
        <div><strong>Efficacité moyenne: </strong>${((memory + video + audio + thermal) / 4).toFixed(1)}%</div>
        <div><strong>Débit total: </strong>${Math.round((memory + video + audio + thermal) * 30)} MB/s</div>
      `);
    }
    
    // Fonction pour mettre à jour la carte thermique de mémoire
    function updateThermalMap() {
      // Simuler des données pour les zones (à connecter à une API réelle plus tard)
      const zoneData = [
        { count: Math.floor(Math.random() * 5) + 3, fill: Math.random() * 40 + 60 }, // Zone 1
        { count: Math.floor(Math.random() * 4) + 1, fill: Math.random() * 30 + 30 }, // Zone 2
        { count: Math.floor(Math.random() * 3) + 0, fill: Math.random() * 25 + 15 }, // Zone 3
        { count: Math.floor(Math.random() * 2) + 0, fill: Math.random() * 20 + 5 },  // Zone 4
        { count: Math.floor(Math.random() * 2) + 0, fill: Math.random() * 15 },     // Zone 5
        { count: Math.floor(Math.random() * 1) + 0, fill: Math.random() * 10 }      // Zone 6
      ];
      
      // Mettre à jour l'affichage
      for (let i = 0; i < 6; i++) {
        const zone = i + 1;
        $(`.z${zone}-fill`).css('height', zoneData[i].fill + '%');
        $(`.zone-item[data-zone="${zone}"] .zone-count`).text(zoneData[i].count);
      }
      
      // Mettre à jour le nombre d'éléments en zone 6
      $('#zone6-count').text(zoneData[5].count);
      
      // Mettre à jour le dernier archivage
      if (Math.random() > 0.7) { // Simuler des mises à jour occasionnelles
        $('#last-archive').text(new Date().toLocaleTimeString());
      }
    }
    
    // Initialiser l'interface
    initCpuGauge();
    initTemperatureChart();
    
    // Mettre à jour périodiquement
    updateTemperatureData();
    updateKyberAccelerators();
    updateThermalMap();
    
    setInterval(updateTemperatureData, 5000);      // Toutes les 5 secondes
    setInterval(updateKyberAccelerators, 10000);   // Toutes les 10 secondes
    setInterval(updateThermalMap, 15000);          // Toutes les 15 secondes
    
    // Gestionnaire pour le déclenchement manuel du refroidissement
    $('#trigger-cooling').on('click', function() {
      $.ajax({
        url: '/luna/api/cooling/trigger',
        type: 'POST',
        success: function(response) {
          if (response.success) {
            alert('Cycle de refroidissement déclenché avec succès!');
            // Forcer une mise à jour immédiate des données
            updateTemperatureData();
          } else {
            alert('Erreur lors du déclenchement du cycle de refroidissement: ' + response.error);
          }
        },
        error: function() {
          alert('Erreur de communication avec le serveur');
        }
      });
    });
    
    // Gestionnaire pour l'activation/désactivation de la régulation automatique
    $('#auto-regulation').on('change', function() {
      const enabled = $(this).is(':checked');
      
      $.ajax({
        url: '/luna/api/temperature/regulation',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ enabled: enabled }),
        success: function(response) {
          if (!response.success) {
            alert('Erreur lors du changement de mode de régulation: ' + response.error);
            // Rétablir l'état précédent
            $('#auto-regulation').prop('checked', !enabled);
          }
        },
        error: function() {
          alert('Erreur de communication avec le serveur');
          // Rétablir l'état précédent
          $('#auto-regulation').prop('checked', !enabled);
        }
      });
    });
    
    // Gestionnaire pour forcer l'archivage profond
    $('#force-archive').on('click', function() {
      $('#archive-status').text('Archivage profond en cours...');
      
      // Simuler un processus d'archivage
      setTimeout(function() {
        $('#archive-status').text('Archivage profond terminé');
        $('#last-archive').text(new Date().toLocaleTimeString());
        updateThermalMap();
      }, 3000);
    });
  });
</script>
