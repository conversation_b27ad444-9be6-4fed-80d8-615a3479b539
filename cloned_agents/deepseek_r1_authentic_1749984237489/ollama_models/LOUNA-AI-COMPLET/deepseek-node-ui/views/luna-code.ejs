<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %></title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="/css/luna-accelerators.css">
  <link rel="stylesheet" href="/css/luna-code.css">
  <style>
    :root {
      --luna-primary: #9c89b8;
      --luna-secondary: #f0a6ca;
      --luna-accent: #b8bedd;
      --luna-dark: #1a1a2e;
      --luna-light: #edf2fb;
      --luna-gradient: linear-gradient(135deg, #9c89b8, #f0a6ca);
      --header-height: 60px;
    }

    body {
      font-family: 'Quicksand', sans-serif;
      background-color: var(--luna-dark);
      color: var(--luna-light);
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }

    .navbar-luna {
      background: var(--luna-gradient);
      box-shadow: 0 2px 15px rgba(0,0,0,0.2);
    }

    .navbar-brand {
      font-weight: 700;
      font-size: 1.8rem;
    }

    .nav-link {
      font-weight: 500;
      color: var(--luna-dark) !important;
      transition: all 0.3s ease;
    }

    .nav-link:hover {
      color: white !important;
      transform: translateY(-2px);
    }

    .luna-container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 2rem;
    }

    .luna-card {
      background: rgba(26, 26, 46, 0.7);
      border: 1px solid rgba(184, 190, 221, 0.2);
      border-radius: 15px;
      padding: 1.5rem;
      margin-bottom: 1.5rem;
      box-shadow: 0 10px 25px rgba(0,0,0,0.2);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
    }

    .luna-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 35px rgba(0,0,0,0.25);
      border-color: var(--luna-secondary);
    }

    .btn-luna {
      background: var(--luna-gradient);
      border: none;
      border-radius: 25px;
      font-weight: 600;
      padding: 0.5rem 1.5rem;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(0,0,0,0.15);
    }

    .btn-luna:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 20px rgba(0,0,0,0.2);
    }

    .btn-luna-outline {
      background: transparent;
      border: 2px solid var(--luna-accent);
      color: var(--luna-accent);
    }

    .btn-luna-outline:hover {
      background: var(--luna-accent);
      color: var(--luna-dark);
    }
  </style>
</head>
<body>
  <!-- Barre de navigation -->
  <nav class="navbar navbar-expand-lg navbar-dark navbar-luna">
    <div class="container">
      <a class="navbar-brand" href="/luna">
        <i class="bi bi-moon-stars me-2"></i> Luna
      </a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarLuna">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarLuna">
        <ul class="navbar-nav ms-auto">
          <li class="nav-item">
            <a class="nav-link" href="/luna"><i class="bi bi-house"></i> Accueil</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/luna/prompts"><i class="bi bi-lightning"></i> Prompts</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/luna/reflection"><i class="bi bi-lightbulb"></i> Réflexion</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/luna/memory"><i class="bi bi-hdd"></i> Mémoire</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/luna/brain"><i class="bi bi-diagram-3"></i> Cerveau</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/luna/accelerators"><i class="bi bi-lightning-charge"></i> Accélérateurs</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/luna/code"><i class="bi bi-code-slash"></i> Code</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/luna/mcp"><i class="bi bi-cpu"></i> MCP</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/luna/settings"><i class="bi bi-gear"></i> Paramètres</a>
          </li>
        </ul>
      </div>
    </div>
  </nav>

  <div class="luna-container">
    <div class="row align-items-center mb-4">
      <div class="col-md-8">
        <h1 class="mb-0" style="font-weight: 700; letter-spacing: -1px;">Interface Luna</h1>
        <p class="text-muted">Système cognitif avancé avec mémoire thermique</p>
      </div>
      <div class="col-md-4 text-end">
        <div class="d-flex align-items-center justify-content-end">
          <span class="me-3">Statut du système:</span>
          <span id="systemStatus">
            <span class="status-indicator status-active"></span>
            <span>Actif</span>
          </span>
          <button id="toggleSystem" class="btn btn-danger ms-3">
            <i class="bi bi-power"></i> Désactiver
          </button>
        </div>
      </div>
    </div>

    <!-- Contenu principal -->
    <div class="row mt-4">
      <!-- Panneau principal de l'éditeur de code -->
      <div class="col-md-9">
        <div class="luna-card" style="padding: 0; overflow: hidden;">
          <!-- Onglets de fichiers -->
          <div class="code-tabs">
            <div class="code-tab active" data-file="main.js">
              <i class="bi bi-file-earmark-code"></i> main.js
              <span class="close-tab"><i class="bi bi-x"></i></span>
            </div>
            <div class="code-tab" data-file="styles.css">
              <i class="bi bi-file-earmark-code"></i> styles.css
              <span class="close-tab"><i class="bi bi-x"></i></span>
            </div>
            <div class="code-tab" data-file="index.html">
              <i class="bi bi-file-earmark-code"></i> index.html
              <span class="close-tab"><i class="bi bi-x"></i></span>
            </div>
            <div class="code-tab add-tab">
              <i class="bi bi-plus"></i>
            </div>
          </div>

          <!-- Éditeur de code -->
          <div class="code-editor-container">
            <!-- Barre d'outils -->
            <div class="code-editor-toolbar">
              <div class="left-tools">
                <select id="language-selector" class="form-select form-select-sm" style="width: 120px;">
                  <option value="javascript">JavaScript</option>
                  <option value="python">Python</option>
                  <option value="html">HTML</option>
                  <option value="css">CSS</option>
                </select>
                <button id="run-code-btn" class="btn btn-sm btn-success">
                  <i class="bi bi-play-fill"></i> Exécuter
                </button>
                <button id="format-code-btn" class="btn btn-sm btn-secondary">
                  <i class="bi bi-text-indent-left"></i> Formater
                </button>
              </div>
              <div class="right-tools">
                <button id="copy-code-btn" class="btn btn-sm btn-outline-light">
                  <i class="bi bi-clipboard"></i> Copier
                </button>
                <button id="save-code-btn" class="btn btn-sm btn-primary">
                  <i class="bi bi-save"></i> Enregistrer
                </button>
              </div>
            </div>

            <!-- Zone d'édition avec numéros de ligne -->
            <div style="display: flex; flex-grow: 1; overflow: hidden;">
              <div class="line-numbers" id="line-numbers">
                <span></span>
              </div>
              <textarea id="code-editor" class="code-editor" spellcheck="false">// Exemple de code JavaScript
/**
 * Fonction pour calculer la suite de Fibonacci
 * @param {number} n - Nombre de termes à calculer
 * @returns {Array} - Suite de Fibonacci
 */
function fibonacci(n) {
  const sequence = [0, 1];

  if (n <= 2) return sequence.slice(0, n);

  for (let i = 2; i < n; i++) {
    const nextValue = sequence[i-1] + sequence[i-2];
    sequence.push(nextValue);
  }

  return sequence;
}

// Calculer les 10 premiers termes
const result = fibonacci(10);
console.log("Suite de Fibonacci:", result);

// Classe pour gérer les accélérateurs Kyber
class KyberAccelerator {
  constructor(name, type, efficiency = 100) {
    this.name = name;
    this.type = type;
    this.efficiency = efficiency;
    this.active = true;
  }

  activate() {
    this.active = true;
    console.log(`Accélérateur ${this.name} activé`);
    return this;
  }

  deactivate() {
    this.active = false;
    console.log(`Accélérateur ${this.name} désactivé`);
    return this;
  }

  optimize() {
    this.efficiency *= 1.1;
    console.log(`Accélérateur ${this.name} optimisé: ${this.efficiency.toFixed(1)}%`);
    return this;
  }
}

// Créer et utiliser un accélérateur
const memoryAccelerator = new KyberAccelerator("Memory-1", "memory", 120);
memoryAccelerator.optimize().optimize();

// Exemple d'utilisation de l'API Fetch
async function fetchData() {
  try {
    const response = await fetch('https://api.example.com/data');
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Erreur lors de la récupération des données:", error);
    return null;
  }
}</textarea>
            </div>

            <!-- Barre de statut -->
            <div class="code-editor-statusbar">
              <div>
                <span id="cursor-position">Ligne: 1, Colonne: 1</span>
              </div>
              <div>
                <span id="language-indicator">JavaScript</span>
                <span class="ms-3" id="indentation-type">Espaces: 2</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Résultat de l'exécution -->
        <div class="luna-card mt-3">
          <h4><i class="bi bi-terminal me-2"></i> Console</h4>
          <div id="code-output" style="background-color: rgba(0,0,0,0.3); border-radius: 8px; padding: 1rem; font-family: 'Fira Code', monospace; height: 150px; overflow-y: auto;">
            > Suite de Fibonacci: [0, 1, 1, 2, 3, 5, 8, 13, 21, 34]
            > Accélérateur Memory-1 optimisé: 132.0%
            > Accélérateur Memory-1 optimisé: 145.2%
          </div>
        </div>
      </div>

      <!-- Panneau latéral avec les fichiers et options -->
      <div class="col-md-3">
        <!-- Explorateur de fichiers -->
        <div class="luna-card">
          <h4><i class="bi bi-folder me-2"></i> Fichiers</h4>
          <div class="file-explorer">
            <ul class="list-unstyled">
              <li>
                <div class="d-flex align-items-center mb-2">
                  <i class="bi bi-folder-fill me-2"></i>
                  <span>projet</span>
                </div>
                <ul class="list-unstyled ms-3">
                  <li class="d-flex align-items-center mb-2">
                    <i class="bi bi-file-earmark-code me-2"></i>
                    <span class="active-file">main.js</span>
                  </li>
                  <li class="d-flex align-items-center mb-2">
                    <i class="bi bi-file-earmark-code me-2"></i>
                    <span>styles.css</span>
                  </li>
                  <li class="d-flex align-items-center mb-2">
                    <i class="bi bi-file-earmark-code me-2"></i>
                    <span>index.html</span>
                  </li>
                </ul>
              </li>
              <li>
                <div class="d-flex align-items-center mb-2">
                  <i class="bi bi-folder me-2"></i>
                  <span>libs</span>
                </div>
              </li>
              <li>
                <div class="d-flex align-items-center mb-2">
                  <i class="bi bi-folder me-2"></i>
                  <span>assets</span>
                </div>
              </li>
            </ul>
          </div>
          <div class="d-flex justify-content-between mt-2">
            <button class="btn btn-sm btn-luna-outline">
              <i class="bi bi-folder-plus me-1"></i> Nouveau dossier
            </button>
            <button class="btn btn-sm btn-luna">
              <i class="bi bi-file-earmark-plus me-1"></i> Nouveau fichier
            </button>
          </div>
        </div>

        <!-- Snippets de code -->
        <div class="luna-card mt-3">
          <h4><i class="bi bi-code-square me-2"></i> Snippets</h4>
          <div class="list-group">
            <a href="#" class="list-group-item list-group-item-action" data-snippet="function">
              Fonction JavaScript
            </a>
            <a href="#" class="list-group-item list-group-item-action" data-snippet="class">
              Classe JavaScript
            </a>
            <a href="#" class="list-group-item list-group-item-action" data-snippet="fetch">
              Requête Fetch
            </a>
            <a href="#" class="list-group-item list-group-item-action" data-snippet="kyber">
              Accélérateur Kyber
            </a>
          </div>
          <button class="btn btn-sm btn-luna w-100 mt-2">
            <i class="bi bi-plus-circle me-1"></i> Ajouter un snippet
          </button>
        </div>

        <!-- Historique des versions -->
        <div class="luna-card mt-3">
          <h4><i class="bi bi-clock-history me-2"></i> Historique</h4>
          <div id="versions-list" class="versions-list">
            <div class="text-center py-3">
              <div class="spinner-border text-light" role="status">
                <span class="visually-hidden">Chargement...</span>
              </div>
              <p class="mt-2">Chargement de l'historique...</p>
            </div>
          </div>
          <div class="d-flex justify-content-between mt-2">
            <button id="refresh-versions-btn" class="btn btn-sm btn-luna-outline">
              <i class="bi bi-arrow-clockwise me-1"></i> Actualiser
            </button>
            <button id="restore-version-btn" class="btn btn-sm btn-luna" disabled>
              <i class="bi bi-arrow-counterclockwise me-1"></i> Restaurer
            </button>
          </div>
        </div>

        <!-- Paramètres de l'éditeur -->
        <div class="luna-card mt-3">
          <h4><i class="bi bi-gear me-2"></i> Paramètres</h4>
          <div class="mb-3">
            <label for="theme-selector" class="form-label">Thème</label>
            <select id="theme-selector" class="form-select">
              <option value="dark">Sombre</option>
              <option value="light">Clair</option>
              <option value="luna">Luna</option>
            </select>
          </div>
          <div class="mb-3">
            <label for="font-size" class="form-label">Taille de police</label>
            <input type="range" class="form-range" id="font-size" min="10" max="24" value="14">
          </div>
          <div class="form-check mb-3">
            <input class="form-check-input" type="checkbox" id="auto-save" checked>
            <label class="form-check-label" for="auto-save">
              Sauvegarde automatique
            </label>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Bibliothèques JavaScript nécessaires -->
  <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
  <script src="/socket.io/socket.io.js"></script>
  <script src="/js/luna-code.js"></script>
  <script src="/js/luna-code-persistence.js"></script>

  <script>
    $(document).ready(function() {
      // Script global pour Luna
      console.log('Interface Luna Code chargée');

      // Fonctionnalité du bouton d'activation système
      $('#toggleSystem').on('click', function() {
        const isActive = $('#systemStatus .status-indicator').hasClass('status-active');

        if (isActive) {
          // Désactiver le système
          $('#systemStatus .status-indicator').removeClass('status-active').addClass('status-inactive');
          $('#systemStatus span:not(.status-indicator)').text('Inactif');
          $(this).html('<i class="bi bi-power"></i> Activer');
        } else {
          // Activer le système
          $('#systemStatus .status-indicator').removeClass('status-inactive').addClass('status-active');
          $('#systemStatus span:not(.status-indicator)').text('Actif');
          $(this).html('<i class="bi bi-power"></i> Désactiver');
        }
      });
    });
  </script>
</body>
</html>
