<%- include('luna-base') %>
<link rel="stylesheet" href="/css/luna-accelerators.css">

    <!-- Contenu principal -->
    <div class="row mt-4">
      <!-- Panneau principal des accélérateurs -->
      <div class="col-md-8">
        <div class="luna-card" style="height: 65vh; display: flex; flex-direction: column;">
          <h3 class="mb-3 d-flex align-items-center justify-content-between">
            <div><i class="bi bi-lightning-charge me-2"></i> Accélérateurs Kyber</div>
            <div>
              <button id="home-btn" class="btn btn-sm btn-luna-outline me-2" onclick="window.location.href='/luna'">
                <i class="bi bi-house-door me-1"></i> Accueil
              </button>
              <button id="view-list-btn" class="btn btn-sm btn-luna me-2">
                <i class="bi bi-list me-1"></i> Vue Liste
              </button>
              <button id="view-hierarchy-btn" class="btn btn-sm btn-luna-outline me-2">
                <i class="bi bi-diagram-3 me-1"></i> Vue Hiérarchie
              </button>
              <button id="accelerators-settings-btn" class="btn btn-sm btn-luna">
                <i class="bi bi-gear me-1"></i> Paramètres
              </button>
            </div>
          </h3>

          <div id="accelerators-container" style="flex-grow: 1; overflow-y: auto; margin-bottom: 1rem; padding: 1rem; border-radius: 10px; background-color: rgba(0,0,0,0.2); background-image: linear-gradient(to bottom, rgba(156, 137, 184, 0.05), rgba(240, 166, 202, 0.05)); position: relative;">
            <!-- Vue en liste des accélérateurs (visible par défaut) -->
            <div id="accelerators-list-view">
              <!-- Tableau des accélérateurs -->
              <div class="table-responsive">
                <table class="table table-dark table-hover accelerator-table">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>Type</th>
                      <th>Statut</th>
                      <th>Efficacité</th>
                      <th>Débit</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody id="accelerators-list">
                    <!-- Les accélérateurs seront chargés ici dynamiquement -->
                  </tbody>
                </table>
              </div>
            </div>

            <!-- Vue hiérarchique des accélérateurs (cachée par défaut) -->
            <div id="accelerators-hierarchy-view" class="accelerators-hierarchy" style="display: none;">
              <!-- Le contenu sera chargé dynamiquement par le JavaScript -->
            </div>
          </div>
        </div>

        <!-- Panneau de flux de données -->
        <div class="luna-card mt-3">
          <h4><i class="bi bi-activity me-2"></i> Flux de données en temps réel</h4>
          <div class="data-flow-container">
            <canvas id="data-flow-chart" height="150"></canvas>
          </div>
          <div class="d-flex justify-content-between mt-2">
            <div class="small text-muted">Débit: <span id="data-throughput">1.28 GB/s</span></div>
            <div class="small text-muted">Latence: <span id="data-latency">12.4 ms</span></div>
            <div class="small text-muted">Efficacité: <span id="data-efficiency">187.5%</span></div>
          </div>
        </div>

        <!-- Panneau d'activité en temps réel -->
        <div class="luna-card mt-3">
          <h4><i class="bi bi-bar-chart-steps me-2"></i> Activité en temps réel</h4>
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <div class="d-flex justify-content-between align-items-center mb-1">
                  <span>Traitement des données</span>
                  <span id="data-processing-value">78%</span>
                </div>
                <div class="progress" style="height: 10px;">
                  <div id="data-processing-bar" class="progress-bar progress-bar-striped progress-bar-animated bg-info" role="progressbar" style="width: 78%"></div>
                </div>
              </div>
              <div class="mb-3">
                <div class="d-flex justify-content-between align-items-center mb-1">
                  <span>Synchronisation neurale</span>
                  <span id="neural-sync-value">92%</span>
                </div>
                <div class="progress" style="height: 10px;">
                  <div id="neural-sync-bar" class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" style="width: 92%"></div>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <div class="d-flex justify-content-between align-items-center mb-1">
                  <span>Transfert thermique</span>
                  <span id="thermal-transfer-value">65%</span>
                </div>
                <div class="progress" style="height: 10px;">
                  <div id="thermal-transfer-bar" class="progress-bar progress-bar-striped progress-bar-animated bg-warning" role="progressbar" style="width: 65%"></div>
                </div>
              </div>
              <div class="mb-3">
                <div class="d-flex justify-content-between align-items-center mb-1">
                  <span>Optimisation cascade</span>
                  <span id="cascade-optimization-value">87%</span>
                </div>
                <div class="progress" style="height: 10px;">
                  <div id="cascade-optimization-bar" class="progress-bar progress-bar-striped progress-bar-animated bg-primary" role="progressbar" style="width: 87%"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="text-center mt-2">
            <span class="badge bg-info me-2">16 accélérateurs actifs</span>
            <span class="badge bg-success me-2">4 zones synchronisées</span>
            <span class="badge bg-warning me-2">Température: 42.3°C</span>
            <span class="badge bg-primary">Efficacité: 187.5%</span>
          </div>
        </div>
      </div>

      <!-- Panneau latéral avec les contrôles et statistiques -->
      <div class="col-md-4">
        <!-- Contrôles des accélérateurs -->
        <div class="luna-card mb-3">
          <h4><i class="bi bi-sliders me-2"></i> Contrôles</h4>
          <div class="mb-3">
            <button id="optimize-accelerators-btn" class="btn btn-luna w-100 mb-2">
              <i class="bi bi-lightning-charge me-1"></i> Optimiser tous les accélérateurs
            </button>
            <button id="balance-load-btn" class="btn btn-luna-outline w-100 mb-2">
              <i class="bi bi-arrow-repeat me-1"></i> Équilibrer la charge
            </button>
            <button id="reset-accelerators-btn" class="btn btn-danger w-100">
              <i class="bi bi-arrow-counterclockwise me-1"></i> Réinitialiser les accélérateurs
            </button>
          </div>
        </div>

        <!-- Statistiques des accélérateurs -->
        <div class="luna-card">
          <h4><i class="bi bi-bar-chart me-2"></i> Statistiques</h4>
          <div class="mb-3">
            <h6 class="mb-2">Performance par zone</h6>
            <canvas id="zone-performance-chart" height="200"></canvas>
          </div>
        </div>
      </div>
    </div>

<!-- Pied de page fixe -->
<footer class="footer-fixed">
  <small>Vision Ultra - Interface Accélérateurs Kyber - <span id="current-datetime"></span> - &copy; 2024 Jean Passave - Sainte-Anne, Guadeloupe (97180)</small>
</footer>

<script src="/socket.io/socket.io.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="/js/luna-accelerators.js"></script>
<script src="/js/luna-accelerators-hierarchy.js"></script>

<script>
  $(document).ready(function() {
    // Script global pour Luna
    console.log('Interface Luna chargée');

    // Fonctionnalité du bouton d'activation système
    $('#toggleSystem').on('click', function() {
      const isActive = $('#systemStatus .status-indicator').hasClass('status-active');

      if (isActive) {
        // Désactiver le système
        $('#systemStatus .status-indicator').removeClass('status-active').addClass('status-inactive');
        $('#systemStatus span:not(.status-indicator)').text('Inactif');
        $(this).html('<i class="bi bi-power"></i> Activer');
      } else {
        // Activer le système
        $('#systemStatus .status-indicator').removeClass('status-inactive').addClass('status-active');
        $('#systemStatus span:not(.status-indicator)').text('Actif');
        $(this).html('<i class="bi bi-power"></i> Désactiver');
      }
    });

    // Gestionnaire pour le bouton Vue Liste
    $('#view-list-btn').on('click', function() {
      // Activer le bouton Liste et désactiver le bouton Hiérarchie
      $(this).removeClass('btn-luna-outline').addClass('btn-luna');
      $('#view-hierarchy-btn').removeClass('btn-luna').addClass('btn-luna-outline');

      // Afficher la vue Liste et masquer la vue Hiérarchie
      $('#accelerators-list-view').show();
      $('#accelerators-hierarchy-view').hide();

      // Charger les données de la liste
      if (typeof loadAccelerators === 'function') {
        loadAccelerators();
      }
    });

    // Gestionnaire pour le bouton Vue Hiérarchie
    $('#view-hierarchy-btn').on('click', function() {
      // Activer le bouton Hiérarchie et désactiver le bouton Liste
      $(this).removeClass('btn-luna-outline').addClass('btn-luna');
      $('#view-list-btn').removeClass('btn-luna').addClass('btn-luna-outline');

      // Afficher la vue Hiérarchie et masquer la vue Liste
      $('#accelerators-hierarchy-view').show();
      $('#accelerators-list-view').hide();

      // Mettre à jour les connexions visuelles
      if (typeof initializeConnections === 'function') {
        setTimeout(initializeConnections, 100);
      }
    });
  });
</script>
