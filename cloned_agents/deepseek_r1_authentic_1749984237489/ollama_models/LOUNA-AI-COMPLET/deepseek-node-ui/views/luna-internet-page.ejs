<!-- Styles spécifiques à la page Internet -->
<style>
  .browser-container {
    background: rgba(26, 26, 46, 0.8);
    border-radius: 15px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 70vh;
    min-height: 500px;
    box-shadow: 0 10px 25px rgba(0,0,0,0.3);
    border: 1px solid rgba(184, 190, 221, 0.2);
  }

  .browser-toolbar {
    background: rgba(20, 20, 35, 0.9);
    padding: 0.8rem;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(184, 190, 221, 0.2);
  }

  .browser-actions {
    display: flex;
    margin-right: 1rem;
  }

  .browser-button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
    cursor: pointer;
  }

  .browser-button.close {
    background-color: #ff5f56;
  }

  .browser-button.minimize {
    background-color: #ffbd2e;
  }

  .browser-button.maximize {
    background-color: #27c93f;
  }

  .url-bar {
    flex-grow: 1;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 25px;
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    border: 1px solid rgba(184, 190, 221, 0.2);
  }

  .url-bar input {
    background: transparent;
    border: none;
    color: white;
    flex-grow: 1;
    outline: none;
    font-family: 'Quicksand', sans-serif;
  }

  .url-bar .site-info {
    margin-right: 0.5rem;
    color: #4caf50;
  }

  .browser-controls {
    display: flex;
    margin-left: 1rem;
  }

  .browser-control-button {
    background: transparent;
    border: none;
    color: var(--luna-light);
    font-size: 1.2rem;
    margin-left: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .browser-control-button:hover {
    color: var(--luna-secondary);
    transform: translateY(-2px);
  }

  .browser-tabs {
    display: flex;
    background: rgba(20, 20, 35, 0.7);
    padding: 0.5rem 0.5rem 0 0.5rem;
    overflow-x: auto;
    white-space: nowrap;
    scrollbar-width: thin;
  }

  .browser-tabs::-webkit-scrollbar {
    height: 5px;
  }

  .browser-tabs::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
  }

  .browser-tabs::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 5px;
  }

  .browser-tab {
    background: rgba(40, 40, 60, 0.7);
    border-radius: 8px 8px 0 0;
    padding: 0.5rem 1rem;
    margin-right: 0.5rem;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(184, 190, 221, 0.1);
    border-bottom: none;
    min-width: 120px;
    max-width: 200px;
    position: relative;
  }

  .browser-tab.active {
    background: rgba(26, 26, 46, 0.8);
    border-color: rgba(184, 190, 221, 0.2);
  }

  .browser-tab:hover {
    background: rgba(60, 60, 80, 0.7);
  }

  .browser-tab .tab-icon {
    margin-right: 0.5rem;
    font-size: 0.9rem;
  }

  .browser-tab .tab-title {
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 0.9rem;
  }

  .browser-tab .tab-close {
    margin-left: 0.5rem;
    font-size: 0.8rem;
    opacity: 0.5;
    transition: all 0.3s ease;
  }

  .browser-tab .tab-close:hover {
    opacity: 1;
    color: #ff5f56;
  }

  .browser-tab.new-tab {
    min-width: auto;
    padding: 0.5rem;
  }

  .browser-content {
    flex-grow: 1;
    background: white;
    position: relative;
    overflow: hidden;
  }

  .browser-iframe {
    width: 100%;
    height: 100%;
    border: none;
  }

  .browser-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(26, 26, 46, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    z-index: 10;
  }

  .browser-loading .spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.1);
    border-top-color: var(--luna-secondary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  .browser-bookmarks {
    display: flex;
    background: rgba(26, 26, 46, 0.8);
    padding: 0.5rem;
    overflow-x: auto;
    white-space: nowrap;
    border-bottom: 1px solid rgba(184, 190, 221, 0.2);
  }

  .browser-bookmark {
    display: flex;
    align-items: center;
    padding: 0.3rem 0.8rem;
    margin-right: 0.5rem;
    background: rgba(40, 40, 60, 0.7);
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
  }

  .browser-bookmark:hover {
    background: rgba(60, 60, 80, 0.7);
    transform: translateY(-2px);
  }

  .browser-bookmark .bookmark-icon {
    margin-right: 0.5rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .browser-error {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(26, 26, 46, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    z-index: 10;
    padding: 2rem;
    text-align: center;
  }

  .browser-error .error-icon {
    font-size: 3rem;
    color: #ff5f56;
    margin-bottom: 1rem;
  }

  .browser-error .error-message {
    font-size: 1.2rem;
    margin-bottom: 1rem;
  }

  .browser-error .error-details {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 2rem;
  }

  .browser-startpage {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(26, 26, 46, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    z-index: 10;
    padding: 2rem;
  }

  .browser-startpage .search-container {
    width: 80%;
    max-width: 600px;
    margin-bottom: 2rem;
  }

  .browser-startpage .search-box {
    width: 100%;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 25px;
    padding: 0.8rem 1.5rem;
    display: flex;
    align-items: center;
    border: 1px solid rgba(184, 190, 221, 0.2);
    transition: all 0.3s ease;
  }

  .browser-startpage .search-box:focus-within {
    box-shadow: 0 0 15px rgba(240, 166, 202, 0.5);
    border-color: var(--luna-secondary);
  }

  .browser-startpage .search-box input {
    background: transparent;
    border: none;
    color: white;
    flex-grow: 1;
    outline: none;
    font-family: 'Quicksand', sans-serif;
    font-size: 1.1rem;
  }

  .browser-startpage .search-box .search-icon {
    margin-right: 0.8rem;
    color: var(--luna-secondary);
    font-size: 1.2rem;
  }

  .browser-startpage .quick-links {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    max-width: 800px;
  }

  .browser-startpage .quick-link {
    background: rgba(40, 40, 60, 0.7);
    border-radius: 10px;
    padding: 1rem;
    margin: 0.5rem;
    width: 100px;
    height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
  }

  .browser-startpage .quick-link:hover {
    background: rgba(60, 60, 80, 0.7);
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  }

  .browser-startpage .quick-link-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: var(--luna-secondary);
  }

  .browser-startpage .quick-link-title {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.9);
  }
</style>

<!-- Contenu principal -->
<div class="row">
  <div class="col-md-12 mb-4">
    <div class="d-flex justify-content-between align-items-center">
      <h2><i class="bi bi-globe me-2"></i> Navigateur Internet Luna</h2>
      <div>
        <button id="toggle-fullscreen" class="btn btn-luna">
          <i class="bi bi-arrows-fullscreen me-2"></i> Plein écran
        </button>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-12">
    <div class="browser-container">
      <!-- Onglets du navigateur -->
      <div class="browser-tabs">
        <div class="browser-tab active" data-tab-id="tab1">
          <span class="tab-icon"><i class="bi bi-house-fill"></i></span>
          <span class="tab-title">Accueil Luna</span>
          <span class="tab-close"><i class="bi bi-x"></i></span>
        </div>
        <div class="browser-tab" data-tab-id="tab2">
          <span class="tab-icon"><i class="bi bi-google"></i></span>
          <span class="tab-title">Google</span>
          <span class="tab-close"><i class="bi bi-x"></i></span>
        </div>
        <div class="browser-tab new-tab">
          <i class="bi bi-plus"></i>
        </div>
      </div>

      <!-- Barre d'outils du navigateur -->
      <div class="browser-toolbar">
        <div class="browser-actions">
          <div class="browser-button close"></div>
          <div class="browser-button minimize"></div>
          <div class="browser-button maximize"></div>
        </div>
        <div class="url-bar">
          <span class="site-info"><i class="bi bi-shield-check"></i></span>
          <input type="text" id="url-input" placeholder="Entrez une URL ou recherchez sur le web" value="luna://home">
        </div>
        <div class="browser-controls">
          <button class="browser-control-button" id="refresh-button"><i class="bi bi-arrow-clockwise"></i></button>
          <button class="browser-control-button" id="home-button"><i class="bi bi-house"></i></button>
          <button class="browser-control-button" id="bookmark-button"><i class="bi bi-bookmark-plus"></i></button>
        </div>
      </div>

      <!-- Favoris -->
      <div class="browser-bookmarks">
        <div class="browser-bookmark" data-url="https://www.google.com">
          <span class="bookmark-icon"><i class="bi bi-google"></i></span>
          <span>Google</span>
        </div>
        <div class="browser-bookmark" data-url="https://www.youtube.com">
          <span class="bookmark-icon"><i class="bi bi-youtube"></i></span>
          <span>YouTube</span>
        </div>
        <div class="browser-bookmark" data-url="https://www.github.com">
          <span class="bookmark-icon"><i class="bi bi-github"></i></span>
          <span>GitHub</span>
        </div>
        <div class="browser-bookmark" data-url="https://www.wikipedia.org">
          <span class="bookmark-icon"><i class="bi bi-wikipedia"></i></span>
          <span>Wikipedia</span>
        </div>
        <div class="browser-bookmark" data-url="https://www.openai.com">
          <span class="bookmark-icon"><i class="bi bi-robot"></i></span>
          <span>OpenAI</span>
        </div>
      </div>

      <!-- Contenu du navigateur -->
      <div class="browser-content">
        <!-- Page de démarrage -->
        <div class="browser-startpage" id="startpage">
          <div class="search-container">
            <div class="search-box">
              <span class="search-icon"><i class="bi bi-search"></i></span>
              <input type="text" id="search-input" placeholder="Rechercher sur le web ou entrer une URL">
            </div>
          </div>

          <div class="quick-links">
            <div class="quick-link" data-url="https://www.google.com">
              <div class="quick-link-icon"><i class="bi bi-google"></i></div>
              <div class="quick-link-title">Google</div>
            </div>
            <div class="quick-link" data-url="https://www.youtube.com">
              <div class="quick-link-icon"><i class="bi bi-youtube"></i></div>
              <div class="quick-link-title">YouTube</div>
            </div>
            <div class="quick-link" data-url="https://www.github.com">
              <div class="quick-link-icon"><i class="bi bi-github"></i></div>
              <div class="quick-link-title">GitHub</div>
            </div>
            <div class="quick-link" data-url="https://www.wikipedia.org">
              <div class="quick-link-icon"><i class="bi bi-wikipedia"></i></div>
              <div class="quick-link-title">Wikipedia</div>
            </div>
            <div class="quick-link" data-url="https://www.openai.com">
              <div class="quick-link-icon"><i class="bi bi-robot"></i></div>
              <div class="quick-link-title">OpenAI</div>
            </div>
            <div class="quick-link" data-url="https://www.anthropic.com">
              <div class="quick-link-icon"><i class="bi bi-cpu"></i></div>
              <div class="quick-link-title">Anthropic</div>
            </div>
            <div class="quick-link" data-url="https://www.deepseek.ai">
              <div class="quick-link-icon"><i class="bi bi-search"></i></div>
              <div class="quick-link-title">DeepSeek</div>
            </div>
            <div class="quick-link" data-url="https://www.mistral.ai">
              <div class="quick-link-icon"><i class="bi bi-wind"></i></div>
              <div class="quick-link-title">Mistral AI</div>
            </div>
          </div>
        </div>

        <!-- Iframe pour le contenu web -->
        <iframe id="browser-iframe" class="browser-iframe" style="display: none;"></iframe>

        <!-- Écran de chargement -->
        <div class="browser-loading" id="loading-screen" style="display: none;">
          <div class="spinner"></div>
          <div>Chargement en cours...</div>
        </div>

        <!-- Écran d'erreur -->
        <div class="browser-error" id="error-screen" style="display: none;">
          <div class="error-icon"><i class="bi bi-exclamation-triangle"></i></div>
          <div class="error-message">Impossible de charger la page</div>
          <div class="error-details">Vérifiez l'URL ou votre connexion Internet et réessayez.</div>
          <button class="btn btn-luna" id="retry-button">
            <i class="bi bi-arrow-clockwise me-2"></i> Réessayer
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Scripts spécifiques à la page Internet -->
<script>
  $(document).ready(function() {
    // Variables globales
    let currentTabId = 'tab1';
    let tabs = {
      'tab1': { url: 'luna://home', title: 'Accueil Luna', icon: 'bi-house-fill' },
      'tab2': { url: 'https://www.google.com', title: 'Google', icon: 'bi-google' }
    };
    let tabCounter = 3; // Pour générer de nouveaux IDs d'onglets

    // Fonction pour charger une URL dans l'iframe
    function loadUrl(url) {
      // Vérifier si c'est une URL interne Luna
      if (url.startsWith('luna://')) {
        // Afficher la page de démarrage pour luna://home
        if (url === 'luna://home') {
          $('#browser-iframe').hide();
          $('#startpage').show();
          $('#loading-screen').hide();
          $('#error-screen').hide();

          // Mettre à jour l'onglet actif
          tabs[currentTabId].url = url;
          tabs[currentTabId].title = 'Accueil Luna';
          tabs[currentTabId].icon = 'bi-house-fill';
          updateTabUI(currentTabId);

          // Mettre à jour la barre d'URL
          $('#url-input').val(url);
          return;
        }

        // Autres URLs internes Luna pourraient être gérées ici
        return;
      }

      // Ajouter http:// si nécessaire
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = 'https://' + url;
      }

      // Mettre à jour l'onglet actif
      tabs[currentTabId].url = url;

      // Extraire le nom de domaine pour le titre
      try {
        const domain = new URL(url).hostname.replace('www.', '');
        tabs[currentTabId].title = domain.charAt(0).toUpperCase() + domain.slice(1);

        // Définir l'icône en fonction du domaine
        if (domain.includes('google')) {
          tabs[currentTabId].icon = 'bi-google';
        } else if (domain.includes('youtube')) {
          tabs[currentTabId].icon = 'bi-youtube';
        } else if (domain.includes('github')) {
          tabs[currentTabId].icon = 'bi-github';
        } else if (domain.includes('wikipedia')) {
          tabs[currentTabId].icon = 'bi-wikipedia';
        } else {
          tabs[currentTabId].icon = 'bi-globe';
        }
      } catch (e) {
        tabs[currentTabId].title = 'Nouvelle page';
        tabs[currentTabId].icon = 'bi-globe';
      }

      // Mettre à jour l'interface de l'onglet
      updateTabUI(currentTabId);

      // Afficher l'écran de chargement
      $('#startpage').hide();
      $('#browser-iframe').hide();
      $('#loading-screen').show();
      $('#error-screen').hide();

      // Mettre à jour la barre d'URL
      $('#url-input').val(url);

      // Simuler le chargement (dans un environnement réel, nous chargerions l'iframe)
      setTimeout(function() {
        // Simuler une erreur aléatoire (10% de chance)
        if (Math.random() < 0.1) {
          $('#loading-screen').hide();
          $('#error-screen').show();
          return;
        }

        // Afficher l'iframe
        $('#browser-iframe').attr('src', url);
        $('#browser-iframe').show();
        $('#loading-screen').hide();
      }, 1500);
    }

    // Fonction pour mettre à jour l'interface d'un onglet
    function updateTabUI(tabId) {
      const tab = tabs[tabId];
      const $tab = $(`.browser-tab[data-tab-id="${tabId}"]`);

      $tab.find('.tab-icon').html(`<i class="bi ${tab.icon}"></i>`);
      $tab.find('.tab-title').text(tab.title);
    }

    // Fonction pour changer d'onglet
    function switchTab(tabId) {
      // Désactiver tous les onglets
      $('.browser-tab').removeClass('active');

      // Activer l'onglet sélectionné
      $(`.browser-tab[data-tab-id="${tabId}"]`).addClass('active');

      // Mettre à jour l'onglet actif
      currentTabId = tabId;

      // Charger l'URL de l'onglet
      loadUrl(tabs[tabId].url);
    }

    // Fonction pour créer un nouvel onglet
    function createNewTab(url = 'luna://home', title = 'Nouvelle page', icon = 'bi-globe') {
      const tabId = 'tab' + tabCounter++;

      // Créer le nouvel onglet dans l'objet tabs
      tabs[tabId] = { url, title, icon };

      // Créer l'élément HTML de l'onglet
      const $newTab = $(`
        <div class="browser-tab" data-tab-id="${tabId}">
          <span class="tab-icon"><i class="bi ${icon}"></i></span>
          <span class="tab-title">${title}</span>
          <span class="tab-close"><i class="bi bi-x"></i></span>
        </div>
      `);

      // Insérer le nouvel onglet avant le bouton "+"
      $('.browser-tab.new-tab').before($newTab);

      // Activer le nouvel onglet
      switchTab(tabId);

      return tabId;
    }

    // Fonction pour fermer un onglet
    function closeTab(tabId) {
      // Supprimer l'onglet de l'interface
      $(`.browser-tab[data-tab-id="${tabId}"]`).remove();

      // Supprimer l'onglet de l'objet tabs
      delete tabs[tabId];

      // Si l'onglet fermé était l'onglet actif, activer le premier onglet disponible
      if (tabId === currentTabId) {
        const firstTabId = Object.keys(tabs)[0];
        if (firstTabId) {
          switchTab(firstTabId);
        } else {
          // S'il n'y a plus d'onglets, en créer un nouveau
          createNewTab();
        }
      }
    }

    // Gestionnaire d'événements pour la barre d'URL
    $('#url-input').on('keypress', function(e) {
      if (e.which === 13) { // Touche Entrée
        e.preventDefault();
        const url = $(this).val().trim();
        loadUrl(url);
      }
    });

    // Gestionnaire d'événements pour la recherche sur la page de démarrage
    $('#search-input').on('keypress', function(e) {
      if (e.which === 13) { // Touche Entrée
        e.preventDefault();
        const query = $(this).val().trim();

        // Vérifier si c'est une URL ou une recherche
        if (query.includes('.') && !query.includes(' ')) {
          loadUrl(query);
        } else {
          loadUrl(`https://www.google.com/search?q=${encodeURIComponent(query)}`);
        }
      }
    });

    // Gestionnaire d'événements pour les liens rapides
    $('.quick-link').on('click', function() {
      const url = $(this).data('url');
      loadUrl(url);
    });

    // Gestionnaire d'événements pour les favoris
    $('.browser-bookmark').on('click', function() {
      const url = $(this).data('url');
      loadUrl(url);
    });

    // Gestionnaire d'événements pour le bouton d'actualisation
    $('#refresh-button').on('click', function() {
      loadUrl(tabs[currentTabId].url);
    });

    // Gestionnaire d'événements pour le bouton d'accueil
    $('#home-button').on('click', function() {
      loadUrl('luna://home');
    });

    // Gestionnaire d'événements pour le bouton de favoris
    $('#bookmark-button').on('click', function() {
      // Simuler l'ajout aux favoris
      const url = tabs[currentTabId].url;
      const title = tabs[currentTabId].title;
      const icon = tabs[currentTabId].icon;

      // Vérifier si le favori existe déjà
      const exists = $('.browser-bookmark').toArray().some(bookmark =>
        $(bookmark).data('url') === url
      );

      if (!exists && url !== 'luna://home') {
        // Créer un nouveau favori
        const $newBookmark = $(`
          <div class="browser-bookmark" data-url="${url}">
            <span class="bookmark-icon"><i class="bi ${icon}"></i></span>
            <span>${title}</span>
          </div>
        `);

        // Ajouter le favori à la barre de favoris
        $('.browser-bookmarks').append($newBookmark);

        // Ajouter le gestionnaire d'événements au nouveau favori
        $newBookmark.on('click', function() {
          loadUrl($(this).data('url'));
        });

        // Animation pour indiquer que le favori a été ajouté
        $newBookmark.css('transform', 'scale(1.2)');
        setTimeout(() => $newBookmark.css('transform', ''), 300);
      }
    });

    // Gestionnaire d'événements pour le bouton de nouvel onglet
    $('.browser-tab.new-tab').on('click', function() {
      createNewTab();
    });

    // Gestionnaire d'événements pour le clic sur un onglet
    $(document).on('click', '.browser-tab:not(.new-tab)', function(e) {
      // Ignorer si le clic est sur le bouton de fermeture
      if (!$(e.target).closest('.tab-close').length) {
        const tabId = $(this).data('tab-id');
        switchTab(tabId);
      }
    });

    // Gestionnaire d'événements pour le bouton de fermeture d'onglet
    $(document).on('click', '.tab-close', function(e) {
      e.stopPropagation();
      const tabId = $(this).closest('.browser-tab').data('tab-id');
      closeTab(tabId);
    });

    // Gestionnaire d'événements pour le bouton de réessai
    $('#retry-button').on('click', function() {
      loadUrl(tabs[currentTabId].url);
    });

    // Gestionnaire d'événements pour le bouton plein écran
    $('#toggle-fullscreen').on('click', function() {
      $('.browser-container').toggleClass('fullscreen');

      if ($('.browser-container').hasClass('fullscreen')) {
        $(this).html('<i class="bi bi-fullscreen-exit me-2"></i> Quitter le plein écran');

        // Ajouter des styles pour le mode plein écran
        $('<style id="fullscreen-style">')
          .text(`
            .browser-container.fullscreen {
              position: fixed;
              top: 0;
              left: 0;
              width: 100vw;
              height: 100vh;
              z-index: 9999;
              border-radius: 0;
            }
            .luna-container {
              z-index: 1;
            }
          `)
          .appendTo('head');
      } else {
        $(this).html('<i class="bi bi-arrows-fullscreen me-2"></i> Plein écran');
        $('#fullscreen-style').remove();
      }
    });

    // Initialiser le premier onglet
    switchTab('tab1');
  });
</script>