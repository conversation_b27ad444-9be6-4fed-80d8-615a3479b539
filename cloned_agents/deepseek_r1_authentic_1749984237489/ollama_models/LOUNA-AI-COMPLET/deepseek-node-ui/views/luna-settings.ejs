<%- include('luna-base') %>
<link rel="stylesheet" href="/css/luna-settings.css">

<!-- Intégration du contenu principal Luna Paramètres -->
<div class="row mt-4">
  <!-- Panneau principal des paramètres -->
  <div class="col-md-8">
    <div class="luna-card" style="height: 65vh; display: flex; flex-direction: column;">
      <h3 class="mb-3 d-flex align-items-center justify-content-between">
        <div><i class="bi bi-gear me-2"></i> Paramètres</div>
        <div>
          <button id="save-settings-btn" class="btn btn-sm btn-luna">
            <i class="bi bi-save me-1"></i> Enregistrer
          </button>
        </div>
      </h3>

      <div id="settings-container" style="flex-grow: 1; overflow-y: auto; margin-bottom: 1rem; padding: 1rem; border-radius: 10px; background-color: rgba(0,0,0,0.2); background-image: linear-gradient(to bottom, rgba(156, 137, 184, 0.05), rgba(240, 166, 202, 0.05));">
        <!-- Onglets de paramètres -->
        <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
          <li class="nav-item" role="presentation">
            <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab" aria-controls="general" aria-selected="true">
              <i class="bi bi-sliders me-1"></i> Général
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="memory-tab" data-bs-toggle="tab" data-bs-target="#memory" type="button" role="tab" aria-controls="memory" aria-selected="false">
              <i class="bi bi-hdd me-1"></i> Mémoire
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="accelerators-tab" data-bs-toggle="tab" data-bs-target="#accelerators" type="button" role="tab" aria-controls="accelerators" aria-selected="false">
              <i class="bi bi-lightning-charge me-1"></i> Accélérateurs
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="interface-tab" data-bs-toggle="tab" data-bs-target="#interface" type="button" role="tab" aria-controls="interface" aria-selected="false">
              <i class="bi bi-window me-1"></i> Interface
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab" aria-controls="security" aria-selected="false">
              <i class="bi bi-shield-lock me-1"></i> Sécurité
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="advanced-tab" data-bs-toggle="tab" data-bs-target="#advanced" type="button" role="tab" aria-controls="advanced" aria-selected="false">
              <i class="bi bi-code-slash me-1"></i> Avancé
            </button>
          </li>
        </ul>

        <!-- Contenu des onglets -->
        <div class="tab-content p-3" id="settingsTabContent">
          <!-- Paramètres généraux -->
          <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
            <h4 class="mb-4">Paramètres généraux</h4>

            <div class="mb-4">
              <h5 class="mb-3">Modèle de langage</h5>
              <div class="form-group mb-3">
                <label for="model-select" class="form-label">Modèle principal</label>
                <select class="form-select" id="model-select">
                  <option value="claude-3-opus" selected>Claude 3 Opus</option>
                  <option value="claude-3-sonnet">Claude 3 Sonnet</option>
                  <option value="claude-3-haiku">Claude 3 Haiku</option>
                  <option value="gpt-4o">GPT-4o</option>
                  <option value="gpt-4-turbo">GPT-4 Turbo</option>
                  <option value="llama-3-70b">Llama 3 70B</option>
                </select>
              </div>

              <div class="form-group mb-3">
                <label for="temperature-range" class="form-label d-flex justify-content-between">
                  <span>Température</span>
                  <span id="temperature-value">0.7</span>
                </label>
                <input type="range" class="form-range" id="temperature-range" min="0" max="1" step="0.1" value="0.7">
                <div class="form-text">Contrôle la créativité des réponses. Valeurs plus élevées = plus de créativité.</div>
              </div>

              <div class="form-group mb-3">
                <label for="max-tokens-input" class="form-label">Longueur maximale de réponse</label>
                <input type="number" class="form-control" id="max-tokens-input" value="4096" min="256" max="32768">
                <div class="form-text">Nombre maximum de tokens dans la réponse.</div>
              </div>
            </div>

            <div class="mb-4">
              <h5 class="mb-3">Périphériques</h5>
              <div class="form-check form-switch mb-3">
                <input class="form-check-input" type="checkbox" id="camera-toggle" checked>
                <label class="form-check-label" for="camera-toggle">Activer la caméra</label>
              </div>

              <div class="form-check form-switch mb-3">
                <input class="form-check-input" type="checkbox" id="microphone-toggle" checked>
                <label class="form-check-label" for="microphone-toggle">Activer le microphone</label>
              </div>

              <div class="form-check form-switch mb-3">
                <input class="form-check-input" type="checkbox" id="speaker-toggle" checked>
                <label class="form-check-label" for="speaker-toggle">Activer la synthèse vocale</label>
              </div>
            </div>
          </div>

          <!-- Paramètres de mémoire -->
          <div class="tab-pane fade" id="memory" role="tabpanel" aria-labelledby="memory-tab">
            <h4 class="mb-4">Paramètres de mémoire</h4>

            <div class="mb-4">
              <h5 class="mb-3">Mémoire thermique</h5>
              <div class="form-group mb-3">
                <label for="memory-capacity-input" class="form-label">Capacité maximale</label>
                <input type="number" class="form-control" id="memory-capacity-input" value="1000" min="100" max="10000">
                <div class="form-text">Nombre maximum de conversations stockées dans la mémoire thermique.</div>
              </div>

              <div class="form-group mb-3">
                <label for="memory-retention-range" class="form-label d-flex justify-content-between">
                  <span>Rétention mémoire</span>
                  <span id="memory-retention-value">0.85</span>
                </label>
                <input type="range" class="form-range" id="memory-retention-range" min="0.5" max="1" step="0.05" value="0.85">
                <div class="form-text">Contrôle la durée de rétention des souvenirs. Valeurs plus élevées = rétention plus longue.</div>
              </div>

              <div class="form-group mb-3">
                <label for="thermal-transfer-range" class="form-label d-flex justify-content-between">
                  <span>Taux de transfert thermique</span>
                  <span id="thermal-transfer-value">0.3</span>
                </label>
                <input type="range" class="form-range" id="thermal-transfer-range" min="0.1" max="0.5" step="0.05" value="0.3">
                <div class="form-text">Contrôle la vitesse de transfert entre les zones. Valeurs plus élevées = transfert plus rapide.</div>
              </div>
            </div>

            <div class="mb-4">
              <h5 class="mb-3">Contexte</h5>
              <div class="form-group mb-3">
                <label for="context-window-input" class="form-label">Fenêtre de contexte</label>
                <input type="number" class="form-control" id="context-window-input" value="10" min="1" max="50">
                <div class="form-text">Nombre de conversations précédentes à inclure dans le contexte.</div>
              </div>

              <div class="form-check form-switch mb-3">
                <input class="form-check-input" type="checkbox" id="semantic-search-toggle" checked>
                <label class="form-check-label" for="semantic-search-toggle">Recherche sémantique</label>
                <div class="form-text">Utilise la recherche sémantique pour trouver des souvenirs pertinents.</div>
              </div>
            </div>
          </div>

          <!-- Paramètres des accélérateurs -->
          <div class="tab-pane fade" id="accelerators" role="tabpanel" aria-labelledby="accelerators-tab">
            <h4 class="mb-4">Paramètres des accélérateurs</h4>

            <div class="mb-4">
              <h5 class="mb-3">Configuration des accélérateurs Kyber</h5>
              <div class="form-group mb-3">
                <label for="accelerator-count-input" class="form-label">Nombre d'accélérateurs</label>
                <input type="number" class="form-control" id="accelerator-count-input" value="16" min="4" max="32" step="4">
                <div class="form-text">Nombre total d'accélérateurs Kyber à utiliser.</div>
              </div>

              <div class="form-group mb-3">
                <label for="cascade-depth-input" class="form-label">Profondeur de cascade</label>
                <input type="number" class="form-control" id="cascade-depth-input" value="3" min="1" max="5">
                <div class="form-text">Nombre de niveaux dans la cascade d'accélérateurs.</div>
              </div>

              <div class="form-group mb-3">
                <label for="master-power-range" class="form-label d-flex justify-content-between">
                  <span>Puissance principale</span>
                  <span id="master-power-value">87%</span>
                </label>
                <input type="range" class="form-range" id="master-power-range" min="50" max="100" value="87">
                <div class="form-text">Puissance allouée aux accélérateurs. Valeurs plus élevées = plus de performance mais plus de chaleur.</div>
              </div>
            </div>

            <div class="mb-4">
              <h5 class="mb-3">Allocation par zone</h5>
              <div class="form-group mb-3">
                <label for="reflection-allocation-range" class="form-label d-flex justify-content-between">
                  <span>Zone Réflexion</span>
                  <span id="reflection-allocation-value">25%</span>
                </label>
                <input type="range" class="form-range" id="reflection-allocation-range" min="10" max="40" value="25">
              </div>

              <div class="form-group mb-3">
                <label for="memory-allocation-range" class="form-label d-flex justify-content-between">
                  <span>Zone Mémoire</span>
                  <span id="memory-allocation-value">25%</span>
                </label>
                <input type="range" class="form-range" id="memory-allocation-range" min="10" max="40" value="25">
              </div>

              <div class="form-group mb-3">
                <label for="processing-allocation-range" class="form-label d-flex justify-content-between">
                  <span>Zone Traitement</span>
                  <span id="processing-allocation-value">25%</span>
                </label>
                <input type="range" class="form-range" id="processing-allocation-range" min="10" max="40" value="25">
              </div>

              <div class="form-group mb-3">
                <label for="creativity-allocation-range" class="form-label d-flex justify-content-between">
                  <span>Zone Créativité</span>
                  <span id="creativity-allocation-value">25%</span>
                </label>
                <input type="range" class="form-range" id="creativity-allocation-range" min="10" max="40" value="25">
                <div class="form-text">Total: <span id="total-allocation">100</span>% (doit être égal à 100%)</div>
              </div>
            </div>
          </div>

          <!-- Paramètres d'interface -->
          <div class="tab-pane fade" id="interface" role="tabpanel" aria-labelledby="interface-tab">
            <h4 class="mb-4">Paramètres d'interface</h4>

            <div class="mb-4">
              <h5 class="mb-3">Apparence</h5>
              <div class="form-group mb-3">
                <label for="theme-select" class="form-label">Thème</label>
                <select class="form-select" id="theme-select">
                  <option value="default" selected>Luna (Défaut)</option>
                  <option value="dark">Nuit profonde</option>
                  <option value="light">Aurore</option>
                  <option value="contrast">Contraste élevé</option>
                </select>
              </div>

              <div class="form-group mb-3">
                <label for="font-size-select" class="form-label">Taille de police</label>
                <select class="form-select" id="font-size-select">
                  <option value="small">Petite</option>
                  <option value="medium" selected>Moyenne</option>
                  <option value="large">Grande</option>
                </select>
              </div>

              <div class="form-check form-switch mb-3">
                <input class="form-check-input" type="checkbox" id="animations-toggle" checked>
                <label class="form-check-label" for="animations-toggle">Animations</label>
                <div class="form-text">Active ou désactive les animations de l'interface.</div>
              </div>
            </div>

            <div class="mb-4">
              <h5 class="mb-3">Conversation</h5>
              <div class="form-check form-switch mb-3">
                <input class="form-check-input" type="checkbox" id="bubble-style-toggle" checked>
                <label class="form-check-label" for="bubble-style-toggle">Style bulle</label>
                <div class="form-text">Affiche les messages dans des bulles de conversation.</div>
              </div>

              <div class="form-check form-switch mb-3">
                <input class="form-check-input" type="checkbox" id="timestamps-toggle" checked>
                <label class="form-check-label" for="timestamps-toggle">Horodatage</label>
                <div class="form-text">Affiche l'heure d'envoi des messages.</div>
              </div>

              <div class="form-check form-switch mb-3">
                <input class="form-check-input" type="checkbox" id="system-messages-toggle">
                <label class="form-check-label" for="system-messages-toggle">Messages système</label>
                <div class="form-text">Affiche les messages système dans la conversation.</div>
              </div>
            </div>
          </div>

          <!-- Paramètres de sécurité -->
          <div class="tab-pane fade" id="security" role="tabpanel" aria-labelledby="security-tab">
            <h4 class="mb-4">Paramètres de sécurité</h4>

            <div class="mb-4">
              <h5 class="mb-3">VPN</h5>
              <div class="form-check form-switch mb-3">
                <input class="form-check-input" type="checkbox" id="vpn-toggle">
                <label class="form-check-label" for="vpn-toggle">Activer le VPN</label>
                <div class="form-text">Protège votre connexion Internet en chiffrant votre trafic.</div>
              </div>

              <div id="vpn-settings" style="display: none;">
                <div class="form-group mb-3">
                  <label for="vpn-server-select" class="form-label">Serveur VPN</label>
                  <select class="form-select" id="vpn-server-select">
                    <option value="fr" selected>France (15ms)</option>
                    <option value="us">États-Unis (120ms)</option>
                    <option value="jp">Japon (280ms)</option>
                    <option value="uk">Royaume-Uni (45ms)</option>
                    <option value="de">Allemagne (30ms)</option>
                    <option value="sg">Singapour (250ms)</option>
                    <option value="ca">Canada (110ms)</option>
                  </select>
                </div>

                <div class="form-check form-switch mb-3">
                  <input class="form-check-input" type="checkbox" id="vpn-autoconnect-toggle">
                  <label class="form-check-label" for="vpn-autoconnect-toggle">Connexion automatique</label>
                  <div class="form-text">Connecte automatiquement le VPN au démarrage.</div>
                </div>

                <div class="form-check form-switch mb-3">
                  <input class="form-check-input" type="checkbox" id="vpn-killswitch-toggle">
                  <label class="form-check-label" for="vpn-killswitch-toggle">Kill Switch</label>
                  <div class="form-text">Coupe Internet si le VPN se déconnecte pour éviter les fuites.</div>
                </div>

                <div class="form-check form-switch mb-3">
                  <input class="form-check-input" type="checkbox" id="vpn-adblock-toggle" checked>
                  <label class="form-check-label" for="vpn-adblock-toggle">Bloquer les publicités</label>
                  <div class="form-text">Bloque les publicités et les traqueurs pendant la navigation.</div>
                </div>

                <div class="form-check form-switch mb-3">
                  <input class="form-check-input" type="checkbox" id="vpn-antitracking-toggle" checked>
                  <label class="form-check-label" for="vpn-antitracking-toggle">Anti-tracking</label>
                  <div class="form-text">Empêche les sites web de suivre votre activité en ligne.</div>
                </div>

                <div class="vpn-status-card mb-3 p-3" style="background: rgba(0,0,0,0.2); border-radius: 10px;">
                  <div class="d-flex justify-content-between align-items-center mb-2">
                    <span><i class="bi bi-shield-lock me-2"></i> Statut VPN</span>
                    <span id="vpn-status-badge" class="badge bg-danger">Déconnecté</span>
                  </div>
                  <div class="d-flex justify-content-between mb-2">
                    <span>Adresse IP</span>
                    <span id="vpn-ip">Non masquée</span>
                  </div>
                  <div class="d-flex justify-content-between mb-2">
                    <span>Localisation</span>
                    <span id="vpn-location">Guadeloupe</span>
                  </div>
                  <div class="d-flex justify-content-between mb-2">
                    <span>Temps de connexion</span>
                    <span id="vpn-uptime">00:00:00</span>
                  </div>
                  <div class="d-flex justify-content-between">
                    <span>Niveau de protection</span>
                    <span id="vpn-protection">0%</span>
                  </div>
                </div>

                <div class="d-grid">
                  <button id="vpn-connect-btn" class="btn btn-luna">
                    <i class="bi bi-shield-check me-2"></i> Connecter le VPN
                  </button>
                </div>
              </div>
            </div>

            <div class="mb-4">
              <h5 class="mb-3">Pare-feu</h5>
              <div class="form-check form-switch mb-3">
                <input class="form-check-input" type="checkbox" id="firewall-toggle" checked>
                <label class="form-check-label" for="firewall-toggle">Activer le pare-feu</label>
                <div class="form-text">Protège contre les accès non autorisés.</div>
              </div>

              <div class="form-group mb-3">
                <label for="firewall-level-select" class="form-label">Niveau de protection</label>
                <select class="form-select" id="firewall-level-select">
                  <option value="low">Faible</option>
                  <option value="medium" selected>Moyen</option>
                  <option value="high">Élevé</option>
                </select>
              </div>
            </div>

            <div class="mb-4">
              <h5 class="mb-3">Antivirus</h5>
              <div class="form-check form-switch mb-3">
                <input class="form-check-input" type="checkbox" id="antivirus-toggle" checked>
                <label class="form-check-label" for="antivirus-toggle">Activer l'antivirus</label>
                <div class="form-text">Protège contre les logiciels malveillants.</div>
              </div>

              <div class="form-group mb-3">
                <label for="scan-frequency-select" class="form-label">Fréquence d'analyse</label>
                <select class="form-select" id="scan-frequency-select">
                  <option value="daily">Quotidienne</option>
                  <option value="weekly" selected>Hebdomadaire</option>
                  <option value="monthly">Mensuelle</option>
                </select>
              </div>

              <div class="d-grid">
                <button id="scan-now-btn" class="btn btn-luna-outline">
                  <i class="bi bi-search me-2"></i> Analyser maintenant
                </button>
              </div>
            </div>
          </div>

          <!-- Paramètres avancés -->
          <div class="tab-pane fade" id="advanced" role="tabpanel" aria-labelledby="advanced-tab">
            <h4 class="mb-4">Paramètres avancés</h4>

            <div class="alert alert-warning mb-4">
              <i class="bi bi-exclamation-triangle me-2"></i> Attention : La modification de ces paramètres peut affecter les performances du système.
            </div>

            <div class="mb-4">
              <h5 class="mb-3">Système</h5>
              <div class="form-group mb-3">
                <label for="log-level-select" class="form-label">Niveau de journalisation</label>
                <select class="form-select" id="log-level-select">
                  <option value="error">Erreur</option>
                  <option value="warn">Avertissement</option>
                  <option value="info" selected>Information</option>
                  <option value="debug">Débogage</option>
                  <option value="trace">Trace</option>
                </select>
              </div>

              <div class="form-check form-switch mb-3">
                <input class="form-check-input" type="checkbox" id="debug-mode-toggle">
                <label class="form-check-label" for="debug-mode-toggle">Mode débogage</label>
                <div class="form-text">Active le mode débogage avec des informations supplémentaires.</div>
              </div>

              <div class="form-check form-switch mb-3">
                <input class="form-check-input" type="checkbox" id="experimental-features-toggle">
                <label class="form-check-label" for="experimental-features-toggle">Fonctionnalités expérimentales</label>
                <div class="form-text">Active les fonctionnalités expérimentales non testées.</div>
              </div>
            </div>

            <div class="mb-4">
              <h5 class="mb-3">Réseau</h5>
              <div class="form-group mb-3">
                <label for="api-url-input" class="form-label">URL de l'API</label>
                <input type="text" class="form-control" id="api-url-input" value="https://api.anthropic.com/v1">
              </div>

              <div class="form-group mb-3">
                <label for="timeout-input" class="form-label">Délai d'attente (ms)</label>
                <input type="number" class="form-control" id="timeout-input" value="30000" min="5000" max="120000" step="1000">
              </div>

              <div class="form-check form-switch mb-3">
                <input class="form-check-input" type="checkbox" id="proxy-toggle">
                <label class="form-check-label" for="proxy-toggle">Utiliser un proxy</label>
              </div>

              <div class="form-group mb-3" id="proxy-settings" style="display: none;">
                <label for="proxy-url-input" class="form-label">URL du proxy</label>
                <input type="text" class="form-control" id="proxy-url-input" placeholder="http://proxy.example.com:8080">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Panneau latéral avec les actions et informations -->
  <div class="col-md-4">
    <!-- Actions rapides -->
    <div class="luna-card mb-3">
      <h4><i class="bi bi-lightning me-2"></i> Actions rapides</h4>
      <div class="d-grid gap-2 mb-3">
        <button id="vpn-quick-toggle-btn" class="btn btn-luna" style="font-size: 1.1rem; padding: 10px; margin-bottom: 10px; box-shadow: 0 0 15px rgba(0,0,0,0.3);">
          <i class="bi bi-shield-check me-1"></i> Activer VPN
        </button>
        <button id="antivirus-quick-toggle-btn" class="btn btn-success" style="font-size: 1.1rem; padding: 10px; margin-bottom: 10px; box-shadow: 0 0 15px rgba(0,0,0,0.3);">
          <i class="bi bi-virus me-1"></i> Antivirus Actif
        </button>
        <button id="reset-settings-btn" class="btn btn-luna-outline">
          <i class="bi bi-arrow-counterclockwise me-1"></i> Réinitialiser les paramètres
        </button>
        <button id="export-settings-btn" class="btn btn-luna-outline">
          <i class="bi bi-download me-1"></i> Exporter les paramètres
        </button>
        <button id="import-settings-btn" class="btn btn-luna-outline">
          <i class="bi bi-upload me-1"></i> Importer des paramètres
        </button>
      </div>
    </div>

    <!-- Informations système -->
    <div class="luna-card mb-3">
      <h4><i class="bi bi-info-circle me-2"></i> Informations système</h4>
      <div class="mb-3">
        <div class="d-flex justify-content-between mb-2">
          <span>Version</span>
          <span id="system-version">1.0.0</span>
        </div>
        <div class="d-flex justify-content-between mb-2">
          <span>Modèle</span>
          <span id="system-model">Claude 3 Opus</span>
        </div>
        <div class="d-flex justify-content-between mb-2">
          <span>Mémoire utilisée</span>
          <span id="system-memory">124/1000</span>
        </div>
        <div class="d-flex justify-content-between mb-2">
          <span>Accélérateurs</span>
          <span id="system-accelerators">16 actifs</span>
        </div>
        <div class="d-flex justify-content-between">
          <span>Dernière mise à jour</span>
          <span id="system-last-update"><%= new Date().toLocaleString('fr-FR', {day: '2-digit', month: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit'}) %></span>
        </div>
      </div>
    </div>

    <!-- Journal des modifications -->
    <div class="luna-card">
      <h4><i class="bi bi-journal-text me-2"></i> Journal des modifications</h4>
      <div id="settings-log" class="settings-log">
        <div class="log-entry">
          <span class="log-time">01/06/2023 14:30</span>
          <span class="log-message">Mise à jour du système vers la version 1.0.0</span>
        </div>
        <div class="log-entry">
          <span class="log-time">01/06/2023 14:25</span>
          <span class="log-message">Ajout de 4 nouveaux accélérateurs Kyber</span>
        </div>
        <div class="log-entry">
          <span class="log-time">01/06/2023 14:20</span>
          <span class="log-message">Modification du modèle principal vers Claude 3 Opus</span>
        </div>
        <div class="log-entry">
          <span class="log-time">01/06/2023 14:15</span>
          <span class="log-message">Augmentation de la capacité mémoire à 1000</span>
        </div>
        <div class="log-entry">
          <span class="log-time">01/06/2023 14:10</span>
          <span class="log-message">Activation des fonctionnalités expérimentales</span>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="/socket.io/socket.io.js"></script>
<script src="/js/luna-settings.js"></script>
