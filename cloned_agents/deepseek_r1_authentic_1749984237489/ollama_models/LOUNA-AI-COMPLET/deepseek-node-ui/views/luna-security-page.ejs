<!-- Styles spécifiques à la page de sécurité -->
<style>
  .security-card {
    background: rgba(26, 26, 46, 0.8);
    border-radius: 15px;
    padding: 1.2rem;
    margin-bottom: 1rem;
    border-left: 4px solid var(--luna-accent);
    transition: all 0.3s ease;
  }

  .security-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.3);
  }

  .security-card.active {
    border-left-color: #4caf50;
  }

  .security-card.inactive {
    border-left-color: #f44336;
  }

  .security-icon {
    font-size: 2rem;
    margin-right: 1rem;
    color: var(--luna-accent);
  }

  .security-log {
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    padding: 1rem;
    height: 200px;
    overflow-y: auto;
    font-family: monospace;
    font-size: 0.9rem;
  }

  .log-entry {
    margin-bottom: 0.5rem;
    padding: 0.3rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .log-entry.info { color: #3498db; }
  .log-entry.success { color: #2ecc71; }
  .log-entry.warning { color: #f39c12; }
  .log-entry.error { color: #e74c3c; }

  .progress {
    height: 8px;
    background-color: rgba(255, 255, 255, 0.1);
  }

  .progress-bar {
    background: var(--luna-gradient);
  }

  .threat-item {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 0.8rem;
    margin-bottom: 0.5rem;
    border-left: 3px solid #f44336;
  }

  .threat-item .threat-name {
    font-weight: 600;
  }

  .threat-item .threat-path {
    font-size: 0.8rem;
    color: #aaa;
    word-break: break-all;
  }

  .threat-item .threat-actions {
    margin-top: 0.5rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .spin {
    animation: spin 1s linear infinite;
    display: inline-block;
  }
</style>

<!-- Contenu principal -->
<div class="row">
  <div class="col-md-12 mb-4">
    <div class="d-flex justify-content-between align-items-center">
      <h2><i class="bi bi-shield-lock me-2"></i> Système de Sécurité</h2>
      <div>
        <button id="scan-now" class="btn btn-luna">
          <i class="bi bi-shield-check me-2"></i> Analyser maintenant
        </button>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <!-- Cartes de statut -->
  <div class="col-md-4">
    <div class="luna-card">
      <h4 class="mb-4"><i class="bi bi-shield"></i> Statut de sécurité</h4>

      <div class="security-card active" id="vpn-status-card">
        <div class="d-flex align-items-center">
          <i class="bi bi-shield-lock security-icon"></i>
          <div class="flex-grow-1">
            <h5 class="mb-1">VPN</h5>
            <p class="mb-2" id="vpn-status-text">Connecté</p>
            <div class="d-grid">
              <button id="toggle-vpn" class="btn btn-sm btn-danger">Déconnecter</button>
            </div>
          </div>
        </div>
      </div>

      <div class="security-card active" id="antivirus-status-card">
        <div class="d-flex align-items-center">
          <i class="bi bi-virus-slash security-icon"></i>
          <div class="flex-grow-1">
            <h5 class="mb-1">Antivirus</h5>
            <p class="mb-2" id="antivirus-status-text">Actif</p>
            <div class="d-grid">
              <button id="toggle-antivirus" class="btn btn-sm btn-danger">Désactiver</button>
            </div>
          </div>
        </div>
      </div>

      <div class="security-card active" id="firewall-status-card">
        <div class="d-flex align-items-center">
          <i class="bi bi-bricks security-icon"></i>
          <div class="flex-grow-1">
            <h5 class="mb-1">Pare-feu</h5>
            <p class="mb-2" id="firewall-status-text">Actif</p>
            <div class="d-grid">
              <button id="toggle-firewall" class="btn btn-sm btn-danger">Désactiver</button>
            </div>
          </div>
        </div>
      </div>

      <div class="security-card inactive" id="encryption-status-card">
        <div class="d-flex align-items-center">
          <i class="bi bi-key security-icon"></i>
          <div class="flex-grow-1">
            <h5 class="mb-1">Chiffrement</h5>
            <p class="mb-2" id="encryption-status-text">Inactif</p>
            <div class="d-grid">
              <button id="toggle-encryption" class="btn btn-sm btn-success">Activer</button>
            </div>
          </div>
        </div>
      </div>

      <div class="security-card inactive" id="behavioral-status-card">
        <div class="d-flex align-items-center">
          <i class="bi bi-braces-asterisk security-icon"></i>
          <div class="flex-grow-1">
            <h5 class="mb-1">Analyse comportementale</h5>
            <p class="mb-2" id="behavioral-status-text">Inactif</p>
            <div class="d-grid">
              <button id="toggle-behavioral" class="btn btn-sm btn-success">Activer</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Score de sécurité et journal -->
  <div class="col-md-8">
    <div class="luna-card">
      <h4 class="mb-4"><i class="bi bi-graph-up"></i> Score de sécurité</h4>
      <div class="row align-items-center">
        <div class="col-md-4 text-center">
          <div class="display-1 fw-bold" id="security-score">70</div>
          <p class="text-muted">sur 100</p>
        </div>
        <div class="col-md-8">
          <h5 class="mb-2">Niveau de protection: <span id="protection-level">Bon</span></h5>
          <div class="progress mb-4">
            <div class="progress-bar" id="security-score-bar" role="progressbar" style="width: 70%" aria-valuenow="70" aria-valuemin="0" aria-valuemax="100"></div>
          </div>

          <div class="row">
            <div class="col-6">
              <div class="d-flex align-items-center mb-2">
                <div class="status-indicator status-active me-2"></div>
                <div>VPN</div>
              </div>
              <div class="d-flex align-items-center mb-2">
                <div class="status-indicator status-active me-2"></div>
                <div>Antivirus</div>
              </div>
              <div class="d-flex align-items-center mb-2">
                <div class="status-indicator status-active me-2"></div>
                <div>Pare-feu</div>
              </div>
            </div>
            <div class="col-6">
              <div class="d-flex align-items-center mb-2">
                <div class="status-indicator status-inactive me-2"></div>
                <div>Chiffrement</div>
              </div>
              <div class="d-flex align-items-center mb-2">
                <div class="status-indicator status-inactive me-2"></div>
                <div>Analyse comportementale</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="luna-card">
      <h4 class="mb-3"><i class="bi bi-journal-text"></i> Journal de sécurité</h4>
      <div class="security-log" id="security-log">
        <div class="log-entry info">Système de sécurité initialisé</div>
        <div class="log-entry success">Pare-feu activé</div>
        <div class="log-entry success">Antivirus activé</div>
        <div class="log-entry info">Vérification des mises à jour de sécurité...</div>
        <div class="log-entry success">Système à jour</div>
      </div>
    </div>
  </div>
</div>

<div class="row mt-4">
  <div class="col-md-6">
    <div class="luna-card">
      <h4 class="mb-3"><i class="bi bi-shield-exclamation"></i> Menaces détectées</h4>
      <div id="threats-container">
        <p class="text-center text-muted" id="no-threats-message">Aucune menace détectée</p>
        <!-- Les menaces détectées seront ajoutées ici dynamiquement -->
      </div>
    </div>
  </div>

  <div class="col-md-6">
    <div class="luna-card">
      <h4 class="mb-3"><i class="bi bi-gear"></i> Paramètres de sécurité</h4>

      <div class="mb-3">
        <label for="security-level" class="form-label">Niveau de sécurité</label>
        <select class="form-select" id="security-level">
          <option value="low">Faible</option>
          <option value="standard" selected>Standard</option>
          <option value="high">Élevé</option>
          <option value="paranoid">Paranoïaque</option>
        </select>
      </div>

      <div class="mb-3">
        <label for="scan-frequency" class="form-label">Fréquence d'analyse</label>
        <select class="form-select" id="scan-frequency">
          <option value="daily">Quotidienne</option>
          <option value="weekly" selected>Hebdomadaire</option>
          <option value="monthly">Mensuelle</option>
        </select>
      </div>

      <div class="mb-3">
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="auto-update" checked>
          <label class="form-check-label" for="auto-update">Mises à jour automatiques</label>
        </div>
      </div>

      <div class="mb-3">
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="ransomware-protection" checked>
          <label class="form-check-label" for="ransomware-protection">Protection contre les ransomwares</label>
        </div>
      </div>

      <div class="mb-3">
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="network-protection" checked>
          <label class="form-check-label" for="network-protection">Protection réseau</label>
        </div>
      </div>

      <button id="save-settings" class="btn btn-luna">
        <i class="bi bi-save me-2"></i> Enregistrer les paramètres
      </button>
    </div>
  </div>
</div>

<!-- Scripts spécifiques à la page de sécurité -->
<script>
  $(document).ready(function() {
    // Initialiser Socket.IO
    const socket = io();

    // Charger les données de sécurité
    socket.emit('get security state');

    // Gestionnaire d'événements pour le bouton d'analyse
    $('#scan-now').on('click', function() {
      $(this).prop('disabled', true).html('<i class="bi bi-arrow-repeat spin"></i> Analyse en cours...');
      addLogEntry('Démarrage de l\'analyse...', 'info');
      socket.emit('start security scan');

      // Simuler une analyse (pour la démo)
      setTimeout(() => {
        addLogEntry('Analyse des fichiers système...', 'info');

        setTimeout(() => {
          addLogEntry('Vérification des connexions réseau...', 'info');

          setTimeout(() => {
            addLogEntry('Analyse des processus en cours d\'exécution...', 'info');

            setTimeout(() => {
              const threatFound = Math.random() > 0.7;

              if (threatFound) {
                addLogEntry('Menace détectée: PUP.Optional.Adware', 'warning');

                // Ajouter la menace à la liste
                $('#no-threats-message').hide();
                $('#threats-container').append(`
                  <div class="threat-item">
                    <div class="threat-name">PUP.Optional.Adware</div>
                    <div class="threat-path">/Users/<USER>/Downloads/installer.dmg</div>
                    <div class="threat-actions">
                      <button class="btn btn-sm btn-danger me-2">Supprimer</button>
                      <button class="btn btn-sm btn-secondary">Ignorer</button>
                    </div>
                  </div>
                `);

                // Mettre à jour le score de sécurité
                $('#security-score').text('65');
                $('#security-score-bar').css('width', '65%');
                $('#protection-level').text('Moyen').css('color', '#f39c12');
              } else {
                addLogEntry('Analyse terminée. Aucune menace détectée.', 'success');
              }

              // Réactiver le bouton d'analyse
              $('#scan-now').prop('disabled', false).html('<i class="bi bi-shield-check me-2"></i> Analyser maintenant');
            }, 1500);
          }, 1500);
        }, 1500);
      }, 1500);
    });

    // Gestionnaire d'événements pour le bouton VPN
    $('#toggle-vpn').on('click', function() {
      const isActive = $('#vpn-status-card').hasClass('active');
      $(this).prop('disabled', true);

      if (isActive) {
        addLogEntry('Déconnexion du VPN...', 'info');

        setTimeout(() => {
          $('#vpn-status-card').removeClass('active').addClass('inactive');
          $('#vpn-status-text').text('Déconnecté');
          $(this).removeClass('btn-danger').addClass('btn-success').text('Connecter');
          $(this).prop('disabled', false);

          // Mettre à jour le statut dans l'interface
          $('.status-indicator').eq(0).removeClass('status-active').addClass('status-inactive');

          // Mettre à jour le score de sécurité
          $('#security-score').text('60');
          $('#security-score-bar').css('width', '60%');
          $('#protection-level').text('Moyen').css('color', '#f39c12');

          addLogEntry('VPN déconnecté', 'info');
        }, 1000);
      } else {
        addLogEntry('Connexion au VPN...', 'info');

        setTimeout(() => {
          $('#vpn-status-card').removeClass('inactive').addClass('active');
          $('#vpn-status-text').text('Connecté');
          $(this).removeClass('btn-success').addClass('btn-danger').text('Déconnecter');
          $(this).prop('disabled', false);

          // Mettre à jour le statut dans l'interface
          $('.status-indicator').eq(0).removeClass('status-inactive').addClass('status-active');

          // Mettre à jour le score de sécurité
          $('#security-score').text('70');
          $('#security-score-bar').css('width', '70%');
          $('#protection-level').text('Bon').css('color', '#3498db');

          addLogEntry('VPN connecté', 'success');
        }, 1500);
      }
    });

    // Fonction pour ajouter une entrée au journal
    function addLogEntry(message, level = 'info') {
      const logContainer = $('#security-log');
      const timestamp = new Date().toLocaleTimeString();
      const entry = $(`<div class="log-entry ${level}">[${timestamp}] ${message}</div>`);
      logContainer.append(entry);
      logContainer.scrollTop(logContainer[0].scrollHeight);
    }

    // Gestionnaires d'événements pour les autres boutons
    $('#toggle-antivirus, #toggle-firewall, #toggle-encryption, #toggle-behavioral').on('click', function() {
      const id = $(this).attr('id');
      const type = id.replace('toggle-', '');
      const cardId = `#${type}-status-card`;
      const textId = `#${type}-status-text`;
      const isActive = $(cardId).hasClass('active');

      $(this).prop('disabled', true);

      if (isActive) {
        addLogEntry(`Désactivation de ${type}...`, 'info');

        setTimeout(() => {
          $(cardId).removeClass('active').addClass('inactive');
          $(textId).text('Inactif');
          $(this).removeClass('btn-danger').addClass('btn-success').text('Activer');
          $(this).prop('disabled', false);

          // Mettre à jour le score de sécurité
          const currentScore = parseInt($('#security-score').text());
          const newScore = Math.max(40, currentScore - 10);
          $('#security-score').text(newScore);
          $('#security-score-bar').css('width', `${newScore}%`);

          // Mettre à jour le niveau de protection
          if (newScore >= 80) {
            $('#protection-level').text('Excellent').css('color', '#2ecc71');
          } else if (newScore >= 60) {
            $('#protection-level').text('Bon').css('color', '#3498db');
          } else if (newScore >= 40) {
            $('#protection-level').text('Moyen').css('color', '#f39c12');
          } else {
            $('#protection-level').text('Faible').css('color', '#e74c3c');
          }

          addLogEntry(`${type} désactivé`, 'warning');
        }, 1000);
      } else {
        addLogEntry(`Activation de ${type}...`, 'info');

        setTimeout(() => {
          $(cardId).removeClass('inactive').addClass('active');
          $(textId).text('Actif');
          $(this).removeClass('btn-success').addClass('btn-danger').text('Désactiver');
          $(this).prop('disabled', false);

          // Mettre à jour le score de sécurité
          const currentScore = parseInt($('#security-score').text());
          const newScore = Math.min(100, currentScore + 10);
          $('#security-score').text(newScore);
          $('#security-score-bar').css('width', `${newScore}%`);

          // Mettre à jour le niveau de protection
          if (newScore >= 80) {
            $('#protection-level').text('Excellent').css('color', '#2ecc71');
          } else if (newScore >= 60) {
            $('#protection-level').text('Bon').css('color', '#3498db');
          } else if (newScore >= 40) {
            $('#protection-level').text('Moyen').css('color', '#f39c12');
          } else {
            $('#protection-level').text('Faible').css('color', '#e74c3c');
          }

          addLogEntry(`${type} activé`, 'success');
        }, 1500);
      }
    });

    // Gestionnaire d'événements pour le bouton d'enregistrement des paramètres
    $('#save-settings').on('click', function() {
      $(this).prop('disabled', true).html('<i class="bi bi-hourglass-split me-2"></i> Enregistrement...');

      addLogEntry('Enregistrement des paramètres de sécurité...', 'info');

      setTimeout(() => {
        $(this).prop('disabled', false).html('<i class="bi bi-save me-2"></i> Enregistrer les paramètres');
        addLogEntry('Paramètres de sécurité enregistrés avec succès', 'success');
      }, 1500);
    });
  });
</script>