<%- include('luna-base') %>

<!-- Intégration du contenu principal Luna -->
<div class="row mt-4">
  <!-- Panneau de conversation principal -->
  <div class="col-md-8">
    <div class="luna-card" style="height: 65vh; display: flex; flex-direction: column;">
      <h3 class="mb-3 d-flex align-items-center justify-content-between">
        <div><i class="bi bi-chat-square-text me-2"></i> Conversation avec Louna</div>
        <div>
          <span id="agentStatus" class="me-3">
            <span class="status-indicator status-inactive"></span>
            <span>En attente</span>
          </span>
        </div>
      </h3>
      
      <div id="conversation-container" style="flex-grow: 1; overflow-y: auto; margin-bottom: 1rem; padding: 1rem; border-radius: 10px; background-color: rgba(0,0,0,0.2); background-image: linear-gradient(to bottom, rgba(156, 137, 184, 0.05), rgba(240, 166, 202, 0.05));">
        <!-- Message de bienvenue initial -->
        <div class="message agent-message">
          <div class="message-avatar agent-avatar"><i class="bi bi-robot"></i></div>
          <div class="message-bubble agent-bubble">
            <div class="message-content">
              <p>Bienvenue dans l'interface Luna. Je suis Louna, votre assistant cognitif avancé.</p>
              <p>Comment puis-je vous aider aujourd'hui ?</p>
            </div>
            <div class="message-timestamp"><%=new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})%></div>
          </div>
        </div>
        
        <!-- Indicateur de connexion -->
        <div id="connection-status" class="message system-message" style="display: none;">
          <div class="message-avatar system-avatar"><i class="bi bi-info-circle"></i></div>
          <div class="message-bubble system-bubble">
            <div class="message-content">
              <p><i class="bi bi-cpu me-2"></i> Connexion à Ollama en cours d'établissement...</p>
            </div>
          </div>
        </div>
      </div>
      
      <div class="input-group">
        <input type="text" id="user-input" class="form-control" placeholder="Écrivez votre message ici..."
               style="border-radius: 25px 0 0 25px; padding: 0.75rem 1.5rem; background: rgba(255,255,255,0.1); border-color: var(--luna-accent); color: white;">
        <button id="send-button" class="btn btn-luna" style="border-radius: 0 25px 25px 0;">
          <i class="bi bi-send"></i> Envoyer
        </button>
      </div>
      
      <!-- Contenu des onglets -->
      <div class="tab-content" id="lunaInterfaceTabContent">
        <div class="tab-pane fade show active" id="chat-content" role="tabpanel" aria-labelledby="chat-tab">
          <div id="conversation-container" style="flex-grow: 1; overflow-y: auto; margin-bottom: 1rem; padding: 1rem; border-radius: 10px; background-color: rgba(0,0,0,0.2); background-image: linear-gradient(to bottom, rgba(156, 137, 184, 0.05), rgba(240, 166, 202, 0.05));">
            <!-- Message de bienvenue initial -->
            <div class="message agent-message">
              <div class="message-avatar agent-avatar"><i class="bi bi-robot"></i></div>
              <div class="message-bubble agent-bubble">
                <div class="message-content">
                  <p>Bienvenue dans l'interface Luna. Je suis Louna, votre assistant cognitif avancé.</p>
                  <p>Comment puis-je vous aider aujourd'hui ?</p>
                </div>
                <div class="message-timestamp"><%=new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})%></div>
              </div>
            <div class="message-content">
              <p>Bienvenue dans l'interface Luna. Je suis Louna, votre assistant cognitif avancé.</p>
              <p>Comment puis-je vous aider aujourd'hui ?</p>
            </div>
            <div class="message-timestamp"><%=new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})%></div>
          </div>
        </div>
        
        <!-- Indicateur de connexion -->
        <div id="connection-status" class="message system-message" style="display: none;">
          <div class="message-avatar system-avatar"><i class="bi bi-info-circle"></i></div>
          <div class="message-bubble system-bubble">
            <div class="message-content">
              <p><i class="bi bi-cpu me-2"></i> Connexion à Ollama en cours d'établissement...</p>
            </div>
          </div>
        </div>
      </div>
      
      <div class="input-group">
        <input type="text" id="user-input" class="form-control" placeholder="Écrivez votre message ici..."
               style="border-radius: 25px 0 0 25px; padding: 0.75rem 1.5rem; background: rgba(255,255,255,0.1); border-color: var(--luna-accent); color: white;">
        <button id="send-button" class="btn btn-luna" style="border-radius: 0 25px 25px 0;">
          <i class="bi bi-send"></i> Envoyer
        </button>
      </div>
    </div>
  </div>
  
  <!-- Panneau latéral avec les fonctions et les capteurs -->
  <div class="col-md-4">
    <!-- Sélection de modèle -->
    <div class="luna-card mb-3">
      <h4><i class="bi bi-cpu me-2"></i> Modèle IA</h4>
      <div class="mb-3">
        <label for="model-select" class="form-label">Modèle actif</label>
        <select id="model-select" class="form-select" aria-label="Sélection du modèle">
          <!-- Les modèles seront ajoutés dynamiquement ici -->
          <option value="deepseek-r1:7b" selected>deepseek-r1:7b (par défaut)</option>
        </select>
      </div>
      <div class="d-flex justify-content-between mb-2">
        <button id="refresh-models" class="btn btn-sm btn-outline-light">
          <i class="bi bi-arrow-clockwise me-1"></i> Actualiser
        </button>
        <button id="install-model" class="btn btn-sm btn-luna">
          <i class="bi bi-download me-1"></i> Installer un modèle
        </button>
      </div>
    </div>

    <!-- Carte des fonctions cognitives -->
    <div class="luna-card mb-3">
      <h4><i class="bi bi-braces-asterisk me-2"></i> Fonctions Cognitives</h4>
      <div class="mb-3">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span><i class="bi bi-ear me-2"></i> Écoute vocale</span>
          <div>
            <span id="voiceListenStatus">
              <span class="status-indicator status-inactive"></span>
            </span>
            <button id="toggleListenBtn" class="btn btn-sm btn-luna">
              <i class="bi bi-mic"></i>
            </button>
          </div>
        </div>
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span><i class="bi bi-eye me-2"></i> Vision</span>
          <div>
            <span id="visionStatus">
              <span class="status-indicator status-inactive"></span>
            </span>
            <button id="toggleVisionBtn" class="btn btn-sm btn-luna">
              <i class="bi bi-camera"></i>
            </button>
          </div>
        </div>
        <div class="d-flex justify-content-between align-items-center">
          <span><i class="bi bi-volume-up me-2"></i> Synthèse vocale</span>
          <div>
            <span id="speechStatus">
              <span class="status-indicator status-inactive"></span>
            </span>
            <button id="testSpeechBtn" class="btn btn-sm btn-luna">
              <i class="bi bi-chat-quote"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Carte de la mémoire thermique -->
    <div class="luna-card mb-3">
      <h4><i class="bi bi-hdd-network me-2"></i> Mémoire Thermique</h4>
      <div id="memory-status" class="mb-2" style="font-size: 0.9rem;">
        <div><strong>État:</strong> <span id="memory-state">Optimale</span></div>
        <div><strong>Capacité:</strong> <span id="memory-capacity">94%</span></div>
        <div><strong>Zones actives:</strong> <span id="memory-zones">6/6</span></div>
      </div>
      <div class="progress" style="height: 15px; margin-bottom: 10px; background: rgba(0,0,0,0.2);">
        <div id="memory-progress" class="progress-bar" role="progressbar" style="width: 64%; background: var(--luna-gradient);" aria-valuenow="64" aria-valuemin="0" aria-valuemax="100">64%</div>
      </div>
      <div class="d-grid gap-2">
        <button id="viewMemoryBtn" class="btn btn-sm btn-luna-outline">
          <i class="bi bi-eye me-1"></i> Voir la mémoire
        </button>
      </div>
    </div>
    
    <!-- Carte MCP -->
    <div class="luna-card">
      <h4><i class="bi bi-cpu me-2"></i> Système MCP</h4>
      <div class="mb-2">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span title="Accès Internet"><i class="bi bi-globe me-2"></i> Internet</span>
          <span id="internetStatus">
            <span class="status-indicator status-active"></span>
          </span>
        </div>
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span title="Accès aux fichiers système"><i class="bi bi-folder me-2"></i> Système</span>
          <span id="filesystemStatus">
            <span class="status-indicator status-active"></span>
          </span>
        </div>
        <div class="d-flex justify-content-between align-items-center">
          <span title="Accès aux ressources matérielles"><i class="bi bi-pc-display me-2"></i> Matériel</span>
          <span id="hardwareStatus">
            <span class="status-indicator status-active"></span>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Panneau inférieur avec les métriques système -->
<div class="row mt-3">
  <div class="col-12">
    <div class="luna-card">
      <h4 class="mb-3"><i class="bi bi-speedometer2 me-2"></i> Métriques Système</h4>
      <div class="row">
        <div class="col-md-3">
          <div class="d-flex align-items-center">
            <i class="bi bi-cpu me-3" style="font-size: 2rem; color: var(--luna-secondary);"></i>
            <div>
              <div class="text-muted">Utilisation CPU</div>
              <div class="h5 mb-0" id="cpu-usage">32%</div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="d-flex align-items-center">
            <i class="bi bi-memory me-3" style="font-size: 2rem; color: var(--luna-primary);"></i>
            <div>
              <div class="text-muted">Mémoire</div>
              <div class="h5 mb-0" id="ram-usage">2.4 GB</div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="d-flex align-items-center">
            <i class="bi bi-activity me-3" style="font-size: 2rem; color: var(--luna-accent);"></i>
            <div>
              <div class="text-muted">Latence</div>
              <div class="h5 mb-0" id="latency">24ms</div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="d-flex align-items-center">
            <i class="bi bi-thermometer-half me-3" style="font-size: 2rem; color: var(--luna-secondary);"></i>
            <div>
              <div class="text-muted">Temp. Mémoire</div>
              <div class="h5 mb-0" id="thermal-index">72°C</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="/socket.io/socket.io.js"></script>
<script src="/js/luna-interface.js"></script>

<script>
  // Initialisation supplémentaire spécifique à cette page
  $(document).ready(function() {
    // Initialiser l'interface
    const conversationContainer = $('#conversation-container');
    const userInput = $('#user-input');
    const sendButton = $('#send-button');
    const modelSelect = $('#model-select');
    const refreshModelsButton = $('#refresh-models');
    const installModelButton = $('#install-model');
    let awaitingResponse = false;
    let isConnected = false;
    
    // Configuration initiale de l'interface
    updateConnectionStatus(false);
    loadAvailableModels();
    
    // Charger les modèles disponibles
    function loadAvailableModels() {
      $.ajax({
        url: '/luna/models',
      }
      
      .user-message {
        float: right;
      }
      
      .agent-message, .system-message {
        float: left;
      }
      
      .message-content {
        padding: 0.75rem 1rem;
        border-radius: 18px;
        overflow-wrap: break-word;
      }
      
      .user-message .message-content {
        background: var(--luna-gradient);
        color: white;
        border-radius: 18px 18px 3px 18px;
      }
      
      .agent-message .message-content {
        background: rgba(184, 190, 221, 0.15);
        color: var(--luna-light);
        border-radius: 18px 18px 18px 3px;
        border-left: 3px solid var(--luna-secondary);
      }
      
      .system-message .message-content {
        background: rgba(184, 190, 221, 0.05);
        color: #aaa;
        font-style: italic;
        border-radius: 18px;
        text-align: center;
        max-width: 90%;
        margin: 0 auto;
        float: none;
      }
      
      .message-content p:last-child {
        margin-bottom: 0;
      }
      
      /* Animation des points de suspension pour l'indication de frappe */
      .typing-dots {
        display: flex;
        justify-content: center;
        padding: 0.5rem;
      }
      
      .typing-dots span {
        width: 8px;
        height: 8px;
        margin: 0 3px;
        background-color: var(--luna-light);
        border-radius: 50%;
        display: inline-block;
        animation: typing-dot 1.5s infinite ease-in-out;
      }
      
      .typing-dots span:nth-child(1) { animation-delay: 0s; }
      .typing-dots span:nth-child(2) { animation-delay: 0.3s; }
      .typing-dots span:nth-child(3) { animation-delay: 0.6s; }
      
      @keyframes typing-dot {
        0%, 60%, 100% { transform: translateY(0); }
        30% { transform: translateY(-5px); }
      }
    `;
    document.head.appendChild(style);
  });
</script>
