<%- include('luna-base') %>
<link rel="stylesheet" href="/css/luna-dashboard.css">
<link rel="stylesheet" href="/css/brain-presence.css">

<!-- Intégration du contenu principal Luna Dashboard -->
<div class="row mt-4">
  <!-- Panneau principal avec les statistiques du système -->
  <div class="col-md-8">
    <div class="luna-card mb-4">
      <h3 class="mb-3 d-flex align-items-center justify-content-between">
        <div><i class="bi bi-speedometer2 me-2"></i> Tableau de bord système</div>
        <div>
          <button id="refresh-dashboard" class="btn btn-sm btn-luna-outline">
            <i class="bi bi-arrow-clockwise me-1"></i> Actualiser
          </button>
        </div>
      </h3>
      
      <!-- Statistiques principales -->
      <div class="row mb-4">
        <div class="col-md-3">
          <div class="stat-card">
            <div class="stat-icon"><i class="bi bi-cpu"></i></div>
            <div class="stat-info">
              <div class="stat-label">CPU</div>
              <div class="stat-value" id="cpu-usage">42%</div>
              <div class="progress">
                <div class="progress-bar" id="cpu-progress" role="progressbar" style="width: 42%"></div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="stat-card">
            <div class="stat-icon"><i class="bi bi-memory"></i></div>
            <div class="stat-info">
              <div class="stat-label">Mémoire</div>
              <div class="stat-value" id="memory-usage">2.4 GB</div>
              <div class="progress">
                <div class="progress-bar" id="memory-progress" role="progressbar" style="width: 65%"></div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="stat-card">
            <div class="stat-icon"><i class="bi bi-thermometer-half"></i></div>
            <div class="stat-info">
              <div class="stat-label">Température</div>
              <div class="stat-value" id="thermal-index">72°C</div>
              <div class="progress">
                <div class="progress-bar" id="thermal-progress" role="progressbar" style="width: 72%"></div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="stat-card">
            <div class="stat-icon"><i class="bi bi-lightning-charge"></i></div>
            <div class="stat-info">
              <div class="stat-label">Accélérateurs</div>
              <div class="stat-value" id="accelerator-efficiency">149%</div>
              <div class="progress">
                <div class="progress-bar" id="accelerator-progress" role="progressbar" style="width: 90%"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Graphique d'activité -->
      <div class="row">
        <div class="col-md-12">
          <div class="chart-container">
            <canvas id="system-activity-chart"></canvas>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Activité récente -->
    <div class="luna-card mb-4">
      <h3 class="mb-3"><i class="bi bi-activity me-2"></i> Activité récente</h3>
      <div class="activity-list" id="activity-list">
        <div class="activity-item">
          <div class="activity-icon"><i class="bi bi-chat-dots"></i></div>
          <div class="activity-content">
            <div class="activity-title">Conversation avec l'utilisateur</div>
            <div class="activity-description">Échange sur les capacités du système</div>
            <div class="activity-time">Il y a 5 minutes</div>
          </div>
        </div>
        <div class="activity-item">
          <div class="activity-icon"><i class="bi bi-lightning-charge"></i></div>
          <div class="activity-content">
            <div class="activity-title">Optimisation des accélérateurs</div>
            <div class="activity-description">Amélioration de l'efficacité de 3%</div>
            <div class="activity-time">Il y a 15 minutes</div>
          </div>
        </div>
        <div class="activity-item">
          <div class="activity-icon"><i class="bi bi-thermometer-half"></i></div>
          <div class="activity-content">
            <div class="activity-title">Cycle de refroidissement</div>
            <div class="activity-description">Température réduite de 85°C à 72°C</div>
            <div class="activity-time">Il y a 30 minutes</div>
          </div>
        </div>
        <div class="activity-item">
          <div class="activity-icon"><i class="bi bi-hdd"></i></div>
          <div class="activity-content">
            <div class="activity-title">Consolidation de mémoire</div>
            <div class="activity-description">12 éléments transférés vers la mémoire à long terme</div>
            <div class="activity-time">Il y a 45 minutes</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Panneau latéral avec les accès rapides et l'état du système -->
  <div class="col-md-4">
    <!-- Carte d'accès rapide -->
    <div class="luna-card mb-4">
      <h4><i class="bi bi-lightning me-2"></i> Accès rapide</h4>
      <div class="quick-access-grid">
        <a href="/luna" class="quick-access-item">
          <i class="bi bi-chat-dots"></i>
          <span>Chat</span>
        </a>
        <a href="/luna/memory" class="quick-access-item">
          <i class="bi bi-hdd"></i>
          <span>Mémoire</span>
        </a>
        <a href="/luna/training" class="quick-access-item">
          <i class="bi bi-graduation-cap"></i>
          <span>Formation</span>
        </a>
        <a href="/luna/code" class="quick-access-item">
          <i class="bi bi-code-slash"></i>
          <span>Code</span>
        </a>
        <a href="/luna/internet" class="quick-access-item">
          <i class="bi bi-globe"></i>
          <span>Internet</span>
        </a>
        <a href="/luna/security" class="quick-access-item">
          <i class="bi bi-shield-lock"></i>
          <span>Sécurité</span>
        </a>
        <a href="/luna/brain" class="quick-access-item">
          <i class="bi bi-diagram-3"></i>
          <span>Cerveau</span>
        </a>
        <a href="/luna/thermal" class="quick-access-item">
          <i class="bi bi-thermometer-half"></i>
          <span>Thermique</span>
        </a>
      </div>
    </div>
    
    <!-- Carte de statut du système -->
    <div class="luna-card mb-4">
      <h4><i class="bi bi-check-circle me-2"></i> Statut du système</h4>
      <div class="system-status-list">
        <div class="system-status-item">
          <div class="status-label">Ollama</div>
          <div class="status-value">
            <span class="status-indicator status-active"></span>
            <span id="ollama-status">Connecté</span>
          </div>
        </div>
        <div class="system-status-item">
          <div class="status-label">Mémoire thermique</div>
          <div class="status-value">
            <span class="status-indicator status-active"></span>
            <span id="thermal-memory-status">Optimale</span>
          </div>
        </div>
        <div class="system-status-item">
          <div class="status-label">Accélérateurs Kyber</div>
          <div class="status-value">
            <span class="status-indicator status-active"></span>
            <span id="kyber-status">30 actifs</span>
          </div>
        </div>
        <div class="system-status-item">
          <div class="status-label">Système MCP</div>
          <div class="status-value">
            <span class="status-indicator status-active"></span>
            <span id="mcp-status">Opérationnel</span>
          </div>
        </div>
        <div class="system-status-item">
          <div class="status-label">Présence du cerveau</div>
          <div class="status-value">
            <span class="status-indicator status-active"></span>
            <span id="brain-presence-status">Active</span>
          </div>
        </div>
        <div class="system-status-item">
          <div class="status-label">Sauvegarde auto</div>
          <div class="status-value">
            <span class="status-indicator status-active"></span>
            <span id="backup-status">Activée</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Carte de modèle actif -->
    <div class="luna-card mb-4">
      <h4><i class="bi bi-cpu me-2"></i> Modèle IA actif</h4>
      <div class="model-info">
        <div class="model-name" id="active-model">deepseek-r1:7b</div>
        <div class="model-details">
          <div class="model-detail">
            <span class="detail-label">Type:</span>
            <span class="detail-value" id="model-type">LLM</span>
          </div>
          <div class="model-detail">
            <span class="detail-label">Taille:</span>
            <span class="detail-value" id="model-size">7B</span>
          </div>
          <div class="model-detail">
            <span class="detail-label">Contexte:</span>
            <span class="detail-value" id="model-context">8K tokens</span>
          </div>
        </div>
        <div class="model-actions">
          <button id="change-model" class="btn btn-sm btn-luna">
            <i class="bi bi-arrow-repeat me-1"></i> Changer de modèle
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Présence du cerveau -->
<div class="row mt-4">
  <div class="col-md-12">
    <%- include('partials/brain-presence') %>
  </div>
</div>

<script src="/socket.io/socket.io.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="/js/brain-presence-ui.js"></script>

<script>
  $(document).ready(function() {
    // Initialiser la connexion Socket.IO
    const socket = io();
    
    // Initialiser le graphique d'activité système
    const ctx = document.getElementById('system-activity-chart').getContext('2d');
    const systemActivityChart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: Array(20).fill('').map((_, i) => `-${20-i}s`),
        datasets: [
          {
            label: 'CPU',
            data: Array(20).fill(0),
            borderColor: 'rgba(156, 137, 184, 1)',
            backgroundColor: 'rgba(156, 137, 184, 0.1)',
            tension: 0.4,
            fill: true
          },
          {
            label: 'Mémoire',
            data: Array(20).fill(0),
            borderColor: 'rgba(240, 166, 202, 1)',
            backgroundColor: 'rgba(240, 166, 202, 0.1)',
            tension: 0.4,
            fill: true
          },
          {
            label: 'Température',
            data: Array(20).fill(0),
            borderColor: 'rgba(255, 107, 107, 1)',
            backgroundColor: 'rgba(255, 107, 107, 0.1)',
            tension: 0.4,
            fill: true
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'top',
            labels: {
              color: 'rgba(255, 255, 255, 0.7)'
            }
          },
          title: {
            display: true,
            text: 'Activité système en temps réel',
            color: 'rgba(255, 255, 255, 0.9)'
          }
        },
        scales: {
          x: {
            grid: {
              color: 'rgba(255, 255, 255, 0.1)'
            },
            ticks: {
              color: 'rgba(255, 255, 255, 0.7)'
            }
          },
          y: {
            grid: {
              color: 'rgba(255, 255, 255, 0.1)'
            },
            ticks: {
              color: 'rgba(255, 255, 255, 0.7)'
            },
            min: 0,
            max: 100
          }
        }
      }
    });
    
    // Fonction pour mettre à jour le graphique avec de nouvelles données
    function updateChart(cpuUsage, memoryUsage, temperature) {
      // Ajouter les nouvelles données
      systemActivityChart.data.datasets[0].data.push(cpuUsage);
      systemActivityChart.data.datasets[1].data.push(memoryUsage);
      systemActivityChart.data.datasets[2].data.push(temperature);
      
      // Supprimer les anciennes données
      if (systemActivityChart.data.datasets[0].data.length > 20) {
        systemActivityChart.data.datasets[0].data.shift();
        systemActivityChart.data.datasets[1].data.shift();
        systemActivityChart.data.datasets[2].data.shift();
      }
      
      // Mettre à jour le graphique
      systemActivityChart.update();
    }
    
    // Simuler des mises à jour de données pour le graphique
    let simulationInterval = setInterval(() => {
      const cpuUsage = Math.floor(30 + Math.random() * 30);
      const memoryUsage = Math.floor(50 + Math.random() * 20);
      const temperature = Math.floor(60 + Math.random() * 25);
      
      // Mettre à jour le graphique
      updateChart(cpuUsage, memoryUsage, temperature);
      
      // Mettre à jour les indicateurs
      $('#cpu-usage').text(`${cpuUsage}%`);
      $('#cpu-progress').css('width', `${cpuUsage}%`);
      
      $('#memory-usage').text(`${(memoryUsage / 10).toFixed(1)} GB`);
      $('#memory-progress').css('width', `${memoryUsage}%`);
      
      $('#thermal-index').text(`${temperature}°C`);
      $('#thermal-progress').css('width', `${temperature}%`);
    }, 2000);
    
    // Nettoyer l'intervalle lorsque la page est fermée
    $(window).on('beforeunload', function() {
      clearInterval(simulationInterval);
    });
    
    // Gestionnaire pour le bouton d'actualisation
    $('#refresh-dashboard').on('click', function() {
      // Simuler une actualisation des données
      $(this).html('<i class="bi bi-arrow-clockwise me-1 spin"></i> Actualisation...');
      
      setTimeout(() => {
        $(this).html('<i class="bi bi-arrow-clockwise me-1"></i> Actualiser');
        // Mettre à jour les données ici
      }, 1000);
    });
    
    // Gestionnaire pour le bouton de changement de modèle
    $('#change-model').on('click', function() {
      window.location.href = '/luna/models';
    });
  });
</script>
