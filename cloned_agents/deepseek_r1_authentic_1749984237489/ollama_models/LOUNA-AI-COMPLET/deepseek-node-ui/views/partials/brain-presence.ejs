<!-- Composant de présence du cerveau -->
<div id="brain-presence-container" class="brain-presence-container compact">
  <!-- En-tête -->
  <div class="brain-presence-header">
    <div class="brain-presence-title">
      <div id="brain-presence-indicator" class="brain-presence-indicator presence-inactive"></div>
      <span>Activité Cérébrale</span>
      <span id="brain-presence-status" class="status-inactive ms-2">
        <i class="bi bi-broadcast-pin"></i> Déconnecté
      </span>
    </div>
    <div class="brain-presence-controls">
      <button id="toggle-animation-btn" title="Activer/désactiver l'animation">
        <i class="bi bi-pause-circle"></i>
      </button>
      <button id="toggle-thought-mode-btn" title="Basculer entre pensée actuelle et historique">
        <i class="bi bi-chat"></i>
      </button>
      <button id="toggle-display-mode-btn" title="Développer/réduire">
        <i class="bi bi-arrows-angle-expand"></i>
      </button>
    </div>
  </div>

  <!-- Contenu -->
  <div class="brain-presence-content">
    <!-- Informations d'activité -->
    <div class="brain-activity-info">
      <div class="activity-level">
        Niveau d'activité: <span id="brain-activity-level">0%</span>
      </div>
    </div>
    <div class="activity-progress">
      <div id="brain-activity-progress" class="progress-bar bg-info" style="width: 0%"></div>
    </div>

    <!-- Mode étendu uniquement -->
    <div class="expanded-content" style="display: none;">
      <!-- Animation du cerveau -->
      <div class="brain-animation-container">
        <div id="brain-animation" class="brain-animation"></div>
      </div>

      <!-- Statistiques avancées -->
      <div class="brain-stats-container">
        <div class="row">
          <div class="col-md-6">
            <div class="stats-card">
              <div class="stats-title">Statistiques Neuronales</div>
              <div class="stats-content">
                <div class="stats-item">
                  <span class="stats-label">Neurones actifs:</span>
                  <span id="neuron-count" class="stats-value">1,000</span>
                </div>
                <div class="stats-item">
                  <span class="stats-label">Connexions:</span>
                  <span id="connection-count" class="stats-value">5,000</span>
                </div>
                <div class="stats-item">
                  <span class="stats-label">QI estimé:</span>
                  <span id="iq-estimate" class="stats-value">100</span>
                </div>
                <div class="stats-item">
                  <span class="stats-label">Taux d'apprentissage:</span>
                  <span id="learning-rate" class="stats-value">50%</span>
                </div>
                <div class="stats-item">
                  <span class="stats-label">Pensées générées:</span>
                  <span id="thought-count" class="stats-value">0</span>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="stats-card">
              <div class="stats-title">Accélérateurs Kyber</div>
              <div class="stats-content">
                <div class="stats-item">
                  <span class="stats-label">Efficacité:</span>
                  <span id="accelerator-efficiency" class="stats-value">100%</span>
                </div>
                <div class="stats-item">
                  <span class="stats-label">Nombre actif:</span>
                  <span id="accelerator-count" class="stats-value">0</span>
                </div>
                <div class="stats-item">
                  <span class="stats-label">Charge:</span>
                  <span id="accelerator-load" class="stats-value">0%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Graphiques -->
      <div class="brain-charts">
        <div class="chart-container">
          <div class="chart-title">Activité au fil du temps</div>
          <canvas id="activity-chart"></canvas>
        </div>
        <div class="chart-container">
          <div class="chart-title">Activité par zone</div>
          <canvas id="zone-activity-chart"></canvas>
        </div>
      </div>

      <!-- Pensées -->
      <div class="thought-container">
        <div class="thought-title">
          <span>Pensées Actives</span>
        </div>

        <!-- Pensée actuelle -->
        <div id="current-thought-container">
          <div id="current-thought">
            <div class="thought-item thought-observation">
              <div class="thought-header">
                <span class="thought-type">observation</span>
                <span class="thought-time">00:00:00</span>
              </div>
              <div class="thought-content">
                Initialisation du système de présence autonome...
              </div>
            </div>
          </div>
        </div>

        <!-- Historique des pensées -->
        <div id="thought-history-container" class="thought-history-container">
          <div id="thought-history"></div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Script pour gérer l'affichage du mode étendu -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const presenceContainer = document.getElementById('brain-presence-container');
    const expandedContent = document.querySelector('.expanded-content');
    const toggleDisplayModeBtn = document.getElementById('toggle-display-mode-btn');

    if (presenceContainer && expandedContent && toggleDisplayModeBtn) {
      toggleDisplayModeBtn.addEventListener('click', function() {
        const isCompact = presenceContainer.classList.contains('compact');

        if (isCompact) {
          presenceContainer.classList.remove('compact');
          presenceContainer.classList.add('expanded');
          expandedContent.style.display = 'block';
          this.innerHTML = '<i class="bi bi-arrows-angle-contract"></i>';
        } else {
          presenceContainer.classList.remove('expanded');
          presenceContainer.classList.add('compact');
          expandedContent.style.display = 'none';
          this.innerHTML = '<i class="bi bi-arrows-angle-expand"></i>';
        }
      });
    }
  });
</script>
