<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %></title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.0/font/bootstrap-icons.css">
  <style>
    body { background-color: #121212; color: #f0f0f0; }
    .main-container { max-width: 1200px; margin: 0 auto; padding: 20px; }
    .cognitive-card { background-color: #1e1e1e; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
    .sensor-output { background-color: #2a2a2a; padding: 15px; border-radius: 5px; font-family: monospace; min-height: 100px; margin-top: 10px; }
    .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 5px; }
    .status-active { background-color: #28a745; }
    .status-inactive { background-color: #dc3545; }
    .btn-primary { background-color: #7e57c2; border-color: #7e57c2; }
    .btn-primary:hover { background-color: #673ab7; border-color: #673ab7; }
  </style>
</head>
<body>
  <!-- Barre de navigation -->
  <%- include('partials/navbar', { activeTab: 'cognitive' }) %>

  <div class="main-container">
    <h1 class="my-4">Système Cognitif DeepSeek</h1>
    <p class="lead">Interface de contrôle pour les capacités cognitives de l'agent DeepSeek r1</p>
    
    <div class="row">
      <div class="col-md-6">
        <div class="cognitive-card">
          <h3><i class="bi bi-soundwave"></i> Système Vocal</h3>
          <div class="d-flex justify-content-between mb-3">
            <span id="speechStatus">
              <span class="status-indicator status-inactive"></span>
              Inactif
            </span>
            <div>
              <button id="startListeningBtn" class="btn btn-sm btn-primary"><i class="bi bi-mic"></i> Écouter</button>
              <button id="stopListeningBtn" class="btn btn-sm btn-outline-light"><i class="bi bi-mic-mute"></i> Arrêter</button>
            </div>
          </div>
          <div class="mb-3">
            <label for="speechText" class="form-label">Texte à prononcer</label>
            <div class="input-group">
              <input type="text" class="form-control bg-dark text-light" id="speechText" placeholder="Entrez un texte...">
              <button class="btn btn-primary" id="speakBtn">Parler</button>
            </div>
          </div>
          <div class="sensor-output" id="speechOutput">Aucune activité vocale</div>
        </div>
      </div>
      
      <div class="col-md-6">
        <div class="cognitive-card">
          <h3><i class="bi bi-camera"></i> Système Visuel</h3>
          <div class="d-flex justify-content-between mb-3">
            <span id="visionStatus">
              <span class="status-indicator status-inactive"></span>
              Inactif
            </span>
            <div>
              <button id="observeBtn" class="btn btn-sm btn-primary"><i class="bi bi-eye"></i> Observer</button>
              <button id="startContinuousBtn" class="btn btn-sm btn-outline-light"><i class="bi bi-camera-video"></i> Continu</button>
              <button id="stopContinuousBtn" class="btn btn-sm btn-outline-light"><i class="bi bi-camera-video-off"></i> Arrêter</button>
            </div>
          </div>
          <div class="sensor-output" id="visionOutput">Aucune observation</div>
        </div>
      </div>
    </div>
    
    <div class="cognitive-card">
      <h3><i class="bi bi-braces"></i> État du Système Cognitif</h3>
      <div class="d-flex justify-content-between mb-3">
        <span id="cognitiveStatus">
          <span class="status-indicator status-inactive"></span>
          Inactif
        </span>
        <div>
          <button id="activateBtn" class="btn btn-sm btn-success"><i class="bi bi-power"></i> Activer</button>
          <button id="deactivateBtn" class="btn btn-sm btn-danger"><i class="bi bi-power"></i> Désactiver</button>
        </div>
      </div>
      <div class="sensor-output" id="stateOutput">Aucune donnée d'état</div>
    </div>
    
    <div class="cognitive-card">
      <h3><i class="bi bi-tools"></i> Configuration Matérielle</h3>
      <p>Pour utiliser des périphériques réels avec le système cognitif, installez les dépendances suivantes :</p>
      
      <div class="row">
        <div class="col-md-6">
          <h5>Reconnaissance Vocale</h5>
          <pre class="bg-dark p-3 text-light">
# MacOS
brew install sox
brew install portaudio

# Linux
sudo apt-get install sox libsox-fmt-all
sudo apt-get install portaudio19-dev

# Installation Node.js
npm install node-record-lpcm16</pre>
        </div>
        
        <div class="col-md-6">
          <h5>Synthèse Vocale</h5>
          <pre class="bg-dark p-3 text-light">
# MacOS
brew install espeak

# Linux
sudo apt-get install espeak

# Ou Google TTS (pour les deux)
npm install gtts</pre>
        </div>
      </div>
      
      <div class="mt-3">
        <h5>Intégration Caméra</h5>
        <pre class="bg-dark p-3 text-light">
# Installation
npm install node-webcam
npm install @tensorflow/tfjs-node
npm install @tensorflow-models/coco-ssd</pre>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Activer le système cognitif
      document.getElementById('activateBtn').addEventListener('click', async () => {
        try {
          const response = await fetch('/api/cognitive/activate');
          const data = await response.json();
          if (data.success) {
            updateStatus('cognitiveStatus', true);
            document.getElementById('stateOutput').textContent = 'Système cognitif activé';
            fetchState();
          }
        } catch (error) {
          console.error('Erreur:', error);
        }
      });
      
      // Désactiver le système cognitif
      document.getElementById('deactivateBtn').addEventListener('click', async () => {
        try {
          const response = await fetch('/api/cognitive/deactivate');
          const data = await response.json();
          if (data.success) {
            updateStatus('cognitiveStatus', false);
            document.getElementById('stateOutput').textContent = 'Système cognitif désactivé';
            fetchState();
          }
        } catch (error) {
          console.error('Erreur:', error);
        }
      });
      
      // Synthèse vocale
      document.getElementById('speakBtn').addEventListener('click', async () => {
        const text = document.getElementById('speechText').value;
        if (!text) return;
        
        try {
          const response = await fetch('/api/cognitive/speak', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ text })
          });
          
          const data = await response.json();
          if (data.success) {
            updateStatus('speechStatus', true);
            document.getElementById('speechOutput').textContent = 'Synthèse vocale: "' + text + '"';
            
            setTimeout(() => {
              updateStatus('speechStatus', false);
            }, 2000);
          }
        } catch (error) {
          console.error('Erreur:', error);
        }
      });
      
      // Démarrer l'écoute vocale
      document.getElementById('startListeningBtn').addEventListener('click', async () => {
        try {
          const response = await fetch('/api/cognitive/listen/start');
          const data = await response.json();
          if (data.success) {
            updateStatus('speechStatus', true);
            document.getElementById('speechOutput').textContent = 'Écoute vocale démarrée...';
          }
        } catch (error) {
          console.error('Erreur:', error);
        }
      });
      
      // Arrêter l'écoute vocale
      document.getElementById('stopListeningBtn').addEventListener('click', async () => {
        try {
          const response = await fetch('/api/cognitive/listen/stop');
          const data = await response.json();
          if (data.success) {
            updateStatus('speechStatus', false);
            document.getElementById('speechOutput').textContent = 'Écoute vocale arrêtée';
          }
        } catch (error) {
          console.error('Erreur:', error);
        }
      });
      
      // Observer l'environnement
      document.getElementById('observeBtn').addEventListener('click', async () => {
        try {
          const response = await fetch('/api/cognitive/observe');
          const data = await response.json();
          if (data.success) {
            updateStatus('visionStatus', true);
            document.getElementById('visionOutput').textContent = 'Observation en cours...';
            
            // Récupérer l'état après un délai pour voir les résultats
            setTimeout(() => {
              fetchState();
              updateStatus('visionStatus', false);
            }, 2000);
          }
        } catch (error) {
          console.error('Erreur:', error);
        }
      });
      
      // Récupérer l'état du système
      async function fetchState() {
        try {
          const response = await fetch('/api/cognitive/state');
          const data = await response.json();
          if (data.success) {
            const stateOutput = document.getElementById('stateOutput');
            stateOutput.textContent = JSON.stringify(data.state, null, 2);
            
            // Mettre à jour les indicateurs d'état
            updateStatus('cognitiveStatus', data.state.isActive);
            updateStatus('speechStatus', data.state.isListening || data.state.isSpeaking);
            updateStatus('visionStatus', data.state.isObserving);
            
            if (data.state.lastObservation) {
              document.getElementById('visionOutput').textContent = 
                data.state.lastObservation.scene ? 
                `Scène: ${data.state.lastObservation.scene}\n${JSON.stringify(data.state.lastObservation, null, 2)}` : 
                JSON.stringify(data.state.lastObservation, null, 2);
            }
          }
        } catch (error) {
          console.error('Erreur:', error);
        }
      }
      
      // Mise à jour des indicateurs d'état
      function updateStatus(elementId, isActive) {
        const element = document.getElementById(elementId);
        const indicator = element.querySelector('.status-indicator');
        
        if (isActive) {
          indicator.classList.remove('status-inactive');
          indicator.classList.add('status-active');
          element.childNodes[1].nodeValue = ' Actif';
        } else {
          indicator.classList.remove('status-active');
          indicator.classList.add('status-inactive');
          element.childNodes[1].nodeValue = ' Inactif';
        }
      }
      
      // Vérifier l'état initial
      fetchState();
      
      // Actualiser l'état toutes les 5 secondes
      setInterval(fetchState, 5000);
    });
  </script>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
