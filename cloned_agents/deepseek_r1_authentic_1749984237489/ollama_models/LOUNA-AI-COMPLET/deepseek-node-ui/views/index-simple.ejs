<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %></title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github-dark.min.css">
  <link rel="stylesheet" href="/css/style.css">
  <style>
    body {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }
    #chat-messages {
      flex: 1;
      overflow-y: auto;
      padding: 1rem;
      height: calc(100vh - 200px);
    }
    .message {
      margin-bottom: 1rem;
      display: flex;
      align-items: flex-start;
    }
    .user-message {
      justify-content: flex-end;
    }
    .bot-message {
      justify-content: flex-start;
    }
    .system-message {
      justify-content: center;
    }
    .message-avatar {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #303030;
      margin-right: 10px;
    }
    .user-message .message-avatar {
      order: 2;
      margin-right: 0;
      margin-left: 10px;
      background-color: #0d6efd;
    }
    .message-content {
      background-color: #303030;
      padding: 10px 15px;
      border-radius: 10px;
      max-width: 70%;
    }
    .user-message .message-content {
      background-color: #0d6efd;
    }
    .system-message .message-content {
      background-color: #2c2c2c;
      max-width: 90%;
      text-align: center;
    }
    .typing-indicator {
      display: inline-flex;
      align-items: center;
    }
    .typing-indicator span {
      width: 8px;
      height: 8px;
      background-color: #fff;
      border-radius: 50%;
      margin: 0 2px;
      animation: typing 1s infinite;
    }
    .typing-indicator span:nth-child(2) {
      animation-delay: 0.2s;
    }
    .typing-indicator span:nth-child(3) {
      animation-delay: 0.4s;
    }
    @keyframes typing {
      0%, 100% { transform: translateY(0); }
      50% { transform: translateY(-5px); }
    }
    pre {
      background-color: #1e1e1e;
      padding: 0.5rem;
      border-radius: 5px;
      overflow-x: auto;
    }
    code {
      font-family: 'Courier New', monospace;
    }
  </style>
</head>
<body class="bg-dark text-light">
  <header class="border-bottom border-secondary mb-3">
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
      <div class="container-fluid">
        <a class="navbar-brand d-flex align-items-center" href="/">
          <i class="bi bi-robot fs-3 me-2"></i>
          <span>DeepSeek r1 Simplifié</span>
          <span class="badge bg-success ms-2">Local</span>
        </a>
        <div id="ollama-status" class="ms-auto">
          <span class="badge bg-warning">Vérification d'Ollama...</span>
        </div>
      </div>
    </nav>
  </header>

  <main class="container-fluid flex-grow-1 d-flex flex-column">
    <div class="row flex-grow-1">
      <div class="col-md-3 border-end border-secondary">
        <div class="p-3">
          <h4 class="mb-3">Paramètres</h4>
          <div class="mb-3">
            <label for="model-selector" class="form-label">Modèle</label>
            <select id="model-selector" class="form-select bg-dark text-light border-secondary">
              <option value="deepseek-r1:7b" selected>DeepSeek r1 7B</option>
            </select>
          </div>
          <div class="mb-3">
            <label for="temperature" class="form-label">Température: <span id="temperature-value">0.7</span></label>
            <input type="range" class="form-range" id="temperature" min="0" max="1" step="0.1" value="0.7">
          </div>
          <div class="mb-3">
            <label for="max-tokens" class="form-label">Max Tokens: <span id="max-tokens-value">1000</span></label>
            <input type="range" class="form-range" id="max-tokens" min="100" max="4000" step="100" value="1000">
          </div>
          <button id="check-status-btn" onclick="checkOllamaStatus()" class="btn btn-outline-primary w-100 mb-3">
            Vérifier Ollama
          </button>
        </div>
      </div>
      <div class="col-md-9 d-flex flex-column">
        <div id="current-chat-title" class="p-2 border-bottom border-secondary text-center">
          Nouvelle conversation
        </div>
        <div id="chat-messages" class="flex-grow-1"></div>
        <div class="p-3 border-top border-secondary">
          <form id="chat-form" class="d-flex">
            <input type="text" id="message-input" class="form-control bg-dark text-light border-secondary me-2" placeholder="Écrire un message...">
            <button type="submit" class="btn btn-primary">
              <i class="bi bi-send"></i>
            </button>
          </form>
        </div>
      </div>
    </div>
  </main>

  <footer class="border-top border-secondary py-3 text-center">
    <div class="container">
      <small class="text-muted">DeepSeek r1 Simplifié - Version locale</small>
    </div>
  </footer>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/4.3.0/marked.min.js"></script>
  <script src="/js/main-simple.js"></script>
</body>
</html>
