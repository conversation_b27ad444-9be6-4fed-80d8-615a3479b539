<%- include('luna-base', {
  title: 'Luna - Réflexion Améliorée',
  page: 'reflection'
}) %>
<link rel="stylesheet" href="/css/luna-reflection-enhanced.css">

<!-- Intégration du contenu principal Luna Réflexion Améliorée -->
<div class="container-fluid mt-4">
  <div class="row">
    <!-- Panneau principal de réflexion -->
    <div class="col-md-8">
      <div class="luna-card" style="height: 65vh; display: flex; flex-direction: column;">
        <h3 class="mb-3 d-flex align-items-center justify-content-between">
          <div><i class="bi bi-lightbulb me-2"></i> Réflexion Vision Ultra</div>
          <div>
            <span id="current-datetime" class="datetime-display me-3">
              <%= currentDate %>
            </span>
            <button id="settings-btn" class="btn btn-sm btn-luna" data-bs-toggle="modal" data-bs-target="#settings-modal">
              <i class="bi bi-gear me-1"></i> Paramètres
            </button>
          </div>
        </h3>

        <div class="reflection-container" style="flex-grow: 1; overflow-y: auto; margin-bottom: 1rem; padding: 1rem; border-radius: 10px; background-color: rgba(0,0,0,0.2); background-image: linear-gradient(to bottom, rgba(156, 137, 184, 0.05), rgba(240, 166, 202, 0.05));">
          <!-- Formulaire de réflexion -->
          <div class="reflection-form-container">
            <form id="reflection-form">
              <div class="mb-3">
                <label for="reflection-input" class="form-label">Entrez votre requête pour voir la réflexion de Vision Ultra</label>
                <textarea id="reflection-input" class="form-control reflection-input" rows="3" placeholder="Posez une question ou entrez un sujet..."></textarea>
              </div>
              <div class="d-flex justify-content-between">
                <button type="submit" class="btn btn-luna">
                  <i class="bi bi-lightbulb me-1"></i> Générer une réflexion
                </button>
                <div>
                  <span class="me-2">
                    <i class="bi bi-globe me-1"></i> Internet:
                    <span class="status-indicator active" id="internet-status"></span>
                  </span>
                  <span class="me-2">
                    <i class="bi bi-hdd me-1"></i> Mémoire thermique:
                    <span class="status-indicator active" id="memory-status"></span>
                  </span>
                  <span>
                    <i class="bi bi-lightning me-1"></i> Accélérateurs:
                    <span class="status-indicator active" id="accelerators-status"></span>
                  </span>
                </div>
              </div>
            </form>
          </div>

          <!-- Conteneur de réflexion -->
          <div id="reflection-container" class="mt-4">
            <div class="text-center p-5">
              <i class="bi bi-lightbulb fs-1 mb-3" style="opacity: 0.5;"></i>
              <h5>Réflexion Vision Ultra</h5>
              <p class="text-muted">Entrez une requête ci-dessus pour voir le processus de réflexion de Vision Ultra.</p>
              <p class="text-muted">La réflexion est accélérée par les accélérateurs Kyber et enrichie par la mémoire thermique.</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Panneau latéral avec l'historique et les statistiques -->
    <div class="col-md-4">
      <!-- Historique de réflexion -->
      <div class="luna-card mb-3">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h4 class="mb-0"><i class="bi bi-clock-history me-2"></i> Historique</h4>
          <button id="clear-history-btn" class="btn btn-sm btn-luna-outline">
            <i class="bi bi-trash me-1"></i> Effacer
          </button>
        </div>
        <div id="reflection-history" class="reflection-history-container">
          <div class="text-center text-muted p-4">
            <i class="bi bi-clock-history fs-3 mb-2"></i>
            <p>Aucune réflexion dans l'historique</p>
          </div>
        </div>
      </div>

      <!-- Statistiques des accélérateurs -->
      <div class="luna-card mb-3">
        <h4><i class="bi bi-lightning me-2"></i> Accélérateurs Kyber</h4>
        <div class="mb-3">
          <div class="d-flex justify-content-between mb-1">
            <span>Réflexion</span>
            <span id="reflection-accelerator-value">90%</span>
          </div>
          <div class="progress mb-3" style="height: 8px;">
            <div id="reflection-accelerator-bar" class="progress-bar" role="progressbar" style="width: 90%;" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100"></div>
          </div>

          <div class="d-flex justify-content-between mb-1">
            <span>Mémoire</span>
            <span id="memory-accelerator-value">85%</span>
          </div>
          <div class="progress mb-3" style="height: 8px;">
            <div id="memory-accelerator-bar" class="progress-bar" role="progressbar" style="width: 85%;" aria-valuenow="85" aria-valuemin="0" aria-valuemax="100"></div>
          </div>

          <div class="d-flex justify-content-between mb-1">
            <span>Thermique</span>
            <span id="thermal-accelerator-value">80%</span>
          </div>
          <div class="progress mb-3" style="height: 8px;">
            <div id="thermal-accelerator-bar" class="progress-bar" role="progressbar" style="width: 80%;" aria-valuenow="80" aria-valuemin="0" aria-valuemax="100"></div>
          </div>

          <div class="d-flex justify-content-between mb-1">
            <span>Formation</span>
            <span id="training-accelerator-value">95%</span>
          </div>
          <div class="progress" style="height: 8px;">
            <div id="training-accelerator-bar" class="progress-bar" role="progressbar" style="width: 95%;" aria-valuenow="95" aria-valuemin="0" aria-valuemax="100"></div>
          </div>
        </div>
      </div>

      <!-- Statistiques de la mémoire thermique -->
      <div class="luna-card">
        <h4><i class="bi bi-thermometer-half me-2"></i> Mémoire thermique</h4>
        <div class="mb-3">
          <div class="d-flex justify-content-between mb-1">
            <span>Zone active</span>
            <span id="active-zone">Zone 3 (60°C)</span>
          </div>
          <div class="progress mb-3" style="height: 8px;">
            <div id="active-zone-bar" class="progress-bar" role="progressbar" style="width: 60%;" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100"></div>
          </div>

          <div class="d-flex justify-content-between mb-1">
            <span>Température</span>
            <span id="temperature-value">42°C</span>
          </div>
          <div class="progress mb-3" style="height: 8px;">
            <div id="temperature-bar" class="progress-bar" role="progressbar" style="width: 42%;" aria-valuenow="42" aria-valuemin="0" aria-valuemax="100"></div>
          </div>

          <div class="d-flex justify-content-between mb-1">
            <span>Compression</span>
            <span id="compression-value">85%</span>
          </div>
          <div class="progress" style="height: 8px;">
            <div id="compression-bar" class="progress-bar" role="progressbar" style="width: 85%;" aria-valuenow="85" aria-valuemin="0" aria-valuemax="100"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal des paramètres -->
<div class="modal fade" id="settings-modal" tabindex="-1" aria-labelledby="settings-modal-label" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content bg-dark text-light">
      <div class="modal-header">
        <h5 class="modal-title" id="settings-modal-label">Paramètres de réflexion</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fermer"></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
          <label class="form-label">Affichage de la réflexion</label>
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="show-reflection-toggle" checked>
            <label class="form-check-label" for="show-reflection-toggle">
              Afficher la réflexion
            </label>
          </div>
        </div>
        <div class="mb-3">
          <label class="form-label">Traduction automatique</label>
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="auto-translate-toggle" checked>
            <label class="form-check-label" for="auto-translate-toggle">
              Traduire en français
            </label>
          </div>
        </div>
        <div class="mb-3">
          <label class="form-label">Style d'affichage</label>
          <div class="display-style-options">
            <div class="display-style-option active" id="display-style-collapsed" data-style="collapsed">
              <i class="bi bi-arrows-collapse"></i> Masqué
            </div>
            <div class="display-style-option" id="display-style-expanded" data-style="expanded">
              <i class="bi bi-arrows-expand"></i> Affiché
            </div>
            <div class="display-style-option" id="display-style-inline" data-style="inline">
              <i class="bi bi-text-paragraph"></i> Intégré
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
        <button type="button" class="btn btn-luna" id="save-settings-btn">Enregistrer</button>
      </div>
    </div>
  </div>
</div>

<!-- Modal des détails de réflexion -->
<div class="modal fade" id="reflection-details-modal" tabindex="-1" aria-labelledby="reflection-details-modal-label" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content bg-dark text-light">
      <div class="modal-header">
        <h5 class="modal-title" id="reflection-details-modal-label">Détails de la réflexion</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fermer"></button>
      </div>
      <div class="modal-body">
        <!-- Le contenu sera injecté dynamiquement -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
      </div>
    </div>
  </div>
</div>

<!-- Conteneur de toasts -->
<div class="toast-container"></div>

<script src="/js/luna-reflection-enhanced.js"></script>
