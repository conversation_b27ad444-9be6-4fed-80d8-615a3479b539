<!-- Intégration du contenu principal Luna -->
<div class="row mt-4">
  <!-- Panneau de conversation principal -->
  <div class="col-md-8">
    <div class="luna-card" style="height: 65vh; display: flex; flex-direction: column;">
      <h3 class="mb-3 d-flex align-items-center justify-content-between">
        <div><i class="bi bi-chat-square-text me-2"></i> Conversation avec Vision Ultra</div>
        <div>
          <span id="agentStatus" class="me-3">
            <span class="status-indicator status-inactive"></span>
            <span>En attente</span>
          </span>
        </div>
      </h3>

      <div id="conversation-container" style="flex-grow: 1; overflow-y: auto; margin-bottom: 1rem; padding: 1rem; border-radius: 10px; background-color: rgba(0,0,0,0.2); background-image: linear-gradient(to bottom, rgba(156, 137, 184, 0.05), rgba(240, 166, 202, 0.05));">
        <!-- Message de bienvenue initial -->
        <div class="message agent-message">
          <div class="message-avatar agent-avatar"><i class="bi bi-robot"></i></div>
          <div class="message-bubble agent-bubble">
            <button class="copy-button" onclick="copyMessageContent(this)" title="Copier le message">
              <i class="bi bi-clipboard"></i>
            </button>
            <div class="message-content">
              <p>Bienvenue dans l'interface Luna. Je suis Vision Ultra, votre assistant cognitif créé par Jean Passave à Sainte-Anne, Guadeloupe (97180).</p>
              <p>Nous sommes le <span class="current-date-inline"><%=new Date().toLocaleDateString('fr-FR', {weekday: 'long', year: 'numeric', month: 'long', day: 'numeric'})%></span> et il est <span class="current-time-inline"><%=new Date().toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit', second: '2-digit'})%></span>.</p>
              <p>Comment puis-je vous aider aujourd'hui ?</p>
            </div>
            <div class="message-timestamp"><%=new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})%></div>
          </div>
        </div>

        <!-- Indicateur de connexion -->
        <div id="connection-status" class="message system-message" style="display: none;">
          <div class="message-avatar system-avatar"><i class="bi bi-info-circle"></i></div>
          <div class="message-bubble system-bubble">
            <div class="message-content">
              <p><i class="bi bi-cpu me-2"></i> Connexion à Ollama en cours d'établissement...</p>
            </div>
          </div>
        </div>
      </div>

      <div class="input-group">
        <button id="prompt-selector-btn" class="btn btn-luna-outline" style="border-radius: 25px 0 0 25px;">
          <i class="bi bi-lightning"></i>
        </button>
        <button id="mic-btn" class="btn btn-luna-outline" style="border-radius: 0;" title="Activer le microphone" onclick="toggleMicrophone()">
          <i class="bi bi-mic"></i>
        </button>
        <button id="speaker-btn" class="btn btn-luna-outline" style="border-radius: 0;" title="Activer la synthèse vocale" onclick="toggleSpeech()">
          <i class="bi bi-volume-up"></i>
        </button>
        <button id="camera-btn" class="btn btn-luna-outline" style="border-radius: 0;" title="Activer la caméra" onclick="toggleCamera()">
          <i class="bi bi-camera"></i>
        </button>
        <input type="text" id="user-input" class="form-control" placeholder="Écrivez votre message ici..."
               style="border-radius: 0; padding: 0.75rem 1.5rem; background: rgba(255,255,255,0.1); border-color: var(--luna-accent); color: white;">
        <button id="send-button" class="btn btn-luna" style="border-radius: 0 25px 25px 0;">
          <i class="bi bi-send"></i> Envoyer
        </button>
      </div>

      <!-- Sélecteur de prompts (masqué par défaut) -->
      <div id="prompt-selector" class="mt-3" style="display: none;">
        <div class="card bg-dark">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Prompts rapides</h5>
            <button type="button" class="btn-close btn-close-white" id="close-prompt-selector"></button>
          </div>
          <div class="card-body" style="max-height: 300px; overflow-y: auto;">
            <div id="quick-prompts-container">
              <!-- Les prompts favoris seront chargés ici -->
              <div class="text-center py-3 text-muted" id="no-quick-prompts">
                <p>Aucun prompt favori disponible. Visitez la <a href="/luna/prompts">bibliothèque de prompts</a> pour en ajouter.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Panneau latéral avec les fonctions et les capteurs -->
  <div class="col-md-4">
    <!-- Sélection de modèle -->
    <div class="luna-card mb-3">
      <h4><i class="bi bi-cpu me-2"></i> Modèle IA</h4>
      <div class="mb-3">
        <label for="model-select" class="form-label">Modèle actif</label>
        <select id="model-select" class="form-select" aria-label="Sélection du modèle">
          <!-- Les modèles seront ajoutés dynamiquement ici -->
          <option value="incept5/llama3.1-claude:latest" selected>Claude (par défaut)</option>
        </select>
      </div>
      <div class="d-flex justify-content-between">
        <button id="refresh-models" class="btn btn-sm btn-luna-outline">
          <i class="bi bi-arrow-clockwise me-1"></i> Actualiser
        </button>
        <button id="install-model" class="btn btn-sm btn-luna">
          <i class="bi bi-download me-1"></i> Installer un modèle
        </button>
      </div>
      <div id="model-modal" class="modal fade" tabindex="-1" aria-labelledby="modelModalLabel" aria-hidden="true">
        <div class="modal-dialog">
          <div class="modal-content bg-dark">
            <div class="modal-header">
              <h5 class="modal-title" id="modelModalLabel">Installer un modèle</h5>
              <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fermer"></button>
            </div>
            <div class="modal-body">
              <div class="mb-3">
                <label for="model-name-input" class="form-label">Nom du modèle</label>
                <input type="text" class="form-control" id="model-name-input" placeholder="Exemple: llama2:7b">
                <div class="form-text text-muted">Exemples : llama2:7b, mistral:7b, phi-2:mini</div>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" id="cancel-install" data-bs-dismiss="modal">Annuler</button>
              <button type="button" class="btn btn-primary" id="confirm-install">Installer</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Périphériques -->
    <div class="luna-card mb-3">
      <h4><i class="bi bi-hdd-network me-2"></i> Périphériques</h4>
      <div class="mb-3">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span title="Microphone"><i class="bi bi-mic me-2"></i> Microphone</span>
          <div>
            <span id="microphoneStatus">
              <span class="status-indicator status-inactive"></span>
            </span>
            <button id="toggleMicrophoneBtn" class="btn btn-sm btn-luna" onclick="toggleMicrophone()">
              <i class="bi bi-mic"></i>
            </button>
          </div>
        </div>
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span title="Caméra"><i class="bi bi-camera me-2"></i> Caméra</span>
          <div>
            <span id="visionStatus">
              <span class="status-indicator status-inactive"></span>
            </span>
            <button id="toggleVisionBtn" class="btn btn-sm btn-luna" onclick="toggleCamera()">
              <i class="bi bi-camera"></i>
            </button>
          </div>
        </div>
        <div class="d-flex justify-content-between align-items-center">
          <span><i class="bi bi-volume-up me-2"></i> Synthèse vocale</span>
          <div>
            <span id="speechStatus">
              <span class="status-indicator status-inactive"></span>
            </span>
            <button id="testSpeechBtn" class="btn btn-sm btn-luna" onclick="toggleSpeech()">
              <i class="bi bi-chat-quote"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Carte de la mémoire thermique -->
    <div class="luna-card mb-3">
      <h4><i class="bi bi-hdd-network me-2"></i> Mémoire Thermique</h4>
      <div id="memory-status" class="mb-2" style="font-size: 0.9rem;">
        <div><strong>État:</strong> <span id="memory-state">Optimale</span></div>
        <div><strong>Capacité:</strong> <span id="memory-capacity">94%</span></div>
        <div><strong>Zones actives:</strong> <span id="memory-zones">6/6</span></div>
      </div>
      <div class="progress" style="height: 15px; margin-bottom: 10px; background: rgba(0,0,0,0.2);">
        <div id="memory-progress" class="progress-bar" role="progressbar" style="width: 64%; background: var(--luna-gradient);" aria-valuenow="64" aria-valuemin="0" aria-valuemax="100">64%</div>
      </div>
      <div class="d-flex gap-2 justify-content-between">
        <button id="viewMemoryBtn" class="btn btn-sm btn-luna-outline w-100">
          <i class="bi bi-eye me-1"></i> Voir la mémoire
        </button>
      </div>
    </div>

    <!-- Carte de formation -->
    <div class="luna-card mb-3">
      <h4><i class="bi bi-graduation-cap me-2"></i> Formation IA</h4>
      <div class="mb-2">
        <p>Améliorez les capacités de votre assistant en lançant des sessions de formation.</p>
        <div class="d-flex justify-content-between">
          <button id="trainingBtn" class="btn btn-luna" onclick="window.location.href='/luna/training'">
            <i class="bi bi-lightning-charge me-1"></i> Accéder à la formation
          </button>
        </div>
      </div>
    </div>

    <!-- Carte MCP -->
    <div class="luna-card">
      <h4><i class="bi bi-cpu me-2"></i> Système MCP</h4>
      <div class="mb-2">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span title="Accès Internet"><i class="bi bi-globe me-2"></i> Internet</span>
          <span id="internetStatus">
            <span class="status-indicator status-active"></span>
          </span>
        </div>
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span title="Accès aux fichiers système"><i class="bi bi-folder me-2"></i> Système</span>
          <span id="filesystemStatus">
            <span class="status-indicator status-active"></span>
          </span>
        </div>
        <div class="d-flex justify-content-between align-items-center">
          <span title="Accès aux ressources matérielles"><i class="bi bi-pc-display me-2"></i> Matériel</span>
          <span id="hardwareStatus">
            <span class="status-indicator status-active"></span>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Présence du cerveau -->
<div class="row mt-4">
  <div class="col-md-12">
    <%- include('partials/brain-presence') %>
  </div>
</div>

<!-- Barre de statut et statistiques -->
<div class="row mt-4">
  <div class="col-md-12">
    <div class="luna-card p-3">
      <div class="row">
        <div class="col-md-3">
          <div class="d-flex align-items-center">
            <i class="bi bi-cpu me-3" style="font-size: 2rem; color: var(--luna-primary);"></i>
            <div>
              <div class="text-muted">Processeur</div>
              <div class="h5 mb-0" id="cpu-usage">42%</div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="d-flex align-items-center">
            <i class="bi bi-memory me-3" style="font-size: 2rem; color: var(--luna-primary);"></i>
            <div>
              <div class="text-muted">Mémoire</div>
              <div class="h5 mb-0" id="ram-usage">2.4 GB</div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="d-flex align-items-center">
            <i class="bi bi-activity me-3" style="font-size: 2rem; color: var(--luna-accent);"></i>
            <div>
              <div class="text-muted">Latence</div>
              <div class="h5 mb-0" id="latency">24ms</div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="d-flex align-items-center">
            <i class="bi bi-thermometer-half me-3" style="font-size: 2rem; color: var(--luna-secondary);"></i>
            <div>
              <div class="text-muted">Temp. Mémoire</div>
              <div class="h5 mb-0" id="thermal-index">72°C</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<link rel="stylesheet" href="/css/luna-chat.css">
<link rel="stylesheet" href="/css/brain-presence.css">

<script src="/js/luna-interface.js"></script>
<script src="/js/luna-peripherals.js"></script>
<script src="/js/brain-presence-ui.js"></script>

<script>
  // Fonction pour copier le contenu d'un message
  function copyMessageContent(button) {
    // Trouver le conteneur du message
    const messageBubble = button.closest('.message-bubble');
    const messageContent = messageBubble.querySelector('.message-content');

    // Extraire le texte du message
    const textToCopy = messageContent.innerText || messageContent.textContent;

    // Copier le texte dans le presse-papier
    navigator.clipboard.writeText(textToCopy)
      .then(() => {
        // Changer l'icône et la classe pour indiquer que le texte a été copié
        button.innerHTML = '<i class="bi bi-check"></i>';
        button.classList.add('copied');

        // Rétablir l'icône après 2 secondes
        setTimeout(() => {
          button.innerHTML = '<i class="bi bi-clipboard"></i>';
          button.classList.remove('copied');
        }, 2000);
      })
      .catch(err => {
        console.error('Erreur lors de la copie du texte:', err);
        // Afficher une notification d'erreur
        showNotification('Impossible de copier le texte', 'error');
      });
  }

  // Ajouter un bouton de copie à chaque message de l'agent
  function addCopyButtonToAgentMessages() {
    // Sélectionner tous les messages de l'agent qui n'ont pas encore de bouton de copie
    const agentBubbles = document.querySelectorAll('.agent-bubble:not(:has(.copy-button))');

    agentBubbles.forEach(bubble => {
      // Créer le bouton de copie
      const copyButton = document.createElement('button');
      copyButton.className = 'copy-button';
      copyButton.setAttribute('onclick', 'copyMessageContent(this)');
      copyButton.setAttribute('title', 'Copier le message');
      copyButton.innerHTML = '<i class="bi bi-clipboard"></i>';

      // Ajouter le bouton au début de la bulle de message
      bubble.insertBefore(copyButton, bubble.firstChild);
    });
  }

  // Observer les nouveaux messages pour ajouter des boutons de copie
  const conversationObserver = new MutationObserver(mutations => {
    mutations.forEach(mutation => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        // Vérifier si de nouveaux messages ont été ajoutés
        addCopyButtonToAgentMessages();
      }
    });
  });

  // Démarrer l'observation du conteneur de conversation
  document.addEventListener('DOMContentLoaded', function() {
    const conversationContainer = document.getElementById('conversation-container');
    if (conversationContainer) {
      conversationObserver.observe(conversationContainer, { childList: true, subtree: true });

      // Ajouter des boutons de copie aux messages existants
      addCopyButtonToAgentMessages();

      // Activer automatiquement les périphériques
      setTimeout(function() {
        // Activer le microphone
        if (typeof toggleMicrophone === 'function') {
          toggleMicrophone(true);
        }

        // Activer la caméra
        if (typeof toggleCamera === 'function') {
          toggleCamera(true);
        }

        // Activer la synthèse vocale
        if (typeof toggleSpeech === 'function') {
          toggleSpeech(true);
        }
      }, 2000); // Délai de 2 secondes pour laisser le temps à la page de se charger complètement
    }
  });
</script>
