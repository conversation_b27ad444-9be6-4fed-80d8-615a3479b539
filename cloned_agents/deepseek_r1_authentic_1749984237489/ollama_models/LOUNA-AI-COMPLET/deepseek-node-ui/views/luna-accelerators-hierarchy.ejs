<%- include('luna-base') %>
<link rel="stylesheet" href="/css/luna-accelerators.css">

<!-- Intégration du contenu principal Luna Accélérateurs -->
<div class="row mt-4">
  <!-- Panneau principal des accélérateurs -->
  <div class="col-md-8">
    <div class="luna-card" style="height: 65vh; display: flex; flex-direction: column;">
      <h3 class="mb-3 d-flex align-items-center justify-content-between">
        <div><i class="bi bi-lightning-charge me-2"></i> Accélérateurs Kyber</div>
        <div>
          <button id="view-list-btn" class="btn btn-sm btn-luna-outline me-2">
            <i class="bi bi-list me-1"></i> Vue Liste
          </button>
          <button id="view-hierarchy-btn" class="btn btn-sm btn-luna me-2">
            <i class="bi bi-diagram-3 me-1"></i> Vue Hiérarchie
          </button>
          <button id="accelerators-settings-btn" class="btn btn-sm btn-luna">
            <i class="bi bi-gear me-1"></i> Paramètres
          </button>
        </div>
      </h3>

      <div id="accelerators-container" style="flex-grow: 1; overflow-y: auto; margin-bottom: 1rem; padding: 1rem; border-radius: 10px; background-color: rgba(0,0,0,0.2); background-image: linear-gradient(to bottom, rgba(156, 137, 184, 0.05), rgba(240, 166, 202, 0.05)); position: relative;">
        <!-- Vue en liste des accélérateurs (cachée par défaut) -->
        <div id="accelerators-list-view" style="display: none;">
          <!-- Tableau des accélérateurs -->
          <div class="table-responsive">
            <table class="table table-dark table-hover accelerator-table">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Type</th>
                  <th>Statut</th>
                  <th>Efficacité</th>
                  <th>Débit</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody id="accelerators-list">
                <!-- Les accélérateurs seront chargés ici dynamiquement -->
                <tr class="accelerator-row" data-id="kyber-memory-1">
                  <td>kyber-memory-1</td>
                  <td><span class="badge bg-primary">Mémoire</span></td>
                  <td><span class="status-indicator status-active"></span> Actif</td>
                  <td>
                    <div class="progress">
                      <div class="progress-bar" role="progressbar" style="width: 75%;" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100">75%</div>
                    </div>
                  </td>
                  <td>850 MB/s</td>
                  <td>
                    <button class="btn btn-sm btn-outline-secondary me-1 toggle-accelerator-btn">
                      <i class="bi bi-power"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info me-1 optimize-accelerator-btn">
                      <i class="bi bi-lightning-charge"></i>
                    </button>
                  </td>
                </tr>
                <tr class="accelerator-row" data-id="kyber-thermal-1">
                  <td>kyber-thermal-1</td>
                  <td><span class="badge bg-info">Thermique</span></td>
                  <td><span class="status-indicator status-active"></span> Actif</td>
                  <td>
                    <div class="progress">
                      <div class="progress-bar" role="progressbar" style="width: 82%;" aria-valuenow="82" aria-valuemin="0" aria-valuemax="100">82%</div>
                    </div>
                  </td>
                  <td>920 MB/s</td>
                  <td>
                    <button class="btn btn-sm btn-outline-secondary me-1 toggle-accelerator-btn">
                      <i class="bi bi-power"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info me-1 optimize-accelerator-btn">
                      <i class="bi bi-lightning-charge"></i>
                    </button>
                  </td>
                </tr>
                <tr class="accelerator-row" data-id="kyber-reflection-1">
                  <td>kyber-reflection-1</td>
                  <td><span class="badge bg-warning">Réflexion</span></td>
                  <td><span class="status-indicator status-active"></span> Actif</td>
                  <td>
                    <div class="progress">
                      <div class="progress-bar" role="progressbar" style="width: 95%;" aria-valuenow="95" aria-valuemin="0" aria-valuemax="100">95%</div>
                    </div>
                  </td>
                  <td>1250 MB/s</td>
                  <td>
                    <button class="btn btn-sm btn-outline-secondary me-1 toggle-accelerator-btn">
                      <i class="bi bi-power"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info me-1 optimize-accelerator-btn">
                      <i class="bi bi-lightning-charge"></i>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Vue hiérarchique des accélérateurs (visible par défaut) -->
        <div id="accelerators-hierarchy-view" class="accelerators-hierarchy">
          <!-- Chef d'orchestre principal -->
          <div class="master-conductor">
            <div class="conductor-card">
              <div class="conductor-header">
                <h4>Chef d'Orchestre Principal</h4>
                <div class="conductor-status">
                  <span class="status-indicator status-active pulse"></span>
                  <span>Actif</span>
                </div>
              </div>
              <div class="conductor-body">
                <div class="conductor-stats">
                  <div class="stat-item">
                    <span>Efficacité globale</span>
                    <span id="master-efficiency">187.5%</span>
                  </div>
                  <div class="stat-item">
                    <span>Accélérateurs actifs</span>
                    <span id="active-accelerators">16/16</span>
                  </div>
                  <div class="stat-item">
                    <span>Température système</span>
                    <span id="system-temp">42.3°C</span>
                  </div>
                </div>
                <div class="conductor-meter">
                  <div class="meter-label">Puissance</div>
                  <div class="meter-bar">
                    <div class="meter-fill" style="width: 87%;"></div>
                  </div>
                  <div class="meter-value">87%</div>
                </div>
              </div>
            </div>

            <!-- Connexions vers les chefs de zone -->
            <div class="conductor-connections">
              <div class="connection-line" id="connection-to-reflection"></div>
              <div class="connection-line" id="connection-to-memory"></div>
              <div class="connection-line" id="connection-to-processing"></div>
              <div class="connection-line" id="connection-to-creativity"></div>
            </div>
          </div>

          <!-- Chefs d'orchestre de zone -->
          <div class="zone-conductors">
            <!-- Zone 1: Réflexion -->
            <div class="zone-conductor" data-zone="reflection">
              <div class="conductor-card">
                <div class="conductor-header">
                  <h5>Chef Zone Réflexion</h5>
                  <div class="conductor-status">
                    <span class="status-indicator status-active pulse"></span>
                    <span>Actif</span>
                  </div>
                </div>
                <div class="conductor-body">
                  <div class="conductor-stats">
                    <div class="stat-item">
                      <span>Efficacité</span>
                      <span id="reflection-efficiency">192.3%</span>
                    </div>
                    <div class="stat-item">
                      <span>Accélérateurs</span>
                      <span id="reflection-count">4</span>
                    </div>
                  </div>
                  <div class="conductor-meter">
                    <div class="meter-bar">
                      <div class="meter-fill" style="width: 92%;"></div>
                    </div>
                    <div class="meter-value">92%</div>
                  </div>
                </div>
              </div>
              <div class="accelerator-group" id="reflection-accelerators">
                <!-- Les accélérateurs seront ajoutés dynamiquement ici -->
              </div>
            </div>

            <!-- Zone 2: Mémoire -->
            <div class="zone-conductor" data-zone="memory">
              <div class="conductor-card">
                <div class="conductor-header">
                  <h5>Chef Zone Mémoire</h5>
                  <div class="conductor-status">
                    <span class="status-indicator status-active pulse"></span>
                    <span>Actif</span>
                  </div>
                </div>
                <div class="conductor-body">
                  <div class="conductor-stats">
                    <div class="stat-item">
                      <span>Efficacité</span>
                      <span id="memory-efficiency">178.6%</span>
                    </div>
                    <div class="stat-item">
                      <span>Accélérateurs</span>
                      <span id="memory-count">4</span>
                    </div>
                  </div>
                  <div class="conductor-meter">
                    <div class="meter-bar">
                      <div class="meter-fill" style="width: 85%;"></div>
                    </div>
                    <div class="meter-value">85%</div>
                  </div>
                </div>
              </div>
              <div class="accelerator-group" id="memory-accelerators">
                <!-- Les accélérateurs seront ajoutés dynamiquement ici -->
              </div>
            </div>

            <!-- Zone 3: Traitement -->
            <div class="zone-conductor" data-zone="processing">
              <div class="conductor-card">
                <div class="conductor-header">
                  <h5>Chef Zone Traitement</h5>
                  <div class="conductor-status">
                    <span class="status-indicator status-active pulse"></span>
                    <span>Actif</span>
                  </div>
                </div>
                <div class="conductor-body">
                  <div class="conductor-stats">
                    <div class="stat-item">
                      <span>Efficacité</span>
                      <span id="processing-efficiency">201.4%</span>
                    </div>
                    <div class="stat-item">
                      <span>Accélérateurs</span>
                      <span id="processing-count">4</span>
                    </div>
                  </div>
                  <div class="conductor-meter">
                    <div class="meter-bar">
                      <div class="meter-fill" style="width: 95%;"></div>
                    </div>
                    <div class="meter-value">95%</div>
                  </div>
                </div>
              </div>
              <div class="accelerator-group" id="processing-accelerators">
                <!-- Les accélérateurs seront ajoutés dynamiquement ici -->
              </div>
            </div>

            <!-- Zone 4: Créativité -->
            <div class="zone-conductor" data-zone="creativity">
              <div class="conductor-card">
                <div class="conductor-header">
                  <h5>Chef Zone Créativité</h5>
                  <div class="conductor-status">
                    <span class="status-indicator status-active pulse"></span>
                    <span>Actif</span>
                  </div>
                </div>
                <div class="conductor-body">
                  <div class="conductor-stats">
                    <div class="stat-item">
                      <span>Efficacité</span>
                      <span id="creativity-efficiency">167.8%</span>
                    </div>
                    <div class="stat-item">
                      <span>Accélérateurs</span>
                      <span id="creativity-count">4</span>
                    </div>
                  </div>
                  <div class="conductor-meter">
                    <div class="meter-bar">
                      <div class="meter-fill" style="width: 78%;"></div>
                    </div>
                    <div class="meter-value">78%</div>
                  </div>
                </div>
              </div>
              <div class="accelerator-group" id="creativity-accelerators">
                <!-- Les accélérateurs seront ajoutés dynamiquement ici -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Panneau de flux de données -->
    <div class="luna-card mt-3">
      <h4><i class="bi bi-activity me-2"></i> Flux de données en temps réel</h4>
      <div class="data-flow-container">
        <canvas id="data-flow-chart" height="150"></canvas>
      </div>
      <div class="d-flex justify-content-between mt-2">
        <div class="small text-muted">Débit: <span id="data-throughput">1.28 GB/s</span></div>
        <div class="small text-muted">Latence: <span id="data-latency">12.4 ms</span></div>
        <div class="small text-muted">Efficacité: <span id="data-efficiency">187.5%</span></div>
      </div>
    </div>

    <!-- Panneau d'activité en temps réel -->
    <div class="luna-card mt-3">
      <h4><i class="bi bi-bar-chart-steps me-2"></i> Activité en temps réel</h4>
      <div class="row">
        <div class="col-md-6">
          <div class="mb-3">
            <div class="d-flex justify-content-between align-items-center mb-1">
              <span>Traitement des données</span>
              <span id="data-processing-value">78%</span>
            </div>
            <div class="progress" style="height: 10px;">
              <div id="data-processing-bar" class="progress-bar progress-bar-striped progress-bar-animated bg-info" role="progressbar" style="width: 78%"></div>
            </div>
          </div>
          <div class="mb-3">
            <div class="d-flex justify-content-between align-items-center mb-1">
              <span>Synchronisation neurale</span>
              <span id="neural-sync-value">92%</span>
            </div>
            <div class="progress" style="height: 10px;">
              <div id="neural-sync-bar" class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" style="width: 92%"></div>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <div class="d-flex justify-content-between align-items-center mb-1">
              <span>Transfert thermique</span>
              <span id="thermal-transfer-value">65%</span>
            </div>
            <div class="progress" style="height: 10px;">
              <div id="thermal-transfer-bar" class="progress-bar progress-bar-striped progress-bar-animated bg-warning" role="progressbar" style="width: 65%"></div>
            </div>
          </div>
          <div class="mb-3">
            <div class="d-flex justify-content-between align-items-center mb-1">
              <span>Optimisation cascade</span>
              <span id="cascade-optimization-value">87%</span>
            </div>
            <div class="progress" style="height: 10px;">
              <div id="cascade-optimization-bar" class="progress-bar progress-bar-striped progress-bar-animated bg-primary" role="progressbar" style="width: 87%"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="text-center mt-2">
        <span class="badge bg-info me-2">16 accélérateurs actifs</span>
        <span class="badge bg-success me-2">4 zones synchronisées</span>
        <span class="badge bg-warning me-2">Température: 42.3°C</span>
        <span class="badge bg-primary">Efficacité: 187.5%</span>
      </div>
    </div>
  </div>

  <!-- Panneau latéral avec les contrôles et statistiques -->
  <div class="col-md-4">
    <!-- Contrôles des accélérateurs -->
    <div class="luna-card mb-3">
      <h4><i class="bi bi-sliders me-2"></i> Contrôles</h4>
      <div class="mb-3">
        <button id="optimize-accelerators-btn" class="btn btn-luna w-100 mb-2">
          <i class="bi bi-lightning-charge me-1"></i> Optimiser tous les accélérateurs
        </button>
        <button id="balance-load-btn" class="btn btn-luna-outline w-100 mb-2">
          <i class="bi bi-arrow-repeat me-1"></i> Équilibrer la charge
        </button>
        <button id="reset-accelerators-btn" class="btn btn-danger w-100">
          <i class="bi bi-arrow-counterclockwise me-1"></i> Réinitialiser les accélérateurs
        </button>
      </div>

      <div class="mt-4">
        <h5 class="mb-3">Paramètres globaux</h5>

        <div class="mb-3">
          <label for="master-power" class="form-label d-flex justify-content-between">
            <span>Puissance principale</span>
            <span id="master-power-value">87%</span>
          </label>
          <input type="range" class="form-range" id="master-power" min="50" max="100" value="87">
        </div>

        <div class="mb-3">
          <label for="thermal-threshold" class="form-label d-flex justify-content-between">
            <span>Seuil thermique</span>
            <span id="thermal-threshold-value">75°C</span>
          </label>
          <input type="range" class="form-range" id="thermal-threshold" min="50" max="95" value="75">
        </div>

        <div class="mb-3">
          <label for="cascade-depth" class="form-label d-flex justify-content-between">
            <span>Profondeur de cascade</span>
            <span id="cascade-depth-value">3</span>
          </label>
          <input type="range" class="form-range" id="cascade-depth" min="1" max="5" value="3">
        </div>
      </div>
    </div>

    <!-- Statistiques des accélérateurs -->
    <div class="luna-card">
      <h4><i class="bi bi-bar-chart me-2"></i> Statistiques</h4>

      <div class="mb-3">
        <h6 class="mb-2">Performance par zone</h6>
        <canvas id="zone-performance-chart" height="200"></canvas>
      </div>

      <div class="mb-3">
        <h6 class="mb-2">Évolution de l'efficacité</h6>
        <div class="d-flex justify-content-between align-items-center mb-1">
          <span>Dernière heure</span>
          <span id="efficiency-hour">+2.3%</span>
        </div>
        <div class="progress mb-2" style="height: 8px;">
          <div class="progress-bar bg-success" role="progressbar" style="width: 65%;" aria-valuenow="65" aria-valuemin="0" aria-valuemax="100"></div>
        </div>

        <div class="d-flex justify-content-between align-items-center mb-1">
          <span>Dernières 24 heures</span>
          <span id="efficiency-day">+8.7%</span>
        </div>
        <div class="progress mb-2" style="height: 8px;">
          <div class="progress-bar bg-info" role="progressbar" style="width: 78%;" aria-valuenow="78" aria-valuemin="0" aria-valuemax="100"></div>
        </div>

        <div class="d-flex justify-content-between align-items-center mb-1">
          <span>Dernière semaine</span>
          <span id="efficiency-week">+15.2%</span>
        </div>
        <div class="progress" style="height: 8px;">
          <div class="progress-bar bg-primary" role="progressbar" style="width: 85%;" aria-valuenow="85" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
      </div>

      <div class="mt-3 pt-2 border-top border-secondary">
        <h6 class="mb-2">Statistiques avancées</h6>
        <div class="d-flex justify-content-between small">
          <div>
            <div>Cycles d'optimisation</div>
            <div class="h5 mb-0">1,284</div>
          </div>
          <div>
            <div>Connexions neurales</div>
            <div class="h5 mb-0">8,192</div>
          </div>
          <div>
            <div>Génération</div>
            <div class="h5 mb-0">v4.2</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="/socket.io/socket.io.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="/js/luna-accelerators.js"></script>
<script src="/js/luna-accelerators-hierarchy.js"></script>

<script>
  // Script pour gérer les deux vues des accélérateurs
  $(document).ready(function() {
    // Gestionnaire pour le bouton Vue Liste
    $('#view-list-btn').on('click', function() {
      // Activer le bouton Liste et désactiver le bouton Hiérarchie
      $(this).removeClass('btn-luna-outline').addClass('btn-luna');
      $('#view-hierarchy-btn').removeClass('btn-luna').addClass('btn-luna-outline');

      // Afficher la vue Liste et masquer la vue Hiérarchie
      $('#accelerators-list-view').show();
      $('#accelerators-hierarchy-view').hide();

      // Charger les données de la liste
      if (typeof loadAccelerators === 'function') {
        loadAccelerators();
      }
    });

    // Gestionnaire pour le bouton Vue Hiérarchie
    $('#view-hierarchy-btn').on('click', function() {
      // Activer le bouton Hiérarchie et désactiver le bouton Liste
      $(this).removeClass('btn-luna-outline').addClass('btn-luna');
      $('#view-list-btn').removeClass('btn-luna').addClass('btn-luna-outline');

      // Afficher la vue Hiérarchie et masquer la vue Liste
      $('#accelerators-hierarchy-view').show();
      $('#accelerators-list-view').hide();

      // Mettre à jour les connexions visuelles
      if (typeof initializeConnections === 'function') {
        setTimeout(initializeConnections, 100);
      }
    });
  });
</script>
