<%- include('luna-base', {
  title: 'Luna - Système Cognitif',
  page: 'cognitive'
}) %>
<link rel="stylesheet" href="/css/luna-cognitive.css">
<link rel="stylesheet" href="/css/luna-cognitive-fixed.css">


<div class="container-fluid mt-4 cognitive-container">
  <div class="row">
    <div class="col-12 mb-4">
      <div class="card shadow-sm">
        <div class="card-header bg-gradient-primary text-white">
          <h5 class="mb-0">Système Cognitif</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h6 class="fw-bold mb-3">État du système cognitif</h6>
              <div class="d-flex align-items-center mb-3">
                <div class="me-3">
                  <span class="badge bg-success p-2">Actif</span>
                </div>
                <div class="progress flex-grow-1" style="height: 8px;">
                  <div class="progress-bar bg-success" role="progressbar" style="width: 85%;" aria-valuenow="85" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
                <span class="ms-2 small fw-bold">85%</span>
              </div>

              <div class="row mb-3">
                <div class="col-6">
                  <div class="card bg-light">
                    <div class="card-body p-3">
                      <h6 class="card-title small text-muted mb-1">Modules actifs</h6>
                      <p class="card-text h5 mb-0 numeric-value">7/8</p>
                    </div>
                  </div>
                </div>
                <div class="col-6">
                  <div class="card bg-light">
                    <div class="card-body p-3">
                      <h6 class="card-title small text-muted mb-1">Efficacité</h6>
                      <p class="card-text h5 mb-0 numeric-value">92%</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-md-6">
              <h6 class="fw-bold mb-3">Activité neuronale</h6>
              <div class="neural-activity-container">
                <canvas id="neuralActivityChart" width="400" height="180"></canvas>
                <div class="d-flex justify-content-between mt-2">
                  <small class="text-muted">Dernières 10 secondes</small>
                  <small class="text-muted">Intensité: <span id="neuralActivityValue" class="numeric-value">78.5</span> unités</small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-6 mb-4">
      <div class="card shadow-sm h-100">
        <div class="card-header bg-gradient-info text-white">
          <h5 class="mb-0">Traitement Vidéo (TLX)</h5>
        </div>
        <div class="card-body">
          <div class="d-flex justify-content-between mb-3">
            <button id="startVideoBtn" class="btn btn-primary">
              <i class="bi bi-camera-video"></i> Activer la caméra
            </button>
            <button id="processVideoBtn" class="btn btn-success" disabled>
              <i class="bi bi-cpu"></i> Traiter avec TLX
            </button>
          </div>

          <div class="video-container mb-3">
            <div id="videoPlaceholder" class="bg-light d-flex align-items-center justify-content-center" style="height: 240px; border-radius: 8px;">
              <div class="text-center">
                <i class="bi bi-camera-video-off fs-1 text-muted"></i>
                <p class="mt-2 mb-0 text-muted">Caméra désactivée</p>
              </div>
            </div>
            <video id="videoElement" style="width: 100%; height: 240px; border-radius: 8px; display: none;" autoplay></video>
          </div>

          <div id="videoProcessingResults" class="bg-light p-3 rounded" style="display: none;">
            <h6 class="fw-bold mb-2">Résultats du traitement</h6>
            <div class="row">
              <div class="col-6">
                <div class="result-item">
                  <span class="result-label">Objets détectés:</span>
                  <span class="result-value" id="detectedObjects">-</span>
                </div>
                <div class="result-item">
                  <span class="result-label">Visages:</span>
                  <span class="result-value" id="detectedFaces">-</span>
                </div>
                <div class="result-item">
                  <span class="result-label">Environnement:</span>
                  <span class="result-value" id="environment">-</span>
                </div>
              </div>
              <div class="col-6">
                <div class="result-item">
                  <span class="result-label">FPS:</span>
                  <span class="result-value numeric-value" id="frameRate">-</span>
                </div>
                <div class="result-item">
                  <span class="result-label">Résolution:</span>
                  <span class="result-value" id="resolution">-</span>
                </div>
                <div class="result-item">
                  <span class="result-label">Temps:</span>
                  <span class="result-value numeric-value" id="processingTime">-</span> ms
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-6 mb-4">
      <div class="card shadow-sm h-100">
        <div class="card-header bg-gradient-warning text-white">
          <h5 class="mb-0">Traitement Audio</h5>
        </div>
        <div class="card-body">
          <div class="d-flex justify-content-between mb-3">
            <button id="startAudioBtn" class="btn btn-primary">
              <i class="bi bi-mic"></i> Activer le microphone
            </button>
            <button id="processAudioBtn" class="btn btn-success" disabled>
              <i class="bi bi-soundwave"></i> Analyser l'audio
            </button>
          </div>

          <div class="audio-container mb-3">
            <div id="audioVisualizer" class="bg-light d-flex align-items-center justify-content-center" style="height: 120px; border-radius: 8px;">
              <canvas id="audioWaveform" width="100%" height="100" style="display: none;"></canvas>
              <div id="audioPlaceholder" class="text-center">
                <i class="bi bi-mic-mute fs-1 text-muted"></i>
                <p class="mt-2 mb-0 text-muted">Microphone désactivé</p>
              </div>
            </div>
          </div>

          <div id="audioProcessingResults" class="bg-light p-3 rounded" style="display: none;">
            <h6 class="fw-bold mb-2">Résultats de l'analyse audio</h6>
            <div class="row">
              <div class="col-6">
                <div class="result-item">
                  <span class="result-label">Parole détectée:</span>
                  <span class="result-value" id="speechDetected">-</span>
                </div>
                <div class="result-item">
                  <span class="result-label">Langue:</span>
                  <span class="result-value" id="detectedLanguage">-</span>
                </div>
                <div class="result-item">
                  <span class="result-label">Émotion:</span>
                  <span class="result-value" id="audioEmotion">-</span>
                </div>
              </div>
              <div class="col-6">
                <div class="result-item">
                  <span class="result-label">Niveau sonore:</span>
                  <span class="result-value numeric-value" id="audioLevel">-</span> dB
                </div>
                <div class="result-item">
                  <span class="result-label">Clarté:</span>
                  <span class="result-value" id="audioClarity">-</span>
                </div>
                <div class="result-item">
                  <span class="result-label">Temps:</span>
                  <span class="result-value numeric-value" id="audioProcessingTime">-</span> ms
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-6 mb-4">
      <div class="card shadow-sm h-100">
        <div class="card-header bg-gradient-success text-white">
          <h5 class="mb-0">Traitement de Texte</h5>
        </div>
        <div class="card-body">
          <div class="form-group mb-3">
            <label for="textInput" class="form-label">Texte à analyser</label>
            <textarea id="textInput" class="form-control" rows="4" placeholder="Entrez du texte à analyser..."></textarea>
          </div>

          <div class="d-flex justify-content-end mb-3">
            <button id="processTextBtn" class="btn btn-success">
              <i class="bi bi-braces"></i> Analyser le texte
            </button>
          </div>

          <div id="textProcessingResults" class="bg-light p-3 rounded" style="display: none;">
            <h6 class="fw-bold mb-2">Résultats de l'analyse textuelle</h6>
            <div class="row">
              <div class="col-6">
                <div class="result-item">
                  <span class="result-label">Entités:</span>
                  <span class="result-value" id="textEntities">-</span>
                </div>
                <div class="result-item">
                  <span class="result-label">Sentiment:</span>
                  <span class="result-value" id="textSentiment">-</span>
                </div>
                <div class="result-item">
                  <span class="result-label">Langue:</span>
                  <span class="result-value" id="textLanguage">-</span>
                </div>
              </div>
              <div class="col-6">
                <div class="result-item">
                  <span class="result-label">Mots-clés:</span>
                  <span class="result-value" id="textKeywords">-</span>
                </div>
                <div class="result-item">
                  <span class="result-label">Complexité:</span>
                  <span class="result-value" id="textComplexity">-</span>
                </div>
                <div class="result-item">
                  <span class="result-label">Temps:</span>
                  <span class="result-value numeric-value" id="textProcessingTime">-</span> ms
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-6 mb-4">
      <div class="card shadow-sm h-100">
        <div class="card-header bg-gradient-danger text-white">
          <h5 class="mb-0">Injection Directe au Cerveau</h5>
        </div>
        <div class="card-body">
          <div class="form-group mb-3">
            <label for="knowledgeInput" class="form-label">Connaissance à injecter</label>
            <textarea id="knowledgeInput" class="form-control" rows="4" placeholder="Entrez une connaissance à injecter directement dans le cerveau..."></textarea>
          </div>

          <div class="row mb-3">
            <div class="col-md-6">
              <div class="form-group">
                <label for="domainSelect" class="form-label">Domaine</label>
                <select id="domainSelect" class="form-select">
                  <option value="science">Science</option>
                  <option value="technologie">Technologie</option>
                  <option value="art">Art</option>
                  <option value="histoire">Histoire</option>
                  <option value="philosophie">Philosophie</option>
                  <option value="mathematiques">Mathématiques</option>
                  <option value="autre">Autre</option>
                </select>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="importanceRange" class="form-label">Importance (1-10)</label>
                <input type="range" class="form-range" id="importanceRange" min="1" max="10" value="5">
                <div class="d-flex justify-content-between">
                  <span class="small">Faible</span>
                  <span class="small" id="importanceValue">5</span>
                  <span class="small">Élevée</span>
                </div>
              </div>
            </div>
          </div>

          <div class="d-flex justify-content-end mb-3">
            <button id="injectKnowledgeBtn" class="btn btn-danger">
              <i class="bi bi-lightning"></i> Injecter au cerveau
            </button>
          </div>

          <div id="injectionResults" class="bg-light p-3 rounded" style="display: none;">
            <h6 class="fw-bold mb-2">Résultats de l'injection</h6>
            <div class="alert alert-success" role="alert">
              <i class="bi bi-check-circle-fill me-2"></i>
              <span id="injectionMessage">Connaissance injectée avec succès!</span>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="result-item">
                  <span class="result-label">Connexions neuronales:</span>
                  <span class="result-value numeric-value" id="neuralConnections">-</span>
                </div>
                <div class="result-item">
                  <span class="result-label">Mots-clés extraits:</span>
                  <span class="result-value" id="extractedKeywords">-</span>
                </div>
              </div>
              <div class="col-md-6">
                <div class="result-item">
                  <span class="result-label">Zone de stockage:</span>
                  <span class="result-value" id="storageZone">-</span>
                </div>
                <div class="result-item">
                  <span class="result-label">Temps d'intégration:</span>
                  <span class="result-value numeric-value" id="integrationTime">125</span> ms
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Nouvelle section pour l'intégration vidéo et l'apprentissage -->
  <div class="row">
    <div class="col-md-6 mb-4">
      <div class="card shadow-sm h-100">
        <div class="card-header bg-gradient-primary text-white">
          <h5 class="mb-0">Intégration Vidéo</h5>
        </div>
        <div class="card-body">
          <div class="form-group mb-3">
            <label for="videoUrlInput" class="form-label">URL de la vidéo</label>
            <div class="input-group">
              <input type="text" id="videoUrlInput" class="form-control" placeholder="https://www.youtube.com/watch?v=...">
              <button id="loadVideoBtn" class="btn btn-primary">
                <i class="bi bi-arrow-right-circle"></i> Charger
              </button>
            </div>
          </div>

          <div class="video-integration-container mb-3">
            <div id="videoIntegrationPlaceholder" class="bg-light d-flex align-items-center justify-content-center" style="height: 240px; border-radius: 8px;">
              <div class="text-center">
                <i class="bi bi-film fs-1 text-muted"></i>
                <p class="mt-2 mb-0 text-muted">Aucune vidéo chargée</p>
              </div>
            </div>
            <div id="videoIntegrationPlayer" style="display: none; width: 100%; height: 240px; border-radius: 8px;"></div>
          </div>

          <div class="d-flex justify-content-between mb-3">
            <button id="extractVideoInfoBtn" class="btn btn-info" disabled>
              <i class="bi bi-info-circle"></i> Extraire informations
            </button>
            <button id="learnFromVideoBtn" class="btn btn-success" disabled>
              <i class="bi bi-mortarboard"></i> Apprendre de la vidéo
            </button>
          </div>

          <div id="videoIntegrationResults" class="bg-light p-3 rounded" style="display: none;">
            <h6 class="fw-bold mb-2">Résultats de l'intégration</h6>
            <div class="row">
              <div class="col-6">
                <div class="result-item">
                  <span class="result-label">Titre:</span>
                  <span class="result-value" id="videoTitle">-</span>
                </div>
                <div class="result-item">
                  <span class="result-label">Durée:</span>
                  <span class="result-value" id="videoDuration">-</span>
                </div>
                <div class="result-item">
                  <span class="result-label">Catégorie:</span>
                  <span class="result-value" id="videoCategory">-</span>
                </div>
              </div>
              <div class="col-6">
                <div class="result-item">
                  <span class="result-label">Concepts identifiés:</span>
                  <span class="result-value" id="videoConcepts">-</span>
                </div>
                <div class="result-item">
                  <span class="result-label">Connaissances extraites:</span>
                  <span class="result-value numeric-value" id="videoKnowledge">-</span>
                </div>
                <div class="result-item">
                  <span class="result-label">Temps d'analyse:</span>
                  <span class="result-value numeric-value" id="videoAnalysisTime">-</span> ms
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-6 mb-4">
      <div class="card shadow-sm h-100">
        <div class="card-header bg-gradient-info text-white">
          <h5 class="mb-0">Apprentissage Cognitif</h5>
        </div>
        <div class="card-body">
          <div class="row mb-3">
            <div class="col-md-6">
              <div class="card bg-light">
                <div class="card-body p-3">
                  <h6 class="card-title small text-muted mb-1">Niveau d'apprentissage</h6>
                  <p class="card-text h5 mb-0 numeric-value" id="learningLevel">3</p>
                  <div class="progress mt-2" style="height: 5px;">
                    <div class="progress-bar bg-info" role="progressbar" style="width: 60%;" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100"></div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="card bg-light">
                <div class="card-body p-3">
                  <h6 class="card-title small text-muted mb-1">Capacité d'absorption</h6>
                  <p class="card-text h5 mb-0 numeric-value" id="absorptionCapacity">87%</p>
                  <div class="progress mt-2" style="height: 5px;">
                    <div class="progress-bar bg-success" role="progressbar" style="width: 87%;" aria-valuenow="87" aria-valuemin="0" aria-valuemax="100"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="form-group mb-3">
            <label for="learningSourceSelect" class="form-label">Source d'apprentissage</label>
            <select id="learningSourceSelect" class="form-select">
              <option value="internet">Internet</option>
              <option value="database">Base de données</option>
              <option value="memory">Mémoire thermique</option>
              <option value="video">Vidéo intégrée</option>
              <option value="custom">Personnalisée</option>
            </select>
          </div>

          <div class="form-group mb-3">
            <label for="learningTopicInput" class="form-label">Sujet d'apprentissage</label>
            <input type="text" id="learningTopicInput" class="form-control" placeholder="Intelligence artificielle, physique quantique, etc.">
          </div>

          <div class="form-group mb-3">
            <label for="learningDepthRange" class="form-label">Profondeur d'apprentissage</label>
            <input type="range" class="form-range" id="learningDepthRange" min="1" max="10" value="5">
            <div class="d-flex justify-content-between">
              <span class="small">Superficiel</span>
              <span class="small" id="learningDepthValue">5</span>
              <span class="small">Approfondi</span>
            </div>
          </div>

          <div class="d-flex justify-content-end mb-3">
            <button id="startLearningBtn" class="btn btn-info">
              <i class="bi bi-brain"></i> Démarrer l'apprentissage
            </button>
          </div>

          <div id="learningResults" class="bg-light p-3 rounded" style="display: none;">
            <h6 class="fw-bold mb-2">Résultats de l'apprentissage</h6>
            <div class="progress mb-3" style="height: 10px;">
              <div class="progress-bar progress-bar-striped progress-bar-animated bg-info" id="learningProgressBar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
            <div class="d-flex justify-content-between mb-3">
              <span id="learningStatus">En attente...</span>
              <span id="learningProgressValue">0%</span>
            </div>
            <div id="learningStats" style="display: none;">
              <div class="row">
                <div class="col-6">
                  <div class="result-item">
                    <span class="result-label">Concepts appris:</span>
                    <span class="result-value numeric-value" id="conceptsLearned">-</span>
                  </div>
                  <div class="result-item">
                    <span class="result-label">Connexions créées:</span>
                    <span class="result-value numeric-value" id="connectionsCreated">-</span>
                  </div>
                </div>
                <div class="col-6">
                  <div class="result-item">
                    <span class="result-label">Gain de QI:</span>
                    <span class="result-value numeric-value" id="iqGain">-</span> points
                  </div>
                  <div class="result-item">
                    <span class="result-label">Temps total:</span>
                    <span class="result-value numeric-value" id="learningTime">-</span> s
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Pied de page fixe -->
<footer class="footer-fixed">
  <small>Vision Ultra - Interface Cognitive - <span id="current-datetime"></span> - &copy; 2024 Jean Passave - Sainte-Anne, Guadeloupe (97180)</small>
</footer>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://www.youtube.com/iframe_api"></script>
<script src="/js/luna-cognitive.js"></script>
