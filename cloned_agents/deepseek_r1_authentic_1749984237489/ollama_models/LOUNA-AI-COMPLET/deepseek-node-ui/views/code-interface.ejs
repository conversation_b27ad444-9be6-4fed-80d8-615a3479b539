<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %></title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/atom-one-dark.min.css">
  <style>
    body {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      background-color: #120B1A; /* Noir nuancé de violet foncé */
      color: #e0e0e0;
      overflow: hidden; /* Empêche le défilement global */
    }
    
    /* Variables pour la disposition fixe */
    :root {
      --header-height: 60px;
      --footer-height: 100px;
    }
    
    .navbar {
      background: linear-gradient(to right, #1A1025, #2D142C) !important; /* Dégradé violet-rouge foncé */
      border-bottom: 1px solid #4A1942;
    }
    
    .sidebar {
      background-color: #1A1025; /* Noir nuancé de violet */
      border-right: 1px solid #4A1942;
    }
    
    .form-control, .form-select {
      background-color: #241B2F; /* Fond violet sombre */
      color: #e0e0e0;
      border-color: #4A1942; /* Bordure rouge-violet */
    }
    
    .form-control:focus, .form-select:focus {
      background-color: #241B2F;
      color: #e0e0e0;
      border-color: #7B2869; /* Bordure rose-violet */
      box-shadow: 0 0 0 0.25rem rgba(123, 40, 105, 0.25); /* Ombre rose-violet */
    }
    
    #chat-messages {
      flex: 1;
      overflow-y: auto;
      padding: 1rem;
      height: calc(100vh - var(--header-height) - var(--footer-height));
      position: fixed;
      top: var(--header-height);
      left: 0;
      right: 0;
      bottom: var(--footer-height);
      padding-left: 25%; /* Espace pour la sidebar */
    }
    
    .message {
      margin-bottom: 1.5rem;
      display: flex;
      align-items: flex-start;
    }
    
    .user-message {
      justify-content: flex-end;
    }
    
    .bot-message {
      justify-content: flex-start;
    }
    
    .system-message {
      justify-content: center;
      opacity: 0.7;
    }
    
    .message-avatar {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #383838;
      margin-right: 10px;
      flex-shrink: 0;
    }
    
    .user-message .message-avatar {
      order: 2;
      margin-right: 0;
      margin-left: 10px;
      background-color: #0d6efd;
    }
    
    .message-content {
      background-color: #2d2d2d;
      padding: 15px;
      border-radius: 10px;
      max-width: 80%;
      overflow-wrap: break-word;
    }
    
    .user-message .message-content {
      background: linear-gradient(135deg, #7B2869, #4A1942); /* Dégradé rouge-violet */
    }
    
    .system-message .message-content {
      background-color: #383838;
      max-width: 90%;
      text-align: center;
      padding: 8px 15px;
    }
    
    .typing-indicator {
      display: inline-flex;
      align-items: center;
    }
    
    .typing-indicator span {
      width: 8px;
      height: 8px;
      background-color: #e0e0e0;
      border-radius: 50%;
      margin: 0 2px;
      animation: typing 1s infinite;
    }
    
    .typing-indicator span:nth-child(2) {
      animation-delay: 0.2s;
    }
    
    .typing-indicator span:nth-child(3) {
      animation-delay: 0.4s;
    }
    
    @keyframes typing {
      0%, 100% { transform: translateY(0); }
      50% { transform: translateY(-5px); }
    }
    
    /* Style pour les blocs de code */
    pre {
      position: relative;
      background-color: #282c34;
      border-radius: 6px;
      margin: 1rem 0;
      padding: 1rem;
      overflow-x: auto;
    }
    
    code {
      font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
      font-size: 0.9rem;
      padding: 0;
      color: #abb2bf;
    }
    
    .copy-button {
      position: absolute;
      top: 5px;
      right: 5px;
      background-color: #0d6efd;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 5px 10px;
      font-size: 0.8rem;
      cursor: pointer;
      opacity: 0;
      transition: opacity 0.3s;
    }
    
    pre:hover .copy-button {
      opacity: 1;
    }
    
    .copy-button:hover {
      background-color: #0b5ed7;
    }
    
    .copy-success {
      background-color: #198754 !important;
    }
    
    /* Animation flash */
    @keyframes statusFlash {
      0%   { box-shadow: 0 0 0 rgba(156, 39, 176, 0); }
      50%  { box-shadow: 0 0 10px rgba(156, 39, 176, 0.7); }
      100% { box-shadow: 0 0 0 rgba(156, 39, 176, 0); }
    }
    
    .status-flash {
      animation: statusFlash 0.3s ease-out;
    }
    
    .text-purple-300 {
      color: #BA68C8;
    }
    
    /* Badges et boutons */
    .badge {
      font-size: 0.8rem;
      padding: 0.35em 0.65em;
    }
    
    .badge.bg-success {
      background-color: #9C27B0 !important; /* Violet */
    }
    
    .badge.bg-warning {
      background-color: #C2185B !important; /* Rouge-rose */
      color: white;
    }
    
    .badge.bg-danger {
      background-color: #880E4F !important; /* Rouge foncé */
    }
    
    .btn-primary {
      background: linear-gradient(135deg, #7B2869, #4A1942);
      border-color: #4A1942;
    }
    
    .btn-primary:hover {
      background: linear-gradient(135deg, #9C27B0, #7B2869);
      border-color: #7B2869;
    }
    
    .btn-outline-primary {
      color: #9C27B0;
      border-color: #9C27B0;
    }
    
    .btn-outline-primary:hover {
      background-color: #9C27B0;
      color: white;
    }
    
    .btn-outline-secondary {
      color: #C2185B;
      border-color: #C2185B;
    }
    
    .btn-outline-secondary:hover {
      background-color: #C2185B;
      border-color: #C2185B;
      color: white;
    }
    
    /* Barre de défilement personnalisée */
    ::-webkit-scrollbar {
      width: 10px;
    }
    
    ::-webkit-scrollbar-track {
      background: #252526;
    }
    
    ::-webkit-scrollbar-thumb {
      background: #555;
      border-radius: 5px;
    }
    
    ::-webkit-scrollbar-thumb:hover {
      background: #777;
    }
  </style>
</head>
<body>
  <header style="height: var(--header-height); position: fixed; top: 0; left: 0; right: 0; z-index: 1000;">
    <nav class="navbar navbar-expand-lg navbar-dark">
      <div class="container-fluid">
        <a class="navbar-brand d-flex align-items-center" href="/">
          <i class="bi bi-braces-asterisk fs-3 me-2"></i>
          <span>DeepSeek Code</span>
          <span class="badge bg-success ms-2">Français</span>
        </a>
        <div id="ollama-status" class="ms-auto">
          <span class="badge bg-warning">Vérification d'Ollama...</span>
        </div>
      </div>
    </nav>
  </header>
  
  <div style="height: var(--header-height);"></div> <!-- Espace réservé pour l'en-tête fixe -->

  <main class="container-fluid flex-grow-1 d-flex flex-column">
    <div class="row flex-grow-1">
      <div class="col-md-3 sidebar p-3" style="position: fixed; top: var(--header-height); left: 0; height: calc(100vh - var(--header-height)); overflow-y: auto; width: 25%; z-index: 900;">
        <!-- Zone de statut système -->
        <div id="system-status-area" class="mb-4 p-3 rounded" style="background-color: #1D0F2D; border: 1px solid #4A1942;">
          <h6 class="d-flex align-items-center mb-2">
            <i class="bi bi-info-circle-fill me-2" style="color: #9C27B0;"></i>
            Statut Système
          </h6>
          <div id="system-messages" class="small" style="max-height: 100px; overflow-y: auto;">
            <!-- Les messages système seront ajoutés ici par JS -->
          </div>
        </div>
        
        <!-- Conversations -->
        <div class="mb-4">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="mb-0">Conversations</h5>
            <div>
              <button id="refresh-conversations-btn" class="btn btn-sm btn-outline-secondary me-1" title="Rafraîchir">
                <i class="bi bi-arrow-clockwise"></i>
              </button>
              <button id="thermal-memory-btn" class="btn btn-sm btn-outline-primary" title="Mémoire thermique">
                <i class="bi bi-thermometer-half"></i>
              </button>
            </div>
          </div>
          
          <!-- Statistiques de la mémoire thermique -->
          <div id="thermal-stats" class="mb-3 small p-2 rounded" style="background-color: #1D0F2D; border: 1px solid #4A1942; display: none;">
            <div class="d-flex justify-content-between align-items-center mb-1">
              <span>Mémoire thermique</span>
              <span class="badge bg-primary" id="thermal-total-count">0</span>
            </div>
            <div class="progress mb-2" style="height: 15px; background-color: #120B1A; border-radius: 10px; overflow: hidden;">
              <div class="progress-bar bg-danger" id="zone1-bar" style="width: 0%" title="Zone 1 (Récente)"></div>
              <div class="progress-bar bg-warning" id="zone2-bar" style="width: 0%" title="Zone 2 (Chaude)"></div>
              <div class="progress-bar bg-info" id="zone3-bar" style="width: 0%" title="Zone 3 (Tiède)"></div>
              <div class="progress-bar" id="zone4-bar" style="width: 0%; background-color: #9C27B0;" title="Zone 4 (Fraîche)"></div>
              <div class="progress-bar" id="zone5-bar" style="width: 0%; background-color: #4A1942;" title="Zone 5 (Froide)"></div>
              <div class="progress-bar bg-dark" id="zone6-bar" style="width: 0%" title="Zone 6 (Archive)"></div>
            </div>
            <div class="d-flex justify-content-between">
              <span class="badge rounded-pill bg-danger me-1" title="Zone 1 (Récente)">
                <i class="bi bi-thermometer-high"></i> <span id="zone1-count">0</span>
              </span>
              <span class="badge rounded-pill bg-warning me-1" title="Zone 2 (Chaude)">
                <span id="zone2-count">0</span>
              </span>
              <span class="badge rounded-pill bg-info me-1" title="Zone 3 (Tiède)">
                <span id="zone3-count">0</span>
              </span>
              <span class="badge rounded-pill me-1" style="background-color: #9C27B0;" title="Zone 4 (Fraîche)">
                <span id="zone4-count">0</span>
              </span>
              <span class="badge rounded-pill me-1" style="background-color: #4A1942;" title="Zone 5 (Froide)">
                <span id="zone5-count">0</span>
              </span>
              <span class="badge rounded-pill bg-dark" title="Zone 6 (Archive)">
                <i class="bi bi-thermometer-low"></i> <span id="zone6-count">0</span>
              </span>
            </div>
          </div>
          
          <!-- Moniteur des accélérateurs Kyber -->
          <div id="kyber-monitor" class="mt-3 small p-2 rounded" style="background-color: #1D0F2D; border: 1px solid #4A1942;">
            <div class="d-flex justify-content-between align-items-center mb-1">
              <span>⚡ Accélérateurs Kyber</span>
              <span class="badge bg-primary" id="kyber-total-count">0</span>
            </div>
            
            <!-- Barres principales des accélérateurs -->
            <div class="kyber-stats">
              <div class="mb-1">
                <div class="d-flex justify-content-between">
                  <small>Mémoire</small>
                  <small id="memory-value">0%</small>
                </div>
                <div class="progress" style="height: 8px; background-color: #120B1A;">
                  <div class="progress-bar" id="memory-bar" style="width: 0%; background-color: #03A9F4;"></div>
                </div>
              </div>
              
              <div class="mb-1">
                <div class="d-flex justify-content-between">
                  <small>Vidéo</small>
                  <small id="video-value">0%</small>
                </div>
                <div class="progress" style="height: 8px; background-color: #120B1A;">
                  <div class="progress-bar" id="video-bar" style="width: 0%; background-color: #4CAF50;"></div>
                </div>
              </div>
              
              <div class="mb-1">
                <div class="d-flex justify-content-between">
                  <small>Audio</small>
                  <small id="audio-value">0%</small>
                </div>
                <div class="progress" style="height: 8px; background-color: #120B1A;">
                  <div class="progress-bar" id="audio-bar" style="width: 0%; background-color: #FF9800;"></div>
                </div>
              </div>
              
              <div class="mb-2">
                <div class="d-flex justify-content-between">
                  <small>Thermique</small>
                  <small id="thermal-kyber-value">0%</small>
                </div>
                <div class="progress" style="height: 8px; background-color: #120B1A;">
                  <div class="progress-bar" id="thermal-kyber-bar" style="width: 0%; background-color: #F44336;"></div>
                </div>
              </div>
            </div>
            
            <!-- Accélérateurs de zone -->
            <div class="mt-2 mb-1">
              <small class="d-block mb-1">Accélérateurs de zone</small>
              <div class="d-flex justify-content-between">
                <div class="d-flex flex-column align-items-center" style="width: 16%;">
                  <div class="badge rounded-pill bg-danger mb-1" style="width: 100%;">Z1</div>
                  <div class="progress" style="height: 40px; width: 8px; background-color: #120B1A;">
                    <div class="progress-bar bg-danger" id="z1-kyber-bar" style="width: 100%; height: 0;"></div>
                  </div>
                  <small id="z1-kyber-value">0%</small>
                </div>
                <div class="d-flex flex-column align-items-center" style="width: 16%;">
                  <div class="badge rounded-pill bg-warning mb-1" style="width: 100%;">Z2</div>
                  <div class="progress" style="height: 40px; width: 8px; background-color: #120B1A;">
                    <div class="progress-bar bg-warning" id="z2-kyber-bar" style="width: 100%; height: 0;"></div>
                  </div>
                  <small id="z2-kyber-value">0%</small>
                </div>
                <div class="d-flex flex-column align-items-center" style="width: 16%;">
                  <div class="badge rounded-pill bg-info mb-1" style="width: 100%;">Z3</div>
                  <div class="progress" style="height: 40px; width: 8px; background-color: #120B1A;">
                    <div class="progress-bar bg-info" id="z3-kyber-bar" style="width: 100%; height: 0;"></div>
                  </div>
                  <small id="z3-kyber-value">0%</small>
                </div>
                <div class="d-flex flex-column align-items-center" style="width: 16%;">
                  <div class="badge rounded-pill mb-1" style="width: 100%; background-color: #9C27B0;">Z4</div>
                  <div class="progress" style="height: 40px; width: 8px; background-color: #120B1A;">
                    <div class="progress-bar" id="z4-kyber-bar" style="width: 100%; height: 0%; background-color: #9C27B0;"></div>
                  </div>
                  <small id="z4-kyber-value">0%</small>
                </div>
                <div class="d-flex flex-column align-items-center" style="width: 16%;">
                  <div class="badge rounded-pill mb-1" style="width: 100%; background-color: #4A1942;">Z5</div>
                  <div class="progress" style="height: 40px; width: 8px; background-color: #120B1A;">
                    <div class="progress-bar" id="z5-kyber-bar" style="width: 100%; height: 0%; background-color: #4A1942;"></div>
                  </div>
                  <small id="z5-kyber-value">0%</small>
                </div>
                <div class="d-flex flex-column align-items-center" style="width: 16%;">
                  <div class="badge rounded-pill bg-dark mb-1" style="width: 100%;">Z6</div>
                  <div class="progress" style="height: 40px; width: 8px; background-color: #120B1A;">
                    <div class="progress-bar bg-dark" id="z6-kyber-bar" style="width: 100%; height: 0%;"></div>
                  </div>
                  <small id="z6-kyber-value">0%</small>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Onglets pour les conversations (normales/archivées) -->
          <ul class="nav nav-tabs mb-2" id="conversation-tabs">
            <li class="nav-item">
              <a class="nav-link active" id="active-tab" data-bs-toggle="tab" href="#active-conversations">Actives</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="hidden-tab" data-bs-toggle="tab" href="#hidden-conversations">Archivées <span id="hidden-count" class="badge rounded-pill bg-secondary">0</span></a>
            </li>
          </ul>
          
          <div class="tab-content" id="conversation-tab-content">
            <!-- Conversations actives -->
            <div class="tab-pane fade show active" id="active-conversations">
              <div id="conversations-list" class="list-group mb-3" style="max-height: 200px; overflow-y: auto;">
                <!-- Les conversations seront ajoutées ici par JS -->
                <div class="text-center p-3 text-muted small">
                  Chargement des conversations...
                </div>
              </div>
            </div>
            
            <!-- Conversations archivées -->
            <div class="tab-pane fade" id="hidden-conversations">
              <div id="hidden-conversations-list" class="list-group mb-3" style="max-height: 200px; overflow-y: auto;">
                <!-- Les conversations masquées seront ajoutées ici par JS -->
                <div class="text-center p-3 text-muted small">
                  Aucune conversation archivée
                </div>
              </div>
            </div>
          </div>
          
          <button id="new-chat-btn" class="btn btn-outline-primary w-100 mb-2">
            <i class="bi bi-plus-circle"></i> Nouvelle conversation
          </button>
        </div>
        
        <h5 class="mb-3">Paramètres</h5>
        <div class="mb-4">
          <label for="language-selector" class="form-label">Langage de programmation</label>
          <select id="language-selector" class="form-select mb-2">
            <option value="javascript">JavaScript</option>
            <option value="python">Python</option>
            <option value="java">Java</option>
            <option value="csharp">C#</option>
            <option value="cpp">C++</option>
            <option value="php">PHP</option>
            <option value="ruby">Ruby</option>
            <option value="go">Go</option>
            <option value="swift">Swift</option>
            <option value="kotlin">Kotlin</option>
            <option value="rust">Rust</option>
            <option value="typescript">TypeScript</option>
            <option value="html">HTML</option>
            <option value="css">CSS</option>
            <option value="sql">SQL</option>
            <option value="bash">Bash</option>
          </select>
          <div class="form-text text-light opacity-75">Aide au formatage du code</div>
        </div>
        
        <div class="mb-3">
          <label for="temperature" class="form-label">Créativité: <span id="temperature-value">0.7</span></label>
          <input type="range" class="form-range" id="temperature" min="0" max="1" step="0.1" value="0.7">
          <div class="form-text text-light opacity-75">
            <i class="bi bi-info-circle"></i> 
            Valeur basse pour des réponses précises, haute pour plus de créativité
          </div>
        </div>

        <hr class="my-4 border-secondary">
        
        <div class="mb-3">
          <button id="check-status-btn" onclick="checkOllamaStatus()" class="btn btn-outline-secondary w-100">
            <i class="bi bi-arrow-repeat"></i> Vérifier Ollama
          </button>
        </div>
        
        <div class="mt-4">
          <h6 class="mb-2">Suggestions de prompts:</h6>
          <div class="list-group bg-transparent">
            <button class="list-group-item list-group-item-action bg-dark text-light border-secondary prompt-suggestion">
              Explique-moi comment créer une API REST avec Express.js
            </button>
            <button class="list-group-item list-group-item-action bg-dark text-light border-secondary prompt-suggestion">
              Écris une fonction pour trier un tableau d'objets
            </button>
            <button class="list-group-item list-group-item-action bg-dark text-light border-secondary prompt-suggestion">
              Comment implémenter un design pattern Observer?
            </button>
          </div>
        </div>
      </div>
      
      <div class="col-md-9 d-flex flex-column">
        <div id="current-chat-title" class="p-2 border-bottom border-secondary text-center">
          Nouvelle conversation
        </div>
        <div id="chat-messages" class="flex-grow-1"></div>
        <div class="p-3 border-top border-secondary" style="position: fixed; bottom: 0; left: 25%; right: 0; background-color: #120B1A; z-index: 1000; height: var(--footer-height);">
          <form id="chat-form" class="d-flex">
            <input type="text" id="message-input" class="form-control me-2" placeholder="Posez votre question sur le code...">
            <button type="submit" class="btn btn-primary">
              <i class="bi bi-send"></i>
            </button>
          </form>
        </div>
        <div style="height: var(--footer-height);"></div> <!-- Espace réservé pour le pied de page fixe -->
      </div>
    </div>
  </main>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/4.3.0/marked.min.js"></script>
  <script>
  document.addEventListener('DOMContentLoaded', () => {
    // Éléments DOM
    const chatForm = document.getElementById('chat-form');
    const messageInput = document.getElementById('message-input');
    const chatMessages = document.getElementById('chat-messages');
    const ollamaStatus = document.getElementById('ollama-status');
    const temperatureSlider = document.getElementById('temperature');
    const temperatureValue = document.getElementById('temperature-value');
    const languageSelector = document.getElementById('language-selector');
    const newChatBtn = document.getElementById('new-chat-btn');
    const promptSuggestions = document.querySelectorAll('.prompt-suggestion');
    const currentChatTitle = document.getElementById('current-chat-title');
    const conversationsList = document.getElementById('conversations-list');
    const refreshConversationsBtn = document.getElementById('refresh-conversations-btn');
    const hiddenConversationsList = document.getElementById('hidden-conversations-list');
    const thermalMemoryBtn = document.getElementById('thermal-memory-btn');
    const thermalStats = document.getElementById('thermal-stats');
    const hiddenTab = document.getElementById('hidden-tab');
    const hiddenCount = document.getElementById('hidden-count');

    // Variables globales
    let socket;
    let isWaitingForResponse = false;
    let currentLanguage = 'javascript';
    let currentConversationId = null;

    // Initialisation
    initializeSocket();
    initializeUI();

    // Fonctions
    function initializeSocket() {
      console.log('Initialisation de la connexion socket...');
      
      socket = io({
        reconnectionAttempts: Infinity,
        reconnectionDelay: 1000,
        reconnectionDelayMax: 5000,
        timeout: 20000,
        transports: ['websocket', 'polling'],
        upgrade: true,
        forceNew: true
      });

      socket.on('connect', () => {
        console.log('Connecté au serveur');
        displaySystemMessage('Connecté au serveur');
        checkOllamaStatus();
        loadConversations();
      });

      socket.on('reconnect', (attemptNumber) => {
        console.log(`Reconnecté au serveur après ${attemptNumber} tentatives`);
        displaySystemMessage(`Reconnecté au serveur après ${attemptNumber} tentatives`);
        checkOllamaStatus();
        loadConversations();
      });

      socket.on('connect_error', (error) => {
        console.error('Erreur de connexion:', error);
        displaySystemMessage(`Erreur de connexion: ${error.message}`);
      });

      socket.on('connect_timeout', () => {
        console.error('Délai de connexion dépassé');
        displaySystemMessage('Délai de connexion dépassé');
      });

      socket.on('error', (error) => {
        console.error('Erreur socket:', error);
        displaySystemMessage(`Erreur socket: ${error.message}`);
      });

      socket.on('disconnect', (reason) => {
        console.log('Déconnecté du serveur:', reason);
        displaySystemMessage(`Déconnecté du serveur: ${reason}`);
        
        if (reason === 'io server disconnect') {
          socket.connect();
        }
      });

      socket.on('ollama status', (data) => {
        updateOllamaStatus(data);
      });

      socket.on('chat response', (data) => {
        handleChatResponse(data);
      });

      socket.on('processing', (isProcessing) => {
        updateProcessingStatus(isProcessing);
      });
      
      // Événements de gestion des conversations
      socket.on('conversations list', (data) => {
        displayConversations(data.conversations);
        
        // Mettre à jour les statistiques de la mémoire thermique si disponibles
        if (data.stats) {
          updateThermalStats(data.stats);
        }
        
        // Mettre à jour les statistiques des accélérateurs Kyber si disponibles
        if (data.accelerators) {
          updateKyberAccelerators(data.accelerators);
        }
      });
      
      socket.on('conversation', (conversation) => {
        loadConversation(conversation);
      });
      
      socket.on('conversation created', (data) => {
        currentConversationId = data.id;
        currentChatTitle.textContent = data.title;
        loadConversations(); // Rafraîchir la liste des conversations
        displaySystemMessage(`Nouvelle conversation créée (ID: ${data.id})`);
      });
      
      socket.on('conversation deleted', (data) => {
        if (data.id === currentConversationId) {
          currentConversationId = null;
          chatMessages.innerHTML = '';
          currentChatTitle.textContent = 'Nouvelle conversation';
        }
        loadConversations();
        loadHiddenConversations(); // Rafraîchir également les conversations cachées
        displaySystemMessage(`Conversation supprimée (ID: ${data.id})`);
      });
      
      socket.on('conversation hidden', (data) => {
        if (data.id === currentConversationId) {
          currentConversationId = null;
          chatMessages.innerHTML = '';
          currentChatTitle.textContent = 'Nouvelle conversation';
        }
        loadConversations();
        loadHiddenConversations(); // Rafraîchir les conversations cachées
        displaySystemMessage(`Conversation archivée en mémoire thermique (ID: ${data.id})`);
      });
      
      socket.on('conversation restored', (data) => {
        loadConversations();
        loadHiddenConversations();
        displaySystemMessage(`Conversation restaurée (ID: ${data.id})`);
      });
      
      socket.on('conversation title updated', (data) => {
        if (data.id === currentConversationId) {
          currentChatTitle.textContent = data.title;
        }
        loadConversations();
      });
      
      socket.on('hidden conversations', (data) => {
        displayHiddenConversations(data.conversations);
        
        // Mettre à jour le compteur
        if (hiddenCount) {
          hiddenCount.textContent = data.conversations.length;
        }
        
        // Mettre à jour les statistiques de la mémoire thermique si disponibles
        if (data.stats) {
          updateThermalStats(data.stats);
        }
      });
    }

    function initializeUI() {
      // Initialiser les contrôles de l'interface
      if (temperatureSlider && temperatureValue) {
        temperatureSlider.addEventListener('input', () => {
          temperatureValue.textContent = temperatureSlider.value;
        });
      }

      // Gestionnaire du formulaire de chat
      if (chatForm) {
        chatForm.addEventListener('submit', (e) => {
          e.preventDefault();
          sendMessage();
        });
      }

      // Gestionnaire du sélecteur de langage
      if (languageSelector) {
        languageSelector.addEventListener('change', () => {
          currentLanguage = languageSelector.value;
        });
      }

      // Gestionnaire des suggestions de prompts
      promptSuggestions.forEach(button => {
        button.addEventListener('click', () => {
          messageInput.value = button.textContent.trim();
          messageInput.focus();
        });
      });

      // Gestionnaire pour nouvelle conversation
      if (newChatBtn) {
        newChatBtn.addEventListener('click', () => {
          startNewConversation();
        });
      }
      
      // Gestionnaire pour rafraîchir la liste des conversations
      if (refreshConversationsBtn) {
        refreshConversationsBtn.addEventListener('click', () => {
          loadConversations();
          loadHiddenConversations();
        });
      }
      
      // Gestionnaire pour afficher/masquer les statistiques de la mémoire thermique
      if (thermalMemoryBtn) {
        thermalMemoryBtn.addEventListener('click', () => {
          if (thermalStats) {
            const isVisible = thermalStats.style.display !== 'none';
            thermalStats.style.display = isVisible ? 'none' : 'block';
            
            if (!isVisible) {
              // Mettre à jour les statistiques si on les affiche
              loadHiddenConversations();
            }
          }
        });
      }
      
      // Gestionnaire pour l'onglet des conversations cachées
      if (hiddenTab) {
        hiddenTab.addEventListener('click', () => {
          loadHiddenConversations();
        });
      }

      // Configuration de marked pour le parsing du markdown
      marked.setOptions({
        highlight: function(code, lang) {
          const language = hljs.getLanguage(lang) ? lang : 'plaintext';
          return hljs.highlight(code, { language }).value;
        },
        langPrefix: 'hljs language-',
        breaks: true,
        gfm: true
      });

      displaySystemMessage('Application initialisée');
      
      // Charger les conversations cachées pour initialiser le compteur
      loadHiddenConversations();
    }
    
    // Fonctions de gestion des conversations
    function loadConversations() {
      if (conversationsList) {
        conversationsList.innerHTML = '<div class="text-center p-3 text-muted small">Chargement des conversations...</div>';
      }
      socket.emit('get conversations');
    }
    
    function loadHiddenConversations() {
      if (hiddenConversationsList) {
        hiddenConversationsList.innerHTML = '<div class="text-center p-3 text-muted small">Chargement des conversations archivées...</div>';
      }
      socket.emit('get hidden conversations');
    }
    
    function updateThermalStats(stats) {
      if (!stats) return;
      
      // Mettre à jour le compteur total
      const totalCount = document.getElementById('thermal-total-count');
      if (totalCount) {
        totalCount.textContent = stats.totalMemories || 0;
      }
      
      // Mettre à jour les compteurs par zone
      for (let i = 1; i <= 6; i++) {
        const countElement = document.getElementById(`zone${i}-count`);
        const barElement = document.getElementById(`zone${i}-bar`);
        const count = stats[`zone${i}Count`] || 0;
        
        if (countElement) {
          countElement.textContent = count;
        }
        
        if (barElement && stats.totalMemories > 0) {
          const percent = (count / stats.totalMemories) * 100;
          barElement.style.width = `${percent}%`;
        }
      }
    }
    
    function displayConversations(conversations) {
      if (!conversationsList) return;
      
      if (!conversations || conversations.length === 0) {
        conversationsList.innerHTML = `
          <div class="text-center p-3 text-muted small">
            Aucune conversation trouvée
          </div>
        `;
        return;
      }
      
      conversationsList.innerHTML = '';
      
      conversations.forEach(conv => {
        const date = new Date(conv.updated).toLocaleDateString();
        const isActive = conv.id === currentConversationId;
        
        // Couleur basée sur la température
        let tempColor = 'bg-primary';
        if (conv.temperature >= 90) {
          tempColor = 'bg-danger'; // Zone 1 (Récente)
        } else if (conv.temperature >= 70) {
          tempColor = 'bg-warning'; // Zone 2 (Chaude)
        } else if (conv.temperature >= 50) {
          tempColor = 'bg-info'; // Zone 3 (Tiède)
        } else if (conv.temperature >= 30) {
          tempColor = 'bg-purple'; // Zone 4 (Fraîche)
        } else if (conv.temperature >= 10) {
          tempColor = 'bg-secondary'; // Zone 5 (Froide)
        } else {
          tempColor = 'bg-dark'; // Zone 6 (Archive)
        }
        
        const item = document.createElement('a');
        item.href = '#';
        item.className = `list-group-item list-group-item-action d-flex justify-content-between align-items-start ${isActive ? 'active bg-primary' : 'bg-dark text-light'} border-secondary`;
        item.innerHTML = `
          <div class="ms-2 me-auto text-truncate">
            <div class="text-truncate">${conv.title}</div>
            <small class="text-muted">${date}</small>
          </div>
          <div class="d-flex align-items-center">
            <span class="badge rounded-pill ${tempColor} me-1" title="Zone ${conv.zone || 1}">${conv.temperature || 100}°</span>
            <span class="badge rounded-pill ${isActive ? 'bg-light text-dark' : 'bg-primary'}">${conv.messageCount}</span>
            <div class="dropdown ms-2">
              <button class="btn btn-sm btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="bi bi-three-dots-vertical"></i>
              </button>
              <ul class="dropdown-menu dropdown-menu-end bg-dark">
                <li><a class="dropdown-item text-light open-conv-btn" href="#" data-id="${conv.id}">Ouvrir</a></li>
                <li><a class="dropdown-item text-light hide-conv-btn" href="#" data-id="${conv.id}">Archiver</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item text-danger delete-conv-btn" href="#" data-id="${conv.id}">Supprimer</a></li>
              </ul>
            </div>
          </div>
        `;
        
        item.addEventListener('click', (e) => {
          const target = e.target;
          
          if (target.closest('.open-conv-btn')) {
            e.preventDefault();
            e.stopPropagation();
            openConversation(conv.id);
            return;
          }
          
          if (target.closest('.hide-conv-btn')) {
            e.preventDefault();
            e.stopPropagation();
            hideConversation(conv.id);
            return;
          }
          
          if (target.closest('.delete-conv-btn')) {
            e.preventDefault();
            e.stopPropagation();
            deleteConversation(conv.id);
            return;
          }
          
          if (!target.closest('.dropdown-toggle') && !target.closest('.dropdown-menu')) {
            openConversation(conv.id);
          }
        });
        
        conversationsList.appendChild(item);
      });
      
      displaySystemMessage(`${conversations.length} conversation(s) chargée(s)`);
    }
    
    function displayHiddenConversations(conversations) {
      if (!hiddenConversationsList) return;
      
      if (!conversations || conversations.length === 0) {
        hiddenConversationsList.innerHTML = `
          <div class="text-center p-3 text-muted small">
            Aucune conversation archivée
          </div>
        `;
        return;
      }
      
      hiddenConversationsList.innerHTML = '';
      
      conversations.forEach(conv => {
        const date = new Date(conv.updated).toLocaleDateString();
        
        // Couleur basée sur la température
        let tempColor = 'bg-primary';
        if (conv.temperature >= 90) {
          tempColor = 'bg-danger'; // Zone 1 (Récente)
        } else if (conv.temperature >= 70) {
          tempColor = 'bg-warning'; // Zone 2 (Chaude)
        } else if (conv.temperature >= 50) {
          tempColor = 'bg-info'; // Zone 3 (Tiède)
        } else if (conv.temperature >= 30) {
          tempColor = 'bg-purple'; // Zone 4 (Fraîche)
        } else if (conv.temperature >= 10) {
          tempColor = 'bg-secondary'; // Zone 5 (Froide)
        } else {
          tempColor = 'bg-dark'; // Zone 6 (Archive)
        }
        
        const item = document.createElement('div');
        item.className = 'list-group-item d-flex justify-content-between align-items-start bg-dark text-light border-secondary';
        item.innerHTML = `
          <div class="ms-2 me-auto text-truncate">
            <div class="text-truncate">${conv.title}</div>
            <small class="text-muted">${date}</small>
          </div>
          <div class="d-flex align-items-center">
            <span class="badge rounded-pill ${tempColor} me-1" title="Zone ${conv.zone || 1}">${conv.temperature || 100}°</span>
            <span class="badge rounded-pill bg-primary">${conv.messageCount}</span>
            <div class="btn-group ms-2">
              <button class="btn btn-sm btn-success restore-conv-btn" data-id="${conv.id}" title="Restaurer">
                <i class="bi bi-arrow-counterclockwise"></i>
              </button>
              <button class="btn btn-sm btn-danger delete-hidden-conv-btn" data-id="${conv.id}" title="Supprimer">
                <i class="bi bi-trash"></i>
              </button>
            </div>
          </div>
        `;
        
        // Ajouter les gestionnaires d'événements
        const restoreBtn = item.querySelector('.restore-conv-btn');
        if (restoreBtn) {
          restoreBtn.addEventListener('click', () => {
            restoreConversation(conv.id);
          });
        }
        
        const deleteBtn = item.querySelector('.delete-hidden-conv-btn');
        if (deleteBtn) {
          deleteBtn.addEventListener('click', () => {
            deleteConversation(conv.id);
          });
        }
        
        hiddenConversationsList.appendChild(item);
      });
    }
    
    function openConversation(id) {
      chatMessages.innerHTML = '';
      displayLoading();
      socket.emit('get conversation', { id });
    }
    
    function loadConversation(conversation) {
      if (!chatMessages) return;
      
      chatMessages.innerHTML = '';
      currentConversationId = conversation.id;
      currentChatTitle.textContent = conversation.title;
      
      if (conversation.messages && conversation.messages.length > 0) {
        conversation.messages.forEach(msg => {
          if (msg.role === 'user' || msg.role === 'assistant') {
            displayMessage(msg.content, msg.role === 'user' ? 'user' : 'bot');
          }
        });
      }
      
      displaySystemMessage(`Conversation "${conversation.title}" chargée`);
    }
    
    function startNewConversation() {
      chatMessages.innerHTML = '';
      currentChatTitle.textContent = 'Nouvelle conversation';
      socket.emit('new conversation');
    }
    
    function deleteConversation(id) {
      if (confirm('Êtes-vous sûr de vouloir supprimer définitivement cette conversation ?')) {
        socket.emit('delete conversation', { id });
      }
    }
    
    function hideConversation(id) {
      if (confirm('Archiver cette conversation dans la mémoire thermique ? Elle ne sera plus visible mais pourra être restaurée plus tard.')) {
        socket.emit('hide conversation', { id });
      }
    }
    
    function restoreConversation(id) {
      socket.emit('restore conversation', { id });
    }

    function checkOllamaStatus() {
      socket.emit('check ollama');
    }

    function updateOllamaStatus(data) {
      if (!ollamaStatus) return;

      if (data.available) {
        ollamaStatus.innerHTML = `<span class="badge bg-success">Ollama disponible (v${data.version || 'inconnue'})</span>`;
      } else {
        ollamaStatus.innerHTML = `<span class="badge bg-danger">Ollama indisponible</span>`;
        ollamaStatus.title = data.error || 'Service non disponible';
      }
    }

    function sendMessage() {
      if (!messageInput || !socket || isWaitingForResponse) return;
      
      const message = messageInput.value.trim();
      if (message === '') return;

      // Afficher le message de l'utilisateur
      displayMessage(message, 'user');
      
      // Préparer et envoyer la requête
      const data = {
        message,
        language: currentLanguage,
        temperature: temperatureSlider ? temperatureSlider.value : 0.7
      };
      
      console.log('Envoi du message:', data);
      socket.emit('chat message', data);
      
      // Réinitialiser l'entrée et mettre à jour l'état
      messageInput.value = '';
      isWaitingForResponse = true;
      
      // Afficher l'indicateur de chargement
      displayLoading();
    }

    function handleChatResponse(data) {
      // Supprimer l'indicateur de chargement
      removeLoading();
      
      // Extraire le contenu de la réponse
      let content = '';
      
      if (data.error) {
        content = `Erreur: ${data.error}`;
      } else if (data.message && data.message.content) {
        content = data.message.content;
        
        // Mettre à jour l'ID de conversation si présent
        if (data.conversationId) {
          currentConversationId = data.conversationId;
        }
      } else if (typeof data === 'string') {
        content = data;
      } else {
        content = "Désolé, je n'ai pas pu comprendre la réponse.";
      }
      
      // Afficher la réponse
      displayMessage(content, 'bot');
      
      // Mise à jour de l'état
      isWaitingForResponse = false;
    }

    function updateProcessingStatus(isProcessing) {
      if (isProcessing) {
        displayLoading();
      } else {
        removeLoading();
      }
    }

    function displayMessage(content, sender) {
      if (!chatMessages) return;
      
      const messageDiv = document.createElement('div');
      messageDiv.className = `message ${sender}-message`;
      
      // Si c'est un message du bot, traiter le markdown
      if (sender === 'bot') {
        messageDiv.innerHTML = `
          <div class="message-avatar">
            <i class="bi ${sender === 'user' ? 'bi-person-circle' : 'bi-braces-asterisk'}"></i>
          </div>
          <div class="message-content">
            ${marked.parse(content)}
          </div>
        `;
        
        // Ajouter des boutons de copie aux blocs de code
        messageDiv.querySelectorAll('pre').forEach(block => {
          const button = document.createElement('button');
          button.className = 'copy-button';
          button.textContent = 'Copier';
          button.onclick = function() {
            const code = block.querySelector('code').innerText;
            navigator.clipboard.writeText(code)
              .then(() => {
                button.textContent = 'Copié !';
                button.classList.add('copy-success');
                setTimeout(() => {
                  button.textContent = 'Copier';
                  button.classList.remove('copy-success');
                }, 2000);
              })
              .catch(err => {
                console.error('Erreur lors de la copie :', err);
                button.textContent = 'Erreur !';
                setTimeout(() => {
                  button.textContent = 'Copier';
                }, 2000);
              });
          };
          block.appendChild(button);
        });
      } else {
        messageDiv.innerHTML = `
          <div class="message-avatar">
            <i class="bi ${sender === 'user' ? 'bi-person-circle' : 'bi-braces-asterisk'}"></i>
          </div>
          <div class="message-content">
            <p>${content}</p>
          </div>
        `;
      }
      
      chatMessages.appendChild(messageDiv);
      
      // Appliquer la coloration syntaxique
      messageDiv.querySelectorAll('pre code').forEach((block) => {
        hljs.highlightElement(block);
      });
      
      // Défiler vers le bas
      chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    function displaySystemMessage(text) {
      const systemMessages = document.getElementById('system-messages');
      if (!systemMessages) return;
      
      const messageDiv = document.createElement('div');
      messageDiv.className = 'system-status-item mb-1';
      messageDiv.innerHTML = `
        <span class="text-purple-300"><i class="bi bi-dot"></i></span>
        ${text}
      `;
      
      // Ajouter la timestamp
      const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      const timeSpan = document.createElement('span');
      timeSpan.className = 'ms-1 text-muted small';
      timeSpan.textContent = timestamp;
      messageDiv.appendChild(timeSpan);
      
      systemMessages.appendChild(messageDiv);
      systemMessages.scrollTop = systemMessages.scrollHeight;
      
      // Animation flash pour indiquer un nouveau message
      const statusArea = document.getElementById('system-status-area');
      if (statusArea) {
        statusArea.classList.add('status-flash');
        setTimeout(() => statusArea.classList.remove('status-flash'), 300);
      }
    }

    function displayLoading() {
      if (!chatMessages) return;
      
      // Supprimer tout indicateur de chargement existant
      removeLoading();
      
      const loadingDiv = document.createElement('div');
      loadingDiv.className = 'message bot-message loading';
      loadingDiv.id = 'loading-indicator';
      loadingDiv.innerHTML = `
        <div class="message-avatar">
          <i class="bi bi-braces-asterisk"></i>
        </div>
        <div class="message-content">
          <div class="typing-indicator">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      `;
      
      chatMessages.appendChild(loadingDiv);
      chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    function removeLoading() {
      const loadingIndicator = document.getElementById('loading-indicator');
      if (loadingIndicator) {
        loadingIndicator.remove();
      }
    }

    // Exposer les fonctions pertinentes globalement
    window.checkOllamaStatus = checkOllamaStatus;
    window.startNewConversation = startNewConversation;
    window.loadConversations = loadConversations;
  });
  </script>
</body>
</html>
