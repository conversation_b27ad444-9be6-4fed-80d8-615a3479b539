<div class="container-fluid luna-container">
  <div class="row">
    <div class="col-12">
      <div class="card luna-card mb-4">
        <div class="card-header">
          <h5 class="card-title">
            <i class="bi bi-globe"></i> Accès Internet pour Vision Ultra
          </h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="card luna-inner-card mb-4">
                <div class="card-header">
                  <h6 class="card-title">
                    <i class="bi bi-gear-wide-connected"></i> Configuration de l'accès Internet
                  </h6>
                </div>
                <div class="card-body">
                  <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" id="internetAccessSwitch" checked>
                    <label class="form-check-label" for="internetAccessSwitch">Activer l'accès Internet</label>
                  </div>
                  
                  <div class="mb-3">
                    <label for="securityLevelSelect" class="form-label">Niveau de sécurité</label>
                    <select class="form-select" id="securityLevelSelect">
                      <option value="low">Faible - Accès complet</option>
                      <option value="medium" selected>Moyen - Restrictions basiques</option>
                      <option value="high">Élevé - Restrictions avancées</option>
                      <option value="maximum">Maximum - Accès très limité</option>
                    </select>
                  </div>
                  
                  <div class="mb-3">
                    <label for="requestsPerMinuteRange" class="form-label">Requêtes par minute: <span id="requestsPerMinuteValue">30</span></label>
                    <input type="range" class="form-range" min="5" max="60" step="5" value="30" id="requestsPerMinuteRange">
                  </div>
                  
                  <button class="btn btn-primary" id="saveInternetConfigBtn">
                    <i class="bi bi-save"></i> Enregistrer la configuration
                  </button>
                </div>
              </div>
              
              <div class="card luna-inner-card">
                <div class="card-header">
                  <h6 class="card-title">
                    <i class="bi bi-shield-check"></i> Sécurité Internet
                  </h6>
                </div>
                <div class="card-body">
                  <div class="d-grid gap-2">
                    <a href="/luna/security#vpn" class="btn btn-primary">
                      <i class="bi bi-shield-lock"></i> Configurer le VPN
                    </a>
                    <a href="/luna/security#antivirus" class="btn btn-success">
                      <i class="bi bi-virus"></i> Configurer l'antivirus
                    </a>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="col-md-6">
              <div class="card luna-inner-card mb-4">
                <div class="card-header">
                  <h6 class="card-title">
                    <i class="bi bi-search"></i> Test de recherche Internet
                  </h6>
                </div>
                <div class="card-body">
                  <div class="input-group mb-3">
                    <input type="text" class="form-control" placeholder="Entrez une requête de recherche..." id="searchQueryInput">
                    <button class="btn btn-primary" type="button" id="searchButton">
                      <i class="bi bi-search"></i> Rechercher
                    </button>
                  </div>
                  
                  <div class="search-results mt-3" id="searchResults">
                    <div class="alert alert-info">
                      Entrez une requête et cliquez sur Rechercher pour tester l'accès Internet.
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="card luna-inner-card">
                <div class="card-header">
                  <h6 class="card-title">
                    <i class="bi bi-activity"></i> Activité Internet
                  </h6>
                </div>
                <div class="card-body">
                  <div class="internet-activity-log" id="internetActivityLog">
                    <div class="activity-item">
                      <span class="activity-time"><%- new Date().toLocaleTimeString('fr-FR') %></span>
                      <span class="activity-text">Système d'accès Internet initialisé</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Pied de page fixe -->
<footer class="footer-fixed">
  <small>Vision Ultra - Interface Internet - <span id="current-datetime"></span> - &copy; 2024 Jean Passave - Sainte-Anne, Guadeloupe (97180)</small>
</footer>

<style>
  .luna-inner-card {
    background-color: rgba(26, 26, 46, 0.7);
    border: 1px solid rgba(184, 190, 221, 0.2);
  }
  
  .search-results {
    max-height: 300px;
    overflow-y: auto;
  }
  
  .internet-activity-log {
    max-height: 200px;
    overflow-y: auto;
  }
  
  .activity-item {
    padding: 8px 0;
    border-bottom: 1px solid rgba(184, 190, 221, 0.1);
  }
  
  .activity-time {
    color: #b8bedd;
    margin-right: 10px;
    font-size: 0.85rem;
  }
  
  .notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
  }
  
  /* Styles pour le pied de page fixe */
  .footer-fixed {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 40px;
    background-color: #1a1a2e;
    border-top: 1px solid rgba(184, 190, 221, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    color: #edf2fb;
  }
</style>

<script>
  $(document).ready(function() {
    // Vérifier si socket.io est disponible
    if (typeof io === 'undefined') {
      console.error('Socket.io n\'est pas chargé');
      $('#searchResults').html('<div class="alert alert-danger">Erreur: Socket.io n\'est pas disponible. Veuillez recharger la page.</div>');
      return;
    }
    
    // Initialiser socket.io
    const socket = io();
    
    // Vérifier si la connexion est établie
    socket.on('connect', function() {
      console.log('Connecté au serveur Socket.io');
      addActivityToLog('Connecté au serveur Socket.io');
    });
    
    socket.on('connect_error', function(error) {
      console.error('Erreur de connexion Socket.io:', error);
      $('#searchResults').html('<div class="alert alert-danger">Erreur de connexion au serveur. Veuillez recharger la page.</div>');
    });
    
    // Mettre à jour la date et l'heure
    function updateDateTime() {
      const now = new Date();
      $('#current-datetime').text(now.toLocaleString('fr-FR'));
    }
    
    // Mettre à jour la date et l'heure toutes les secondes
    updateDateTime();
    setInterval(updateDateTime, 1000);
    
    // Mettre à jour la valeur affichée pour les requêtes par minute
    $('#requestsPerMinuteRange').on('input', function() {
      $('#requestsPerMinuteValue').text($(this).val());
    });
    
    // Gérer le bouton de recherche
    $('#searchButton').click(function() {
      const query = $('#searchQueryInput').val().trim();
      
      if (!query) {
        return;
      }
      
      // Afficher un indicateur de chargement
      $('#searchResults').html('<div class="d-flex justify-content-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Chargement...</span></div></div>');
      
      // Émettre l'événement de recherche
      socket.emit('agent search internet', { query: query });
      
      // Ajouter l'activité au journal
      addActivityToLog(`Recherche: "${query}"`);
    });
    
    // Gérer le bouton de sauvegarde de la configuration
    $('#saveInternetConfigBtn').click(function() {
      const config = {
        internetAccess: $('#internetAccessSwitch').is(':checked'),
        securityLevel: $('#securityLevelSelect').val(),
        requestsPerMinute: parseInt($('#requestsPerMinuteRange').val())
      };
      
      // Émettre l'événement de configuration
      socket.emit('set agent config', config);
      
      // Ajouter l'activité au journal
      addActivityToLog(`Configuration mise à jour: Accès Internet ${config.internetAccess ? 'activé' : 'désactivé'}, Niveau de sécurité: ${config.securityLevel}`);
      
      // Afficher une notification
      showNotification('Configuration enregistrée avec succès', 'success');
    });
    
    // Écouter les résultats de recherche
    socket.on('agent internet search results', function(data) {
      if (data.success) {
        let resultsHtml = '<div class="list-group">';
        
        if (data.results.length === 0) {
          resultsHtml = '<div class="alert alert-warning">Aucun résultat trouvé.</div>';
        } else {
          data.results.forEach(result => {
            resultsHtml += `
              <a href="${result.url}" class="list-group-item list-group-item-action" target="_blank">
                <div class="d-flex w-100 justify-content-between">
                  <h6 class="mb-1">${result.title}</h6>
                </div>
                <p class="mb-1">${result.snippet}</p>
                <small class="text-muted">${result.url}</small>
              </a>
            `;
          });
          resultsHtml += '</div>';
        }
        
        $('#searchResults').html(resultsHtml);
      } else {
        $('#searchResults').html(`<div class="alert alert-danger">Erreur: ${data.error}</div>`);
      }
    });
    
    // Fonction pour ajouter une activité au journal
    function addActivityToLog(text) {
      const now = new Date();
      const timeString = now.toLocaleTimeString('fr-FR');
      
      const activityHtml = `
        <div class="activity-item">
          <span class="activity-time">${timeString}</span>
          <span class="activity-text">${text}</span>
        </div>
      `;
      
      $('#internetActivityLog').prepend(activityHtml);
    }
    
    // Fonction pour afficher une notification
    function showNotification(message, type = 'info') {
      const notification = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
          ${message}
          <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
      `;
      
      $('body').append(`<div class="notification-container">${notification}</div>`);
      
      setTimeout(() => {
        $('.notification-container').fadeOut(500, function() {
          $(this).remove();
        });
      }, 3000);
    }
    
    // Initialiser l'état de l'interface
    socket.emit('get agent status');
    
    // Écouter le statut de l'agent
    socket.on('agent status', function(data) {
      if (data.success) {
        $('#internetAccessSwitch').prop('checked', data.internetAccess);
        $('#securityLevelSelect').val(data.securityLevel || 'medium');
        
        // Ajouter l'activité au journal
        addActivityToLog(`Statut de l'agent récupéré: Accès Internet ${data.internetAccess ? 'activé' : 'désactivé'}`);
      }
    });
  });
</script>
