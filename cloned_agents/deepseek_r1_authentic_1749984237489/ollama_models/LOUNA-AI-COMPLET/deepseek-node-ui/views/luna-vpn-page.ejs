<!-- Styles spécifiques à la page VPN -->
<style>
  .vpn-container {
    background: rgba(26, 26, 46, 0.8);
    border-radius: 15px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 70vh;
    min-height: 500px;
    box-shadow: 0 10px 25px rgba(0,0,0,0.3);
    border: 1px solid rgba(184, 190, 221, 0.2);
  }

  .vpn-header {
    background: rgba(20, 20, 35, 0.9);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(184, 190, 221, 0.2);
  }

  .vpn-title {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 600;
  }

  .vpn-title i {
    margin-right: 1rem;
    font-size: 2rem;
    color: var(--luna-secondary);
  }

  .vpn-status {
    display: flex;
    align-items: center;
    padding: 0.5rem 1.5rem;
    border-radius: 25px;
    background: rgba(0, 0, 0, 0.3);
    font-weight: 500;
  }

  .vpn-status.connected {
    background: rgba(76, 175, 80, 0.2);
    border: 1px solid rgba(76, 175, 80, 0.5);
  }

  .vpn-status.disconnected {
    background: rgba(244, 67, 54, 0.2);
    border: 1px solid rgba(244, 67, 54, 0.5);
  }

  .vpn-status i {
    margin-right: 0.5rem;
  }

  .vpn-content {
    display: flex;
    flex-grow: 1;
    overflow: hidden;
  }

  .vpn-sidebar {
    width: 250px;
    background: rgba(20, 20, 35, 0.7);
    padding: 1.5rem;
    border-right: 1px solid rgba(184, 190, 221, 0.2);
    overflow-y: auto;
  }

  .vpn-main {
    flex-grow: 1;
    padding: 1.5rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
  }

  .vpn-connect-btn {
    width: 100%;
    padding: 1rem;
    margin-bottom: 2rem;
    border-radius: 10px;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
  }

  .vpn-connect-btn i {
    margin-right: 0.5rem;
    font-size: 1.5rem;
  }

  .vpn-connect-btn.connect {
    background: var(--luna-gradient);
    color: white;
    border: none;
  }

  .vpn-connect-btn.disconnect {
    background: rgba(244, 67, 54, 0.8);
    color: white;
    border: none;
  }

  .vpn-connect-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  }

  .vpn-section {
    margin-bottom: 2rem;
  }

  .vpn-section-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(184, 190, 221, 0.2);
  }

  .vpn-server-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .vpn-server-item {
    padding: 0.8rem 1rem;
    margin-bottom: 0.5rem;
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
  }

  .vpn-server-item:hover {
    background: rgba(156, 137, 184, 0.2);
    transform: translateY(-2px);
  }

  .vpn-server-item.active {
    background: rgba(156, 137, 184, 0.3);
    border-left: 3px solid var(--luna-secondary);
  }

  .vpn-server-flag {
    width: 24px;
    height: 24px;
    margin-right: 0.8rem;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
  }

  .vpn-server-info {
    flex-grow: 1;
  }

  .vpn-server-name {
    font-weight: 500;
    margin-bottom: 0.2rem;
  }

  .vpn-server-ping {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
  }

  .vpn-server-signal {
    margin-left: 0.5rem;
    color: rgba(76, 175, 80, 0.8);
  }

  .vpn-stats {
    display: flex;
    margin-bottom: 2rem;
  }

  .vpn-stat-card {
    flex: 1;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    padding: 1.5rem;
    margin-right: 1rem;
    text-align: center;
    transition: all 0.3s ease;
  }

  .vpn-stat-card:last-child {
    margin-right: 0;
  }

  .vpn-stat-card:hover {
    transform: translateY(-3px);
    background: rgba(0, 0, 0, 0.3);
  }

  .vpn-stat-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--luna-secondary);
  }

  .vpn-stat-value {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
  }

  .vpn-stat-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
  }

  .vpn-map-container {
    flex-grow: 1;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    padding: 1rem;
    position: relative;
    min-height: 300px;
    overflow: hidden;
  }

  .vpn-map {
    width: 100%;
    height: 100%;
    background-image: url('/images/world-map-dark.png');
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
  }

  .vpn-map-point {
    position: absolute;
    width: 12px;
    height: 12px;
    background: var(--luna-secondary);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 0 10px var(--luna-secondary);
    animation: pulse 2s infinite;
  }

  .vpn-map-connection {
    position: absolute;
    height: 2px;
    background: linear-gradient(90deg, rgba(156, 137, 184, 0.8), rgba(240, 166, 202, 0.8));
    transform-origin: left center;
    box-shadow: 0 0 10px rgba(240, 166, 202, 0.5);
  }

  .vpn-settings {
    margin-top: 2rem;
  }

  .vpn-settings-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .vpn-setting-item {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .vpn-setting-label {
    font-weight: 500;
  }

  .vpn-setting-toggle {
    position: relative;
    width: 50px;
    height: 26px;
  }

  .vpn-setting-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .vpn-setting-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3);
    transition: .4s;
    border-radius: 34px;
  }

  .vpn-setting-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
  }

  input:checked + .vpn-setting-slider {
    background: var(--luna-gradient);
  }

  input:checked + .vpn-setting-slider:before {
    transform: translateX(24px);
  }

  .vpn-log {
    margin-top: 2rem;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 1rem;
    height: 150px;
    overflow-y: auto;
    font-family: monospace;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
  }

  .vpn-log-entry {
    margin-bottom: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .vpn-log-time {
    color: var(--luna-secondary);
    margin-right: 0.5rem;
  }

  .vpn-log-message {
    color: rgba(255, 255, 255, 0.9);
  }

  .vpn-log-error {
    color: #ff5f56;
  }

  .vpn-log-success {
    color: #4caf50;
  }

  .vpn-log-warning {
    color: #ffbd2e;
  }

  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(240, 166, 202, 0.7);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(240, 166, 202, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(240, 166, 202, 0);
    }
  }
</style>

<!-- Contenu principal -->
<div class="row">
  <div class="col-md-12 mb-4">
    <div class="d-flex justify-content-between align-items-center">
      <h2><i class="bi bi-shield-lock me-2"></i> Service VPN Luna</h2>
      <div>
        <a href="/luna/mcp" class="btn btn-luna-outline me-2">
          <i class="bi bi-cpu me-2"></i> MCP
        </a>
        <button id="refresh-vpn" class="btn btn-luna">
          <i class="bi bi-arrow-clockwise me-2"></i> Actualiser
        </button>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-12">
    <div class="vpn-container">
      <div class="vpn-header">
        <div class="vpn-title">
          <i class="bi bi-shield-lock"></i>
          <span>Luna VPN</span>
        </div>
        <div id="vpn-status" class="vpn-status disconnected">
          <i class="bi bi-shield-x"></i>
          <span>Déconnecté</span>
        </div>
      </div>

      <div class="vpn-content">
        <!-- Sidebar avec liste des serveurs -->
        <div class="vpn-sidebar">
          <button id="vpn-connect-btn" class="vpn-connect-btn connect">
            <i class="bi bi-shield-check"></i>
            <span>Connecter</span>
          </button>

          <div class="vpn-section">
            <div class="vpn-section-title">Serveurs VPN</div>
            <ul class="vpn-server-list" id="vpn-server-list">
              <li class="vpn-server-item active" data-server="fr">
                <div class="vpn-server-flag">FR</div>
                <div class="vpn-server-info">
                  <div class="vpn-server-name">France</div>
                  <div class="vpn-server-ping">Ping: 15ms</div>
                </div>
                <div class="vpn-server-signal">
                  <i class="bi bi-reception-4"></i>
                </div>
              </li>
              <li class="vpn-server-item" data-server="us">
                <div class="vpn-server-flag">US</div>
                <div class="vpn-server-info">
                  <div class="vpn-server-name">États-Unis</div>
                  <div class="vpn-server-ping">Ping: 120ms</div>
                </div>
                <div class="vpn-server-signal">
                  <i class="bi bi-reception-3"></i>
                </div>
              </li>
              <li class="vpn-server-item" data-server="jp">
                <div class="vpn-server-flag">JP</div>
                <div class="vpn-server-info">
                  <div class="vpn-server-name">Japon</div>
                  <div class="vpn-server-ping">Ping: 280ms</div>
                </div>
                <div class="vpn-server-signal">
                  <i class="bi bi-reception-2"></i>
                </div>
              </li>
              <li class="vpn-server-item" data-server="uk">
                <div class="vpn-server-flag">UK</div>
                <div class="vpn-server-info">
                  <div class="vpn-server-name">Royaume-Uni</div>
                  <div class="vpn-server-ping">Ping: 45ms</div>
                </div>
                <div class="vpn-server-signal">
                  <i class="bi bi-reception-4"></i>
                </div>
              </li>
              <li class="vpn-server-item" data-server="de">
                <div class="vpn-server-flag">DE</div>
                <div class="vpn-server-info">
                  <div class="vpn-server-name">Allemagne</div>
                  <div class="vpn-server-ping">Ping: 30ms</div>
                </div>
                <div class="vpn-server-signal">
                  <i class="bi bi-reception-4"></i>
                </div>
              </li>
              <li class="vpn-server-item" data-server="sg">
                <div class="vpn-server-flag">SG</div>
                <div class="vpn-server-info">
                  <div class="vpn-server-name">Singapour</div>
                  <div class="vpn-server-ping">Ping: 250ms</div>
                </div>
                <div class="vpn-server-signal">
                  <i class="bi bi-reception-2"></i>
                </div>
              </li>
              <li class="vpn-server-item" data-server="ca">
                <div class="vpn-server-flag">CA</div>
                <div class="vpn-server-info">
                  <div class="vpn-server-name">Canada</div>
                  <div class="vpn-server-ping">Ping: 110ms</div>
                </div>
                <div class="vpn-server-signal">
                  <i class="bi bi-reception-3"></i>
                </div>
              </li>
            </ul>
          </div>
        </div>

        <!-- Contenu principal -->
        <div class="vpn-main">
          <!-- Statistiques VPN -->
          <div class="vpn-stats">
            <div class="vpn-stat-card">
              <div class="vpn-stat-icon"><i class="bi bi-shield-check"></i></div>
              <div class="vpn-stat-value" id="vpn-protection">0%</div>
              <div class="vpn-stat-label">Protection</div>
            </div>
            <div class="vpn-stat-card">
              <div class="vpn-stat-icon"><i class="bi bi-arrow-down"></i></div>
              <div class="vpn-stat-value" id="vpn-download">0 MB/s</div>
              <div class="vpn-stat-label">Téléchargement</div>
            </div>
            <div class="vpn-stat-card">
              <div class="vpn-stat-icon"><i class="bi bi-arrow-up"></i></div>
              <div class="vpn-stat-value" id="vpn-upload">0 MB/s</div>
              <div class="vpn-stat-label">Envoi</div>
            </div>
            <div class="vpn-stat-card">
              <div class="vpn-stat-icon"><i class="bi bi-clock"></i></div>
              <div class="vpn-stat-value" id="vpn-uptime">00:00:00</div>
              <div class="vpn-stat-label">Temps de connexion</div>
            </div>
          </div>

          <!-- Carte du monde avec connexion VPN -->
          <div class="vpn-map-container">
            <div class="vpn-map" id="vpn-map">
              <!-- Points et connexions ajoutés dynamiquement par JavaScript -->
            </div>
          </div>

          <!-- Paramètres VPN -->
          <div class="vpn-settings">
            <div class="vpn-section-title">Paramètres VPN</div>
            <div class="vpn-settings-grid">
              <div class="vpn-setting-item">
                <div class="vpn-setting-label">Kill Switch</div>
                <label class="vpn-setting-toggle">
                  <input type="checkbox" id="setting-killswitch">
                  <span class="vpn-setting-slider"></span>
                </label>
              </div>
              <div class="vpn-setting-item">
                <div class="vpn-setting-label">Connexion automatique</div>
                <label class="vpn-setting-toggle">
                  <input type="checkbox" id="setting-autoconnect">
                  <span class="vpn-setting-slider"></span>
                </label>
              </div>
              <div class="vpn-setting-item">
                <div class="vpn-setting-label">Bloquer les publicités</div>
                <label class="vpn-setting-toggle">
                  <input type="checkbox" id="setting-adblock" checked>
                  <span class="vpn-setting-slider"></span>
                </label>
              </div>
              <div class="vpn-setting-item">
                <div class="vpn-setting-label">Anti-tracking</div>
                <label class="vpn-setting-toggle">
                  <input type="checkbox" id="setting-antitracking" checked>
                  <span class="vpn-setting-slider"></span>
                </label>
              </div>
            </div>
          </div>

          <!-- Journal VPN -->
          <div class="vpn-log" id="vpn-log">
            <div class="vpn-log-entry">
              <span class="vpn-log-time">[<%=new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit', second:'2-digit'})%>]</span>
              <span class="vpn-log-message">Initialisation du service VPN Luna...</span>
            </div>
            <div class="vpn-log-entry">
              <span class="vpn-log-time">[<%=new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit', second:'2-digit'})%>]</span>
              <span class="vpn-log-message">Chargement des serveurs VPN disponibles...</span>
            </div>
            <div class="vpn-log-entry">
              <span class="vpn-log-time">[<%=new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit', second:'2-digit'})%>]</span>
              <span class="vpn-log-message vpn-log-success">7 serveurs VPN disponibles et prêts à l'emploi.</span>
            </div>
            <div class="vpn-log-entry">
              <span class="vpn-log-time">[<%=new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit', second:'2-digit'})%>]</span>
              <span class="vpn-log-message">Service VPN prêt. En attente de connexion...</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Scripts spécifiques à la page VPN -->
<script>
  $(document).ready(function() {
    // Variables globales
    let isConnected = false;
    let selectedServer = 'fr';
    let connectionStartTime = null;
    let uptimeInterval = null;
    let statsInterval = null;
    let mapPoints = [];
    let mapConnection = null;

    // Coordonnées des serveurs sur la carte (en pourcentage)
    const serverCoordinates = {
      'fr': { x: 48, y: 40 },
      'us': { x: 25, y: 40 },
      'jp': { x: 82, y: 40 },
      'uk': { x: 45, y: 35 },
      'de': { x: 50, y: 37 },
      'sg': { x: 75, y: 55 },
      'ca': { x: 20, y: 35 }
    };

    // Coordonnées de l'utilisateur (Guadeloupe)
    const userCoordinates = { x: 30, y: 50 };

    // Initialiser la carte
    initMap();

    // Gestionnaire d'événements pour le bouton de connexion
    $('#vpn-connect-btn').on('click', function() {
      if (isConnected) {
        disconnectVPN();
      } else {
        connectVPN();
      }
    });

    // Gestionnaire d'événements pour la sélection de serveur
    $('.vpn-server-item').on('click', function() {
      // Si déjà connecté, déconnecter d'abord
      if (isConnected) {
        disconnectVPN();
      }

      // Mettre à jour le serveur sélectionné
      $('.vpn-server-item').removeClass('active');
      $(this).addClass('active');
      selectedServer = $(this).data('server');

      // Mettre à jour la carte
      updateMap();

      // Ajouter un message au journal
      addLogMessage(`Serveur ${getServerName(selectedServer)} sélectionné.`);
    });

    // Gestionnaire d'événements pour le bouton d'actualisation
    $('#refresh-vpn').on('click', function() {
      $(this).html('<i class="bi bi-arrow-clockwise me-2 spin"></i> Actualisation...');

      // Simuler une actualisation des serveurs
      setTimeout(() => {
        $(this).html('<i class="bi bi-arrow-clockwise me-2"></i> Actualiser');
        addLogMessage('Liste des serveurs VPN actualisée.');

        // Mettre à jour les pings des serveurs de manière aléatoire
        $('.vpn-server-item').each(function() {
          const ping = Math.floor(Math.random() * 300) + 10;
          $(this).find('.vpn-server-ping').text(`Ping: ${ping}ms`);

          // Mettre à jour l'indicateur de signal en fonction du ping
          let signalIcon = 'bi-reception-4';
          if (ping > 200) {
            signalIcon = 'bi-reception-2';
          } else if (ping > 100) {
            signalIcon = 'bi-reception-3';
          }

          $(this).find('.vpn-server-signal i').attr('class', `bi ${signalIcon}`);
        });
      }, 1500);
    });

    // Gestionnaire d'événements pour les paramètres
    $('.vpn-setting-toggle input').on('change', function() {
      const settingId = $(this).attr('id');
      const isEnabled = $(this).prop('checked');
      const settingName = $(this).closest('.vpn-setting-item').find('.vpn-setting-label').text();

      addLogMessage(`Paramètre "${settingName}" ${isEnabled ? 'activé' : 'désactivé'}.`);

      // Si le kill switch est activé et que le VPN est connecté, afficher un avertissement
      if (settingId === 'setting-killswitch' && isEnabled && isConnected) {
        addLogMessage('Kill Switch activé. La connexion Internet sera coupée si le VPN se déconnecte.', 'warning');
      }
    });

    // Fonction pour connecter le VPN
    function connectVPN() {
      // Afficher l'animation de connexion
      $('#vpn-status').removeClass('disconnected').addClass('connecting');
      $('#vpn-status i').attr('class', 'bi bi-arrow-repeat spin');
      $('#vpn-status span').text('Connexion en cours...');

      // Désactiver le bouton pendant la connexion
      $('#vpn-connect-btn').prop('disabled', true);

      // Ajouter un message au journal
      addLogMessage(`Connexion au serveur VPN ${getServerName(selectedServer)} en cours...`);

      // Simuler le temps de connexion
      setTimeout(() => {
        // Mettre à jour l'état de connexion
        isConnected = true;
        connectionStartTime = new Date();

        // Mettre à jour l'interface
        $('#vpn-status').removeClass('connecting').addClass('connected');
        $('#vpn-status i').attr('class', 'bi bi-shield-check');
        $('#vpn-status span').text(`Connecté à ${getServerName(selectedServer)}`);

        // Mettre à jour le bouton de connexion
        $('#vpn-connect-btn').removeClass('connect').addClass('disconnect');
        $('#vpn-connect-btn i').attr('class', 'bi bi-shield-x');
        $('#vpn-connect-btn span').text('Déconnecter');
        $('#vpn-connect-btn').prop('disabled', false);

        // Mettre à jour la carte
        updateMap(true);

        // Ajouter un message au journal
        addLogMessage(`Connecté au serveur VPN ${getServerName(selectedServer)}.`, 'success');

        // Mettre à jour les statistiques
        $('#vpn-protection').text('100%');
        updateStats();

        // Démarrer les intervalles de mise à jour
        startIntervals();
      }, 2000);
    }

    // Fonction pour déconnecter le VPN
    function disconnectVPN() {
      // Afficher l'animation de déconnexion
      $('#vpn-status').removeClass('connected').addClass('disconnecting');
      $('#vpn-status i').attr('class', 'bi bi-arrow-repeat spin');
      $('#vpn-status span').text('Déconnexion en cours...');

      // Désactiver le bouton pendant la déconnexion
      $('#vpn-connect-btn').prop('disabled', true);

      // Ajouter un message au journal
      addLogMessage('Déconnexion du VPN en cours...');

      // Simuler le temps de déconnexion
      setTimeout(() => {
        // Mettre à jour l'état de connexion
        isConnected = false;
        connectionStartTime = null;

        // Mettre à jour l'interface
        $('#vpn-status').removeClass('disconnecting').addClass('disconnected');
        $('#vpn-status i').attr('class', 'bi bi-shield-x');
        $('#vpn-status span').text('Déconnecté');

        // Mettre à jour le bouton de connexion
        $('#vpn-connect-btn').removeClass('disconnect').addClass('connect');
        $('#vpn-connect-btn i').attr('class', 'bi bi-shield-check');
        $('#vpn-connect-btn span').text('Connecter');
        $('#vpn-connect-btn').prop('disabled', false);

        // Mettre à jour la carte
        updateMap(false);

        // Ajouter un message au journal
        addLogMessage('Déconnecté du VPN.', 'warning');

        // Réinitialiser les statistiques
        $('#vpn-protection').text('0%');
        $('#vpn-download').text('0 MB/s');
        $('#vpn-upload').text('0 MB/s');
        $('#vpn-uptime').text('00:00:00');

        // Arrêter les intervalles de mise à jour
        stopIntervals();
      }, 1500);
    }

    // Fonction pour initialiser la carte
    function initMap() {
      // Ajouter le point de l'utilisateur
      const userPoint = $('<div class="vpn-map-point"></div>').css({
        left: `${userCoordinates.x}%`,
        top: `${userCoordinates.y}%`
      });

      $('#vpn-map').append(userPoint);
      mapPoints.push(userPoint);

      // Ajouter les points des serveurs
      for (const server in serverCoordinates) {
        const serverPoint = $('<div class="vpn-map-point"></div>').css({
          left: `${serverCoordinates[server].x}%`,
          top: `${serverCoordinates[server].y}%`,
          background: 'rgba(156, 137, 184, 0.8)',
          boxShadow: '0 0 10px rgba(156, 137, 184, 0.8)'
        });

        $('#vpn-map').append(serverPoint);
        mapPoints.push(serverPoint);
      }

      // Mettre à jour la carte
      updateMap();
    }

    // Fonction pour mettre à jour la carte
    function updateMap(connected = false) {
      // Supprimer la connexion existante
      if (mapConnection) {
        mapConnection.remove();
        mapConnection = null;
      }

      // Si connecté, ajouter une ligne de connexion
      if (connected) {
        const serverCoords = serverCoordinates[selectedServer];

        // Calculer la distance et l'angle entre l'utilisateur et le serveur
        const dx = serverCoords.x - userCoordinates.x;
        const dy = serverCoords.y - userCoordinates.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        const angle = Math.atan2(dy, dx) * (180 / Math.PI);

        // Créer la ligne de connexion
        mapConnection = $('<div class="vpn-map-connection"></div>').css({
          left: `${userCoordinates.x}%`,
          top: `${userCoordinates.y}%`,
          width: `${distance}%`,
          transform: `rotate(${angle}deg)`
        });

        $('#vpn-map').append(mapConnection);
      }
    }

    // Fonction pour démarrer les intervalles de mise à jour
    function startIntervals() {
      // Intervalle pour mettre à jour le temps de connexion
      uptimeInterval = setInterval(updateUptime, 1000);

      // Intervalle pour mettre à jour les statistiques
      statsInterval = setInterval(updateStats, 3000);
    }

    // Fonction pour arrêter les intervalles de mise à jour
    function stopIntervals() {
      clearInterval(uptimeInterval);
      clearInterval(statsInterval);
      uptimeInterval = null;
      statsInterval = null;
    }

    // Fonction pour mettre à jour le temps de connexion
    function updateUptime() {
      if (!connectionStartTime) return;

      const now = new Date();
      const diff = now - connectionStartTime;

      // Convertir en heures, minutes, secondes
      const hours = Math.floor(diff / 3600000).toString().padStart(2, '0');
      const minutes = Math.floor((diff % 3600000) / 60000).toString().padStart(2, '0');
      const seconds = Math.floor((diff % 60000) / 1000).toString().padStart(2, '0');

      $('#vpn-uptime').text(`${hours}:${minutes}:${seconds}`);
    }

    // Fonction pour mettre à jour les statistiques
    function updateStats() {
      if (!isConnected) return;

      // Générer des valeurs aléatoires pour les statistiques
      const downloadSpeed = (Math.random() * 10 + 5).toFixed(1);
      const uploadSpeed = (Math.random() * 5 + 1).toFixed(1);

      $('#vpn-download').text(`${downloadSpeed} MB/s`);
      $('#vpn-upload').text(`${uploadSpeed} MB/s`);

      // Ajouter un message au journal de temps en temps
      if (Math.random() < 0.3) {
        const messages = [
          'Trafic chiffré envoyé via le tunnel VPN.',
          `Vitesse de téléchargement: ${downloadSpeed} MB/s`,
          `Vitesse d'envoi: ${uploadSpeed} MB/s`,
          'Connexion VPN stable.',
          'Adresse IP masquée avec succès.'
        ];

        addLogMessage(messages[Math.floor(Math.random() * messages.length)]);
      }
    }

    // Fonction pour ajouter un message au journal
    function addLogMessage(message, type = '') {
      const now = new Date();
      const timeStr = now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit', second:'2-digit'});

      const logEntry = $('<div class="vpn-log-entry"></div>');
      logEntry.append(`<span class="vpn-log-time">[${timeStr}]</span>`);

      const messageSpan = $('<span class="vpn-log-message"></span>').text(message);

      if (type === 'success') {
        messageSpan.addClass('vpn-log-success');
      } else if (type === 'error') {
        messageSpan.addClass('vpn-log-error');
      } else if (type === 'warning') {
        messageSpan.addClass('vpn-log-warning');
      }

      logEntry.append(messageSpan);

      // Ajouter au journal et faire défiler vers le bas
      $('#vpn-log').append(logEntry);
      $('#vpn-log').scrollTop($('#vpn-log')[0].scrollHeight);

      // Limiter le nombre d'entrées dans le journal
      if ($('#vpn-log .vpn-log-entry').length > 100) {
        $('#vpn-log .vpn-log-entry:first').remove();
      }
    }

    // Fonction pour obtenir le nom du serveur à partir de son code
    function getServerName(code) {
      const serverNames = {
        'fr': 'France',
        'us': 'États-Unis',
        'jp': 'Japon',
        'uk': 'Royaume-Uni',
        'de': 'Allemagne',
        'sg': 'Singapour',
        'ca': 'Canada'
      };

      return serverNames[code] || code;
    }

    // Ajouter des styles pour l'animation de rotation
    $('<style>')
      .text(`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .spin {
          animation: spin 1s linear infinite;
        }

        .vpn-status.connecting, .vpn-status.disconnecting {
          background: rgba(255, 193, 7, 0.2);
          border: 1px solid rgba(255, 193, 7, 0.5);
        }
      `)
      .appendTo('head');
  });
</script>