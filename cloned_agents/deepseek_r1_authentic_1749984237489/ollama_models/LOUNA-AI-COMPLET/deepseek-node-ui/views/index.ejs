<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %></title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github-dark.min.css">
  <link rel="stylesheet" href="/css/style.css">
  <style>
    .nav-link {
      transition: all 0.3s ease;
    }
    .nav-link:hover {
      transform: translateY(-2px);
    }
    .nav-pills .nav-link.active {
      background-color: #0d6efd;
    }
    .navbar {
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    }
  </style>
</head>
<body class="bg-dark text-light">
  <div class="container-fluid">
    <div class="row">
      <!-- Navigation Bar -->
      <nav class="navbar navbar-expand-lg navbar-dark bg-dark border-bottom border-secondary mb-3">
        <div class="container-fluid">
          <a class="navbar-brand d-flex align-items-center" href="/">
            <i class="bi bi-robot fs-3 me-2"></i>
            <span>DeepSeek r1</span>
            <span class="badge bg-success ms-2">Local</span>
          </a>
          <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
          </button>
          <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav ms-auto">
              <li class="nav-item">
                <a class="nav-link active" href="/">
                  <i class="bi bi-chat-dots me-1"></i> Chat
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="/book-analysis">
                  <i class="bi bi-book me-1"></i> Analyse de Livres
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="/memory">
                  <i class="bi bi-brain me-1"></i> Mémoire Thermique
                </a>
              </li>
              <li class="nav-item">
                <div id="ollama-status-nav" class="nav-link">
                  <div class="d-flex align-items-center">
                    <div class="spinner-border spinner-border-sm me-1" role="status"></div>
                    <small>Vérification d'Ollama...</small>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </nav>

      <!-- Sidebar -->
      <div class="col-md-3 col-lg-2 sidebar p-3">
        <div class="mb-4">
          <button class="btn btn-primary w-100 mb-3" id="new-chat-btn">
            <i class="bi bi-plus-circle me-2"></i>Nouvelle conversation
          </button>

          <div id="ollama-status" class="alert alert-info mb-3">
            <div class="d-flex align-items-center">
              <div class="spinner-border spinner-border-sm me-2" role="status"></div>
              <div>Vérification du statut d'Ollama...</div>
            </div>
          </div>
        </div>

        <div class="mb-4">
          <h5>Modèles disponibles</h5>
          <div id="models-container" class="mb-3">
            <div class="d-flex justify-content-between mb-2">
              <select class="form-select bg-dark text-light border-secondary" id="model-selector">
                <option value="">Chargement des modèles...</option>
              </select>
              <button class="btn btn-outline-light ms-2" id="refresh-models-btn">
                <i class="bi bi-arrow-clockwise"></i>
              </button>
            </div>

            <button class="btn btn-success w-100 mb-2" id="download-model-btn">
              <i class="bi bi-download me-2"></i>Télécharger un modèle
            </button>

            <div class="form-text text-muted small">
              Modèles recommandés: deepseek-r1:7b, deepseek-r1:14b
            </div>
          </div>
        </div>

        <div class="mb-4">
          <h5>Paramètres du modèle</h5>
          <div class="form-group mb-3">
            <label for="temperature" class="form-label d-flex justify-content-between">
              <span>Température</span>
              <span id="temperature-value">0.7</span>
            </label>
            <input type="range" class="form-range" id="temperature" min="0" max="1" step="0.1" value="0.7">
            <small class="form-text text-muted">Contrôle la créativité des réponses</small>
          </div>

          <div class="form-group mb-3">
            <label for="max-tokens" class="form-label d-flex justify-content-between">
              <span>Longueur max</span>
              <span id="max-tokens-value">1000</span>
            </label>
            <input type="range" class="form-range" id="max-tokens" min="100" max="4000" step="100" value="1000">
            <small class="form-text text-muted">Limite la longueur des réponses</small>
          </div>

          <div class="form-check form-switch mb-3">
            <input class="form-check-input" type="checkbox" id="use-memory" checked>
            <label class="form-check-label" for="use-memory">Utiliser la mémoire thermique</label>
            <small class="form-text text-muted d-block">Enrichit les réponses avec la mémoire de l'agent</small>
          </div>

          <button class="btn btn-outline-light w-100" id="save-settings-btn">
            <i class="bi bi-save me-2"></i>Sauvegarder les paramètres
          </button>
        </div>

        <div class="conversations-list">
          <h5>Conversations récentes</h5>
          <ul class="list-group list-group-flush" id="conversations-list">
            <!-- Les conversations seront ajoutées ici dynamiquement -->
          </ul>
        </div>
      </div>

      <!-- Main content -->
      <div class="col-md-9 col-lg-10 main-content p-0">
        <div class="chat-container d-flex flex-column">
          <div class="chat-header p-3 border-bottom border-secondary">
            <div class="d-flex justify-content-between align-items-center">
              <h4 id="current-chat-title">Nouvelle conversation</h4>
              <div>
                <button class="btn btn-outline-light btn-sm me-2" id="export-chat-btn">
                  <i class="bi bi-download me-1"></i>Exporter
                </button>
                <button class="btn btn-outline-danger btn-sm" id="clear-chat-btn">
                  <i class="bi bi-trash me-1"></i>Effacer
                </button>
              </div>
            </div>
          </div>

          <div class="chat-messages p-3 flex-grow-1" id="chat-messages">
            <div class="welcome-message text-center my-5">
              <i class="bi bi-robot display-1 mb-3"></i>
              <h2>Bienvenue sur l'interface DeepSeek r1 Local</h2>
              <p class="lead">Commencez à discuter avec l'agent DeepSeek r1 en utilisant Ollama en local.</p>
              <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i>
                Vérification du statut d'Ollama en cours...
              </div>
            </div>
            <!-- Les messages seront ajoutés ici dynamiquement -->
          </div>

          <div class="chat-input p-3 border-top border-secondary">
            <form id="chat-form" onsubmit="return false;">
              <div class="input-group">
                <textarea class="form-control bg-dark text-light border-secondary" id="message-input" placeholder="Entrez votre message..." rows="2"></textarea>
                <button class="btn btn-primary" type="button" id="send-button" onclick="sendChatMessage()">
                  <i class="bi bi-send"></i>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal pour télécharger un modèle -->
  <div class="modal fade" id="download-model-modal" tabindex="-1" aria-labelledby="download-model-modal-label" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content bg-dark text-light">
        <div class="modal-header">
          <h5 class="modal-title" id="download-model-modal-label">Télécharger un modèle DeepSeek r1</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <p>Sélectionnez un modèle DeepSeek r1 à télécharger. Les modèles plus grands offrent de meilleures performances mais nécessitent plus de ressources.</p>

          <div class="list-group mb-3">
            <button type="button" class="list-group-item list-group-item-action bg-dark text-light border-secondary" data-model="deepseek-r1:1.5b">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <strong>DeepSeek r1 1.5B</strong>
                  <div class="small text-muted">Taille: 1.1 Go</div>
                </div>
                <span class="badge bg-info">Léger</span>
              </div>
            </button>
            <button type="button" class="list-group-item list-group-item-action bg-dark text-light border-secondary" data-model="deepseek-r1:7b">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <strong>DeepSeek r1 7B</strong>
                  <div class="small text-muted">Taille: 4.7 Go</div>
                </div>
                <span class="badge bg-success">Recommandé</span>
              </div>
            </button>
            <button type="button" class="list-group-item list-group-item-action bg-dark text-light border-secondary" data-model="deepseek-r1:14b">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <strong>DeepSeek r1 14B</strong>
                  <div class="small text-muted">Taille: 9.0 Go</div>
                </div>
                <span class="badge bg-warning">Avancé</span>
              </div>
            </button>
          </div>

          <div class="form-group">
            <label for="custom-model-name" class="form-label">Ou entrez un nom de modèle personnalisé:</label>
            <input type="text" class="form-control bg-dark text-light border-secondary" id="custom-model-name" placeholder="Exemple: deepseek-r1:32b">
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
          <button type="button" class="btn btn-primary" id="confirm-download-btn">Télécharger</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/4.3.0/marked.min.js"></script>
  <script src="/js/model-manager.js"></script>
  <script src="/js/chat-handler.js"></script>
  <script src="/js/main-ollama.js"></script>
</body>
</html>
