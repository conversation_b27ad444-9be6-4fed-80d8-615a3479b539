<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Mémoire Thermique - DeepSeek r1</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github-dark.min.css">
  <link rel="stylesheet" href="/css/style.css">
  <style>
    .nav-link {
      transition: all 0.3s ease;
    }
    .nav-link:hover {
      transform: translateY(-2px);
    }
    .nav-pills .nav-link.active {
      background-color: #0d6efd;
    }
    .navbar {
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    }
    .memory-card {
      transition: transform 0.3s ease;
    }
    .memory-card:hover {
      transform: translateY(-5px);
    }
    .memory-level {
      margin-bottom: 2rem;
    }
    .temperature-indicator {
      width: 100%;
      height: 5px;
      background: linear-gradient(to right, #0d6efd, #dc3545);
      margin-bottom: 0.5rem;
    }
    .temperature-marker {
      height: 100%;
      width: 10px;
      background-color: #fff;
      position: relative;
    }
    .brain-visualization {
      position: relative;
      width: 100%;
      height: 300px;
      background-color: #343a40;
      border-radius: 10px;
      overflow: hidden;
      margin-bottom: 1.5rem;
      background-image: radial-gradient(circle at center, #444, #222);
    }
    .brain-region {
      position: absolute;
      border-radius: 50%;
      opacity: 0.7;
      transition: all 0.5s ease;
      box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
      cursor: pointer;
    }
    .brain-region:hover {
      opacity: 0.9;
      box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
    }
    .brain-connection {
      position: absolute;
      background-color: rgba(255, 255, 255, 0.2);
      height: 2px;
      transform-origin: 0 0;
      z-index: 1;
      animation: pulse 3s infinite;
    }
    .brain-info-panel {
      position: absolute;
      bottom: 10px;
      left: 10px;
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 5px 10px;
      border-radius: 5px;
      font-size: 0.8rem;
      z-index: 10;
    }
    .memory-flow {
      position: absolute;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #fff;
      z-index: 5;
      opacity: 0.8;
      animation: flow 5s linear infinite;
    }
    @keyframes pulse {
      0% { opacity: 0.1; }
      50% { opacity: 0.3; }
      100% { opacity: 0.1; }
    }
    @keyframes flow {
      0% { transform: scale(0.5); opacity: 0.8; }
      100% { transform: scale(0); opacity: 0; }
    }
    .stats-card {
      background-color: #343a40;
      border-radius: 10px;
      padding: 1rem;
      margin-bottom: 1rem;
    }
    .progress-bar {
      transition: width 0.5s ease;
    }
    .memory-search {
      margin-bottom: 2rem;
    }
    .memory-item {
      cursor: pointer;
    }
  </style>
</head>
<body class="bg-dark text-light">
  <!-- Navigation Bar -->
  <nav class="navbar navbar-expand-lg navbar-dark bg-dark border-bottom border-secondary mb-3">
    <div class="container-fluid">
      <a class="navbar-brand d-flex align-items-center" href="/">
        <i class="bi bi-robot fs-3 me-2"></i>
        <span>DeepSeek r1</span>
        <span class="badge bg-success ms-2">Local</span>
      </a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav ms-auto">
          <li class="nav-item">
            <a class="nav-link" href="/">
              <i class="bi bi-chat-dots me-1"></i> Chat
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/book-analysis">
              <i class="bi bi-book me-1"></i> Analyse de Livres
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link active" href="/memory">
              <i class="bi bi-brain me-1"></i> Mémoire Thermique
            </a>
          </li>
          <li class="nav-item">
            <div id="ollama-status-nav" class="nav-link">
              <div class="d-flex align-items-center">
                <div class="spinner-border spinner-border-sm me-1" role="status"></div>
                <small>Vérification d'Ollama...</small>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </nav>

  <div class="container-fluid">
    <div class="row">
      <!-- Sidebar -->
      <div class="col-md-3 col-lg-2 sidebar p-3">

        <div class="mb-4">
          <h5>Niveaux de mémoire</h5>
          <div class="list-group list-group-flush">
            <a href="#instant-memory" class="list-group-item list-group-item-action bg-dark text-light border-secondary">
              <div class="d-flex justify-content-between align-items-center">
                <span>Mémoire instantanée</span>
                <span class="badge bg-primary rounded-pill" id="instant-count">0</span>
              </div>
            </a>
            <a href="#short-term" class="list-group-item list-group-item-action bg-dark text-light border-secondary">
              <div class="d-flex justify-content-between align-items-center">
                <span>Mémoire à court terme</span>
                <span class="badge bg-primary rounded-pill" id="short-term-count">0</span>
              </div>
            </a>
            <a href="#working-memory" class="list-group-item list-group-item-action bg-dark text-light border-secondary">
              <div class="d-flex justify-content-between align-items-center">
                <span>Mémoire de travail</span>
                <span class="badge bg-primary rounded-pill" id="working-memory-count">0</span>
              </div>
            </a>
            <a href="#medium-term" class="list-group-item list-group-item-action bg-dark text-light border-secondary">
              <div class="d-flex justify-content-between align-items-center">
                <span>Mémoire à moyen terme</span>
                <span class="badge bg-primary rounded-pill" id="medium-term-count">0</span>
              </div>
            </a>
            <a href="#long-term" class="list-group-item list-group-item-action bg-dark text-light border-secondary">
              <div class="d-flex justify-content-between align-items-center">
                <span>Mémoire à long terme</span>
                <span class="badge bg-primary rounded-pill" id="long-term-count">0</span>
              </div>
            </a>
            <a href="#dream-memory" class="list-group-item list-group-item-action bg-dark text-light border-secondary">
              <div class="d-flex justify-content-between align-items-center">
                <span>Mémoire des rêves</span>
                <span class="badge bg-primary rounded-pill" id="dream-memory-count">0</span>
              </div>
            </a>
          </div>
        </div>

        <div class="mb-4">
          <h5>Actions</h5>
          <button class="btn btn-outline-light w-100 mb-2" id="force-cycle-btn">
            <i class="bi bi-arrow-repeat me-2"></i>Forcer un cycle de mémoire
          </button>
          <button class="btn btn-outline-light w-100 mb-2" id="generate-dream-btn">
            <i class="bi bi-cloud-moon me-2"></i>Générer un rêve
          </button>
          <button class="btn btn-outline-danger w-100" id="clear-memory-btn">
            <i class="bi bi-trash me-2"></i>Effacer la mémoire
          </button>
        </div>
      </div>

      <!-- Main content -->
      <div class="col-md-9 col-lg-10 main-content p-0">
        <div class="d-flex flex-column h-100">
          <div class="p-3 border-bottom border-secondary">
            <div class="d-flex justify-content-between align-items-center">
              <h4>Mémoire Thermique</h4>
              <div>
                <button class="btn btn-outline-light btn-sm me-2" id="refresh-btn">
                  <i class="bi bi-arrow-clockwise me-1"></i>Rafraîchir
                </button>
                <button class="btn btn-outline-light btn-sm" id="export-memory-btn">
                  <i class="bi bi-download me-1"></i>Exporter
                </button>
              </div>
            </div>
          </div>

          <div class="p-3 flex-grow-1 overflow-auto">
            <div class="row mb-4">
              <div class="col-md-8">
                <div class="memory-search">
                  <h5>Rechercher dans la mémoire</h5>
                  <div class="input-group mb-3">
                    <input type="text" class="form-control bg-dark text-light border-secondary" id="memory-search-input" placeholder="Rechercher...">
                    <button class="btn btn-primary" type="button" id="memory-search-btn">
                      <i class="bi bi-search"></i>
                    </button>
                  </div>
                  <div id="memory-search-results" class="d-none">
                    <h6>Résultats de la recherche</h6>
                    <div class="list-group" id="search-results-list">
                      <!-- Les résultats seront ajoutés ici dynamiquement -->
                    </div>
                  </div>
                </div>

                <div class="brain-visualization" id="brain-visualization">
                  <!-- Les régions du cerveau seront ajoutées ici dynamiquement -->
                </div>
              </div>

              <div class="col-md-4">
                <div class="stats-card">
                  <h5>Statistiques globales</h5>
                  <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                      <span>Entrées totales</span>
                      <span id="total-entries">0</span>
                    </div>
                    <div class="progress bg-secondary">
                      <div class="progress-bar" role="progressbar" style="width: 0%" id="total-entries-bar"></div>
                    </div>
                  </div>

                  <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                      <span>Température moyenne</span>
                      <span id="average-temperature">0.0</span>
                    </div>
                    <div class="progress bg-secondary">
                      <div class="progress-bar bg-danger" role="progressbar" style="width: 0%" id="average-temperature-bar"></div>
                    </div>
                  </div>

                  <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                      <span>Cycles effectués</span>
                      <span id="cycles-performed">0</span>
                    </div>
                  </div>

                  <div>
                    <div class="d-flex justify-content-between mb-1">
                      <span>Dernier cycle</span>
                      <span id="last-cycle-time">-</span>
                    </div>
                  </div>
                </div>

                <div class="stats-card">
                  <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0">Accélérateur Kyber</h5>
                    <div class="form-check form-switch">
                      <input class="form-check-input" type="checkbox" id="kyber-enabled" checked>
                      <label class="form-check-label" for="kyber-enabled">Activé</label>
                    </div>
                  </div>

                  <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                      <span>Facteur d'accélération</span>
                      <span id="boost-factor">1.5x</span>
                    </div>
                    <input type="range" class="form-range" id="boost-factor-slider" min="1.0" max="2.0" step="0.1" value="1.5">
                    <div class="progress bg-secondary">
                      <div class="progress-bar bg-info" role="progressbar" style="width: 75%" id="boost-factor-bar"></div>
                    </div>
                  </div>

                  <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                      <span>Température</span>
                      <span id="kyber-temperature">0.6</span>
                    </div>
                    <input type="range" class="form-range" id="temperature-slider" min="0.1" max="1.0" step="0.1" value="0.6">
                    <div class="progress bg-secondary">
                      <div class="progress-bar bg-warning" role="progressbar" style="width: 60%" id="kyber-temperature-bar"></div>
                    </div>
                  </div>

                  <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                      <span>Stabilité</span>
                      <span id="stability">0.9</span>
                    </div>
                    <input type="range" class="form-range" id="stability-slider" min="0.1" max="1.0" step="0.1" value="0.9">
                    <div class="progress bg-secondary">
                      <div class="progress-bar bg-success" role="progressbar" style="width: 90%" id="stability-bar"></div>
                    </div>
                  </div>

                  <div class="d-flex justify-content-between">
                    <button class="btn btn-sm btn-outline-primary" id="save-kyber-config-btn">
                      <i class="bi bi-save me-1"></i>Sauvegarder
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" id="reset-kyber-config-btn">
                      <i class="bi bi-arrow-counterclockwise me-1"></i>Réinitialiser
                    </button>
                    <button class="btn btn-sm btn-outline-danger" id="lock-kyber-btn">
                      <i class="bi bi-lock me-1"></i>Verrouiller
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Niveaux de mémoire -->
            <div class="memory-level" id="instant-memory">
              <h5>Mémoire instantanée (Niveau 1)</h5>
              <p class="text-muted">Stockage ultra-volatile pour les informations immédiates. Température très élevée (0.9-1.0).</p>
              <div class="row" id="instant-memory-items">
                <div class="text-center p-5 text-muted">
                  <i class="bi bi-hourglass fs-1"></i>
                  <div class="mt-3">Aucune entrée dans la mémoire instantanée</div>
                </div>
              </div>
            </div>

            <div class="memory-level" id="short-term">
              <h5>Mémoire à court terme (Niveau 2)</h5>
              <p class="text-muted">Stockage volatile pour les informations récentes. Température élevée (0.7-0.9).</p>
              <div class="row" id="short-term-items">
                <div class="text-center p-5 text-muted">
                  <i class="bi bi-hourglass fs-1"></i>
                  <div class="mt-3">Aucune entrée dans la mémoire à court terme</div>
                </div>
              </div>
            </div>

            <div class="memory-level" id="working-memory">
              <h5>Mémoire de travail (Niveau 3)</h5>
              <p class="text-muted">Stockage semi-volatile pour les informations en cours de traitement. Température moyenne à élevée (0.6-0.8).</p>
              <div class="row" id="working-memory-items">
                <div class="text-center p-5 text-muted">
                  <i class="bi bi-hourglass fs-1"></i>
                  <div class="mt-3">Aucune entrée dans la mémoire de travail</div>
                </div>
              </div>
            </div>

            <div class="memory-level" id="medium-term">
              <h5>Mémoire à moyen terme (Niveau 4)</h5>
              <p class="text-muted">Stockage semi-persistant pour les informations importantes. Température moyenne (0.4-0.7).</p>
              <div class="row" id="medium-term-items">
                <div class="text-center p-5 text-muted">
                  <i class="bi bi-hourglass fs-1"></i>
                  <div class="mt-3">Aucune entrée dans la mémoire à moyen terme</div>
                </div>
              </div>
            </div>

            <div class="memory-level" id="long-term">
              <h5>Mémoire à long terme (Niveau 5)</h5>
              <p class="text-muted">Stockage persistant pour les informations cruciales. Température basse (0.2-0.5).</p>
              <div class="row" id="long-term-items">
                <div class="text-center p-5 text-muted">
                  <i class="bi bi-hourglass fs-1"></i>
                  <div class="mt-3">Aucune entrée dans la mémoire à long terme</div>
                </div>
              </div>
            </div>

            <div class="memory-level" id="dream-memory">
              <h5>Mémoire des rêves (Niveau 6)</h5>
              <p class="text-muted">Stockage spécial pour les rêves générés et leurs insights. Température variable (0.3-0.8).</p>
              <div class="row" id="dream-memory-items">
                <div class="text-center p-5 text-muted">
                  <i class="bi bi-hourglass fs-1"></i>
                  <div class="mt-3">Aucune entrée dans la mémoire des rêves</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal de détail de mémoire -->
  <div class="modal fade" id="memory-detail-modal" tabindex="-1" aria-labelledby="memory-detail-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content bg-dark text-light">
        <div class="modal-header">
          <h5 class="modal-title" id="memory-detail-modal-label">Détail de la mémoire</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="mb-3">
            <h6>Clé</h6>
            <div class="p-2 bg-secondary rounded" id="memory-detail-key"></div>
          </div>

          <div class="mb-3">
            <h6>Données</h6>
            <div class="p-2 bg-secondary rounded" id="memory-detail-data"></div>
          </div>

          <div class="row mb-3">
            <div class="col-md-4">
              <h6>Température</h6>
              <div class="p-2 bg-secondary rounded" id="memory-detail-temperature"></div>
            </div>
            <div class="col-md-4">
              <h6>Catégorie</h6>
              <div class="p-2 bg-secondary rounded" id="memory-detail-category"></div>
            </div>
            <div class="col-md-4">
              <h6>Source</h6>
              <div class="p-2 bg-secondary rounded" id="memory-detail-source"></div>
            </div>
          </div>

          <div class="row mb-3">
            <div class="col-md-6">
              <h6>Date de création</h6>
              <div class="p-2 bg-secondary rounded" id="memory-detail-timestamp"></div>
            </div>
            <div class="col-md-6">
              <h6>Dernier accès</h6>
              <div class="p-2 bg-secondary rounded" id="memory-detail-last-accessed"></div>
            </div>
          </div>

          <div class="mb-3">
            <h6>Nombre d'accès</h6>
            <div class="p-2 bg-secondary rounded" id="memory-detail-access-count"></div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-danger" id="delete-memory-item-btn">Supprimer</button>
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
  <script src="/js/memory.js"></script>
</body>
</html>
