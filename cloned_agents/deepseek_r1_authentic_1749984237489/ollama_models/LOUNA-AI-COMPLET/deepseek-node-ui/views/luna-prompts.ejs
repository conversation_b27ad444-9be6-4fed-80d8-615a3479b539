<%- include('luna-base') %>

<!-- Intégration du contenu principal Luna Prompts -->
<div class="row mt-4">
  <!-- Panneau de gestion des prompts -->
  <div class="col-md-8">
    <div class="luna-card" style="height: 65vh; display: flex; flex-direction: column;">
      <h3 class="mb-3 d-flex align-items-center justify-content-between">
        <div><i class="bi bi-lightning me-2"></i> Bibliothèque de Prompts</div>
        <div>
          <button id="new-prompt-btn" class="btn btn-sm btn-luna">
            <i class="bi bi-plus-circle me-1"></i> Nouveau Prompt
          </button>
        </div>
      </h3>
      
      <div id="prompts-container" style="flex-grow: 1; overflow-y: auto; margin-bottom: 1rem; padding: 1rem; border-radius: 10px; background-color: rgba(0,0,0,0.2); background-image: linear-gradient(to bottom, rgba(156, 137, 184, 0.05), rgba(240, 166, 202, 0.05));">
        <!-- Les prompts seront chargés ici dynamiquement -->
        <div class="text-center py-5 text-muted" id="no-prompts-message">
          <i class="bi bi-lightning" style="font-size: 3rem;"></i>
          <p class="mt-3">Aucun prompt enregistré. Créez votre premier prompt en cliquant sur "Nouveau Prompt".</p>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Panneau latéral avec les catégories et options -->
  <div class="col-md-4">
    <!-- Catégories de prompts -->
    <div class="luna-card mb-3">
      <h4><i class="bi bi-folder me-2"></i> Catégories</h4>
      <div class="mb-3">
        <div class="list-group" id="prompt-categories">
          <a href="#" class="list-group-item list-group-item-action active" data-category="all">
            Tous les prompts
          </a>
          <a href="#" class="list-group-item list-group-item-action" data-category="creative">
            Créativité
          </a>
          <a href="#" class="list-group-item list-group-item-action" data-category="technical">
            Technique
          </a>
          <a href="#" class="list-group-item list-group-item-action" data-category="business">
            Business
          </a>
          <a href="#" class="list-group-item list-group-item-action" data-category="personal">
            Personnel
          </a>
        </div>
      </div>
      <div class="d-flex justify-content-between">
        <button id="add-category-btn" class="btn btn-sm btn-luna-outline">
          <i class="bi bi-plus me-1"></i> Ajouter
        </button>
        <button id="edit-categories-btn" class="btn btn-sm btn-luna">
          <i class="bi bi-pencil me-1"></i> Modifier
        </button>
      </div>
    </div>
    
    <!-- Options d'importation/exportation -->
    <div class="luna-card mb-3">
      <h4><i class="bi bi-gear me-2"></i> Options</h4>
      <div class="d-grid gap-2">
        <button id="import-prompts-btn" class="btn btn-luna-outline">
          <i class="bi bi-upload me-1"></i> Importer des prompts
        </button>
        <button id="export-prompts-btn" class="btn btn-luna-outline">
          <i class="bi bi-download me-1"></i> Exporter tous les prompts
        </button>
        <button id="backup-prompts-btn" class="btn btn-luna-outline">
          <i class="bi bi-cloud-arrow-up me-1"></i> Sauvegarder
        </button>
      </div>
    </div>
    
    <!-- Statistiques -->
    <div class="luna-card">
      <h4><i class="bi bi-bar-chart me-2"></i> Statistiques</h4>
      <div class="mb-2">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span>Total des prompts</span>
          <span id="total-prompts-count">0</span>
        </div>
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span>Prompts utilisés</span>
          <span id="used-prompts-count">0</span>
        </div>
        <div class="d-flex justify-content-between align-items-center">
          <span>Dernière modification</span>
          <span id="last-modified-date">-</span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal pour créer/éditer un prompt -->
<div class="modal fade" id="prompt-modal" tabindex="-1" aria-labelledby="promptModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content bg-dark">
      <div class="modal-header">
        <h5 class="modal-title" id="promptModalLabel">Nouveau Prompt</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fermer"></button>
      </div>
      <div class="modal-body">
        <form id="prompt-form">
          <input type="hidden" id="prompt-id">
          <div class="mb-3">
            <label for="prompt-title" class="form-label">Titre</label>
            <input type="text" class="form-control" id="prompt-title" placeholder="Titre du prompt" required>
          </div>
          <div class="mb-3">
            <label for="prompt-category" class="form-label">Catégorie</label>
            <select class="form-select" id="prompt-category" required>
              <option value="creative">Créativité</option>
              <option value="technical">Technique</option>
              <option value="business">Business</option>
              <option value="personal">Personnel</option>
            </select>
          </div>
          <div class="mb-3">
            <label for="prompt-content" class="form-label">Contenu du prompt</label>
            <textarea class="form-control" id="prompt-content" rows="10" placeholder="Écrivez votre prompt ici..." required></textarea>
          </div>
          <div class="mb-3">
            <label for="prompt-description" class="form-label">Description (optionnelle)</label>
            <textarea class="form-control" id="prompt-description" rows="3" placeholder="Description ou notes sur ce prompt..."></textarea>
          </div>
          <div class="form-check mb-3">
            <input class="form-check-input" type="checkbox" id="prompt-favorite">
            <label class="form-check-label" for="prompt-favorite">
              Marquer comme favori
            </label>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
        <button type="button" class="btn btn-primary" id="save-prompt-btn">Enregistrer</button>
      </div>
    </div>
  </div>
</div>

<!-- Modal pour ajouter une catégorie -->
<div class="modal fade" id="category-modal" tabindex="-1" aria-labelledby="categoryModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content bg-dark">
      <div class="modal-header">
        <h5 class="modal-title" id="categoryModalLabel">Nouvelle Catégorie</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fermer"></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
          <label for="category-name" class="form-label">Nom de la catégorie</label>
          <input type="text" class="form-control" id="category-name" placeholder="Nom de la catégorie" required>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
        <button type="button" class="btn btn-primary" id="save-category-btn">Ajouter</button>
      </div>
    </div>
  </div>
</div>

<script src="/js/luna-prompts.js"></script>
