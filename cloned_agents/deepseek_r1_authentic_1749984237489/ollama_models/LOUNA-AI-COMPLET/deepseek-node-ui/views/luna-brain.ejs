<%- include('luna-base') %>
<link rel="stylesheet" href="/css/luna-brain.css">
<link rel="stylesheet" href="/css/brain-presence.css">

<!-- Intégration du contenu principal Luna Brain -->
<div class="row mt-4">
  <!-- Panneau principal de visualisation du cerveau -->
  <div class="col-md-8">
    <div class="luna-card" style="height: 65vh; display: flex; flex-direction: column;">
      <h3 class="mb-3 d-flex align-items-center justify-content-between">
        <div><i class="bi bi-diagram-3 me-2"></i> Visualisation du Cerveau</div>
        <div>
          <div class="btn-group me-2">
            <button id="home-btn" class="btn btn-sm btn-luna-outline" onclick="window.location.href='/luna'">
              <i class="bi bi-house-door me-1"></i> Accueil
            </button>
            <button id="cognitive-btn" class="btn btn-sm btn-luna-outline" onclick="window.location.href='/luna/cognitive'">
              <i class="bi bi-braces-asterisk me-1"></i> Système Cognitif
            </button>
          </div>
          <div class="btn-group">
            <button id="view-2d-btn" class="btn btn-sm btn-luna active">
              <i class="bi bi-grid me-1"></i> 2D
            </button>
            <button id="view-3d-btn" class="btn btn-sm btn-luna-outline">
              <i class="bi bi-badge-3d me-1"></i> 3D
            </button>
          </div>
        </div>
      </h3>

      <div id="brain-container" style="flex-grow: 1; overflow: hidden; margin-bottom: 1rem; padding: 1rem; border-radius: 10px; background-color: rgba(0,0,0,0.2); background-image: linear-gradient(to bottom, rgba(156, 137, 184, 0.05), rgba(240, 166, 202, 0.05));">
        <!-- Vue 2D du cerveau -->
        <div id="brain-2d-view" class="brain-view active">
          <div class="brain-zones">
            <div class="brain-zone zone-1" data-zone="1">
              <div class="zone-label">Zone 1<br><small>Récente</small></div>
              <div class="zone-nodes"></div>
            </div>
            <div class="brain-zone zone-2" data-zone="2">
              <div class="zone-label">Zone 2<br><small>Chaude</small></div>
              <div class="zone-nodes"></div>
            </div>
            <div class="brain-zone zone-3" data-zone="3">
              <div class="zone-label">Zone 3<br><small>Tiède</small></div>
              <div class="zone-nodes"></div>
            </div>
            <div class="brain-zone zone-4" data-zone="4">
              <div class="zone-label">Zone 4<br><small>Fraîche</small></div>
              <div class="zone-nodes"></div>
            </div>
            <div class="brain-zone zone-5" data-zone="5">
              <div class="zone-label">Zone 5<br><small>Froide</small></div>
              <div class="zone-nodes"></div>
            </div>
            <div class="brain-zone zone-6" data-zone="6">
              <div class="zone-label">Zone 6<br><small>Archive</small></div>
              <div class="zone-nodes"></div>
            </div>
          </div>
          <div id="connections-container"></div>
        </div>

        <!-- Vue 3D du cerveau (masquée par défaut) -->
        <div id="brain-3d-view" class="brain-view">
          <div class="text-center py-5">
            <p class="mb-4"><i class="bi bi-badge-3d" style="font-size: 3rem;"></i></p>
            <h4>Visualisation 3D</h4>
            <p class="text-muted">La visualisation 3D sera disponible prochainement.</p>
            <button id="enable-3d-btn" class="btn btn-luna mt-3">
              <i class="bi bi-gpu-card me-1"></i> Activer le rendu 3D
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Panneau latéral avec les statistiques et contrôles -->
  <div class="col-md-4">
    <!-- Statistiques du cerveau -->
    <div class="luna-card mb-3">
      <h4><i class="bi bi-bar-chart me-2"></i> Statistiques</h4>
      <div class="mb-3">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span>Mémoires totales</span>
          <span id="total-memories">124</span>
        </div>
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span>Connexions</span>
          <span id="total-connections">342</span>
        </div>
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span>Température moyenne</span>
          <span id="avg-temperature">72°C</span>
        </div>
        <div class="d-flex justify-content-between align-items-center">
          <span>Activité neuronale</span>
          <span id="neural-activity">64%</span>
        </div>
      </div>
    </div>

    <!-- Répartition des mémoires -->
    <div class="luna-card mb-3">
      <h4><i class="bi bi-pie-chart me-2"></i> Répartition</h4>
      <div class="mb-3">
        <canvas id="memory-distribution-chart" width="100%" height="200"></canvas>
      </div>
      <div class="memory-zones-legend">
        <div class="zone-legend zone-1-legend">Zone 1 (Récente)</div>
        <div class="zone-legend zone-2-legend">Zone 2 (Chaude)</div>
        <div class="zone-legend zone-3-legend">Zone 3 (Tiède)</div>
        <div class="zone-legend zone-4-legend">Zone 4 (Fraîche)</div>
        <div class="zone-legend zone-5-legend">Zone 5 (Froide)</div>
        <div class="zone-legend zone-6-legend">Zone 6 (Archive)</div>
      </div>
    </div>

    <!-- Contrôles de visualisation -->
    <div class="luna-card">
      <h4><i class="bi bi-sliders me-2"></i> Contrôles</h4>
      <div class="mb-3">
        <label class="form-label">Affichage des connexions</label>
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="show-connections-toggle" checked>
          <label class="form-check-label" for="show-connections-toggle">
            Afficher les connexions
          </label>
        </div>
      </div>
      <div class="mb-3">
        <label class="form-label">Animation</label>
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="animation-toggle" checked>
          <label class="form-check-label" for="animation-toggle">
            Activer l'animation
          </label>
        </div>
      </div>
      <div class="mb-3">
        <label for="detail-level" class="form-label">Niveau de détail</label>
        <select class="form-select" id="detail-level">
          <option value="low">Faible</option>
          <option value="medium" selected>Moyen</option>
          <option value="high">Élevé</option>
        </select>
      </div>
      <div class="d-grid gap-2 mt-4">
        <button id="refresh-brain-btn" class="btn btn-luna">
          <i class="bi bi-arrow-clockwise me-1"></i> Actualiser
        </button>
        <button id="export-brain-btn" class="btn btn-luna-outline">
          <i class="bi bi-download me-1"></i> Exporter
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal de détail de mémoire -->
<div class="modal fade" id="memory-detail-modal" tabindex="-1" aria-labelledby="memoryDetailModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content bg-dark">
      <div class="modal-header">
        <h5 class="modal-title" id="memoryDetailModalLabel">Détail de la mémoire</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fermer"></button>
      </div>
      <div class="modal-body">
        <div class="memory-detail-header">
          <div class="memory-id">ID: <span id="memory-id">mem_12345</span></div>
          <div class="memory-zone">Zone: <span id="memory-zone">2 (Chaude)</span></div>
          <div class="memory-temperature">Température: <span id="memory-temp">78°C</span></div>
          <div class="memory-created">Créée le: <span id="memory-created">12/05/2023 14:32</span></div>
        </div>
        <div class="memory-content mt-4">
          <h6>Contenu</h6>
          <div id="memory-content-display" class="p-3 rounded" style="background-color: rgba(0,0,0,0.2);">
            Le contenu de la mémoire sera affiché ici.
          </div>
        </div>
        <div class="memory-connections mt-4">
          <h6>Connexions (<span id="memory-connections-count">5</span>)</h6>
          <div id="memory-connections-list" class="memory-connections-container">
            <!-- Les connexions seront ajoutées ici dynamiquement -->
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
        <button type="button" class="btn btn-danger" id="delete-memory-btn">Supprimer</button>
        <button type="button" class="btn btn-primary" id="promote-memory-btn">Promouvoir</button>
      </div>
    </div>
  </div>
</div>

<!-- Présence du cerveau -->
<div class="row mt-4">
  <div class="col-md-12">
    <%- include('partials/brain-presence') %>
  </div>
</div>

<!-- Barre de statut et statistiques -->
<div class="row mt-4">
  <div class="col-md-12">
    <div class="luna-card p-3">
      <div class="row">
        <div class="col-md-3">
          <div class="d-flex align-items-center">
            <i class="bi bi-cpu me-3" style="font-size: 2rem; color: var(--luna-primary);"></i>
            <div>
              <div class="text-muted">Processeur</div>
              <div class="h5 mb-0" id="cpu-usage">42%</div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="d-flex align-items-center">
            <i class="bi bi-memory me-3" style="font-size: 2rem; color: var(--luna-primary);"></i>
            <div>
              <div class="text-muted">Mémoire</div>
              <div class="h5 mb-0" id="ram-usage">2.4 GB</div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="d-flex align-items-center">
            <i class="bi bi-activity me-3" style="font-size: 2rem; color: var(--luna-accent);"></i>
            <div>
              <div class="text-muted">Latence</div>
              <div class="h5 mb-0" id="latency">24ms</div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="d-flex align-items-center">
            <i class="bi bi-thermometer-half me-3" style="font-size: 2rem; color: var(--luna-secondary);"></i>
            <div>
              <div class="text-muted">Temp. Mémoire</div>
              <div class="h5 mb-0" id="thermal-index">72°C</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="/socket.io/socket.io.js"></script>
<script src="/js/luna-interface.js"></script>
<script src="/js/luna-peripherals.js"></script>
<script src="/js/luna-brain.js"></script>
<script src="/js/brain-presence-ui.js"></script>
