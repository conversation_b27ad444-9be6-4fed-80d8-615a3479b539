<%- include('luna-base') %>

<!-- Interface de gestion des documents -->
<div class="container-fluid mt-4">
  <div class="row">
    <div class="col-md-12">
      <div class="luna-card">
        <h3><i class="bi bi-file-earmark-text me-2"></i> Gestion des documents</h3>
        <p class="text-muted">Importez des documents pour enrichir la mémoire cognitive de Luna</p>
        
        <!-- Zone de dépôt -->
        <div class="mb-4 p-4 border-dashed rounded" id="dropzone" style="border: 2px dashed var(--luna-accent); background-color: rgba(0,0,0,0.1);">
          <div class="text-center">
            <i class="bi bi-cloud-upload display-4 mb-2" style="color: var(--luna-accent);"></i>
            <h5>Déposez vos fichiers ici ou cliquez pour sélectionner</h5>
            <p class="text-muted">Formats acceptés : PDF, TXT, DOC, DOCX, JPG, PNG, MP3, MP4</p>
            <form id="upload-form" enctype="multipart/form-data">
              <input type="file" id="file-input" class="d-none" multiple>
              <button type="button" id="select-files-btn" class="btn btn-luna mt-2">
                <i class="bi bi-folder-plus me-2"></i>Sélectionner des fichiers
              </button>
            </form>
          </div>
        </div>
        
        <!-- Barre de progression -->
        <div class="progress mb-3" id="upload-progress-container" style="display: none;">
          <div class="progress-bar progress-bar-striped progress-bar-animated" id="upload-progress" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%; background: var(--luna-gradient);">0%</div>
        </div>
        
        <!-- Liste des documents -->
        <div class="document-library mt-4">
          <h4 class="mb-3">Bibliothèque de documents</h4>
          
          <!-- Filtres -->
          <div class="d-flex justify-content-between mb-3">
            <div class="btn-group" role="group" aria-label="Filtres de documents">
              <button type="button" class="btn btn-sm btn-luna active" data-filter="all">Tous</button>
              <button type="button" class="btn btn-sm btn-luna-outline" data-filter="documents">Documents</button>
              <button type="button" class="btn btn-sm btn-luna-outline" data-filter="images">Images</button>
              <button type="button" class="btn btn-sm btn-luna-outline" data-filter="audio">Audio</button>
              <button type="button" class="btn btn-sm btn-luna-outline" data-filter="videos">Vidéos</button>
            </div>
            <button id="refresh-documents" class="btn btn-sm btn-luna">
              <i class="bi bi-arrow-clockwise me-1"></i> Actualiser
            </button>
          </div>
          
          <!-- Tableau des documents -->
          <div class="table-responsive">
            <table class="table table-dark table-hover" id="documents-table">
              <thead>
                <tr>
                  <th>Type</th>
                  <th>Nom</th>
                  <th>Taille</th>
                  <th>Date de modification</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody id="documents-list">
                <tr>
                  <td colspan="5" class="text-center">Chargement des documents...</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  $(document).ready(function() {
    // Charger la liste des documents au démarrage
    loadDocuments();
    
    // Configurer la sélection de fichiers
    $('#select-files-btn').on('click', function() {
      $('#file-input').click();
    });
    
    // Configurer le dropzone
    const dropzone = document.getElementById('dropzone');
    
    dropzone.addEventListener('dragover', function(e) {
      e.preventDefault();
      dropzone.classList.add('dropzone-active');
    });
    
    dropzone.addEventListener('dragleave', function(e) {
      e.preventDefault();
      dropzone.classList.remove('dropzone-active');
    });
    
    dropzone.addEventListener('drop', function(e) {
      e.preventDefault();
      dropzone.classList.remove('dropzone-active');
      
      const files = e.dataTransfer.files;
      if (files.length > 0) {
        handleFiles(files);
      }
    });
    
    // Gérer la sélection de fichiers
    $('#file-input').on('change', function() {
      const files = this.files;
      if (files.length > 0) {
        handleFiles(files);
      }
    });
    
    // Gestion du filtre de documents
    $('.btn-group button').on('click', function() {
      $('.btn-group button').removeClass('active').addClass('btn-luna-outline');
      $(this).removeClass('btn-luna-outline').addClass('active');
      
      const filter = $(this).data('filter');
      if (filter === 'all') {
        $('#documents-list tr').show();
      } else {
        $('#documents-list tr').hide();
        $(`#documents-list tr[data-type="${filter}"]`).show();
      }
    });
    
    // Actualiser la liste des documents
    $('#refresh-documents').on('click', function() {
      loadDocuments();
    });
    
    // Fonction pour charger les documents
    function loadDocuments() {
      $.ajax({
        url: '/luna/documents/list',
        method: 'GET',
        success: function(response) {
          if (response.success) {
            displayDocuments(response.files);
          } else {
            showAlert('error', 'Erreur lors du chargement des documents: ' + response.message);
          }
        },
        error: function(xhr, status, error) {
          showAlert('error', 'Erreur de connexion: ' + error);
        }
      });
    }
    
    // Afficher les documents dans le tableau
    function displayDocuments(files) {
      const tbody = $('#documents-list');
      tbody.empty();
      
      let hasDocuments = false;
      
      // Parcourir chaque catégorie
      for (const category in files) {
        const categoryFiles = files[category];
        
        if (categoryFiles.length > 0) {
          hasDocuments = true;
          
          categoryFiles.forEach(file => {
            // Déterminer l'icône selon le type
            let icon;
            switch(category) {
              case 'documents':
                icon = 'bi-file-earmark-text';
                break;
              case 'images':
                icon = 'bi-file-earmark-image';
                break;
              case 'audio':
                icon = 'bi-file-earmark-music';
                break;
              case 'videos':
                icon = 'bi-file-earmark-play';
                break;
              case 'pdf':
                icon = 'bi-file-earmark-pdf';
                break;
              default:
                icon = 'bi-file-earmark';
            }
            
            const size = formatFileSize(file.size);
            const date = new Date(file.modified).toLocaleString();
            
            const row = `
              <tr data-type="${category}" data-path="${file.path}">
                <td><i class="bi ${icon} me-2"></i>${category}</td>
                <td>${file.name}</td>
                <td>${size}</td>
                <td>${date}</td>
                <td>
                  <button class="btn btn-sm btn-luna-outline view-document" data-path="${file.path}" title="Visualiser">
                    <i class="bi bi-eye"></i>
                  </button>
                  <button class="btn btn-sm btn-danger delete-document" data-path="${file.path}" title="Supprimer">
                    <i class="bi bi-trash"></i>
                  </button>
                </td>
              </tr>
            `;
            
            tbody.append(row);
          });
        }
      }
      
      if (!hasDocuments) {
        tbody.html('<tr><td colspan="5" class="text-center">Aucun document trouvé</td></tr>');
      }
      
      // Configurer les boutons de suppression
      $('.delete-document').on('click', function() {
        const path = $(this).data('path');
        const name = $(this).closest('tr').find('td:nth-child(2)').text();
        
        if (confirm(`Êtes-vous sûr de vouloir supprimer le document "${name}" ?`)) {
          deleteDocument(path);
        }
      });
      
      // Configurer les boutons de visualisation
      $('.view-document').on('click', function() {
        const path = $(this).data('path');
        viewDocument(path);
      });
    }
    
    // Gestion du téléchargement de fichiers
    function handleFiles(files) {
      const formData = new FormData();
      
      for (let i = 0; i < files.length; i++) {
        formData.append('document', files[i]);
      }
      
      // Afficher la barre de progression
      $('#upload-progress-container').show();
      $('#upload-progress').css('width', '0%').attr('aria-valuenow', 0).text('0%');
      
      $.ajax({
        url: '/luna/documents/upload',
        method: 'POST',
        data: formData,
        contentType: false,
        processData: false,
        xhr: function() {
          const xhr = new XMLHttpRequest();
          
          xhr.upload.addEventListener('progress', function(e) {
            if (e.lengthComputable) {
              const percent = Math.round((e.loaded / e.total) * 100);
              $('#upload-progress').css('width', percent + '%').attr('aria-valuenow', percent).text(percent + '%');
            }
          }, false);
          
          return xhr;
        },
        success: function(response) {
          if (response.success) {
            showAlert('success', 'Document téléchargé avec succès');
            loadDocuments();
          } else {
            showAlert('error', 'Erreur lors du téléchargement: ' + response.message);
          }
          
          // Masquer la barre de progression après 3 secondes
          setTimeout(function() {
            $('#upload-progress-container').hide();
          }, 3000);
        },
        error: function(xhr, status, error) {
          showAlert('error', 'Erreur de connexion: ' + error);
          
          // Masquer la barre de progression
          $('#upload-progress-container').hide();
        }
      });
    }
    
    // Supprimer un document
    function deleteDocument(filepath) {
      $.ajax({
        url: '/luna/documents/remove',
        method: 'DELETE',
        contentType: 'application/json',
        data: JSON.stringify({ filepath }),
        success: function(response) {
          if (response.success) {
            showAlert('success', 'Document supprimé avec succès');
            loadDocuments();
          } else {
            showAlert('error', 'Erreur lors de la suppression: ' + response.message);
          }
        },
        error: function(xhr, status, error) {
          showAlert('error', 'Erreur de connexion: ' + error);
        }
      });
    }
    
    // Visualiser un document
    function viewDocument(path) {
      window.open('/' + path, '_blank');
    }
    
    // Formater la taille de fichier
    function formatFileSize(bytes) {
      if (bytes === 0) return '0 B';
      
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
      const i = Math.floor(Math.log(bytes) / Math.log(1024));
      
      return (bytes / Math.pow(1024, i)).toFixed(2) + ' ' + sizes[i];
    }
    
    // Afficher une alerte
    function showAlert(type, message) {
      const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
      const alert = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
          ${message}
          <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fermer"></button>
        </div>
      `;
      
      $('.luna-card').prepend(alert);
      
      setTimeout(function() {
        $('.alert').alert('close');
      }, 5000);
    }
  });
</script>
