<%- include('luna-base') %>
<link rel="stylesheet" href="/css/luna-mcp.css">

<!-- Intégration du contenu principal Luna MCP -->
<div class="row mt-4">
  <!-- Panneau principal MCP -->
  <div class="col-md-8">
    <div class="luna-card" style="height: 65vh; display: flex; flex-direction: column;">
      <h3 class="mb-3 d-flex align-items-center justify-content-between">
        <div><i class="bi bi-cpu me-2"></i> Master Control Program</div>
        <div>
          <button id="mcp-status-btn" class="btn btn-sm btn-luna">
            <i class="bi bi-power"></i> <span id="mcp-status-text">Activer MCP</span>
          </button>
        </div>
      </h3>

      <div id="mcp-container" style="flex-grow: 1; overflow-y: auto; margin-bottom: 1rem; padding: 1rem; border-radius: 10px; background-color: rgba(0,0,0,0.2); background-image: linear-gradient(to bottom, rgba(156, 137, 184, 0.05), rgba(240, 166, 202, 0.05));">
        <!-- Terminal MCP -->
        <div id="mcp-terminal" class="p-3" style="background-color: rgba(0,0,0,0.5); border-radius: 8px; height: 300px; overflow-y: auto; font-family: 'Courier New', monospace; color: #0f0; text-shadow: 0 0 5px #0f0;">
          <div class="mcp-line">Initialisation du Master Control Program...</div>
          <div class="mcp-line">Chargement des modules système...</div>
          <div class="mcp-line">Vérification des accélérateurs Kyber...</div>
          <div class="mcp-line">Connexion à la mémoire thermique...</div>
          <div class="mcp-line">Système prêt.</div>
          <div class="mcp-line">MCP v1.0 en attente de commandes.</div>
          <div class="mcp-line" id="mcp-prompt">></div>
        </div>

        <!-- Contrôles MCP -->
        <div class="mt-4">
          <div class="row">
            <div class="col-md-6">
              <div class="card bg-dark mb-3">
                <div class="card-header">
                  <h5 class="mb-0">Contrôles Système</h5>
                </div>
                <div class="card-body">
                  <div class="mb-3">
                    <label class="form-label">Mode MCP</label>
                    <div class="form-check form-switch">
                      <input class="form-check-input" type="checkbox" id="mcp-mode-toggle">
                      <label class="form-check-label" for="mcp-mode-toggle">
                        <span id="mcp-mode-status">Désactivé</span>
                      </label>
                    </div>
                  </div>
                  <div class="mb-3">
                    <label class="form-label">Accès Internet</label>
                    <div class="form-check form-switch">
                      <input class="form-check-input" type="checkbox" id="internet-access-toggle" checked>
                      <label class="form-check-label" for="internet-access-toggle">
                        <span id="internet-access-status">Activé</span>
                      </label>
                    </div>
                  </div>
                  <div class="mb-3">
                    <label class="form-label">Niveau de sécurité</label>
                    <select class="form-select" id="security-level">
                      <option value="low">Faible</option>
                      <option value="medium" selected>Moyen</option>
                      <option value="high">Élevé</option>
                      <option value="maximum">Maximum</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="card bg-dark mb-3">
                <div class="card-header">
                  <h5 class="mb-0">Commandes Rapides</h5>
                </div>
                <div class="card-body">
                  <div class="d-grid gap-2">
                    <button class="btn btn-sm btn-luna-outline mcp-command-btn" data-command="status">
                      <i class="bi bi-info-circle me-2"></i> Statut Système
                    </button>
                    <button class="btn btn-sm btn-luna-outline mcp-command-btn" data-command="accelerators">
                      <i class="bi bi-lightning me-2"></i> Rapport Accélérateurs
                    </button>
                    <button class="btn btn-sm btn-luna-outline mcp-command-btn" data-command="memory">
                      <i class="bi bi-hdd me-2"></i> Statut Mémoire Thermique
                    </button>
                    <button class="btn btn-sm btn-luna-outline mcp-command-btn" data-command="network">
                      <i class="bi bi-globe me-2"></i> Diagnostic Réseau
                    </button>
                    <a href="/luna/vpn" class="btn btn-sm btn-luna-outline">
                      <i class="bi bi-shield-check me-2"></i> Gérer VPN
                    </a>
                    <button class="btn btn-sm btn-luna-outline mcp-command-btn" data-command="clear">
                      <i class="bi bi-trash me-2"></i> Effacer Terminal
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Panneau latéral avec les métriques système -->
  <div class="col-md-4">
    <!-- Métriques système -->
    <div class="luna-card mb-3">
      <h4><i class="bi bi-speedometer2 me-2"></i> Métriques Système</h4>
      <div class="mb-3">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span>CPU</span>
          <span id="mcp-cpu-usage">0%</span>
        </div>
        <div class="progress" style="height: 8px;">
          <div id="mcp-cpu-bar" class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
      </div>
      <div class="mb-3">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span>Mémoire</span>
          <span id="mcp-memory-usage">0 MB</span>
        </div>
        <div class="progress" style="height: 8px;">
          <div id="mcp-memory-bar" class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
      </div>
      <div class="mb-3">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span>Réseau</span>
          <span id="mcp-network-usage">0 Mbps</span>
        </div>
        <div class="progress" style="height: 8px;">
          <div id="mcp-network-bar" class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
      </div>
      <div class="mb-3">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span>Température</span>
          <span id="mcp-temp-value">0°C</span>
        </div>
        <div class="progress" style="height: 8px;">
          <div id="mcp-temp-bar" class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
      </div>
    </div>

    <!-- Statut des accélérateurs -->
    <div class="luna-card mb-3">
      <h4><i class="bi bi-lightning me-2"></i> Accélérateurs</h4>
      <div class="mb-2">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span>Mémoire</span>
          <span id="acc-memory-count">3</span>
        </div>
        <div class="progress" style="height: 8px;">
          <div id="acc-memory-bar" class="progress-bar" role="progressbar" style="width: 60%;" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
      </div>
      <div class="mb-2">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span>Thermique</span>
          <span id="acc-thermal-count">3</span>
        </div>
        <div class="progress" style="height: 8px;">
          <div id="acc-thermal-bar" class="progress-bar" role="progressbar" style="width: 70%;" aria-valuenow="70" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
      </div>
      <div class="mb-2">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span>Réflexion</span>
          <span id="acc-reflection-count">3</span>
        </div>
        <div class="progress" style="height: 8px;">
          <div id="acc-reflection-bar" class="progress-bar" role="progressbar" style="width: 90%;" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
      </div>
    </div>

    <!-- Journal d'activité -->
    <div class="luna-card">
      <h4><i class="bi bi-journal-text me-2"></i> Journal d'activité</h4>
      <div id="mcp-activity-log" style="max-height: 200px; overflow-y: auto; font-size: 0.8rem;">
        <div class="log-entry"><span class="log-time">10:45:12</span> Système initialisé</div>
        <div class="log-entry"><span class="log-time">10:45:15</span> Connexion à la mémoire thermique établie</div>
        <div class="log-entry"><span class="log-time">10:45:18</span> Accélérateurs Kyber activés</div>
        <div class="log-entry"><span class="log-time">10:45:20</span> Interface MCP prête</div>
      </div>
    </div>
  </div>
</div>

<!-- Pied de page fixe -->
<footer class="footer-fixed">
  <small>Vision Ultra - Master Control Program - <span id="current-datetime"></span> - &copy; 2024 Jean Passave - Sainte-Anne, Guadeloupe (97180)</small>
</footer>

<script src="/js/luna-mcp.js"></script>
