<%- include('luna-base') %>
<link rel="stylesheet" href="/css/luna-memory.css">

<script>
// Définir memoryStats directement dans le template pour éviter l'erreur
window.memoryStats = {
    zones: [
        { name: '<PERSON><PERSON><PERSON><PERSON>', count: 150, percentage: 15 },
        { name: '<PERSON><PERSON>', count: 250, percentage: 25 },
        { name: '<PERSON><PERSON><PERSON><PERSON>', count: 200, percentage: 20 },
        { name: '<PERSON><PERSON><PERSON><PERSON>', count: 150, percentage: 15 },
        { name: '<PERSON><PERSON><PERSON>', count: 150, percentage: 15 },
        { name: 'Archive', count: 100, percentage: 10 }
    ],
    total: 1000
};
</script>

<!-- Intégration du contenu principal Luna Mémoire -->
<div class="row mt-4">
  <!-- Panneau principal de mémoire -->
  <div class="col-md-8">
    <div class="luna-card" style="height: 65vh; display: flex; flex-direction: column;">
      <h3 class="mb-3 d-flex align-items-center justify-content-between">
        <div><i class="bi bi-hdd-network me-2"></i> Mémoire Thermique</div>
        <div>
          <button id="memory-settings-btn" class="btn btn-sm btn-luna">
            <i class="bi bi-gear me-1"></i> Paramètres
          </button>
        </div>
      </h3>

      <div id="memory-container" style="flex-grow: 1; overflow-y: auto; margin-bottom: 1rem; padding: 1rem; border-radius: 10px; background-color: rgba(0,0,0,0.2); background-image: linear-gradient(to bottom, rgba(156, 137, 184, 0.05), rgba(240, 166, 202, 0.05));">
        <!-- Visualisation des zones de mémoire thermique -->
        <div class="memory-zones-container">
          <div class="memory-zone-header">
            <h4>Zones de mémoire thermique</h4>
            <p class="text-muted">Visualisation des 6 zones de mémoire thermique et leur contenu</p>
          </div>

          <div class="memory-zones">
            <div class="memory-zone active" data-zone="1">
              <div class="zone-header">
                <h5>Zone 1 - Récente (100°)</h5>
                <span class="badge bg-danger">34 conversations</span>
              </div>
              <div class="zone-content">
                <div class="memory-item">
                  <div class="memory-item-header">
                    <span class="memory-timestamp">Aujourd'hui 14:25</span>
                    <span class="memory-title">Conversation sur la capitale de la France</span>
                  </div>
                  <div class="memory-item-content">
                    <p><strong>User:</strong> Quelle est la capitale de la France ?</p>
                    <p><strong>Assistant:</strong> La capitale de la France est Paris.</p>
                  </div>
                </div>

                <div class="memory-item">
                  <div class="memory-item-header">
                    <span class="memory-timestamp">Aujourd'hui 14:20</span>
                    <span class="memory-title">Question sur le président de la Mauritanie</span>
                  </div>
                  <div class="memory-item-content">
                    <p><strong>User:</strong> Qui est le président actuel de la Mauritanie ?</p>
                    <p><strong>Assistant:</strong> Le président actuel de la Mauritanie est Mohamed Ould Ghazouani, qui a pris ses fonctions le 1er août 2019.</p>
                  </div>
                </div>

                <div class="memory-item">
                  <div class="memory-item-header">
                    <span class="memory-timestamp">Aujourd'hui 14:15</span>
                    <span class="memory-title">Salutations</span>
                  </div>
                  <div class="memory-item-content">
                    <p><strong>User:</strong> Bonjour, comment vas-tu ?</p>
                    <p><strong>Assistant:</strong> Bonjour ! Je vais bien, merci de demander. Comment puis-je vous aider aujourd'hui ?</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="memory-zone" data-zone="2">
              <div class="zone-header">
                <h5>Zone 2 - Chaude (80°)</h5>
                <span class="badge bg-warning">0 conversations</span>
              </div>
              <div class="zone-content">
                <div class="empty-zone-message">
                  <i class="bi bi-info-circle"></i>
                  <p>Aucune conversation dans cette zone</p>
                </div>
              </div>
            </div>

            <div class="memory-zone" data-zone="3">
              <div class="zone-header">
                <h5>Zone 3 - Tiède (60°)</h5>
                <span class="badge bg-info">0 conversations</span>
              </div>
              <div class="zone-content">
                <div class="empty-zone-message">
                  <i class="bi bi-info-circle"></i>
                  <p>Aucune conversation dans cette zone</p>
                </div>
              </div>
            </div>

            <div class="memory-zone" data-zone="4">
              <div class="zone-header">
                <h5>Zone 4 - Fraîche (40°)</h5>
                <span class="badge bg-primary">0 conversations</span>
              </div>
              <div class="zone-content">
                <div class="empty-zone-message">
                  <i class="bi bi-info-circle"></i>
                  <p>Aucune conversation dans cette zone</p>
                </div>
              </div>
            </div>

            <div class="memory-zone" data-zone="5">
              <div class="zone-header">
                <h5>Zone 5 - Froide (20°)</h5>
                <span class="badge bg-secondary">0 conversations</span>
              </div>
              <div class="zone-content">
                <div class="empty-zone-message">
                  <i class="bi bi-info-circle"></i>
                  <p>Aucune conversation dans cette zone</p>
                </div>
              </div>
            </div>

            <div class="memory-zone" data-zone="6">
              <div class="zone-header">
                <h5>Zone 6 - Archive (5°)</h5>
                <span class="badge bg-dark">0 conversations</span>
              </div>
              <div class="zone-content">
                <div class="empty-zone-message">
                  <i class="bi bi-info-circle"></i>
                  <p>Aucune conversation dans cette zone</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Panneau latéral avec les statistiques et contrôles -->
  <div class="col-md-4">
    <!-- Statistiques de mémoire -->
    <div class="luna-card mb-3">
      <h4><i class="bi bi-bar-chart me-2"></i> Statistiques</h4>
      <div class="mb-3">
        <div class="d-flex justify-content-between mb-1">
          <span>Température moyenne</span>
          <span id="avg-temp">72.5°C</span>
        </div>
        <div class="progress mb-3" style="height: 8px;">
          <div class="progress-bar bg-danger" role="progressbar" style="width: 72.5%;" aria-valuenow="72.5" aria-valuemin="0" aria-valuemax="100"></div>
        </div>

        <div class="d-flex justify-content-between mb-1">
          <span>Utilisation de la mémoire</span>
          <span id="memory-usage">34/1000</span>
        </div>
        <div class="progress mb-3" style="height: 8px;">
          <div class="progress-bar bg-success" role="progressbar" style="width: 3.4%;" aria-valuenow="3.4" aria-valuemin="0" aria-valuemax="100"></div>
        </div>

        <div class="d-flex justify-content-between mb-1">
          <span>Efficacité des accélérateurs</span>
          <span id="accelerator-efficiency">166.5%</span>
        </div>
        <div class="progress" style="height: 8px;">
          <div class="progress-bar bg-primary" role="progressbar" style="width: 83.3%;" aria-valuenow="83.3" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
      </div>
    </div>

    <!-- Contrôles de mémoire -->
    <div class="luna-card mb-3">
      <h4><i class="bi bi-sliders me-2"></i> Contrôles</h4>
      <div class="mb-3">
        <button id="refresh-memory-btn" class="btn btn-luna w-100 mb-2">
          <i class="bi bi-arrow-clockwise me-1"></i> Rafraîchir les données
        </button>
        <button id="optimize-memory-btn" class="btn btn-luna-outline w-100 mb-2">
          <i class="bi bi-lightning-charge me-1"></i> Optimiser la mémoire
        </button>
        <button id="clear-memory-btn" class="btn btn-danger w-100">
          <i class="bi bi-trash me-1"></i> Effacer la mémoire
        </button>
      </div>
    </div>

    <!-- Flux thermique -->
    <div class="luna-card">
      <h4><i class="bi bi-thermometer-half me-2"></i> Flux thermique</h4>
      <div class="mb-3">
        <canvas id="thermal-flow-canvas" width="100%" height="200"></canvas>
      </div>
      <div class="d-flex justify-content-between text-center">
        <div>
          <div class="h5 mb-0" id="hot-points">3</div>
          <small class="text-danger">Points chauds</small>
        </div>
        <div>
          <div class="h5 mb-0" id="thermal-transfers">12</div>
          <small class="text-info">Transferts</small>
        </div>
        <div>
          <div class="h5 mb-0" id="cold-points">2</div>
          <small class="text-primary">Points froids</small>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Pied de page fixe -->
<footer class="footer-fixed">
  <small>Vision Ultra - Interface Mémoire Thermique - <span id="current-datetime"></span> - &copy; 2024 Jean Passave - Sainte-Anne, Guadeloupe (97180)</small>
</footer>

<script src="/socket.io/socket.io.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="/js/thermal-flow-visualization.js"></script>
<script src="/js/luna-memory.js"></script>

<script>
  $(document).ready(function() {
    // La visualisation du flux thermique est maintenant gérée par thermal-flow-visualization.js

    // Initialiser Socket.IO pour les mises à jour en temps réel
    const socket = io();

    socket.on('connect', () => {
      console.log('Connecté au serveur pour les mises à jour de mémoire thermique');
    });

    // Écouter les mises à jour de mémoire thermique
    socket.on('memory_update', (data) => {
      console.log('Mise à jour de la mémoire thermique reçue:', data);

      // Mettre à jour la visualisation du flux thermique si elle existe
      if (window.thermalFlowVisualization) {
        window.thermalFlowVisualization.updateState({
          zones: data.zones || [],
          hotPoints: data.hotPoints,
          coldPoints: data.coldPoints,
          transfers: data.transfers
        });
      }

      // Mettre à jour les statistiques
      if (data.avgTemp) {
        $('#avg-temp').text(`${data.avgTemp.toFixed(1)}°C`);
        $('.progress-bar.bg-danger').css('width', `${data.avgTemp}%`);
      }

      if (data.memoryUsage) {
        $('#memory-usage').text(`${data.memoryUsage.current}/${data.memoryUsage.max}`);
        const usagePercent = (data.memoryUsage.current / data.memoryUsage.max) * 100;
        $('.progress-bar.bg-success').css('width', `${usagePercent}%`);
      }

      if (data.acceleratorEfficiency) {
        $('#accelerator-efficiency').text(`${data.acceleratorEfficiency.toFixed(1)}%`);
        $('.progress-bar.bg-primary').css('width', `${Math.min(data.acceleratorEfficiency / 2, 100)}%`);
      }
    });

    // Gestion des zones de mémoire
    $('.memory-zone').on('click', function() {
      $('.memory-zone').removeClass('active');
      $(this).addClass('active');

      // Récupérer l'index de la zone
      const zoneIndex = parseInt($(this).data('zone')) - 1;

      // Simuler un transfert vers cette zone si la visualisation existe
      if (window.thermalFlowVisualization && zoneIndex >= 0) {
        // Simuler un transfert depuis la zone 1 (la plus chaude) vers la zone sélectionnée
        window.thermalFlowVisualization.simulateTransfer(0, zoneIndex);
      }
    });

    // Gestion des boutons
    $('#refresh-memory-btn').on('click', function() {
      // Simuler le rafraîchissement
      $(this).prop('disabled', true);
      $(this).html('<i class="bi bi-arrow-repeat spin me-1"></i> Rafraîchissement...');

      // Simuler une mise à jour des données
      const randomData = {
        zones: Array(6).fill().map((_, i) => ({
          activity: Math.random() * 100 / (i + 1)
        })),
        hotPoints: Math.floor(Math.random() * 5) + 1,
        coldPoints: Math.floor(Math.random() * 3) + 1,
        transfers: Math.floor(Math.random() * 10) + 5,
        avgTemp: Math.random() * 30 + 50,
        memoryUsage: {
          current: Math.floor(Math.random() * 100) + 20,
          max: 1000
        },
        acceleratorEfficiency: Math.random() * 100 + 100
      };

      // Émettre un événement de mise à jour
      socket.emit('request_memory_update', randomData);

      // Mettre à jour la visualisation directement
      if (window.thermalFlowVisualization) {
        window.thermalFlowVisualization.updateState(randomData);
      }

      setTimeout(() => {
        $(this).prop('disabled', false);
        $(this).html('<i class="bi bi-arrow-clockwise me-1"></i> Rafraîchir les données');
        showNotification('Données de mémoire rafraîchies', 'success');
      }, 1000);
    });

    // Gestion du bouton d'optimisation
    $('#optimize-memory-btn').on('click', function() {
      $(this).prop('disabled', true);
      $(this).html('<i class="bi bi-lightning-charge spin me-1"></i> Optimisation...');

      // Simuler une optimisation
      setTimeout(() => {
        // Augmenter l'efficacité des accélérateurs
        const newEfficiency = parseFloat($('#accelerator-efficiency').text()) + 10;
        $('#accelerator-efficiency').text(`${newEfficiency.toFixed(1)}%`);
        $('.progress-bar.bg-primary').css('width', `${Math.min(newEfficiency / 2, 100)}%`);

        // Simuler des transferts entre zones
        if (window.thermalFlowVisualization) {
          window.thermalFlowVisualization.simulateTransfer(0, 2);
          window.thermalFlowVisualization.simulateTransfer(1, 3);
          window.thermalFlowVisualization.simulateTransfer(2, 4);
        }

        $(this).prop('disabled', false);
        $(this).html('<i class="bi bi-lightning-charge me-1"></i> Optimiser la mémoire');
        showNotification('Mémoire thermique optimisée avec succès', 'success');
      }, 2000);
    });

    // Fonction pour afficher une notification
    function showNotification(message, type = 'info') {
      // Créer l'élément de notification s'il n'existe pas
      let notificationArea = $('#notification-area');
      if (notificationArea.length === 0) {
        $('body').append('<div id="notification-area" style="position: fixed; top: 20px; right: 20px; z-index: 9999; width: 300px;"></div>');
        notificationArea = $('#notification-area');
      }

      // Créer la notification
      const notification = $(`<div class="alert alert-${type}" style="margin-bottom: 10px; opacity: 0; transition: opacity 0.3s ease;">${message}</div>`);
      notificationArea.append(notification);

      // Afficher avec animation
      setTimeout(() => {
        notification.css('opacity', '1');

        // Masquer après 3 secondes
        setTimeout(() => {
          notification.css('opacity', '0');
          setTimeout(() => notification.remove(), 300);
        }, 3000);
      }, 100);
    }
  });
</script>
