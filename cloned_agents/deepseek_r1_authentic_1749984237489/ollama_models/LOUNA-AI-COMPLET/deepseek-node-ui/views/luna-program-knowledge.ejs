<!-- Intégration du contenu principal Luna Connaissance du Programme -->
<link rel="stylesheet" href="/css/luna-program-knowledge.css">
<div class="container-fluid mt-4">
  <div class="row">
    <!-- Panneau principal de connaissance du programme -->
    <div class="col-md-8">
      <div class="luna-card" style="height: 65vh; display: flex; flex-direction: column;">
        <h3 class="mb-3 d-flex align-items-center justify-content-between">
          <div><i class="bi bi-cpu me-2"></i> Connaissance du Programme Vision Ultra</div>
          <div>
            <span id="current-datetime" class="datetime-display me-3"></span>
            <button id="scan-program-btn" class="btn btn-sm btn-luna">
              <i class="bi bi-search me-1"></i> Scanner le Programme
            </button>
          </div>
        </h3>

        <div class="program-knowledge-container" style="flex-grow: 1; overflow-y: auto; margin-bottom: 1rem; padding: 1rem; border-radius: 10px; background-color: rgba(0,0,0,0.2); background-image: linear-gradient(to bottom, rgba(156, 137, 184, 0.05), rgba(240, 166, 202, 0.05));">
          <!-- Onglets pour les différentes sections -->
          <ul class="nav nav-tabs" id="programKnowledgeTabs" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab" aria-controls="overview" aria-selected="true">Vue d'ensemble</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="analysis-tab" data-bs-toggle="tab" data-bs-target="#analysis" type="button" role="tab" aria-controls="analysis" aria-selected="false">
                Analyse
                <span id="analysis-badge" class="badge bg-danger ms-1" style="display: none;">0</span>
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="modules-tab" data-bs-toggle="tab" data-bs-target="#modules" type="button" role="tab" aria-controls="modules" aria-selected="false">Modules</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="interfaces-tab" data-bs-toggle="tab" data-bs-target="#interfaces" type="button" role="tab" aria-controls="interfaces" aria-selected="false">Interfaces</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="services-tab" data-bs-toggle="tab" data-bs-target="#services" type="button" role="tab" aria-controls="services" aria-selected="false">Services</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="capabilities-tab" data-bs-toggle="tab" data-bs-target="#capabilities" type="button" role="tab" aria-controls="capabilities" aria-selected="false">Capacités</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="routes-tab" data-bs-toggle="tab" data-bs-target="#routes" type="button" role="tab" aria-controls="routes" aria-selected="false">Routes</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="configurations-tab" data-bs-toggle="tab" data-bs-target="#configurations" type="button" role="tab" aria-controls="configurations" aria-selected="false">Configurations</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="components-tab" data-bs-toggle="tab" data-bs-target="#components" type="button" role="tab" aria-controls="components" aria-selected="false">Composants</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="assets-tab" data-bs-toggle="tab" data-bs-target="#assets" type="button" role="tab" aria-controls="assets" aria-selected="false">Assets</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="styles-tab" data-bs-toggle="tab" data-bs-target="#styles" type="button" role="tab" aria-controls="styles" aria-selected="false">Styles</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="scripts-tab" data-bs-toggle="tab" data-bs-target="#scripts" type="button" role="tab" aria-controls="scripts" aria-selected="false">Scripts</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="models-tab" data-bs-toggle="tab" data-bs-target="#models" type="button" role="tab" aria-controls="models" aria-selected="false">Modèles</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="plugins-tab" data-bs-toggle="tab" data-bs-target="#plugins" type="button" role="tab" aria-controls="plugins" aria-selected="false">Plugins</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab" aria-controls="system" aria-selected="false">Système</button>
            </li>
          </ul>

          <!-- Contenu des onglets -->
          <div class="tab-content mt-3" id="programKnowledgeTabsContent">
            <!-- Analyse -->
            <div class="tab-pane fade" id="analysis" role="tabpanel" aria-labelledby="analysis-tab">
              <div class="row mb-3">
                <div class="col-12">
                  <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4>Analyse de l'interface</h4>
                    <button id="analyze-program-btn" class="btn btn-sm btn-luna">
                      <i class="bi bi-search me-1"></i> Analyser l'interface
                    </button>
                  </div>
                  <p class="text-muted">Cette analyse identifie les éléments manquants et incomplets dans l'interface.</p>
                </div>
              </div>

              <div class="row">
                <div class="col-md-6">
                  <div class="card bg-dark mb-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                      <h5 class="card-title mb-0">Éléments manquants</h5>
                      <span id="missing-count" class="badge bg-danger">0</span>
                    </div>
                    <div class="card-body">
                      <div id="missing-elements-list">
                        <div class="text-center">
                          <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                          </div>
                          <p class="mt-2">Chargement des éléments manquants...</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="col-md-6">
                  <div class="card bg-dark mb-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                      <h5 class="card-title mb-0">Éléments incomplets</h5>
                      <span id="incomplete-count" class="badge bg-warning">0</span>
                    </div>
                    <div class="card-body">
                      <div id="incomplete-elements-list">
                        <div class="text-center">
                          <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                          </div>
                          <p class="mt-2">Chargement des éléments incomplets...</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-12">
                  <div class="card bg-dark">
                    <div class="card-header">
                      <h5 class="card-title mb-0">Dernière analyse</h5>
                    </div>
                    <div class="card-body">
                      <div id="last-analysis-info">
                        <div class="text-center">
                          <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                          </div>
                          <p class="mt-2">Chargement des informations d'analyse...</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Vue d'ensemble -->
            <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
              <div class="row">
                <div class="col-md-6">
                  <div class="card bg-dark mb-3">
                    <div class="card-header">
                      <h5 class="card-title mb-0">Statistiques du Programme</h5>
                    </div>
                    <div class="card-body">
                      <div id="program-stats">
                        <div class="text-center">
                          <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                          </div>
                          <p class="mt-2">Chargement des statistiques...</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="card bg-dark mb-3">
                    <div class="card-header">
                      <h5 class="card-title mb-0">Dernière Analyse</h5>
                    </div>
                    <div class="card-body">
                      <div id="last-scan-info">
                        <div class="text-center">
                          <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                          </div>
                          <p class="mt-2">Chargement des informations...</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row mt-3">
                <div class="col-12">
                  <div class="card bg-dark">
                    <div class="card-header">
                      <h5 class="card-title mb-0">Structure du Programme</h5>
                    </div>
                    <div class="card-body">
                      <div id="program-structure">
                        <div class="text-center">
                          <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                          </div>
                          <p class="mt-2">Chargement de la structure...</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Modules -->
            <div class="tab-pane fade" id="modules" role="tabpanel" aria-labelledby="modules-tab">
              <div id="modules-list">
                <div class="text-center">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                  </div>
                  <p class="mt-2">Chargement des modules...</p>
                </div>
              </div>
            </div>

            <!-- Interfaces -->
            <div class="tab-pane fade" id="interfaces" role="tabpanel" aria-labelledby="interfaces-tab">
              <div id="interfaces-list">
                <div class="text-center">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                  </div>
                  <p class="mt-2">Chargement des interfaces...</p>
                </div>
              </div>
            </div>

            <!-- Services -->
            <div class="tab-pane fade" id="services" role="tabpanel" aria-labelledby="services-tab">
              <div id="services-list">
                <div class="text-center">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                  </div>
                  <p class="mt-2">Chargement des services...</p>
                </div>
              </div>
            </div>

            <!-- Capacités -->
            <div class="tab-pane fade" id="capabilities" role="tabpanel" aria-labelledby="capabilities-tab">
              <div id="capabilities-list">
                <div class="text-center">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                  </div>
                  <p class="mt-2">Chargement des capacités...</p>
                </div>
              </div>
            </div>

            <!-- Routes -->
            <div class="tab-pane fade" id="routes" role="tabpanel" aria-labelledby="routes-tab">
              <div id="routes-list">
                <div class="text-center">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                  </div>
                  <p class="mt-2">Chargement des routes...</p>
                </div>
              </div>
            </div>

            <!-- Configurations -->
            <div class="tab-pane fade" id="configurations" role="tabpanel" aria-labelledby="configurations-tab">
              <div id="configurations-list">
                <div class="text-center">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                  </div>
                  <p class="mt-2">Chargement des configurations...</p>
                </div>
              </div>
            </div>

            <!-- Composants -->
            <div class="tab-pane fade" id="components" role="tabpanel" aria-labelledby="components-tab">
              <div id="components-list">
                <div class="text-center">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                  </div>
                  <p class="mt-2">Chargement des composants...</p>
                </div>
              </div>
            </div>

            <!-- Assets -->
            <div class="tab-pane fade" id="assets" role="tabpanel" aria-labelledby="assets-tab">
              <div id="assets-list">
                <div class="text-center">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                  </div>
                  <p class="mt-2">Chargement des assets...</p>
                </div>
              </div>
            </div>

            <!-- Styles -->
            <div class="tab-pane fade" id="styles" role="tabpanel" aria-labelledby="styles-tab">
              <div id="styles-list">
                <div class="text-center">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                  </div>
                  <p class="mt-2">Chargement des styles...</p>
                </div>
              </div>
            </div>

            <!-- Scripts -->
            <div class="tab-pane fade" id="scripts" role="tabpanel" aria-labelledby="scripts-tab">
              <div id="scripts-list">
                <div class="text-center">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                  </div>
                  <p class="mt-2">Chargement des scripts...</p>
                </div>
              </div>
            </div>

            <!-- Modèles -->
            <div class="tab-pane fade" id="models" role="tabpanel" aria-labelledby="models-tab">
              <div id="models-list">
                <div class="text-center">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                  </div>
                  <p class="mt-2">Chargement des modèles...</p>
                </div>
              </div>
            </div>

            <!-- Plugins -->
            <div class="tab-pane fade" id="plugins" role="tabpanel" aria-labelledby="plugins-tab">
              <div id="plugins-list">
                <div class="text-center">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                  </div>
                  <p class="mt-2">Chargement des plugins...</p>
                </div>
              </div>
            </div>

            <!-- Système -->
            <div class="tab-pane fade" id="system" role="tabpanel" aria-labelledby="system-tab">
              <div id="system-status">
                <div class="text-center">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                  </div>
                  <p class="mt-2">Chargement du statut du système...</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Panneau latéral avec les statistiques et les informations -->
    <div class="col-md-4">
      <!-- Cerveau thermique -->
      <div class="luna-card mb-3">
        <h4><i class="bi bi-thermometer-half me-2"></i> Cerveau Thermique</h4>
        <div class="mb-3">
          <div class="d-flex justify-content-between mb-1">
            <span>Température</span>
            <span id="brain-temperature">42°C</span>
          </div>
          <div class="progress mb-3" style="height: 8px;">
            <div id="brain-temperature-bar" class="progress-bar" role="progressbar" style="width: 42%;" aria-valuenow="42" aria-valuemin="0" aria-valuemax="100"></div>
          </div>

          <div class="d-flex justify-content-between mb-1">
            <span>Activité neuronale</span>
            <span id="brain-activity">85%</span>
          </div>
          <div class="progress mb-3" style="height: 8px;">
            <div id="brain-activity-bar" class="progress-bar" role="progressbar" style="width: 85%;" aria-valuenow="85" aria-valuemin="0" aria-valuemax="100"></div>
          </div>

          <div class="d-flex justify-content-between mb-1">
            <span>Zones actives</span>
            <span id="active-zones">6/10</span>
          </div>
          <div class="progress" style="height: 8px;">
            <div id="active-zones-bar" class="progress-bar" role="progressbar" style="width: 60%;" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100"></div>
          </div>
        </div>
      </div>

      <!-- Connexion Internet -->
      <div class="luna-card mb-3">
        <h4><i class="bi bi-globe me-2"></i> Connexion Internet</h4>
        <div class="mb-3">
          <div class="d-flex justify-content-between mb-1">
            <span>Statut</span>
            <span id="internet-status" class="text-success">Connecté</span>
          </div>
          <div class="d-flex justify-content-between mb-1">
            <span>Latence</span>
            <span id="internet-latency">12 ms</span>
          </div>
          <div class="d-flex justify-content-between mb-1">
            <span>Débit</span>
            <span id="internet-bandwidth">85 Mbps</span>
          </div>
          <div class="d-flex justify-content-between mb-1">
            <span>Recherches actives</span>
            <span id="internet-searches">3</span>
          </div>
        </div>
      </div>

      <!-- Date et heure -->
      <div class="luna-card">
        <h4><i class="bi bi-clock me-2"></i> Date et Heure</h4>
        <div class="mb-3">
          <div class="d-flex justify-content-between mb-1">
            <span>Date actuelle</span>
            <span id="current-date"></span>
          </div>
          <div class="d-flex justify-content-between mb-1">
            <span>Heure actuelle</span>
            <span id="current-time"></span>
          </div>
          <div class="d-flex justify-content-between mb-1">
            <span>Fuseau horaire</span>
            <span id="timezone">Europe/Paris</span>
          </div>
          <div class="d-flex justify-content-between mb-1">
            <span>Format</span>
            <span id="date-format">fr-FR</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Conteneur de toasts -->
<div class="toast-container"></div>

<script src="/js/luna-program-knowledge.js"></script>
