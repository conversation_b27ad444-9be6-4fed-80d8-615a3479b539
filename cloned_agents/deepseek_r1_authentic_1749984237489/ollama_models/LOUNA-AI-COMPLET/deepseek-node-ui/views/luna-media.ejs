<%- include('luna-header') %>

<div class="container-fluid luna-container">
  <div class="row">
    <div class="col-md-3">
      <%- include('luna-sidebar') %>
    </div>
    
    <div class="col-md-9">
      <div class="luna-content">
        <div class="luna-header">
          <h1><i class="bi bi-film"></i> Générateur Multimédia</h1>
          <p class="lead">Générez des vidéos, des images, du code et de la musique avec Vision Ultra</p>
        </div>
        
        <div class="row mb-4">
          <div class="col-md-12">
            <div class="card luna-card">
              <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" id="mediaTypeTabs" role="tablist">
                  <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="image-tab" data-bs-toggle="tab" data-bs-target="#image-content" type="button" role="tab" aria-controls="image-content" aria-selected="true">
                      <i class="bi bi-image"></i> Images
                    </button>
                  </li>
                  <li class="nav-item" role="presentation">
                    <button class="nav-link" id="video-tab" data-bs-toggle="tab" data-bs-target="#video-content" type="button" role="tab" aria-controls="video-content" aria-selected="false">
                      <i class="bi bi-film"></i> Vidéos
                    </button>
                  </li>
                  <li class="nav-item" role="presentation">
                    <button class="nav-link" id="music-tab" data-bs-toggle="tab" data-bs-target="#music-content" type="button" role="tab" aria-controls="music-content" aria-selected="false">
                      <i class="bi bi-music-note-beamed"></i> Musique
                    </button>
                  </li>
                  <li class="nav-item" role="presentation">
                    <button class="nav-link" id="code-tab" data-bs-toggle="tab" data-bs-target="#code-content" type="button" role="tab" aria-controls="code-content" aria-selected="false">
                      <i class="bi bi-code-slash"></i> Code
                    </button>
                  </li>
                </ul>
              </div>
              
              <div class="card-body">
                <div class="tab-content" id="mediaTypeContent">
                  <!-- Onglet Images -->
                  <div class="tab-pane fade show active" id="image-content" role="tabpanel" aria-labelledby="image-tab">
                    <h3>Génération d'Images</h3>
                    <p>Décrivez l'image que vous souhaitez générer en détail.</p>
                    
                    <form id="imageGenerationForm" class="mb-4">
                      <div class="mb-3">
                        <label for="imagePrompt" class="form-label">Description de l'image</label>
                        <textarea class="form-control" id="imagePrompt" rows="3" placeholder="Décrivez l'image que vous souhaitez générer..."></textarea>
                      </div>
                      
                      <div class="row mb-3">
                        <div class="col-md-6">
                          <label for="imageWidth" class="form-label">Largeur</label>
                          <select class="form-select" id="imageWidth">
                            <option value="512">512px</option>
                            <option value="768" selected>768px</option>
                            <option value="1024">1024px</option>
                          </select>
                        </div>
                        <div class="col-md-6">
                          <label for="imageHeight" class="form-label">Hauteur</label>
                          <select class="form-select" id="imageHeight">
                            <option value="512">512px</option>
                            <option value="768" selected>768px</option>
                            <option value="1024">1024px</option>
                          </select>
                        </div>
                      </div>
                      
                      <button type="submit" class="btn btn-luna-primary">
                        <i class="bi bi-image"></i> Générer l'image
                      </button>
                    </form>
                    
                    <div id="imageGenerationProgress" class="progress mb-3 d-none">
                      <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                    </div>
                    
                    <div id="imageResult" class="mt-4 d-none">
                      <h4>Image générée</h4>
                      <div class="card">
                        <div class="card-body text-center">
                          <img id="generatedImage" src="" alt="Image générée" class="img-fluid mb-3">
                          <p id="imagePromptDisplay" class="text-muted"></p>
                        </div>
                      </div>
                    </div>
                    
                    <div id="imageGallery" class="mt-4">
                      <h4>Images récentes</h4>
                      <div class="row" id="imageGalleryContent">
                        <div class="col-12 text-center">
                          <p class="text-muted">Chargement des images...</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Onglet Vidéos -->
                  <div class="tab-pane fade" id="video-content" role="tabpanel" aria-labelledby="video-tab">
                    <h3>Génération de Vidéos</h3>
                    <p>Décrivez la vidéo que vous souhaitez générer en détail.</p>
                    
                    <form id="videoGenerationForm" class="mb-4">
                      <div class="mb-3">
                        <label for="videoPrompt" class="form-label">Description de la vidéo</label>
                        <textarea class="form-control" id="videoPrompt" rows="3" placeholder="Décrivez la vidéo que vous souhaitez générer..."></textarea>
                      </div>
                      
                      <div class="row mb-3">
                        <div class="col-md-6">
                          <label for="videoDuration" class="form-label">Durée (secondes)</label>
                          <select class="form-select" id="videoDuration">
                            <option value="5">5 secondes</option>
                            <option value="10" selected>10 secondes</option>
                            <option value="15">15 secondes</option>
                            <option value="30">30 secondes</option>
                          </select>
                        </div>
                        <div class="col-md-6">
                          <label for="videoResolution" class="form-label">Résolution</label>
                          <select class="form-select" id="videoResolution">
                            <option value="480p">480p</option>
                            <option value="720p" selected>720p</option>
                            <option value="1080p">1080p</option>
                          </select>
                        </div>
                      </div>
                      
                      <button type="submit" class="btn btn-luna-primary">
                        <i class="bi bi-film"></i> Générer la vidéo
                      </button>
                    </form>
                    
                    <div id="videoGenerationProgress" class="progress mb-3 d-none">
                      <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                    </div>
                    
                    <div id="videoResult" class="mt-4 d-none">
                      <h4>Vidéo générée</h4>
                      <div class="card">
                        <div class="card-body text-center">
                          <video id="generatedVideo" controls class="img-fluid mb-3">
                            <source src="" type="video/mp4">
                            Votre navigateur ne supporte pas la lecture de vidéos.
                          </video>
                          <p id="videoPromptDisplay" class="text-muted"></p>
                        </div>
                      </div>
                    </div>
                    
                    <div id="videoGallery" class="mt-4">
                      <h4>Vidéos récentes</h4>
                      <div class="row" id="videoGalleryContent">
                        <div class="col-12 text-center">
                          <p class="text-muted">Chargement des vidéos...</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Onglet Musique -->
                  <div class="tab-pane fade" id="music-content" role="tabpanel" aria-labelledby="music-tab">
                    <h3>Génération de Musique</h3>
                    <p>Décrivez la musique que vous souhaitez générer en détail.</p>
                    
                    <form id="musicGenerationForm" class="mb-4">
                      <div class="mb-3">
                        <label for="musicPrompt" class="form-label">Description de la musique</label>
                        <textarea class="form-control" id="musicPrompt" rows="3" placeholder="Décrivez la musique que vous souhaitez générer..."></textarea>
                      </div>
                      
                      <div class="row mb-3">
                        <div class="col-md-6">
                          <label for="musicDuration" class="form-label">Durée (secondes)</label>
                          <select class="form-select" id="musicDuration">
                            <option value="15">15 secondes</option>
                            <option value="30" selected>30 secondes</option>
                            <option value="60">1 minute</option>
                            <option value="120">2 minutes</option>
                          </select>
                        </div>
                        <div class="col-md-6">
                          <label for="musicGenre" class="form-label">Genre</label>
                          <select class="form-select" id="musicGenre">
                            <option value="ambient">Ambient</option>
                            <option value="electronic" selected>Électronique</option>
                            <option value="jazz">Jazz</option>
                            <option value="rock">Rock</option>
                            <option value="classical">Classique</option>
                          </select>
                        </div>
                      </div>
                      
                      <button type="submit" class="btn btn-luna-primary">
                        <i class="bi bi-music-note-beamed"></i> Générer la musique
                      </button>
                    </form>
                    
                    <div id="musicGenerationProgress" class="progress mb-3 d-none">
                      <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                    </div>
                    
                    <div id="musicResult" class="mt-4 d-none">
                      <h4>Musique générée</h4>
                      <div class="card">
                        <div class="card-body text-center">
                          <audio id="generatedMusic" controls class="w-100 mb-3">
                            <source src="" type="audio/mpeg">
                            Votre navigateur ne supporte pas la lecture audio.
                          </audio>
                          <p id="musicPromptDisplay" class="text-muted"></p>
                        </div>
                      </div>
                    </div>
                    
                    <div id="musicGallery" class="mt-4">
                      <h4>Musiques récentes</h4>
                      <div class="row" id="musicGalleryContent">
                        <div class="col-12 text-center">
                          <p class="text-muted">Chargement des musiques...</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Onglet Code -->
                  <div class="tab-pane fade" id="code-content" role="tabpanel" aria-labelledby="code-tab">
                    <h3>Génération de Code</h3>
                    <p>Décrivez le code que vous souhaitez générer en détail.</p>
                    
                    <form id="codeGenerationForm" class="mb-4">
                      <div class="mb-3">
                        <label for="codePrompt" class="form-label">Description du code</label>
                        <textarea class="form-control" id="codePrompt" rows="3" placeholder="Décrivez le code que vous souhaitez générer..."></textarea>
                      </div>
                      
                      <div class="row mb-3">
                        <div class="col-md-6">
                          <label for="codeLanguage" class="form-label">Langage</label>
                          <select class="form-select" id="codeLanguage">
                            <option value="javascript" selected>JavaScript</option>
                            <option value="python">Python</option>
                            <option value="java">Java</option>
                            <option value="cpp">C++</option>
                            <option value="php">PHP</option>
                            <option value="html">HTML</option>
                            <option value="css">CSS</option>
                          </select>
                        </div>
                        <div class="col-md-6">
                          <label for="codeComplexity" class="form-label">Complexité</label>
                          <select class="form-select" id="codeComplexity">
                            <option value="simple">Simple</option>
                            <option value="medium" selected>Moyenne</option>
                            <option value="complex">Complexe</option>
                          </select>
                        </div>
                      </div>
                      
                      <button type="submit" class="btn btn-luna-primary">
                        <i class="bi bi-code-slash"></i> Générer le code
                      </button>
                    </form>
                    
                    <div id="codeGenerationProgress" class="progress mb-3 d-none">
                      <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                    </div>
                    
                    <div id="codeResult" class="mt-4 d-none">
                      <h4>Code généré</h4>
                      <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                          <span id="codeLanguageDisplay">JavaScript</span>
                          <button id="copyCodeBtn" class="btn btn-sm btn-luna-outline">
                            <i class="bi bi-clipboard"></i> Copier
                          </button>
                        </div>
                        <div class="card-body">
                          <pre><code id="generatedCode" class="language-javascript"></code></pre>
                          <p id="codePromptDisplay" class="text-muted mt-3"></p>
                        </div>
                      </div>
                    </div>
                    
                    <div id="codeGallery" class="mt-4">
                      <h4>Codes récents</h4>
                      <div class="row" id="codeGalleryContent">
                        <div class="col-12 text-center">
                          <p class="text-muted">Chargement des codes...</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Section des tâches en cours -->
        <div class="row">
          <div class="col-md-12">
            <div class="card luna-card">
              <div class="card-header">
                <h5><i class="bi bi-list-task"></i> Tâches en cours</h5>
              </div>
              <div class="card-body">
                <div id="activeJobsList">
                  <p class="text-center text-muted">Aucune tâche en cours</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- JavaScript spécifique à la page de génération multimédia -->
<script src="/js/luna-media.js"></script>

<%- include('luna-footer') %>
