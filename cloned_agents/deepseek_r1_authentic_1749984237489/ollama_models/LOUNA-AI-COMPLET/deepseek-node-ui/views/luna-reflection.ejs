<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Luna - Réflexion</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="/css/luna-reflection.css">
  <style>
    :root {
      --luna-primary: #9c89b8;
      --luna-secondary: #f0a6ca;
      --luna-accent: #b8bedd;
      --luna-dark: #1a1a2e;
      --luna-light: #edf2fb;
      --luna-gradient: linear-gradient(135deg, #9c89b8, #f0a6ca);
      --header-height: 60px;
    }

    body {
      font-family: 'Quicksand', sans-serif;
      background-color: var(--luna-dark);
      color: var(--luna-light);
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }

    .navbar-luna {
      background: var(--luna-gradient);
      box-shadow: 0 2px 15px rgba(0,0,0,0.2);
    }

    .navbar-brand {
      font-weight: 700;
      font-size: 1.8rem;
    }

    .nav-link {
      font-weight: 500;
      color: var(--luna-dark) !important;
      transition: all 0.3s ease;
    }

    .nav-link:hover {
      color: white !important;
      transform: translateY(-2px);
    }

    .luna-container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 2rem;
    }

    .luna-card {
      background: rgba(26, 26, 46, 0.7);
      border: 1px solid rgba(184, 190, 221, 0.2);
      border-radius: 15px;
      padding: 1.5rem;
      margin-bottom: 1.5rem;
      box-shadow: 0 10px 25px rgba(0,0,0,0.2);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
    }

    .luna-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 35px rgba(0,0,0,0.25);
      border-color: var(--luna-secondary);
    }

    .status-indicator {
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 8px;
      transition: all 0.5s ease;
    }

    .status-active {
      background-color: #4caf50;
      box-shadow: 0 0 10px #4caf50;
      animation: pulse 2s infinite;
    }

    .status-inactive {
      background-color: #f44336;
    }

    .btn-luna {
      background: var(--luna-gradient);
      border: none;
      border-radius: 25px;
      font-weight: 600;
      padding: 0.5rem 1.5rem;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(0,0,0,0.15);
    }

    .btn-luna:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 20px rgba(0,0,0,0.2);
    }

    .btn-luna-outline {
      background: transparent;
      border: 2px solid var(--luna-accent);
      color: var(--luna-accent);
    }

    .btn-luna-outline:hover {
      background: var(--luna-accent);
      color: var(--luna-dark);
    }

    /* Styles pour les notifications */
    #notification-area {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 9999;
      width: 300px;
    }

    .notification {
      background: rgba(26, 26, 46, 0.9);
      border-left: 4px solid var(--luna-primary);
      color: var(--luna-light);
      padding: 15px;
      margin-bottom: 10px;
      border-radius: 5px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
      transform: translateX(120%);
      transition: transform 0.3s ease;
    }

    .notification.show {
      transform: translateX(0);
    }

    .notification-success {
      border-left-color: #4caf50;
    }

    .notification-error {
      border-left-color: #f44336;
    }

    .notification-info {
      border-left-color: var(--luna-accent);
    }

    /* Styles pour le pied de page fixe */
    .footer-fixed {
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 40px;
      background-color: var(--luna-dark);
      border-top: 1px solid rgba(184, 190, 221, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      color: var(--luna-light);
    }
  </style>
</head>
<body>
  <!-- Barre de navigation -->
  <nav class="navbar navbar-expand-lg navbar-dark navbar-luna">
    <div class="container">
      <a class="navbar-brand" href="/luna">
        <i class="bi bi-moon-stars me-2"></i> Luna
      </a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarLuna">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarLuna">
        <ul class="navbar-nav ms-auto">
          <li class="nav-item">
            <a class="nav-link" href="/luna"><i class="bi bi-house"></i> Accueil</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/luna/prompts"><i class="bi bi-lightning"></i> Prompts</a>
          </li>
          <li class="nav-item">
            <a class="nav-link active" href="/luna/reflection"><i class="bi bi-lightbulb"></i> Réflexion</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/luna/memory"><i class="bi bi-hdd"></i> Mémoire</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/luna/brain"><i class="bi bi-diagram-3"></i> Cerveau</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/luna/accelerators"><i class="bi bi-lightning-charge"></i> Accélérateurs</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/luna/mcp"><i class="bi bi-cpu"></i> MCP</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/luna/settings"><i class="bi bi-gear"></i> Paramètres</a>
          </li>
        </ul>
      </div>
    </div>
  </nav>

  <div class="luna-container">
    <div class="row align-items-center mb-4">
      <div class="col-md-8">
        <h1 class="mb-0" style="font-weight: 700; letter-spacing: -1px;">Interface Luna</h1>
        <p class="text-muted">Système cognitif avancé avec mémoire thermique</p>
      </div>
      <div class="col-md-4 text-end">
        <div class="d-flex align-items-center justify-content-end">
          <span class="me-3">Statut du système:</span>
          <span id="systemStatus">
            <span class="status-indicator status-active"></span>
            <span>Actif</span>
          </span>
          <button id="toggleSystem" class="btn btn-danger ms-3">
            <i class="bi bi-power"></i> Désactiver
          </button>
        </div>
      </div>
    </div>

    <!-- Intégration du contenu principal Luna Réflexion -->
    <div class="row">
      <div class="col-md-8">
        <div class="luna-card">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h3 class="mb-0"><i class="bi bi-lightbulb me-2"></i> Réflexion</h3>
            <button id="reflection-settings-btn" class="btn btn-sm btn-luna-outline">
              <i class="bi bi-gear me-1"></i> Paramètres avancés
            </button>
          </div>
          <p class="text-muted mb-4">Visualisez le processus de réflexion interne de Luna en temps réel. La réflexion est accélérée par les accélérateurs Kyber pour une performance optimale.</p>

          <div class="mb-4">
            <label for="reflection-input" class="form-label">Entrée pour la réflexion</label>
            <textarea id="reflection-input" class="form-control mb-2" rows="3" placeholder="Entrez un texte ou une question pour voir la réflexion de Luna..."></textarea>
            <div class="d-flex justify-content-between">
              <div>
                <button id="start-reflection-btn" class="btn btn-luna me-2">
                  <i class="bi bi-play-fill me-1"></i> Démarrer la réflexion
                </button>
                <button id="clear-reflection-btn" class="btn btn-secondary" disabled>
                  <i class="bi bi-x-lg me-1"></i> Effacer
                </button>
              </div>
              <div>
                <button id="optimize-accelerators-btn" class="btn btn-luna-outline">
                  <i class="bi bi-lightning-charge me-1"></i> Optimiser les accélérateurs
                </button>
              </div>
            </div>
          </div>

          <div id="reflection-output-container" class="mb-4" style="display: none;">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <h5 class="mb-0">Résultat de la réflexion</h5>
              <div>
                <span class="badge bg-primary me-2" id="reflection-time">0.00s</span>
                <span class="badge bg-success" id="reflection-acceleration">x1.0</span>
              </div>
            </div>
            <div id="reflection-output" class="p-3 rounded" style="background-color: rgba(26, 26, 46, 0.5); border: 1px solid rgba(184, 190, 221, 0.2);">
              <p class="text-muted text-center">La réflexion apparaîtra ici...</p>
            </div>
          </div>

          <div id="reflection-details-container" style="display: none;">
            <div class="d-flex align-items-center mb-2">
              <h5 class="mb-0 me-auto">Détails de la réflexion</h5>
              <button class="btn btn-sm btn-link text-decoration-none" id="toggle-reflection-details">
                <span id="reflection-details-text">Afficher les détails</span> <i class="bi bi-chevron-down" id="reflection-details-icon"></i>
              </button>
            </div>
            <div id="reflection-details" class="p-3 rounded mb-3" style="display: none; background-color: rgba(26, 26, 46, 0.5); border: 1px solid rgba(184, 190, 221, 0.2);">
              <p class="text-muted text-center">Les détails de la réflexion apparaîtront ici...</p>
            </div>
          </div>

          <div class="d-flex justify-content-between align-items-center">
            <div>
              <span class="text-muted">Statut de la réflexion:</span>
              <span id="reflection-status" class="ms-2">
                <span class="status-indicator status-inactive"></span>
                <span>Inactif</span>
              </span>
            </div>
            <div>
              <button id="save-reflection-btn" class="btn btn-sm btn-luna-outline" disabled>
                <i class="bi bi-save me-1"></i> Sauvegarder
              </button>
              <button id="share-reflection-btn" class="btn btn-sm btn-luna-outline" disabled>
                <i class="bi bi-share me-1"></i> Partager
              </button>
            </div>
          </div>
        </div>

        <div class="luna-card">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h3 class="mb-0"><i class="bi bi-clock-history me-2"></i> Historique des réflexions</h3>
            <div>
              <button id="clear-history-btn" class="btn btn-sm btn-secondary">
                <i class="bi bi-trash me-1"></i> Effacer l'historique
              </button>
            </div>
          </div>

          <div id="reflection-history" class="mb-0">
            <div class="text-center text-muted py-4">
              <i class="bi bi-clock-history fs-4 mb-2 d-block"></i>
              Aucune réflexion dans l'historique
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-4">
        <div class="luna-card">
          <h3 class="mb-3"><i class="bi bi-speedometer2 me-2"></i> Accélérateurs Kyber</h3>
          <p class="text-muted mb-3">Performance des accélérateurs Kyber pour la réflexion</p>

          <div class="mb-4">
            <canvas id="accelerators-chart" height="200"></canvas>
          </div>

          <div class="mb-3">
            <div class="d-flex justify-content-between mb-1">
              <span>Efficacité globale</span>
              <span id="global-efficiency">165.0%</span>
            </div>
            <div class="progress mb-3" style="height: 8px;">
              <div class="progress-bar bg-success" role="progressbar" style="width: 82.5%;" aria-valuenow="82.5" aria-valuemin="0" aria-valuemax="100"></div>
            </div>

            <div class="d-flex justify-content-between mb-1">
              <span>Accélérateurs de réflexion</span>
              <span id="reflection-accelerators">172.9%</span>
            </div>
            <div class="progress mb-3" style="height: 8px;">
              <div class="progress-bar bg-info" role="progressbar" style="width: 86.5%;" aria-valuenow="86.5" aria-valuemin="0" aria-valuemax="100"></div>
            </div>

            <div class="d-flex justify-content-between mb-1">
              <span>Accélérateurs de mémoire</span>
              <span id="memory-accelerators">141.5%</span>
            </div>
            <div class="progress mb-3" style="height: 8px;">
              <div class="progress-bar bg-primary" role="progressbar" style="width: 70.8%;" aria-valuenow="70.8" aria-valuemin="0" aria-valuemax="100"></div>
            </div>

            <div class="d-flex justify-content-between mb-1">
              <span>Accélérateurs thermiques</span>
              <span id="thermal-accelerators">131.1%</span>
            </div>
            <div class="progress" style="height: 8px;">
              <div class="progress-bar bg-warning" role="progressbar" style="width: 65.6%;" aria-valuenow="65.6" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>
        </div>

        <div class="luna-card">
          <h3 class="mb-3"><i class="bi bi-thermometer-half me-2"></i> Mémoire thermique</h3>
          <p class="text-muted mb-3">État actuel de la mémoire thermique</p>

          <div class="mb-3">
            <div class="d-flex justify-content-between mb-1">
              <span>Zone active</span>
              <span id="active-zone">Zone 3 (60°)</span>
            </div>
            <div class="progress mb-3" style="height: 8px;">
              <div class="progress-bar bg-danger" role="progressbar" style="width: 60%;" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100"></div>
            </div>

            <div class="d-flex justify-content-between mb-1">
              <span>Température CPU</span>
              <span id="cpu-temp">42.9°C</span>
            </div>
            <div class="progress mb-3" style="height: 8px;">
              <div class="progress-bar bg-info" role="progressbar" style="width: 50.5%;" aria-valuenow="50.5" aria-valuemin="0" aria-valuemax="100"></div>
            </div>

            <div class="d-flex justify-content-between mb-1">
              <span>Activité du système</span>
              <span id="system-activity">82.4%</span>
            </div>
            <div class="progress" style="height: 8px;">
              <div class="progress-bar bg-success" role="progressbar" style="width: 82.4%;" aria-valuenow="82.4" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="text-center">
            <button id="refresh-thermal-btn" class="btn btn-sm btn-luna-outline">
              <i class="bi bi-arrow-clockwise me-1"></i> Rafraîchir les données
            </button>
          </div>
        </div>
      </div>
    </div>
<div class="row mt-4">
  <!-- Panneau principal de réflexion -->
  <div class="col-md-8">
    <div class="luna-card" style="height: 65vh; display: flex; flex-direction: column;">
      <h3 class="mb-3 d-flex align-items-center justify-content-between">
        <div><i class="bi bi-lightbulb me-2"></i> Réflexion</div>
        <div>
          <button id="reflection-settings-btn" class="btn btn-sm btn-luna">
            <i class="bi bi-gear me-1"></i> Paramètres
          </button>
        </div>
      </h3>

      <div id="reflection-container" style="flex-grow: 1; overflow-y: auto; margin-bottom: 1rem; padding: 1rem; border-radius: 10px; background-color: rgba(0,0,0,0.2); background-image: linear-gradient(to bottom, rgba(156, 137, 184, 0.05), rgba(240, 166, 202, 0.05));">
        <!-- Zone de réflexion -->
        <div id="reflection-area" class="p-3">
          <div class="reflection-header">
            <h4>Processus de réflexion</h4>
            <p class="text-muted">Cette zone affiche le processus de réflexion du modèle Claude lors de la génération de réponses.</p>
          </div>

          <div class="reflection-examples">
            <h5>Exemples de réflexion</h5>

            <div class="reflection-example">
              <div class="reflection-example-header">
                <span class="badge bg-primary">Question</span>
                <span>Quelle est la capitale de l'Italie?</span>
              </div>
              <div class="reflection-example-content">
                <div class="reflection-thinking">
                  <i class="bi bi-lightbulb me-2"></i> <strong>Réflexion:</strong>
                  <p>Je dois répondre à une question factuelle sur la capitale de l'Italie. C'est une information géographique de base que je connais avec certitude. La capitale de l'Italie est Rome. Je vais fournir cette information de manière claire et concise.</p>
                </div>
                <div class="reflection-response">
                  <i class="bi bi-chat-dots me-2"></i> <strong>Réponse:</strong>
                  <p>La capitale de l'Italie est Rome.</p>
                </div>
              </div>
            </div>

            <div class="reflection-example mt-4">
              <div class="reflection-example-header">
                <span class="badge bg-primary">Question</span>
                <span>Comment fonctionne la photosynthèse?</span>
              </div>
              <div class="reflection-example-content">
                <div class="reflection-thinking">
                  <i class="bi bi-lightbulb me-2"></i> <strong>Réflexion:</strong>
                  <p>Cette question porte sur un processus biologique fondamental. Je dois expliquer la photosynthèse de manière claire et accessible. Je vais structurer ma réponse en expliquant d'abord ce qu'est la photosynthèse, puis les étapes principales du processus, les éléments nécessaires et les produits générés. Je vais éviter d'être trop technique tout en restant scientifiquement précis.</p>
                </div>
                <div class="reflection-response">
                  <i class="bi bi-chat-dots me-2"></i> <strong>Réponse:</strong>
                  <p>La photosynthèse est le processus par lequel les plantes, les algues et certaines bactéries convertissent l'énergie lumineuse en énergie chimique. Voici comment cela fonctionne:</p>
                  <ol>
                    <li>Les plantes captent la lumière du soleil grâce à la chlorophylle présente dans leurs feuilles</li>
                    <li>Elles absorbent également du dioxyde de carbone (CO2) de l'air par de petits pores appelés stomates</li>
                    <li>L'eau (H2O) est absorbée par les racines et transportée vers les feuilles</li>
                    <li>Grâce à l'énergie lumineuse, la plante transforme le CO2 et l'H2O en glucose (sucre) et en oxygène (O2)</li>
                    <li>L'oxygène est libéré dans l'atmosphère, tandis que le glucose est utilisé comme source d'énergie ou stocké</li>
                  </ol>
                  <p>Cette équation résume le processus: 6 CO2 + 6 H2O + lumière → C6H12O6 (glucose) + 6 O2</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Panneau latéral avec les paramètres et statistiques -->
  <div class="col-md-4">
    <!-- Paramètres de réflexion -->
    <div class="luna-card mb-3">
      <h4><i class="bi bi-sliders me-2"></i> Paramètres</h4>
      <div class="mb-3">
        <label class="form-label">Affichage de la réflexion</label>
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="show-reflection-toggle" checked>
          <label class="form-check-label" for="show-reflection-toggle">
            Afficher la réflexion
          </label>
        </div>
      </div>
      <div class="mb-3">
        <label class="form-label">Traduction automatique</label>
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="auto-translate-toggle" checked>
          <label class="form-check-label" for="auto-translate-toggle">
            Traduire en français
          </label>
        </div>
      </div>
      <div class="mb-3">
        <label for="reflection-style" class="form-label">Style d'affichage</label>
        <select class="form-select" id="reflection-style">
          <option value="collapsed" selected>Masqué par défaut</option>
          <option value="expanded">Affiché par défaut</option>
          <option value="inline">Intégré à la réponse</option>
        </select>
      </div>
    </div>

    <!-- Accélérateurs de réflexion -->
    <div class="luna-card mb-3">
      <h4><i class="bi bi-lightning me-2"></i> Accélérateurs</h4>
      <div class="mb-2">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span>Accélérateur #1</span>
          <span id="acc-reflection-1-status">Actif</span>
        </div>
        <div class="progress" style="height: 8px;">
          <div id="acc-reflection-1-bar" class="progress-bar" role="progressbar" style="width: 90%;" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
      </div>
      <div class="mb-2">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span>Accélérateur #2</span>
          <span id="acc-reflection-2-status">Actif</span>
        </div>
        <div class="progress" style="height: 8px;">
          <div id="acc-reflection-2-bar" class="progress-bar" role="progressbar" style="width: 85%;" aria-valuenow="85" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
      </div>
      <div class="mb-2">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span>Accélérateur #3</span>
          <span id="acc-reflection-3-status">Actif</span>
        </div>
        <div class="progress" style="height: 8px;">
          <div id="acc-reflection-3-bar" class="progress-bar" role="progressbar" style="width: 95%;" aria-valuenow="95" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
      </div>
      <div class="d-grid gap-2 mt-3">
        <button id="optimize-accelerators-btn" class="btn btn-sm btn-luna">
          <i class="bi bi-lightning-charge me-1"></i> Optimiser les accélérateurs
        </button>
      </div>
    </div>

    <!-- Statistiques -->
    <div class="luna-card">
      <h4><i class="bi bi-bar-chart me-2"></i> Statistiques</h4>
      <div class="mb-2">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span>Temps moyen de réflexion</span>
          <span id="avg-reflection-time">1.2s</span>
        </div>
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span>Ratio réflexion/réponse</span>
          <span id="reflection-ratio">2.4:1</span>
        </div>
        <div class="d-flex justify-content-between align-items-center">
          <span>Efficacité de traduction</span>
          <span id="translation-efficiency">92%</span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal pour les paramètres avancés de réflexion -->
<div class="modal fade" id="reflection-advanced-modal" tabindex="-1" aria-labelledby="reflectionAdvancedModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content bg-dark">
      <div class="modal-header">
        <h5 class="modal-title" id="reflectionAdvancedModalLabel">Paramètres avancés de réflexion</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fermer"></button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-md-6">
            <h6 class="mb-3">Paramètres de traduction</h6>
            <div class="mb-3">
              <label for="translation-quality" class="form-label">Qualité de traduction</label>
              <select class="form-select" id="translation-quality">
                <option value="high">Haute (plus lent)</option>
                <option value="medium" selected>Moyenne (équilibré)</option>
                <option value="low">Basse (plus rapide)</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="translation-model" class="form-label">Modèle de traduction</label>
              <select class="form-select" id="translation-model">
                <option value="neural" selected>Neuronal (Claude)</option>
                <option value="statistical">Statistique (plus rapide)</option>
                <option value="hybrid">Hybride (équilibré)</option>
              </select>
            </div>
            <div class="mb-3">
              <label class="form-label">Langues cibles</label>
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="lang-fr" checked>
                <label class="form-check-label" for="lang-fr">Français</label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="lang-en">
                <label class="form-check-label" for="lang-en">Anglais</label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="lang-es">
                <label class="form-check-label" for="lang-es">Espagnol</label>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <h6 class="mb-3">Paramètres d'extraction</h6>
            <div class="mb-3">
              <label for="extraction-depth" class="form-label">Profondeur d'extraction</label>
              <input type="range" class="form-range" id="extraction-depth" min="1" max="5" value="3">
              <div class="d-flex justify-content-between">
                <small>Superficielle</small>
                <small id="depth-value">Moyenne (3)</small>
                <small>Profonde</small>
              </div>
            </div>
            <div class="mb-3">
              <label for="extraction-format" class="form-label">Format d'extraction</label>
              <select class="form-select" id="extraction-format">
                <option value="structured" selected>Structuré (par étapes)</option>
                <option value="narrative">Narratif (flux de pensée)</option>
                <option value="analytical">Analytique (pros/cons)</option>
              </select>
            </div>
            <div class="mb-3">
              <label class="form-label">Options avancées</label>
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="extract-emotions" checked>
                <label class="form-check-label" for="extract-emotions">Extraire les émotions</label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="extract-uncertainty" checked>
                <label class="form-check-label" for="extract-uncertainty">Extraire l'incertitude</label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="extract-sources">
                <label class="form-check-label" for="extract-sources">Extraire les sources</label>
              </div>
            </div>
          </div>
        </div>
        <hr>
        <div class="row">
          <div class="col-12">
            <h6 class="mb-3">Intégration avec les accélérateurs</h6>
            <div class="mb-3">
              <label for="accelerator-priority" class="form-label">Priorité des accélérateurs</label>
              <select class="form-select" id="accelerator-priority">
                <option value="speed">Vitesse (priorité maximale)</option>
                <option value="balanced" selected>Équilibrée</option>
                <option value="quality">Qualité (priorité maximale)</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="thermal-allocation" class="form-label">Allocation de mémoire thermique</label>
              <input type="range" class="form-range" id="thermal-allocation" min="10" max="90" value="50">
              <div class="d-flex justify-content-between">
                <small>10% (minimal)</small>
                <small id="allocation-value">50%</small>
                <small>90% (maximal)</small>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
        <button type="button" class="btn btn-primary" id="save-advanced-settings">Enregistrer</button>
      </div>
    </div>
  </div>
</div>

<!-- Bibliothèques JavaScript nécessaires -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/socket.io/client-dist/socket.io.min.js"></script>
<!-- Service d'accélérateurs de réflexion -->
<script src="/js/services/reflection-accelerators.js"></script>
<!-- Script principal de la page de réflexion -->
<script src="/js/luna-reflection.js"></script>

<script>
  $(document).ready(function() {
    // Script global pour Luna
    console.log('Interface Luna Réflexion chargée');

    // Fonctionnalité du bouton d'activation système
    $('#toggleSystem').on('click', function() {
      const isActive = $('#systemStatus .status-indicator').hasClass('status-active');

      if (isActive) {
        // Désactiver le système
        $('#systemStatus .status-indicator').removeClass('status-active').addClass('status-inactive');
        $('#systemStatus span:not(.status-indicator)').text('Inactif');
        $(this).removeClass('btn-danger').addClass('btn-success').html('<i class="bi bi-power"></i> Activer');
      } else {
        // Activer le système
        $('#systemStatus .status-indicator').removeClass('status-inactive').addClass('status-active');
        $('#systemStatus span:not(.status-indicator)').text('Actif');
        $(this).removeClass('btn-success').addClass('btn-danger').html('<i class="bi bi-power"></i> Désactiver');
      }
    });

    // Gestionnaire pour le bouton de paramètres avancés
    $('#reflection-settings-btn').on('click', function() {
      $('#reflection-advanced-modal').modal('show');
    });

    // Mise à jour des valeurs des sliders
    $('#extraction-depth').on('input', function() {
      const value = $(this).val();
      let depthText = 'Moyenne (3)';

      if (value == 1) depthText = 'Très superficielle (1)';
      else if (value == 2) depthText = 'Superficielle (2)';
      else if (value == 4) depthText = 'Profonde (4)';
      else if (value == 5) depthText = 'Très profonde (5)';

      $('#depth-value').text(depthText);
    });

    $('#thermal-allocation').on('input', function() {
      $('#allocation-value').text($(this).val() + '%');
    });

    // Gestionnaire pour le bouton de sauvegarde des paramètres avancés
    $('#save-advanced-settings').on('click', function() {
      // Simuler la sauvegarde
      $(this).prop('disabled', true).html('<i class="bi bi-arrow-repeat spin me-1"></i> Enregistrement...');

      setTimeout(() => {
        $(this).prop('disabled', false).html('Enregistrer');
        $('#reflection-advanced-modal').modal('hide');

        // Afficher une notification
        showNotification('Paramètres avancés de réflexion enregistrés avec succès', 'success');
      }, 1000);
    });

    // Fonction pour afficher une notification
    function showNotification(message, type = 'info') {
      // Créer l'élément de notification s'il n'existe pas
      let notificationArea = $('#notification-area');
      if (notificationArea.length === 0) {
        $('body').append('<div id="notification-area"></div>');
        notificationArea = $('#notification-area');
      }

      // Créer la notification
      const notification = $(`<div class="notification notification-${type}">${message}</div>`);
      notificationArea.append(notification);

      // Afficher avec animation
      setTimeout(() => {
        notification.addClass('show');

        // Masquer après 3 secondes
        setTimeout(() => {
          notification.removeClass('show');
          setTimeout(() => notification.remove(), 300);
        }, 3000);
      }, 100);
    }
  });
</script>

<!-- Pied de page fixe -->
<footer class="footer-fixed">
  <small>Vision Ultra - Interface Réflexion - <span id="current-datetime"></span> - &copy; 2024 Jean Passave - Sainte-Anne, Guadeloupe (97180)</small>
</footer>
</body>
</html>
