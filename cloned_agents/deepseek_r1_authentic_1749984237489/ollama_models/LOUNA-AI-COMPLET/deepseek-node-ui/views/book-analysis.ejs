<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Analyse de Livres - DeepSeek r1</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github-dark.min.css">
  <link rel="stylesheet" href="/css/style.css">
  <style>
    .book-card {
      transition: transform 0.3s ease;
    }
    .book-card:hover {
      transform: translateY(-5px);
    }
    .analysis-section {
      margin-bottom: 2rem;
    }
    .chart-container {
      height: 300px;
      margin-bottom: 1.5rem;
    }
    .tag-cloud {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      margin-top: 1rem;
    }
    .tag {
      padding: 0.25rem 0.75rem;
      border-radius: 20px;
      background-color: #343a40;
      font-size: 0.9rem;
    }
    .upload-area {
      border: 2px dashed #6c757d;
      border-radius: 5px;
      padding: 2rem;
      text-align: center;
      cursor: pointer;
      transition: border-color 0.3s ease;
    }
    .upload-area:hover {
      border-color: #0d6efd;
    }
    .upload-area.dragover {
      border-color: #0d6efd;
      background-color: rgba(13, 110, 253, 0.1);
    }
    .progress-container {
      margin-top: 1rem;
      display: none;
    }
  </style>
</head>
<body class="bg-dark text-light">
  <div class="container-fluid">
    <div class="row">
      <!-- Sidebar -->
      <div class="col-md-3 col-lg-2 sidebar p-3">
        <div class="d-flex align-items-center mb-4">
          <i class="bi bi-robot fs-2 me-2"></i>
          <h3 class="mb-0">DeepSeek r1</h3>
          <span class="badge bg-success ms-2">Local</span>
        </div>
        
        <div class="mb-4">
          <a href="/" class="btn btn-outline-light w-100 mb-3">
            <i class="bi bi-chat-dots me-2"></i>Chat
          </a>
          <a href="/book-analysis" class="btn btn-primary w-100 mb-3">
            <i class="bi bi-book me-2"></i>Analyse de Livres
          </a>
        </div>
        
        <div class="mb-4">
          <h5>Livres analysés</h5>
          <div id="books-list" class="list-group list-group-flush">
            <div class="text-center p-3">
              <div class="spinner-border spinner-border-sm text-light" role="status"></div>
              <div class="mt-2">Chargement des livres...</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Main content -->
      <div class="col-md-9 col-lg-10 main-content p-0">
        <div class="d-flex flex-column h-100">
          <div class="p-3 border-bottom border-secondary">
            <div class="d-flex justify-content-between align-items-center">
              <h4 id="page-title">Analyse de Livres</h4>
              <div>
                <button class="btn btn-outline-light btn-sm me-2" id="refresh-btn">
                  <i class="bi bi-arrow-clockwise me-1"></i>Rafraîchir
                </button>
                <button class="btn btn-primary btn-sm" id="upload-btn">
                  <i class="bi bi-upload me-1"></i>Télécharger un livre
                </button>
              </div>
            </div>
          </div>
          
          <div class="p-3 flex-grow-1 overflow-auto" id="main-container">
            <!-- Vue d'accueil -->
            <div id="home-view">
              <div class="row">
                <div class="col-md-6 mb-4">
                  <div class="card bg-dark border-secondary">
                    <div class="card-body">
                      <h5 class="card-title">Télécharger un livre</h5>
                      <p class="card-text">Téléchargez un livre au format PDF, EPUB ou TXT pour l'analyser.</p>
                      <div class="upload-area" id="drop-area">
                        <i class="bi bi-cloud-upload fs-1"></i>
                        <p class="mt-3">Glissez-déposez un fichier ici ou cliquez pour sélectionner</p>
                        <input type="file" id="file-input" class="d-none" accept=".pdf,.epub,.txt">
                      </div>
                      <div class="progress-container">
                        <div class="progress">
                          <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div class="text-center mt-1 small" id="progress-text">Préparation...</div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="col-md-6 mb-4">
                  <div class="card bg-dark border-secondary">
                    <div class="card-body">
                      <h5 class="card-title">À propos de l'analyse de livres</h5>
                      <p class="card-text">Notre système d'analyse de livres utilise des techniques avancées pour extraire des informations précieuses de vos livres :</p>
                      <ul>
                        <li>Analyse statistique du texte</li>
                        <li>Génération de résumés automatiques</li>
                        <li>Identification des thèmes principaux</li>
                        <li>Analyse des sentiments</li>
                        <li>Application de techniques MPC (Model Predictive Control)</li>
                      </ul>
                      <p class="card-text">Les formats pris en charge sont PDF, EPUB et TXT.</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <h5 class="mt-3 mb-4">Livres récemment analysés</h5>
              <div class="row" id="recent-books">
                <div class="text-center p-5">
                  <div class="spinner-border text-light" role="status"></div>
                  <div class="mt-3">Chargement des livres récents...</div>
                </div>
              </div>
            </div>
            
            <!-- Vue détaillée d'un livre -->
            <div id="book-detail-view" class="d-none">
              <div class="text-center p-5" id="book-loading">
                <div class="spinner-border text-light" role="status"></div>
                <div class="mt-3">Chargement de l'analyse...</div>
              </div>
              
              <div id="book-content" class="d-none">
                <div class="d-flex justify-content-between align-items-start mb-4">
                  <div>
                    <h3 id="book-title">Titre du livre</h3>
                    <div class="text-muted" id="book-meta">Type de fichier, taille, date d'analyse</div>
                  </div>
                  <div>
                    <button class="btn btn-outline-danger btn-sm" id="delete-book-btn">
                      <i class="bi bi-trash me-1"></i>Supprimer
                    </button>
                  </div>
                </div>
                
                <div class="row">
                  <div class="col-md-6 mb-4">
                    <div class="card bg-dark border-secondary">
                      <div class="card-header">Résumé</div>
                      <div class="card-body">
                        <p id="book-summary">Chargement du résumé...</p>
                      </div>
                    </div>
                  </div>
                  
                  <div class="col-md-6 mb-4">
                    <div class="card bg-dark border-secondary">
                      <div class="card-header">Statistiques</div>
                      <div class="card-body">
                        <div class="row">
                          <div class="col-6 mb-3">
                            <div class="small text-muted">Nombre de mots</div>
                            <div class="fs-5" id="word-count">-</div>
                          </div>
                          <div class="col-6 mb-3">
                            <div class="small text-muted">Nombre de caractères</div>
                            <div class="fs-5" id="char-count">-</div>
                          </div>
                          <div class="col-6 mb-3">
                            <div class="small text-muted">Nombre de phrases</div>
                            <div class="fs-5" id="sentence-count">-</div>
                          </div>
                          <div class="col-6 mb-3">
                            <div class="small text-muted">Nombre de paragraphes</div>
                            <div class="fs-5" id="paragraph-count">-</div>
                          </div>
                          <div class="col-6 mb-3">
                            <div class="small text-muted">Longueur moyenne des mots</div>
                            <div class="fs-5" id="avg-word-length">-</div>
                          </div>
                          <div class="col-6 mb-3">
                            <div class="small text-muted">Sentiment</div>
                            <div class="fs-5" id="sentiment">-</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="analysis-section">
                  <h4>Mots les plus fréquents</h4>
                  <div class="chart-container">
                    <canvas id="word-frequency-chart"></canvas>
                  </div>
                </div>
                
                <div class="analysis-section">
                  <h4>Analyse MPC</h4>
                  <div class="card bg-dark border-secondary mb-4">
                    <div class="card-header">
                      <div class="d-flex justify-content-between align-items-center">
                        <span>Thèmes prédits</span>
                        <button class="btn btn-outline-primary btn-sm" id="run-mpc-btn">
                          <i class="bi bi-play-fill me-1"></i>Exécuter MPC
                        </button>
                      </div>
                    </div>
                    <div class="card-body">
                      <div id="mpc-loading" class="text-center p-3 d-none">
                        <div class="spinner-border spinner-border-sm text-light" role="status"></div>
                        <div class="mt-2">Exécution de l'analyse MPC...</div>
                      </div>
                      <div id="predicted-themes">
                        <div class="text-muted">Aucune analyse MPC n'a encore été effectuée.</div>
                      </div>
                    </div>
                  </div>
                  
                  <div class="card bg-dark border-secondary">
                    <div class="card-header">Motifs récurrents</div>
                    <div class="card-body">
                      <div id="recurring-patterns">
                        <div class="text-muted">Aucune analyse MPC n'a encore été effectuée.</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Modal de téléchargement -->
  <div class="modal fade" id="upload-modal" tabindex="-1" aria-labelledby="upload-modal-label" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content bg-dark text-light">
        <div class="modal-header">
          <h5 class="modal-title" id="upload-modal-label">Télécharger un livre</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="upload-form">
            <div class="mb-3">
              <label for="book-file" class="form-label">Sélectionnez un fichier (PDF, EPUB, TXT)</label>
              <input class="form-control bg-dark text-light border-secondary" type="file" id="book-file" accept=".pdf,.epub,.txt">
              <div class="form-text">Taille maximale : 50 Mo</div>
            </div>
            
            <div class="progress-container">
              <div class="progress">
                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
              </div>
              <div class="text-center mt-1 small" id="modal-progress-text">Préparation...</div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
          <button type="button" class="btn btn-primary" id="modal-upload-btn">Télécharger</button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="/js/book-analysis.js"></script>
</body>
</html>
