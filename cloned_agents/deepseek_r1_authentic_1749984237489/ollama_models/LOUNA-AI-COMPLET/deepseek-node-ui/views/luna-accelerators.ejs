<%- include('luna-base') %>
<link rel="stylesheet" href="/css/luna-accelerators.css">

<!-- Intégration du contenu principal Luna Accélérateurs -->
<div class="row mt-4">
  <!-- Panneau principal des accélérateurs -->
  <div class="col-md-8">
    <div class="luna-card" style="height: 65vh; display: flex; flex-direction: column;">
      <h3 class="mb-3 d-flex align-items-center justify-content-between">
        <div><i class="bi bi-lightning-charge me-2"></i> Accélérateurs Kyber</div>
        <div>
          <button id="accelerator-settings-btn" class="btn btn-sm btn-luna">
            <i class="bi bi-gear me-1"></i> Paramètres
          </button>
        </div>
      </h3>

      <div id="accelerators-container" style="flex-grow: 1; overflow-y: auto; margin-bottom: 1rem; padding: 1rem; border-radius: 10px; background-color: rgba(0,0,0,0.2); background-image: linear-gradient(to bottom, rgba(156, 137, 184, 0.05), rgba(240, 166, 202, 0.05));">
        <!-- Visualisation des accélérateurs Kyber -->
        <div class="accelerators-visualization">
          <canvas id="accelerators-canvas" width="100%" height="400"></canvas>
          <div id="accelerators-loading" class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Chargement...</span>
            </div>
            <p class="mt-3">Chargement des accélérateurs Kyber...</p>
          </div>
        </div>

        <!-- Légende des accélérateurs -->
        <div class="accelerators-legend mt-4">
          <div class="legend-title">Types d'accélérateurs</div>
          <div class="legend-items">
            <div class="legend-item">
              <div class="legend-color" style="background: linear-gradient(135deg, #ff6b6b, #ee5253);"></div>
              <div class="legend-label">Mémoire</div>
            </div>
            <div class="legend-item">
              <div class="legend-color" style="background: linear-gradient(135deg, #48dbfb, #0abde3);"></div>
              <div class="legend-label">Réflexion</div>
            </div>
            <div class="legend-item">
              <div class="legend-color" style="background: linear-gradient(135deg, #1dd1a1, #10ac84);"></div>
              <div class="legend-label">Formation</div>
            </div>
            <div class="legend-item">
              <div class="legend-color" style="background: linear-gradient(135deg, #feca57, #ff9f43);"></div>
              <div class="legend-label">Thermique</div>
            </div>
            <div class="legend-item">
              <div class="legend-color" style="background: linear-gradient(135deg, #a29bfe, #6c5ce7);"></div>
              <div class="legend-label">Audio</div>
            </div>
            <div class="legend-item">
              <div class="legend-color" style="background: linear-gradient(135deg, #fd79a8, #e84393);"></div>
              <div class="legend-label">Vidéo</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Panneau latéral avec les statistiques et contrôles -->
  <div class="col-md-4">
    <!-- Statistiques des accélérateurs -->
    <div class="luna-card mb-3">
      <h4><i class="bi bi-bar-chart me-2"></i> Statistiques</h4>
      <div class="mb-3">
        <div class="d-flex justify-content-between mb-1">
          <span>Accélérateurs actifs</span>
          <span id="active-accelerators">29</span>
        </div>
        <div class="progress mb-3" style="height: 8px;">
          <div class="progress-bar bg-primary" role="progressbar" style="width: 72.5%;" aria-valuenow="72.5" aria-valuemin="0" aria-valuemax="100"></div>
        </div>

        <div class="d-flex justify-content-between mb-1">
          <span>Efficacité moyenne</span>
          <span id="avg-efficiency">153.6%</span>
        </div>
        <div class="progress mb-3" style="height: 8px;">
          <div class="progress-bar bg-success" role="progressbar" style="width: 76.8%;" aria-valuenow="76.8" aria-valuemin="0" aria-valuemax="100"></div>
        </div>

        <div class="d-flex justify-content-between mb-1">
          <span>Vitesse de réflexion</span>
          <span id="reflection-speed">x6.4</span>
        </div>
        <div class="progress" style="height: 8px;">
          <div class="progress-bar bg-warning" role="progressbar" style="width: 64%;" aria-valuenow="64" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
      </div>
    </div>

    <!-- Contrôles des accélérateurs -->
    <div class="luna-card mb-3">
      <h4><i class="bi bi-sliders me-2"></i> Contrôles</h4>
      <div class="mb-3">
        <button id="refresh-accelerators-btn" class="btn btn-luna w-100 mb-2">
          <i class="bi bi-arrow-clockwise me-1"></i> Rafraîchir les données
        </button>
        <button id="optimize-accelerators-btn" class="btn btn-luna-outline w-100 mb-2">
          <i class="bi bi-lightning-charge me-1"></i> Optimiser les accélérateurs
        </button>
        <button id="reset-accelerators-btn" class="btn btn-danger w-100">
          <i class="bi bi-arrow-repeat me-1"></i> Réinitialiser la configuration
        </button>
      </div>
    </div>

    <!-- Détails des accélérateurs -->
    <div class="luna-card">
      <h4><i class="bi bi-info-circle me-2"></i> Détails</h4>
      <div class="accelerator-details mb-3">
        <div class="detail-group">
          <div class="detail-label">Mémoire</div>
          <div class="detail-value">3 actifs, efficacité 153.6%</div>
        </div>
        <div class="detail-group">
          <div class="detail-label">Réflexion</div>
          <div class="detail-value">3 actifs, efficacité 204.7%</div>
        </div>
        <div class="detail-group">
          <div class="detail-label">Formation</div>
          <div class="detail-value">6 actifs, efficacité 222.6%</div>
        </div>
        <div class="detail-group">
          <div class="detail-label">Thermique</div>
          <div class="detail-value">3 actifs, efficacité 165.8%</div>
        </div>
        <div class="detail-group">
          <div class="detail-label">Audio</div>
          <div class="detail-value">3 actifs, efficacité 147.0%</div>
        </div>
        <div class="detail-group">
          <div class="detail-label">Vidéo</div>
          <div class="detail-value">3 actifs, efficacité 122.8%</div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Pied de page fixe -->
<footer class="footer-fixed">
  <small>Vision Ultra - Interface Accélérateurs - <span id="current-datetime"></span> - &copy; 2024 Jean Passave - Sainte-Anne, Guadeloupe (97180)</small>
</footer>

<script src="/socket.io/socket.io.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/d3@7"></script>
<script src="/js/luna-accelerators.js"></script>

<script>
  $(document).ready(function() {
    // Initialiser Socket.IO pour les mises à jour en temps réel
    const socket = io();

    socket.on('connect', () => {
      console.log('Connecté au serveur pour les mises à jour des accélérateurs');
    });

    // Mettre à jour l'horloge
    function updateClock() {
      const now = new Date();
      const dateStr = now.toLocaleDateString('fr-FR', {weekday: 'long', year: 'numeric', month: 'long', day: 'numeric'});
      const timeStr = now.toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit', second: '2-digit'});
      
      document.getElementById('current-datetime').textContent = `${dateStr} ${timeStr}`;
      
      setTimeout(updateClock, 1000);
    }

    // Démarrer l'horloge
    updateClock();

    // Fonction pour afficher une notification
    function showNotification(message, type = 'info') {
      // Créer l'élément de notification s'il n'existe pas
      let notificationArea = $('#notification-area');
      if (notificationArea.length === 0) {
        $('body').append('<div id="notification-area" style="position: fixed; top: 20px; right: 20px; z-index: 9999; width: 300px;"></div>');
        notificationArea = $('#notification-area');
      }

      // Créer la notification
      const notification = $(`<div class="alert alert-${type}" style="margin-bottom: 10px; opacity: 0; transition: opacity 0.3s ease;">${message}</div>`);
      notificationArea.append(notification);

      // Afficher avec animation
      setTimeout(() => {
        notification.css('opacity', '1');

        // Masquer après 3 secondes
        setTimeout(() => {
          notification.css('opacity', '0');
          setTimeout(() => notification.remove(), 300);
        }, 3000);
      }, 100);
    }

    // Gestion des boutons
    $('#refresh-accelerators-btn').on('click', function() {
      // Simuler le rafraîchissement
      $(this).prop('disabled', true);
      $(this).html('<i class="bi bi-arrow-repeat spin me-1"></i> Rafraîchissement...');

      setTimeout(() => {
        $(this).prop('disabled', false);
        $(this).html('<i class="bi bi-arrow-clockwise me-1"></i> Rafraîchir les données');
        showNotification('Données des accélérateurs rafraîchies', 'success');
      }, 1000);
    });

    $('#optimize-accelerators-btn').on('click', function() {
      $(this).prop('disabled', true);
      $(this).html('<i class="bi bi-lightning-charge spin me-1"></i> Optimisation...');

      setTimeout(() => {
        $(this).prop('disabled', false);
        $(this).html('<i class="bi bi-lightning-charge me-1"></i> Optimiser les accélérateurs');
        showNotification('Accélérateurs optimisés avec succès', 'success');
      }, 2000);
    });

    $('#reset-accelerators-btn').on('click', function() {
      if (confirm('Êtes-vous sûr de vouloir réinitialiser la configuration des accélérateurs ? Cette action ne peut pas être annulée.')) {
        $(this).prop('disabled', true);
        $(this).html('<i class="bi bi-arrow-repeat spin me-1"></i> Réinitialisation...');

        setTimeout(() => {
          $(this).prop('disabled', false);
          $(this).html('<i class="bi bi-arrow-repeat me-1"></i> Réinitialiser la configuration');
          showNotification('Configuration des accélérateurs réinitialisée', 'warning');
        }, 3000);
      }
    });
  });
</script>
