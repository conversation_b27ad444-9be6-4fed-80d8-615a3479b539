<!-- Interface pour le mode sommeil et les zones thermiques glissantes -->
<div id="sleep-mode-controller" class="luna-card mb-3">
  <h4><i class="bi bi-moon-stars me-2"></i> Mode Sommeil</h4>
  <p class="text-muted">Le mode sommeil permet aux informations de descendre plus profondément dans la mémoire thermique, facilitant l'archivage.</p>
  
  <div class="d-flex justify-content-between align-items-center">
    <div class="form-check form-switch">
      <input class="form-check-input" type="checkbox" id="sleep-mode-toggle">
      <label class="form-check-label" for="sleep-mode-toggle">Activer le mode sommeil</label>
    </div>
    
    <div>
      <span id="sleep-status" class="me-3">
        <span class="status-indicator status-inactive"></span>
        <span>Inactif</span>
      </span>
    </div>
  </div>
  
  <div class="mt-3">
    <button id="trigger-sleep" class="btn btn-luna">
      <i class="bi bi-moon-fill me-1"></i> Déclencher le mode sommeil temporaire
    </button>
    <small class="text-muted d-block mt-2">Applique un cycle de 30 secondes de mode sommeil pour favoriser l'archivage profond.</small>
  </div>
</div>

<script>
  $(document).ready(function() {
    // État du mode sommeil
    let sleepModeActive = false;
    
    // Mettre à jour l'affichage du statut
    function updateSleepStatus(active) {
      const statusElement = $('#sleep-status');
      const statusIndicator = statusElement.find('.status-indicator');
      const statusText = statusElement.find('span:not(.status-indicator)');
      
      if (active) {
        statusIndicator.removeClass('status-inactive').addClass('status-active');
        statusText.text('Actif');
      } else {
        statusIndicator.removeClass('status-active').addClass('status-inactive');
        statusText.text('Inactif');
      }
    }
    
    // Toggle du mode sommeil
    $('#sleep-mode-toggle').on('change', function() {
      const enabled = $(this).is(':checked');
      
      $.ajax({
        url: '/luna/api/thermal/sleep',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ enabled }),
        success: function(response) {
          if (response.success) {
            sleepModeActive = response.sleepMode;
            updateSleepStatus(sleepModeActive);
            
            // Notifier l'utilisateur
            const message = enabled ? 
              'Mode sommeil activé. Les informations vont progressivement descendre vers les zones plus profondes.' : 
              'Mode sommeil désactivé. Le système revient au mode normal.';
            
            $('#zone-message').text(message).removeClass('alert-info').addClass('alert-warning');
            setTimeout(() => {
              $('#zone-message').removeClass('alert-warning').addClass('alert-info');
            }, 3000);
          }
        },
        error: function() {
          // Rétablir l'état du toggle
          $('#sleep-mode-toggle').prop('checked', sleepModeActive);
          alert('Erreur lors de la modification du mode sommeil');
        }
      });
    });
    
    // Déclenchement temporaire du mode sommeil
    $('#trigger-sleep').on('click', function() {
      $.ajax({
        url: '/luna/api/cooling/trigger',
        type: 'POST',
        success: function(response) {
          if (response.success) {
            // Mettre à jour temporairement l'affichage
            updateSleepStatus(true);
            $('#sleep-mode-toggle').prop('checked', true);
            
            // Message de notification
            $('#zone-message').text('Mode sommeil temporaire activé pour 30 secondes. Archivage profond en cours...').removeClass('alert-info').addClass('alert-success');
            
            // Réinitialiser après 30 secondes
            setTimeout(() => {
              if (!sleepModeActive) { // Ne pas réinitialiser si l'utilisateur a activé le mode permanent
                updateSleepStatus(false);
                $('#sleep-mode-toggle').prop('checked', false);
              }
              $('#zone-message').removeClass('alert-success').addClass('alert-info');
            }, 30000);
          }
        }
      });
    });
    
    // Vérifier périodiquement l'état du mode sommeil
    function checkSleepMode() {
      $.ajax({
        url: '/luna/api/temperature',
        type: 'GET',
        success: function(response) {
          if (response.success && response.data && response.data.sleepMode !== undefined) {
            sleepModeActive = response.data.sleepMode;
            $('#sleep-mode-toggle').prop('checked', sleepModeActive);
            updateSleepStatus(sleepModeActive);
          }
        }
      });
    }
    
    // Vérifier l'état initial
    checkSleepMode();
    
    // Vérifier périodiquement (toutes les 10 secondes)
    setInterval(checkSleepMode, 10000);
  });
</script>
