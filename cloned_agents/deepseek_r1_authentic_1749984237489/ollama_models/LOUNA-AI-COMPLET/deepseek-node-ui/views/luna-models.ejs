<%- include('luna-base') %>

<!-- Interface de gestion des modèles IA -->
<div class="container-fluid mt-4">
  <div class="row">
    <div class="col-md-12">
      <div class="luna-card">
        <h3><i class="bi bi-cpu me-2"></i> Gestion des modèles IA</h3>
        <p class="text-muted"><PERSON><PERSON><PERSON> les modèles disponibles dans Ollama pour améliorer les capacités de Luna</p>
        
        <!-- État d'Ollama -->
        <div class="alert" id="ollama-status-alert" role="alert">
          <i class="bi bi-info-circle me-2"></i> Vérification de l'état d'Ollama...
        </div>
        
        <!-- Modèle actif -->
        <div class="mb-4 p-4 rounded" style="background-color: rgba(0,0,0,0.1); border-left: 4px solid var(--luna-accent);">
          <h5><i class="bi bi-lightning-charge me-2"></i> Modèle actif</h5>
          <div class="d-flex align-items-center">
            <div class="me-auto">
              <h4 id="active-model-name" class="mb-0">deepseek-r1:7b</h4>
              <small class="text-muted" id="active-model-info">Modèle par défaut</small>
            </div>
            <button class="btn btn-luna-outline refresh-models">
              <i class="bi bi-arrow-clockwise me-1"></i> Actualiser
            </button>
          </div>
        </div>
        
        <!-- Installation d'un nouveau modèle -->
        <div class="mb-4">
          <h5><i class="bi bi-box-arrow-down me-2"></i> Installer un nouveau modèle</h5>
          <div class="input-group mb-3">
            <input type="text" class="form-control" id="new-model-input" placeholder="Nom du modèle (ex: llama2:7b)" aria-label="Nom du modèle">
            <button class="btn btn-luna" id="install-model-btn">
              <i class="bi bi-download me-1"></i> Installer
            </button>
          </div>
          <div id="installation-progress-container" class="progress mb-2" style="display: none;">
            <div class="progress-bar progress-bar-striped progress-bar-animated" id="installation-progress" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%; background: var(--luna-gradient);">0%</div>
          </div>
          <small class="text-muted">Exemples : llama2:7b, gemma:7b, mistral:7b, phi-2:mini, openchat:7b</small>
        </div>
        
        <!-- Liste des modèles disponibles -->
        <div class="mt-4">
          <h5><i class="bi bi-collection me-2"></i> Modèles disponibles</h5>
          <div class="table-responsive">
            <table class="table table-dark table-hover" id="models-table">
              <thead>
                <tr>
                  <th>Modèle</th>
                  <th>Statut</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody id="models-list">
                <tr>
                  <td colspan="3" class="text-center">Chargement des modèles...</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  $(document).ready(function() {
    // Charger la liste des modèles au démarrage
    loadModels();
    checkOllamaStatus();
    
    // Installation d'un nouveau modèle
    $('#install-model-btn').on('click', function() {
      const modelName = $('#new-model-input').val().trim();
      
      if (modelName) {
        installModel(modelName);
      } else {
        showAlert('error', 'Veuillez spécifier un nom de modèle');
      }
    });
    
    // Actualiser la liste des modèles
    $('.refresh-models').on('click', function() {
      loadModels();
    });
    
    // Fonction pour charger les modèles
    function loadModels() {
      $.ajax({
        url: '/luna/models',
        method: 'GET',
        success: function(response) {
          if (response.success) {
            updateActiveModel(response.activeModel);
            displayModels(response.models, response.activeModel);
          } else {
            showAlert('error', 'Erreur lors du chargement des modèles: ' + response.error);
          }
        },
        error: function(xhr, status, error) {
          showAlert('error', 'Erreur de connexion: ' + error);
        }
      });
    }
    
    // Mettre à jour l'affichage du modèle actif
    function updateActiveModel(model) {
      $('#active-model-name').text(model);
      $('#active-model-info').text('Modèle sélectionné');
    }
    
    // Afficher les modèles dans le tableau
    function displayModels(models, activeModel) {
      const tbody = $('#models-list');
      tbody.empty();
      
      if (models.length > 0) {
        models.forEach(model => {
          const isActive = model === activeModel;
          const statusBadge = isActive 
            ? '<span class="badge bg-success">Actif</span>' 
            : '<span class="badge bg-secondary">Disponible</span>';
          
          const selectButton = isActive 
            ? '<button class="btn btn-sm btn-success" disabled><i class="bi bi-check2-circle me-1"></i> Sélectionné</button>' 
            : '<button class="btn btn-sm btn-luna select-model-btn" data-model="' + model + '"><i class="bi bi-check-circle me-1"></i> Sélectionner</button>';
          
          const deleteButton = isActive
            ? '<button class="btn btn-sm btn-danger delete-model-btn" disabled title="Impossible de supprimer le modèle actif"><i class="bi bi-trash"></i></button>'
            : '<button class="btn btn-sm btn-danger delete-model-btn" data-model="' + model + '" title="Supprimer"><i class="bi bi-trash"></i></button>';
          
          const row = `
            <tr>
              <td>${model}</td>
              <td>${statusBadge}</td>
              <td>
                ${selectButton}
                ${deleteButton}
              </td>
            </tr>
          `;
          
          tbody.append(row);
        });
        
        // Configurer les boutons de sélection
        $('.select-model-btn').on('click', function() {
          const model = $(this).data('model');
          selectModel(model);
        });
        
        // Configurer les boutons de suppression
        $('.delete-model-btn:not([disabled])').on('click', function() {
          const model = $(this).data('model');
          if (confirm(`Êtes-vous sûr de vouloir supprimer le modèle "${model}" ?`)) {
            deleteModel(model);
          }
        });
      } else {
        tbody.html('<tr><td colspan="3" class="text-center">Aucun modèle trouvé</td></tr>');
      }
    }
    
    // Sélectionner un modèle
    function selectModel(model) {
      $.ajax({
        url: '/luna/models/select',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ model }),
        success: function(response) {
          if (response.success) {
            showAlert('success', `Modèle "${model}" activé avec succès`);
            loadModels();
          } else {
            showAlert('error', 'Erreur lors de l\'activation du modèle: ' + response.error);
          }
        },
        error: function(xhr, status, error) {
          showAlert('error', 'Erreur de connexion: ' + error);
        }
      });
    }
    
    // Supprimer un modèle
    function deleteModel(model) {
      $.ajax({
        url: '/luna/models/remove',
        method: 'DELETE',
        contentType: 'application/json',
        data: JSON.stringify({ model }),
        success: function(response) {
          if (response.success) {
            showAlert('success', `Modèle "${model}" supprimé avec succès`);
            loadModels();
          } else {
            showAlert('error', 'Erreur lors de la suppression: ' + response.error);
          }
        },
        error: function(xhr, status, error) {
          showAlert('error', 'Erreur de connexion: ' + error);
        }
      });
    }
    
    // Installer un modèle
    function installModel(model) {
      // Afficher la barre de progression
      $('#installation-progress-container').show();
      $('#installation-progress').css('width', '0%').attr('aria-valuenow', 0).text('0%');
      
      // Désactiver le bouton
      $('#install-model-btn').prop('disabled', true).html('<i class="spinner-border spinner-border-sm me-1"></i> Installation...');
      
      // Lancement de l'installation
      $.ajax({
        url: '/luna/models/install',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ model }),
        xhr: function() {
          const xhr = new XMLHttpRequest();
          
          // Simuler une progression car Ollama ne fournit pas de progression réelle
          let progress = 0;
          const interval = setInterval(function() {
            progress += Math.random() * 3;
            if (progress > 95) {
              progress = 95;
              clearInterval(interval);
            }
            $('#installation-progress').css('width', progress + '%').attr('aria-valuenow', Math.round(progress)).text(Math.round(progress) + '%');
          }, 1000);
          
          xhr.addEventListener('loadend', function() {
            clearInterval(interval);
          });
          
          return xhr;
        },
        success: function(response) {
          // Afficher 100% à la fin
          $('#installation-progress').css('width', '100%').attr('aria-valuenow', 100).text('100%');
          
          if (response.success) {
            showAlert('success', `Modèle "${model}" installé avec succès`);
            loadModels();
          } else {
            showAlert('error', 'Erreur lors de l\'installation: ' + response.error);
          }
          
          // Réinitialiser les éléments UI
          setTimeout(function() {
            $('#installation-progress-container').hide();
            $('#new-model-input').val('');
            $('#install-model-btn').prop('disabled', false).html('<i class="bi bi-download me-1"></i> Installer');
          }, 3000);
        },
        error: function(xhr, status, error) {
          showAlert('error', 'Erreur de connexion: ' + error);
          
          // Réinitialiser les éléments UI
          $('#installation-progress-container').hide();
          $('#install-model-btn').prop('disabled', false).html('<i class="bi bi-download me-1"></i> Installer');
        }
      });
    }
    
    // Vérifier l'état d'Ollama
    function checkOllamaStatus() {
      const statusAlert = $('#ollama-status-alert');
      
      // Tenter de se connecter à l'API d'Ollama
      $.ajax({
        url: '/luna/ollama/status',
        method: 'GET',
        success: function(response) {
          if (response.success) {
            statusAlert.removeClass('alert-warning alert-danger').addClass('alert-success');
            statusAlert.html('<i class="bi bi-check-circle me-2"></i> Ollama est opérationnel');
          } else {
            statusAlert.removeClass('alert-success alert-warning').addClass('alert-danger');
            statusAlert.html('<i class="bi bi-exclamation-triangle me-2"></i> Ollama n\'est pas disponible: ' + response.error);
          }
        },
        error: function(xhr, status, error) {
          statusAlert.removeClass('alert-success alert-warning').addClass('alert-danger');
          statusAlert.html('<i class="bi bi-exclamation-triangle me-2"></i> Impossible de vérifier le statut d\'Ollama');
        }
      });
    }
    
    // Afficher une alerte
    function showAlert(type, message) {
      const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
      const alert = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
          ${message}
          <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fermer"></button>
        </div>
      `;
      
      $('.luna-card').prepend(alert);
      
      setTimeout(function() {
        $('.alert').alert('close');
      }, 5000);
    }
  });
</script>
