/**
 * Script de test pour l'API Ollama
 */

const axios = require('axios');

// Configuration
const OLLAMA_API_URL = 'http://localhost:11434';
const OLLAMA_API_URL_ALT = 'http://127.0.0.1:11434';
const MODEL_NAME = 'deepseek-r1:7b';

// Fonction principale
async function testOllamaApi() {
  try {
    console.log('Test de l\'API Ollama...');

    // Vérifier si Ollama est en cours d'exécution
    try {
      const versionResponse = await axios.get(`${OLLAMA_API_URL}/version`);
      console.log('Ollama est en cours d\'exécution. Version:', versionResponse.data.version);
    } catch (error) {
      console.error('Erreur avec le premier URL:', error.message);

      // Essayer avec l'URL alternative
      try {
        console.log('Essai avec l\'URL alternative...');
        const versionResponse = await axios.get(`${OLLAMA_API_URL_ALT}/version`);
        console.log('Ollama est en cours d\'exécution (URL alternative). Version:', versionResponse.data.version);

        // Si l'URL alternative fonctionne, l'utiliser pour la suite
        OLLAMA_API_URL = OLLAMA_API_URL_ALT;
      } catch (error2) {
        console.error('Erreur: Ollama n\'est pas en cours d\'exécution');
        return;
      }
    }

    // Vérifier les modèles disponibles
    try {
      const modelsResponse = await axios.get(`${OLLAMA_API_URL}/api/tags`);
      console.log('Modèles disponibles:', modelsResponse.data.models ? modelsResponse.data.models.map(model => model.name).join(', ') : 'Aucun modèle trouvé');
    } catch (error) {
      console.error('Erreur lors de la récupération des modèles:', error.message);

      // Essayer un autre point de terminaison
      try {
        console.log('Essai avec un autre point de terminaison...');
        const modelsResponse = await axios.get(`${OLLAMA_API_URL}/api/models`);
        console.log('Modèles disponibles:', modelsResponse.data.models ? modelsResponse.data.models.join(', ') : 'Aucun modèle trouvé');
      } catch (error2) {
        console.error('Erreur lors de la récupération des modèles (2e tentative):', error2.message);
      }
    }

    // Tester l'API de chat
    console.log(`\nTest de l'API de chat avec le modèle ${MODEL_NAME}...`);

    const requestData = {
      model: MODEL_NAME,
      messages: [
        { role: 'user', content: 'Bonjour, comment ça va?' }
      ],
      options: {
        temperature: 0.7,
        num_predict: 1000
      }
    };

    try {
      console.log('Envoi de la requête...');
      const response = await axios.post(`${OLLAMA_API_URL}/api/chat`, requestData);

      console.log('\nRéponse reçue:');
      console.log(JSON.stringify(response.data, null, 2));

      if (response.data.message && response.data.message.content) {
        console.log('\nContenu de la réponse:');
        console.log(response.data.message.content);
      } else {
        console.log('\nATTENTION: Format de réponse non standard');
        console.log('Structure de la réponse:');
        console.log(Object.keys(response.data).join(', '));
      }
    } catch (error) {
      console.error('Erreur lors de l\'appel à l\'API de chat:', error.message);
      if (error.response) {
        console.error('Statut de la réponse:', error.response.status);
        console.error('Données de la réponse:', error.response.data);
      }

      // Essayer un autre point de terminaison
      try {
        console.log('\nEssai avec un autre point de terminaison...');
        const response = await axios.post(`${OLLAMA_API_URL}/chat`, requestData);

        console.log('\nRéponse reçue (2e tentative):');
        console.log(JSON.stringify(response.data, null, 2));

        if (response.data.message && response.data.message.content) {
          console.log('\nContenu de la réponse:');
          console.log(response.data.message.content);
        } else {
          console.log('\nATTENTION: Format de réponse non standard');
          console.log('Structure de la réponse:');
          console.log(Object.keys(response.data).join(', '));
        }
      } catch (error2) {
        console.error('Erreur lors de l\'appel à l\'API de chat (2e tentative):', error2.message);
        if (error2.response) {
          console.error('Statut de la réponse:', error2.response.status);
          console.error('Données de la réponse:', error2.response.data);
        }
      }
    }

    // Tester l'API de génération
    console.log(`\nTest de l'API de génération avec le modèle ${MODEL_NAME}...`);

    const generateRequestData = {
      model: MODEL_NAME,
      prompt: 'Bonjour, comment ça va?',
      options: {
        temperature: 0.7,
        num_predict: 1000
      }
    };

    try {
      console.log('Envoi de la requête...');
      const response = await axios.post(`${OLLAMA_API_URL}/api/generate`, generateRequestData);

      console.log('\nRéponse reçue:');
      console.log(JSON.stringify(response.data, null, 2));

      if (response.data.response) {
        console.log('\nContenu de la réponse:');
        console.log(response.data.response);
      } else {
        console.log('\nATTENTION: Format de réponse non standard');
        console.log('Structure de la réponse:');
        console.log(Object.keys(response.data).join(', '));
      }
    } catch (error) {
      console.error('Erreur lors de l\'appel à l\'API de génération:', error.message);
      if (error.response) {
        console.error('Statut de la réponse:', error.response.status);
        console.error('Données de la réponse:', error.response.data);
      }
    }
  } catch (error) {
    console.error('Erreur générale:', error.message);
  }
}

// Exécuter le test
testOllamaApi();
