/**
 * Patch pour s'assurer que le système de présence du cerveau est toujours activé
 * et correctement connecté à la mémoire thermique
 */

const fs = require('fs');
const path = require('path');

/**
 * S'assure que le système de présence du cerveau est activé et connecté
 * @param {Object} brainPresence - Instance du système de présence du cerveau
 * @param {Object} thermalMemory - Instance de la mémoire thermique
 * @returns {boolean} - true si le patch a été appliqué avec succès, false sinon
 */
function ensureBrainPresence(brainPresence, thermalMemory) {
  if (!brainPresence || !thermalMemory) {
    console.error('❌ Impossible d\'appliquer le patch de présence du cerveau: instances manquantes');
    return false;
  }

  try {
    console.log('🧠 Application du patch de présence du cerveau...');

    // Vérifier si le cerveau est déjà actif
    let isActive = false;
    if (typeof brainPresence.presenceState === 'object' && brainPresence.presenceState !== null) {
      isActive = brainPresence.presenceState.isActive === true;
    }

    // Activer le cerveau s'il n'est pas déjà actif
    if (!isActive && typeof brainPresence.activate === 'function') {
      console.log('🧠 Activation du système de présence du cerveau...');
      brainPresence.activate();
      console.log('✅ Système de présence du cerveau activé');
    } else if (isActive) {
      console.log('✅ Le système de présence du cerveau est déjà actif');
    } else {
      console.log('❌ Impossible d\'activer le système de présence du cerveau: méthode activate non disponible');
    }

    // Vérifier si le cerveau est connecté à la mémoire thermique
    if (brainPresence.thermalMemory !== thermalMemory) {
      console.log('🧠 Connexion du cerveau à la mémoire thermique...');
      brainPresence.thermalMemory = thermalMemory;
      console.log('✅ Cerveau connecté à la mémoire thermique');
    } else {
      console.log('✅ Le cerveau est déjà connecté à la mémoire thermique');
    }

    // Vérifier si les accélérateurs Kyber sont disponibles
    if (thermalMemory && thermalMemory.kyberAccelerators) {
      if (!brainPresence.hasKyberAccelerators) {
        console.log('🧠 Activation des accélérateurs Kyber pour le cerveau...');
        brainPresence.hasKyberAccelerators = true;
        
        // Initialiser les accélérateurs de présence si la méthode existe
        if (typeof brainPresence.initializePresenceAccelerators === 'function') {
          brainPresence.initializePresenceAccelerators();
          console.log('✅ Accélérateurs Kyber activés pour le cerveau');
        } else {
          console.log('❌ Impossible d\'initialiser les accélérateurs de présence: méthode non disponible');
        }
      } else {
        console.log('✅ Les accélérateurs Kyber sont déjà activés pour le cerveau');
      }
    }

    // Sauvegarder l'état du cerveau dans une variable globale pour y accéder facilement
    global.brainPresence = brainPresence;

    // Créer un fichier de configuration pour le cerveau
    const configPath = path.join(__dirname, '../data/config/brain-config.json');
    
    // Créer le dossier de configuration s'il n'existe pas
    if (!fs.existsSync(path.join(__dirname, '../data/config'))) {
      fs.mkdirSync(path.join(__dirname, '../data/config'), { recursive: true });
    }
    
    // Sauvegarder la configuration
    const brainConfig = {
      isActive: true,
      autoActivate: true,
      backgroundActivityInterval: 3000,
      presenceUpdateInterval: 1000,
      thoughtGenerationInterval: 10000,
      lastActivated: new Date().toISOString()
    };
    
    fs.writeFileSync(configPath, JSON.stringify(brainConfig, null, 2), 'utf8');
    console.log('✅ Configuration du cerveau sauvegardée');

    return true;
  } catch (error) {
    console.error('❌ Erreur lors de l\'application du patch de présence du cerveau:', error.message);
    return false;
  }
}

module.exports = ensureBrainPresence;
