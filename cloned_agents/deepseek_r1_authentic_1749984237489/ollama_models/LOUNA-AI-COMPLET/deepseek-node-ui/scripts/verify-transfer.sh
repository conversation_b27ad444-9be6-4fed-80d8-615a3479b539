#!/bin/bash

# Script pour vérifier si le transfert a été effectué correctement
# et si nous pouvons supprimer les fichiers originaux en toute sécurité

# Configuration
SOURCE_DIR="/Users/<USER>/Documents/augment-projects/Jarvis"
MODELS_DIR="/Users/<USER>/.ollama/models"
EXTERNAL_DIR="/Volumes/seagate/Jarvis_Working"
EXTERNAL_CODE_DIR="${EXTERNAL_DIR}/code"
EXTERNAL_MODELS_DIR="${EXTERNAL_DIR}/models"
LOG_FILE="${EXTERNAL_DIR}/verify_log.txt"

# Fonction pour écrire dans le journal
log_message() {
    echo "[$(date +"%Y-%m-%d %H:%M:%S")] $1" >> "$LOG_FILE"
    echo "[$(date +"%Y-%m-%d %H:%M:%S")] $1"
}

# Vérifier si le disque externe est monté
if [ ! -d "/Volumes/seagate" ]; then
    echo "ERREUR: Le disque externe n'est pas monté. Veuillez connecter le disque Seagate."
    exit 1
fi

# Créer le fichier journal s'il n'existe pas
if [ ! -f "$LOG_FILE" ]; then
    touch "$LOG_FILE"
    log_message "Fichier journal créé"
fi

log_message "Début de la vérification du transfert"

# Vérifier si les répertoires existent
if [ ! -d "$EXTERNAL_CODE_DIR" ]; then
    log_message "ERREUR: Le répertoire de code externe n'existe pas: $EXTERNAL_CODE_DIR"
    log_message "Veuillez exécuter le script work-from-external.sh d'abord."
    exit 1
fi

if [ ! -d "$EXTERNAL_MODELS_DIR" ]; then
    log_message "ERREUR: Le répertoire de modèles externe n'existe pas: $EXTERNAL_MODELS_DIR"
    log_message "Veuillez exécuter le script work-from-external.sh d'abord."
    exit 1
fi

# Vérifier le nombre de fichiers
SOURCE_FILES=$(find "$SOURCE_DIR" -type f | wc -l)
EXTERNAL_FILES=$(find "$EXTERNAL_CODE_DIR" -type f | wc -l)
MODELS_FILES=$(find "$MODELS_DIR" -type f | wc -l)
EXTERNAL_MODELS_FILES=$(find "$EXTERNAL_MODELS_DIR" -type f | wc -l)

log_message "Nombre de fichiers dans le répertoire source: $SOURCE_FILES"
log_message "Nombre de fichiers dans le répertoire externe: $EXTERNAL_FILES"
log_message "Nombre de fichiers dans le répertoire des modèles: $MODELS_FILES"
log_message "Nombre de fichiers dans le répertoire des modèles externe: $EXTERNAL_MODELS_FILES"

# Vérifier la taille des répertoires
SOURCE_SIZE=$(du -sh "$SOURCE_DIR" | awk '{print $1}')
EXTERNAL_SIZE=$(du -sh "$EXTERNAL_CODE_DIR" | awk '{print $1}')
MODELS_SIZE=$(du -sh "$MODELS_DIR" | awk '{print $1}')
EXTERNAL_MODELS_SIZE=$(du -sh "$EXTERNAL_MODELS_DIR" | awk '{print $1}')

log_message "Taille du répertoire source: $SOURCE_SIZE"
log_message "Taille du répertoire externe: $EXTERNAL_SIZE"
log_message "Taille du répertoire des modèles: $MODELS_SIZE"
log_message "Taille du répertoire des modèles externe: $EXTERNAL_MODELS_SIZE"

# Vérifier si les fichiers importants existent
IMPORTANT_FILES=(
    "deepseek-node-ui/server-luna.js"
    "deepseek-node-ui/thermal-memory/thermal-memory.js"
    "deepseek-node-ui/thermal-memory/kyber-accelerators.js"
    "deepseek-node-ui/scripts/backup-project.sh"
    "deepseek-node-ui/scripts/work-from-external.sh"
    "deepseek-node-ui/scripts/configure-external-paths.js"
)

MISSING_FILES=0
for file in "${IMPORTANT_FILES[@]}"; do
    if [ ! -f "${EXTERNAL_CODE_DIR}/${file}" ]; then
        log_message "ERREUR: Fichier important manquant: ${file}"
        MISSING_FILES=$((MISSING_FILES + 1))
    fi
done

if [ $MISSING_FILES -eq 0 ]; then
    log_message "Tous les fichiers importants sont présents dans le répertoire externe"
else
    log_message "ATTENTION: $MISSING_FILES fichiers importants sont manquants dans le répertoire externe"
fi

# Vérifier si les modèles importants existent
IMPORTANT_MODELS=(
    "deepseek-r1"
    "claude"
    "llama3"
)

MISSING_MODELS=0
for model in "${IMPORTANT_MODELS[@]}"; do
    if [ ! -d "${EXTERNAL_MODELS_DIR}/${model}" ] && [ -d "${MODELS_DIR}/${model}" ]; then
        log_message "ATTENTION: Modèle important manquant: ${model}"
        MISSING_MODELS=$((MISSING_MODELS + 1))
    fi
done

if [ $MISSING_MODELS -eq 0 ]; then
    log_message "Tous les modèles importants sont présents dans le répertoire externe"
else
    log_message "ATTENTION: $MISSING_MODELS modèles importants sont manquants dans le répertoire externe"
fi

# Vérifier si le serveur Luna peut démarrer avec les chemins externes
log_message "Vérification du démarrage du serveur Luna avec les chemins externes..."
cd "$EXTERNAL_CODE_DIR/deepseek-node-ui"
node -e "try { require('./server-luna'); console.log('Le serveur Luna peut démarrer avec les chemins externes'); } catch (error) { console.error('ERREUR: Le serveur Luna ne peut pas démarrer avec les chemins externes:', error.message); process.exit(1); }" > /dev/null 2>&1

if [ $? -eq 0 ]; then
    log_message "Le serveur Luna peut démarrer avec les chemins externes"
    CAN_START=true
else
    log_message "ERREUR: Le serveur Luna ne peut pas démarrer avec les chemins externes"
    CAN_START=false
fi

# Résumé
log_message "Fin de la vérification du transfert"

echo ""
echo "=== Résumé de la vérification ==="
echo "Nombre de fichiers dans le répertoire source: $SOURCE_FILES"
echo "Nombre de fichiers dans le répertoire externe: $EXTERNAL_FILES"
echo "Taille du répertoire source: $SOURCE_SIZE"
echo "Taille du répertoire externe: $EXTERNAL_SIZE"
echo ""
echo "Nombre de fichiers dans le répertoire des modèles: $MODELS_FILES"
echo "Nombre de fichiers dans le répertoire des modèles externe: $EXTERNAL_MODELS_FILES"
echo "Taille du répertoire des modèles: $MODELS_SIZE"
echo "Taille du répertoire des modèles externe: $EXTERNAL_MODELS_SIZE"
echo ""

if [ $MISSING_FILES -eq 0 ] && [ $MISSING_MODELS -eq 0 ] && [ "$CAN_START" = true ]; then
    echo "✅ Le transfert a été effectué correctement"
    echo "✅ Vous pouvez maintenant travailler directement depuis le disque externe"
    echo "✅ Si vous avez testé le serveur Luna avec les chemins externes et que tout fonctionne,"
    echo "   vous pouvez supprimer les fichiers originaux en toute sécurité"
    echo ""
    echo "Pour démarrer le serveur Luna avec les chemins externes, exécutez:"
    echo "$EXTERNAL_CODE_DIR/deepseek-node-ui/scripts/start-luna-external.sh"
else
    echo "❌ Le transfert n'a pas été effectué correctement"
    echo "❌ Veuillez corriger les erreurs avant de supprimer les fichiers originaux"
    echo ""
    echo "Consultez le journal pour plus de détails: $LOG_FILE"
fi
