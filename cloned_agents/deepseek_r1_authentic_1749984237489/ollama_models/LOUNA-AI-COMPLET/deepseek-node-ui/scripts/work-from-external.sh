#!/bin/bash

# Script pour configurer l'environnement de travail sur le disque externe
# Créé le $(date +"%Y-%m-%d")

# Configuration
SOURCE_DIR="/Users/<USER>/Documents/augment-projects/Jarvis"
MODELS_DIR="/Users/<USER>/.ollama/models"
EXTERNAL_DIR="/Volumes/seagate/Jarvis_Working"
EXTERNAL_CODE_DIR="${EXTERNAL_DIR}/code"
EXTERNAL_MODELS_DIR="${EXTERNAL_DIR}/models"
LOG_FILE="${EXTERNAL_DIR}/setup_log.txt"

# Fonction pour écrire dans le journal
log_message() {
    echo "[$(date +"%Y-%m-%d %H:%M:%S")] $1" >> "$LOG_FILE"
    echo "[$(date +"%Y-%m-%d %H:%M:%S")] $1"
}

# Vérifier si le disque externe est monté
if [ ! -d "/Volumes/seagate" ]; then
    echo "ERREUR: Le disque externe n'est pas monté. Veuillez connecter le disque Seagate."
    exit 1
fi

# Créer le répertoire de travail s'il n'existe pas
if [ ! -d "$EXTERNAL_DIR" ]; then
    mkdir -p "$EXTERNAL_DIR"
    log_message "Répertoire de travail créé: $EXTERNAL_DIR"
fi

# Créer le fichier journal s'il n'existe pas
if [ ! -f "$LOG_FILE" ]; then
    touch "$LOG_FILE"
    log_message "Fichier journal créé"
fi

log_message "Début de la configuration de l'environnement de travail"

# Créer les sous-répertoires
mkdir -p "$EXTERNAL_CODE_DIR"
mkdir -p "$EXTERNAL_MODELS_DIR"

# Synchroniser les fichiers du projet vers le disque externe
log_message "Synchronisation des fichiers du projet vers le disque externe..."
rsync -av --progress "$SOURCE_DIR/" "$EXTERNAL_CODE_DIR/" 2>&1 | tee -a "$LOG_FILE"
PROJECT_SYNC_STATUS=$?

# Synchroniser les modèles d'IA vers le disque externe
log_message "Synchronisation des modèles d'IA vers le disque externe (cela peut prendre un certain temps)..."
rsync -av --progress "$MODELS_DIR/" "$EXTERNAL_MODELS_DIR/" 2>&1 | tee -a "$LOG_FILE"
MODELS_SYNC_STATUS=$?

# Vérifier si les synchronisations ont réussi
if [ $PROJECT_SYNC_STATUS -eq 0 ] && [ $MODELS_SYNC_STATUS -eq 0 ]; then
    log_message "Synchronisation terminée avec succès"

    # Créer un lien symbolique dans le répertoire d'origine vers le répertoire de travail
    if [ ! -L "${SOURCE_DIR}_link" ]; then
        ln -sf "$EXTERNAL_CODE_DIR" "${SOURCE_DIR}_link"
        log_message "Lien symbolique créé: ${SOURCE_DIR}_link -> $EXTERNAL_CODE_DIR"
    fi

    # Créer un lien symbolique pour les modèles
    if [ ! -L "${MODELS_DIR}_link" ]; then
        ln -sf "$EXTERNAL_MODELS_DIR" "${MODELS_DIR}_link"
        log_message "Lien symbolique créé: ${MODELS_DIR}_link -> $EXTERNAL_MODELS_DIR"
    fi

    # Afficher l'espace disque restant
    SPACE_LEFT=$(df -h "/Volumes/seagate" | awk 'NR==2 {print $4}')
    log_message "Espace restant sur le disque externe: $SPACE_LEFT"

    # Configurer les chemins d'accès pour le serveur Luna
    log_message "Configuration des chemins d'accès pour le serveur Luna..."
    cd "$SOURCE_DIR/deepseek-node-ui"
    node scripts/configure-external-paths.js
    CONFIG_STATUS=$?

    if [ $CONFIG_STATUS -eq 0 ]; then
        log_message "Configuration des chemins d'accès terminée avec succès"
    else
        log_message "ERREUR: La configuration des chemins d'accès a échoué"
    fi

    echo "Configuration terminée. Vous pouvez maintenant travailler depuis: $EXTERNAL_DIR"
    echo "Utilisez les liens symboliques suivants:"
    echo "- Code: ${SOURCE_DIR}_link"
    echo "- Modèles: ${MODELS_DIR}_link"

    echo ""
    echo "Pour démarrer le serveur Luna avec les chemins externes, exécutez:"
    echo "$SOURCE_DIR/deepseek-node-ui/scripts/start-luna-external.sh"
    echo ""
    echo "Si tout fonctionne correctement, vous pourrez supprimer les fichiers originaux."
else
    if [ $PROJECT_SYNC_STATUS -ne 0 ]; then
        log_message "ERREUR: La synchronisation du projet a échoué"
    fi
    if [ $MODELS_SYNC_STATUS -ne 0 ]; then
        log_message "ERREUR: La synchronisation des modèles a échoué"
    fi
    echo "ERREUR: La configuration a échoué. Consultez le journal pour plus de détails: $LOG_FILE"
fi
