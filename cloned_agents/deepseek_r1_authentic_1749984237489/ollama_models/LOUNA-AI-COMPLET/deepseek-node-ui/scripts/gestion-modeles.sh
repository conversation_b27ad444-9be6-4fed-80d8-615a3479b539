#!/bin/bash

# Couleurs pour une meilleure lisibilité
BLEU='\033[0;34m'
VERT='\033[0;32m'
ROUGE='\033[0;31m'
JAUNE='\033[0;33m'
SANS='\033[0m'

# Vérifier si Ollama est installé
if ! command -v ollama &> /dev/null; then
    echo -e "${ROUGE}Ollama n'est pas installé. Veuillez l'installer depuis https://ollama.com/download${SANS}"
    exit 1
fi

# Vérifier si Ollama est en cours d'exécution
if ! curl -s http://localhost:11434/api/version &> /dev/null; then
    echo -e "${JAUNE}Démarrage d'Ollama...${SANS}"
    ollama serve &
    sleep 3
fi

# Fonction pour afficher le menu principal
afficher_menu() {
    clear
    echo -e "${BLEU}=======================================${SANS}"
    echo -e "${BLEU}  GESTIONNAIRE DE MODÈLES OLLAMA  ${SANS}"
    echo -e "${BLEU}=======================================${SANS}"
    echo -e "${VERT}1. Afficher les modèles installés${SANS}"
    echo -e "${VERT}2. Télécharger un nouveau modèle${SANS}"
    echo -e "${VERT}3. Supprimer un modèle${SANS}"
    echo -e "${VERT}4. Afficher les informations d'un modèle${SANS}"
    echo -e "${VERT}5. Comparer les modèles disponibles${SANS}"
    echo -e "${VERT}6. Démarrer l'interface avec un modèle spécifique${SANS}"
    echo -e "${ROUGE}0. Quitter${SANS}"
    echo -e "${BLEU}=======================================${SANS}"
    echo -ne "${JAUNE}Choisissez une option: ${SANS}"
}

# Fonction pour afficher les modèles installés
afficher_modeles_installes() {
    echo -e "\n${BLEU}Modèles installés sur votre système:${SANS}"
    ollama list
    echo -e "\nTaille totale des modèles:"
    du -sh ~/.ollama/models 2>/dev/null || echo -e "${ROUGE}Impossible de déterminer la taille${SANS}"
    
    echo -e "\n${JAUNE}Appuyez sur Entrée pour continuer...${SANS}"
    read
}

# Fonction pour télécharger un nouveau modèle
telecharger_modele() {
    echo -e "\n${BLEU}=== TÉLÉCHARGEMENT DE MODÈLE ===${SANS}"
    echo -e "${JAUNE}Modèles populaires:${SANS}"
    echo -e "1. llama3.1 - Meta AI (8B, 70B)"
    echo -e "2. mistral - Mistral AI (7B)"
    echo -e "3. gemma - Google (2B, 7B)" 
    echo -e "4. phi3 - Microsoft (3.8B)"
    echo -e "5. deepseek-r1 - DeepSeek (7B)"
    echo -e "6. incept5/llama3.1-claude - Llama 3.1 avec prompt Claude"
    echo -e "7. Autre (spécifier le nom complet)"
    
    echo -ne "\n${JAUNE}Sélectionnez un modèle à télécharger (1-7): ${SANS}"
    read choix_modele
    
    case $choix_modele in
        1)
            echo -ne "${JAUNE}Quelle taille? (8b, 70b): ${SANS}"
            read taille
            modele="llama3.1:$taille"
            ;;
        2)
            modele="mistral"
            ;;
        3)
            echo -ne "${JAUNE}Quelle taille? (2b, 7b): ${SANS}"
            read taille
            modele="gemma:$taille"
            ;;
        4)
            modele="phi3"
            ;;
        5)
            modele="deepseek-r1:7b"
            ;;
        6)
            modele="incept5/llama3.1-claude"
            ;;
        7)
            echo -ne "${JAUNE}Entrez le nom complet du modèle: ${SANS}"
            read modele
            ;;
        *)
            echo -e "${ROUGE}Option invalide!${SANS}"
            echo -e "${JAUNE}Appuyez sur Entrée pour continuer...${SANS}"
            read
            return
            ;;
    esac
    
    echo -e "${BLEU}Téléchargement du modèle $modele...${SANS}"
    ollama pull $modele
    
    echo -e "\n${JAUNE}Appuyez sur Entrée pour continuer...${SANS}"
    read
}

# Fonction pour supprimer un modèle
supprimer_modele() {
    echo -e "\n${BLEU}=== SUPPRESSION DE MODÈLE ===${SANS}"
    echo -e "${JAUNE}Modèles installés:${SANS}"
    ollama list
    
    echo -ne "\n${JAUNE}Entrez le nom exact du modèle à supprimer: ${SANS}"
    read modele
    
    echo -ne "${ROUGE}Êtes-vous sûr de vouloir supprimer $modele? (o/n): ${SANS}"
    read confirmation
    
    if [[ $confirmation == "o" || $confirmation == "O" ]]; then
        echo -e "${BLEU}Suppression du modèle $modele...${SANS}"
        ollama rm $modele
        echo -e "${VERT}Modèle supprimé!${SANS}"
    else
        echo -e "${JAUNE}Suppression annulée.${SANS}"
    fi
    
    echo -e "\n${JAUNE}Appuyez sur Entrée pour continuer...${SANS}"
    read
}

# Fonction pour afficher les informations d'un modèle
afficher_infos_modele() {
    echo -e "\n${BLEU}=== INFORMATIONS SUR UN MODÈLE ===${SANS}"
    echo -e "${JAUNE}Modèles installés:${SANS}"
    ollama list
    
    echo -ne "\n${JAUNE}Entrez le nom du modèle pour voir ses détails: ${SANS}"
    read modele
    
    echo -e "${BLEU}Informations sur le modèle $modele:${SANS}"
    ollama show $modele
    
    echo -e "\n${JAUNE}Appuyez sur Entrée pour continuer...${SANS}"
    read
}

# Fonction pour comparer les modèles disponibles
comparer_modeles() {
    echo -e "\n${BLEU}=== COMPARAISON DES MODÈLES ===${SANS}"
    
    echo -e "${JAUNE}Voici une comparaison des modèles populaires disponibles:${SANS}"
    echo -e "
${VERT}Modèle                  | Taille | Forces                                        | Faiblesses${SANS}
----------------------------------------------------------------------------------------
Claude 3.5 Sonnet         | ~100B+ | Le plus puissant, excellente compréhension     | Non disponible localement
                          |        | Raisonnement avancé, vision multimodale        |
----------------------------------------------------------------------------------------
Llama 3.1 70B            | 70B    | Très bon en général, open source               | Nécessite beaucoup de RAM
                          |        | Bonne compréhension du contexte                |
----------------------------------------------------------------------------------------
Llama 3.1 8B             | 8B     | Léger, performances décentes                   | Moins capable que les modèles plus grands
                          |        | Fonctionne sur des machines modestes           |
----------------------------------------------------------------------------------------
incept5/llama3.1-claude  | ~8B    | Utilise le prompt système de Claude            | Bien moins puissant que le vrai Claude
                          |        | Fonctionne localement                          |
----------------------------------------------------------------------------------------
DeepSeek r1 7B           | 7B     | Bon pour du code, léger                        | Moins bon pour les tâches générales
                          |        | Performances décentes                          |
----------------------------------------------------------------------------------------
Mistral                   | 7B     | Excellent rapport taille/performance           | Moins bon que les modèles plus grands
                          |        | Bonne compréhension du contexte                |
----------------------------------------------------------------------------------------
Phi3                      | 3.8B   | Très petit, surprenant pour sa taille          | Capacités limitées par sa taille
                          |        | Fonctionne sur des machines modestes           |
    "
    
    echo -e "\n${JAUNE}Appuyez sur Entrée pour continuer...${SANS}"
    read
}

# Fonction pour démarrer l'interface avec un modèle spécifique
demarrer_interface() {
    echo -e "\n${BLEU}=== DÉMARRAGE DE L'INTERFACE ===${SANS}"
    echo -e "${JAUNE}Modèles installés:${SANS}"
    ollama list
    
    echo -ne "\n${JAUNE}Entrez le nom du modèle à utiliser: ${SANS}"
    read modele
    
    # Modifier le fichier french-code-server.js pour utiliser le modèle choisi
    sed -i '' "s/model: \"[^\"]*\"/model: \"$modele\"/" ../french-code-server.js
    
    echo -e "${VERT}Configuration mise à jour pour utiliser $modele${SANS}"
    echo -e "${BLEU}Démarrage de l'interface...${SANS}"
    
    # Retourner au répertoire parent et démarrer le serveur
    cd ..
    bash start-code-fr.sh &
    
    echo -e "\n${JAUNE}Appuyez sur Entrée pour continuer...${SANS}"
    read
}

# Boucle principale
while true; do
    afficher_menu
    read choix
    
    case $choix in
        1)
            afficher_modeles_installes
            ;;
        2)
            telecharger_modele
            ;;
        3)
            supprimer_modele
            ;;
        4)
            afficher_infos_modele
            ;;
        5)
            comparer_modeles
            ;;
        6)
            demarrer_interface
            ;;
        0)
            echo -e "${VERT}Merci d'avoir utilisé le gestionnaire de modèles Ollama. Au revoir!${SANS}"
            exit 0
            ;;
        *)
            echo -e "${ROUGE}Option invalide!${SANS}"
            echo -e "${JAUNE}Appuyez sur Entrée pour continuer...${SANS}"
            read
            ;;
    esac
done
