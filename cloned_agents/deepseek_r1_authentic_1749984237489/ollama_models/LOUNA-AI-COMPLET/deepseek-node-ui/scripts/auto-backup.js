/**
 * Module de sauvegarde automatique pour le projet Jarvis
 * Ce module surveille les modifications importantes et déclenche des sauvegardes automatiques
 */

const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');

class AutoBackup {
  constructor(options = {}) {
    this.options = {
      backupInterval: options.backupInterval || 3600000, // 1 heure par défaut
      backupScript: options.backupScript || path.join(__dirname, 'backup-project.sh'),
      minChangesForBackup: options.minChangesForBackup || 10, // Nombre minimum de modifications pour déclencher une sauvegarde
      debug: options.debug || false,
      enabled: options.enabled !== undefined ? options.enabled : true
    };
    
    this.lastBackupTime = Date.now();
    this.changesSinceLastBackup = 0;
    this.isBackupRunning = false;
    this.backupInterval = null;
    
    // Rendre le script exécutable
    try {
      fs.chmodSync(this.options.backupScript, '755');
    } catch (error) {
      this.log(`Erreur lors de la modification des permissions du script: ${error.message}`);
    }
    
    this.log('Module de sauvegarde automatique initialisé');
  }
  
  /**
   * Démarre le service de sauvegarde automatique
   */
  start() {
    if (!this.options.enabled) {
      this.log('Service de sauvegarde automatique désactivé');
      return;
    }
    
    this.log('Démarrage du service de sauvegarde automatique');
    
    // Planifier des sauvegardes périodiques
    this.backupInterval = setInterval(() => {
      this.checkAndBackup(true);
    }, this.options.backupInterval);
    
    // Effectuer une sauvegarde initiale
    setTimeout(() => {
      this.checkAndBackup(true);
    }, 60000); // Attendre 1 minute après le démarrage
  }
  
  /**
   * Arrête le service de sauvegarde automatique
   */
  stop() {
    this.log('Arrêt du service de sauvegarde automatique');
    
    if (this.backupInterval) {
      clearInterval(this.backupInterval);
      this.backupInterval = null;
    }
  }
  
  /**
   * Enregistre une modification importante
   * @param {string} type - Type de modification
   * @param {string} description - Description de la modification
   */
  recordChange(type, description) {
    if (!this.options.enabled) return;
    
    this.changesSinceLastBackup++;
    this.log(`Modification enregistrée (${this.changesSinceLastBackup}): ${type} - ${description}`);
    
    // Vérifier si une sauvegarde est nécessaire
    this.checkAndBackup();
  }
  
  /**
   * Vérifie si une sauvegarde est nécessaire et la déclenche si c'est le cas
   * @param {boolean} force - Forcer la sauvegarde même si les conditions ne sont pas remplies
   */
  checkAndBackup(force = false) {
    if (this.isBackupRunning) {
      this.log('Une sauvegarde est déjà en cours, ignoré');
      return;
    }
    
    const timeSinceLastBackup = Date.now() - this.lastBackupTime;
    const shouldBackup = force || 
                        (this.changesSinceLastBackup >= this.options.minChangesForBackup && 
                         timeSinceLastBackup >= 300000); // Au moins 5 minutes entre les sauvegardes
    
    if (shouldBackup) {
      this.runBackup();
    }
  }
  
  /**
   * Exécute le script de sauvegarde
   */
  runBackup() {
    this.isBackupRunning = true;
    this.log('Démarrage de la sauvegarde automatique...');
    
    exec(this.options.backupScript, (error, stdout, stderr) => {
      this.isBackupRunning = false;
      this.lastBackupTime = Date.now();
      this.changesSinceLastBackup = 0;
      
      if (error) {
        this.log(`Erreur lors de la sauvegarde: ${error.message}`);
        return;
      }
      
      if (stderr) {
        this.log(`Avertissements lors de la sauvegarde: ${stderr}`);
      }
      
      this.log('Sauvegarde automatique terminée avec succès');
      this.log(stdout);
    });
  }
  
  /**
   * Écrit un message dans le journal
   * @param {string} message - Message à journaliser
   */
  log(message) {
    if (this.options.debug) {
      console.log(`[AutoBackup] ${message}`);
    }
  }
}

module.exports = AutoBackup;
