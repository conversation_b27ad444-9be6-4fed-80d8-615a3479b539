#!/bin/bash

# Script de sauvegarde automatique du projet Jarvis
# Créé le $(date +"%Y-%m-%d")

# Configuration
SOURCE_DIR="/Users/<USER>/Documents/augment-projects/Jarvis"
MODELS_DIR="/Users/<USER>/.ollama/models"
BACKUP_DIR="/Volumes/seagate/Jarvis_Backup"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_NAME="Jarvis_Backup_${TIMESTAMP}"
LOG_FILE="${BACKUP_DIR}/backup_log.txt"

# Fonction pour écrire dans le journal
log_message() {
    echo "[$(date +"%Y-%m-%d %H:%M:%S")] $1" >> "$LOG_FILE"
    echo "[$(date +"%Y-%m-%d %H:%M:%S")] $1"
}

# Vérifier si le disque de sauvegarde est monté
if [ ! -d "/Volumes/seagate" ]; then
    echo "ERREUR: Le disque de sauvegarde n'est pas monté. Veuillez connecter le disque Seagate."
    exit 1
fi

# Créer le répertoire de sauvegarde s'il n'existe pas
if [ ! -d "$BACKUP_DIR" ]; then
    mkdir -p "$BACKUP_DIR"
    log_message "Répertoire de sauvegarde créé: $BACKUP_DIR"
fi

# Créer le fichier journal s'il n'existe pas
if [ ! -f "$LOG_FILE" ]; then
    touch "$LOG_FILE"
    log_message "Fichier journal créé"
fi

# Créer un nouveau répertoire pour cette sauvegarde
FULL_BACKUP_PATH="${BACKUP_DIR}/${BACKUP_NAME}"
mkdir -p "$FULL_BACKUP_PATH"

log_message "Début de la sauvegarde: $BACKUP_NAME"

# Créer les sous-répertoires
mkdir -p "$FULL_BACKUP_PATH/code"
mkdir -p "$FULL_BACKUP_PATH/models"

# Copier les fichiers du projet
log_message "Copie des fichiers du projet Jarvis..."
rsync -av --progress "$SOURCE_DIR/" "$FULL_BACKUP_PATH/code/" 2>&1 | tee -a "$LOG_FILE"
PROJECT_COPY_STATUS=$?

# Copier les modèles d'IA
log_message "Copie des modèles d'IA (cela peut prendre un certain temps)..."
rsync -av --progress "$MODELS_DIR/" "$FULL_BACKUP_PATH/models/" 2>&1 | tee -a "$LOG_FILE"
MODELS_COPY_STATUS=$?

# Vérifier si les sauvegardes ont réussi
if [ $PROJECT_COPY_STATUS -eq 0 ] && [ $MODELS_COPY_STATUS -eq 0 ]; then
    log_message "Sauvegarde terminée avec succès: $BACKUP_NAME"

    # Créer un fichier de lien symbolique vers la dernière sauvegarde
    echo "$FULL_BACKUP_PATH" > "${BACKUP_DIR}/latest_backup.txt"

    # Afficher l'espace disque restant
    SPACE_LEFT=$(df -h "/Volumes/seagate" | awk 'NR==2 {print $4}')
    log_message "Espace restant sur le disque de sauvegarde: $SPACE_LEFT"
else
    if [ $PROJECT_COPY_STATUS -ne 0 ]; then
        log_message "ERREUR: La sauvegarde du projet a échoué"
    fi
    if [ $MODELS_COPY_STATUS -ne 0 ]; then
        log_message "ERREUR: La sauvegarde des modèles a échoué"
    fi
fi

# Nettoyer les anciennes sauvegardes (garder les 5 plus récentes)
cd "$BACKUP_DIR"
ls -dt Jarvis_Backup_* | tail -n +6 | xargs -I {} rm -rf {} 2>/dev/null
if [ $? -eq 0 ]; then
    log_message "Nettoyage des anciennes sauvegardes terminé"
fi

log_message "Processus de sauvegarde terminé"
echo "Sauvegarde terminée. Consultez le journal pour plus de détails: $LOG_FILE"
