#!/bin/bash

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Afficher le logo
echo -e "${BLUE}"
echo "  ____                 ____            _      ____  _ "
echo " |  _ \  ___  ___ _ __/ ___|  ___  ___| | __ |  _ \/ |"
echo " | | | |/ _ \/ _ \ '_ \___ \ / _ \/ _ \ |/ / | |_) | |"
echo " | |_| |  __/  __/ |_) |__) |  __/  __/   <  |  _ <| |"
echo " |____/ \___|\___| .__/____/ \___|\___|_|\_\ |_| \_\_|"
echo "                 |_|                                  "
echo -e "${NC}"
echo -e "${CYAN}Interface Cognitive avec Mémoire Thermique et MCP${NC}"
echo -e "${YELLOW}Développé par Jean-<PERSON>AV<PERSON>${NC}"
echo ""

# Vérifier d'abord si un serveur est en cours d'exécution sur le port 3001
if lsof -i:3001 &> /dev/null; then
    echo -e "${YELLOW}Un serveur est déjà en cours d'exécution sur le port 3001.${NC}"
    echo -e "${YELLOW}Arrêt du serveur existant...${NC}"
    kill $(lsof -t -i:3001) 2> /dev/null || true
    sleep 2
fi

# Créer les dossiers nécessaires
echo -e "${BLUE}Création des dossiers nécessaires...${NC}"
mkdir -p data/memory
mkdir -p data/memory/instant
mkdir -p data/memory/short_term
mkdir -p data/memory/working
mkdir -p data/memory/medium_term
mkdir -p data/memory/long_term
mkdir -p data/memory/dream
mkdir -p data/memory/kyber

# Vérifier si Ollama est installé
if ! command -v ollama &> /dev/null; then
    echo -e "${YELLOW}Ollama n'est pas installé. L'application fonctionnera en mode simulé.${NC}"
    export OLLAMA_AVAILABLE=false
else
    # Vérifier si Ollama est en cours d'exécution
    echo -e "${BLUE}Vérification si Ollama est en cours d'exécution...${NC}"
    if curl -s http://localhost:11434/api/version &> /dev/null; then
        echo -e "${GREEN}Ollama est en cours d'exécution.${NC}"
        export OLLAMA_AVAILABLE=true
        
        # Vérifier si le modèle DeepSeek r1 est disponible
        echo -e "${BLUE}Vérification si le modèle DeepSeek r1 est disponible...${NC}"
        if ollama list | grep -q "deepseek-r1"; then
            echo -e "${GREEN}Le modèle DeepSeek r1 est disponible.${NC}"
        else
            echo -e "${YELLOW}Le modèle DeepSeek r1 n'est pas disponible. L'application fonctionnera en mode simulé.${NC}"
            export OLLAMA_AVAILABLE=false
        fi
    else
        echo -e "${YELLOW}Ollama n'est pas en cours d'exécution. L'application fonctionnera en mode simulé.${NC}"
        export OLLAMA_AVAILABLE=false
    fi
fi

# Démarrer le serveur cognitif
echo -e "${BLUE}Démarrage du serveur cognitif avec mémoire thermique et MCP...${NC}"
echo -e "${GREEN}L'interface sera disponible à l'adresse http://localhost:3001${NC}"
echo -e "${GREEN}L'interface cognitive sera disponible à l'adresse http://localhost:3001/cognitive${NC}"
echo -e "${YELLOW}Appuyez sur Ctrl+C pour arrêter le serveur.${NC}"
echo ""

# Création d'un URL endpoint pour l'interface cognitive si non existant
if ! grep -q "/cognitive" "server-cognitif.js"; then
    echo -e "${YELLOW}Ajout de la route /cognitive au serveur...${NC}"
    sed -i '' '/app.get.*.\/.*=>.*{/ a\\
app.get("/cognitive", (req, res) => {\
  res.render("cognitive", { title: "Interface Cognitive DeepSeek" });\
});\
' server-cognitif.js || echo -e "${RED}Impossible de modifier le fichier server-cognitif.js.${NC}"
fi

# Démarrer le serveur
node server-cognitif.js
