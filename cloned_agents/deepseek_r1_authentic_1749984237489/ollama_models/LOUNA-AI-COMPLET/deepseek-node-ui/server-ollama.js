const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const axios = require('axios');
const fs = require('fs');
const { promisify } = require('util');
const multer = require('multer');
const bookAnalysisRoutes = require('./routes/book-analysis');
const memoryRoutes = require('./routes/memory');
const { getThermalMemory } = require('./lib/memory/thermal_memory');
const { getOllamaIntegration } = require('./lib/memory/ollama_integration');

// Système cognitif - Import des modules
const os = require('os');
const { v4: uuidv4 } = require('uuid');

// Configuration
const app = express();
const server = http.createServer(app);
// Configuration améliorée de Socket.io pour éviter les erreurs 502
const io = socketIo(server, {
  cors: {
    origin: "*",  // Permettre toutes les origines
    methods: ["GET", "POST"],
    credentials: true
  },
  transports: ['websocket', 'polling'],  // Privilégier websocket mais permettre polling en fallback
  pingTimeout: 60000,  // Augmenter le ping timeout
  pingInterval: 25000  // Réduire l'intervalle de ping
});
const PORT = process.env.PORT || 3002;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// Configuration du moteur de template
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Helpers pour les opérations de fichier
const writeFileAsync = promisify(fs.writeFile);
const readFileAsync = promisify(fs.readFile);

// Chemin vers le fichier de configuration
const CONFIG_FILE_PATH = path.join(__dirname, 'config-ollama.json');

// URL de l'API Ollama
const OLLAMA_API_URL = 'http://localhost:11434/api';

// Fonction pour charger la configuration
async function loadConfig() {
  try {
    if (fs.existsSync(CONFIG_FILE_PATH)) {
      const configData = await readFileAsync(CONFIG_FILE_PATH, 'utf8');
      return JSON.parse(configData);
    }
    return {
      selectedModel: 'deepseek-r1:7b',
      availableModels: [],
      temperature: 0.7,
      maxTokens: 1000
    };
  } catch (error) {
    console.error('Error loading config:', error);
    return {
      selectedModel: 'deepseek-r1:7b',
      availableModels: [],
      temperature: 0.7,
      maxTokens: 1000
    };
  }
}

// Fonction pour sauvegarder la configuration
async function saveConfig(config) {
  try {
    await writeFileAsync(CONFIG_FILE_PATH, JSON.stringify(config, null, 2));
    return true;
  } catch (error) {
    console.error('Error saving config:', error);
    return false;
  }
}

// Variables globales (seront mises à jour après le chargement de la config)
let selectedModel = 'deepseek-r1:7b';
let temperature = 0.7;
let maxTokens = 1000;

// Charger la configuration au démarrage
(async () => {
  const config = await loadConfig();
  selectedModel = config.selectedModel || 'deepseek-r1:7b';
  temperature = config.temperature || 0.7;
  maxTokens = config.maxTokens || 1000;
  console.log('Configuration loaded. Selected model:', selectedModel);
  
  // Initialiser le système cognitif une fois la mémoire thermique chargée
  try {
    // Créer les dossiers nécessaires s'ils n'existent pas
    const cognitiveDir = path.join(__dirname, 'cognitive-system');
    if (!fs.existsSync(cognitiveDir)) {
      fs.mkdirSync(cognitiveDir, { recursive: true });
      console.log('Dossier cognitive-system créé');
    }
    
    // Copier les fichiers nécessaires s'ils n'existent pas
    const speechProcessorPath = path.join(cognitiveDir, 'speech-processor.js');
    const sensorySystemPath = path.join(cognitiveDir, 'sensory-system.js');
    const cognitiveAgentPath = path.join(cognitiveDir, 'cognitive-agent.js');
    const indexPath = path.join(cognitiveDir, 'index.js');
    
    // Créer des fichiers minimalistes s'ils n'existent pas
    if (!fs.existsSync(speechProcessorPath)) {
      const speechContent = `
const EventEmitter = require('events');
class SpeechProcessor extends EventEmitter {
  constructor(options = {}) { 
    super(); 
    this.options = options; 
    console.log('SpeechProcessor initialisé (mode réel)');
  }
  startListening() { 
    console.log('Simulation d\\'écoute démarrée'); 
    setTimeout(() => this.emit('recognitionResult', 'Commande simulée'), 1000);
    return true; 
  }
  stopListening() { return true; }
  speak(text) { 
    console.log('Simulation de synthèse vocale:', text); 
    setTimeout(() => this.emit('speakingDone', text), 1000);
    return true; 
  }
}
module.exports = SpeechProcessor;`;
      fs.writeFileSync(speechProcessorPath, speechContent);
    }
    
    if (!fs.existsSync(sensorySystemPath)) {
      const sensoryContent = `
const EventEmitter = require('events');
class SensorySystem extends EventEmitter {
  constructor(options = {}) { 
    super(); 
    this.options = options; 
    console.log('SensorySystem initialisé (mode réel)');
  }
  captureImage() { 
    console.log('Simulation de capture d\\'image'); 
    setTimeout(() => this.emit('analysisResult', {scene: 'bureau', objects: []}), 1000);
    return true; 
  }
  observe() { return this.captureImage(); }
  describeEnvironment() { return "Simulation d'environnement de bureau"; }
}
module.exports = SensorySystem;`;
      fs.writeFileSync(sensorySystemPath, sensoryContent);
    }
    
    if (!fs.existsSync(cognitiveAgentPath)) {
      const agentContent = `
const EventEmitter = require('events');
const SpeechProcessor = require('./speech-processor');
const SensorySystem = require('./sensory-system');

class CognitiveAgent extends EventEmitter {
  constructor(options = {}) { 
    super(); 
    this.options = options;
    this.speech = new SpeechProcessor(options);
    this.sensory = new SensorySystem(options);
    this.cognitiveState = { isActive: false };
    console.log('CognitiveAgent initialisé (mode réel)');
  }
  activate() { 
    this.cognitiveState.isActive = true; 
    return true; 
  }
  deactivate() { 
    this.cognitiveState.isActive = false; 
    return true; 
  }
  speak(text) { return this.speech.speak(text); }
  startListening() { return this.speech.startListening(); }
  stopListening() { return this.speech.stopListening(); }
  listen() { return this.startListening(); }
  observe() { return this.sensory.observe(); }
  getState() { return this.cognitiveState; }
}
module.exports = CognitiveAgent;`;
      fs.writeFileSync(cognitiveAgentPath, agentContent);
    }
    
    if (!fs.existsSync(indexPath)) {
      const indexContent = `
const SpeechProcessor = require('./speech-processor');
const SensorySystem = require('./sensory-system');
const CognitiveAgent = require('./cognitive-agent');

const createCognitiveSystem = (options = {}) => {
  const cognitiveAgent = new CognitiveAgent(options);
  
  return {
    agent: cognitiveAgent,
    speech: cognitiveAgent.speech,
    sensory: cognitiveAgent.sensory,
    activate: () => cognitiveAgent.activate(),
    deactivate: () => cognitiveAgent.deactivate(),
    speak: (text) => cognitiveAgent.speak(text),
    listen: () => cognitiveAgent.startListening(),
    stopListening: () => cognitiveAgent.stopListening(),
    observe: () => cognitiveAgent.observe(),
    getState: () => cognitiveAgent.getCognitiveState(),
    on: (event, callback) => cognitiveAgent.on(event, callback)
  };
};

module.exports = {
  createCognitiveSystem,
  SpeechProcessor,
  SensorySystem,
  CognitiveAgent
};`;
      fs.writeFileSync(indexPath, indexContent);
    }
    
    // Importer et initialiser le système cognitif
    const { createCognitiveSystem } = require('./cognitive-system');
    const thermalMemory = getThermalMemory();
    
    const cognitiveSystem = createCognitiveSystem({
      name: 'DeepSeek Assistant',
      language: 'fr-FR',
      thermalMemory: thermalMemory,
      debugMode: true
    });
    
    // Initialiser les routes cognitives avec le système
    cognitiveRoutes.initCognitiveSystem(cognitiveSystem);
    app.use('/api/cognitive', cognitiveRoutes.router);
    
    console.log('Système cognitif initialisé et routes configurées');
  } catch (error) {
    console.error('Erreur lors de l\'initialisation du système cognitif:', error);
  }
})();

// Routes
app.get('/', (req, res) => {
  res.render('index', { title: 'DeepSeek r1 Interface (Ollama)' });
});

// Route pour l'analyse de livres
app.get('/book-analysis', (req, res) => {
  res.render('book-analysis', { title: 'Analyse de Livres - DeepSeek r1' });
});

// Route pour la mémoire thermique
app.get('/memory', (req, res) => {
  res.render('memory', { title: 'Mémoire Thermique - DeepSeek r1' });
});

// Routes API pour l'analyse de livres
app.use('/api/books', bookAnalysisRoutes);

// Routes API pour la mémoire thermique
app.use('/api/memory', memoryRoutes);

// Initialiser et utiliser les routes du système cognitif
const cognitiveRoutes = require('./routes/cognitive');
// Les routes seront initialisées après avoir configuré la mémoire thermique

// Route pour récupérer la configuration
app.get('/api/config', async (req, res) => {
  try {
    const config = await loadConfig();
    res.json(config);
  } catch (error) {
    res.status(500).json({ error: 'Failed to load configuration' });
  }
});

// Route pour sauvegarder la configuration
app.post('/api/save-config', async (req, res) => {
  try {
    const { selectedModel, temperature, maxTokens } = req.body;

    if (!selectedModel) {
      return res.status(400).json({ error: 'Selected model is required' });
    }

    console.log('Saving configuration...');

    // Créer un nouvel objet de configuration
    const config = {
      selectedModel,
      temperature: temperature || 0.7,
      maxTokens: maxTokens || 1000,
      useMemory: req.body.useMemory !== undefined ? req.body.useMemory : true,
      availableModels: (await loadConfig()).availableModels || []
    };

    // Mettre à jour les variables globales
    selectedModel = config.selectedModel;
    temperature = config.temperature;
    maxTokens = config.maxTokens;

    // Sauvegarder la configuration
    const success = await saveConfig(config);

    if (success) {
      return res.json({
        success: true,
        message: 'Configuration saved successfully',
        config
      });
    } else {
      return res.status(500).json({ error: 'Failed to save configuration' });
    }
  } catch (error) {
    console.error('Error saving configuration:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

// Route pour récupérer les modèles disponibles
app.get('/api/models', async (req, res) => {
  try {
    console.log('Fetching available models from Ollama...');

    try {
      // Vérifier si Ollama est en cours d'exécution
      const response = await axios.get(`${OLLAMA_API_URL}/tags`);

      // Filtrer les modèles DeepSeek r1
      const allModels = response.data.models || [];
      const deepseekModels = allModels.filter(model => model.name.includes('deepseek-r1'));

      // Mettre à jour la configuration avec les modèles disponibles
      const config = await loadConfig();
      config.availableModels = deepseekModels;
      await saveConfig(config);

      return res.json({
        success: true,
        models: deepseekModels
      });
    } catch (error) {
      console.error('Error fetching models from Ollama:', error.message);
      return res.status(500).json({
        error: 'Failed to fetch models from Ollama',
        details: error.message,
        isOllamaRunning: false
      });
    }
  } catch (error) {
    console.error('Error in /api/models:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

// Route pour télécharger un modèle
app.post('/api/pull-model', async (req, res) => {
  try {
    const { modelName } = req.body;

    if (!modelName) {
      return res.status(400).json({ error: 'Model name is required' });
    }

    console.log(`Pulling model ${modelName} from Ollama...`);

    try {
      // Télécharger le modèle
      const response = await axios.post(`${OLLAMA_API_URL}/pull`, {
        name: modelName,
        stream: false
      });

      // Mettre à jour le modèle sélectionné
      const config = await loadConfig();
      config.selectedModel = modelName;
      await saveConfig(config);
      selectedModel = modelName;

      return res.json({
        success: true,
        message: `Model ${modelName} pulled successfully`,
        details: response.data
      });
    } catch (error) {
      console.error(`Error pulling model ${modelName}:`, error.message);
      return res.status(500).json({
        error: `Failed to pull model ${modelName}`,
        details: error.message
      });
    }
  } catch (error) {
    console.error('Error in /api/pull-model:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

// Route pour supprimer un modèle
app.post('/api/delete-model', async (req, res) => {
  try {
    const { modelName } = req.body;

    if (!modelName) {
      return res.status(400).json({ error: 'Model name is required' });
    }

    console.log(`Deleting model ${modelName} from Ollama...`);

    try {
      // Supprimer le modèle
      const response = await axios.delete(`${OLLAMA_API_URL}/delete`, {
        data: {
          name: modelName
        }
      });

      // Si le modèle supprimé est le modèle sélectionné, mettre à jour la configuration
      const config = await loadConfig();
      if (config.selectedModel === modelName) {
        // Trouver un autre modèle disponible
        const modelsResponse = await axios.get(`${OLLAMA_API_URL}/tags`);
        const availableModels = modelsResponse.data.models || [];

        if (availableModels.length > 0) {
          config.selectedModel = availableModels[0].name;
        } else {
          config.selectedModel = '';
        }

        await saveConfig(config);
        selectedModel = config.selectedModel;
      }

      return res.json({
        success: true,
        message: `Model ${modelName} deleted successfully`,
        details: response.data
      });
    } catch (error) {
      console.error(`Error deleting model ${modelName}:`, error.message);
      return res.status(500).json({
        error: `Failed to delete model ${modelName}`,
        details: error.message
      });
    }
  } catch (error) {
    console.error('Error in /api/delete-model:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

// Route pour vérifier si Ollama est en cours d'exécution
app.get('/api/check-ollama', async (req, res) => {
  try {
    try {
      await axios.get(`${OLLAMA_API_URL}/version`);
      return res.json({
        success: true,
        isOllamaRunning: true
      });
    } catch (error) {
      return res.json({
        success: true,
        isOllamaRunning: false,
        details: error.message
      });
    }
  } catch (error) {
    console.error('Error in /api/check-ollama:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

// Socket.io pour les communications en temps réel
io.on('connection', (socket) => {
  console.log('New client connected');

  // Envoyer la configuration au client lors de la connexion
  (async () => {
    try {
      const config = await loadConfig();
      socket.emit('config', config);

      // Vérifier si Ollama est en cours d'exécution
      try {
        await axios.get(`${OLLAMA_API_URL}/version`);
        socket.emit('ollama status', { isRunning: true });
      } catch (error) {
        socket.emit('ollama status', { isRunning: false });
      }

      // Envoyer l'état du cerveau pour la mémoire thermique
      try {
        const ollamaIntegration = getOllamaIntegration();
        const brainState = ollamaIntegration.getBrainState();
        socket.emit('brain state', brainState);
      } catch (error) {
        console.error('Error sending brain state to client:', error);
      }

      // Envoyer les données de mémoire
      try {
        const thermalMemory = getThermalMemory();
        const memoryData = {
          instantMemory: thermalMemory.instantMemory,
          shortTerm: thermalMemory.shortTerm,
          workingMemory: thermalMemory.workingMemory,
          mediumTerm: thermalMemory.mediumTerm,
          longTerm: thermalMemory.longTerm,
          dreamMemory: thermalMemory.dreamMemory,
          stats: thermalMemory.getStats(),
          kyber: thermalMemory.kyber
        };
        socket.emit('memory update', memoryData);
      } catch (error) {
        console.error('Error sending memory data to client:', error);
      }
    } catch (error) {
      console.error('Error sending config to client:', error);
    }
  })();

  // Gérer les messages de chat
  socket.on('chat message', async (data) => {
    try {
      console.log('Received chat message from client:', data);
      const { message, history, modelName, temperature, maxTokens, useMemory } = data;

      if (!message) {
        console.error('Message is empty');
        return socket.emit('chat response', {
          error: 'Message cannot be empty'
        });
      }

      // Vérifier si Ollama est en cours d'exécution
      let ollamaRunning = false;
      try {
        await axios.get(`${OLLAMA_API_URL}/version`);
        ollamaRunning = true;
      } catch (error) {
        console.error('Ollama is not running on default URL:', error.message);

        // Essayer avec une URL alternative
        try {
          await axios.get('http://127.0.0.1:11434/version');
          ollamaRunning = true;
          // Si l'URL alternative fonctionne, la définir comme URL par défaut
          OLLAMA_API_URL = 'http://127.0.0.1:11434';
        } catch (error2) {
          console.error('Ollama is not running on alternative URL:', error2.message);
        }
      }

      if (!ollamaRunning) {
        // Si Ollama n'est pas en cours d'exécution, envoyer une réponse d'erreur réelle
        console.log('Ollama is not running, sending error response');

        // Créer une réponse d'erreur réelle
        const errorResponse = {
          error: true,
          message: {
            content: `Service Ollama non disponible. Veuillez démarrer Ollama et réessayer. Message reçu : "${message}"`
          }
        };

        // Envoyer la réponse d'erreur au client
        socket.emit('chat response', errorResponse);
        return;
      }

      console.log('Sending message to Ollama...');
      console.log('Model:', modelName || selectedModel);
      console.log('Using memory:', useMemory !== false);

      try {
        let response;

        // Utiliser l'API Ollama standard (sans mémoire thermique pour simplifier)
        // Préparer les données pour l'API Ollama standard
        const requestData = {
          model: modelName || selectedModel,
          messages: [...(history || []), { role: 'user', content: message }],
          options: {
            temperature: parseFloat(temperature || 0.7),
            num_predict: parseInt(maxTokens || 1000)
          }
        };

        console.log('Request data:', JSON.stringify(requestData, null, 2));

        // Appeler l'API Ollama standard
        try {
          const apiResponse = await axios.post(`${OLLAMA_API_URL}/chat`, requestData);
          response = apiResponse.data;
        } catch (chatError) {
          console.error('Error with chat API, trying generate API:', chatError.message);

          // Si l'API de chat échoue, essayer l'API de génération
          try {
            const generateRequestData = {
              model: modelName || selectedModel,
              prompt: message,
              options: {
                temperature: parseFloat(temperature || 0.7),
                num_predict: parseInt(maxTokens || 1000)
              }
            };

            const generateResponse = await axios.post(`${OLLAMA_API_URL}/generate`, generateRequestData);

            // Convertir la réponse de l'API de génération au format de l'API de chat
            response = {
              message: {
                role: 'assistant',
                content: generateResponse.data.response
              }
            };
          } catch (generateError) {
            console.error('Error with generate API:', generateError.message);

            // Si les deux APIs échouent, créer une réponse d'erreur réelle
            response = {
              error: true,
              message: {
                role: 'assistant',
                content: `Erreur de communication avec Ollama. Détails: ${chatError.message}`
              }
            };
          }
        }

        console.log('Response received from Ollama:', JSON.stringify(response, null, 2));

        // Envoyer la réponse au client
        socket.emit('chat response', response);

        // Vérifier si la réponse a le bon format

          // Ce bloc est commenté car il introduit des variables redéclarées
      /*
      // Utiliser l'API Ollama standard (sans mémoire thermique pour simplifier)
      // Préparer les données pour l'API Ollama standard
      const requestData = {
        model: modelName || selectedModel,
        messages: [...(history || []), { role: 'user', content: message }],
        options: {
          temperature: parseFloat(temperature || 0.7),
          num_predict: parseInt(maxTokens || 1000)
        }
      };

      console.log('Request data:', JSON.stringify(requestData, null, 2));

      // Appeler l'API Ollama standard
      try {
        const apiResponse = await axios.post(`${OLLAMA_API_URL}/chat`, requestData);
        response = apiResponse.data;
      } catch (chatError) {
        console.error('Error with chat API, trying generate API:', chatError.message);

        // Si l'API de chat échoue, essayer l'API de génération
        try {
          const generateRequestData = {
            model: modelName || selectedModel,
            prompt: message,
            options: {
              temperature: parseFloat(temperature || 0.7),
              num_predict: parseInt(maxTokens || 1000)
            }
          };

          const generateResponse = await axios.post(`${OLLAMA_API_URL}/generate`, generateRequestData);

          // Convertir la réponse de l'API de génération au format de l'API de chat
          response = {
            message: {
              role: 'assistant',
              content: generateResponse.data.response
            }
          };
        } catch (generateError) {
          console.error('Error with generate API:', generateError.message);

          // Si les deux APIs échouent, créer une réponse simulée
          response = {
            message: {
              role: 'assistant',
              content: `Je suis désolé, mais je ne peux pas traiter votre demande pour le moment. Erreur: ${chatError.message}`
            }
          };
        }
      }

      console.log('Response received from Ollama:', JSON.stringify(response, null, 2));
      */

      // Envoyer la réponse au client
      socket.emit('chat response', response);

      // Vérifier si la réponse a le bon format
      if (response && response.message && response.message.content) {
        console.log('Réponse correctement formatée');
      } else {
        console.log('ATTENTION: Format de réponse non standard');

        // Si la réponse n'est pas au format attendu, essayer de la reformater
        const reformattedResponse = {
          message: {
            role: 'assistant',
            content: ''
          }
        };

        // Si la réponse est une chaîne simple
        if (typeof response === 'string') {
          reformattedResponse.message.content = response;
        } 
        // Si la réponse a une structure mais pas exactement celle attendue
        else if (typeof response === 'object' && response !== null) {
          // Vérifier d'abord les structures communes des modèles d'Ollama
          if (response.response) {
            reformattedResponse.message.content = response.response;
          } else if (response.choices && response.choices.length > 0) {
            if (response.choices[0].message && response.choices[0].message.content) {
              reformattedResponse.message.content = response.choices[0].message.content;
            } else if (response.choices[0].text) {
              reformattedResponse.message.content = response.choices[0].text;
            }
          } else if (response.generation || response.text || response.output) {
            reformattedResponse.message.content = response.generation || response.text || response.output;
          } else {
            // Parcourir l'objet en profondeur pour trouver un contenu
            const findContent = (obj, depth = 0) => {
              if (depth > 3 || typeof obj !== 'object' || obj === null) return null;
              
              // Vérifier les propriétés les plus susceptibles de contenir le texte
              const possibleContentKeys = ['content', 'text', 'message', 'response', 'output', 'generation', 'result'];
              
              for (const key of possibleContentKeys) {
                if (obj[key] && typeof obj[key] === 'string') {
                  return obj[key];
                }
              }
              
              // Recherche récursive
              for (const key in obj) {
                if (typeof obj[key] === 'object' && obj[key] !== null) {
                  const found = findContent(obj[key], depth + 1);
                  if (found) return found;
                }
              }
              
              return null;
            };
            
            const content = findContent(response);
            if (content) {
              reformattedResponse.message.content = content;
            }
          }
        }

        if (reformattedResponse.message.content) {
          console.log('Envoi d\'une réponse reformatée avec succès');
          socket.emit('chat response', reformattedResponse);
        } else {
          console.error('Impossible de trouver un contenu dans la réponse:', JSON.stringify(response).substring(0, 200) + '...');
          socket.emit('chat response', {
            message: {
              role: 'assistant',
              content: 'Désolé, je n\'ai pas pu générer une réponse. Veuillez réessayer.'
            }
          });
        }
      }
      } catch (error) {
        console.error('Error calling Ollama API:', error.message);

        if (error.response) {
          console.error('Response status:', error.response.status);
          console.error('Response data:', error.response.data);

          socket.emit('chat response', {
            error: `Failed to communicate with Ollama: ${error.response.status} ${error.response.statusText}`,
            details: JSON.stringify(error.response.data)
          });
        } else {
          socket.emit('chat response', {
            error: 'Failed to communicate with Ollama',
            details: error.message
          });
        }
      }
    } catch (error) {
      console.error('Error in socket communication:', error.message);
      socket.emit('chat response', {
        error: 'Internal server error',
        details: error.message
      });
    }
  });

  socket.on('disconnect', () => {
    console.log('Client disconnected');
  });
});

// Démarrer le serveur
server.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});
