/**
 * Script de restauration des fichiers à partir des sauvegardes
 * Ce script permet de restaurer un fichier à partir d'une sauvegarde spécifique
 * ou de restaurer la dernière sauvegarde d'un fichier
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Fonction pour lister les sauvegardes disponibles pour un fichier
function listBackups(filePath) {
  const backupDir = path.join(__dirname, 'backups', path.dirname(filePath));
  const fileName = path.basename(filePath);
  
  // Vérifier si le répertoire de sauvegarde existe
  if (!fs.existsSync(backupDir)) {
    console.error(`❌ Aucune sauvegarde trouvée pour ${filePath}`);
    return [];
  }
  
  // Lister les fichiers de sauvegarde
  const backupFiles = fs.readdirSync(backupDir)
    .filter(file => file.startsWith(`${fileName}.`) && file.endsWith('.bak'))
    .sort((a, b) => b.localeCompare(a)); // Trier par ordre décroissant (plus récent en premier)
  
  return backupFiles.map(file => path.join(backupDir, file));
}

// Fonction pour restaurer un fichier à partir d'une sauvegarde
function restoreFile(filePath, backupPath) {
  try {
    // Créer une sauvegarde du fichier actuel avant de le restaurer
    const now = new Date();
    const dateStr = now.toISOString().replace(/:/g, '-').replace(/\..+/, '');
    const currentBackupPath = path.join(
      __dirname, 
      'backups', 
      path.dirname(filePath), 
      `${path.basename(filePath)}.current.${dateStr}.bak`
    );
    
    // Créer le répertoire de sauvegarde s'il n'existe pas
    const backupDir = path.join(__dirname, 'backups', path.dirname(filePath));
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }
    
    // Sauvegarder le fichier actuel
    if (fs.existsSync(path.join(__dirname, filePath))) {
      fs.copyFileSync(path.join(__dirname, filePath), currentBackupPath);
      console.log(`✅ Fichier actuel sauvegardé: ${currentBackupPath}`);
    }
    
    // Restaurer le fichier à partir de la sauvegarde
    fs.copyFileSync(backupPath, path.join(__dirname, filePath));
    console.log(`✅ Fichier restauré: ${filePath}`);
    return true;
  } catch (error) {
    console.error(`❌ Erreur lors de la restauration de ${filePath}:`, error.message);
    return false;
  }
}

// Fonction pour restaurer la dernière sauvegarde d'un fichier
function restoreLatestBackup(filePath) {
  const backups = listBackups(filePath);
  
  if (backups.length === 0) {
    console.error(`❌ Aucune sauvegarde trouvée pour ${filePath}`);
    return false;
  }
  
  const latestBackup = backups[0];
  console.log(`📦 Dernière sauvegarde trouvée: ${path.basename(latestBackup)}`);
  
  return restoreFile(filePath, latestBackup);
}

// Interface en ligne de commande
if (require.main === module) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  rl.question('Entrez le chemin du fichier à restaurer (ex: server-luna.js): ', (filePath) => {
    const backups = listBackups(filePath);
    
    if (backups.length === 0) {
      rl.close();
      return;
    }
    
    console.log('\nSauvegardes disponibles:');
    backups.forEach((backup, index) => {
      console.log(`${index + 1}. ${path.basename(backup)}`);
    });
    
    rl.question('\nEntrez le numéro de la sauvegarde à restaurer (ou "latest" pour la plus récente): ', (answer) => {
      if (answer.toLowerCase() === 'latest' || answer === '1') {
        restoreLatestBackup(filePath);
      } else {
        const index = parseInt(answer) - 1;
        if (index >= 0 && index < backups.length) {
          restoreFile(filePath, backups[index]);
        } else {
          console.error('❌ Numéro de sauvegarde invalide.');
        }
      }
      
      rl.close();
    });
  });
}

// Exporter les fonctions pour une utilisation dans d'autres scripts
module.exports = {
  listBackups,
  restoreFile,
  restoreLatestBackup
};
