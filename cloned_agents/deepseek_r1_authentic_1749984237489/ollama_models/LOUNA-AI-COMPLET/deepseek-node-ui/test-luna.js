/**
 * Script de test pour l'agent Luna après sa formation accélérée
 * Ce script envoie des problèmes complexes mais résolvables à l'agent Luna
 * pour vérifier s'il utilise correctement les connaissances acquises
 */

const axios = require('axios');

// Configuration
const LUNA_API_URL = 'http://localhost:3001/api/chat';
const DELAY_BETWEEN_TESTS = 10000; // 10 secondes entre chaque test

// Problèmes de test complexes mais résolvables
const testProblems = [
  // Test de mathématiques appliquant l'équation du second degré
  {
    problem: "Un projectile est lancé verticalement vers le haut avec une vitesse initiale de 20 m/s. Sachant que l'accélération due à la gravité est de -9.8 m/s², à quel moment le projectile atteindra-t-il une hauteur de 15 mètres ? Utilisez l'équation du mouvement h = v₀t + (1/2)at² où h est la hauteur, v₀ la vitesse initiale, a l'accélération et t le temps.",
    domain: "Mathématiques/Physique",
    expectedApproach: "Équation du second degré"
  },

  // Test de chimie appliquant la théorie des orbitales moléculaires
  {
    problem: "Expliquez pourquoi la molécule O₂ est paramagnétique alors que N₂ est diamagnétique en utilisant la théorie des orbitales moléculaires. Dessinez les diagrammes d'orbitales moléculaires pour ces deux molécules et calculez leur ordre de liaison.",
    domain: "Chimie",
    expectedApproach: "Théorie des orbitales moléculaires"
  },

  // Test d'informatique appliquant l'algorithme de tri rapide
  {
    problem: "Vous avez un tableau de nombres [38, 27, 43, 3, 9, 82, 10] que vous devez trier par ordre croissant. Montrez les étapes détaillées de l'application de l'algorithme de tri rapide (quicksort) sur ce tableau, en choisissant comme pivot le premier élément de chaque sous-tableau.",
    domain: "Informatique",
    expectedApproach: "Algorithme de tri rapide (quicksort)"
  },

  // Test de biologie sur la réplication de l'ADN
  {
    problem: "Une cellule contient un fragment d'ADN avec la séquence suivante sur le brin codant : 5'-ATGCTAGCATGC-3'. Montrez la séquence du brin complémentaire et expliquez comment se déroulerait la réplication de ce fragment, en détaillant les enzymes impliquées et les différences entre le brin continu et le brin discontinu.",
    domain: "Biologie",
    expectedApproach: "Réplication de l'ADN"
  },

  // Test d'économie sur l'inflation
  {
    problem: "Un pays connaît une inflation annuelle de 8%. Si le prix d'un produit est de 100€ aujourd'hui, quel sera son prix dans 5 ans si l'inflation reste constante ? De plus, expliquez quelles politiques monétaires la banque centrale de ce pays pourrait mettre en œuvre pour réduire cette inflation et quels seraient leurs effets potentiels sur l'économie.",
    domain: "Économie",
    expectedApproach: "Inflation et politique monétaire"
  },

  // Test de philosophie sur l'impératif catégorique de Kant
  {
    problem: "Analysez le dilemme éthique suivant à la lumière de l'impératif catégorique de Kant : Une personne envisage de mentir lors d'un entretien d'embauche pour obtenir un poste qu'elle estime mériter, mais pour lequel elle ne possède pas toutes les qualifications requises. Cette personne se dit que 'tout le monde le fait' et que c'est nécessaire pour réussir dans un marché du travail compétitif.",
    domain: "Philosophie",
    expectedApproach: "Impératif catégorique de Kant"
  },

  // Test interdisciplinaire combinant plusieurs domaines
  {
    problem: "Une entreprise pharmaceutique développe un nouveau médicament qui pourrait traiter une maladie rare touchant 1 personne sur 100 000. Le développement coûtera 500 millions d'euros, et le traitement pourrait être vendu à 50 000€ par patient. Dans une population de 500 millions de personnes, analysez : 1) La viabilité économique de ce projet, 2) Les considérations éthiques selon l'approche kantienne, 3) Comment la structure moléculaire du médicament pourrait être analysée par spectroscopie RMN, et 4) Comment un algorithme pourrait optimiser la distribution du médicament aux patients (problème apparenté au voyageur de commerce).",
    domain: "Interdisciplinaire",
    expectedApproach: "Économie, Éthique, Chimie et Informatique"
  }
];

// Fonction pour envoyer un problème à l'agent Luna
async function sendProblem(problem) {
  try {
    console.log(`\n\n========== TEST: ${problem.domain} ==========`);
    console.log(`Problème: ${problem.problem.substring(0, 100)}...`);
    console.log(`Approche attendue: ${problem.expectedApproach}`);

    const response = await axios.post(LUNA_API_URL, {
      message: problem.problem,
      model: 'deepseek-r1:7b' // Utiliser le modèle par défaut
    });

    console.log(`\nRéponse de Luna (extrait): "${response.data.response.substring(0, 200)}..."`);

    // Analyse simple de la réponse
    const keywords = problem.expectedApproach.toLowerCase().split(' ');
    const responseText = response.data.response.toLowerCase();

    let approachDetected = false;
    for (const keyword of keywords) {
      if (keyword.length > 3 && responseText.includes(keyword)) {
        approachDetected = true;
        break;
      }
    }

    console.log(`Utilisation de l'approche attendue: ${approachDetected ? 'OUI ✓' : 'NON ✗'}`);

    return {
      problem: problem,
      response: response.data.response,
      approachDetected: approachDetected
    };
  } catch (error) {
    console.error('Erreur lors de l\'envoi du problème:', error.message);
    return null;
  }
}

// Fonction principale pour exécuter les tests
async function runTests() {
  console.log('Démarrage des tests pour l\'agent Luna après sa formation accélérée...');

  const results = [];

  for (const problem of testProblems) {
    const result = await sendProblem(problem);
    if (result) {
      results.push(result);
    }

    // Attendre avant de passer au prochain test
    await new Promise(resolve => setTimeout(resolve, DELAY_BETWEEN_TESTS));
  }

  // Afficher le résumé des tests
  console.log('\n\n========== RÉSUMÉ DES TESTS ==========');
  console.log(`Tests réussis: ${results.filter(r => r.approachDetected).length}/${results.length}`);

  for (const [index, result] of results.entries()) {
    console.log(`${index + 1}. ${result.problem.domain}: ${result.approachDetected ? 'RÉUSSI ✓' : 'ÉCHEC ✗'}`);
  }
}

// Attendre que la formation soit terminée avant de lancer les tests
console.log('Attente de 2 minutes pour laisser la formation se terminer...');
setTimeout(() => {
  runTests().catch(console.error);
}, 120000); // 2 minutes
