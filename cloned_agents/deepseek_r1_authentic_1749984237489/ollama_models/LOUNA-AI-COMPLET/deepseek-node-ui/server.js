const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const axios = require('axios');
const fs = require('fs');
const { promisify } = require('util');

// Configuration
const app = express();
const server = http.createServer(app);
const io = socketIo(server);
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// Configuration du moteur de template
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Helpers pour les opérations de fichier
const writeFileAsync = promisify(fs.writeFile);
const readFileAsync = promisify(fs.readFile);

// Chemin vers le fichier de configuration
const CONFIG_FILE_PATH = path.join(__dirname, 'config.json');

// Fonction pour charger la configuration
async function loadConfig() {
  try {
    if (fs.existsSync(CONFIG_FILE_PATH)) {
      const configData = await readFileAsync(CONFIG_FILE_PATH, 'utf8');
      return JSON.parse(configData);
    }
    return { apiKey: '', apiUrl: 'https://api.deepseek.com' };
  } catch (error) {
    console.error('Error loading config:', error);
    return { apiKey: '', apiUrl: 'https://api.deepseek.com' };
  }
}

// Fonction pour sauvegarder la configuration
async function saveConfig(config) {
  try {
    await writeFileAsync(CONFIG_FILE_PATH, JSON.stringify(config, null, 2));
    return true;
  } catch (error) {
    console.error('Error saving config:', error);
    return false;
  }
}

// Variables globales (seront mises à jour après le chargement de la config)
let DEEPSEEK_API_URL = 'https://api.deepseek.com';  // URL officielle de l'API DeepSeek
let DEEPSEEK_API_KEY = '';

// Charger la configuration au démarrage
(async () => {
  const config = await loadConfig();
  DEEPSEEK_API_URL = config.apiUrl || 'https://api.deepseek.com';  // URL officielle de l'API DeepSeek
  DEEPSEEK_API_KEY = config.apiKey || '';
  console.log('Configuration loaded. API URL:', DEEPSEEK_API_URL);
  console.log('API Key configured:', DEEPSEEK_API_KEY ? 'Yes' : 'No');

  // Mettre à jour la configuration avec la bonne URL
  if (config.apiUrl !== DEEPSEEK_API_URL) {
    config.apiUrl = DEEPSEEK_API_URL;
    await saveConfig(config);
    console.log('Updated config with correct API URL');
  }
})();

// Routes
app.get('/', (req, res) => {
  res.render('index', { title: 'DeepSeek r1 Interface' });
});

// Route pour récupérer la configuration
app.get('/api/config', async (req, res) => {
  try {
    const config = await loadConfig();
    // Ne pas renvoyer la clé API complète pour des raisons de sécurité
    const maskedConfig = {
      apiUrl: config.apiUrl,
      hasApiKey: !!config.apiKey,
      apiKeyPreview: config.apiKey ? `${config.apiKey.substring(0, 4)}...${config.apiKey.substring(config.apiKey.length - 4)}` : ''
    };
    res.json(maskedConfig);
  } catch (error) {
    res.status(500).json({ error: 'Failed to load configuration' });
  }
});

// Route pour sauvegarder la clé API - version simplifiée
app.post('/api/save-api-key', async (req, res) => {
  try {
    const { apiKey } = req.body;

    if (!apiKey) {
      return res.status(400).json({ error: 'API key is required' });
    }

    console.log('Saving API key...');

    // Créer directement un nouvel objet de configuration
    const config = {
      apiKey: apiKey,
      apiUrl: 'https://api.deepseek.com'  // URL officielle de l'API DeepSeek
    };

    // Écrire directement dans le fichier
    try {
      fs.writeFileSync(CONFIG_FILE_PATH, JSON.stringify(config, null, 2));
      console.log('API key saved to file successfully');

      // Mettre à jour la variable globale
      DEEPSEEK_API_KEY = apiKey;
      DEEPSEEK_API_URL = config.apiUrl;

      return res.json({
        success: true,
        message: 'API key saved successfully',
        apiKeyPreview: `${apiKey.substring(0, 4)}...${apiKey.substring(apiKey.length - 4)}`
      });
    } catch (fsError) {
      console.error('Error writing to config file:', fsError);
      return res.status(500).json({ error: 'Failed to write to config file' });
    }
  } catch (error) {
    console.error('Error saving API key:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

// Route pour tester la clé API
app.get('/api/test-api-key', async (req, res) => {
  try {
    if (!DEEPSEEK_API_KEY) {
      return res.status(400).json({ error: 'No API key configured' });
    }

    console.log('Testing API key...');

    try {
      // Tester la connexion à l'API
      const testResponse = await axios.get(`${DEEPSEEK_API_URL}/v1/models`, {
        headers: {
          'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('API test successful');
      console.log('Available models:', testResponse.data);

      return res.json({
        success: true,
        message: 'API key is valid',
        models: testResponse.data
      });
    } catch (apiError) {
      console.error('API test failed:', apiError.message);

      if (apiError.response) {
        console.error('Response status:', apiError.response.status);
        console.error('Response data:', apiError.response.data);

        return res.status(apiError.response.status).json({
          error: `API test failed: ${apiError.response.status} ${apiError.response.statusText}`,
          details: apiError.response.data
        });
      } else {
        return res.status(500).json({
          error: 'API test failed',
          details: apiError.message
        });
      }
    }
  } catch (error) {
    console.error('Error testing API key:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

// API pour communiquer avec DeepSeek r1
app.post('/api/chat', async (req, res) => {
  try {
    const { message, history } = req.body;

    // Si l'API key n'est pas configurée, retourner un message d'erreur
    if (!DEEPSEEK_API_KEY) {
      return res.status(400).json({
        error: 'API key not configured. Please set the DEEPSEEK_API_KEY environment variable.'
      });
    }

    // Préparer les données pour l'API DeepSeek
    const requestData = {
      model: 'deepseek-r1',
      messages: [...history, { role: 'user', content: message }],
      temperature: 0.7,
      max_tokens: 1000
    };

    // Appeler l'API DeepSeek
    const response = await axios.post(`${DEEPSEEK_API_URL}/v1/chat/completions`, requestData, {
      headers: {
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    // Retourner la réponse
    return res.json(response.data);
  } catch (error) {
    console.error('Error calling DeepSeek API:', error.message);
    return res.status(500).json({
      error: 'Failed to communicate with DeepSeek API',
      details: error.message
    });
  }
});

// Socket.io pour les communications en temps réel
io.on('connection', (socket) => {
  console.log('New client connected');

  // Envoyer la configuration au client lors de la connexion
  (async () => {
    try {
      const config = await loadConfig();
      socket.emit('config', {
        hasApiKey: !!config.apiKey,
        apiKeyPreview: config.apiKey ? `${config.apiKey.substring(0, 4)}...${config.apiKey.substring(config.apiKey.length - 4)}` : ''
      });
    } catch (error) {
      console.error('Error sending config to client:', error);
    }
  })();

  // Gérer la sauvegarde de la clé API
  socket.on('save api key', async (data) => {
    try {
      const { apiKey } = data;

      if (!apiKey) {
        return socket.emit('api key saved', {
          success: false,
          error: 'API key is required'
        });
      }

      // Charger la configuration existante
      const config = await loadConfig();

      // Mettre à jour la clé API
      config.apiKey = apiKey;

      // Sauvegarder la configuration
      const success = await saveConfig(config);

      if (success) {
        // Mettre à jour la variable globale
        DEEPSEEK_API_KEY = apiKey;
        socket.emit('api key saved', {
          success: true,
          message: 'API key saved successfully',
          apiKeyPreview: `${apiKey.substring(0, 4)}...${apiKey.substring(apiKey.length - 4)}`
        });
      } else {
        socket.emit('api key saved', {
          success: false,
          error: 'Failed to save API key'
        });
      }
    } catch (error) {
      console.error('Error saving API key:', error);
      socket.emit('api key saved', {
        success: false,
        error: 'Internal server error'
      });
    }
  });

  socket.on('chat message', async (data) => {
    try {
      const { message, history, temperature, maxTokens } = data;

      // Si l'API key n'est pas configurée, envoyer un message d'erreur
      if (!DEEPSEEK_API_KEY) {
        return socket.emit('chat response', {
          error: 'API key not configured. Please save your API key first.'
        });
      }

      console.log('Sending message to DeepSeek API...');
      console.log('API URL:', DEEPSEEK_API_URL);
      console.log('API Key (masked):', `${DEEPSEEK_API_KEY.substring(0, 4)}...${DEEPSEEK_API_KEY.substring(DEEPSEEK_API_KEY.length - 4)}`);

      // Préparer les données pour l'API DeepSeek
      const requestData = {
        model: 'deepseek-reasoner',  // Utiliser le modèle DeepSeek-R1 (deepseek-reasoner)
        messages: [...history, { role: 'user', content: message }],
        temperature: temperature || 0.7,
        max_tokens: maxTokens || 1000
      };

      console.log('Request data:', JSON.stringify(requestData, null, 2));

      try {
        // Tester la connexion à l'API
        const testResponse = await axios.get(`${DEEPSEEK_API_URL}/v1/models`, {
          headers: {
            'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
            'Content-Type': 'application/json'
          }
        });
        console.log('Available models:', testResponse.data);

        // Si nous avons une liste de modèles, utiliser le premier modèle disponible
        if (testResponse.data && testResponse.data.data && testResponse.data.data.length > 0) {
          requestData.model = testResponse.data.data[0].id;
          console.log('Using model:', requestData.model);
        }
      } catch (testError) {
        console.error('Error testing API connection:', testError.message);
        if (testError.response) {
          console.error('Response status:', testError.response.status);
          console.error('Response data:', testError.response.data);
        }
      }

      // Appeler l'API DeepSeek
      const response = await axios.post(`${DEEPSEEK_API_URL}/v1/chat/completions`, requestData, {
        headers: {
          'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Response received from DeepSeek API');

      // Envoyer la réponse au client
      socket.emit('chat response', response.data);
    } catch (error) {
      console.error('Error in socket communication:', error.message);

      // Afficher plus de détails sur l'erreur
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);

        // Envoyer des détails plus précis au client
        socket.emit('chat response', {
          error: `Failed to communicate with DeepSeek API: ${error.response.status} ${error.response.statusText}`,
          details: JSON.stringify(error.response.data)
        });
      } else {
        socket.emit('chat response', {
          error: 'Failed to communicate with DeepSeek API',
          details: error.message
        });
      }
    }
  });

  socket.on('disconnect', () => {
    console.log('Client disconnected');
  });
});

// Démarrer le serveur
server.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});
