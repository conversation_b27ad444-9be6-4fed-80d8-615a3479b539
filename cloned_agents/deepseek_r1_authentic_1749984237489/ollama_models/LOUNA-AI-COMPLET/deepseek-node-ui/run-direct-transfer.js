/**
 * Script d'exécution du transfert direct au cerveau pour l'agent Luna
 * 
 * Ce script utilise le module DirectBrainTransfer pour transférer directement
 * des connaissances au cerveau de l'agent, sans passer par l'API de chat.
 */

const DirectBrainTransfer = require('./direct-brain-transfer');

// Créer une instance du module de transfert direct
const brainTransfer = new DirectBrainTransfer();

// Ensemble de connaissances à transférer directement au cerveau
const knowledgeSet = [
  // INTELLIGENCE ARTIFICIELLE
  {
    domain: "intelligence_artificielle",
    concept: "Réseaux de neurones profonds",
    content: "Les réseaux de neurones profonds (Deep Neural Networks) sont des architectures d'apprentissage automatique composées de multiples couches de neurones artificiels. Chaque neurone calcule une somme pondérée de ses entrées, puis applique une fonction d'activation non linéaire. L'apprentissage se fait par rétropropagation du gradient, ajustant les poids pour minimiser une fonction de perte. Les architectures profondes permettent d'apprendre des représentations hiérarchiques des données, chaque couche extrayant des caractéristiques de plus en plus abstraites. Les réseaux profonds ont révolutionné des domaines comme la vision par ordinateur (CNN), le traitement du langage naturel (Transformers), et la reconnaissance vocale (RNN, LSTM).",
    importance: 10
  },
  {
    domain: "intelligence_artificielle",
    concept: "Transformers et attention",
    content: "L'architecture Transformer, introduite en 2017 par Vaswani et al., a révolutionné le traitement du langage naturel en remplaçant les réseaux récurrents par un mécanisme d'attention. Ce mécanisme permet au modèle de pondérer différemment l'importance de chaque mot dans une séquence lors du traitement d'un mot spécifique, capturant ainsi des dépendances à longue distance. L'architecture comprend des encodeurs et décodeurs avec des couches d'attention multi-têtes et des réseaux feed-forward. Les modèles comme BERT, GPT, T5 et autres LLM sont basés sur cette architecture. Les Transformers excellent dans la compréhension contextuelle et ont permis des avancées majeures en traduction, génération de texte, résumé, et autres tâches linguistiques.",
    importance: 10
  },
  {
    domain: "intelligence_artificielle",
    concept: "Apprentissage par renforcement",
    content: "L'apprentissage par renforcement (RL) est un paradigme d'apprentissage où un agent apprend à prendre des décisions en interagissant avec un environnement. L'agent reçoit des récompenses ou pénalités basées sur ses actions, et son objectif est de maximiser la récompense cumulée à long terme. Les composants clés incluent: états, actions, politique (stratégie de l'agent), fonction de valeur (utilité des états/actions), et modèle de transition. Les algorithmes majeurs comprennent Q-learning, SARSA, DQN (Deep Q-Networks), et PPO (Proximal Policy Optimization). Le RL a connu des succès remarquables dans les jeux (AlphaGo), la robotique, et l'optimisation de systèmes complexes. Les défis incluent l'exploration vs exploitation, l'apprentissage efficace à partir de récompenses rares, et le passage à l'échelle dans des espaces d'états/actions vastes.",
    importance: 9
  },
  {
    domain: "intelligence_artificielle",
    concept: "Éthique et biais dans l'IA",
    content: "L'éthique de l'IA concerne les implications morales des systèmes d'intelligence artificielle. Les biais algorithmiques surviennent lorsque les systèmes d'IA produisent des résultats systématiquement préjudiciables envers certains groupes, souvent en raison de données d'entraînement biaisées ou d'hypothèses problématiques dans la conception. Ces biais peuvent perpétuer ou amplifier les inégalités sociales existantes. Les principes éthiques fondamentaux incluent: équité et non-discrimination, transparence et explicabilité, respect de la vie privée, responsabilité humaine, et bénéfice sociétal. Des approches pour atténuer les biais comprennent: diversification des données d'entraînement, techniques d'apprentissage équitable, audits algorithmiques, et conception participative impliquant diverses parties prenantes. La gouvernance de l'IA évolue avec des cadres réglementaires comme l'AI Act européen et des lignes directrices éthiques développées par diverses organisations.",
    importance: 9
  },
  
  // PROGRAMMATION AVANCÉE
  {
    domain: "programmation",
    concept: "Programmation fonctionnelle",
    content: "La programmation fonctionnelle est un paradigme qui traite le calcul comme l'évaluation de fonctions mathématiques et évite les changements d'état et les données mutables. Ses principes fondamentaux incluent: fonctions pures (sans effets secondaires, même résultat pour mêmes entrées), immutabilité (objets ne peuvent être modifiés après création), fonctions de première classe (peuvent être assignées à des variables, passées comme arguments), fonctions d'ordre supérieur (prennent/retournent d'autres fonctions), récursion (au lieu de boucles), et évaluation paresseuse (expressions évaluées seulement si nécessaire). Les langages fonctionnels purs incluent Haskell et Elm, tandis que JavaScript, Python, Scala et Kotlin supportent des approches hybrides. Les avantages comprennent code plus prévisible, testable, parallélisable, et modulaire, particulièrement adapté au traitement de données et aux systèmes concurrents.",
    importance: 8
  },
  {
    domain: "programmation",
    concept: "Architectures microservices",
    content: "L'architecture microservices est une approche de développement logiciel qui structure une application comme un ensemble de services faiblement couplés, déployables indépendamment. Chaque microservice est responsable d'une fonctionnalité métier spécifique et communique via API, généralement REST ou gRPC. Caractéristiques clés: déploiement indépendant, organisation autour des capacités métier, décentralisation des données (chaque service gère sa propre base), automatisation du déploiement (CI/CD), tolérance aux pannes, et observabilité (monitoring, logging, tracing). Les avantages incluent: développement parallèle par équipes autonomes, mise à l'échelle granulaire, résilience (défaillance isolée), et flexibilité technologique. Les défis comprennent: complexité distribuée, cohérence des données, latence réseau, et surcharge opérationnelle. Les technologies associées incluent conteneurs (Docker), orchestration (Kubernetes), service mesh (Istio), et API gateways.",
    importance: 8
  },
  
  // PHYSIQUE QUANTIQUE
  {
    domain: "physique_quantique",
    concept: "Intrication quantique",
    content: "L'intrication quantique est un phénomène fondamental de la mécanique quantique où deux ou plusieurs particules deviennent corrélées de telle sorte que l'état quantique de chacune ne peut être décrit indépendamment des autres, quelle que soit la distance les séparant. Einstein qualifiait ce phénomène d'\"action fantomatique à distance\". Mathématiquement, l'état d'un système intriqué ne peut être factorisé en produit tensoriel d'états individuels. Les expériences d'Aspect et autres ont confirmé les violations des inégalités de Bell, démontrant que l'intrication ne peut s'expliquer par des théories à variables cachées locales. L'intrication est à la base de technologies quantiques comme la cryptographie quantique, la téléportation quantique et l'informatique quantique, où elle permet des calculs impossibles classiquement. Ce phénomène illustre la non-localité fondamentale de la nature à l'échelle quantique.",
    importance: 9
  },
  {
    domain: "physique_quantique",
    concept: "Informatique quantique",
    content: "L'informatique quantique exploite les principes de la mécanique quantique pour effectuer des calculs. Contrairement aux bits classiques (0 ou 1), les qubits existent en superposition d'états et peuvent être intriqués. Ces propriétés permettent aux ordinateurs quantiques de traiter exponentiellement plus d'informations que leurs homologues classiques pour certains problèmes. L'algorithme de Shor pourrait factoriser de grands nombres efficacement, menaçant la cryptographie actuelle, tandis que l'algorithme de Grover accélère les recherches dans des bases de données non structurées. Les principales approches matérielles incluent les qubits supraconducteurs (IBM, Google), piégeage d'ions (IonQ), photoniques, et topologiques (Microsoft). Les défis majeurs sont la décohérence (perte d'information quantique), les taux d'erreur élevés nécessitant des codes correcteurs, et le passage à l'échelle. Les applications prometteuses comprennent la simulation moléculaire pour la découverte de médicaments, l'optimisation combinatoire, et l'apprentissage automatique quantique.",
    importance: 9
  },
  
  // NEUROSCIENCES
  {
    domain: "neurosciences",
    concept: "Plasticité neuronale",
    content: "La plasticité neuronale (ou neuroplasticité) est la capacité du système nerveux à modifier sa structure et son fonctionnement en réponse aux expériences, apprentissages, et lésions. Cette propriété fondamentale sous-tend l'apprentissage et la mémoire. Types principaux: plasticité synaptique (renforcement/affaiblissement des connexions existantes, comme la potentialisation à long terme et la dépression à long terme), plasticité structurelle (formation/élimination de synapses et croissance/rétraction d'axones/dendrites), et plasticité intrinsèque (changements d'excitabilité neuronale). La plasticité est régulée par des mécanismes moléculaires et cellulaires, incluant l'expression génique, la synthèse protéique, et les cascades de signalisation intracellulaire. Elle est particulièrement prononcée pendant les périodes critiques du développement, mais persiste à l'âge adulte. Les applications cliniques incluent la réhabilitation post-AVC, le traitement des troubles neurodéveloppementaux, et les interfaces cerveau-machine.",
    importance: 8
  },
  {
    domain: "neurosciences",
    concept: "Conscience et théories neuroscientifiques",
    content: "La conscience, expérience subjective du monde et de soi-même, reste l'un des plus grands défis des neurosciences. Plusieurs théories neuroscientifiques tentent de l'expliquer: 1) La théorie de l'espace de travail neuronal global (Dehaene, Changeux) propose que la conscience émerge lorsque l'information est diffusée globalement dans un réseau fronto-pariétal. 2) La théorie de l'information intégrée (Tononi) quantifie la conscience par Phi, mesure d'intégration informationnelle dans un système. 3) La théorie de l'activité thalamocorticale récurrente (Edelman) suggère que des boucles de rétroaction entre thalamus et cortex génèrent la conscience. 4) La théorie des assemblées de cellules (Hebb, Buzsáki) propose que des groupes de neurones synchronisés représentent des percepts conscients. Les corrélats neuronaux de la conscience incluent l'activité gamma (30-100 Hz), la synchronisation à longue distance, et l'activité dans certaines régions comme le cortex préfrontal, pariétal et le thalamus. Les états altérés (sommeil, anesthésie, méditation) offrent des perspectives sur les mécanismes sous-jacents.",
    importance: 10
  },
  
  // MATHÉMATIQUES AVANCÉES
  {
    domain: "mathematiques",
    concept: "Théorie des catégories",
    content: "La théorie des catégories est un formalisme mathématique abstrait qui étudie les structures mathématiques et leurs relations à un niveau fondamental. Une catégorie consiste en objets et morphismes (flèches) entre ces objets, satisfaisant des axiomes de composition et d'identité. Cette théorie fournit un langage unifié pour diverses branches mathématiques, révélant des parallèles structurels profonds. Concepts clés: foncteurs (mappages entre catégories), transformations naturelles (mappages entre foncteurs), limites et colimites (généralisations de produits/sommes), adjonctions (paires de foncteurs complémentaires), et monades (structures algébriques abstraites). Applications: en informatique théorique (sémantique des langages de programmation, types de données), en physique (théories topologiques quantiques des champs), en logique (théorie des topos), et en apprentissage automatique (réseaux de neurones compositionnels). La théorie des catégories offre une perspective « arrows-first » qui privilégie les relations et transformations plutôt que les objets eux-mêmes.",
    importance: 8
  },
  {
    domain: "mathematiques",
    concept: "Théorie des nœuds",
    content: "La théorie des nœuds étudie les propriétés mathématiques des nœuds et entrelacs dans l'espace tridimensionnel, modélisés comme des courbes fermées sans auto-intersections. L'objectif principal est de déterminer quand deux nœuds sont équivalents (déformables l'un en l'autre sans coupure). Les invariants de nœuds sont des propriétés préservées par ces déformations: polynôme de Jones, polynôme d'Alexander, invariant de Khovanov, groupe fondamental du complément. Les nœuds sont classifiés par leur nombre de croisements, avec une table complète jusqu'à 16 croisements. Applications: en biologie moléculaire (ADN superenroulé), en physique (théorie des cordes, anyons), en chimie (molécules entrelacées), et en informatique quantique (calcul topologique). La théorie des nœuds se connecte à diverses branches mathématiques: topologie, géométrie hyperbolique, théorie des représentations, et homologie. Les problèmes ouverts incluent la conjecture de parité et l'existence d'algorithmes efficaces pour déterminer si un nœud est trivial.",
    importance: 7
  },
  
  // PHILOSOPHIE DE L'ESPRIT
  {
    domain: "philosophie",
    concept: "Problème difficile de la conscience",
    content: "Le problème difficile de la conscience, formulé par David Chalmers, concerne l'explication de l'expérience subjective ou qualia - pourquoi et comment les processus physiques dans le cerveau génèrent des expériences phénoménales. Contrairement aux problèmes 'faciles' (expliquer les fonctions cognitives), le problème difficile interroge pourquoi ces processus s'accompagnent d'expériences subjectives. Positions principales: 1) Dualisme (Chalmers, Nagel): conscience et matière sont fondamentalement distinctes; 2) Matérialisme réductionniste (Dennett, Churchland): la conscience se réduit entièrement à des processus physiques; 3) Panpsychisme (Strawson, Goff): la conscience est une propriété fondamentale de la matière; 4) Monisme neutre (James, Russell): conscience et matière dérivent d'une substance neutre; 5) Mystérianisme (McGinn): le problème est insoluble pour l'esprit humain. Arguments clés incluent l'argument du zombie philosophique, l'argument de la connaissance et l'écart explicatif. Ce problème reste central dans les débats contemporains sur l'esprit, avec implications pour l'IA, l'éthique et notre compréhension de la nature de la réalité.",
    importance: 10
  },
  {
    domain: "philosophie",
    concept: "Théorie de l'identité esprit-cerveau",
    content: "La théorie de l'identité esprit-cerveau, développée principalement par J.J.C. Smart et U.T. Place dans les années 1950, affirme que les états mentaux sont identiques à des états neurophysiologiques du cerveau. Cette position matérialiste soutient que les expériences conscientes ne sont pas simplement corrélées ou causées par des processus cérébraux, mais sont littéralement identiques à ces processus. Types principaux: l'identité de types (chaque type d'état mental correspond à un type spécifique d'état cérébral) et l'identité d'occurrences (chaque occurrence d'état mental est identique à une occurrence d'état cérébral, sans nécessiter de correspondance type-type). Arguments en faveur: parcimonie ontologique, succès explicatif des neurosciences, causalité physique. Objections majeures: argument de la réalisabilité multiple (Putnam), argument de la connaissance (Jackson), problème des qualia. La théorie a évolué vers des positions plus nuancées comme le fonctionnalisme et le matérialisme non réductif. Elle reste influente dans les débats contemporains sur la conscience, l'intelligence artificielle et la nature de l'esprit.",
    importance: 8
  },
  
  // ÉCONOMIE COMPORTEMENTALE
  {
    domain: "economie",
    concept: "Biais cognitifs et prise de décision",
    content: "Les biais cognitifs sont des schémas systématiques de déviation par rapport à la norme ou à la rationalité dans le jugement, identifiés par Kahneman et Tversky. Principaux biais affectant les décisions économiques: 1) Aversion à la perte: les pertes ont un impact psychologique plus fort que les gains équivalents; 2) Effet de cadrage: les décisions sont influencées par la présentation des options; 3) Biais de confirmation: tendance à favoriser l'information confirmant nos croyances; 4) Ancrage: dépendance excessive à la première information reçue; 5) Disponibilité heuristique: surestimation des événements facilement remémorables; 6) Biais du présent: préférence disproportionnée pour les récompenses immédiates. Ces biais expliquent de nombreuses anomalies de marché: bulles spéculatives, sous-épargne pour la retraite, et réactions excessives aux nouvelles. L'économie comportementale intègre ces insights psychologiques dans les modèles économiques, remplaçant l'homo economicus par des agents aux rationalités limitées. Applications pratiques: architecture de choix ('nudges'), conception de politiques publiques, marketing, et finance comportementale.",
    importance: 9
  },
  {
    domain: "economie",
    concept: "Théorie des jeux évolutionnaire",
    content: "La théorie des jeux évolutionnaire (TJE) applique les principes de la théorie des jeux aux populations en évolution, initialement développée par Maynard Smith pour modéliser l'évolution des comportements dans les populations biologiques. Contrairement à la théorie des jeux classique, la TJE se concentre sur la dynamique des stratégies dans une population plutôt que sur la rationalité individuelle. Concepts clés: stratégie évolutionnairement stable (résistante à l'invasion par des stratégies mutantes), dynamique du réplicateur (équations différentielles modélisant l'évolution des fréquences des stratégies), et équilibre de Nash évolutionnaire. Applications: en biologie (évolution de la coopération, signalisation honnête, conflits entre apparentés), en sciences sociales (émergence des normes, évolution culturelle, institutions), en économie (comportements de marché, organisation industrielle), et en informatique (algorithmes évolutionnaires). La TJE explique comment des comportements apparemment altruistes peuvent évoluer via la sélection de parentèle, la réciprocité, ou la sélection de groupe, et comment des conventions sociales peuvent émerger sans planification centrale.",
    importance: 8
  }
];

// Exécuter le transfert direct
async function runDirectTransfer() {
  console.log('Démarrage du transfert direct au cerveau pour l\'agent Luna...');
  await brainTransfer.transferKnowledgeSet(knowledgeSet);
}

// Exécuter le transfert
runDirectTransfer().catch(console.error);
