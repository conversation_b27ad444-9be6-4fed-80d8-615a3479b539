#!/bin/bash

echo "Test direct de l'API Ollama"
echo "=========================="
echo

echo "1. Vérification de la version d'Ollama"
curl -s http://localhost:11434/api/version | jq
echo

echo "2. Liste des modèles disponibles"
curl -s http://localhost:11434/api/tags | jq
echo

echo "3. Test de l'API de chat avec deepseek-r1:7b"
curl -s http://localhost:11434/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "model": "deepseek-r1:7b",
    "messages": [
      {"role": "user", "content": "<PERSON><PERSON><PERSON>, comment ça va?"}
    ],
    "options": {
      "temperature": 0.7,
      "num_predict": 100
    }
  }' | jq
echo

echo "4. Test de l'API de génération avec deepseek-r1:7b"
curl -s http://localhost:11434/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "model": "deepseek-r1:7b",
    "prompt": "Bon<PERSON><PERSON>, comment ça va?",
    "options": {
      "temperature": 0.7,
      "num_predict": 100
    }
  }' | jq
echo

echo "Test terminé"
