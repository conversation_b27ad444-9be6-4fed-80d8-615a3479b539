/**
 * Routes pour le système cognitif
 * Gère les endpoints API pour les fonctionnalités de parole et de vision
 */

const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');

// Référence vers l'instance du système cognitif
// Ceci sera initialisé par le fichier serveur principal
let cognitiveSystem = null;

// Initialisation du système cognitif avec les dépendances nécessaires
const initCognitiveSystem = (system) => {
  cognitiveSystem = system;
  console.log('Routes du système cognitif initialisées');
};

// Middleware pour vérifier si le système cognitif est initialisé
const checkCognitiveSystem = (req, res, next) => {
  if (!cognitiveSystem) {
    return res.status(503).json({
      success: false,
      message: 'Le système cognitif n\'est pas encore initialisé'
    });
  }
  next();
};

// Appliquer le middleware à toutes les routes
router.use(checkCognitiveSystem);

// Activer le système cognitif
router.get('/activate', (req, res) => {
  try {
    const result = cognitiveSystem.activate();
    res.json({
      success: true,
      active: result,
      message: 'Système cognitif activé'
    });
  } catch (error) {
    console.error('Erreur lors de l\'activation du système cognitif:', error);
    res.status(500).json({
      success: false,
      message: `Erreur: ${error.message}`
    });
  }
});

// Désactiver le système cognitif
router.get('/deactivate', (req, res) => {
  try {
    const result = cognitiveSystem.deactivate();
    res.json({
      success: true,
      active: !result,
      message: 'Système cognitif désactivé'
    });
  } catch (error) {
    console.error('Erreur lors de la désactivation du système cognitif:', error);
    res.status(500).json({
      success: false,
      message: `Erreur: ${error.message}`
    });
  }
});

// Faire parler le système cognitif
router.post('/speak', (req, res) => {
  try {
    const { text } = req.body;
    
    if (!text) {
      return res.status(400).json({
        success: false,
        message: 'Le texte à prononcer est requis'
      });
    }
    
    const result = cognitiveSystem.speak(text);
    res.json({
      success: true,
      speaking: result,
      text: text,
      message: 'Synthèse vocale démarrée'
    });
  } catch (error) {
    console.error('Erreur lors de la synthèse vocale:', error);
    res.status(500).json({
      success: false,
      message: `Erreur: ${error.message}`
    });
  }
});

// Démarrer l'écoute vocale
router.get('/listen/start', (req, res) => {
  try {
    const result = cognitiveSystem.listen();
    res.json({
      success: true,
      listening: result,
      message: 'Écoute vocale démarrée'
    });
  } catch (error) {
    console.error('Erreur lors du démarrage de l\'écoute vocale:', error);
    res.status(500).json({
      success: false,
      message: `Erreur: ${error.message}`
    });
  }
});

// Arrêter l'écoute vocale
router.get('/listen/stop', (req, res) => {
  try {
    const result = cognitiveSystem.stopListening();
    res.json({
      success: true,
      listening: !result,
      message: 'Écoute vocale arrêtée'
    });
  } catch (error) {
    console.error('Erreur lors de l\'arrêt de l\'écoute vocale:', error);
    res.status(500).json({
      success: false,
      message: `Erreur: ${error.message}`
    });
  }
});

// Observer l'environnement
router.get('/observe', (req, res) => {
  try {
    const result = cognitiveSystem.observe();
    res.json({
      success: true,
      observing: result,
      message: 'Observation de l\'environnement démarrée'
    });
  } catch (error) {
    console.error('Erreur lors de l\'observation de l\'environnement:', error);
    res.status(500).json({
      success: false,
      message: `Erreur: ${error.message}`
    });
  }
});

// Obtenir l'état du système cognitif
router.get('/state', (req, res) => {
  try {
    const state = cognitiveSystem.getState();
    res.json({
      success: true,
      state: state
    });
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'état du système cognitif:', error);
    res.status(500).json({
      success: false,
      message: `Erreur: ${error.message}`
    });
  }
});

module.exports = {
  router,
  initCognitiveSystem
};