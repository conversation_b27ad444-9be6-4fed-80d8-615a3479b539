/**
 * Routes pour la connaissance du programme de Vision Ultra
 * Ce fichier définit les routes pour la connaissance du programme de l'agent
 */

const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');

// Référence au service de connaissance du programme
let programKnowledgeService;

/**
 * Initialise le routeur avec les services nécessaires
 * @param {Object} services - Services nécessaires
 * @param {Object} services.programKnowledgeService - Service de connaissance du programme
 */
function init(services) {
  programKnowledgeService = services.programKnowledgeService;
  console.log('Routeur de connaissance du programme initialisé');
  return router;
}

// Route pour la page de connaissance du programme
router.get('/program-knowledge', (req, res) => {
  res.render('luna-program-knowledge', {
    title: 'Luna - Connaissance du Programme',
    page: 'program-knowledge',
    currentDate: new Date().toLocaleString('fr-FR')
  });
});

// Route pour obtenir la connaissance du programme
router.get('/api/program-knowledge', (req, res) => {
  if (!programKnowledgeService) {
    return res.status(503).json({
      success: false,
      error: 'Service de connaissance du programme non disponible'
    });
  }

  const programKnowledge = programKnowledgeService.getProgramKnowledge();

  res.json({
    success: true,
    programKnowledge: programKnowledge.programKnowledge,
    lastScanTime: programKnowledge.lastScanTime
  });
});

// Route pour lancer un scan du programme
router.post('/api/program-knowledge/scan', (req, res) => {
  if (!programKnowledgeService) {
    return res.status(503).json({
      success: false,
      error: 'Service de connaissance du programme non disponible'
    });
  }

  // Le scan est asynchrone, donc on renvoie juste un statut de démarrage
  res.json({
    success: true,
    message: 'Scan du programme démarré'
  });

  // Émettre un événement pour lancer le scan
  programKnowledgeService.io.emit('scan program');
});

// Route pour lancer une analyse du programme
router.post('/api/program-knowledge/analyze', (req, res) => {
  if (!programKnowledgeService) {
    return res.status(503).json({
      success: false,
      error: 'Service de connaissance du programme non disponible'
    });
  }

  // L'analyse est asynchrone, donc on renvoie juste un statut de démarrage
  res.json({
    success: true,
    message: 'Analyse du programme démarrée'
  });

  // Lancer l'analyse des éléments manquants et incomplets
  programKnowledgeService.analyzeMissingAndIncompleteElements()
    .then(() => {
      console.log('Analyse du programme terminée');
    })
    .catch(error => {
      console.error('Erreur lors de l\'analyse du programme:', error);
    });
});

// Route pour obtenir les modules du programme
router.get('/api/program-knowledge/modules', (req, res) => {
  if (!programKnowledgeService) {
    return res.status(503).json({
      success: false,
      error: 'Service de connaissance du programme non disponible'
    });
  }

  const programKnowledge = programKnowledgeService.getProgramKnowledge();

  res.json({
    success: true,
    modules: programKnowledge.programKnowledge.modules
  });
});

// Route pour obtenir les interfaces du programme
router.get('/api/program-knowledge/interfaces', (req, res) => {
  if (!programKnowledgeService) {
    return res.status(503).json({
      success: false,
      error: 'Service de connaissance du programme non disponible'
    });
  }

  const programKnowledge = programKnowledgeService.getProgramKnowledge();

  res.json({
    success: true,
    interfaces: programKnowledge.programKnowledge.interfaces
  });
});

// Route pour obtenir les services du programme
router.get('/api/program-knowledge/services', (req, res) => {
  if (!programKnowledgeService) {
    return res.status(503).json({
      success: false,
      error: 'Service de connaissance du programme non disponible'
    });
  }

  const programKnowledge = programKnowledgeService.getProgramKnowledge();

  res.json({
    success: true,
    services: programKnowledge.programKnowledge.services
  });
});

// Route pour obtenir les capacités du programme
router.get('/api/program-knowledge/capabilities', (req, res) => {
  if (!programKnowledgeService) {
    return res.status(503).json({
      success: false,
      error: 'Service de connaissance du programme non disponible'
    });
  }

  const programKnowledge = programKnowledgeService.getProgramKnowledge();

  res.json({
    success: true,
    capabilities: programKnowledge.programKnowledge.capabilities
  });
});

// Route pour obtenir les routes du programme
router.get('/api/program-knowledge/routes', (req, res) => {
  if (!programKnowledgeService) {
    return res.status(503).json({
      success: false,
      error: 'Service de connaissance du programme non disponible'
    });
  }

  const programKnowledge = programKnowledgeService.getProgramKnowledge();

  res.json({
    success: true,
    routes: programKnowledge.programKnowledge.routes
  });
});

// Route pour obtenir les configurations du programme
router.get('/api/program-knowledge/configurations', (req, res) => {
  if (!programKnowledgeService) {
    return res.status(503).json({
      success: false,
      error: 'Service de connaissance du programme non disponible'
    });
  }

  const programKnowledge = programKnowledgeService.getProgramKnowledge();

  res.json({
    success: true,
    configurations: programKnowledge.programKnowledge.configurations
  });
});

/**
 * Initialise les gestionnaires d'événements Socket.IO
 * @param {Object} io - Instance Socket.IO
 */
function initSocketHandlers(io) {
  if (!programKnowledgeService) {
    console.error('Service de connaissance du programme non disponible pour les gestionnaires Socket.IO');
    return;
  }

  console.log('Gestionnaires Socket.IO pour la connaissance du programme initialisés');
}

module.exports = {
  router,
  init,
  initSocketHandlers
};
