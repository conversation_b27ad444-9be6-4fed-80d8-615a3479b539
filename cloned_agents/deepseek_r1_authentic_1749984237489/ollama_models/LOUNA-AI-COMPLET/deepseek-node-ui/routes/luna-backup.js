/**
 * Routes pour la sauvegarde Luna
 * Version temporaire pour permettre le démarrage de Luna
 */

const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');

// Route pour la page de sauvegarde
router.get('/backup', (req, res) => {
  res.render('luna-base', {
    page: 'luna-backup',
    title: 'Luna - Sauvegarde',
    path: '/luna/backup',
    systemStatus: {
      active: true,
      version: '1.0.0'
    }
  });
});

// Route API pour obtenir les sauvegardes disponibles
router.get('/api/backup/list', (req, res) => {
  res.json({
    success: true,
    backups: [
      {
        id: 'backup-' + Date.now(),
        date: new Date().toISOString(),
        size: '2.4 MB',
        type: 'Auto',
        status: 'Complet'
      }
    ]
  });
});

// Fonction d'initialisation du routeur de sauvegarde
function initBackupRouter(options = {}) {
  const socketIo = options.socketIo;
  
  // Initialiser les gestionnaires de socket
  const initSocketHandlers = (io) => {
    if (!io) return;
    
    io.on('connection', (socket) => {
      // Gérer les événements liés à la sauvegarde
      socket.on('backup:create', () => {
        // Simuler une création de sauvegarde
        socket.emit('backup:progress', { progress: 0 });
        
        let progress = 0;
        const progressInterval = setInterval(() => {
          progress += 20;
          socket.emit('backup:progress', { progress });
          
          if (progress >= 100) {
            clearInterval(progressInterval);
            socket.emit('backup:complete', {
              success: true,
              backupId: 'backup-' + Date.now(),
              date: new Date().toISOString(),
              size: '2.4 MB'
            });
          }
        }, 500);
      });
    });
  };
  
  return {
    router,
    initSocketHandlers
  };
}

module.exports = {
  router,
  initBackupRouter
};