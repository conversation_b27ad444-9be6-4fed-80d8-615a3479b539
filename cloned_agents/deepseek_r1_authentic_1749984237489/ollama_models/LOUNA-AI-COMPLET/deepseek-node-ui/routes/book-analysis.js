/**
 * Routes pour l'API d'analyse de livres
 */

const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { promisify } = require('util');
const bookAnalyzer = require('../lib/book-analyzer');

// Promisify des fonctions de fs
const readdirAsync = promisify(fs.readdir);
const statAsync = promisify(fs.stat);
const unlinkAsync = promisify(fs.unlink);

// Configuration de multer pour le téléchargement de fichiers
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../data/books');
    
    // Créer le dossier s'il n'existe pas
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // Générer un nom de fichier unique
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const extension = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + extension);
  }
});

// Filtrer les types de fichiers acceptés
const fileFilter = (req, file, cb) => {
  const allowedTypes = ['.pdf', '.epub', '.txt'];
  const extension = path.extname(file.originalname).toLowerCase();
  
  if (allowedTypes.includes(extension)) {
    cb(null, true);
  } else {
    cb(new Error('Type de fichier non pris en charge. Seuls les fichiers PDF, EPUB et TXT sont acceptés.'));
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 50 * 1024 * 1024 // 50 MB
  }
});

/**
 * @route POST /api/books/upload
 * @desc Télécharger et analyser un livre
 * @access Public
 */
router.post('/upload', upload.single('book'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'Aucun fichier n\'a été téléchargé' });
    }
    
    // Analyser le livre
    const filePath = req.file.path;
    const analysis = await bookAnalyzer.analyzeBook(filePath);
    
    return res.json({
      success: true,
      message: 'Livre analysé avec succès',
      bookId: analysis.bookId,
      fileName: analysis.fileName,
      summary: analysis.summary
    });
  } catch (error) {
    console.error('Error uploading and analyzing book:', error);
    return res.status(500).json({ error: 'Erreur lors de l\'analyse du livre', details: error.message });
  }
});

/**
 * @route GET /api/books/analysis/:bookId
 * @desc Récupérer l'analyse d'un livre
 * @access Public
 */
router.get('/analysis/:bookId', async (req, res) => {
  try {
    const { bookId } = req.params;
    
    if (!bookId) {
      return res.status(400).json({ error: 'ID du livre requis' });
    }
    
    // Charger l'analyse
    const analysis = await bookAnalyzer.loadAnalysis(bookId);
    
    return res.json({
      success: true,
      analysis
    });
  } catch (error) {
    console.error('Error loading book analysis:', error);
    return res.status(500).json({ error: 'Erreur lors du chargement de l\'analyse', details: error.message });
  }
});

/**
 * @route GET /api/books/list
 * @desc Lister toutes les analyses de livres
 * @access Public
 */
router.get('/list', async (req, res) => {
  try {
    const analysisDir = path.join(__dirname, '../data/analysis');
    
    // Créer le dossier s'il n'existe pas
    if (!fs.existsSync(analysisDir)) {
      fs.mkdirSync(analysisDir, { recursive: true });
      return res.json({ success: true, analyses: [] });
    }
    
    // Lire les fichiers d'analyse
    const files = await readdirAsync(analysisDir);
    const analyses = [];
    
    for (const file of files) {
      if (path.extname(file) === '.json') {
        try {
          const filePath = path.join(analysisDir, file);
          const stats = await statAsync(filePath);
          const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
          
          analyses.push({
            bookId: data.bookId,
            fileName: data.fileName,
            fileType: data.fileType,
            analysisDate: data.analysisDate,
            fileSize: data.fileSize,
            summary: data.summary.substring(0, 200) + '...'
          });
        } catch (err) {
          console.error(`Error reading analysis file ${file}:`, err);
        }
      }
    }
    
    // Trier par date d'analyse (la plus récente en premier)
    analyses.sort((a, b) => new Date(b.analysisDate) - new Date(a.analysisDate));
    
    return res.json({
      success: true,
      analyses
    });
  } catch (error) {
    console.error('Error listing book analyses:', error);
    return res.status(500).json({ error: 'Erreur lors de la récupération des analyses', details: error.message });
  }
});

/**
 * @route DELETE /api/books/:bookId
 * @desc Supprimer l'analyse d'un livre
 * @access Public
 */
router.delete('/:bookId', async (req, res) => {
  try {
    const { bookId } = req.params;
    
    if (!bookId) {
      return res.status(400).json({ error: 'ID du livre requis' });
    }
    
    const analysisPath = path.join(__dirname, '../data/analysis', `${bookId}.json`);
    
    // Vérifier si le fichier existe
    if (!fs.existsSync(analysisPath)) {
      return res.status(404).json({ error: 'Analyse non trouvée' });
    }
    
    // Supprimer le fichier
    await unlinkAsync(analysisPath);
    
    return res.json({
      success: true,
      message: 'Analyse supprimée avec succès'
    });
  } catch (error) {
    console.error('Error deleting book analysis:', error);
    return res.status(500).json({ error: 'Erreur lors de la suppression de l\'analyse', details: error.message });
  }
});

/**
 * @route POST /api/books/mpc/:bookId
 * @desc Appliquer MPC à un livre déjà analysé
 * @access Public
 */
router.post('/mpc/:bookId', async (req, res) => {
  try {
    const { bookId } = req.params;
    
    if (!bookId) {
      return res.status(400).json({ error: 'ID du livre requis' });
    }
    
    // Charger l'analyse existante
    const analysis = await bookAnalyzer.loadAnalysis(bookId);
    
    // Extraire le texte du livre
    const bookPath = path.join(__dirname, '../data/books', analysis.fileName);
    let text;
    
    if (fs.existsSync(bookPath)) {
      text = await bookAnalyzer.extractTextFromBook(bookPath);
    } else {
      return res.status(404).json({ error: 'Fichier du livre non trouvé' });
    }
    
    // Appliquer MPC
    const mpcResults = bookAnalyzer.applyMPC(text);
    
    // Mettre à jour l'analyse
    analysis.mpcResults = mpcResults;
    analysis.mpcDate = new Date().toISOString();
    
    // Sauvegarder l'analyse mise à jour
    const analysisPath = path.join(__dirname, '../data/analysis', `${bookId}.json`);
    fs.writeFileSync(analysisPath, JSON.stringify(analysis, null, 2));
    
    return res.json({
      success: true,
      message: 'MPC appliqué avec succès',
      mpcResults
    });
  } catch (error) {
    console.error('Error applying MPC:', error);
    return res.status(500).json({ error: 'Erreur lors de l\'application de MPC', details: error.message });
  }
});

module.exports = router;
