/**
 * Routes pour l'API de mémoire thermique
 */

const express = require('express');
const router = express.Router();
const { getThermalMemory } = require('../lib/memory/thermal_memory');
const { getOllamaIntegration } = require('../lib/memory/ollama_integration');

// Obtenir l'instance de la mémoire thermique
const thermalMemory = getThermalMemory();

// Obtenir l'instance de l'intégration Ollama
const ollamaIntegration = getOllamaIntegration({
  useMemory: true,
  maxContextItems: 5,
  memoryImportance: 0.7
});

/**
 * @route GET /api/memory/data
 * @desc Récupérer toutes les données de mémoire
 * @access Public
 */
router.get('/data', (req, res) => {
  try {
    // Récupérer les données de mémoire
    const memoryData = {
      instantMemory: thermalMemory.instantMemory,
      shortTerm: thermalMemory.shortTerm,
      workingMemory: thermalMemory.workingMemory,
      mediumTerm: thermalMemory.mediumTerm,
      longTerm: thermalMemory.longTerm,
      dreamMemory: thermalMemory.dreamMemory,
      stats: thermalMemory.getStats(),
      kyber: thermalMemory.kyber
    };

    return res.json({
      success: true,
      memory: memoryData
    });
  } catch (error) {
    console.error('Error getting memory data:', error);
    return res.status(500).json({ error: 'Erreur lors de la récupération des données de mémoire' });
  }
});

/**
 * @route GET /api/memory/stats
 * @desc Récupérer les statistiques de la mémoire
 * @access Public
 */
router.get('/stats', (req, res) => {
  try {
    const stats = thermalMemory.getStats();

    return res.json({
      success: true,
      stats
    });
  } catch (error) {
    console.error('Error getting memory stats:', error);
    return res.status(500).json({ error: 'Erreur lors de la récupération des statistiques de mémoire' });
  }
});

/**
 * @route GET /api/memory/brain
 * @desc Récupérer l'état du cerveau
 * @access Public
 */
router.get('/brain', (req, res) => {
  try {
    const brainState = ollamaIntegration.getBrainState();

    return res.json({
      success: true,
      brainState
    });
  } catch (error) {
    console.error('Error getting brain state:', error);
    return res.status(500).json({ error: 'Erreur lors de la récupération de l\'état du cerveau' });
  }
});

/**
 * @route GET /api/memory/search
 * @desc Rechercher dans la mémoire
 * @access Public
 */
router.get('/search', (req, res) => {
  try {
    const { query } = req.query;

    if (!query) {
      return res.status(400).json({ error: 'Requête de recherche requise' });
    }

    // Rechercher dans la mémoire
    const results = thermalMemory.retrieve(query, 10);

    return res.json({
      success: true,
      results
    });
  } catch (error) {
    console.error('Error searching memory:', error);
    return res.status(500).json({ error: 'Erreur lors de la recherche dans la mémoire' });
  }
});

/**
 * @route POST /api/memory/add
 * @desc Ajouter une information à la mémoire
 * @access Public
 */
router.post('/add', (req, res) => {
  try {
    const { key, data, importance, category } = req.body;

    if (!key || !data) {
      return res.status(400).json({ error: 'Clé et données requises' });
    }

    // Ajouter à la mémoire
    const id = thermalMemory.add(key, data, importance, category);

    return res.json({
      success: true,
      message: 'Information ajoutée à la mémoire',
      id
    });
  } catch (error) {
    console.error('Error adding to memory:', error);
    return res.status(500).json({ error: 'Erreur lors de l\'ajout à la mémoire' });
  }
});

/**
 * @route GET /api/memory/item/:id
 * @desc Récupérer un élément de mémoire
 * @access Public
 */
router.get('/item/:id', (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({ error: 'ID requis' });
    }

    // Récupérer l'élément
    const item = thermalMemory.get(id);

    if (!item) {
      return res.status(404).json({ error: 'Élément non trouvé' });
    }

    return res.json({
      success: true,
      item
    });
  } catch (error) {
    console.error('Error getting memory item:', error);
    return res.status(500).json({ error: 'Erreur lors de la récupération de l\'élément de mémoire' });
  }
});

/**
 * @route DELETE /api/memory/item/:id
 * @desc Supprimer un élément de mémoire
 * @access Public
 */
router.delete('/item/:id', (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({ error: 'ID requis' });
    }

    // Trouver l'élément dans tous les niveaux de mémoire
    let found = false;

    // Vérifier dans la mémoire instantanée
    if (thermalMemory.instantMemory[id]) {
      delete thermalMemory.instantMemory[id];
      found = true;
    }

    // Vérifier dans la mémoire à court terme
    if (thermalMemory.shortTerm[id]) {
      delete thermalMemory.shortTerm[id];
      found = true;
    }

    // Vérifier dans la mémoire de travail
    if (thermalMemory.workingMemory[id]) {
      delete thermalMemory.workingMemory[id];
      found = true;
    }

    // Vérifier dans la mémoire à moyen terme
    if (thermalMemory.mediumTerm[id]) {
      delete thermalMemory.mediumTerm[id];
      found = true;
    }

    // Vérifier dans la mémoire à long terme
    if (thermalMemory.longTerm[id]) {
      delete thermalMemory.longTerm[id];
      found = true;
    }

    // Vérifier dans la mémoire des rêves
    if (thermalMemory.dreamMemory[id]) {
      delete thermalMemory.dreamMemory[id];
      found = true;
    }

    if (!found) {
      return res.status(404).json({ error: 'Élément non trouvé' });
    }

    // Mettre à jour les statistiques
    thermalMemory.updateStats();

    // Sauvegarder la mémoire
    thermalMemory.saveMemory();

    return res.json({
      success: true,
      message: 'Élément supprimé avec succès'
    });
  } catch (error) {
    console.error('Error deleting memory item:', error);
    return res.status(500).json({ error: 'Erreur lors de la suppression de l\'élément de mémoire' });
  }
});

/**
 * @route POST /api/memory/cycle
 * @desc Forcer un cycle de mémoire
 * @access Public
 */
router.post('/cycle', async (req, res) => {
  try {
    await thermalMemory.performMemoryCycle();

    return res.json({
      success: true,
      message: 'Cycle de mémoire exécuté avec succès'
    });
  } catch (error) {
    console.error('Error performing memory cycle:', error);
    return res.status(500).json({ error: 'Erreur lors de l\'exécution du cycle de mémoire' });
  }
});

/**
 * @route POST /api/memory/dream
 * @desc Générer un rêve
 * @access Public
 */
router.post('/dream', (req, res) => {
  try {
    thermalMemory.generateDream();

    return res.json({
      success: true,
      message: 'Rêve généré avec succès'
    });
  } catch (error) {
    console.error('Error generating dream:', error);
    return res.status(500).json({ error: 'Erreur lors de la génération du rêve' });
  }
});

/**
 * @route POST /api/memory/clear
 * @desc Effacer toute la mémoire
 * @access Public
 */
router.post('/clear', async (req, res) => {
  try {
    // Réinitialiser tous les niveaux de mémoire
    thermalMemory.instantMemory = {};
    thermalMemory.shortTerm = {};
    thermalMemory.workingMemory = {};
    thermalMemory.mediumTerm = {};
    thermalMemory.longTerm = {};
    thermalMemory.dreamMemory = {};

    // Mettre à jour les statistiques
    thermalMemory.updateStats();

    // Sauvegarder la mémoire
    await thermalMemory.saveMemory();

    return res.json({
      success: true,
      message: 'Mémoire effacée avec succès'
    });
  } catch (error) {
    console.error('Error clearing memory:', error);
    return res.status(500).json({ error: 'Erreur lors de l\'effacement de la mémoire' });
  }
});

/**
 * @route GET /api/memory/kyber
 * @desc Obtenir l'état de l'accélérateur Kyber
 * @access Public
 */
router.get('/kyber', (req, res) => {
  try {
    const kyberState = thermalMemory.getKyberState();

    return res.json({
      success: true,
      kyberState
    });
  } catch (error) {
    console.error('Error getting Kyber state:', error);
    return res.status(500).json({ error: 'Erreur lors de la récupération de l\'état de Kyber' });
  }
});

/**
 * @route POST /api/memory/kyber/enable
 * @desc Activer l'accélérateur Kyber
 * @access Public
 */
router.post('/kyber/enable', (req, res) => {
  try {
    const success = thermalMemory.enableKyberAccelerator();

    return res.json({
      success,
      message: success ? 'Accélérateur Kyber activé' : 'Impossible d\'activer l\'accélérateur Kyber'
    });
  } catch (error) {
    console.error('Error enabling Kyber accelerator:', error);
    return res.status(500).json({ error: 'Erreur lors de l\'activation de l\'accélérateur Kyber' });
  }
});

/**
 * @route POST /api/memory/kyber/disable
 * @desc Désactiver l'accélérateur Kyber
 * @access Public
 */
router.post('/kyber/disable', (req, res) => {
  try {
    const success = thermalMemory.disableKyberAccelerator();

    return res.json({
      success,
      message: success ? 'Accélérateur Kyber désactivé' : 'Impossible de désactiver l\'accélérateur Kyber'
    });
  } catch (error) {
    console.error('Error disabling Kyber accelerator:', error);
    return res.status(500).json({ error: 'Erreur lors de la désactivation de l\'accélérateur Kyber' });
  }
});

/**
 * @route POST /api/memory/kyber/configure
 * @desc Configurer l'accélérateur Kyber
 * @access Public
 */
router.post('/kyber/configure', (req, res) => {
  try {
    const { boostFactor, temperature, stability, enabled } = req.body;

    const config = {};
    if (boostFactor !== undefined) config.boostFactor = parseFloat(boostFactor);
    if (temperature !== undefined) config.temperature = parseFloat(temperature);
    if (stability !== undefined) config.stability = parseFloat(stability);
    if (enabled !== undefined) config.enabled = enabled === true || enabled === 'true';

    const success = thermalMemory.configureKyberAccelerator(config);

    return res.json({
      success,
      message: success ? 'Configuration de l\'accélérateur Kyber mise à jour' : 'Impossible de configurer l\'accélérateur Kyber',
      kyberState: thermalMemory.getKyberState()
    });
  } catch (error) {
    console.error('Error configuring Kyber accelerator:', error);
    return res.status(500).json({ error: 'Erreur lors de la configuration de l\'accélérateur Kyber' });
  }
});

/**
 * @route POST /api/memory/kyber/lock
 * @desc Verrouiller ou déverrouiller l'accélérateur Kyber
 * @access Public
 */
router.post('/kyber/lock', (req, res) => {
  try {
    const { locked } = req.body;

    if (locked === undefined) {
      return res.status(400).json({ error: 'Paramètre "locked" requis' });
    }

    const lockState = locked === true || locked === 'true';
    const success = thermalMemory.setKyberLock(lockState);

    return res.json({
      success,
      message: success ? `Accélérateur Kyber ${lockState ? 'verrouillé' : 'déverrouillé'}` : 'Impossible de modifier l\'état de verrouillage',
      kyberState: thermalMemory.getKyberState()
    });
  } catch (error) {
    console.error('Error setting Kyber lock state:', error);
    return res.status(500).json({ error: 'Erreur lors de la modification de l\'état de verrouillage' });
  }
});

/**
 * @route POST /api/memory/chat
 * @desc Envoyer un message à l'agent avec mémoire
 * @access Public
 */
router.post('/chat', async (req, res) => {
  try {
    const { message, history, modelName } = req.body;

    if (!message) {
      return res.status(400).json({ error: 'Message requis' });
    }

    // Appeler l'API Ollama avec la mémoire
    const response = await ollamaIntegration.callOllamaApi(message, history || [], modelName);

    return res.json(response);
  } catch (error) {
    console.error('Error in memory chat:', error);
    return res.status(500).json({ error: 'Erreur lors de la communication avec l\'agent' });
  }
});

module.exports = router;
