/**
 * Routes pour la sécurité Luna
 * Version temporaire pour permettre le démarrage de Luna
 */

const express = require('express');
const router = express.Router();

// Route pour la page de sécurité
router.get('/security', (req, res) => {
  res.render('luna-base', {
    page: 'luna-security',
    title: 'Luna - Sécurité',
    path: '/luna/security',
    systemStatus: {
      active: true,
      version: '1.0.0'
    }
  });
});

// Route API pour obtenir le statut de sécurité
router.get('/api/security/status', (req, res) => {
  res.json({
    success: true,
    securityStatus: {
      active: true,
      firewallActive: true,
      vpnActive: false,
      encryptionActive: true,
      lastScan: new Date().toISOString(),
      threatLevel: 'low'
    }
  });
});

// Fonction d'initialisation du routeur de sécurité
function initSecurityRouter(options = {}) {
  const socketIo = options.socketIo;
  
  // Initialiser les gestionnaires de socket
  const initSocketHandlers = (io) => {
    if (!io) return;
    
    io.on('connection', (socket) => {
      // Gérer les événements liés à la sécurité
      socket.on('security:scan', () => {
        // Simuler un scan de sécurité
        setTimeout(() => {
          socket.emit('security:scan:result', {
            success: true,
            threats: [],
            scanTime: new Date().toISOString()
          });
        }, 2000);
      });
    });
  };
  
  return {
    router,
    initSocketHandlers
  };
}

module.exports = {
  router,
  initSecurityRouter
};