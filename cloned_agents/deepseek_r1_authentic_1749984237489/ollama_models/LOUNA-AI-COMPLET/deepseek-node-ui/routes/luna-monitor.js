/**
 * Routes pour la surveillance du système Luna
 * Version temporaire pour permettre le démarrage de Luna
 */

const express = require('express');
const router = express.Router();
const os = require('os');

// Route pour la page de surveillance du système
router.get('/monitor', (req, res) => {
  res.render('luna-base', {
    page: 'luna-monitor',
    title: 'Luna - Surveillance du Système',
    path: '/luna/monitor',
    systemStatus: {
      active: true,
      version: '1.0.0'
    }
  });
});

// Route API pour obtenir les statistiques système
router.get('/api/monitor/stats', (req, res) => {
  // Obtenir les informations système de base
  const totalMem = os.totalmem();
  const freeMem = os.freemem();
  const usedMem = totalMem - freeMem;
  const memPercent = Math.round((usedMem / totalMem) * 100);
  
  const cpuCount = os.cpus().length;
  const loadAvg = os.loadavg();
  const cpuPercent = Math.round((loadAvg[0] / cpuCount) * 100);
  
  res.json({
    success: true,
    stats: {
      memory: {
        total: totalMem,
        used: usedMem,
        free: freeMem,
        percent: memPercent
      },
      cpu: {
        count: cpuCount,
        loadAvg: loadAvg,
        percent: cpuPercent
      },
      uptime: os.uptime(),
      platform: os.platform(),
      hostname: os.hostname()
    }
  });
});

// Fonction d'initialisation du routeur de surveillance
function initMonitorRouter(options = {}) {
  const socketIo = options.socketIo;
  
  // Initialiser les gestionnaires de socket
  const initSocketHandlers = (io) => {
    if (!io) return;
    
    // Envoyer des mises à jour périodiques des statistiques système
    const statsInterval = setInterval(() => {
      const totalMem = os.totalmem();
      const freeMem = os.freemem();
      const usedMem = totalMem - freeMem;
      const memPercent = Math.round((usedMem / totalMem) * 100);
      
      const cpuCount = os.cpus().length;
      const loadAvg = os.loadavg();
      const cpuPercent = Math.round((loadAvg[0] / cpuCount) * 100);
      
      io.emit('monitor:stats:update', {
        memory: {
          total: totalMem,
          used: usedMem,
          free: freeMem,
          percent: memPercent
        },
        cpu: {
          count: cpuCount,
          loadAvg: loadAvg,
          percent: cpuPercent
        },
        uptime: os.uptime(),
        timestamp: new Date().toISOString()
      });
    }, 5000);
    
    // Nettoyer l'intervalle lorsque le serveur est arrêté
    process.on('SIGINT', () => {
      clearInterval(statsInterval);
    });
  };
  
  return {
    router,
    initSocketHandlers
  };
}

module.exports = {
  router,
  initMonitorRouter
};