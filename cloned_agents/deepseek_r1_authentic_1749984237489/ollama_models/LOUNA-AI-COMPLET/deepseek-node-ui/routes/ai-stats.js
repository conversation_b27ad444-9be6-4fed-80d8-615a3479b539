/**
 * Routes pour les statistiques d'évolution de l'IA
 */

const express = require('express');
const router = express.Router();

// Référence à la mémoire thermique et au réseau neuronal
let thermalMemory;
let neuralNetwork;

// Initialiser les références
const init = (memoryInstance) => {
  thermalMemory = memoryInstance;
  neuralNetwork = memoryInstance.neuralNetwork;
};

// Statistiques de base de l'IA
let aiStats = {
  // Coefficient intellectuel (QI) - commence à 100 et évolue avec l'apprentissage
  iq: 100,

  // Nombre de neurones - commence à 1000 et augmente avec chaque session de formation
  neurons: 1000,

  // Vitesse d'apprentissage (pourcentage)
  learningSpeed: 0,

  // Temps d'évolution total (en heures)
  evolutionTime: 0,

  // Niveau d'évolution (de 1 à 10)
  evolutionLevel: 1,

  // Progression vers le niveau suivant (pourcentage)
  levelProgress: 0,

  // Date de la dernière mise à jour
  lastUpdate: new Date().toISOString(),

  // Historique des évolutions
  history: []
};

// Charger les statistiques depuis le stockage (simulé pour l'instant)
const loadStats = () => {
  try {
    // Dans une implémentation réelle, on chargerait depuis un fichier ou une base de données
    // Pour l'instant, on utilise des valeurs simulées

    // Si le réseau neuronal est disponible, utiliser ses données
    if (neuralNetwork) {
      const generation = neuralNetwork.generation || 1;
      const connections = neuralNetwork.connections ? Object.keys(neuralNetwork.connections).length : 0;

      // Calculer le QI en fonction de la génération et des connexions
      aiStats.iq = 100 + (generation * 5) + (connections / 10);

      // Calculer le nombre de neurones
      aiStats.neurons = 1000 + (generation * 100) + connections;

      // Niveau d'évolution basé sur la génération
      aiStats.evolutionLevel = Math.min(10, Math.max(1, Math.floor(generation / 5) + 1));

      // Progression vers le niveau suivant
      const levelProgress = (generation % 5) / 5 * 100;
      aiStats.levelProgress = Math.round(levelProgress);
    }

    // Calculer le temps d'évolution en fonction des sessions de formation
    if (thermalMemory) {
      const trainingSessions = thermalMemory.getMemories ?
        thermalMemory.getMemories().filter(m => m.type === 'training_session') :
        [];

      // Temps d'évolution basé sur le nombre de sessions (1 heure par 10 sessions)
      aiStats.evolutionTime = Math.round(trainingSessions.length / 10 * 100) / 100;

      // Vitesse d'apprentissage basée sur les sessions récentes
      const recentSessions = trainingSessions.filter(s => {
        const sessionDate = new Date(s.timestamp);
        const now = new Date();
        const diffTime = Math.abs(now - sessionDate);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays <= 7; // Sessions des 7 derniers jours
      });

      aiStats.learningSpeed = Math.min(100, recentSessions.length * 5);

      // Mettre à jour l'historique
      if (trainingSessions.length > 0) {
        // Grouper les sessions par jour
        const sessionsByDay = {};
        trainingSessions.forEach(session => {
          const day = new Date(session.timestamp).toISOString().split('T')[0];
          if (!sessionsByDay[day]) {
            sessionsByDay[day] = 0;
          }
          sessionsByDay[day]++;
        });

        // Convertir en tableau pour l'historique
        aiStats.history = Object.keys(sessionsByDay).map(day => ({
          date: day,
          sessions: sessionsByDay[day],
          iqGain: sessionsByDay[day] * 0.5
        })).sort((a, b) => new Date(b.date) - new Date(a.date)).slice(0, 30); // 30 derniers jours
      }
    }

    // Mettre à jour la date de dernière mise à jour
    aiStats.lastUpdate = new Date().toISOString();

    return aiStats;
  } catch (error) {
    console.error('Erreur lors du chargement des statistiques de l\'IA:', error);
    return aiStats;
  }
};

// Route pour obtenir les statistiques de l'IA
router.get('/ai/stats', (req, res) => {
  try {
    // Charger les statistiques à jour
    const stats = loadStats();

    res.json({
      success: true,
      stats
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques de l\'IA:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Route pour mettre à jour les statistiques après une session de formation
router.post('/ai/training-complete', (req, res) => {
  try {
    const { sessionCount = 1, accelerated = false, speedMultiplier = 1.0 } = req.body;

    // Calculer le multiplicateur d'apprentissage
    const learningMultiplier = accelerated ? speedMultiplier : 1.0;
    console.log(`Mise à jour des statistiques avec ${sessionCount} session(s)${accelerated ? ` (accélérée x${speedMultiplier.toFixed(1)})` : ''}`);

    // Mettre à jour les statistiques avec le multiplicateur d'accélération
    aiStats.iq += sessionCount * 0.5 * learningMultiplier;
    aiStats.neurons += sessionCount * 10 * learningMultiplier;
    aiStats.learningSpeed = Math.min(100, aiStats.learningSpeed + sessionCount * 2 * learningMultiplier);
    aiStats.evolutionTime += sessionCount * 0.1;

    // Mettre à jour la progression de niveau
    aiStats.levelProgress += sessionCount * 2 * learningMultiplier;
    if (aiStats.levelProgress >= 100) {
      aiStats.evolutionLevel = Math.min(10, aiStats.evolutionLevel + 1);
      aiStats.levelProgress = aiStats.levelProgress - 100;
    }

    // Mettre à jour la date de dernière mise à jour
    aiStats.lastUpdate = new Date().toISOString();

    // Ajouter à l'historique
    const today = new Date().toISOString().split('T')[0];
    const existingEntry = aiStats.history.find(entry => entry.date === today);

    if (existingEntry) {
      existingEntry.sessions += sessionCount;
      existingEntry.iqGain += sessionCount * 0.5 * learningMultiplier;
      if (accelerated) {
        existingEntry.accelerated = true;
        existingEntry.speedMultiplier = Math.max(existingEntry.speedMultiplier || 1.0, speedMultiplier);
      }
    } else {
      aiStats.history.unshift({
        date: today,
        sessions: sessionCount,
        iqGain: sessionCount * 0.5 * learningMultiplier,
        accelerated: accelerated,
        speedMultiplier: accelerated ? speedMultiplier : 1.0
      });
    }

    // Limiter l'historique aux 30 derniers jours
    aiStats.history = aiStats.history.slice(0, 30);

    res.json({
      success: true,
      stats: aiStats
    });
  } catch (error) {
    console.error('Erreur lors de la mise à jour des statistiques de l\'IA:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = { router, init };
