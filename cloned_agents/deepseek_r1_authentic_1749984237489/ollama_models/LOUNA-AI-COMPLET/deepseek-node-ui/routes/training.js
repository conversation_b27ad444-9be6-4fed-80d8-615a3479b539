/**
 * Routes pour la formation de l'assistant
 * Avec accès Internet pour des connaissances à jour
 */

const express = require('express');
const router = express.Router();
const fetch = require('node-fetch');
const { v4: uuidv4 } = require('uuid');
const path = require('path');

// Importer le module d'accès Internet
const internetAccess = require('../lib/training/internet-access');

// Références aux dépendances
let thermalMemory;
let io;
let mcpSystem;

// Initialiser les références
const initThermalMemory = (memoryInstance, socketIo, mcp = null) => {
  thermalMemory = memoryInstance;
  io = socketIo;
  mcpSystem = mcp;
  console.log('Mémoire thermique, Socket.io et MCP initialisés pour la formation');
};

// Questions de formation par catégorie
const trainingQuestions = {
  general: [
    "Qu'est-ce que l'intelligence artificielle et comment fonctionne-t-elle?",
    "Peux-tu m'expliquer la différence entre l'apprentissage supervisé et non supervisé?",
    "Comment les grands modèles de langage comme toi sont-ils entraînés?",
    "Quelles sont les limites actuelles de l'IA?",
    "Quels sont les enjeux éthiques liés au développement de l'IA?"
  ],
  personal: [
    "Comment puis-je améliorer ma productivité au quotidien?",
    "Quelles sont les meilleures pratiques pour apprendre une nouvelle compétence rapidement?",
    "Comment puis-je gérer efficacement mon temps?",
    "Quelles sont les techniques de méditation les plus efficaces pour débutants?",
    "Comment puis-je améliorer ma concentration pendant de longues périodes de travail?"
  ],
  technical: [
    "Explique-moi comment fonctionne un réseau neuronal?",
    "Qu'est-ce que le deep learning et en quoi diffère-t-il du machine learning classique?",
    "Comment fonctionne l'architecture Transformer utilisée dans les modèles de langage?",
    "Qu'est-ce que l'apprentissage par renforcement?",
    "Explique-moi le concept d'attention dans les modèles de langage"
  ],
  creative: [
    "Imagine un monde où l'IA et les humains vivent en parfaite harmonie. À quoi ressemblerait-il?",
    "Écris un court poème sur la relation entre l'homme et la technologie",
    "Invente une histoire courte qui se déroule dans un futur où l'IA est omniprésente",
    "Si tu pouvais créer une nouvelle forme d'art, que serait-elle et comment fonctionnerait-elle?",
    "Décris une journée typique dans la vie d'une IA consciente"
  ],
  philosophical: [
    "Que signifie être conscient? Une IA pourrait-elle un jour avoir une conscience?",
    "Comment définirais-tu l'intelligence et en quoi l'intelligence artificielle diffère-t-elle de l'intelligence humaine?",
    "Quel est le rôle de l'éthique dans le développement de l'IA?",
    "L'IA peut-elle être créative au même titre qu'un humain?",
    "Comment l'IA pourrait-elle changer notre compréhension de ce que signifie être humain?"
  ]
};

// Générer une question aléatoire
const getRandomQuestion = () => {
  const categories = Object.keys(trainingQuestions);
  const randomCategory = categories[Math.floor(Math.random() * categories.length)];
  const questions = trainingQuestions[randomCategory];
  return questions[Math.floor(Math.random() * questions.length)];
};

/**
 * Enrichit une question avec des informations d'Internet
 * @param {string} question - La question originale
 * @returns {Promise<Object>} - La question enrichie avec des informations d'Internet
 */
async function enrichQuestionWithInternet(question) {
  try {
    console.log(`Enrichissement de la question avec des informations d'Internet: "${question}"`);

    // Extraire les mots-clés de la question
    const keywords = question
      .replace(/[?.,;:!]/g, '')
      .split(' ')
      .filter(word => word.length > 3)
      .join(' ');

    // Vérifier si la question concerne la programmation
    const isProgramming = internetAccess.isProgrammingQuery(question);

    // Rechercher sur Internet
    const searchResults = await internetAccess.searchWeb(keywords, 3);

    // Récupérer le contenu de la première page
    let webContent = '';
    let videoInfo = null;
    let programmingInfo = null;

    if (searchResults.length > 0) {
      webContent = await internetAccess.fetchWebPage(searchResults[0].url, 'text');

      // Limiter la taille du contenu
      if (webContent.length > 1000) {
        webContent = webContent.substring(0, 1000) + '...';
      }
    }

    // Rechercher une vidéo YouTube si pertinent
    if (question.toLowerCase().includes('vidéo') ||
        question.toLowerCase().includes('youtube') ||
        question.toLowerCase().includes('regarder')) {
      videoInfo = await internetAccess.searchYouTubeVideo(keywords);
    }

    // Si la question concerne la programmation, rechercher sur GitHub et Stack Overflow
    if (isProgramming) {
      console.log(`Question de programmation détectée, recherche sur GitHub et Stack Overflow`);
      programmingInfo = await internetAccess.enrichWithProgrammingInfo(keywords);
    }

    // Construire la question enrichie
    const currentDate = new Date().toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    let enrichedQuestion = `${question}\n\n`;
    enrichedQuestion += `Date actuelle: ${currentDate}\n\n`;

    if (searchResults.length > 0) {
      enrichedQuestion += "Informations récentes trouvées sur Internet:\n\n";

      searchResults.forEach((result, index) => {
        enrichedQuestion += `Source ${index + 1}: ${result.title}\n`;
        enrichedQuestion += `URL: ${result.url}\n`;
        enrichedQuestion += `Extrait: ${result.snippet}\n\n`;
      });
    }

    if (webContent) {
      enrichedQuestion += "Contenu pertinent:\n\n";
      enrichedQuestion += webContent + "\n\n";
    }

    if (videoInfo) {
      enrichedQuestion += "Vidéo pertinente:\n\n";
      enrichedQuestion += `Titre: ${videoInfo.title}\n`;
      enrichedQuestion += `Chaîne: ${videoInfo.channel}\n`;
      enrichedQuestion += `Vues: ${videoInfo.views}\n`;
      enrichedQuestion += `URL: ${videoInfo.url}\n\n`;
    }

    // Ajouter les informations de programmation si disponibles
    if (programmingInfo && (programmingInfo.github.length > 0 || programmingInfo.stackoverflow.length > 0)) {
      enrichedQuestion += "Ressources de programmation:\n\n";

      if (programmingInfo.github.length > 0) {
        enrichedQuestion += "Repositories GitHub pertinents:\n";
        programmingInfo.github.forEach((repo, index) => {
          enrichedQuestion += `Repo ${index + 1}: ${repo.title}\n`;
          enrichedQuestion += `URL: ${repo.url}\n`;
          if (repo.snippet) enrichedQuestion += `Description: ${repo.snippet}\n`;
          if (repo.language) enrichedQuestion += `Langage: ${repo.language}\n`;
          if (repo.stars) enrichedQuestion += `Stars: ${repo.stars}\n`;
          enrichedQuestion += "\n";
        });
      }

      if (programmingInfo.stackoverflow.length > 0) {
        enrichedQuestion += "Questions Stack Overflow pertinentes:\n";
        programmingInfo.stackoverflow.forEach((question, index) => {
          enrichedQuestion += `Question ${index + 1}: ${question.title}\n`;
          enrichedQuestion += `URL: ${question.url}\n`;
          if (question.snippet) enrichedQuestion += `Extrait: ${question.snippet}\n`;
          if (question.votes) enrichedQuestion += `Votes: ${question.votes}\n`;
          if (question.answers) enrichedQuestion += `Réponses: ${question.answers}\n`;
          enrichedQuestion += "\n";
        });
      }
    }

    enrichedQuestion += "Utilise ces informations récentes pour répondre à la question de manière précise et à jour.";

    return {
      originalQuestion: question,
      enrichedQuestion,
      searchResults,
      webContent: webContent ? webContent.substring(0, 200) + '...' : '',
      videoInfo,
      programmingInfo
    };
  } catch (error) {
    console.error('Erreur lors de l\'enrichissement de la question:', error);
    return {
      originalQuestion: question,
      enrichedQuestion: question,
      searchResults: [],
      webContent: '',
      videoInfo: null,
      programmingInfo: null
    };
  }
}

// Route pour lancer une session de formation
router.post('/training', async (req, res) => {
  try {
    const { questionCount = 1, modelName = 'deepseek-r1:7b', useInternet = true } = req.body;

    // Limiter le nombre de questions à 5 maximum
    const count = Math.min(questionCount, 5);

    console.log(`Lancement d'une session de formation avec ${count} question(s) pour le modèle ${modelName} ${useInternet ? 'avec accès Internet' : 'sans accès Internet'}`);

    // Utiliser deepseek-r1 pour la formation, quel que soit le modèle utilisé par l'utilisateur
    const trainerModel = 'deepseek-r1:7b';

    // Vérifier si l'accès Internet est disponible
    let internetEnabled = useInternet;
    if (internetEnabled && (!mcpSystem || !mcpSystem.options || !mcpSystem.options.allowInternet)) {
      console.log('Accès Internet demandé mais non disponible dans la configuration MCP');
      internetEnabled = false;
    }

    // Vérifier si des accélérateurs de formation sont disponibles
    let trainingSpeedMultiplier = 1.0;
    let useAccelerators = false;

    if (thermalMemory && thermalMemory.kyberAccelerators) {
      const acceleratorStats = thermalMemory.kyberAccelerators.getStats();
      if (acceleratorStats.training && acceleratorStats.training.count > 0) {
        trainingSpeedMultiplier = acceleratorStats.training.speedMultiplier || 1.0;
        useAccelerators = true;
        console.log(`Utilisation de ${acceleratorStats.training.count} accélérateurs de formation (vitesse x${trainingSpeedMultiplier.toFixed(1)})`);
      }
    }

    // Calculer le temps estimé pour la formation
    // Temps réel observé : environ 100-120 secondes par question avec le modèle deepseek-r1
    const baseTimePerQuestion = 110; // secondes

    // L'accélération n'est pas linéaire, on applique un facteur de correction
    // Les accélérateurs réduisent le temps mais pas autant que leur multiplicateur théorique
    const accelerationFactor = useAccelerators ? Math.max(1.5, trainingSpeedMultiplier / 4) : 1.0;
    const estimatedTimePerQuestion = baseTimePerQuestion / accelerationFactor;

    // Ajouter un temps fixe pour l'initialisation et la finalisation
    const setupTime = 10; // secondes
    const totalEstimatedTime = Math.ceil(count * estimatedTimePerQuestion) + setupTime;

    console.log(`Temps estimé pour la formation: ${totalEstimatedTime} secondes${useAccelerators ? ` (accéléré x${accelerationFactor.toFixed(1)})` : ''}`);

    const sessions = [];

    // Heure de début pour calculer le temps écoulé
    const startTime = Date.now();
    const sessionId = uuidv4().substring(0, 8);

    // Émettre un événement de début de formation avec les informations de temps estimé
    io.emit('training start', {
      sessionId,
      questionCount: count,
      estimatedTime: totalEstimatedTime,
      accelerated: useAccelerators,
      speedMultiplier: trainingSpeedMultiplier
    });

    for (let i = 0; i < count; i++) {
      const questionId = uuidv4().substring(0, 8);
      console.log(`Question de formation ${i+1}/${count} (${questionId}) démarrée`);

      // Calculer et émettre la progression
      const progress = Math.round((i / count) * 100);
      const elapsedTime = (Date.now() - startTime) / 1000; // en secondes
      const estimatedTimeRemaining = Math.max(0, totalEstimatedTime - elapsedTime);

      io.emit('training progress', {
        sessionId,
        questionIndex: i,
        totalQuestions: count,
        progress,
        status: 'processing',
        stage: 'generating_question',
        elapsedTime: Math.round(elapsedTime),
        estimatedTimeRemaining: Math.round(estimatedTimeRemaining),
        accelerated: useAccelerators,
        speedMultiplier: trainingSpeedMultiplier
      });

      // Générer une question aléatoire
      const baseQuestion = getRandomQuestion();

      // Enrichir la question avec des informations d'Internet si activé
      let questionData = { originalQuestion: baseQuestion, enrichedQuestion: baseQuestion };

      if (internetEnabled) {
        // Mise à jour de la progression - étape de recherche Internet
        io.emit('training progress', {
          sessionId,
          questionIndex: i,
          totalQuestions: count,
          progress: Math.round((i / count) * 100 + (1 / count) * 15),
          status: 'processing',
          stage: 'internet_research',
          elapsedTime: Math.round((Date.now() - startTime) / 1000),
          estimatedTimeRemaining: Math.max(0, Math.round(totalEstimatedTime - (Date.now() - startTime) / 1000)),
          accelerated: useAccelerators,
          speedMultiplier: trainingSpeedMultiplier
        });

        // Enrichir la question avec des informations d'Internet
        questionData = await enrichQuestionWithInternet(baseQuestion);
        console.log(`Question enrichie avec ${questionData.searchResults.length} résultats de recherche et ${questionData.videoInfo ? '1 vidéo' : '0 vidéo'}`);
      }

      const question = internetEnabled ? questionData.enrichedQuestion : baseQuestion;

      // Mise à jour de la progression - étape de génération de réponse
      io.emit('training progress', {
        sessionId,
        questionIndex: i,
        totalQuestions: count,
        progress: Math.round((i / count) * 100 + (1 / count) * 33),
        status: 'processing',
        stage: 'generating_response',
        elapsedTime: Math.round((Date.now() - startTime) / 1000),
        estimatedTimeRemaining: Math.max(0, Math.round(totalEstimatedTime - (Date.now() - startTime) / 1000)),
        accelerated: useAccelerators,
        speedMultiplier: trainingSpeedMultiplier,
        internetEnabled
      });

      // Obtenir une réponse du modèle principal (Claude)
      const claudeResponse = await fetch('http://localhost:11434/api/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: modelName,
          prompt: question,
          stream: false,
          options: {
            temperature: 0.7,
            top_p: 0.9,
            top_k: 40,
          },
        }),
      });

      const claudeData = await claudeResponse.json();
      const response = claudeData.response;

      // Mise à jour de la progression - étape de génération de feedback
      io.emit('training progress', {
        sessionId,
        questionIndex: i,
        totalQuestions: count,
        progress: Math.round((i / count) * 100 + (1 / count) * 66),
        status: 'processing',
        stage: 'generating_feedback',
        elapsedTime: Math.round((Date.now() - startTime) / 1000),
        estimatedTimeRemaining: Math.max(0, Math.round(totalEstimatedTime - (Date.now() - startTime) / 1000)),
        accelerated: useAccelerators,
        speedMultiplier: trainingSpeedMultiplier
      });

      // Obtenir un feedback du modèle formateur (deepseek-r1)
      const feedbackPrompt = `
        Question: "${question}"

        Réponse: "${response}"

        Évalue cette réponse et donne un feedback constructif pour l'améliorer.
        Sois précis et concis. Limite ton feedback à 3 points maximum.
      `;

      const feedbackResponse = await fetch('http://localhost:11434/api/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: trainerModel,
          prompt: feedbackPrompt,
          stream: false,
          options: {
            temperature: 0.7,
            top_p: 0.9,
            top_k: 40,
          },
        }),
      });

      const feedbackData = await feedbackResponse.json();
      const feedback = feedbackData.response;

      // Stocker la session dans la mémoire thermique
      if (thermalMemory) {
        // Extraire la question originale si elle a été enrichie
        const originalQuestion = questionData.originalQuestion || question;

        // Créer un titre court basé sur la question originale
        const shortTitle = originalQuestion.length > 30
          ? `${originalQuestion.substring(0, 30)}...`
          : originalQuestion;

        const memoryContent = {
          id: `training_${sessionId}`,
          title: `Formation: ${shortTitle}`,
          type: 'training_session',
          question,
          originalQuestion: questionData.originalQuestion || question,
          response,
          feedback,
          timestamp: new Date().toISOString(),
          sessionId,
          accelerated: useAccelerators,
          speedMultiplier: trainingSpeedMultiplier,
          internetEnabled,
          internetData: internetEnabled ? {
            searchResults: questionData.searchResults || [],
            webContent: questionData.webContent || '',
            videoInfo: questionData.videoInfo || null,
            programmingInfo: questionData.programmingInfo || null
          } : null,
          messages: [
            { role: 'user', content: question },
            { role: 'assistant', content: response },
            { role: 'system', content: `Feedback: ${feedback}${useAccelerators ? ` (Accéléré x${trainingSpeedMultiplier.toFixed(1)})` : ''}${internetEnabled ? ' (Avec accès Internet)' : ''}` }
          ]
        };

        // Utiliser addConversation au lieu de addMemory
        if (thermalMemory.addConversation) {
          thermalMemory.addConversation(memoryContent);
          console.log(`Session de formation ${sessionId} ajoutée à la mémoire thermique`);
        } else {
          console.log(`Méthode addConversation non disponible dans la mémoire thermique`);
        }
      }

      // Mise à jour de la progression - étape de stockage et finalisation
      io.emit('training progress', {
        sessionId,
        questionIndex: i,
        totalQuestions: count,
        progress: Math.round((i + 1) / count * 100),
        status: i === count - 1 ? 'completed' : 'processing',
        stage: 'storing_memory',
        elapsedTime: Math.round((Date.now() - startTime) / 1000),
        estimatedTimeRemaining: i === count - 1 ? 0 : Math.max(0, Math.round(totalEstimatedTime - (Date.now() - startTime) / 1000)),
        accelerated: useAccelerators,
        speedMultiplier: trainingSpeedMultiplier,
        session: {
          id: questionId,
          question,
          response,
          feedback: useAccelerators ? `${feedback} (Accéléré x${trainingSpeedMultiplier.toFixed(1)})` : feedback,
          timestamp: new Date().toISOString()
        }
      });

      sessions.push({
        id: questionId,
        question: questionData.originalQuestion || question,
        enrichedQuestion: internetEnabled ? question : null,
        response,
        feedback,
        timestamp: new Date().toISOString(),
        internetEnabled,
        internetData: internetEnabled ? {
          searchResults: questionData.searchResults ? questionData.searchResults.length : 0,
          hasWebContent: !!questionData.webContent,
          hasVideoInfo: !!questionData.videoInfo,
          hasProgrammingInfo: !!(questionData.programmingInfo &&
            (questionData.programmingInfo.github.length > 0 ||
             questionData.programmingInfo.stackoverflow.length > 0)),
          githubResults: questionData.programmingInfo ? questionData.programmingInfo.github.length : 0,
          stackoverflowResults: questionData.programmingInfo ? questionData.programmingInfo.stackoverflow.length : 0
        } : null
      });
    }

    // Mettre à jour les statistiques de l'IA
    try {
      await fetch('http://localhost:3001/api/ai/training-complete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionCount: sessions.length,
          accelerated: useAccelerators,
          speedMultiplier: trainingSpeedMultiplier
        }),
      });

      // Calculer le temps total réel
      const totalTime = Math.round((Date.now() - startTime) / 1000);

      console.log(`Formation terminée avec ${sessions.length} sessions en ${totalTime} secondes${useAccelerators ? ` (accélérée x${trainingSpeedMultiplier.toFixed(1)})` : ''}`);

      // Émettre un événement de fin de formation
      io.emit('training complete', {
        sessionId,
        questionCount: count,
        totalTime,
        accelerated: useAccelerators,
        speedMultiplier: trainingSpeedMultiplier,
        sessions
      });
    } catch (statsError) {
      console.error('Erreur lors de la mise à jour des statistiques de l\'IA:', statsError);
      // Ne pas bloquer la réponse en cas d'erreur de mise à jour des statistiques
    }

    res.json({ success: true, sessions });
  } catch (error) {
    console.error('Erreur lors de la formation:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Route pour récupérer les sessions de formation
router.get('/training/sessions', (req, res) => {
  try {
    if (!thermalMemory) {
      return res.json({ success: false, error: 'Mémoire thermique non disponible', sessions: [] });
    }

    // Récupérer toutes les mémoires/conversations
    let allMemories = [];

    if (thermalMemory.getConversations) {
      allMemories = thermalMemory.getConversations();
      console.log(`Récupération de ${allMemories.length} conversations depuis la mémoire thermique`);
    } else if (thermalMemory.getMemories) {
      allMemories = thermalMemory.getMemories();
      console.log(`Récupération de ${allMemories.length} mémoires depuis la mémoire thermique`);
    }

    // Filtrer les sessions de formation
    const trainingSessions = allMemories
      .filter(memory => memory.type === 'training_session' || memory.id.startsWith('training_'))
      .map(session => ({
        sessionId: session.sessionId || session.id,
        question: session.question || (session.messages && session.messages[0] ? session.messages[0].content : ''),
        response: session.response || (session.messages && session.messages[1] ? session.messages[1].content : ''),
        feedback: session.feedback || (session.messages && session.messages[2] ? session.messages[2].content.replace('Feedback: ', '') : ''),
        timestamp: session.timestamp
      }));

    res.json({ success: true, sessions: trainingSessions });
  } catch (error) {
    console.error('Erreur lors de la récupération des sessions de formation:', error);
    res.status(500).json({ success: false, error: error.message, sessions: [] });
  }
});

module.exports = { router, initThermalMemory };
