/**
 * Routes pour la réflexion de Vision Ultra
 * Ce fichier définit les routes pour la réflexion de l'agent
 */

const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');

// Référence au service de réflexion
let reflectionService;

/**
 * Initialise le routeur avec les services nécessaires
 * @param {Object} services - Services nécessaires
 * @param {Object} services.reflectionService - Service de réflexion
 */
function init(services) {
  reflectionService = services.reflectionService;
  console.log('Routeur de réflexion initialisé');
  return router;
}

// Route pour la page de réflexion
router.get('/reflection', (req, res) => {
  res.render('luna-reflection-enhanced', {
    title: 'Luna - Réflexion Améliorée',
    page: 'reflection',
    currentDate: new Date().toLocaleString('fr-FR')
  });
});

// Route pour obtenir les paramètres de réflexion
router.get('/api/reflection/settings', (req, res) => {
  if (!reflectionService) {
    return res.status(503).json({
      success: false,
      error: 'Service de réflexion non disponible'
    });
  }

  res.json({
    success: true,
    settings: reflectionService.config.general
  });
});

// Route pour mettre à jour les paramètres de réflexion
router.post('/api/reflection/settings', (req, res) => {
  if (!reflectionService) {
    return res.status(503).json({
      success: false,
      error: 'Service de réflexion non disponible'
    });
  }

  const { showReflection, autoTranslate, displayStyle } = req.body;

  // Mettre à jour les paramètres
  if (showReflection !== undefined) {
    reflectionService.config.general.showReflection = showReflection;
  }

  if (autoTranslate !== undefined) {
    reflectionService.config.general.autoTranslate = autoTranslate;
  }

  if (displayStyle !== undefined) {
    reflectionService.config.general.displayStyle = displayStyle;
  }

  // Sauvegarder les paramètres
  reflectionService.saveConfig();

  res.json({
    success: true,
    settings: reflectionService.config.general
  });
});

// Route pour obtenir l'historique de réflexion
router.get('/api/reflection/history', (req, res) => {
  if (!reflectionService) {
    return res.status(503).json({
      success: false,
      error: 'Service de réflexion non disponible'
    });
  }

  const limit = parseInt(req.query.limit) || 20;

  res.json({
    success: true,
    history: reflectionService.reflectionHistory.slice(0, limit)
  });
});

// Route pour effacer l'historique de réflexion
router.delete('/api/reflection/history', (req, res) => {
  if (!reflectionService) {
    return res.status(503).json({
      success: false,
      error: 'Service de réflexion non disponible'
    });
  }

  reflectionService.reflectionHistory = [];

  res.json({
    success: true,
    message: 'Historique de réflexion effacé'
  });
});

// Route pour générer une réflexion
router.post('/api/reflection/generate', async (req, res) => {
  if (!reflectionService) {
    return res.status(503).json({
      success: false,
      error: 'Service de réflexion non disponible'
    });
  }

  const { input } = req.body;

  if (!input) {
    return res.status(400).json({
      success: false,
      error: 'Entrée requise'
    });
  }

  try {
    const result = await reflectionService.generateReflection(input, req.ip);

    res.json({
      success: true,
      reflection: result.reflection,
      details: result.details,
      time: result.time,
      acceleration: result.acceleration
    });
  } catch (error) {
    console.error('Erreur lors de la génération de la réflexion:', error);

    res.status(500).json({
      success: false,
      error: 'Erreur lors de la génération de la réflexion'
    });
  }
});

// Route pour obtenir la date et l'heure actuelles
router.get('/api/reflection/datetime', (req, res) => {
  if (!reflectionService) {
    return res.status(503).json({
      success: false,
      error: 'Service de réflexion non disponible'
    });
  }

  const currentDate = reflectionService.getCurrentDateTime();

  res.json({
    success: true,
    datetime: {
      iso: currentDate.toISOString(),
      locale: currentDate.toLocaleString('fr-FR'),
      date: currentDate.toLocaleDateString('fr-FR'),
      time: currentDate.toLocaleTimeString('fr-FR')
    }
  });
});

/**
 * Initialise les gestionnaires d'événements Socket.IO
 * @param {Object} io - Instance Socket.IO
 */
function initSocketHandlers(io) {
  if (!reflectionService) {
    console.error('Service de réflexion non disponible pour les gestionnaires Socket.IO');
    return;
  }

  console.log('Gestionnaires Socket.IO pour la réflexion initialisés');
}

module.exports = {
  router,
  init,
  initSocketHandlers
};
