const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Configurer le stockage pour différents types de documents
const documentsStorage = multer.diskStorage({
  destination: function(req, file, cb) {
    // Déterminer le dossier en fonction du type de fichier
    let targetFolder;
    
    if (file.mimetype.startsWith('image/')) {
      targetFolder = path.join(__dirname, '../uploads/images');
    } else if (file.mimetype.startsWith('audio/')) {
      targetFolder = path.join(__dirname, '../uploads/audio');
    } else if (file.mimetype.startsWith('video/')) {
      targetFolder = path.join(__dirname, '../uploads/videos');
    } else if (file.mimetype === 'application/pdf') {
      targetFolder = path.join(__dirname, '../uploads/pdf');
    } else {
      targetFolder = path.join(__dirname, '../uploads/documents');
    }
    
    // Créer le répertoire s'il n'existe pas
    if (!fs.existsSync(targetFolder)) {
      fs.mkdirSync(targetFolder, { recursive: true });
    }
    
    cb(null, targetFolder);
  },
  filename: function(req, file, cb) {
    // Générer un nom de fichier unique
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

// Filtrer les types de fichiers acceptés
const fileFilter = (req, file, cb) => {
  // Types de fichiers acceptés
  const allowedTypes = [
    // Images
    'image/jpeg', 'image/png', 'image/gif', 'image/webp',
    // Documents
    'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain', 'text/csv',
    // Audio
    'audio/mpeg', 'audio/wav', 'audio/ogg',
    // Vidéo
    'video/mp4', 'video/webm', 'video/quicktime'
  ];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Type de fichier non supporté'), false);
  }
};

// Configurer multer avec le stockage défini
const upload = multer({ 
  storage: documentsStorage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 50 * 1024 * 1024, // Limite à 50MB par fichier
  }
});

// Route pour télécharger un fichier
router.post('/upload', upload.single('document'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Aucun fichier téléchargé'
      });
    }
    
    // Ajouter le fichier à la mémoire thermique si disponible
    if (global.thermalMemory && global.thermalMemory.addInputMemory) {
      global.thermalMemory.addInputMemory({
        type: 'document',
        channel: 'vision',
        content: `Document reçu: ${req.file.originalname} (${req.file.mimetype})`,
        source: req.file.path,
        timestamp: new Date().toISOString()
      });
      console.log('👁️ Document ajouté à la mémoire thermique');
    }
    
    // Renvoyer les informations sur le fichier
    res.json({
      success: true,
      file: {
        filename: req.file.filename,
        originalName: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size,
        path: req.file.path.replace(/^.*[\\\/]/, '') // Chemin relatif sans dossiers
      },
      message: 'Fichier téléchargé avec succès'
    });
  } catch (error) {
    console.error('Erreur lors du téléchargement:', error);
    res.status(500).json({
      success: false,
      message: `Erreur lors du téléchargement: ${error.message}`
    });
  }
});

// Route pour récupérer la liste des fichiers téléchargés
router.get('/list', (req, res) => {
  try {
    const baseDir = path.join(__dirname, '../uploads');
    const categories = ['documents', 'images', 'audio', 'videos', 'pdf'];
    
    // Créer le dossier uploads s'il n'existe pas
    if (!fs.existsSync(baseDir)) {
      fs.mkdirSync(baseDir, { recursive: true });
    }
    
    // Préparer le résultat
    const result = {
      success: true,
      files: {}
    };
    
    // Parcourir chaque catégorie
    categories.forEach(category => {
      const categoryPath = path.join(baseDir, category);
      
      // Créer le dossier s'il n'existe pas
      if (!fs.existsSync(categoryPath)) {
        fs.mkdirSync(categoryPath, { recursive: true });
        result.files[category] = [];
        return;
      }
      
      // Lister les fichiers dans la catégorie
      try {
        const files = fs.readdirSync(categoryPath)
          .map(filename => {
            const filePath = path.join(categoryPath, filename);
            const stats = fs.statSync(filePath);
            
            return {
              name: filename,
              path: `uploads/${category}/${filename}`,
              size: stats.size,
              modified: stats.mtime
            };
          });
        
        result.files[category] = files;
      } catch (err) {
        result.files[category] = [];
        console.error(`Erreur lors de la lecture du dossier ${category}:`, err);
      }
    });
    
    res.json(result);
  } catch (error) {
    console.error('Erreur lors de la récupération de la liste des fichiers:', error);
    res.status(500).json({
      success: false,
      message: `Erreur: ${error.message}`
    });
  }
});

// Route pour supprimer un fichier
router.delete('/remove', (req, res) => {
  try {
    const { filepath } = req.body;
    
    if (!filepath) {
      return res.status(400).json({
        success: false,
        message: 'Chemin du fichier non spécifié'
      });
    }
    
    // Sécuriser le chemin du fichier (éviter les attaques de traversée de chemin)
    const normalizedPath = path.normalize(filepath).replace(/^(\.\.(\/|\\|$))+/, '');
    const fullPath = path.join(__dirname, '../', normalizedPath);
    
    // Vérifier si le fichier existe
    if (!fs.existsSync(fullPath)) {
      return res.status(404).json({
        success: false,
        message: 'Fichier non trouvé'
      });
    }
    
    // Supprimer le fichier
    fs.unlinkSync(fullPath);
    
    res.json({
      success: true,
      message: 'Fichier supprimé avec succès'
    });
  } catch (error) {
    console.error('Erreur lors de la suppression du fichier:', error);
    res.status(500).json({
      success: false,
      message: `Erreur: ${error.message}`
    });
  }
});

module.exports = router;
