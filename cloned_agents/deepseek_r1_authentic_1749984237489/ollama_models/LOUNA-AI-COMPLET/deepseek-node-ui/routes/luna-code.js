/**
 * Routes pour la gestion des fichiers de code
 */

const express = require('express');
const router = express.Router();
const codeManager = require('../lib/code-manager');
const path = require('path');
const fs = require('fs');

// Middleware pour gérer les erreurs
const asyncHandler = fn => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Route pour obtenir tous les projets
router.get('/api/code/projects', asyncHandler(async (req, res) => {
  const projects = codeManager.getAllProjects();
  res.json({
    success: true,
    projects: projects
  });
}));

// Route pour obtenir un projet spécifique
router.get('/api/code/projects/:projectId', asyncHandler(async (req, res) => {
  const { projectId } = req.params;
  const project = codeManager.getProject(projectId);

  if (!project) {
    return res.status(404).json({
      success: false,
      error: `Projet non trouvé: ${projectId}`
    });
  }

  res.json({
    success: true,
    project: project
  });
}));

// Route pour obtenir le projet actif
router.get('/api/code/active-project', asyncHandler(async (req, res) => {
  const project = codeManager.getActiveProject();

  res.json({
    success: true,
    project: project
  });
}));

// Route pour définir le projet actif
router.post('/api/code/active-project', asyncHandler(async (req, res) => {
  const { projectId } = req.body;

  if (!projectId) {
    return res.status(400).json({
      success: false,
      error: 'ID de projet requis'
    });
  }

  const success = codeManager.setActiveProject(projectId);

  if (!success) {
    return res.status(404).json({
      success: false,
      error: `Projet non trouvé: ${projectId}`
    });
  }

  res.json({
    success: true,
    project: codeManager.getActiveProject()
  });
}));

// Route pour créer un nouveau projet
router.post('/api/code/projects', asyncHandler(async (req, res) => {
  const { name, description } = req.body;

  if (!name) {
    return res.status(400).json({
      success: false,
      error: 'Nom de projet requis'
    });
  }

  const projectId = codeManager.createProject(name, description || '');

  res.json({
    success: true,
    projectId: projectId,
    project: codeManager.getProject(projectId)
  });
}));

// Route pour obtenir le contenu d'un fichier
router.get('/api/code/projects/:projectId/files/:fileName', asyncHandler(async (req, res) => {
  const { projectId, fileName } = req.params;

  try {
    const content = codeManager.loadFile(projectId, fileName);

    res.json({
      success: true,
      content: content,
      fileName: fileName,
      language: codeManager.getLanguageFromFileName(fileName)
    });
  } catch (error) {
    res.status(404).json({
      success: false,
      error: error.message
    });
  }
}));

// Route pour sauvegarder un fichier
router.post('/api/code/projects/:projectId/files/:fileName', asyncHandler(async (req, res) => {
  const { projectId, fileName } = req.params;
  const { content } = req.body;

  if (content === undefined) {
    return res.status(400).json({
      success: false,
      error: 'Contenu requis'
    });
  }

  try {
    const fileInfo = codeManager.saveFile(projectId, fileName, content);

    res.json({
      success: true,
      file: fileInfo
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
}));

// Route pour supprimer un fichier
router.delete('/api/code/projects/:projectId/files/:fileName', asyncHandler(async (req, res) => {
  const { projectId, fileName } = req.params;

  try {
    const success = codeManager.deleteFile(projectId, fileName);

    res.json({
      success: success
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
}));

// Route pour créer un nouveau fichier
router.post('/api/code/projects/:projectId/files', asyncHandler(async (req, res) => {
  const { projectId } = req.params;
  const { fileName, content } = req.body;

  if (!fileName) {
    return res.status(400).json({
      success: false,
      error: 'Nom de fichier requis'
    });
  }

  try {
    const fileInfo = codeManager.saveFile(projectId, fileName, content || '');

    res.json({
      success: true,
      file: fileInfo
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
}));

// Route pour obtenir les versions d'un fichier
router.get('/api/code/projects/:projectId/files/:fileName/versions', asyncHandler(async (req, res) => {
  const { projectId, fileName } = req.params;

  try {
    const versions = codeManager.getFileVersions(projectId, fileName);

    res.json({
      success: true,
      versions: versions
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
}));

// Route pour obtenir le contenu d'une version
router.get('/api/code/projects/:projectId/files/:fileName/versions/:versionId', asyncHandler(async (req, res) => {
  const { projectId, fileName, versionId } = req.params;

  try {
    const content = codeManager.getVersionContent(projectId, fileName, versionId);

    if (!content) {
      return res.status(404).json({
        success: false,
        error: `Version non trouvée: ${versionId}`
      });
    }

    res.json({
      success: true,
      content: content,
      versionId: versionId
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
}));

// Route pour restaurer une version
router.post('/api/code/projects/:projectId/files/:fileName/versions/:versionId/restore', asyncHandler(async (req, res) => {
  const { projectId, fileName, versionId } = req.params;

  try {
    const fileInfo = codeManager.restoreVersion(projectId, fileName, versionId);

    if (!fileInfo) {
      return res.status(500).json({
        success: false,
        error: `Impossible de restaurer la version ${versionId}`
      });
    }

    res.json({
      success: true,
      file: fileInfo
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
}));

// Initialisation des gestionnaires de socket
function initSocketHandlers(io) {
  // Référence au module de sauvegarde automatique
  let autoBackup;
  try {
    // Vérifier si le module de sauvegarde automatique est disponible globalement
    if (global.autoBackup) {
      autoBackup = global.autoBackup;
    }
  } catch (error) {
    console.log('Module de sauvegarde automatique non disponible dans luna-code:', error.message);
  }

  io.on('connection', (socket) => {
    console.log('Client connecté au gestionnaire de code:', socket.id);

    // Événement pour obtenir tous les projets
    socket.on('get code projects', () => {
      const projects = codeManager.getAllProjects();
      socket.emit('code projects', { projects });
    });

    // Événement pour obtenir le projet actif
    socket.on('get active project', () => {
      const project = codeManager.getActiveProject();
      socket.emit('active project', { project });
    });

    // Événement pour définir le projet actif
    socket.on('set active project', ({ projectId }) => {
      const success = codeManager.setActiveProject(projectId);

      if (success) {
        const project = codeManager.getActiveProject();
        io.emit('active project', { project });
      }
    });

    // Événement pour sauvegarder un fichier
    socket.on('save file', ({ projectId, fileName, content }) => {
      try {
        const fileInfo = codeManager.saveFile(projectId, fileName, content);
        io.emit('file saved', { projectId, file: fileInfo });

        // Enregistrer la modification pour la sauvegarde automatique
        if (autoBackup || global.autoBackup) {
          const backupModule = autoBackup || global.autoBackup;
          backupModule.recordChange('code', `Fichier sauvegardé: ${projectId}/${fileName}`);
        }
      } catch (error) {
        socket.emit('code error', { error: error.message });
      }
    });

    // Événement pour créer un nouveau projet
    socket.on('create project', ({ name, description }) => {
      const projectId = codeManager.createProject(name, description || '');
      const project = codeManager.getProject(projectId);

      io.emit('project created', { project });
      io.emit('active project', { project });
    });

    // Événement pour créer un nouveau fichier
    socket.on('create file', ({ projectId, fileName, content }) => {
      try {
        const fileInfo = codeManager.saveFile(projectId, fileName, content || '');
        io.emit('file created', { projectId, file: fileInfo });

        // Enregistrer la modification pour la sauvegarde automatique
        if (autoBackup || global.autoBackup) {
          const backupModule = autoBackup || global.autoBackup;
          backupModule.recordChange('code', `Nouveau fichier créé: ${projectId}/${fileName}`);
        }
      } catch (error) {
        socket.emit('code error', { error: error.message });
      }
    });

    // Événement pour supprimer un fichier
    socket.on('delete file', ({ projectId, fileName }) => {
      try {
        const success = codeManager.deleteFile(projectId, fileName);

        if (success) {
          io.emit('file deleted', { projectId, fileName });
        }
      } catch (error) {
        socket.emit('code error', { error: error.message });
      }
    });

    // Événement pour obtenir les versions d'un fichier
    socket.on('get file versions', ({ projectId, fileName }) => {
      try {
        const versions = codeManager.getFileVersions(projectId, fileName);
        socket.emit('file versions', { projectId, fileName, versions });
      } catch (error) {
        socket.emit('code error', { error: error.message });
      }
    });

    // Événement pour obtenir le contenu d'une version
    socket.on('get version content', ({ projectId, fileName, versionId }) => {
      try {
        const content = codeManager.getVersionContent(projectId, fileName, versionId);

        if (content) {
          socket.emit('version content', { projectId, fileName, versionId, content });
        } else {
          socket.emit('code error', { error: `Version non trouvée: ${versionId}` });
        }
      } catch (error) {
        socket.emit('code error', { error: error.message });
      }
    });

    // Événement pour restaurer une version
    socket.on('restore version', ({ projectId, fileName, versionId }) => {
      try {
        const fileInfo = codeManager.restoreVersion(projectId, fileName, versionId);

        if (fileInfo) {
          io.emit('file saved', { projectId, file: fileInfo });
          socket.emit('version restored', { projectId, fileName, versionId });
        } else {
          socket.emit('code error', { error: `Impossible de restaurer la version ${versionId}` });
        }
      } catch (error) {
        socket.emit('code error', { error: error.message });
      }
    });
  });
}

module.exports = {
  router,
  initSocketHandlers
};
