/**
 * Routes pour tous les accélérateurs Kyber
 * Version temporaire pour permettre le démarrage de Luna
 */

const express = require('express');

/**
 * Initialise le routeur pour tous les accélérateurs
 * @param {Object} options - Options de configuration
 * @returns {Object} - Le routeur initialisé avec les gestionnaires
 */
function initAllAcceleratorsRouter(options = {}) {
  const router = express.Router();
  
  // Route pour obtenir tous les accélérateurs
  router.get('/', (req, res) => {
    const allAccelerators = [];
    
    if (options.kyberAccelerators && Array.isArray(options.kyberAccelerators.accelerators)) {
      allAccelerators.push(...options.kyberAccelerators.accelerators);
    }
    
    res.json({
      success: true,
      accelerators: allAccelerators
    });
  });
  
  // Gestionnaire de socket pour les mises à jour en temps réel
  const initSocketHandlers = (io) => {
    if (!io) return;
    
    // Événement pour les mises à jour périodiques
    setInterval(() => {
      if (options.kyberAccelerators && Array.isArray(options.kyberAccelerators.accelerators)) {
        io.emit('accelerators:update', {
          accelerators: options.kyberAccelerators.accelerators
        });
      }
    }, 5000);
  };
  
  return {
    router,
    initSocketHandlers
  };
}

module.exports = initAllAcceleratorsRouter;