/**
 * Routes pour la génération de contenu multimédia dans Luna
 * Permet de générer des vidéos, des images, du code et de la musique
 * 
 * Créé par <PERSON>, Sainte-Anne, Guadeloupe (97180)
 */

const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');
const MediaGenerator = require('../lib/media-generator');

// Instance du générateur de média
let mediaGenerator;

// Initialiser le générateur de média
function initMediaGenerator(socketIo, mcpSystem) {
  // Créer l'instance du générateur
  mediaGenerator = new MediaGenerator({
    outputDir: path.join(__dirname, '../data/generated'),
    tempDir: path.join(__dirname, '../data/temp'),
    debug: true,
    useExternalAPIs: true,
    mcpPort: 3002
  });
  
  // Configurer les événements Socket.io
  if (socketIo) {
    mediaGenerator.on('job:created', (job) => {
      socketIo.emit('media:job:created', job);
    });
    
    mediaGenerator.on('job:progress', (update) => {
      socketIo.emit('media:job:progress', update);
    });
    
    mediaGenerator.on('job:completed', (job) => {
      socketIo.emit('media:job:completed', job);
    });
    
    mediaGenerator.on('job:failed', (job) => {
      socketIo.emit('media:job:failed', job);
    });
  }
  
  console.log('Générateur de média initialisé');
  return mediaGenerator;
}

// Route pour la page de génération multimédia
router.get('/media', (req, res) => {
  res.render('luna-media', {
    title: 'Luna - Génération Multimédia',
    path: '/luna/media'
  });
});

// API pour générer une image
router.post('/api/media/image', async (req, res) => {
  try {
    const { prompt, options = {} } = req.body;
    
    if (!prompt) {
      return res.status(400).json({
        success: false,
        error: 'Le prompt est requis'
      });
    }
    
    if (!mediaGenerator) {
      return res.status(500).json({
        success: false,
        error: 'Le générateur de média n\'est pas initialisé'
      });
    }
    
    const result = await mediaGenerator.generateImage(prompt, options);
    
    res.json({
      success: true,
      result
    });
  } catch (error) {
    console.error('Erreur lors de la génération d\'image:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// API pour générer une vidéo
router.post('/api/media/video', async (req, res) => {
  try {
    const { prompt, options = {} } = req.body;
    
    if (!prompt) {
      return res.status(400).json({
        success: false,
        error: 'Le prompt est requis'
      });
    }
    
    if (!mediaGenerator) {
      return res.status(500).json({
        success: false,
        error: 'Le générateur de média n\'est pas initialisé'
      });
    }
    
    const result = await mediaGenerator.generateVideo(prompt, options);
    
    res.json({
      success: true,
      result
    });
  } catch (error) {
    console.error('Erreur lors de la génération de vidéo:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// API pour générer de la musique
router.post('/api/media/music', async (req, res) => {
  try {
    const { prompt, options = {} } = req.body;
    
    if (!prompt) {
      return res.status(400).json({
        success: false,
        error: 'Le prompt est requis'
      });
    }
    
    if (!mediaGenerator) {
      return res.status(500).json({
        success: false,
        error: 'Le générateur de média n\'est pas initialisé'
      });
    }
    
    const result = await mediaGenerator.generateMusic(prompt, options);
    
    res.json({
      success: true,
      result
    });
  } catch (error) {
    console.error('Erreur lors de la génération de musique:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// API pour générer du code
router.post('/api/media/code', async (req, res) => {
  try {
    const { prompt, options = {} } = req.body;
    
    if (!prompt) {
      return res.status(400).json({
        success: false,
        error: 'Le prompt est requis'
      });
    }
    
    if (!mediaGenerator) {
      return res.status(500).json({
        success: false,
        error: 'Le générateur de média n\'est pas initialisé'
      });
    }
    
    const result = await mediaGenerator.generateCode(prompt, options);
    
    res.json({
      success: true,
      result
    });
  } catch (error) {
    console.error('Erreur lors de la génération de code:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// API pour obtenir la liste des fichiers générés
// Route pour tous les fichiers médias
router.get('/api/media/files', (req, res) => {
  try {
    const type = req.query.type; // Utilisation d'un paramètre de requête au lieu d'un paramètre de route
    const validTypes = ['image', 'video', 'music', 'code'];
    const types = type && validTypes.includes(type) ? [type] : validTypes;
    
    const result = {};
    
    types.forEach(mediaType => {
      const plural = mediaType === 'image' ? 'images' : 
                    mediaType === 'video' ? 'videos' : 
                    mediaType === 'music' ? 'music' : 'code';
                    
      const dir = path.join(__dirname, '../data/generated', plural);
      
      if (fs.existsSync(dir)) {
        const files = fs.readdirSync(dir)
          .filter(file => {
            // Filtrer par extension selon le type
            if (mediaType === 'image') return file.endsWith('.png') || file.endsWith('.jpg');
            if (mediaType === 'video') return file.endsWith('.mp4') || file.endsWith('.webm');
            if (mediaType === 'music') return file.endsWith('.mp3') || file.endsWith('.wav');
            if (mediaType === 'code') return true; // Tous les fichiers pour le code
            return true;
          })
          .map(file => {
            const filePath = path.join(dir, file);
            const stats = fs.statSync(filePath);
            
            return {
              name: file,
              path: `/generated/${plural}/${file}`,
              size: stats.size,
              created: stats.birthtime
            };
          })
          .sort((a, b) => new Date(b.created) - new Date(a.created));
          
        result[mediaType] = files;
      } else {
        result[mediaType] = [];
      }
    });
    
    res.json({
      success: true,
      files: result
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des fichiers:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Initialiser les gestionnaires de socket pour le média
function initSocketHandlers(io, thermalMemory) {
  io.on('connection', (socket) => {
    console.log('Client connecté au générateur de média:', socket.id);
    
    // Obtenir l'état actuel du générateur
    socket.on('get media generator status', () => {
      if (!mediaGenerator) {
        socket.emit('media generator status', {
          success: false,
          error: 'Le générateur de média n\'est pas initialisé'
        });
        return;
      }
      
      socket.emit('media generator status', {
        success: true,
        activeJobs: Array.from(mediaGenerator.activeJobs.values())
      });
    });
    
    // Générer une image
    socket.on('generate image', async (data) => {
      try {
        const { prompt, options = {} } = data;
        
        if (!prompt) {
          socket.emit('generate image error', {
            success: false,
            error: 'Le prompt est requis'
          });
          return;
        }
        
        if (!mediaGenerator) {
          socket.emit('generate image error', {
            success: false,
            error: 'Le générateur de média n\'est pas initialisé'
          });
          return;
        }
        
        const result = await mediaGenerator.generateImage(prompt, options);
        
        socket.emit('generate image result', {
          success: true,
          result
        });
        
        // Stocker dans la mémoire thermique si disponible
        if (thermalMemory && thermalMemory.addConversation) {
          thermalMemory.addConversation({
            id: `media_${Date.now()}`,
            title: `Image: ${prompt.substring(0, 30)}`,
            messages: [
              { role: 'user', content: `Génère une image: ${prompt}` },
              { role: 'assistant', content: `J'ai généré une image basée sur votre description: "${prompt}". L'image est disponible à ${result.path}.` }
            ],
            zone: 2, // Zone chaude
            temperature: 80,
            timestamp: new Date().toISOString()
          });
        }
      } catch (error) {
        console.error('Erreur lors de la génération d\'image via socket:', error);
        socket.emit('generate image error', {
          success: false,
          error: error.message
        });
      }
    });
    
    // Générer une vidéo
    socket.on('generate video', async (data) => {
      try {
        const { prompt, options = {} } = data;
        
        if (!prompt) {
          socket.emit('generate video error', {
            success: false,
            error: 'Le prompt est requis'
          });
          return;
        }
        
        if (!mediaGenerator) {
          socket.emit('generate video error', {
            success: false,
            error: 'Le générateur de média n\'est pas initialisé'
          });
          return;
        }
        
        const result = await mediaGenerator.generateVideo(prompt, options);
        
        socket.emit('generate video result', {
          success: true,
          result
        });
        
        // Stocker dans la mémoire thermique si disponible
        if (thermalMemory && thermalMemory.addConversation) {
          thermalMemory.addConversation({
            id: `media_${Date.now()}`,
            title: `Vidéo: ${prompt.substring(0, 30)}`,
            messages: [
              { role: 'user', content: `Génère une vidéo: ${prompt}` },
              { role: 'assistant', content: `J'ai généré une vidéo basée sur votre description: "${prompt}". La vidéo est disponible à ${result.path}.` }
            ],
            zone: 2, // Zone chaude
            temperature: 80,
            timestamp: new Date().toISOString()
          });
        }
      } catch (error) {
        console.error('Erreur lors de la génération de vidéo via socket:', error);
        socket.emit('generate video error', {
          success: false,
          error: error.message
        });
      }
    });
  });
}

module.exports = {
  router,
  initMediaGenerator,
  initSocketHandlers
};
