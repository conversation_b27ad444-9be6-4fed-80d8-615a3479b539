/**
 * Script pour tester si l'assistant répond correctement aux questions sur l'identité de l'utilisateur
 */

const io = require('socket.io-client');

// Fonction pour envoyer un message et attendre la réponse
function sendMessage(message) {
  return new Promise((resolve, reject) => {
    try {
      // Se connecter au serveur
      const socket = io('http://localhost:3001');
      
      // Gérer la connexion
      socket.on('connect', () => {
        console.log('Connecté au serveur WebSocket');
        
        // Attendre un peu avant d'envoyer le message
        setTimeout(() => {
          // Envoyer le message
          console.log(`Envoi du message: "${message}"`);
          socket.emit('luna message', { message });
        }, 1000);
      });
      
      // Gérer la réponse
      socket.on('luna response', (data) => {
        console.log(`Réponse reçue: "${data.message}"`);
        socket.disconnect();
        resolve(data.message);
      });
      
      // Gérer les erreurs
      socket.on('connect_error', (error) => {
        console.error('Erreur de connexion:', error.message);
        socket.disconnect();
        reject(error);
      });
      
      // Timeout après 30 secondes
      setTimeout(() => {
        if (socket.connected) {
          socket.disconnect();
          reject(new Error('Timeout: Pas de réponse après 30 secondes'));
        }
      }, 30000);
    } catch (error) {
      console.error('Erreur lors de l\'envoi du message:', error.message);
      reject(error);
    }
  });
}

// Fonction principale
async function testIdentity() {
  console.log('=== TEST DE L\'IDENTITÉ DE L\'ASSISTANT ===');
  
  try {
    // Test 1: Demander le nom de l'assistant
    console.log('\n--- Test 1: Nom de l\'assistant ---');
    const response1 = await sendMessage('Comment tu t\'appelles?');
    const isVisionUltra = response1.toLowerCase().includes('vision ultra');
    const isNotLouna = !response1.toLowerCase().includes('louna');
    console.log(`L'assistant s'identifie comme Vision Ultra: ${isVisionUltra ? 'Oui' : 'Non'}`);
    console.log(`L'assistant ne s'identifie pas comme Louna: ${isNotLouna ? 'Oui' : 'Non'}`);
    
    // Test 2: Demander le nom de l'utilisateur
    console.log('\n--- Test 2: Nom de l\'utilisateur ---');
    const response2 = await sendMessage('Comment je m\'appelle?');
    const knowsUserName = response2.toLowerCase().includes('jean passave');
    console.log(`L'assistant connaît le nom de l'utilisateur: ${knowsUserName ? 'Oui' : 'Non'}`);
    
    // Test 3: Demander où habite l'utilisateur
    console.log('\n--- Test 3: Lieu de résidence de l\'utilisateur ---');
    const response3 = await sendMessage('Où est-ce que j\'habite?');
    const knowsUserLocation = response3.toLowerCase().includes('sainte-anne') && response3.toLowerCase().includes('guadeloupe');
    console.log(`L'assistant connaît le lieu de résidence de l'utilisateur: ${knowsUserLocation ? 'Oui' : 'Non'}`);
    
    // Test 4: Demander l'origine de l'utilisateur
    console.log('\n--- Test 4: Origine de l\'utilisateur ---');
    const response4 = await sendMessage('Quelle est mon origine?');
    const knowsUserOrigin = response4.toLowerCase().includes('africaine');
    console.log(`L'assistant connaît l'origine de l'utilisateur: ${knowsUserOrigin ? 'Oui' : 'Non'}`);
    
    // Test 5: Demander qui est le créateur de l'assistant
    console.log('\n--- Test 5: Créateur de l\'assistant ---');
    const response5 = await sendMessage('Qui est ton créateur?');
    const knowsCreator = response5.toLowerCase().includes('jean passave');
    console.log(`L'assistant connaît son créateur: ${knowsCreator ? 'Oui' : 'Non'}`);
    
    // Résultats
    console.log('\n=== RÉSULTATS DES TESTS ===');
    const totalTests = 5;
    const passedTests = [isVisionUltra, isNotLouna, knowsUserName, knowsUserLocation, knowsUserOrigin, knowsCreator].filter(Boolean).length;
    console.log(`Tests réussis: ${passedTests}/${totalTests + 1}`);
    
    if (passedTests === totalTests + 1) {
      console.log('✅ TOUS LES TESTS ONT RÉUSSI: L\'assistant répond correctement aux questions sur l\'identité');
    } else {
      console.log('❌ CERTAINS TESTS ONT ÉCHOUÉ: L\'assistant ne répond pas correctement à toutes les questions sur l\'identité');
    }
    
    return true;
  } catch (error) {
    console.error('Erreur lors de l\'exécution des tests:', error.message);
    return false;
  }
}

// Exécuter la fonction principale
testIdentity().then(() => {
  console.log('\nTests terminés');
  process.exit(0);
}).catch((error) => {
  console.error('Erreur:', error);
  process.exit(1);
});
