{"name": "morpion-louna", "version": "1.0.0", "description": "Jeu du morpion interactif en console", "main": "src/morpion-louna.js", "scripts": {"start": "node src/morpion-louna.js", "test": "mocha tests/**/*.test.js", "test:watch": "mocha tests/**/*.test.js --watch", "test:coverage": "nyc mocha tests/**/*.test.js", "dev": "nodemon src/index.js", "build": "webpack --mode production", "lint": "eslint src/**/*.js", "lint:fix": "eslint src/**/*.js --fix"}, "dependencies": {}, "devDependencies": {"mocha": "^10.0.0", "chai": "^4.3.0", "nyc": "^15.1.0", "nodemon": "^3.0.0", "eslint": "^8.0.0", "webpack": "^5.0.0", "webpack-cli": "^5.0.0"}, "keywords": ["louna-ai", "generated", "jeu"], "author": "LOUNA-AI", "license": "MIT"}