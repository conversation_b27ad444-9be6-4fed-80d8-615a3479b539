{"version": "0.2.0", "configurations": [{"name": "Lancer application", "type": "node", "request": "launch", "program": "${workspaceFolder}/src/morpion-louna.js", "console": "integratedTerminal", "skipFiles": ["<node_internals>/**"]}, {"name": "Déboguer tests", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/mocha/bin/_mocha", "args": ["tests/**/*.test.js"], "console": "integratedTerminal", "skipFiles": ["<node_internals>/**"]}]}