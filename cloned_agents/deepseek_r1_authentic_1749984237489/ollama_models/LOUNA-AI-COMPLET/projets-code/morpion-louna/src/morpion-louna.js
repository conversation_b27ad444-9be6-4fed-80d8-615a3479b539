/**
 * morpion-louna
 * Jeu du morpion interactif en console
 * Créé par LOUNA-AI le 2025-06-04T03:19:11.324Z
 */

// Imports pour le jeu
const readline = require('readline');
const EventEmitter = require('events');

class MorpionLouna extends EventEmitter {
    constructor() {
        super();
        this.version = '1.0.0';
        this.description = 'Jeu du morpion interactif en console';
        this.plateau = this.initialiserPlateau();
        this.joueurActuel = 'X';
        this.partieTerminee = false;
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        console.log('🎮 Jeu initialisé !');
    }

    initialiserPlateau() {
        return [
            [' ', ' ', ' '],
            [' ', ' ', ' '],
            [' ', ' ', ' ']
        ];
    }
    
    afficherPlateau() {
        console.clear();
        console.log('\n🎯 Morpion - LOUNA-AI\n');
        console.log('   0   1   2');
        for (let i = 0; i < 3; i++) {
            console.log(`${i}  ${this.plateau[i][0]} | ${this.plateau[i][1]} | ${this.plateau[i][2]}`);
            if (i < 2) console.log('  ---|---|---');
        }
        console.log(`\nJoueur actuel: ${this.joueurActuel}`);
    }
    
    jouerCoup(ligne, colonne) {
        if (this.partieTerminee) {
            return { success: false, message: 'Partie terminée' };
        }
        
        if (ligne < 0 || ligne > 2 || colonne < 0 || colonne > 2) {
            return { success: false, message: 'Position invalide' };
        }
        
        if (this.plateau[ligne][colonne] !== ' ') {
            return { success: false, message: 'Case déjà occupée' };
        }
        
        this.plateau[ligne][colonne] = this.joueurActuel;
        
        const gagnant = this.verifierGagnant();
        if (gagnant) {
            this.partieTerminee = true;
            this.emit('partie-terminee', { gagnant, type: 'victoire' });
            return { success: true, message: `Joueur ${gagnant} gagne !`, gagnant };
        }
        
        if (this.plateauPlein()) {
            this.partieTerminee = true;
            this.emit('partie-terminee', { gagnant: null, type: 'egalite' });
            return { success: true, message: 'Match nul !', gagnant: null };
        }
        
        this.changerJoueur();
        this.emit('coup-joue', { ligne, colonne, joueur: this.joueurActuel });
        
        return { success: true, message: 'Coup joué' };
    }
    
    verifierGagnant() {
        // Vérifier lignes
        for (let i = 0; i < 3; i++) {
            if (this.plateau[i][0] !== ' ' && 
                this.plateau[i][0] === this.plateau[i][1] && 
                this.plateau[i][1] === this.plateau[i][2]) {
                return this.plateau[i][0];
            }
        }
        
        // Vérifier colonnes
        for (let j = 0; j < 3; j++) {
            if (this.plateau[0][j] !== ' ' && 
                this.plateau[0][j] === this.plateau[1][j] && 
                this.plateau[1][j] === this.plateau[2][j]) {
                return this.plateau[0][j];
            }
        }
        
        // Vérifier diagonales
        if (this.plateau[0][0] !== ' ' && 
            this.plateau[0][0] === this.plateau[1][1] && 
            this.plateau[1][1] === this.plateau[2][2]) {
            return this.plateau[0][0];
        }
        
        if (this.plateau[0][2] !== ' ' && 
            this.plateau[0][2] === this.plateau[1][1] && 
            this.plateau[1][1] === this.plateau[2][0]) {
            return this.plateau[0][2];
        }
        
        return null;
    }
    
    plateauPlein() {
        for (let i = 0; i < 3; i++) {
            for (let j = 0; j < 3; j++) {
                if (this.plateau[i][j] === ' ') {
                    return false;
                }
            }
        }
        return true;
    }
    
    changerJoueur() {
        this.joueurActuel = this.joueurActuel === 'X' ? 'O' : 'X';
    }
    
    async jouer() {
        console.log('🎮 Démarrage du jeu !');
        
        while (!this.partieTerminee) {
            this.afficherPlateau();
            
            try {
                const coup = await this.demanderCoup();
                const resultat = this.jouerCoup(coup.ligne, coup.colonne);
                
                if (!resultat.success) {
                    console.log('❌', resultat.message);
                    continue;
                }
                
                if (resultat.gagnant !== undefined) {
                    this.afficherPlateau();
                    console.log('🎉', resultat.message);
                    break;
                }
                
            } catch (error) {
                console.log('❌ Erreur:', error.message);
            }
        }
        
        this.rl.close();
    }
    
    demanderCoup() {
        return new Promise((resolve, reject) => {
            this.rl.question(`Joueur ${this.joueurActuel}, entrez votre coup (ligne,colonne): `, (reponse) => {
                try {
                    const [ligne, colonne] = reponse.split(',').map(x => parseInt(x.trim()));
                    if (isNaN(ligne) || isNaN(colonne)) {
                        reject(new Error('Format invalide. Utilisez: ligne,colonne (ex: 1,2)'));
                    } else {
                        resolve({ ligne, colonne });
                    }
                } catch (error) {
                    reject(new Error('Format invalide'));
                }
            });
        });
    }
    
    reinitialiser() {
        this.plateau = this.initialiserPlateau();
        this.joueurActuel = 'X';
        this.partieTerminee = false;
        this.emit('jeu-reinitialise');
    }

}

// Export de la classe
module.exports = MorpionLouna;

// Démarrage automatique si exécuté directement
if (require.main === module) {
    const jeu = new MorpionLouna();
    jeu.jouer().catch(console.error);
}
