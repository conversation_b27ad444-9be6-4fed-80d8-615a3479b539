# morpion-louna

## Description
Jeu du morpion interactif en console

## Installation
```bash
npm install
```

## Utilisation
```bash
npm start
```

## Tests
```bash
# Exécuter tous les tests
npm test

# Tests en mode watch
npm run test:watch

# Coverage des tests
npm run test:coverage
```

## Développement
```bash
# Mode développement avec auto-reload
npm run dev

# Linter
npm run lint
npm run lint:fix
```

## Structure du projet
```
morpion-louna/
├── src/           # Code source
├── tests/         # Tests unitaires
├── docs/          # Documentation
├── assets/        # Ressources
└── .vscode/       # Configuration VS Code
```

## Fonctionnalités
- ✅ Code professionnel généré automatiquement
- ✅ Tests unitaires complets
- ✅ Configuration ESLint
- ✅ Scripts npm optimisés
- ✅ Documentation complète

## Créé par
LOUNA-AI - Assistant de développement intelligent

## License
MIT
