/**
 * Tests complets pour morpion-louna
 * Générés automatiquement par LOUNA-AI
 */

const { expect } = require('chai');
const MorpionLouna = require('../src/morpion-louna');

describe('MorpionLouna', () => {
    let instance;
    
    beforeEach(() => {
        instance = new MorpionLouna();
    });
    
    describe('Initialisation', () => {
        it('devrait créer une instance valide', () => {
            expect(instance).to.be.instanceOf(MorpionLouna);
            expect(instance.version).to.exist;
        });
        
        it('devrait avoir les propriétés requises', () => {
            expect(instance.description).to.equal('Jeu du morpion interactif en console');
        });
    });
    
    describe('Fonctionnalités principales', () => {
        
        it('devrait initialiser un plateau vide', () => {
            expect(instance.plateau).to.deep.equal([
                [' ', ' ', ' '],
                [' ', ' ', ' '],
                [' ', ' ', ' ']
            ]);
        });
        
        it('devrait permettre de jouer un coup valide', () => {
            const resultat = instance.jouerCoup(0, 0);
            expect(resultat.success).to.be.true;
            expect(instance.plateau[0][0]).to.equal('X');
        });
        
        it('devrait détecter une victoire', () => {
            instance.plateau = [
                ['X', 'X', 'X'],
                [' ', ' ', ' '],
                [' ', ' ', ' ']
            ];
            const gagnant = instance.verifierGagnant();
            expect(gagnant).to.equal('X');
        });
        
        it('devrait détecter un plateau plein', () => {
            instance.plateau = [
                ['X', 'O', 'X'],
                ['O', 'X', 'O'],
                ['O', 'X', 'O']
            ];
            expect(instance.plateauPlein()).to.be.true;
        });

    });
    
    describe('Gestion des erreurs', () => {
        it('devrait gérer les entrées invalides', () => {
            expect(() => instance.traiterDonnees(null)).to.not.throw();
        });
        
        it('devrait retourner des erreurs appropriées', () => {
            const resultat = instance.traiterDonnees('invalid');
            expect(resultat).to.have.property('error');
        });
    });
    
    describe('Performance', () => {
        it('devrait traiter les données rapidement', () => {
            const start = Date.now();
            instance.traiterDonnees(['test']);
            const duration = Date.now() - start;
            expect(duration).to.be.below(100); // moins de 100ms
        });
    });
});
