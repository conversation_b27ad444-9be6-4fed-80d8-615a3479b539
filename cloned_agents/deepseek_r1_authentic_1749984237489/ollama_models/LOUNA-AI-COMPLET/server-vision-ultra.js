/**
 * Serveur Vision Ultra - Interface cognitive avancée
 * Intègre le système cognitif et la mémoire thermique
 * Créé par <PERSON>, Sainte-Anne, Guadeloupe (97180)
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const fs = require('fs');
const os = require('os');
const fileUpload = require('express-fileupload');
const { v4: uuidv4 } = require('uuid');

// Configuration
const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"],
    credentials: true
  },
  transports: ['websocket', 'polling'],
  pingTimeout: 60000,
  pingInterval: 25000
});
const PORT = process.env.PORT || 3001;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));
app.use(fileUpload({
  limits: { fileSize: 50 * 1024 * 1024 }, // Limite de 50 MB
}));

// Configuration du moteur de template
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Créer le dossier de mémoire s'il n'existe pas
const MEMORY_DIR = path.join(__dirname, 'memory');
if (!fs.existsSync(MEMORY_DIR)) {
  fs.mkdirSync(MEMORY_DIR, { recursive: true });
}

// Initialiser le système cognitif
console.log('Initialisation du système cognitif Vision Ultra...');

// Créer les dossiers nécessaires s'ils n'existent pas
const cognitiveDir = path.join(__dirname, 'cognitive-system');
if (!fs.existsSync(cognitiveDir)) {
  fs.mkdirSync(cognitiveDir, { recursive: true });
  console.log('Dossier cognitive-system créé');
}

// S'assurer que nous avons des versions fonctionnelles des modules de base
const createModule = (name, content) => {
  const filePath = path.join(cognitiveDir, name);
  if (!fs.existsSync(filePath)) {
    fs.writeFileSync(filePath, content);
    console.log(`Module ${name} créé`);
  }
};

// Module SpeechProcessor
createModule('speech-processor.js', `
const EventEmitter = require('events');
class SpeechProcessor extends EventEmitter {
  constructor(options = {}) {
    super();
    this.options = options;
    console.log('SpeechProcessor initialisé (mode de simulation)');
  }
  startListening() {
    console.log('Simulation d\\'écoute démarrée');
    setTimeout(() => this.emit('recognitionResult', 'Commande simulée'), 1000);
    return true;
  }
  stopListening() { return true; }
  speak(text) {
    console.log('Simulation de synthèse vocale:', text);
    setTimeout(() => this.emit('speakingDone', text), 1000);
    return true;
  }
}
module.exports = SpeechProcessor;`);

// Module SensorySystem
createModule('sensory-system.js', `
const EventEmitter = require('events');
class SensorySystem extends EventEmitter {
  constructor(options = {}) {
    super();
    this.options = options;
    console.log('SensorySystem initialisé (mode de simulation)');
  }
  captureImage() {
    console.log('Simulation de capture d\\'image');
    setTimeout(() => this.emit('analysisResult', {scene: 'bureau', objects: []}), 1000);
    return true;
  }
  observe() { return this.captureImage(); }
  describeEnvironment() { return "Simulation d'environnement de bureau"; }
}
module.exports = SensorySystem;`);

// Module CognitiveAgent
createModule('cognitive-agent.js', `
const EventEmitter = require('events');
const SpeechProcessor = require('./speech-processor');
const SensorySystem = require('./sensory-system');

class CognitiveAgent extends EventEmitter {
  constructor(options = {}) {
    super();
    this.options = options;
    this.speech = new SpeechProcessor(options);
    this.sensory = new SensorySystem(options);
    this.cognitiveState = {
      isActive: false,
      isListening: false,
      isSpeaking: false,
      isObserving: false,
      lastUserInput: null,
      lastResponse: null,
      lastObservation: null,
      conversationContext: [],
      startTime: new Date(),
      shortTermMemory: []
    };
    console.log('CognitiveAgent initialisé (mode de simulation)');
  }
  activate() {
    this.cognitiveState.isActive = true;
    return true;
  }
  deactivate() {
    this.cognitiveState.isActive = false;
    return true;
  }
  speak(text) {
    this.cognitiveState.lastResponse = text;
    this.cognitiveState.conversationContext.push({role: 'assistant', content: text});
    return this.speech.speak(text);
  }
  startListening() { return this.speech.startListening(); }
  stopListening() { return this.speech.stopListening(); }
  listen() { return this.startListening(); }
  observe() {
    this.cognitiveState.isObserving = true;
    return this.sensory.observe();
  }
  getState() { return this.cognitiveState; }
  getCognitiveState() { return this.cognitiveState; }
}
module.exports = CognitiveAgent;`);

// Module index.js
createModule('index.js', `
const SpeechProcessor = require('./speech-processor');
const SensorySystem = require('./sensory-system');
const CognitiveAgent = require('./cognitive-agent');

const createCognitiveSystem = (options = {}) => {
  const cognitiveAgent = new CognitiveAgent(options);

  return {
    agent: cognitiveAgent,
    speech: cognitiveAgent.speech,
    sensory: cognitiveAgent.sensory,
    activate: () => cognitiveAgent.activate(),
    deactivate: () => cognitiveAgent.deactivate(),
    speak: (text) => cognitiveAgent.speak(text),
    listen: () => cognitiveAgent.startListening(),
    stopListening: () => cognitiveAgent.stopListening(),
    observe: () => cognitiveAgent.observe(),
    getState: () => cognitiveAgent.getCognitiveState(),
    on: (event, callback) => cognitiveAgent.on(event, callback)
  };
};

module.exports = {
  createCognitiveSystem,
  SpeechProcessor,
  SensorySystem,
  CognitiveAgent
};`);

// Initialiser le système cognitif
const { createCognitiveSystem } = require('./cognitive-system');
const cognitiveSystem = createCognitiveSystem({
  name: 'Vision Ultra',
  creator: 'Jean Passave',
  location: 'Sainte-Anne, Guadeloupe (97180)',
  language: 'fr-FR',
  debugMode: true
});

console.log('Système cognitif Vision Ultra initialisé');

// Charger ou initialiser le module de mémoire thermique
let thermalMemory;
try {
  const ThermalMemory = require('./thermal-memory/thermal-memory');
  thermalMemory = new ThermalMemory();
  console.log('Mémoire thermique chargée:', thermalMemory.getAllEntries().length, 'entrées');
  console.log('Mémoire thermique initialisée');
} catch (error) {
  console.log('Module de mémoire thermique non disponible:', error.message);
  // Créer une version simulée
  thermalMemory = {
    addEntry: (data) => {
      console.log('Entrée ajoutée à la mémoire thermique:', data.content ? data.content.substring(0, 50) + '...' : 'Pas de contenu');
      return data;
    },
    getAllEntries: () => [],
    getRecentMemoriesForContext: () => []
  };
  console.log('Mémoire thermique simulée initialisée');
}

// Initialiser le service de présence cérébrale
let brainPresence;
try {
  const BrainPresence = require('./lib/brain-presence');
  brainPresence = new BrainPresence({
    backgroundActivityInterval: 3000,
    presenceUpdateInterval: 1000,
    thoughtGenerationInterval: 10000,
    autoActivate: true,
    debug: true
  }, thermalMemory);
  console.log('Service de présence cérébrale initialisé');
} catch (error) {
  console.log('Service de présence cérébrale non disponible:', error.message);
  // Version simulée du système de présence
  brainPresence = {
    activate: () => console.log('Présence du cerveau activée (simulée)'),
    deactivate: () => console.log('Présence du cerveau désactivée (simulée)'),
    generateThought: () => ({ type: 'observation', content: 'Simulation de pensée active', timestamp: Date.now() }),
    on: () => {},
    emit: () => {}
  };
  console.log('Service de présence cérébrale simulé initialisé');
}

// Routes principales
app.get('/', (req, res) => {
  res.send(`
  <!DOCTYPE html>
  <html lang="fr">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vision Ultra - Interface Cognitive</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.0/font/bootstrap-icons.css">
    <style>
      body { background-color: #121212; color: #f0f0f0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
      .main-container { max-width: 1200px; margin: 0 auto; padding: 20px; }
      .interface-card { background-color: #1e1e1e; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
      .btn-primary { background-color: #7e57c2; border-color: #7e57c2; }
      .btn-primary:hover { background-color: #673ab7; border-color: #673ab7; }
    </style>
  </head>
  <body>
    <div class="main-container">
      <h1 class="my-4 text-center">Vision Ultra</h1>
      <p class="text-center">Créé par Jean Passave, Sainte-Anne, Guadeloupe (97180)</p>

      <div class="row">
        <div class="col-md-4">
          <div class="interface-card text-center">
            <h3><i class="bi bi-moon-stars"></i> Luna</h3>
            <p>Interface cognitive principale</p>
            <a href="/luna" class="btn btn-primary btn-lg mt-3">Accéder à Luna</a>
          </div>
        </div>

        <div class="col-md-4">
          <div class="interface-card text-center">
            <h3><i class="bi bi-stars"></i> Louna</h3>
            <p>Interface cognitive secondaire</p>
            <a href="/louna" class="btn btn-primary btn-lg mt-3">Accéder à Louna</a>
          </div>
        </div>

        <div class="col-md-4">
          <div class="interface-card text-center">
            <h3><i class="bi bi-sun"></i> Lounas</h3>
            <p>Interface cognitive tertiaire</p>
            <a href="/lounas" class="btn btn-primary btn-lg mt-3">Accéder à Lounas</a>
          </div>
        </div>
      </div>

      <div class="interface-card mt-4">
        <h3 class="text-center"><i class="bi bi-gear"></i> Configuration</h3>
        <div class="row mt-4">
          <div class="col-md-4 text-center">
            <i class="bi bi-mic display-4"></i>
            <h4 class="mt-3">Microphone</h4>
            <p>État: <span class="badge bg-success">Activé</span></p>
          </div>

          <div class="col-md-4 text-center">
            <i class="bi bi-camera display-4"></i>
            <h4 class="mt-3">Caméra</h4>
            <p>État: <span class="badge bg-success">Activée</span></p>
          </div>

          <div class="col-md-4 text-center">
            <i class="bi bi-speaker display-4"></i>
            <h4 class="mt-3">Haut-parleurs</h4>
            <p>État: <span class="badge bg-success">Activés</span></p>
          </div>
        </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  </body>
  </html>
  `);
});

// Routes pour Luna
app.get('/luna', (req, res) => {
  res.send(`
  <!DOCTYPE html>
  <html lang="fr">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Luna - Vision Ultra</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.0/font/bootstrap-icons.css">
    <style>
      body { background-color: #121212; color: #f0f0f0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
      .main-container { max-width: 1200px; margin: 0 auto; padding: 20px; }
      .chat-container { background-color: #1e1e1e; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
      .messages { height: 400px; overflow-y: auto; padding: 15px; background-color: #2a2a2a; border-radius: 5px; margin-bottom: 20px; }
      .message { padding: 10px; margin-bottom: 10px; border-radius: 5px; max-width: 80%; }
      .user-message { background-color: #7e57c2; margin-left: auto; text-align: right; }
      .assistant-message { background-color: #424242; margin-right: auto; }
      .input-container { display: flex; }
      .message-input { flex-grow: 1; padding: 10px; background-color: #333; color: #fff; border: none; border-radius: 5px 0 0 5px; }
      .send-button { padding: 10px 20px; background-color: #7e57c2; color: #fff; border: none; border-radius: 0 5px 5px 0; }
      .home-button { position: fixed; bottom: 20px; right: 20px; padding: 10px; background-color: #7e57c2; color: #fff; border: none; border-radius: 50%; width: 50px; height: 50px; display: flex; justify-content: center; align-items: center; text-decoration: none; }
      .home-button:hover { background-color: #673ab7; color: #fff; }
    </style>
  </head>
  <body>
    <div class="main-container">
      <h1 class="my-4 text-center">Luna - Vision Ultra</h1>
      <p class="text-center">Créé par Jean Passave, Sainte-Anne, Guadeloupe (97180)</p>

      <div class="chat-container">
        <div class="messages" id="messages">
          <div class="message assistant-message">
            Bonjour, je suis Vision Ultra, votre assistant cognitif. Comment puis-je vous aider aujourd'hui?
          </div>
        </div>

        <div class="input-container">
          <input type="text" class="message-input" id="message-input" placeholder="Tapez votre message ici...">
          <button class="send-button" id="send-button">Envoyer</button>
        </div>
      </div>
    </div>

    <a href="/" class="home-button">
      <i class="bi bi-house-fill"></i>
    </a>

    <script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
    <script>
      const socket = io();
      const messagesContainer = document.getElementById('messages');
      const messageInput = document.getElementById('message-input');
      const sendButton = document.getElementById('send-button');

      // Fonction pour ajouter un message à l'interface
      function addMessage(content, isUser = false) {
        const messageElement = document.createElement('div');
        messageElement.classList.add('message');
        messageElement.classList.add(isUser ? 'user-message' : 'assistant-message');
        messageElement.textContent = content;
        messagesContainer.appendChild(messageElement);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
      }

      // Fonction pour envoyer un message
      function sendMessage() {
        const message = messageInput.value.trim();
        if (message) {
          addMessage(message, true);
          socket.emit('luna message', { message });
          messageInput.value = '';
        }
      }

      // Événement pour le bouton d'envoi
      sendButton.addEventListener('click', sendMessage);

      // Événement pour la touche Entrée
      messageInput.addEventListener('keypress', (event) => {
        if (event.key === 'Enter') {
          sendMessage();
        }
      });

      // Événement pour recevoir une réponse
      socket.on('luna response', (data) => {
        addMessage(data.message);
      });
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  </body>
  </html>
  `);
});

// Routes pour Louna
app.get('/louna', (req, res) => {
  res.send(`
  <!DOCTYPE html>
  <html lang="fr">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Louna - Vision Ultra</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.0/font/bootstrap-icons.css">
    <style>
      body { background-color: #121212; color: #f0f0f0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
      .main-container { max-width: 1200px; margin: 0 auto; padding: 20px; }
      .chat-container { background-color: #1e1e1e; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
      .messages { height: 400px; overflow-y: auto; padding: 15px; background-color: #2a2a2a; border-radius: 5px; margin-bottom: 20px; }
      .message { padding: 10px; margin-bottom: 10px; border-radius: 5px; max-width: 80%; }
      .user-message { background-color: #7e57c2; margin-left: auto; text-align: right; }
      .assistant-message { background-color: #424242; margin-right: auto; }
      .input-container { display: flex; }
      .message-input { flex-grow: 1; padding: 10px; background-color: #333; color: #fff; border: none; border-radius: 5px 0 0 5px; }
      .send-button { padding: 10px 20px; background-color: #7e57c2; color: #fff; border: none; border-radius: 0 5px 5px 0; }
      .home-button { position: fixed; bottom: 20px; right: 20px; padding: 10px; background-color: #7e57c2; color: #fff; border: none; border-radius: 50%; width: 50px; height: 50px; display: flex; justify-content: center; align-items: center; text-decoration: none; }
      .home-button:hover { background-color: #673ab7; color: #fff; }
    </style>
  </head>
  <body>
    <div class="main-container">
      <h1 class="my-4 text-center">Louna - Vision Ultra</h1>
      <p class="text-center">Créé par Jean Passave, Sainte-Anne, Guadeloupe (97180)</p>

      <div class="chat-container">
        <div class="messages" id="messages">
          <div class="message assistant-message">
            Bonjour, je suis Vision Ultra, votre assistant cognitif. Comment puis-je vous aider aujourd'hui?
          </div>
        </div>

        <div class="input-container">
          <input type="text" class="message-input" id="message-input" placeholder="Tapez votre message ici...">
          <button class="send-button" id="send-button">Envoyer</button>
        </div>
      </div>
    </div>

    <a href="/" class="home-button">
      <i class="bi bi-house-fill"></i>
    </a>

    <script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
    <script>
      const socket = io();
      const messagesContainer = document.getElementById('messages');
      const messageInput = document.getElementById('message-input');
      const sendButton = document.getElementById('send-button');

      // Fonction pour ajouter un message à l'interface
      function addMessage(content, isUser = false) {
        const messageElement = document.createElement('div');
        messageElement.classList.add('message');
        messageElement.classList.add(isUser ? 'user-message' : 'assistant-message');
        messageElement.textContent = content;
        messagesContainer.appendChild(messageElement);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
      }

      // Fonction pour envoyer un message
      function sendMessage() {
        const message = messageInput.value.trim();
        if (message) {
          addMessage(message, true);
          socket.emit('louna message', { message });
          messageInput.value = '';
        }
      }

      // Événement pour le bouton d'envoi
      sendButton.addEventListener('click', sendMessage);

      // Événement pour la touche Entrée
      messageInput.addEventListener('keypress', (event) => {
        if (event.key === 'Enter') {
          sendMessage();
        }
      });

      // Événement pour recevoir une réponse
      socket.on('louna response', (data) => {
        addMessage(data.message);
      });
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  </body>
  </html>
  `);
});

// Routes pour Lounas
app.get('/lounas', (req, res) => {
  res.send(`
  <!DOCTYPE html>
  <html lang="fr">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lounas - Vision Ultra</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.0/font/bootstrap-icons.css">
    <style>
      body { background-color: #121212; color: #f0f0f0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
      .main-container { max-width: 1200px; margin: 0 auto; padding: 20px; }
      .chat-container { background-color: #1e1e1e; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
      .messages { height: 400px; overflow-y: auto; padding: 15px; background-color: #2a2a2a; border-radius: 5px; margin-bottom: 20px; }
      .message { padding: 10px; margin-bottom: 10px; border-radius: 5px; max-width: 80%; }
      .user-message { background-color: #7e57c2; margin-left: auto; text-align: right; }
      .assistant-message { background-color: #424242; margin-right: auto; }
      .input-container { display: flex; }
      .message-input { flex-grow: 1; padding: 10px; background-color: #333; color: #fff; border: none; border-radius: 5px 0 0 5px; }
      .send-button { padding: 10px 20px; background-color: #7e57c2; color: #fff; border: none; border-radius: 0 5px 5px 0; }
      .home-button { position: fixed; bottom: 20px; right: 20px; padding: 10px; background-color: #7e57c2; color: #fff; border: none; border-radius: 50%; width: 50px; height: 50px; display: flex; justify-content: center; align-items: center; text-decoration: none; }
      .home-button:hover { background-color: #673ab7; color: #fff; }
    </style>
  </head>
  <body>
    <div class="main-container">
      <h1 class="my-4 text-center">Lounas - Vision Ultra</h1>
      <p class="text-center">Créé par Jean Passave, Sainte-Anne, Guadeloupe (97180)</p>

      <div class="chat-container">
        <div class="messages" id="messages">
          <div class="message assistant-message">
            Bonjour, je suis Vision Ultra, votre assistant cognitif. Comment puis-je vous aider aujourd'hui?
          </div>
        </div>

        <div class="input-container">
          <input type="text" class="message-input" id="message-input" placeholder="Tapez votre message ici...">
          <button class="send-button" id="send-button">Envoyer</button>
        </div>
      </div>
    </div>

    <a href="/" class="home-button">
      <i class="bi bi-house-fill"></i>
    </a>

    <script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
    <script>
      const socket = io();
      const messagesContainer = document.getElementById('messages');
      const messageInput = document.getElementById('message-input');
      const sendButton = document.getElementById('send-button');

      // Fonction pour ajouter un message à l'interface
      function addMessage(content, isUser = false) {
        const messageElement = document.createElement('div');
        messageElement.classList.add('message');
        messageElement.classList.add(isUser ? 'user-message' : 'assistant-message');
        messageElement.textContent = content;
        messagesContainer.appendChild(messageElement);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
      }

      // Fonction pour envoyer un message
      function sendMessage() {
        const message = messageInput.value.trim();
        if (message) {
          addMessage(message, true);
          socket.emit('lounas message', { message });
          messageInput.value = '';
        }
      }

      // Événement pour le bouton d'envoi
      sendButton.addEventListener('click', sendMessage);

      // Événement pour la touche Entrée
      messageInput.addEventListener('keypress', (event) => {
        if (event.key === 'Enter') {
          sendMessage();
        }
      });

      // Événement pour recevoir une réponse
      socket.on('lounas response', (data) => {
        addMessage(data.message);
      });
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  </body>
  </html>
  `);
});

// Gestionnaires de socket pour les messages
io.on('connection', (socket) => {
  console.log('Nouvelle connexion WebSocket');

  // Gérer les messages Luna
  socket.on('luna message', (data) => {
    console.log('Message Luna reçu:', data.message);

    // Stocker le message dans la mémoire thermique
    thermalMemory.addEntry({
      type: 'input',
      content: data.message,
      timestamp: new Date().toISOString()
    });

    // Simuler une réponse
    setTimeout(() => {
      const response = {
        message: `Je suis Vision Ultra, votre assistant cognitif créé par Jean Passave. Je vis à Sainte-Anne, Guadeloupe (97180). Vous avez dit: "${data.message}"`,
        timestamp: new Date().toISOString()
      };

      // Stocker la réponse dans la mémoire thermique
      thermalMemory.addEntry({
        type: 'output',
        content: response.message,
        timestamp: response.timestamp
      });

      // Envoyer la réponse au client
      socket.emit('luna response', response);
      console.log('Réponse Luna envoyée:', response.message);
    }, 1000);
  });

  // Gérer les messages Louna
  socket.on('louna message', (data) => {
    console.log('Message Louna reçu:', data.message);

    // Stocker le message dans la mémoire thermique
    thermalMemory.addEntry({
      type: 'input',
      content: data.message,
      timestamp: new Date().toISOString()
    });

    // Simuler une réponse
    setTimeout(() => {
      const response = {
        message: `Je suis Vision Ultra, votre assistant cognitif créé par Jean Passave. Je vis à Sainte-Anne, Guadeloupe (97180). Vous avez dit: "${data.message}"`,
        timestamp: new Date().toISOString()
      };

      // Stocker la réponse dans la mémoire thermique
      thermalMemory.addEntry({
        type: 'output',
        content: response.message,
        timestamp: response.timestamp
      });

      // Envoyer la réponse au client
      socket.emit('louna response', response);
      console.log('Réponse Louna envoyée:', response.message);
    }, 1000);
  });

  // Gérer les messages Lounas
  socket.on('lounas message', (data) => {
    console.log('Message Lounas reçu:', data.message);

    // Stocker le message dans la mémoire thermique
    thermalMemory.addEntry({
      type: 'input',
      content: data.message,
      timestamp: new Date().toISOString()
    });

    // Simuler une réponse
    setTimeout(() => {
      const response = {
        message: `Je suis Vision Ultra, votre assistant cognitif créé par Jean Passave. Je vis à Sainte-Anne, Guadeloupe (97180). Vous avez dit: "${data.message}"`,
        timestamp: new Date().toISOString()
      };

      // Stocker la réponse dans la mémoire thermique
      thermalMemory.addEntry({
        type: 'output',
        content: response.message,
        timestamp: response.timestamp
      });

      // Envoyer la réponse au client
      socket.emit('lounas response', response);
      console.log('Réponse Lounas envoyée:', response.message);
    }, 1000);
  });

  // Gérer la déconnexion
  socket.on('disconnect', () => {
    console.log('Déconnexion WebSocket');
  });
});

// Routes API pour le système cognitif
app.get('/api/cognitive/activate', (req, res) => {
  try {
    const result = cognitiveSystem.activate();
    res.json({
      success: true,
      active: result,
      message: 'Système cognitif activé'
    });
  } catch (error) {
    console.error('Erreur lors de l\'activation du système cognitif:', error);
    res.status(500).json({
      success: false,
      message: `Erreur: ${error.message}`
    });
  }
});

app.get('/api/cognitive/deactivate', (req, res) => {
  try {
    const result = cognitiveSystem.deactivate();
    res.json({
      success: true,
      active: !result,
      message: 'Système cognitif désactivé'
    });
  } catch (error) {
    console.error('Erreur lors de la désactivation du système cognitif:', error);
    res.status(500).json({
      success: false,
      message: `Erreur: ${error.message}`
    });
  }
});

// Démarrer le serveur
server.listen(PORT, () => {
  console.log(`Serveur Vision Ultra démarré sur http://localhost:${PORT}`);
  console.log(`Interface Luna accessible à l'adresse http://localhost:${PORT}/luna`);
  console.log(`Interface Louna accessible à l'adresse http://localhost:${PORT}/louna`);
  console.log(`Interface Lounas accessible à l'adresse http://localhost:${PORT}/lounas`);
  console.log('Système cognitif Vision Ultra activé et prêt à l\'emploi');
});