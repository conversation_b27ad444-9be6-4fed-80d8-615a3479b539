#!/usr/bin/env node

/**
 * SERVEUR LOUNA AVEC INTERFACE VALIDÉE + COEFFICIENT INTELLECTUEL
 * Interface noir-rose validée par <PERSON><PERSON> + QI évolutif
 */

const express = require('express');
const path = require('path');
const { MemoireThermiqueVivante } = require('./memoire-thermique-vivante');
const { RechercheGoogleSecurisee } = require('./recherche-google-securisee');
const axios = require('axios');

const app = express();
const PORT = 8080;

// Middleware
app.use(express.json());
app.use(express.static('.'));

// Variables globales
let memoireVivante;
let rechercheGoogle;
let isInitialized = false;
let coefficientIntellectuel = 127; // QI de base
let evolutionQI = 0;

// CALCUL COEFFICIENT INTELLECTUEL ÉVOLUTIF
function calculerQIEvolutif() {
    if (!memoireVivante) return coefficientIntellectuel;
    
    const stats = memoireVivante.getStats();
    
    // Facteurs d'évolution du QI
    const totalNeurones = stats.neurones_system?.total_installed || stats.totalNeurones || 201000000;
        const accelerateursActifs = stats.kyber_accelerators?.active_count || stats.accelerateursActifs || 3;
        const totalMemories = stats.totalEntries || stats.totalMemories || 0;
        const temperatureMoyenne = stats.averageTemperature || stats.temperatureMoyenne || 0.5;
        
        const facteurNeurones = Math.min(totalNeurones / 1000000, 50); // Max +50 pour neurones
    const facteurAccelerateurs = accelerateursActifs * 3; // +3 par accélérateur
    const facteurMemoire = Math.min(totalMemories / 10, 20); // Max +20 pour mémoires
    const facteurTemperature = temperatureMoyenne > 0.5 ? 10 : 5; // Bonus température
    
    // Bonus recherche Google sécurisée
    let bonusGoogle = 0;
    if (rechercheGoogle) {
        const statsGoogle = rechercheGoogle.getStats();
        bonusGoogle = Math.min(statsGoogle.recherchesSecurisees * 0.5, 15); // Max +15
    }
    
    // Calcul QI total
    const nouveauQI = Math.floor(
        127 + 
        facteurNeurones + 
        facteurAccelerateurs + 
        facteurMemoire + 
        facteurTemperature + 
        bonusGoogle
    );
    
    // Limiter l'évolution (max 300 QI)
    return Math.min(nouveauQI, 300);
}

// INITIALISATION
async function initializeSystem() {
    try {
        console.log('🚀 INITIALISATION LOUNA AVEC INTERFACE VALIDÉE');
        console.log('===============================================');
        
        // Initialiser mémoire thermique vivante
        console.log('🧠 Initialisation mémoire thermique vivante...');
        memoireVivante = new MemoireThermiqueVivante({
            kyber_auto_install: true,
            neurones_auto_install: true,
            neurones_max_limit: false,
            auto_update_enabled: true,
            update_interval: 300000,
            verification_interval: 600000
        });

        // Initialiser recherche Google sécurisée
        console.log('🔍 Initialisation recherche Google sécurisée...');
        rechercheGoogle = new RechercheGoogleSecurisee();
        console.log('✅ Recherche Google sécurisée initialisée');

        // Attendre initialisation
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Ajouter informations de base
        memoireVivante.add(
            'louna_presentation',
            'LOUNA-AI est un système d\'intelligence artificielle avec mémoire thermique vivante créé par Jean-Luc Passave à Sainte-Anne, Guadeloupe. Le système utilise des neurones qui s\'ajoutent automatiquement à l\'infini et des accélérateurs KYBER pour optimiser les performances.',
            0.9,
            'important'
        );

        // Calculer QI initial
        coefficientIntellectuel = calculerQIEvolutif();
        console.log(`🧠 Coefficient intellectuel initial: ${coefficientIntellectuel}`);

        isInitialized = true;
        console.log('✅ LOUNA AVEC INTERFACE VALIDÉE INITIALISÉE');

    } catch (error) {
        console.error('❌ Erreur initialisation:', error.message);
    }
}

// ROUTE INTERFACE PRINCIPALE (LA BONNE)
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'interface-louna-grande.html'));
});

// ROUTE CHAT PRINCIPAL
app.post('/chat', async (req, res) => {
    try {
        const { message } = req.body;
        
        if (!message) {
            return res.status(400).json({ 
                success: false, 
                error: 'Message manquant' 
            });
        }

        if (!isInitialized || !memoireVivante) {
            return res.status(503).json({ 
                success: false, 
                error: 'Système non initialisé' 
            });
        }

        console.log(`💬 Question reçue: "${message}"`);

        // 1. RECHERCHER DANS LA MÉMOIRE THERMIQUE VIVANTE
        console.log('🧠 Recherche dans mémoire thermique...');
        const memoireResults = memoireVivante.retrieve(message, 3);
        
        let response = '';
        let source = '';

        if (memoireResults.length > 0) {
            // Informations trouvées en mémoire
            const bestResult = memoireResults[0];
            
            console.log(`✅ Information trouvée en mémoire: ${bestResult.key}`);
            
            if (bestResult.freshness > 0.6 && bestResult.doubt_level < 0.3) {
                response = `D'après ma mémoire thermique : ${bestResult.content}`;
                source = `Mémoire thermique (fraîcheur: ${(bestResult.freshness * 100).toFixed(1)}%)`;
            } else {
                console.log('⚠️ Information en mémoire douteuse, recherche mise à jour...');
                
                try {
                    const updatedInfo = await searchInternetForUpdate(message);
                    if (updatedInfo) {
                        response = updatedInfo;
                        source = 'Google sécurisé (information mise à jour)';
                        
                        memoireVivante.add(
                            `updated_${Date.now()}`,
                            updatedInfo,
                            0.8,
                            'actualite'
                        );
                    } else {
                        response = `D'après ma mémoire thermique (à vérifier) : ${bestResult.content}`;
                        source = `Mémoire thermique (information à vérifier)`;
                    }
                } catch (error) {
                    response = `D'après ma mémoire thermique : ${bestResult.content}`;
                    source = 'Mémoire thermique (vérification Internet échouée)';
                }
            }
        } else {
            // 2. RECHERCHER AVEC GOOGLE SÉCURISÉ
            console.log('🌐 Aucune information en mémoire, recherche Google sécurisée...');
            
            try {
                const internetInfo = await searchInternetForUpdate(message);
                if (internetInfo) {
                    response = internetInfo;
                    source = 'Google sécurisé (nouvelle information)';
                    
                    memoireVivante.add(
                        `google_${Date.now()}`,
                        internetInfo,
                        0.7,
                        'recherche_google'
                    );
                } else {
                    // 3. FALLBACK VERS OLLAMA
                    console.log('🤖 Fallback vers agent Ollama...');
                    const ollamaResponse = await queryOllama(message);
                    response = ollamaResponse;
                    source = 'Agent Ollama local';
                    
                    memoireVivante.add(
                        `ollama_${Date.now()}`,
                        ollamaResponse,
                        0.6,
                        'general'
                    );
                }
            } catch (error) {
                console.log('❌ Erreur recherche Google, fallback Ollama...');
                try {
                    const ollamaResponse = await queryOllama(message);
                    response = ollamaResponse;
                    source = 'Agent Ollama local (Google indisponible)';
                } catch (ollamaError) {
                    response = 'Désolé, je ne peux pas répondre à cette question pour le moment.';
                    source = 'Erreur système';
                }
            }
        }

        // Recalculer QI après chaque interaction
        const ancienQI = coefficientIntellectuel;
        coefficientIntellectuel = calculerQIEvolutif();
        
        if (coefficientIntellectuel > ancienQI) {
            console.log(`🧠 ÉVOLUTION QI: ${ancienQI} → ${coefficientIntellectuel} (+${coefficientIntellectuel - ancienQI})`);
        }

        res.json({
            success: true,
            response: response,
            source: source,
            memory_used: memoireResults.length > 0,
            qi_actuel: coefficientIntellectuel,
            evolution_qi: coefficientIntellectuel - ancienQI,
            timestamp: Date.now()
        });

    } catch (error) {
        console.error('❌ Erreur chat:', error.message);
        res.status(500).json({ 
            success: false, 
            error: error.message 
        });
    }
});

// FONCTION RECHERCHE INTERNET SÉCURISÉE
async function searchInternetForUpdate(query) {
    try {
        console.log(`🔍 Recherche Internet sécurisée pour: "${query}"`);
        
        // RÉPONSES SIMPLES DIRECTES
        const simpleAnswer = getSimpleAnswer(query);
        if (simpleAnswer) {
            console.log(`✅ Réponse simple trouvée: ${simpleAnswer}`);
            return simpleAnswer;
        }
        
        // RECHERCHE GOOGLE SÉCURISÉE
        if (rechercheGoogle) {
            console.log('🔍 Utilisation recherche Google sécurisée...');
            const resultatsGoogle = await rechercheGoogle.rechercherSecurise(query, 3);
            
            if (resultatsGoogle && resultatsGoogle.length > 0) {
                const meilleurResultat = resultatsGoogle[0];
                const reponse = `${meilleurResultat.description} (Source: ${meilleurResultat.source} - Sécurité: ${meilleurResultat.scoreSecurite}/100)`;
                
                console.log(`✅ Résultat Google sécurisé trouvé: ${meilleurResultat.titre}`);
                return reponse;
            }
        }
        
        return null;
    } catch (error) {
        console.log(`❌ Erreur recherche Internet: ${error.message}`);
        return null;
    }
}

// RÉPONSES SIMPLES
function getSimpleAnswer(query) {
    const lowerQuery = query.toLowerCase();
    
    // Mathématiques simples
    const mathMatch = lowerQuery.match(/(\d+)\s*\+\s*(\d+)/);
    if (mathMatch) {
        const result = parseInt(mathMatch[1]) + parseInt(mathMatch[2]);
        return `${mathMatch[1]} + ${mathMatch[2]} = ${result}`;
    }
    
    // Informations sur LOUNA
    if (lowerQuery.includes('louna') || lowerQuery.includes('qui es-tu')) {
        return `Je suis LOUNA-AI, créée par Jean-Luc Passave à Sainte-Anne, Guadeloupe. Mon coefficient intellectuel actuel est de ${coefficientIntellectuel} et évolue constamment grâce à ma mémoire thermique vivante et mes accélérateurs KYBER.`;
    }
    
    return null;
}

// FONCTION QUERY OLLAMA
async function queryOllama(message) {
    try {
        const response = await axios.post('http://localhost:11434/api/generate', {
            model: 'llama3.2:1b',
            prompt: message,
            stream: false
        }, {
            timeout: 30000
        });

        if (response.data && response.data.response) {
            return response.data.response;
        }

        return 'Désolé, je n\'ai pas pu générer une réponse appropriée.';
    } catch (error) {
        console.log(`❌ Erreur Ollama: ${error.message}`);
        throw error;
    }
}

// ROUTE STATISTIQUES AVEC QI
app.get('/stats', (req, res) => {
    try {
        if (!isInitialized || !memoireVivante) {
            return res.status(503).json({ 
                success: false, 
                error: 'Système non initialisé' 
            });
        }

        const stats = memoireVivante.getStats();
        const statsGoogle = rechercheGoogle ? rechercheGoogle.getStats() : null;
        
        res.json({
            success: true,
            stats: stats,
            stats_google: statsGoogle,
            coefficient_intellectuel: coefficientIntellectuel,
            evolution_qi: evolutionQI,
            timestamp: Date.now()
        });

    } catch (error) {
        console.error('❌ Erreur stats:', error.message);
        res.status(500).json({ 
            success: false, 
            error: error.message 
        });
    }
});

// DÉMARRAGE SERVEUR
app.listen(PORT, async () => {
    console.log(`🌐 Serveur LOUNA démarré sur http://localhost:${PORT}`);
    console.log('🎨 Interface validée noir-rose chargée');
    console.log('🧠 Coefficient intellectuel évolutif activé');
    console.log('🔍 Recherche Google ultra-sécurisée intégrée');
    
    await initializeSystem();
    
    console.log('\n✅ LOUNA AVEC INTERFACE VALIDÉE OPÉRATIONNELLE');
    console.log('==============================================');
    console.log('🔗 Interface: http://localhost:8080');
    console.log(`🧠 QI actuel: ${coefficientIntellectuel}`);
    console.log('🎨 Design: Interface validée noir-rose');
});

// ARRÊT PROPRE
process.on('SIGINT', async () => {
    console.log('\n🛑 Arrêt LOUNA en cours...');
    
    if (memoireVivante) {
        await memoireVivante.saveMemoryState();
        console.log('💾 Mémoire sauvegardée');
    }
    
    console.log(`🧠 QI final: ${coefficientIntellectuel}`);
    console.log('✅ Arrêt propre terminé');
    process.exit(0);
});
