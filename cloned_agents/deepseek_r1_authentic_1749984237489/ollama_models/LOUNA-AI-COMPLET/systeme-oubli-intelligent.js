/**
 * SYSTÈME D'OUBLI INTELLIGENT POUR LOUNA-AI
 * Doute systématique et mise à jour automatique des connaissances
 */

const fs = require('fs');
const path = require('path');

class SystemeOubliIntelligent {
    constructor(memoireThermique = null) {
        this.memoireThermique = memoireThermique;
        this.derniereVerification = new Map();
        this.informationsObsoletes = new Map();
        this.historiqueVerifications = [];
        
        // Catégories avec fréquences de vérification (en heures)
        this.categoriesVerification = {
            'actualité': 1,      // Actualité : 1h
            'technologie': 6,    // Technologie : 6h  
            'science': 12,       // Science : 12h
            'économie': 6,       // Économie : 6h
            'politique': 2,      // Politique : 2h
            'sport': 4,          // Sport : 4h
            'météo': 1,          // Météo : 1h
            'bourse': 1,         // Bourse : 1h
            'général': 24        // Général : 24h
        };
        
        // Seuils de similarité pour détecter les changements
        this.seuilSimilarite = 0.7; // 70% de similarité minimum
        
        this.demarrerVerificationAutomatique();
    }

    // DÉMARRER VÉRIFICATION AUTOMATIQUE
    demarrerVerificationAutomatique() {
        console.log('🧠 Démarrage du système d\'oubli intelligent...');
        
        // Vérification générale toutes les 2 heures
        setInterval(() => {
            this.verifierInformationsObsoletes();
        }, 2 * 60 * 60 * 1000); // 2 heures
        
        // Vérification catégories critiques toutes les 30 minutes
        setInterval(() => {
            this.verifierCategoriesCritiques();
        }, 30 * 60 * 1000); // 30 minutes
        
        // Doute systématique toutes les 10 minutes
        setInterval(() => {
            this.douteSystematique();
        }, 10 * 60 * 1000); // 10 minutes
        
        console.log('✅ Système d\'oubli intelligent démarré');
    }

    // VÉRIFIER INFORMATIONS OBSOLÈTES
    async verifierInformationsObsoletes() {
        if (!this.memoireThermique) {
            console.log('⚠️ Pas de mémoire thermique connectée');
            return;
        }

        console.log('🔍 Vérification des informations obsolètes...');
        
        try {
            const memoires = this.memoireThermique.obtenirToutesLesMemoires();
            let verificationsEffectuees = 0;
            let informationsMisesAJour = 0;
            
            for (const [id, memoire] of memoires) {
                const categorie = this.determinerCategorie(memoire.contenu);
                const frequenceVerification = this.categoriesVerification[categorie] || 24;
                
                // Vérifier si la vérification est nécessaire
                if (this.verificationNecessaire(memoire, frequenceVerification)) {
                    const resultat = await this.verifierEtMettreAJour(memoire);
                    verificationsEffectuees++;
                    
                    if (resultat.mise_a_jour) {
                        informationsMisesAJour++;
                    }
                }
            }
            
            console.log(`✅ Vérifications: ${verificationsEffectuees}, Mises à jour: ${informationsMisesAJour}`);
            
            // Enregistrer dans l'historique
            this.historiqueVerifications.push({
                type: 'verification_generale',
                timestamp: Date.now(),
                verifications: verificationsEffectuees,
                mises_a_jour: informationsMisesAJour
            });
            
        } catch (error) {
            console.error(`❌ Erreur vérification obsolètes: ${error.message}`);
        }
    }

    // VÉRIFIER CATÉGORIES CRITIQUES
    async verifierCategoriesCritiques() {
        const categoriesCritiques = ['actualité', 'technologie', 'économie', 'bourse', 'météo'];
        
        console.log('⚡ Vérification catégories critiques...');
        
        for (const categorie of categoriesCritiques) {
            await this.verifierCategorie(categorie);
        }
    }

    // DOUTE SYSTÉMATIQUE
    async douteSystematique() {
        if (!this.memoireThermique) return;
        
        console.log('🤔 Doute systématique activé...');
        
        try {
            // Prendre quelques mémoires récemment accédées
            const memoiresRecentes = this.memoireThermique.obtenirMemoiresRecentes(5);
            
            for (const memoire of memoiresRecentes) {
                // Si information >24h, vérification automatique
                const age = Date.now() - memoire.timestamp;
                const ageHeures = age / (1000 * 60 * 60);
                
                if (ageHeures > 24) {
                    console.log(`🤔 Doute sur: ${memoire.contenu.substring(0, 50)}...`);
                    await this.verifierEtMettreAJour(memoire);
                }
            }
            
        } catch (error) {
            console.error(`❌ Erreur doute systématique: ${error.message}`);
        }
    }

    // VÉRIFIER ET METTRE À JOUR UNE MÉMOIRE
    async verifierEtMettreAJour(memoire) {
        try {
            console.log(`🔍 Vérification: ${memoire.contenu.substring(0, 50)}...`);
            
            // Marquer comme vérifié
            this.derniereVerification.set(memoire.id, Date.now());
            
            // Simuler recherche de nouvelle information (dans un vrai système, utiliser API)
            const nouvelleInformation = await this.rechercherNouvelleInformation(memoire.contenu);
            
            if (nouvelleInformation) {
                // Comparer avec l'ancienne information
                const similarite = this.calculerSimilarite(memoire.contenu, nouvelleInformation);
                
                if (similarite < this.seuilSimilarite) {
                    // Information a évolué, mise à jour nécessaire
                    console.log(`🔄 Mise à jour détectée (similarité: ${(similarite * 100).toFixed(1)}%)`);
                    
                    await this.mettreAJourMemoire(memoire, nouvelleInformation);
                    
                    return {
                        mise_a_jour: true,
                        ancienne_info: memoire.contenu,
                        nouvelle_info: nouvelleInformation,
                        similarite: similarite
                    };
                } else {
                    console.log(`✅ Information à jour (similarité: ${(similarite * 100).toFixed(1)}%)`);
                }
            }
            
            return { mise_a_jour: false };
            
        } catch (error) {
            console.error(`❌ Erreur vérification mémoire: ${error.message}`);
            return { mise_a_jour: false, erreur: error.message };
        }
    }

    // METTRE À JOUR UNE MÉMOIRE
    async mettreAJourMemoire(memoire, nouvelleInformation) {
        try {
            // Créer nouvelle version avec historique
            const nouvelleMemoire = {
                ...memoire,
                contenu: nouvelleInformation,
                version: (memoire.version || 1) + 1,
                contenu_precedent: memoire.contenu,
                timestamp_mise_a_jour: Date.now(),
                raison_mise_a_jour: 'verification_automatique'
            };
            
            // Boost fraîcheur : +20°C température, +0.1 importance
            nouvelleMemoire.temperature = Math.min(100, (nouvelleMemoire.temperature || 50) + 20);
            nouvelleMemoire.importance = Math.min(1, (nouvelleMemoire.importance || 0.5) + 0.1);
            
            // Mettre à jour dans la mémoire thermique
            if (this.memoireThermique.mettreAJour) {
                this.memoireThermique.mettreAJour(memoire.id, nouvelleMemoire);
            }
            
            console.log(`✅ Mémoire mise à jour: ${memoire.id}`);
            
        } catch (error) {
            console.error(`❌ Erreur mise à jour mémoire: ${error.message}`);
        }
    }

    // UTILITAIRES
    determinerCategorie(contenu) {
        const contenuLower = contenu.toLowerCase();
        
        const motsClés = {
            'actualité': ['actualité', 'news', 'événement', 'aujourd\'hui', 'récent'],
            'technologie': ['technologie', 'tech', 'logiciel', 'app', 'code', 'programmation'],
            'science': ['science', 'recherche', 'étude', 'découverte', 'scientifique'],
            'économie': ['économie', 'économique', 'marché', 'entreprise', 'business'],
            'politique': ['politique', 'gouvernement', 'élection', 'président', 'ministre'],
            'sport': ['sport', 'football', 'tennis', 'olympique', 'match'],
            'météo': ['météo', 'temps', 'température', 'pluie', 'soleil'],
            'bourse': ['bourse', 'action', 'investissement', 'trading', 'finance']
        };
        
        for (const [categorie, mots] of Object.entries(motsClés)) {
            if (mots.some(mot => contenuLower.includes(mot))) {
                return categorie;
            }
        }
        
        return 'général';
    }

    verificationNecessaire(memoire, frequenceHeures) {
        const derniereVerif = this.derniereVerification.get(memoire.id) || 0;
        const tempsEcoule = Date.now() - derniereVerif;
        const tempsEcouleHeures = tempsEcoule / (1000 * 60 * 60);
        
        return tempsEcouleHeures >= frequenceHeures;
    }

    async rechercherNouvelleInformation(contenu) {
        try {
            // Recherche réelle avec le système de recherche sécurisée
            const RechercheSecurisee = require('./recherche-google-securisee');
            const recherche = new RechercheSecurisee();

            // Extraire mots-clés du contenu pour recherche
            const motsCles = contenu.toLowerCase()
                .replace(/[^\w\s]/g, ' ')
                .split(/\s+/)
                .filter(mot => mot.length > 3)
                .slice(0, 3) // Limiter à 3 mots-clés
                .join(' ');

            if (motsCles.length > 0) {
                const resultats = await recherche.rechercherSecurise(motsCles + ' mise à jour information');

                if (resultats && resultats.length > 0) {
                    return {
                        source: 'recherche_internet',
                        informations: resultats.slice(0, 2), // Top 2 résultats
                        timestamp: new Date().toISOString(),
                        mots_cles: motsCles
                    };
                }
            }

            return null;
        } catch (error) {
            console.log('⚠️ Erreur recherche nouvelle information:', error.message);
            return null;
        }
    }

    calculerSimilarite(texte1, texte2) {
        // Algorithme simple de similarité (Jaccard)
        const mots1 = new Set(texte1.toLowerCase().split(/\s+/));
        const mots2 = new Set(texte2.toLowerCase().split(/\s+/));
        
        const intersection = new Set([...mots1].filter(x => mots2.has(x)));
        const union = new Set([...mots1, ...mots2]);
        
        return intersection.size / union.size;
    }

    async verifierCategorie(categorie) {
        // Vérifier toutes les mémoires d'une catégorie spécifique
        console.log(`🔍 Vérification catégorie: ${categorie}`);
        // Implémentation spécifique par catégorie
    }

    // STATISTIQUES
    obtenirStatistiques() {
        return {
            verifications_effectuees: this.historiqueVerifications.length,
            dernieres_verifications: this.derniereVerification.size,
            informations_obsoletes: this.informationsObsoletes.size,
            categories_surveillees: Object.keys(this.categoriesVerification).length,
            seuil_similarite: this.seuilSimilarite
        };
    }
}

module.exports = SystemeOubliIntelligent;
