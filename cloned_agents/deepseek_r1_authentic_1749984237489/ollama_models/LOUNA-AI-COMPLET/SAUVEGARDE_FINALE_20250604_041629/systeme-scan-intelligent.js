/**
 * SYSTÈME DE SCAN INTELLIGENT POUR LOUNA-AI
 * Scan complet des applications et analyse système
 */

const { exec, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

class SystemeScanIntelligent {
    constructor() {
        this.applicationsDetectees = new Map();
        this.informationsSysteme = {};
        this.historiqueScan = [];
        this.dernierScan = null;
        
        // Répertoires à scanner pour les applications
        this.repertoiresApplications = [
            '/Applications',
            '/System/Applications',
            '/System/Library/CoreServices',
            '/usr/local/bin',
            '/opt/homebrew/bin',
            path.join(os.homedir(), 'Applications')
        ];
        
        // Extensions d'applications reconnues
        this.extensionsApps = ['.app', '.pkg', '.dmg'];
        
        // Catégories d'applications
        this.categories = {
            'développement': ['code', 'xcode', 'terminal', 'git', 'docker', 'postman', 'sublime', 'atom', 'webstorm', 'pycharm'],
            'navigateur': ['chrome', 'safari', 'firefox', 'edge', 'opera', 'brave'],
            'créatif': ['photoshop', 'illustrator', 'figma', 'sketch', 'blender', 'cinema4d', 'after effects'],
            'bureautique': ['word', 'excel', 'powerpoint', 'pages', 'numbers', 'keynote', 'notion'],
            'multimédia': ['vlc', 'quicktime', 'spotify', 'itunes', 'music', 'photos', 'preview'],
            'système': ['finder', 'activity monitor', 'disk utility', 'terminal', 'console'],
            'communication': ['slack', 'discord', 'teams', 'zoom', 'skype', 'facetime', 'messages'],
            'utilitaires': ['1password', 'dropbox', 'google drive', 'cleanmymac', 'alfred']
        };
    }

    // SCAN COMPLET DES APPLICATIONS
    async scannerApplications() {
        console.log('🔍 Début du scan des applications...');
        
        try {
            const applicationsDetectees = new Map();
            let totalApps = 0;
            
            for (const repertoire of this.repertoiresApplications) {
                if (fs.existsSync(repertoire)) {
                    console.log(`📂 Scan du répertoire: ${repertoire}`);
                    const apps = await this.scannerRepertoire(repertoire);
                    
                    apps.forEach(app => {
                        applicationsDetectees.set(app.nom.toLowerCase(), app);
                        totalApps++;
                    });
                }
            }
            
            // Scanner aussi les applications via system_profiler (plus complet)
            const appsSystemProfiler = await this.scannerAvecSystemProfiler();
            appsSystemProfiler.forEach(app => {
                if (!applicationsDetectees.has(app.nom.toLowerCase())) {
                    applicationsDetectees.set(app.nom.toLowerCase(), app);
                    totalApps++;
                }
            });
            
            this.applicationsDetectees = applicationsDetectees;
            this.dernierScan = Date.now();
            
            // Enregistrer dans l'historique
            this.historiqueScan.push({
                type: 'scan_applications',
                timestamp: this.dernierScan,
                applications_trouvees: totalApps,
                repertoires_scannes: this.repertoiresApplications.length
            });
            
            console.log(`✅ Scan terminé: ${totalApps} applications détectées`);
            
            return {
                success: true,
                applications: Array.from(applicationsDetectees.values()),
                total: totalApps,
                repertoires_scannes: this.repertoiresApplications.length,
                message: `${totalApps} applications détectées sur le système`
            };
            
        } catch (error) {
            console.error(`❌ Erreur scan applications: ${error.message}`);
            return {
                success: false,
                error: error.message,
                message: `Erreur lors du scan des applications: ${error.message}`
            };
        }
    }

    // SCANNER UN RÉPERTOIRE SPÉCIFIQUE
    async scannerRepertoire(repertoire) {
        const applications = [];
        
        try {
            const entries = fs.readdirSync(repertoire, { withFileTypes: true });
            
            for (const entry of entries) {
                if (entry.isDirectory() && entry.name.endsWith('.app')) {
                    const cheminComplet = path.join(repertoire, entry.name);
                    const infoApp = await this.analyserApplication(cheminComplet);
                    
                    if (infoApp) {
                        applications.push(infoApp);
                    }
                }
            }
            
        } catch (error) {
            console.log(`⚠️ Impossible de scanner ${repertoire}: ${error.message}`);
        }
        
        return applications;
    }

    // ANALYSER UNE APPLICATION SPÉCIFIQUE
    async analyserApplication(cheminApp) {
        try {
            const nomApp = path.basename(cheminApp, '.app');
            const stats = fs.statSync(cheminApp);
            
            // Lire les informations du plist si disponible
            const plistPath = path.join(cheminApp, 'Contents', 'Info.plist');
            let infosPlist = {};
            
            if (fs.existsSync(plistPath)) {
                try {
                    const resultatPlist = await this.executerCommande(`plutil -convert json -o - "${plistPath}"`);
                    if (resultatPlist.success) {
                        infosPlist = JSON.parse(resultatPlist.sortie);
                    }
                } catch (e) {
                    // Ignorer les erreurs de plist
                }
            }
            
            // Déterminer la catégorie
            const categorie = this.determinerCategorie(nomApp);
            
            // Générer la commande d'ouverture
            const commande = `open -a "${nomApp}"`;
            
            const application = {
                nom: nomApp,
                nom_complet: infosPlist.CFBundleDisplayName || infosPlist.CFBundleName || nomApp,
                chemin: cheminApp,
                version: infosPlist.CFBundleShortVersionString || infosPlist.CFBundleVersion || 'Inconnue',
                identifiant: infosPlist.CFBundleIdentifier || 'Inconnu',
                categorie: categorie,
                taille: stats.size,
                date_modification: stats.mtime,
                commande: commande,
                description: this.genererDescription(nomApp, categorie),
                icone: this.chercherIcone(cheminApp),
                executable: infosPlist.CFBundleExecutable || nomApp
            };
            
            return application;
            
        } catch (error) {
            console.log(`⚠️ Erreur analyse ${cheminApp}: ${error.message}`);
            return null;
        }
    }

    // SCANNER AVEC SYSTEM_PROFILER (plus complet)
    async scannerAvecSystemProfiler() {
        try {
            console.log('🔍 Scan avec system_profiler...');
            
            const commande = 'system_profiler SPApplicationsDataType -json';
            const resultat = await this.executerCommande(commande);
            
            if (!resultat.success) {
                return [];
            }
            
            const data = JSON.parse(resultat.sortie);
            const applications = [];
            
            if (data.SPApplicationsDataType) {
                for (const app of data.SPApplicationsDataType) {
                    const nomApp = app._name || 'Inconnu';
                    const categorie = this.determinerCategorie(nomApp);
                    
                    applications.push({
                        nom: nomApp,
                        nom_complet: nomApp,
                        chemin: app.path || 'Inconnu',
                        version: app.version || 'Inconnue',
                        identifiant: app.bundle_id || 'Inconnu',
                        categorie: categorie,
                        taille: app.size_bytes || 0,
                        date_modification: app.lastModified || null,
                        commande: `open -a "${nomApp}"`,
                        description: this.genererDescription(nomApp, categorie),
                        source: 'system_profiler',
                        architecture: app.arch_kind || 'Inconnue'
                    });
                }
            }
            
            console.log(`✅ System_profiler: ${applications.length} applications`);
            return applications;
            
        } catch (error) {
            console.log(`⚠️ Erreur system_profiler: ${error.message}`);
            return [];
        }
    }

    // SCAN COMPLET DU SYSTÈME
    async scannerSystemeComplet() {
        console.log('🖥️ Début du scan système complet...');
        
        try {
            const informations = {
                // Informations de base
                systeme: {
                    plateforme: os.platform(),
                    architecture: os.arch(),
                    version: os.release(),
                    hostname: os.hostname(),
                    uptime: os.uptime(),
                    utilisateur: os.userInfo()
                },
                
                // Mémoire
                memoire: {
                    totale: os.totalmem(),
                    libre: os.freemem(),
                    utilisee: os.totalmem() - os.freemem(),
                    pourcentage_utilise: ((os.totalmem() - os.freemem()) / os.totalmem() * 100).toFixed(2)
                },
                
                // CPU
                cpu: {
                    modele: os.cpus()[0].model,
                    nombre_coeurs: os.cpus().length,
                    vitesse: os.cpus()[0].speed,
                    architecture: os.arch()
                },
                
                // Réseau
                reseau: os.networkInterfaces(),
                
                // Répertoires importants
                repertoires: {
                    home: os.homedir(),
                    temp: os.tmpdir(),
                    cwd: process.cwd()
                }
            };
            
            // Informations disques
            informations.disques = await this.scannerDisques();
            
            // Informations matériel (macOS)
            if (os.platform() === 'darwin') {
                informations.materiel = await this.scannerMaterielMac();
            }
            
            // Processus en cours
            informations.processus = await this.scannerProcessus();
            
            // Variables d'environnement importantes
            informations.environnement = {
                PATH: process.env.PATH,
                HOME: process.env.HOME,
                USER: process.env.USER,
                SHELL: process.env.SHELL,
                NODE_VERSION: process.version
            };
            
            this.informationsSysteme = informations;
            
            // Enregistrer dans l'historique
            this.historiqueScan.push({
                type: 'scan_systeme',
                timestamp: Date.now(),
                informations_collectees: Object.keys(informations).length
            });
            
            console.log('✅ Scan système terminé');
            
            return {
                success: true,
                informations: informations,
                message: 'Scan système complet terminé avec succès'
            };
            
        } catch (error) {
            console.error(`❌ Erreur scan système: ${error.message}`);
            return {
                success: false,
                error: error.message,
                message: `Erreur lors du scan système: ${error.message}`
            };
        }
    }

    // SCANNER LES DISQUES
    async scannerDisques() {
        try {
            const commande = 'df -h';
            const resultat = await this.executerCommande(commande);
            
            if (resultat.success) {
                const lignes = resultat.sortie.split('\n').slice(1); // Ignorer l'en-tête
                const disques = [];
                
                for (const ligne of lignes) {
                    if (ligne.trim()) {
                        const parties = ligne.trim().split(/\s+/);
                        if (parties.length >= 6) {
                            disques.push({
                                systeme_fichiers: parties[0],
                                taille: parties[1],
                                utilise: parties[2],
                                disponible: parties[3],
                                pourcentage: parties[4],
                                point_montage: parties[5]
                            });
                        }
                    }
                }
                
                return disques;
            }
            
            return [];
            
        } catch (error) {
            console.log(`⚠️ Erreur scan disques: ${error.message}`);
            return [];
        }
    }

    // SCANNER LE MATÉRIEL MAC
    async scannerMaterielMac() {
        try {
            const commande = 'system_profiler SPHardwareDataType -json';
            const resultat = await this.executerCommande(commande);
            
            if (resultat.success) {
                const data = JSON.parse(resultat.sortie);
                return data.SPHardwareDataType ? data.SPHardwareDataType[0] : {};
            }
            
            return {};
            
        } catch (error) {
            console.log(`⚠️ Erreur scan matériel: ${error.message}`);
            return {};
        }
    }

    // SCANNER LES PROCESSUS
    async scannerProcessus() {
        try {
            const commande = 'ps aux | head -20'; // Top 20 processus
            const resultat = await this.executerCommande(commande);
            
            if (resultat.success) {
                const lignes = resultat.sortie.split('\n').slice(1); // Ignorer l'en-tête
                const processus = [];
                
                for (const ligne of lignes) {
                    if (ligne.trim()) {
                        const parties = ligne.trim().split(/\s+/);
                        if (parties.length >= 11) {
                            processus.push({
                                utilisateur: parties[0],
                                pid: parties[1],
                                cpu: parties[2],
                                memoire: parties[3],
                                commande: parties.slice(10).join(' ')
                            });
                        }
                    }
                }
                
                return processus;
            }
            
            return [];
            
        } catch (error) {
            console.log(`⚠️ Erreur scan processus: ${error.message}`);
            return [];
        }
    }

    // DÉTECTION AUTOMATIQUE DE NOUVELLES APPLICATIONS
    async detecterNouvellesApplications() {
        console.log('🔍 Détection de nouvelles applications...');
        
        const anciennesApps = new Set(this.applicationsDetectees.keys());
        const resultatScan = await this.scannerApplications();
        
        if (!resultatScan.success) {
            return resultatScan;
        }
        
        const nouvellesApps = [];
        const appsActuelles = new Set(this.applicationsDetectees.keys());
        
        for (const nomApp of appsActuelles) {
            if (!anciennesApps.has(nomApp)) {
                nouvellesApps.push(this.applicationsDetectees.get(nomApp));
            }
        }
        
        return {
            success: true,
            nouvelles_applications: nouvellesApps,
            total_nouvelles: nouvellesApps.length,
            total_applications: this.applicationsDetectees.size,
            message: `${nouvellesApps.length} nouvelles applications détectées`
        };
    }

    // UTILITAIRES
    determinerCategorie(nomApp) {
        const nomLower = nomApp.toLowerCase();
        
        for (const [categorie, motsClés] of Object.entries(this.categories)) {
            if (motsClés.some(motClé => nomLower.includes(motClé))) {
                return categorie;
            }
        }
        
        return 'autre';
    }

    genererDescription(nomApp, categorie) {
        const descriptions = {
            'développement': 'Outil de développement et programmation',
            'navigateur': 'Navigateur web pour internet',
            'créatif': 'Application créative et design',
            'bureautique': 'Application de bureautique et productivité',
            'multimédia': 'Application multimédia et divertissement',
            'système': 'Utilitaire système',
            'communication': 'Application de communication',
            'utilitaires': 'Utilitaire et outil système'
        };
        
        return descriptions[categorie] || `Application ${nomApp}`;
    }

    chercherIcone(cheminApp) {
        const cheminIcone = path.join(cheminApp, 'Contents', 'Resources');
        
        try {
            if (fs.existsSync(cheminIcone)) {
                const fichiers = fs.readdirSync(cheminIcone);
                const icone = fichiers.find(f => f.endsWith('.icns'));
                return icone ? path.join(cheminIcone, icone) : null;
            }
        } catch (e) {
            // Ignorer les erreurs
        }
        
        return null;
    }

    async executerCommande(commande) {
        return new Promise((resolve) => {
            exec(commande, { timeout: 30000 }, (error, stdout, stderr) => {
                if (error) {
                    resolve({
                        success: false,
                        error: error.message,
                        sortie: stdout,
                        erreur: stderr
                    });
                } else {
                    resolve({
                        success: true,
                        sortie: stdout,
                        erreur: stderr
                    });
                }
            });
        });
    }

    // TRAITEMENT DES DEMANDES
    async traiterDemande(demande) {
        const demandeLower = demande.toLowerCase();
        
        try {
            if (demandeLower.includes('scan') && demandeLower.includes('application')) {
                return await this.scannerApplications();
            }
            
            if (demandeLower.includes('scan') && demandeLower.includes('système')) {
                return await this.scannerSystemeComplet();
            }
            
            if (demandeLower.includes('nouvelles') && demandeLower.includes('application')) {
                return await this.detecterNouvellesApplications();
            }
            
            if (demandeLower.includes('inventaire') || demandeLower.includes('liste complète')) {
                const apps = await this.scannerApplications();
                const systeme = await this.scannerSystemeComplet();
                
                return {
                    success: true,
                    applications: apps.applications || [],
                    systeme: systeme.informations || {},
                    message: 'Inventaire complet de la machine généré'
                };
            }
            
            // Par défaut, scanner les applications
            return await this.scannerApplications();
            
        } catch (error) {
            return {
                success: false,
                error: error.message,
                message: `Erreur lors du traitement: ${error.message}`
            };
        }
    }

    // STATISTIQUES
    obtenirStatistiques() {
        const stats = {
            applications_detectees: this.applicationsDetectees.size,
            dernier_scan: this.dernierScan,
            scans_effectues: this.historiqueScan.length,
            repertoires_surveilles: this.repertoiresApplications.length,
            categories_disponibles: Object.keys(this.categories).length
        };
        
        // Statistiques par catégorie
        const parCategorie = {};
        Array.from(this.applicationsDetectees.values()).forEach(app => {
            parCategorie[app.categorie] = (parCategorie[app.categorie] || 0) + 1;
        });
        stats.applications_par_categorie = parCategorie;
        
        return stats;
    }

    // EXPORT DES DONNÉES
    async exporterInventaire(format = 'json') {
        const inventaire = {
            timestamp: Date.now(),
            applications: Array.from(this.applicationsDetectees.values()),
            systeme: this.informationsSysteme,
            statistiques: this.obtenirStatistiques()
        };
        
        const nomFichier = `inventaire_${new Date().toISOString().split('T')[0]}.${format}`;
        const cheminFichier = path.join(__dirname, 'inventaires', nomFichier);
        
        try {
            const dossier = path.dirname(cheminFichier);
            if (!fs.existsSync(dossier)) {
                fs.mkdirSync(dossier, { recursive: true });
            }
            
            if (format === 'json') {
                fs.writeFileSync(cheminFichier, JSON.stringify(inventaire, null, 2));
            }
            
            return {
                success: true,
                fichier: cheminFichier,
                message: `Inventaire exporté: ${nomFichier}`
            };
            
        } catch (error) {
            return {
                success: false,
                error: error.message,
                message: `Erreur export: ${error.message}`
            };
        }
    }
}

module.exports = SystemeScanIntelligent;
