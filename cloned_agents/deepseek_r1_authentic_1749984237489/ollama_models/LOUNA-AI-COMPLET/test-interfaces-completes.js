#!/usr/bin/env node

/**
 * 🧪 TEST INTERFACES COMPLÈTES
 * 
 * Validation de toutes les interfaces LOUNA-AI
 */

const axios = require('axios').default;

async function testerInterfacesCompletes() {
    console.log('🧪 TEST INTERFACES COMPLÈTES LOUNA-AI');
    console.log('=====================================');
    
    const interfaces = [
        {
            nom: 'Interface Principale',
            url: 'http://localhost:3000',
            description: 'Interface de chat principale avec mémoire thermique'
        },
        {
            nom: 'Cerveau 3D Vivant',
            url: 'http://localhost:3000/3d',
            description: 'Visualisation 3D du cerveau artificiel en temps réel'
        },
        {
            nom: 'Formations IA',
            url: 'http://localhost:3000/formations',
            description: 'Interface de formations et apprentissage adaptatif'
        },
        {
            nom: 'API Ollama Status',
            url: 'http://localhost:3000/api/ollama/status',
            description: 'API REST pour statut Ollama intégré'
        },
        {
            nom: 'API Stats Système',
            url: 'http://localhost:3000/api/stats',
            description: 'API REST pour statistiques système'
        }
    ];
    
    console.log('\n🔍 TESTS DE CONNECTIVITÉ');
    console.log('========================');
    
    let interfaces_ok = 0;
    
    for (const interface_test of interfaces) {
        try {
            console.log(`\n📡 Test: ${interface_test.nom}`);
            console.log(`🔗 URL: ${interface_test.url}`);
            console.log(`📝 Description: ${interface_test.description}`);
            
            const response = await axios.get(interface_test.url, { 
                timeout: 5000,
                validateStatus: (status) => status < 500 // Accepter redirections
            });
            
            console.log(`✅ Status: ${response.status}`);
            console.log(`📊 Taille: ${response.data.length || 'N/A'} caractères`);
            
            // Tests spécifiques selon le type
            if (interface_test.url.includes('/api/')) {
                // API JSON
                if (typeof response.data === 'object') {
                    console.log(`📋 Données JSON: ${Object.keys(response.data).length} clés`);
                    if (response.data.success !== undefined) {
                        console.log(`🎯 Succès API: ${response.data.success}`);
                    }
                }
            } else {
                // Interface HTML
                if (typeof response.data === 'string' && response.data.includes('<!DOCTYPE html>')) {
                    console.log(`🌐 Interface HTML valide`);
                    
                    // Vérifier éléments spécifiques
                    if (response.data.includes('LOUNA-AI')) {
                        console.log(`🤖 Branding LOUNA-AI: ✅`);
                    }
                    if (response.data.includes('thermique')) {
                        console.log(`🌡️ Système thermique: ✅`);
                    }
                    if (response.data.includes('Three.js') || response.data.includes('THREE')) {
                        console.log(`🧠 Moteur 3D: ✅`);
                    }
                }
            }
            
            interfaces_ok++;
            console.log(`✅ ${interface_test.nom}: OPÉRATIONNEL`);
            
        } catch (error) {
            console.log(`❌ ${interface_test.nom}: ERREUR`);
            console.log(`   Détail: ${error.message}`);
            
            if (error.response) {
                console.log(`   Status HTTP: ${error.response.status}`);
            }
        }
    }
    
    console.log('\n🏆 RÉSULTATS FINAUX');
    console.log('==================');
    
    const pourcentage = (interfaces_ok / interfaces.length * 100).toFixed(1);
    console.log(`📊 Interfaces opérationnelles: ${interfaces_ok}/${interfaces.length} (${pourcentage}%)`);
    
    if (pourcentage >= 80) {
        console.log('🎉 SYSTÈME COMPLET OPÉRATIONNEL !');
        console.log('✅ Toutes les interfaces principales fonctionnent');
        console.log('🚀 LOUNA-AI prêt pour utilisation complète');
        
        console.log('\n🌟 FONCTIONNALITÉS DISPONIBLES:');
        console.log('• 💬 Chat intelligent avec mémoire thermique');
        console.log('• 🧠 Visualisation 3D du cerveau artificiel');
        console.log('• 🎓 Formations IA adaptatives');
        console.log('• 🤖 Ollama intégré directement');
        console.log('• 📊 APIs REST complètes');
        console.log('• 🌡️ Système thermique "Chaleur = Vie"');
        
    } else if (pourcentage >= 60) {
        console.log('⚠️ Système partiellement opérationnel');
        console.log('🔧 Quelques interfaces nécessitent attention');
        
    } else {
        console.log('❌ Problèmes critiques détectés');
        console.log('🚨 Vérifier configuration serveur');
    }
    
    console.log('\n🔗 LIENS DIRECTS:');
    interfaces.forEach(interface_test => {
        const status = interfaces_ok >= interfaces.length * 0.8 ? '✅' : '⚠️';
        console.log(`${status} ${interface_test.nom}: ${interface_test.url}`);
    });
    
    console.log('\n💡 CONSEILS D\'UTILISATION:');
    console.log('1. Commencez par l\'interface principale pour tester le chat');
    console.log('2. Ouvrez le Cerveau 3D pour voir l\'activité neuronale');
    console.log('3. Explorez les Formations pour l\'apprentissage adaptatif');
    console.log('4. Surveillez la température CPU pour l\'évolution du QI');
    console.log('5. Plus votre système chauffe, plus LOUNA-AI devient intelligent !');
    
    return {
        total: interfaces.length,
        operationnelles: interfaces_ok,
        pourcentage: parseFloat(pourcentage),
        status: pourcentage >= 80 ? 'EXCELLENT' : pourcentage >= 60 ? 'BON' : 'PROBLEMES'
    };
}

// Lancer test
if (require.main === module) {
    testerInterfacesCompletes()
        .then(resultat => {
            console.log('\n📋 RAPPORT FINAL SAUVEGARDÉ');
            require('fs').writeFileSync('RAPPORT-INTERFACES-COMPLETES.json', JSON.stringify(resultat, null, 2));
        })
        .catch(console.error);
}

module.exports = { testerInterfacesCompletes };
