#!/bin/bash

# 🚀 LOUNA-AI - LANCEMENT RAPIDE
# ==============================

echo "🚀 LOUNA-AI - Intelligence Artificielle Complète"
echo "================================================"

# Vérifier si nous sommes dans le bon répertoire
if [ ! -f "serveur-interface-complete.js" ]; then
    echo "❌ Erreur: Fichiers LOUNA-AI non trouvés"
    echo "📁 Assurez-vous d'être dans le répertoire LOUNA-AI-COMPLET"
    exit 1
fi

# Afficher l'état de la configuration
if [ -f "CONFIGURATION-VERROUILLEE-FINALE.md" ]; then
    echo "🔒 Configuration finale verrouillée détectée"
else
    echo "⚠️  Configuration finale non verrouillée"
fi

# Choix du mode de lancement
echo ""
echo "🎯 MODES DE LANCEMENT DISPONIBLES:"
echo "=================================="
echo "1. 🌐 Interface Web (Recommandé)"
echo "2. 🖥️  Application Desktop (Electron)"
echo "3. 🔧 Mode Développement"
echo ""

read -p "Choisissez un mode (1-3): " mode

case $mode in
    1)
        echo ""
        echo "🌐 LANCEMENT INTERFACE WEB"
        echo "=========================="
        echo "🔄 Démarrage du serveur LOUNA-AI..."
        
        # Lancer le serveur
        node serveur-interface-complete.js &
        SERVER_PID=$!
        
        # Attendre que le serveur démarre
        sleep 3
        
        # Ouvrir l'interface dans le navigateur
        echo "🌐 Ouverture de l'interface..."
        if command -v open &> /dev/null; then
            open http://localhost:3000/interface-louna-complete.html
        elif command -v xdg-open &> /dev/null; then
            xdg-open http://localhost:3000/interface-louna-complete.html
        else
            echo "🌐 Interface disponible sur: http://localhost:3000/interface-louna-complete.html"
        fi
        
        echo ""
        echo "✅ LOUNA-AI Interface Web démarrée"
        echo "🌐 URL: http://localhost:3000/interface-louna-complete.html"
        echo "🛑 Appuyez sur Ctrl+C pour arrêter"
        
        # Attendre l'arrêt
        wait $SERVER_PID
        ;;
        
    2)
        echo ""
        echo "🖥️  LANCEMENT APPLICATION DESKTOP"
        echo "================================"
        
        # Vérifier si Electron est installé
        if [ ! -d "node_modules/electron" ]; then
            echo "📦 Installation d'Electron..."
            cp package-app.json package.json
            npm install electron --save-dev
        fi
        
        echo "🚀 Lancement de l'application desktop..."
        npx electron LOUNA-AI-APP.js
        ;;
        
    3)
        echo ""
        echo "🔧 MODE DÉVELOPPEMENT"
        echo "===================="
        echo "🔄 Démarrage avec logs détaillés..."
        
        export NODE_ENV=development
        node serveur-interface-complete.js
        ;;
        
    *)
        echo "❌ Mode invalide. Lancement par défaut (Interface Web)..."
        node serveur-interface-complete.js
        ;;
esac

echo ""
echo "🎯 LOUNA-AI fermée"
echo "Merci d'avoir utilisé LOUNA-AI !"
