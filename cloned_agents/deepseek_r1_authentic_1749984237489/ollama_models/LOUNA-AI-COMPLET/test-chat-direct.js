#!/usr/bin/env node

/**
 * 🧪 TEST CHAT DIRECT
 * 
 * Test direct du système de chat pour diagnostiquer le problème
 */

const axios = require('axios').default;

async function testerChatDirect() {
    console.log('🧪 TEST CHAT DIRECT LOUNA-AI');
    console.log('============================');
    
    const questions = [
        "<PERSON><PERSON><PERSON>, comment ça va ?",
        "Quelle est la capitale de l'Allemagne ?",
        "Explique-moi l'intelligence artificielle",
        "Calcule 15 + 27",
        "Raconte-moi une blague"
    ];
    
    for (const question of questions) {
        try {
            console.log(`\n❓ Question: "${question}"`);
            console.log('⏳ Envoi de la requête...');
            
            const response = await axios.post('http://localhost:3000/api/chat', {
                message: question
            }, {
                timeout: 30000,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            console.log(`✅ Status: ${response.status}`);
            console.log(`📝 Réponse: "${response.data.response}"`);
            
            if (response.data.response === "Paris est la capitale de la France.") {
                console.log('🚨 PROBLÈME DÉTECTÉ: Réponse figée !');
            }
            
        } catch (error) {
            console.error(`❌ Erreur pour "${question}":`, error.message);
            if (error.response) {
                console.error(`📊 Status HTTP: ${error.response.status}`);
                console.error(`📋 Data: ${JSON.stringify(error.response.data)}`);
            }
        }
        
        // Pause entre questions
        await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    console.log('\n🔍 DIAGNOSTIC COMPLET');
    console.log('====================');
    console.log('Si toutes les réponses sont identiques, le problème est confirmé.');
    console.log('Vérifiez les logs du serveur pour plus de détails.');
}

testerChatDirect();
