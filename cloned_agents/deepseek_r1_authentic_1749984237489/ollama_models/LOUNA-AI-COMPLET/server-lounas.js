/**
 * <PERSON><PERSON><PERSON> - Interface cognitive avancée
 * Version simplifiée pour tester la mémoire thermique
 */

const express = require('express');
const http = require('http');
const path = require('path');
const fs = require('fs');
const socketIo = require('socket.io');

// Configuration
const app = express();
const server = http.createServer(app);
const io = socketIo(server);
const PORT = process.env.PORT || 3003;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// Configuration du moteur de template
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Créer les dossiers nécessaires s'ils n'existent pas
const MEMORY_DIR = path.join(__dirname, 'data/memory');
if (!fs.existsSync(MEMORY_DIR)) {
  fs.mkdirSync(MEMORY_DIR, { recursive: true });
}

// Charger les services
const ThermalMemory = require('./services/thermal-memory');
const BrainPresence = require('./services/brain-presence');

// Initialiser la mémoire thermique
const thermalMemory = new ThermalMemory(path.join(__dirname, 'data/memory/thermal_memory.json'));
console.log('Mémoire thermique initialisée');

// Initialiser le service de présence cérébrale
const brainPresence = new BrainPresence(thermalMemory);
console.log('Service de présence cérébrale initialisé');

// Activer le service de présence cérébrale
brainPresence.initialize();
console.log('Service de présence cérébrale activé');

// Route principale
app.get('/lounas', (req, res) => {
  res.render('luna-chat', {
    title: 'Lounas - Interface Cognitive',
    page: 'chat'
  });
});

// Route pour la page d'accueil
app.get('/lounas/home', (req, res) => {
  res.render('luna-home', {
    title: 'Lounas - Accueil',
    page: 'home'
  });
});

// Route pour la page de chat
app.get('/lounas/chat', (req, res) => {
  res.render('luna-chat', {
    title: 'Lounas - Chat',
    page: 'chat'
  });
});

// Route pour la page de mémoire
app.get('/lounas/memory', (req, res) => {
  res.render('luna-memory', {
    title: 'Lounas - Mémoire',
    page: 'memory'
  });
});

// Route pour la page de formation
app.get('/lounas/training', (req, res) => {
  res.render('luna-training', {
    title: 'Lounas - Formation',
    page: 'training'
  });
});

// Route pour la page de code
app.get('/lounas/code', (req, res) => {
  res.render('luna-code', {
    title: 'Lounas - Code',
    page: 'code'
  });
});

// Route pour la page de sécurité
app.get('/lounas/security', (req, res) => {
  res.render('luna-security', {
    title: 'Lounas - Sécurité',
    page: 'security'
  });
});

// Route pour la page de sauvegarde
app.get('/lounas/backup', (req, res) => {
  res.render('luna-backup', {
    title: 'Lounas - Sauvegarde',
    page: 'backup'
  });
});

// Route pour la page de surveillance
app.get('/lounas/monitor', (req, res) => {
  res.render('luna-monitor', {
    title: 'Lounas - Surveillance',
    page: 'monitor'
  });
});

// Route pour la page des accélérateurs
app.get('/lounas/accelerators', (req, res) => {
  res.render('luna-accelerators', {
    title: 'Lounas - Accélérateurs',
    page: 'accelerators'
  });
});

// Route pour la page des statistiques
app.get('/lounas/stats', (req, res) => {
  res.render('luna-stats', {
    title: 'Lounas - Statistiques',
    page: 'stats'
  });
});

// Route pour la page des paramètres
app.get('/lounas/settings', (req, res) => {
  res.render('luna-settings', {
    title: 'Lounas - Paramètres',
    page: 'settings'
  });
});

// Route pour la page des modèles
app.get('/lounas/models', (req, res) => {
  res.render('luna-models', {
    title: 'Lounas - Modèles',
    page: 'models'
  });
});

// Route pour la page des documents
app.get('/lounas/documents', (req, res) => {
  res.render('luna-documents', {
    title: 'Lounas - Documents',
    page: 'documents'
  });
});

// Route pour la page des prompts
app.get('/lounas/prompts', (req, res) => {
  res.render('luna-prompts', {
    title: 'Lounas - Prompts',
    page: 'prompts'
  });
});

// Route pour la page MCP
app.get('/lounas/mcp', (req, res) => {
  res.render('luna-mcp', {
    title: 'Lounas - MCP',
    page: 'mcp'
  });
});

// Route pour la page Internet
app.get('/lounas/internet', (req, res) => {
  res.render('luna-internet', {
    title: 'Lounas - Internet',
    page: 'internet'
  });
});

// Route pour la page VPN
app.get('/lounas/vpn', (req, res) => {
  res.render('luna-vpn', {
    title: 'Lounas - VPN',
    page: 'vpn'
  });
});

// Route pour la page Antivirus
app.get('/lounas/antivirus', (req, res) => {
  res.render('luna-antivirus', {
    title: 'Lounas - Antivirus',
    page: 'antivirus'
  });
});

// Route pour la page Cognitive
app.get('/lounas/cognitive', (req, res) => {
  res.render('luna-cognitive', {
    title: 'Lounas - Cognitive',
    page: 'cognitive'
  });
});

// Gestionnaire de socket pour les messages
io.on('connection', (socket) => {
  console.log('Nouvelle connexion WebSocket');

  // Gérer les messages de l'utilisateur
  socket.on('lounas message', (data) => {
    console.log('Message reçu:', data.message);

    // Stocker le message dans la mémoire thermique
    thermalMemory.addEntry({
      type: 'input',
      content: data.message,
      timestamp: new Date().toISOString()
    });

    // Simuler une réponse
    setTimeout(() => {
      const response = {
        message: `Je suis Lounas, votre assistant cognitif. Vous avez dit: "${data.message}"`,
        timestamp: new Date().toISOString()
      };

      // Stocker la réponse dans la mémoire thermique
      thermalMemory.addEntry({
        type: 'output',
        content: response.message,
        timestamp: response.timestamp
      });

      // Envoyer la réponse au client
      socket.emit('lounas response', response);
    }, 1000);
  });

  // Gérer la déconnexion
  socket.on('disconnect', () => {
    console.log('Déconnexion WebSocket');
  });
});

// Démarrer le serveur
server.listen(PORT, () => {
  console.log(`Serveur Lounas démarré sur le port ${PORT}`);
  console.log(`Interface accessible à l'adresse http://localhost:${PORT}/lounas`);
});
