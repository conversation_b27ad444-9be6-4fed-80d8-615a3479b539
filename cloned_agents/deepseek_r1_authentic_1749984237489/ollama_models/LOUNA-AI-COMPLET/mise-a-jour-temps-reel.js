/**
 * SYSTÈME DE MISE À JOUR TEMPS RÉEL
 * Met à jour la mémoire selon les bonnes réponses
 */

class MiseAJourTempsReel {
    constructor(memoireThermique) {
        this.memoireThermique = memoireThermique;
        this.bonnesReponses = new Map();
        this.corrections = new Map();
    }

    // RENFORCER MÉMOIRE APRÈS BONNE RÉPONSE
    renforcerMemoire(question, reponse, source) {
        if (!this.memoireThermique) return;

        // Rechercher mémoires liées à cette réponse
        const memoiresLiees = this.memoireThermique.rechercher(question, 5);
        
        for (const memoire of memoiresLiees) {
            if (memoire.pertinence > 0.5) {
                // Augmenter utilisation
                const memoireData = this.memoireThermique.memoires.get(memoire.id);
                if (memoireData) {
                    memoireData.utilisation++;
                    
                    // Recalculer température (plus d'utilisation = plus chaud)
                    const nouvelleTemp = this.memoireThermique.calculerTemperature(
                        memoireData.importance,
                        this.calculerRecence(memoireData.timestamp),
                        Math.min(1, memoireData.utilisation / 10)
                    );
                    
                    memoireData.temperature = nouvelleTemp;
                    memoireData.zone = this.memoireThermique.determinerZone(nouvelleTemp);
                    
                    console.log(`🔥 Mémoire renforcée: ${memoire.id} (temp: ${nouvelleTemp.toFixed(1)}°C, util: ${memoireData.utilisation})`);
                }
            }
        }

        // Sauvegarder les changements
        this.memoireThermique.sauvegarderMemoire();
    }

    // CORRIGER ERREUR IMMÉDIATEMENT
    corrigerErreur(question, mauvaiseReponse, bonneReponse) {
        console.log(`🔧 Correction erreur: "${mauvaiseReponse}" → "${bonneReponse}"`);
        
        // Stocker la correction
        this.corrections.set(question, {
            erreur: mauvaiseReponse,
            correction: bonneReponse,
            timestamp: Date.now()
        });

        // Ajouter la bonne réponse en mémoire avec importance élevée
        if (this.memoireThermique) {
            this.memoireThermique.stocker(
                `CORRECTION: ${question} = ${bonneReponse} (PAS ${mauvaiseReponse})`,
                'Correction automatique',
                0.95 // Importance très élevée
            );
        }
    }

    // CALCULER RÉCENCE
    calculerRecence(timestamp) {
        const maintenant = Date.now();
        const ageJours = (maintenant - timestamp) / (1000 * 60 * 60 * 24);
        return Math.max(0, 1 - (ageJours / 30)); // Décroît sur 30 jours
    }

    // INTÉGRER FEEDBACK UTILISATEUR
    integrerFeedback(question, reponse, feedback) {
        if (feedback === 'correct' || feedback === 'bon') {
            this.renforcerMemoire(question, reponse, 'feedback_positif');
            console.log(`✅ Feedback positif intégré pour: "${question}"`);
        } else if (feedback === 'incorrect' || feedback === 'faux') {
            // Diminuer température des mémoires liées
            const memoiresLiees = this.memoireThermique?.rechercher(question, 3) || [];
            for (const memoire of memoiresLiees) {
                const memoireData = this.memoireThermique.memoires.get(memoire.id);
                if (memoireData) {
                    memoireData.temperature = Math.max(15, memoireData.temperature - 10);
                    memoireData.zone = this.memoireThermique.determinerZone(memoireData.temperature);
                }
            }
            console.log(`❌ Feedback négatif intégré pour: "${question}"`);
        }
    }

    // OPTIMISER ORGANISATION MÉMOIRE
    optimiserOrganisation() {
        if (!this.memoireThermique) return;

        console.log(`🔧 Optimisation organisation mémoire...`);
        
        // Identifier mémoires très utilisées
        const memoiresTresUtilisees = [];
        for (const [id, memoire] of this.memoireThermique.memoires) {
            if (memoire.utilisation > 5) {
                memoiresTresUtilisees.push({ id, memoire });
            }
        }

        // Augmenter leur importance
        for (const { id, memoire } of memoiresTresUtilisees) {
            memoire.importance = Math.min(1.0, memoire.importance + 0.1);
            memoire.temperature = this.memoireThermique.calculerTemperature(
                memoire.importance,
                this.calculerRecence(memoire.timestamp),
                Math.min(1, memoire.utilisation / 10)
            );
            memoire.zone = this.memoireThermique.determinerZone(memoire.temperature);
        }

        console.log(`🔥 ${memoiresTresUtilisees.length} mémoires optimisées`);
        this.memoireThermique.sauvegarderMemoire();
    }

    // STATISTIQUES
    getStats() {
        return {
            bonnes_reponses: this.bonnesReponses.size,
            corrections: this.corrections.size,
            derniere_optimisation: this.derniereOptimisation || 'Jamais'
        };
    }
}

module.exports = { MiseAJourTempsReel };
