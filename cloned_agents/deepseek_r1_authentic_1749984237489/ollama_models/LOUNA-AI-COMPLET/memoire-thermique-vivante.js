/**
 * MÉMOIRE THERMIQUE VIVANTE
 * Mise à jour automatique des informations
 * Vérification et actualisation continue
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');

class MemoireThermiqueVivante {
    constructor(config = {}) {
        console.log('🧠 MÉMOIRE THERMIQUE VIVANTE');
        console.log('============================');
        console.log('✅ Mise à jour automatique des informations');
        console.log('✅ Vérification continue de la pertinence');
        console.log('✅ Actualisation intelligente');

        this.config = {
            // Configuration de base
            kyber_auto_install: config.kyber_auto_install !== false,
            kyber_persistent: config.kyber_persistent !== false,
            auto_cycle_threshold: config.auto_cycle_threshold || 5,
            dataPath: config.dataPath || path.join(__dirname, 'data', 'memory'),
            neurones_auto_install: true,
            neurones_max_limit: false,
            
            // Configuration mémoire vivante
            auto_update_enabled: true,
            update_interval: 300000, // 5 minutes
            verification_interval: 600000, // 10 minutes
            max_age_for_update: 86400000, // 24 heures
            doubt_threshold: 0.3, // Seuil de doute pour mise à jour
            freshness_decay: 0.95, // Décroissance fraîcheur
            
            // Domaines à surveiller
            monitored_topics: [
                'intelligence artificielle',
                'technologie',
                'actualités',
                'science',
                'programmation'
            ]
        };

        // NEURONES AUTOMATIQUES INFINIS
        this.neurones_system = {
            total_installed: 201000000,
            active_count: 0,
            auto_installations: 0,
            specializations: [
                'verification_facts',
                'update_detection',
                'freshness_analysis',
                'doubt_assessment',
                'information_validation',
                'temporal_tracking',
                'relevance_scoring',
                'compression_semantique',
                'pattern_recognition'
            ]
        };

        // ACCÉLÉRATEURS KYBER
        this.kyber_accelerators = {
            installed: [],
            active: [],
            auto_installs: 0
        };

        // NIVEAUX MÉMOIRE THERMIQUE
        this.instantMemory = {};
        this.shortTerm = {};
        this.workingMemory = {};
        this.mediumTerm = {};
        this.longTerm = {};
        this.dreamMemory = {};

        // SYSTÈME DE MISE À JOUR VIVANTE
        this.updateSystem = {
            lastUpdate: Date.now(),
            updateQueue: [],
            verificationQueue: [],
            doubts: new Map(),
            freshness: new Map(),
            updateHistory: [],
            activeUpdates: 0
        };

        // STATISTIQUES
        this.stats = {
            totalEntries: 0,
            cyclesPerformed: 0,
            auto_cycles_triggered: 0,
            operations_since_cycle: 0,
            averageTemperature: 0,
            updates_performed: 0,
            verifications_done: 0,
            information_refreshed: 0
        };

        this.init();
    }

    async init() {
        try {
            console.log('🚀 Initialisation mémoire vivante...');
            
            await this.initDataDir();
            await this.loadMemoryState();
            await this.restoreKyberAccelerators();
            await this.restoreNeurones();
            
            // Démarrer système de mise à jour vivante
            this.startLivingMemorySystem();
            
            console.log('✅ Mémoire thermique vivante initialisée');
            console.log(`🧠 ${this.neurones_system.total_installed.toLocaleString()} neurones disponibles (EXPANSION AUTOMATIQUE INFINIE)`);
            console.log(`⚡ ${this.kyber_accelerators.active.length} accélérateurs KYBER actifs`);
            console.log(`🔄 Système de mise à jour automatique actif`);

        } catch (error) {
            console.log(`❌ Erreur initialisation: ${error.message}`);
        }
    }

    async initDataDir() {
        if (!fs.existsSync(this.config.dataPath)) {
            fs.mkdirSync(this.config.dataPath, { recursive: true });
        }
    }

    async loadMemoryState() {
        console.log('📥 Chargement état mémoire...');
        
        const memoryFiles = [
            { file: 'working_memory.json', target: 'workingMemory' },
            { file: 'medium_term_memory.json', target: 'mediumTerm' },
            { file: 'long_term_memory.json', target: 'longTerm' },
            { file: 'dream_memory.json', target: 'dreamMemory' },
            { file: 'update_system.json', target: 'updateSystem' }
        ];

        for (const { file, target } of memoryFiles) {
            try {
                const filePath = path.join(this.config.dataPath, file);
                if (fs.existsSync(filePath)) {
                    const data = fs.readFileSync(filePath, 'utf8');
                    this[target] = { ...this[target], ...JSON.parse(data) };
                }
            } catch (error) {
                console.log(`⚠️ Erreur chargement ${file}: ${error.message}`);
            }
        }
    }

    async restoreKyberAccelerators() {
        for (let i = 0; i < 3; i++) {
            this.installKyberAcceleratorAutomatic();
        }
    }

    async restoreNeurones() {
        this.neurones_system.active_count = Math.floor(this.neurones_system.total_installed * 0.1);
    }

    // SYSTÈME DE MÉMOIRE VIVANTE
    startLivingMemorySystem() {
        console.log('🔄 Démarrage système mémoire vivante...');
        
        // Mise à jour automatique
        setInterval(() => {
            this.performAutomaticUpdate();
        }, this.config.update_interval);

        // Vérification continue
        setInterval(() => {
            this.performContinuousVerification();
        }, this.config.verification_interval);

        // Analyse de fraîcheur
        setInterval(() => {
            this.analyzeFreshness();
        }, 60000); // Chaque minute

        // Surveillance automatique
        setInterval(() => {
            this.monitorInformationHealth();
        }, 30000); // Toutes les 30 secondes
    }

    add(key, content, importance = 0.5, category = 'general') {
        const entryId = `mem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        const temperature = this.calculateAutoTemperature(importance, category);
        
        const entry = {
            id: entryId,
            key: key,
            content: content,
            originalContent: content,
            temperature: temperature,
            importance: importance,
            category: category,
            timestamp: Date.now(),
            lastAccessed: Date.now(),
            lastUpdated: Date.now(),
            accessCount: 0,
            updateCount: 0,
            freshness: 1.0,
            doubt_level: 0.0,
            verification_needed: false,
            sources: [],
            update_history: []
        };

        this.storeInAppropriateLevel(entry);
        this.stats.totalEntries++;
        this.stats.operations_since_cycle++;

        // Marquer pour surveillance si sujet surveillé
        if (this.isMonitoredTopic(content)) {
            this.updateSystem.verificationQueue.push(entryId);
        }

        // DÉCLENCHER AUTO-INSTALLATION NEURONES (plus fréquent)
        this.checkNeuronesAutoInstall();
        
        // DÉCLENCHER AUTO-INSTALLATION KYBER
        this.checkKyberAutoInstall();

        console.log(`💾 Ajouté avec surveillance: ${entryId} (temp: ${temperature.toFixed(2)})`);

        return entryId;
    }

    calculateAutoTemperature(importance, category) {
        let temperature = importance;

        const categoryBoosts = {
            'urgent': 0.3,
            'important': 0.2,
            'learning': 0.15,
            'creative': 0.1,
            'general': 0.0,
            'actualite': 0.25,
            'technologie': 0.2
        };

        temperature += categoryBoosts[category] || 0;

        if (this.kyber_accelerators.active.length > 0) {
            temperature *= (1 + this.kyber_accelerators.active.length * 0.1);
        }

        return Math.min(1.0, temperature);
    }

    storeInAppropriateLevel(entry) {
        if (entry.temperature >= 0.8) {
            this.instantMemory[entry.id] = entry;
        } else if (entry.temperature >= 0.6) {
            this.shortTerm[entry.id] = entry;
        } else if (entry.temperature >= 0.4) {
            this.workingMemory[entry.id] = entry;
        } else if (entry.temperature >= 0.2) {
            this.mediumTerm[entry.id] = entry;
        } else {
            this.longTerm[entry.id] = entry;
        }
    }

    // MISE À JOUR AUTOMATIQUE
    async performAutomaticUpdate() {
        console.log('🔄 Mise à jour automatique de la mémoire...');
        
        try {
            // Identifier les informations à mettre à jour
            const candidatesForUpdate = this.identifyUpdateCandidates();
            
            for (const candidate of candidatesForUpdate.slice(0, 3)) { // Limiter à 3 par cycle
                await this.updateInformation(candidate);
            }
            
            this.updateSystem.lastUpdate = Date.now();
            this.stats.updates_performed++;
            
            console.log(`✅ Mise à jour automatique terminée (${candidatesForUpdate.length} candidats)`);
            
        } catch (error) {
            console.log(`❌ Erreur mise à jour automatique: ${error.message}`);
        }
    }

    identifyUpdateCandidates() {
        const candidates = [];
        const now = Date.now();
        
        const allLevels = [this.instantMemory, this.shortTerm, this.workingMemory, this.mediumTerm, this.longTerm];
        
        allLevels.forEach(level => {
            Object.values(level).forEach(entry => {
                // Critères de mise à jour
                const age = now - entry.lastUpdated;
                const isOld = age > this.config.max_age_for_update;
                const hasDoubt = entry.doubt_level > this.config.doubt_threshold;
                const isMonitored = this.isMonitoredTopic(entry.content);
                const lowFreshness = entry.freshness < 0.5;
                
                if (isOld || hasDoubt || (isMonitored && lowFreshness)) {
                    candidates.push({
                        entry,
                        priority: this.calculateUpdatePriority(entry, age, hasDoubt, isMonitored)
                    });
                }
            });
        });
        
        return candidates.sort((a, b) => b.priority - a.priority);
    }

    calculateUpdatePriority(entry, age, hasDoubt, isMonitored) {
        let priority = 0;
        
        // Priorité basée sur l'importance
        priority += entry.importance * 100;
        
        // Priorité basée sur l'âge
        priority += (age / this.config.max_age_for_update) * 50;
        
        // Priorité basée sur le doute
        if (hasDoubt) priority += entry.doubt_level * 75;
        
        // Priorité basée sur la surveillance
        if (isMonitored) priority += 60;
        
        // Priorité basée sur l'accès récent
        priority += entry.accessCount * 10;
        
        // Priorité basée sur la fraîcheur
        priority += (1 - entry.freshness) * 40;
        
        return priority;
    }

    async updateInformation(candidate) {
        const entry = candidate.entry;
        
        try {
            console.log(`🔄 Mise à jour: ${entry.key}`);
            
            // Rechercher informations actualisées
            const updatedInfo = await this.searchUpdatedInformation(entry);
            
            if (updatedInfo && this.isInformationDifferent(entry.content, updatedInfo)) {
                // Sauvegarder ancienne version
                entry.update_history.push({
                    timestamp: Date.now(),
                    old_content: entry.content,
                    new_content: updatedInfo,
                    reason: 'automatic_update'
                });
                
                // Mettre à jour le contenu
                entry.content = updatedInfo;
                entry.lastUpdated = Date.now();
                entry.updateCount++;
                entry.freshness = 1.0;
                entry.doubt_level = 0.0;
                
                // Augmenter température (information fraîche)
                entry.temperature = Math.min(1.0, entry.temperature + 0.1);
                
                this.stats.information_refreshed++;
                
                console.log(`✅ Information mise à jour: ${entry.key}`);
            } else {
                // Information toujours valide
                entry.freshness = Math.min(1.0, entry.freshness + 0.1);
                entry.doubt_level = Math.max(0.0, entry.doubt_level - 0.1);
                entry.lastUpdated = Date.now();
            }
            
        } catch (error) {
            console.log(`❌ Erreur mise à jour ${entry.key}: ${error.message}`);
            entry.doubt_level = Math.min(1.0, entry.doubt_level + 0.2);
        }
    }

    async searchUpdatedInformation(entry) {
        // Simuler recherche d'informations actualisées
        // Dans une vraie implémentation, ceci ferait appel à des APIs
        
        const searchTerms = this.extractSearchTerms(entry.content);
        
        try {
            // Exemple de recherche (à remplacer par vraie API)
            const searchQuery = searchTerms.slice(0, 3).join(' ');
            
            // Simuler résultat de recherche
            if (Math.random() > 0.7) { // 30% de chance de trouver une mise à jour
                return `${entry.content} [Mis à jour le ${new Date().toLocaleDateString()}]`;
            }
            
            return null;
            
        } catch (error) {
            console.log(`❌ Erreur recherche pour ${entry.key}: ${error.message}`);
            return null;
        }
    }

    extractSearchTerms(content) {
        // Extraire mots-clés pour recherche
        const words = content.toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 3)
            .filter(word => !['dans', 'avec', 'pour', 'cette', 'sont', 'plus'].includes(word));
        
        return [...new Set(words)].slice(0, 5);
    }

    isInformationDifferent(oldContent, newContent) {
        // Comparer contenus pour détecter changements significatifs
        const similarity = this.calculateSimilarity(oldContent, newContent);
        return similarity < 0.8; // Seuil de différence
    }

    calculateSimilarity(text1, text2) {
        // Calcul simple de similarité (peut être amélioré)
        const words1 = new Set(text1.toLowerCase().split(/\s+/));
        const words2 = new Set(text2.toLowerCase().split(/\s+/));
        
        const intersection = new Set([...words1].filter(x => words2.has(x)));
        const union = new Set([...words1, ...words2]);
        
        return intersection.size / union.size;
    }

    // VÉRIFICATION CONTINUE
    async performContinuousVerification() {
        console.log('🔍 Vérification continue de la mémoire...');
        
        try {
            // Vérifier cohérence des informations
            await this.verifyInformationConsistency();
            
            // Détecter contradictions
            await this.detectContradictions();
            
            // Évaluer niveau de doute
            await this.assessDoubtLevels();
            
            this.stats.verifications_done++;
            
        } catch (error) {
            console.log(`❌ Erreur vérification: ${error.message}`);
        }
    }

    async verifyInformationConsistency() {
        // Vérifier cohérence entre informations liées
        const allEntries = this.getAllEntries();
        
        for (const entry of allEntries) {
            const relatedEntries = this.findRelatedEntries(entry);
            
            if (relatedEntries.length > 0) {
                const consistencyScore = this.calculateConsistencyScore(entry, relatedEntries);
                
                if (consistencyScore < 0.5) {
                    entry.doubt_level = Math.min(1.0, entry.doubt_level + 0.3);
                    entry.verification_needed = true;
                }
            }
        }
    }

    async detectContradictions() {
        // Détecter contradictions dans la mémoire
        const allEntries = this.getAllEntries();
        
        for (let i = 0; i < allEntries.length; i++) {
            for (let j = i + 1; j < allEntries.length; j++) {
                const contradiction = this.detectContradiction(allEntries[i], allEntries[j]);
                
                if (contradiction) {
                    allEntries[i].doubt_level = Math.min(1.0, allEntries[i].doubt_level + 0.2);
                    allEntries[j].doubt_level = Math.min(1.0, allEntries[j].doubt_level + 0.2);
                }
            }
        }
    }

    detectContradiction(entry1, entry2) {
        // Détecter si deux entrées se contredisent
        const similarity = this.calculateSimilarity(entry1.content, entry2.content);
        
        // Si très similaires mais avec des conclusions opposées
        if (similarity > 0.6) {
            const oppositeWords = ['non', 'pas', 'jamais', 'aucun', 'faux', 'incorrect'];
            const hasOpposite1 = oppositeWords.some(word => entry1.content.toLowerCase().includes(word));
            const hasOpposite2 = oppositeWords.some(word => entry2.content.toLowerCase().includes(word));
            
            return hasOpposite1 !== hasOpposite2;
        }
        
        return false;
    }

    async assessDoubtLevels() {
        // Évaluer et ajuster niveaux de doute
        const allEntries = this.getAllEntries();
        
        allEntries.forEach(entry => {
            const age = Date.now() - entry.lastUpdated;
            const ageFactor = age / this.config.max_age_for_update;
            
            // Augmenter doute avec l'âge
            entry.doubt_level = Math.min(1.0, entry.doubt_level + ageFactor * 0.1);
            
            // Diminuer fraîcheur avec le temps
            entry.freshness = Math.max(0.0, entry.freshness * this.config.freshness_decay);
        });
    }

    // ANALYSE DE FRAÎCHEUR
    analyzeFreshness() {
        const allEntries = this.getAllEntries();
        
        allEntries.forEach(entry => {
            const age = Date.now() - entry.lastUpdated;
            const accessRecency = Date.now() - entry.lastAccessed;
            
            // Calculer score de fraîcheur
            let freshnessScore = 1.0;
            freshnessScore -= (age / this.config.max_age_for_update) * 0.5;
            freshnessScore -= (accessRecency / this.config.max_age_for_update) * 0.3;
            freshnessScore = Math.max(0.0, Math.min(1.0, freshnessScore));
            
            entry.freshness = freshnessScore;
            
            // Si fraîcheur très faible, marquer pour mise à jour
            if (freshnessScore < 0.3 && !this.updateSystem.updateQueue.includes(entry.id)) {
                this.updateSystem.updateQueue.push(entry.id);
            }
        });
    }

    // SURVEILLANCE SANTÉ INFORMATION
    monitorInformationHealth() {
        const allEntries = this.getAllEntries();
        const now = Date.now();
        
        let totalDoubt = 0;
        let totalFreshness = 0;
        let needsUpdate = 0;
        
        allEntries.forEach(entry => {
            totalDoubt += entry.doubt_level;
            totalFreshness += entry.freshness;
            
            if (entry.verification_needed || entry.doubt_level > 0.5) {
                needsUpdate++;
            }
        });
        
        if (allEntries.length > 0) {
            const avgDoubt = totalDoubt / allEntries.length;
            const avgFreshness = totalFreshness / allEntries.length;
            
            // Si santé générale dégradée, déclencher mise à jour
            if (avgDoubt > 0.4 || avgFreshness < 0.6) {
                console.log('⚠️ Santé mémoire dégradée, déclenchement mise à jour...');
                this.performAutomaticUpdate();
            }
        }
    }

    // UTILITAIRES
    isMonitoredTopic(content) {
        return this.config.monitored_topics.some(topic => 
            content.toLowerCase().includes(topic.toLowerCase())
        );
    }

    getAllEntries() {
        return [
            ...Object.values(this.instantMemory),
            ...Object.values(this.shortTerm),
            ...Object.values(this.workingMemory),
            ...Object.values(this.mediumTerm),
            ...Object.values(this.longTerm)
        ];
    }

    findRelatedEntries(entry) {
        const allEntries = this.getAllEntries();
        const related = [];
        
        allEntries.forEach(otherEntry => {
            if (otherEntry.id !== entry.id) {
                const similarity = this.calculateSimilarity(entry.content, otherEntry.content);
                if (similarity > 0.3) {
                    related.push(otherEntry);
                }
            }
        });
        
        return related;
    }

    calculateConsistencyScore(entry, relatedEntries) {
        // Calculer score de cohérence avec entrées liées
        let totalConsistency = 0;
        
        relatedEntries.forEach(related => {
            const similarity = this.calculateSimilarity(entry.content, related.content);
            const contradiction = this.detectContradiction(entry, related);
            
            let consistency = similarity;
            if (contradiction) consistency *= 0.3;
            
            totalConsistency += consistency;
        });
        
        return relatedEntries.length > 0 ? totalConsistency / relatedEntries.length : 1.0;
    }

    // RECHERCHE AVEC MÉMOIRE VIVANTE
    retrieve(query, maxResults = 5) {
        console.log(`🔍 Recherche avec mémoire vivante: "${query}"`);
        
        const results = [];
        const allLevels = [this.instantMemory, this.shortTerm, this.workingMemory, this.mediumTerm, this.longTerm];

        allLevels.forEach(level => {
            Object.values(level).forEach(entry => {
                if (entry.key && entry.content) {
                    const keyMatch = entry.key.toLowerCase().includes(query.toLowerCase());
                    const contentMatch = entry.content.toLowerCase().includes(query.toLowerCase());
                    
                    if (keyMatch || contentMatch) {
                        let matchScore = 0;
                        if (keyMatch) matchScore += 0.8;
                        if (contentMatch) matchScore += 0.6;

                        // Bonus pour fraîcheur
                        matchScore *= (0.5 + entry.freshness * 0.5);
                        
                        // Malus pour doute
                        matchScore *= (1.0 - entry.doubt_level * 0.3);

                        results.push({
                            ...entry,
                            matchScore: matchScore
                        });

                        // Marquer accès pour mise à jour température
                        entry.lastAccessed = Date.now();
                        entry.accessCount++;
                        entry.temperature = Math.min(1.0, entry.temperature + 0.02);
                    }
                }
            });
        });

        return results
            .sort((a, b) => b.matchScore - a.matchScore)
            .slice(0, maxResults);
    }

    // API POUR MISE À JOUR MANUELLE
    async forceUpdateMemory() {
        console.log('🔄 Mise à jour forcée de la mémoire...');
        
        await this.performAutomaticUpdate();
        await this.performContinuousVerification();
        
        return {
            success: true,
            updates_performed: this.stats.updates_performed,
            verifications_done: this.stats.verifications_done,
            information_refreshed: this.stats.information_refreshed
        };
    }

    // INSTALLATION AUTOMATIQUE KYBER
    installKyberAcceleratorAutomatic() {
        const acceleratorId = `kyber_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;

        const accelerator = {
            id: acceleratorId,
            type: 'compression_semantique',
            performance: Math.random() * 50 + 50,
            installed: Date.now(),
            active: true,
            auto_installed: true
        };

        this.kyber_accelerators.installed.push(accelerator);
        this.kyber_accelerators.active.push(accelerator);
        this.kyber_accelerators.auto_installs++;
    }

    // STATISTIQUES
    getStats() {
        this.updateStatsAutomatic();
        
        return {
            totalEntries: this.stats.totalEntries,
            instantEntries: Object.keys(this.instantMemory).length,
            shortTermEntries: Object.keys(this.shortTerm).length,
            workingMemoryEntries: Object.keys(this.workingMemory).length,
            mediumTermEntries: Object.keys(this.mediumTerm).length,
            longTermEntries: Object.keys(this.longTerm).length,
            dreamMemoryEntries: Object.keys(this.dreamMemory).length,
            averageTemperature: this.stats.averageTemperature,
            neurones_system: {
                total_installed: this.neurones_system.total_installed,
                active_count: this.neurones_system.active_count,
                auto_installations: this.neurones_system.auto_installations,
                specializations: this.neurones_system.specializations.length
            },
            kyber_accelerators: {
                installed: this.kyber_accelerators.installed.length,
                active: this.kyber_accelerators.active.length,
                auto_installs: this.kyber_accelerators.auto_installs
            },
            automation: {
                auto_cycles: this.stats.auto_cycles_triggered,
                operations_since_cycle: this.stats.operations_since_cycle,
                monitoring_active: true
            },
            living_memory: {
                updates_performed: this.stats.updates_performed,
                verifications_done: this.stats.verifications_done,
                information_refreshed: this.stats.information_refreshed,
                last_update: this.updateSystem.lastUpdate,
                update_queue_size: this.updateSystem.updateQueue.length,
                verification_queue_size: this.updateSystem.verificationQueue.length
            }
        };
    }

    updateStatsAutomatic() {
        const allEntries = this.getAllEntries();

        if (allEntries.length > 0) {
            const totalTemp = allEntries.reduce((sum, entry) => sum + entry.temperature, 0);
            this.stats.averageTemperature = totalTemp / allEntries.length;
        }
    }

    // SAUVEGARDE
    async saveMemoryState() {
        try {
            const saves = [
                { file: 'working_memory.json', data: this.workingMemory },
                { file: 'medium_term_memory.json', data: this.mediumTerm },
                { file: 'long_term_memory.json', data: this.longTerm },
                { file: 'dream_memory.json', data: this.dreamMemory },
                { file: 'update_system.json', data: this.updateSystem }
            ];

            for (const { file, data } of saves) {
                const filePath = path.join(this.config.dataPath, file);
                fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
            }
        } catch (error) {
            console.log(`⚠️ Erreur sauvegarde: ${error.message}`);
        }
    }

    // VÉRIFICATION AUTO-INSTALLATION NEURONES
    checkNeuronesAutoInstall() {
        // Déclencher plus souvent (tous les 2 ajouts)
        if (this.stats.totalEntries % 2 === 0) {
            this.installNeuronesAutomatic();
        }
    }

    installNeuronesAutomatic() {
        const expansionRate = 0.1; // 10% d'expansion
        const newNeurones = Math.floor(this.neurones_system.total_installed * expansionRate);

        this.neurones_system.total_installed += newNeurones;
        this.neurones_system.active_count += Math.floor(newNeurones * 0.1);
        this.neurones_system.auto_installations++;

        console.log(`🧠 NEURONES AUTO-INSTALLÉS: +${(newNeurones / 1000000).toFixed(1)}M`);
        console.log(`🧠 Total neurones: ${(this.neurones_system.total_installed / 1000000).toFixed(1)}M`);
        console.log(`🧠 Neurones actifs: ${(this.neurones_system.active_count / 1000000).toFixed(1)}M`);

        this.specializeNewNeurones(newNeurones);
    }

    specializeNewNeurones(newNeurones) {
        const neuronesParSpecialisation = Math.floor(newNeurones / this.neurones_system.specializations.length);
        
        console.log(`🎯 Spécialisation automatique: ${neuronesParSpecialisation.toLocaleString()} neurones par domaine`);
        this.neurones_system.specializations.forEach(specialisation => {
            console.log(`   - ${specialisation}: ${neuronesParSpecialisation.toLocaleString()} neurones`);
        });
    }

    // VÉRIFICATION AUTO-INSTALLATION KYBER
    checkKyberAutoInstall() {
        // Déclencher tous les 3 ajouts
        if (this.stats.totalEntries % 3 === 0 && this.kyber_accelerators.active.length < 15) {
            this.installKyberAcceleratorAutomatic();
        }
    }
}

module.exports = { MemoireThermiqueVivante };
