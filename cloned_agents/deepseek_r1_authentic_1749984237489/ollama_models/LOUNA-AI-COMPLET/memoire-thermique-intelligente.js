/**
 * MÉMOIRE THERMIQUE INTELLIGENTE POUR LOUNA-AI
 * Fonctionne vraiment comme un cerveau avec évolution thermique
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

class MemoireThermiqueIntelligente {
    constructor() {
        this.fichierMemoire = path.join(__dirname, 'memoire-thermique-intelligente.json');
        this.memoires = new Map();
        this.curseurThermique = 0.5;
        this.temperatureCPU = 50; // Base
        
        // ZONES THERMIQUES RÉELLES (comme un cerveau)
        this.zones = {
            0: { min: 15, max: 25, nom: 'Hibernation', couleur: '🧊' },      // Mémoires dormantes
            1: { min: 25, max: 35, nom: 'Stockage froid', couleur: '❄️' },   // Stockage long terme
            2: { min: 35, max: 45, nom: 'Mé<PERSON>ire tiède', couleur: '🌡️' },    // Accès occasionnel
            3: { min: 45, max: 55, nom: 'Mémoire active', couleur: '🔥' },   // Utilisation régulière
            4: { min: 55, max: 65, nom: 'Mémoire chaude', couleur: '🌋' },   // Très utilisé
            5: { min: 65, max: 75, nom: 'Mémoire brûlante', couleur: '⚡' }  // Ultra-actif
        };
        
        this.chargerMemoire();
        this.demarrerEvolutionThermique();
    }

    // OBTENIR VRAIE TEMPÉRATURE CPU
    obtenirTemperatureCPU() {
        try {
            // Simulation basée sur la charge CPU réelle
            const cpus = os.cpus();
            let totalIdle = 0;
            let totalTick = 0;
            
            cpus.forEach(cpu => {
                for (type in cpu.times) {
                    totalTick += cpu.times[type];
                }
                totalIdle += cpu.times.idle;
            });
            
            const idle = totalIdle / cpus.length;
            const total = totalTick / cpus.length;
            const usage = 100 - ~~(100 * idle / total);
            
            // Convertir usage CPU en température (30-70°C)
            this.temperatureCPU = 30 + (usage * 0.4);
            
            return this.temperatureCPU;
        } catch (error) {
            // Fallback avec variation aléatoire
            this.temperatureCPU += (Math.random() - 0.5) * 2;
            this.temperatureCPU = Math.max(30, Math.min(70, this.temperatureCPU));
            return this.temperatureCPU;
        }
    }

    // CALCULER TEMPÉRATURE INTELLIGENTE
    calculerTemperatureIntelligente(importance, recence, utilisation, pertinence = 0) {
        const tempCPU = this.obtenirTemperatureCPU();
        
        // Base : température CPU
        let temperature = tempCPU;
        
        // Facteur importance (0-1) : +/- 15°C
        temperature += (importance - 0.5) * 30;
        
        // Facteur récence (0-1) : +/- 10°C
        temperature += recence * 20 - 10;
        
        // Facteur utilisation (normalisé) : +/- 10°C
        const utilisationNorm = Math.min(1, utilisation / 20);
        temperature += utilisationNorm * 20 - 10;
        
        // Facteur pertinence (si recherche récente) : +5°C
        temperature += pertinence * 5;
        
        // Limiter entre 15-75°C
        return Math.max(15, Math.min(75, temperature));
    }

    // DÉTERMINER ZONE INTELLIGENTE
    determinerZone(temperature) {
        for (let zone = 5; zone >= 0; zone--) {
            if (temperature >= this.zones[zone].min) {
                return zone;
            }
        }
        return 0;
    }

    // CALCULER RÉCENCE INTELLIGENTE
    calculerRecence(timestamp) {
        const maintenant = Date.now();
        const ageHeures = (maintenant - timestamp) / (1000 * 60 * 60);
        
        // Décroissance exponentielle sur 24h
        return Math.exp(-ageHeures / 24);
    }

    // STOCKER AVEC INTELLIGENCE THERMIQUE
    stocker(contenu, source, importance = 0.5) {
        const id = `mem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const timestamp = Date.now();
        const recence = 1.0; // Nouveau = récent
        const utilisation = 1; // Première utilisation
        
        const temperature = this.calculerTemperatureIntelligente(importance, recence, utilisation);
        const zone = this.determinerZone(temperature);
        
        const memoire = {
            id: id,
            contenu: contenu,
            source: source,
            timestamp: timestamp,
            importance: importance,
            utilisation: utilisation,
            temperature: temperature,
            zone: zone,
            dernierAcces: timestamp,
            liens: [], // Liens vers autres mémoires
            evolution: [{ timestamp, temperature, zone, action: 'creation' }]
        };
        
        this.memoires.set(id, memoire);
        console.log(`🧠 Mémoire stockée: ${id} (${temperature.toFixed(1)}°C, zone ${zone} ${this.zones[zone].couleur})`);
        
        this.sauvegarderMemoire();
        return id;
    }

    // RECHERCHE INTELLIGENTE AVEC ÉVOLUTION THERMIQUE
    rechercher(requete, limite = 5) {
        const resultats = [];
        const requeteLower = requete.toLowerCase();
        
        for (const [id, memoire] of this.memoires) {
            let pertinence = 0;
            
            // Calcul pertinence textuelle
            const contenuLower = memoire.contenu.toLowerCase();
            if (contenuLower.includes(requeteLower)) {
                pertinence += 1.0;
            }
            
            // Recherche par mots-clés
            const mots = requeteLower.split(' ');
            for (const mot of mots) {
                if (mot.length > 2 && contenuLower.includes(mot)) {
                    pertinence += 0.3;
                }
            }
            
            if (pertinence > 0.1) {
                // ÉVOLUTION THERMIQUE : Accès augmente température
                memoire.utilisation++;
                memoire.dernierAcces = Date.now();
                
                const nouvelleRecence = this.calculerRecence(memoire.timestamp);
                const nouvelleTemp = this.calculerTemperatureIntelligente(
                    memoire.importance, 
                    nouvelleRecence, 
                    memoire.utilisation,
                    pertinence
                );
                
                const ancienneZone = memoire.zone;
                memoire.temperature = nouvelleTemp;
                memoire.zone = this.determinerZone(nouvelleTemp);
                
                // Enregistrer évolution
                memoire.evolution.push({
                    timestamp: Date.now(),
                    temperature: nouvelleTemp,
                    zone: memoire.zone,
                    action: 'acces_recherche',
                    pertinence: pertinence
                });
                
                if (memoire.zone !== ancienneZone) {
                    console.log(`🌡️ Migration thermique: ${id} zone ${ancienneZone} → ${memoire.zone} (${nouvelleTemp.toFixed(1)}°C)`);
                }
                
                resultats.push({
                    id: id,
                    contenu: memoire.contenu,
                    source: memoire.source,
                    pertinence: pertinence,
                    temperature: memoire.temperature,
                    zone: memoire.zone,
                    utilisation: memoire.utilisation
                });
            }
        }
        
        // Trier par pertinence ET température (mémoires chaudes prioritaires)
        resultats.sort((a, b) => {
            const scoreA = a.pertinence + (a.temperature / 100);
            const scoreB = b.pertinence + (b.temperature / 100);
            return scoreB - scoreA;
        });
        
        this.sauvegarderMemoire();
        return resultats.slice(0, limite);
    }

    // ÉVOLUTION THERMIQUE AUTOMATIQUE (comme un cerveau)
    demarrerEvolutionThermique() {
        setInterval(() => {
            this.evolutionThermique();
        }, 30000); // Toutes les 30 secondes
    }

    // PROCESSUS D'ÉVOLUTION THERMIQUE
    evolutionThermique() {
        console.log(`🌡️ Évolution thermique (CPU: ${this.obtenirTemperatureCPU().toFixed(1)}°C)`);
        
        let migrations = 0;
        const maintenant = Date.now();
        
        for (const [id, memoire] of this.memoires) {
            const ancienneTemp = memoire.temperature;
            const ancienneZone = memoire.zone;
            
            // Refroidissement naturel si pas utilisé récemment
            const tempsDepuisAcces = (maintenant - memoire.dernierAcces) / (1000 * 60); // minutes
            let facteurRefroidissement = 1;
            
            if (tempsDepuisAcces > 60) { // Plus d'1h
                facteurRefroidissement = 0.98; // Refroidissement lent
            }
            if (tempsDepuisAcces > 1440) { // Plus d'1 jour
                facteurRefroidissement = 0.95; // Refroidissement plus rapide
            }
            
            // Recalculer température avec évolution
            const recence = this.calculerRecence(memoire.timestamp);
            let nouvelleTemp = this.calculerTemperatureIntelligente(
                memoire.importance,
                recence,
                memoire.utilisation
            );
            
            // Appliquer refroidissement
            nouvelleTemp *= facteurRefroidissement;
            
            // Influence de la température CPU globale
            const tempCPU = this.obtenirTemperatureCPU();
            nouvelleTemp += (tempCPU - 50) * 0.1; // Légère influence
            
            // Limiter et appliquer
            memoire.temperature = Math.max(15, Math.min(75, nouvelleTemp));
            memoire.zone = this.determinerZone(memoire.temperature);
            
            // Enregistrer migration si changement de zone
            if (memoire.zone !== ancienneZone) {
                migrations++;
                memoire.evolution.push({
                    timestamp: maintenant,
                    temperature: memoire.temperature,
                    zone: memoire.zone,
                    action: 'evolution_naturelle',
                    refroidissement: facteurRefroidissement
                });
                
                console.log(`❄️ Migration naturelle: ${id} zone ${ancienneZone} → ${memoire.zone} (${memoire.temperature.toFixed(1)}°C)`);
            }
        }
        
        if (migrations > 0) {
            console.log(`🔄 ${migrations} migrations thermiques effectuées`);
            this.sauvegarderMemoire();
        }
    }

    // MAINTENANCE INTELLIGENTE
    maintenance() {
        console.log(`🔧 Maintenance mémoire thermique intelligente...`);
        
        const maintenant = Date.now();
        let memoiresGelees = 0;
        let memoiresOubliees = 0;
        
        for (const [id, memoire] of this.memoires) {
            // Geler mémoires très froides et inutilisées
            if (memoire.temperature < 20 && memoire.utilisation < 3) {
                const ageJours = (maintenant - memoire.dernierAcces) / (1000 * 60 * 60 * 24);
                if (ageJours > 30) {
                    memoire.temperature = 15; // Hibernation
                    memoire.zone = 0;
                    memoiresGelees++;
                }
            }
            
            // Oublier mémoires obsolètes (très vieilles et jamais utilisées)
            if (memoire.utilisation === 1 && memoire.importance < 0.3) {
                const ageJours = (maintenant - memoire.timestamp) / (1000 * 60 * 60 * 24);
                if (ageJours > 90) {
                    this.memoires.delete(id);
                    memoiresOubliees++;
                }
            }
        }
        
        console.log(`❄️ ${memoiresGelees} mémoires gelées, 🗑️ ${memoiresOubliees} mémoires oubliées`);
        this.sauvegarderMemoire();
    }

    // CHARGER MÉMOIRE
    chargerMemoire() {
        try {
            if (fs.existsSync(this.fichierMemoire)) {
                const data = JSON.parse(fs.readFileSync(this.fichierMemoire, 'utf8'));
                this.memoires = new Map(data.memoires || []);
                this.curseurThermique = data.curseurThermique || 0.5;
                console.log(`📥 Mémoire intelligente chargée: ${this.memoires.size} entrées`);
            }
        } catch (error) {
            console.error(`❌ Erreur chargement mémoire intelligente:`, error.message);
            this.memoires = new Map();
        }
    }

    // SAUVEGARDER MÉMOIRE
    sauvegarderMemoire() {
        try {
            const data = {
                memoires: Array.from(this.memoires.entries()),
                curseurThermique: this.curseurThermique,
                temperatureCPU: this.temperatureCPU,
                timestamp: Date.now()
            };
            fs.writeFileSync(this.fichierMemoire, JSON.stringify(data, null, 2));
            return true;
        } catch (error) {
            console.error(`❌ Erreur sauvegarde mémoire intelligente:`, error.message);
            return false;
        }
    }

    // STATISTIQUES INTELLIGENTES
    getStatistiquesIntelligentes() {
        const stats = {
            totalEntries: this.memoires.size,
            temperatureCPU: this.temperatureCPU.toFixed(1),
            zonesDistribution: [0, 0, 0, 0, 0, 0],
            temperaturesParZone: {},
            memoiresPlusUtilisees: [],
            evolutionRecente: []
        };
        
        let totalTemp = 0;
        const memoiresArray = Array.from(this.memoires.values());
        
        // Distribution par zones et températures
        for (const memoire of memoiresArray) {
            stats.zonesDistribution[memoire.zone]++;
            totalTemp += memoire.temperature;
            
            if (!stats.temperaturesParZone[memoire.zone]) {
                stats.temperaturesParZone[memoire.zone] = [];
            }
            stats.temperaturesParZone[memoire.zone].push(memoire.temperature);
        }
        
        stats.averageTemperature = this.memoires.size > 0 ? totalTemp / this.memoires.size : 0;
        
        // Top 5 mémoires les plus utilisées
        stats.memoiresPlusUtilisees = memoiresArray
            .sort((a, b) => b.utilisation - a.utilisation)
            .slice(0, 5)
            .map(m => ({
                id: m.id.substr(-9),
                utilisation: m.utilisation,
                temperature: m.temperature.toFixed(1),
                zone: m.zone,
                source: m.source
            }));
        
        return stats;
    }
}

module.exports = { MemoireThermiqueIntelligente };
