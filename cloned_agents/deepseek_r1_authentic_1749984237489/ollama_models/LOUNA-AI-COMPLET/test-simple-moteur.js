/**
 * TEST SIMPLE DU MOTEUR DE RAISONNEMENT
 */

try {
    console.log('🧪 TEST SIMPLE MOTEUR DE RAISONNEMENT');
    console.log('====================================');
    
    // Import du moteur
    const MoteurSimple = require('./moteur-simple-fonctionnel.js');
    console.log('✅ Import réussi');

    // Création instance
    const moteur = new MoteurSimple();
    console.log('✅ Instance créée');
    
    // Test simple
    console.log('\n🔍 Test "bonjour":');
    const resultat = moteur.penser('bonjour');
    
    if (resultat && resultat.reponse) {
        console.log('✅ SUCCÈS !');
        console.log('📝 Réponse:', resultat.reponse);
        console.log('🎯 Source:', resultat.source);
    } else {
        console.log('❌ ÉCHEC - Pas de réponse');
        console.log('Résultat:', resultat);
    }
    
    // Test calcul
    console.log('\n🔍 Test "2 + 3":');
    const resultatCalcul = moteur.penser('2 + 3');
    
    if (resultatCalcul && resultatCalcul.reponse) {
        console.log('✅ SUCCÈS !');
        console.log('📝 Réponse:', resultatCalcul.reponse);
    } else {
        console.log('❌ ÉCHEC - Pas de réponse calcul');
    }
    
} catch (error) {
    console.log('❌ ERREUR:', error.message);
    console.log('Stack:', error.stack);
}
