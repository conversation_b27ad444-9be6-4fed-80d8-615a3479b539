/**
 * FORMATION CONTINUE POUR PROGRESSION QI
 * Entraînement intensif avec mémoire thermique
 */

const AgentOllamaThermique = require('./agent-ollama-thermique.js');
const fs = require('fs');
const path = require('path');

class FormationContinue {
    constructor() {
        console.log('🎓 FORMATION CONTINUE - PROGRESSION QI');
        console.log('======================================');
        
        this.agent = new AgentOllamaThermique();
        
        this.config = {
            sessions_par_cycle: 10,
            pause_entre_questions: 2000, // 2 secondes
            pause_entre_cycles: 5000, // 5 secondes
            objectif_qi: 150,
            formation_path: '/Volumes/LounaAI_V3/FORMATION-CONTINUE'
        };
        
        this.programmes_formation = {
            mathematiques: [
                'Combien font 25 + 37 ?',
                'Combien font 15 × 8 ?',
                'Quelle est la racine carrée de 64 ?',
                'Combien font 144 ÷ 12 ?',
                'Si x + 15 = 30, que vaut x ?',
                'Combien font 2³ (2 puissance 3) ?',
                'Quelle est 20% de 150 ?',
                'Combien font 7 × 9 ?',
                'Si un triangle a des angles de 60°, 60° et ?, quel est le troisième angle ?',
                'Combien font 100 - 37 ?'
            ],
            
            logique: [
                'Complétez la suite : 1, 4, 9, 16, ?',
                'Complétez la suite : 3, 6, 12, 24, ?',
                'Si tous les chats sont des animaux, et Félix est un chat, que peut-on dire de Félix ?',
                'Complétez : A, C, E, G, ?',
                'Si Pierre est plus grand que Paul, et Paul plus grand que Jacques, qui est le plus grand ?',
                'Complétez la suite : 2, 5, 8, 11, ?',
                'Quel nombre vient après : 1, 1, 2, 3, 5, 8, ?',
                'Si A=1, B=2, C=3, que vaut le mot "CAB" ?',
                'Complétez : Lundi, Mardi, Mercredi, ?',
                'Si demain nous sommes jeudi, quel jour étions-nous avant-hier ?'
            ],
            
            vocabulaire: [
                'Quel est le synonyme de "rapide" ?',
                'Quel est l\'antonyme de "monter" ?',
                'Complétez : "Livre" est à "lire" comme "stylo" est à ?',
                'Quel mot n\'appartient pas : pomme, orange, carotte, banane ?',
                'Que signifie "perspicace" ?',
                'Quel est le féminin de "acteur" ?',
                'Complétez : "Chaud" est à "froid" comme "grand" est à ?',
                'Quel est le pluriel de "cheval" ?',
                'Que veut dire "éphémère" ?',
                'Quel est le contraire de "optimiste" ?'
            ],
            
            culture_generale: [
                'Quelle est la capitale de l\'Italie ?',
                'Combien y a-t-il de continents ?',
                'Qui a peint la Joconde ?',
                'Quelle est la planète la plus proche du Soleil ?',
                'En quelle année l\'homme a-t-il marché sur la Lune ?',
                'Quel est l\'océan le plus grand ?',
                'Qui a écrit "Les Misérables" ?',
                'Combien y a-t-il de secondes dans une minute ?',
                'Quelle est la langue la plus parlée au monde ?',
                'Quel est le plus haut sommet du monde ?'
            ],
            
            raisonnement_spatial: [
                'Si vous regardez vers l\'Est et tournez de 180°, où regardez-vous ?',
                'Combien de faces a un tétraèdre ?',
                'Si vous êtes face au Nord et tournez de 270° vers la droite, où regardez-vous ?',
                'Combien d\'arêtes a un cube ?',
                'Quelle forme a 8 côtés ?',
                'Si vous tournez une clé de 90° vers la droite, puis 90° vers la gauche, où est-elle ?',
                'Combien de sommets a un hexagone ?',
                'Si vous regardez vers l\'Ouest et tournez de 45° vers la droite, vers où regardez-vous ?',
                'Quelle est la forme d\'un ballon de football ?',
                'Combien de diagonales a un carré ?'
            ]
        };
        
        this.stats_formation = {
            cycles_completes: 0,
            questions_posees: 0,
            qi_initial: 0,
            qi_actuel: 0,
            progression_totale: 0,
            domaines_travailles: [],
            debut_formation: Date.now()
        };
        
        this.initialiserFormation();
    }
    
    initialiserFormation() {
        console.log('🔧 Initialisation formation...');
        
        try {
            // Créer dossier formation
            if (!fs.existsSync(this.config.formation_path)) {
                fs.mkdirSync(this.config.formation_path, { recursive: true });
            }
            
            // Charger stats formation
            this.chargerStatsFormation();
            
            console.log('✅ Formation initialisée');
            
        } catch (error) {
            console.log(`❌ Erreur initialisation: ${error.message}`);
        }
    }
    
    chargerStatsFormation() {
        try {
            const statsPath = path.join(this.config.formation_path, 'stats_formation.json');
            
            if (fs.existsSync(statsPath)) {
                this.stats_formation = JSON.parse(fs.readFileSync(statsPath, 'utf8'));
                console.log(`📊 Formation reprise: Cycle ${this.stats_formation.cycles_completes}, QI ${this.stats_formation.qi_actuel}`);
            } else {
                // Récupérer QI initial de l'agent
                this.stats_formation.qi_initial = this.agent.stats_progression.qi_actuel;
                this.stats_formation.qi_actuel = this.agent.stats_progression.qi_actuel;
            }
            
        } catch (error) {
            console.log(`⚠️ Erreur stats formation: ${error.message}`);
        }
    }
    
    sauvegarderStatsFormation() {
        try {
            const statsPath = path.join(this.config.formation_path, 'stats_formation.json');
            fs.writeFileSync(statsPath, JSON.stringify(this.stats_formation, null, 2));
            
        } catch (error) {
            console.log(`⚠️ Erreur sauvegarde: ${error.message}`);
        }
    }
    
    // FORMATION INTENSIVE CONTINUE
    async lancerFormationIntensive() {
        console.log('\n🚀 LANCEMENT FORMATION INTENSIVE');
        console.log('=================================');
        
        console.log(`🎯 Objectif QI: ${this.config.objectif_qi}`);
        console.log(`📊 QI actuel: ${this.stats_formation.qi_actuel}`);
        console.log(`🎓 Progression nécessaire: +${this.config.objectif_qi - this.stats_formation.qi_actuel} points`);
        
        let cycle = 1;
        
        while (this.stats_formation.qi_actuel < this.config.objectif_qi) {
            console.log(`\n🔄 === CYCLE ${cycle} ===`);
            console.log(`🧠 QI début cycle: ${this.stats_formation.qi_actuel}`);
            
            // Sélectionner domaine de formation
            const domaine = this.selectionnerDomaine();
            console.log(`📚 Domaine: ${domaine}`);
            
            // Formation sur le domaine
            await this.formerSurDomaine(domaine);
            
            // Mettre à jour stats
            this.stats_formation.cycles_completes++;
            this.stats_formation.qi_actuel = this.agent.stats_progression.qi_actuel;
            this.stats_formation.progression_totale = this.stats_formation.qi_actuel - this.stats_formation.qi_initial;
            
            console.log(`📈 QI fin cycle: ${this.stats_formation.qi_actuel} (+${this.stats_formation.progression_totale} total)`);
            
            // Sauvegarder progression
            this.sauvegarderStatsFormation();
            
            // Afficher état thermique
            this.agent.afficherEtatThermique();
            
            // Pause entre cycles
            console.log(`⏳ Pause ${this.config.pause_entre_cycles / 1000}s...`);
            await new Promise(resolve => setTimeout(resolve, this.config.pause_entre_cycles));
            
            cycle++;
            
            // Sécurité : arrêt après 20 cycles
            if (cycle > 20) {
                console.log('🛑 Limite de cycles atteinte');
                break;
            }
        }
        
        this.afficherRapportFinal();
    }
    
    selectionnerDomaine() {
        // Rotation équilibrée des domaines
        const domaines = Object.keys(this.programmes_formation);
        const index = this.stats_formation.cycles_completes % domaines.length;
        return domaines[index];
    }
    
    async formerSurDomaine(domaine) {
        console.log(`\n📖 Formation ${domaine}...`);
        
        const questions = this.programmes_formation[domaine];
        const qi_debut = this.agent.stats_progression.qi_actuel;
        
        for (let i = 0; i < this.config.sessions_par_cycle && i < questions.length; i++) {
            const question = questions[i];
            
            console.log(`\n--- Question ${i + 1}/${this.config.sessions_par_cycle} ---`);
            console.log(`❓ ${question}`);
            
            try {
                const resultat = await this.agent.poserQuestionThermique(question);
                
                if (resultat.succes) {
                    console.log(`✅ Réponse obtenue (${resultat.duree}ms)`);
                    console.log(`🧠 QI: ${resultat.qi_actuel} (+${resultat.progression})`);
                    
                    this.stats_formation.questions_posees++;
                } else {
                    console.log(`❌ Erreur: ${resultat.reponse}`);
                }
                
            } catch (error) {
                console.log(`❌ Erreur formation: ${error.message}`);
            }
            
            // Pause entre questions
            await new Promise(resolve => setTimeout(resolve, this.config.pause_entre_questions));
        }
        
        const qi_fin = this.agent.stats_progression.qi_actuel;
        const progression_domaine = qi_fin - qi_debut;
        
        console.log(`📊 Progression ${domaine}: +${progression_domaine} points`);
        
        // Enregistrer domaine travaillé
        if (!this.stats_formation.domaines_travailles.includes(domaine)) {
            this.stats_formation.domaines_travailles.push(domaine);
        }
    }
    
    // FORMATION CIBLÉE SUR FAIBLESSES
    async formationCiblee() {
        console.log('\n🎯 FORMATION CIBLÉE SUR FAIBLESSES');
        console.log('==================================');
        
        // Identifier faiblesses via test rapide
        const faiblesses = await this.identifierFaiblesses();
        
        console.log(`🔍 Faiblesses identifiées: ${faiblesses.join(', ')}`);
        
        // Formation intensive sur faiblesses
        for (const domaine of faiblesses) {
            console.log(`\n🎯 Formation intensive: ${domaine}`);
            
            // Double session sur domaine faible
            await this.formerSurDomaine(domaine);
            await this.formerSurDomaine(domaine);
        }
    }
    
    async identifierFaiblesses() {
        // Test rapide pour identifier domaines faibles
        const resultats_domaines = {};
        
        for (const [domaine, questions] of Object.entries(this.programmes_formation)) {
            console.log(`🧪 Test ${domaine}...`);
            
            const question_test = questions[0];
            const resultat = await this.agent.poserQuestionThermique(question_test);
            
            // Évaluer qualité réponse (simplifié)
            const qualite = resultat.succes ? (resultat.reponse.length > 10 ? 1 : 0.5) : 0;
            resultats_domaines[domaine] = qualite;
            
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        // Retourner domaines avec score < 0.7
        return Object.entries(resultats_domaines)
            .filter(([domaine, score]) => score < 0.7)
            .map(([domaine, score]) => domaine);
    }
    
    // FORMATION MARATHON
    async formationMarathon(duree_minutes = 30) {
        console.log(`\n🏃 FORMATION MARATHON (${duree_minutes} minutes)`);
        console.log('===============================================');
        
        const debut = Date.now();
        const fin = debut + (duree_minutes * 60 * 1000);
        
        let questions_posees = 0;
        const qi_debut = this.agent.stats_progression.qi_actuel;
        
        while (Date.now() < fin) {
            // Sélectionner question aléatoire
            const domaines = Object.keys(this.programmes_formation);
            const domaine = domaines[Math.floor(Math.random() * domaines.length)];
            const questions = this.programmes_formation[domaine];
            const question = questions[Math.floor(Math.random() * questions.length)];
            
            console.log(`\n⚡ Question ${questions_posees + 1} (${domaine})`);
            console.log(`❓ ${question}`);
            
            try {
                const resultat = await this.agent.poserQuestionThermique(question);
                
                if (resultat.succes) {
                    console.log(`✅ QI: ${resultat.qi_actuel}`);
                    questions_posees++;
                }
                
            } catch (error) {
                console.log(`❌ Erreur: ${error.message}`);
            }
            
            // Pause courte
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        const qi_fin = this.agent.stats_progression.qi_actuel;
        const progression = qi_fin - qi_debut;
        
        console.log(`\n🏆 MARATHON TERMINÉ`);
        console.log(`📊 Questions posées: ${questions_posees}`);
        console.log(`📈 Progression: +${progression} points QI`);
        console.log(`⚡ Vitesse: ${(questions_posees / duree_minutes).toFixed(1)} questions/minute`);
    }
    
    afficherRapportFinal() {
        console.log('\n📋 RAPPORT FINAL FORMATION');
        console.log('==========================');
        
        const duree_totale = Date.now() - this.stats_formation.debut_formation;
        const duree_minutes = Math.round(duree_totale / 60000);
        
        console.log(`\n🎯 RÉSULTATS GLOBAUX:`);
        console.log(`├── QI initial: ${this.stats_formation.qi_initial}`);
        console.log(`├── QI final: ${this.stats_formation.qi_actuel}`);
        console.log(`├── Progression totale: +${this.stats_formation.progression_totale} points`);
        console.log(`├── Cycles complétés: ${this.stats_formation.cycles_completes}`);
        console.log(`├── Questions posées: ${this.stats_formation.questions_posees}`);
        console.log(`├── Durée formation: ${duree_minutes} minutes`);
        console.log(`└── Vitesse progression: ${(this.stats_formation.progression_totale / duree_minutes).toFixed(2)} points/minute`);
        
        console.log(`\n📚 DOMAINES TRAVAILLÉS:`);
        this.stats_formation.domaines_travailles.forEach(domaine => {
            console.log(`✅ ${domaine}`);
        });
        
        console.log(`\n🏆 CLASSIFICATION FINALE:`);
        if (this.stats_formation.qi_actuel >= 150) {
            console.log('🥇 OBJECTIF ATTEINT - GÉNIE !');
        } else if (this.stats_formation.qi_actuel >= 130) {
            console.log('🥈 TRÈS SUPÉRIEUR');
        } else if (this.stats_formation.qi_actuel >= 120) {
            console.log('🥉 SUPÉRIEUR');
        } else if (this.stats_formation.qi_actuel >= 110) {
            console.log('📊 AU-DESSUS MOYENNE');
        } else {
            console.log('📈 EN PROGRESSION');
        }
    }
}

// Export
module.exports = FormationContinue;

// Lancement automatique si exécuté directement
if (require.main === module) {
    console.log('🚀 LANCEMENT FORMATION CONTINUE');
    console.log('===============================');
    
    const formation = new FormationContinue();
    
    // Menu de formation
    setTimeout(async () => {
        console.log('\n🎓 TYPES DE FORMATION DISPONIBLES:');
        console.log('1. Formation intensive (cycles jusqu\'objectif)');
        console.log('2. Formation ciblée (faiblesses)');
        console.log('3. Formation marathon (30 minutes)');
        
        console.log('\n🚀 Lancement formation intensive...');
        await formation.lancerFormationIntensive();
        
    }, 3000);
}
