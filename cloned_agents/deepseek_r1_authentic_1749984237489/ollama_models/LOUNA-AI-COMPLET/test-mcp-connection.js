/**
 * Script de test pour vérifier la connexion MCP
 * Ce script teste l'accès à Internet et au bureau via le MCP
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Configuration
const MCP_PORT = 3002;
const MCP_URL = `http://localhost:${MCP_PORT}`;

// Couleurs pour les messages
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

// Fonction pour afficher un message coloré
function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Fonction pour tester le statut du MCP
async function testMcpStatus() {
  try {
    log('1. Test du statut du MCP...', 'blue');
    const response = await axios.get(`${MCP_URL}/mcp/status`);
    
    if (response.status === 200 && response.data.status === 'ok') {
      log('✅ Le serveur MCP est en ligne', 'green');
      log(`   - Version: ${response.data.version}`, 'cyan');
      log(`   - Accès Internet: ${response.data.capabilities.internet ? 'ACTIVÉ' : 'DÉSACTIVÉ'}`, 'cyan');
      log(`   - Accès Bureau: ${response.data.capabilities.desktop ? 'ACTIVÉ' : 'DÉSACTIVÉ'}`, 'cyan');
      log(`   - Commandes Système: ${response.data.capabilities.systemCommands ? 'ACTIVÉ' : 'DÉSACTIVÉ'}`, 'cyan');
      return true;
    } else {
      log('❌ Le serveur MCP est en ligne mais renvoie un statut incorrect', 'red');
      log(`   - Réponse: ${JSON.stringify(response.data)}`, 'yellow');
      return false;
    }
  } catch (error) {
    log('❌ Impossible de se connecter au serveur MCP', 'red');
    log(`   - Erreur: ${error.message}`, 'red');
    return false;
  }
}

// Fonction pour tester l'accès à Internet
async function testInternetAccess() {
  try {
    log('\n2. Test de l\'accès à Internet via MCP...', 'blue');
    const response = await axios.post(`${MCP_URL}/mcp/internet/fetch`, {
      url: 'https://www.google.com'
    });
    
    if (response.status === 200 && response.data.success) {
      log('✅ Accès à Internet fonctionnel', 'green');
      log(`   - Taille des données récupérées: ${response.data.data.length} caractères`, 'cyan');
      return true;
    } else {
      log('❌ Échec de l\'accès à Internet', 'red');
      log(`   - Réponse: ${JSON.stringify(response.data)}`, 'yellow');
      return false;
    }
  } catch (error) {
    log('❌ Erreur lors du test d\'accès à Internet', 'red');
    log(`   - Erreur: ${error.message}`, 'red');
    return false;
  }
}

// Fonction pour tester l'accès au bureau
async function testDesktopAccess() {
  try {
    log('\n3. Test de l\'accès au bureau via MCP...', 'blue');
    
    // D'abord, vérifier si le bureau est accessible
    const checkResponse = await axios.get(`${MCP_URL}/mcp/desktop/check`);
    
    if (checkResponse.status === 200 && checkResponse.data.success) {
      log(`✅ Vérification du bureau réussie`, 'green');
      log(`   - Chemin du bureau: ${checkResponse.data.desktopPath}`, 'cyan');
      log(`   - Existe: ${checkResponse.data.exists ? 'Oui' : 'Non'}`, 'cyan');
      log(`   - Accessible: ${checkResponse.data.accessible ? 'Oui' : 'Non'}`, 'cyan');
      
      // Ensuite, lister les fichiers du bureau
      const filesResponse = await axios.get(`${MCP_URL}/mcp/desktop/files`);
      
      if (filesResponse.status === 200 && filesResponse.data.success) {
        log('✅ Listage des fichiers du bureau réussi', 'green');
        log(`   - Nombre de fichiers: ${filesResponse.data.files.length}`, 'cyan');
        
        // Afficher les 5 premiers fichiers
        const filesToShow = filesResponse.data.files.slice(0, 5);
        if (filesToShow.length > 0) {
          log('   - Premiers fichiers:', 'cyan');
          filesToShow.forEach(file => {
            log(`     * ${file.name} (${file.isDirectory ? 'Dossier' : 'Fichier'})`, 'cyan');
          });
        }
        
        return true;
      } else {
        log('❌ Échec du listage des fichiers du bureau', 'red');
        log(`   - Réponse: ${JSON.stringify(filesResponse.data)}`, 'yellow');
        return false;
      }
    } else {
      log('❌ Échec de la vérification du bureau', 'red');
      log(`   - Réponse: ${JSON.stringify(checkResponse.data)}`, 'yellow');
      return false;
    }
  } catch (error) {
    log('❌ Erreur lors du test d\'accès au bureau', 'red');
    log(`   - Erreur: ${error.message}`, 'red');
    return false;
  }
}

// Fonction pour tester l'exécution de commandes système
async function testSystemCommands() {
  try {
    log('\n4. Test de l\'exécution de commandes système via MCP...', 'blue');
    const response = await axios.post(`${MCP_URL}/mcp/system/execute`, {
      command: 'echo "Test de commande système via MCP"',
      timeout: 5000
    });
    
    if (response.status === 200 && response.data.success) {
      log('✅ Exécution de commande système réussie', 'green');
      log(`   - Sortie: ${response.data.stdout.trim()}`, 'cyan');
      return true;
    } else {
      log('❌ Échec de l\'exécution de commande système', 'red');
      log(`   - Réponse: ${JSON.stringify(response.data)}`, 'yellow');
      return false;
    }
  } catch (error) {
    log('❌ Erreur lors du test d\'exécution de commande système', 'red');
    log(`   - Erreur: ${error.message}`, 'red');
    return false;
  }
}

// Fonction principale
async function main() {
  log('=== TEST DE LA CONNEXION MCP ===', 'yellow');
  log('Vérification de l\'accès à Internet et au bureau via MCP\n', 'yellow');
  
  // Tester le statut du MCP
  const mcpStatus = await testMcpStatus();
  
  if (mcpStatus) {
    // Tester l'accès à Internet
    const internetAccess = await testInternetAccess();
    
    // Tester l'accès au bureau
    const desktopAccess = await testDesktopAccess();
    
    // Tester l'exécution de commandes système
    const systemCommands = await testSystemCommands();
    
    // Afficher le résumé
    log('\n=== RÉSUMÉ DES TESTS ===', 'yellow');
    log(`Statut du MCP: ${mcpStatus ? '✅ OK' : '❌ ÉCHEC'}`, mcpStatus ? 'green' : 'red');
    log(`Accès Internet: ${internetAccess ? '✅ OK' : '❌ ÉCHEC'}`, internetAccess ? 'green' : 'red');
    log(`Accès Bureau: ${desktopAccess ? '✅ OK' : '❌ ÉCHEC'}`, desktopAccess ? 'green' : 'red');
    log(`Commandes Système: ${systemCommands ? '✅ OK' : '❌ ÉCHEC'}`, systemCommands ? 'green' : 'red');
    
    if (mcpStatus && internetAccess && desktopAccess && systemCommands) {
      log('\n✅ TOUS LES TESTS ONT RÉUSSI', 'green');
      log('Le MCP est correctement configuré pour accéder à Internet et au bureau', 'green');
    } else {
      log('\n❌ CERTAINS TESTS ONT ÉCHOUÉ', 'red');
      log('Veuillez vérifier la configuration du MCP', 'red');
    }
  } else {
    log('\n❌ IMPOSSIBLE DE TESTER LES FONCTIONNALITÉS', 'red');
    log('Le serveur MCP n\'est pas accessible', 'red');
  }
}

// Exécuter le script
main().catch(error => {
  log(`\n❌ ERREUR CRITIQUE: ${error.message}`, 'red');
  process.exit(1);
});
