/**
 * ZONES ÉTENDUES 100°C
 * Système de zones thermiques étendues 0°C à 100°C
 */

const fs = require('fs');
const path = require('path');

class ZonesEtendues100C {
    constructor() {
        console.log('🌡️ ZONES ÉTENDUES 100°C');
        console.log('========================');
        
        this.config = {
            memoire: '/Volumes/LounaAI_V3/MEMOIRE-REELLE',
            zones_etendues: '/Volumes/LounaAI_V3/MEMOIRE-REELLE/zones-thermiques-etendues'
        };
        
        this.zones_etendues = {
            'zone1_100C': { nom: 'Transcendance', description: 'Conscience supérieure', couleur: '#FF0000' },
            'zone2_90C': { nom: 'Illumination', description: 'Révélations profondes', couleur: '#FF3300' },
            'zone3_80C': { nom: 'Inspiration', description: 'Créativité maximale', couleur: '#FF6600' },
            'zone4_70C': { nom: 'Excellence', description: 'Performance optimale', couleur: '#FF9900' },
            'zone5_60C': { nom: 'Maîtrise', description: 'Compétence avancée', couleur: '#FFCC00' },
            'zone6_50C': { nom: 'Équilibre', description: 'Fonctionnement normal', couleur: '#FFFF00' },
            'zone7_40C': { nom: 'Apprentissage', description: 'Acquisition savoir', couleur: '#CCFF00' },
            'zone8_30C': { nom: 'Réflexion', description: 'Analyse profonde', couleur: '#99FF00' },
            'zone9_20C': { nom: 'Méditation', description: 'Contemplation', couleur: '#66FF00' },
            'zone10_10C': { nom: 'Repos', description: 'Récupération', couleur: '#33FF00' },
            'zone11_0C': { nom: 'Hibernation', description: 'Mode minimal', couleur: '#00FF00' }
        };
        
        this.curseur_etendu = {
            position: 50.0,
            zone_actuelle: 'zone6_50C',
            historique: [],
            mode_accelere: true,
            bonus_qi: 0
        };
        
        this.initialiserZonesEtendues();
    }
    
    initialiserZonesEtendues() {
        console.log('🔧 Initialisation zones étendues...');
        
        try {
            // Créer structure zones étendues
            this.creerStructureEtendue();
            
            // Migrer données existantes
            this.migrerDonneesExistantes();
            
            // Configurer curseur étendu
            this.configurerCurseurEtendu();
            
            // Créer mappings intelligents
            this.creerMappingsIntelligents();
            
            // Tester système étendu
            this.testerSystemeEtendu();
            
            console.log('✅ Zones étendues initialisées');
            
        } catch (error) {
            console.log(`❌ Erreur initialisation: ${error.message}`);
        }
    }
    
    creerStructureEtendue() {
        console.log('📁 Création structure étendue...');
        
        // Créer dossier principal
        if (!fs.existsSync(this.config.zones_etendues)) {
            fs.mkdirSync(this.config.zones_etendues, { recursive: true });
        }
        
        // Créer chaque zone étendue
        Object.keys(this.zones_etendues).forEach(zone => {
            const cheminZone = path.join(this.config.zones_etendues, zone);
            
            if (!fs.existsSync(cheminZone)) {
                fs.mkdirSync(cheminZone, { recursive: true });
                console.log(`📁 Zone créée: ${zone}`);
            }
            
            // Créer fichier de configuration zone
            const configZone = {
                nom: this.zones_etendues[zone].nom,
                description: this.zones_etendues[zone].description,
                temperature: parseInt(zone.split('_')[1].replace('C', '')),
                couleur: this.zones_etendues[zone].couleur,
                capacite_max: 1000,
                souvenirs_count: 0,
                derniere_maj: Date.now()
            };
            
            const cheminConfig = path.join(cheminZone, 'config.json');
            fs.writeFileSync(cheminConfig, JSON.stringify(configZone, null, 2));
        });
        
        console.log(`✅ ${Object.keys(this.zones_etendues).length} zones étendues créées`);
    }
    
    migrerDonneesExistantes() {
        console.log('🔄 Migration données existantes...');
        
        try {
            const cheminZonesOriginales = path.join(this.config.memoire, 'zones-thermiques');
            
            if (fs.existsSync(cheminZonesOriginales)) {
                const zonesOriginales = fs.readdirSync(cheminZonesOriginales);
                
                // Mapping zones originales vers étendues
                const mapping = {
                    'zone1': 'zone4_70C',  // 70°C → Excellence
                    'zone2': 'zone5_60C',  // 60°C → Maîtrise
                    'zone3': 'zone6_50C',  // 50°C → Équilibre
                    'zone4': 'zone7_40C',  // 40°C → Apprentissage
                    'zone5': 'zone8_30C',  // 30°C → Réflexion
                    'zone6': 'zone9_20C'   // 20°C → Méditation
                };
                
                let souvenirsMigres = 0;
                
                zonesOriginales.forEach(zoneOriginale => {
                    const cheminZoneOriginale = path.join(cheminZonesOriginales, zoneOriginale);
                    
                    if (fs.existsSync(cheminZoneOriginale) && mapping[zoneOriginale]) {
                        const fichiers = fs.readdirSync(cheminZoneOriginale).filter(f => f.endsWith('.json'));
                        const zoneDestination = mapping[zoneOriginale];
                        const cheminDestination = path.join(this.config.zones_etendues, zoneDestination);
                        
                        fichiers.forEach(fichier => {
                            try {
                                const cheminSource = path.join(cheminZoneOriginale, fichier);
                                const cheminDest = path.join(cheminDestination, fichier);
                                
                                // Copier fichier avec métadonnées mises à jour
                                const souvenir = JSON.parse(fs.readFileSync(cheminSource, 'utf8'));
                                souvenir.zone_etendue = zoneDestination;
                                souvenir.migration_date = Date.now();
                                
                                fs.writeFileSync(cheminDest, JSON.stringify(souvenir, null, 2));
                                souvenirsMigres++;
                                
                            } catch (error) {
                                console.log(`⚠️ Erreur migration ${fichier}: ${error.message}`);
                            }
                        });
                        
                        console.log(`📦 ${zoneOriginale} → ${zoneDestination}: ${fichiers.length} souvenirs`);
                    }
                });
                
                console.log(`✅ ${souvenirsMigres} souvenirs migrés vers zones étendues`);
                
            } else {
                console.log('ℹ️ Aucune zone originale à migrer');
            }
            
        } catch (error) {
            console.log(`❌ Erreur migration: ${error.message}`);
        }
    }
    
    configurerCurseurEtendu() {
        console.log('🌡️ Configuration curseur étendu...');
        
        try {
            // Charger position actuelle si existe
            const cheminCurseurOriginal = path.join(this.config.memoire, 'curseur-thermique', 'position.json');
            
            if (fs.existsSync(cheminCurseurOriginal)) {
                const curseurOriginal = JSON.parse(fs.readFileSync(cheminCurseurOriginal, 'utf8'));
                this.curseur_etendu.position = curseurOriginal.position || 50.0;
            }
            
            // Déterminer zone étendue selon position
            this.curseur_etendu.zone_actuelle = this.determinerZoneEtendue(this.curseur_etendu.position);
            
            // Calculer bonus QI étendu
            this.curseur_etendu.bonus_qi = this.calculerBonusQIEtendu(this.curseur_etendu.position);
            
            // Créer dossier curseur étendu
            const cheminCurseurEtendu = path.join(this.config.memoire, 'curseur-thermique-etendu');
            if (!fs.existsSync(cheminCurseurEtendu)) {
                fs.mkdirSync(cheminCurseurEtendu, { recursive: true });
            }
            
            // Sauvegarder configuration étendue
            const configCurseur = {
                position: this.curseur_etendu.position,
                zone_actuelle: this.curseur_etendu.zone_actuelle,
                temperature_min: 0,
                temperature_max: 100,
                mode_accelere: true,
                bonus_qi: this.curseur_etendu.bonus_qi,
                zones_disponibles: Object.keys(this.zones_etendues),
                derniere_maj: Date.now()
            };
            
            const cheminConfig = path.join(cheminCurseurEtendu, 'position.json');
            fs.writeFileSync(cheminConfig, JSON.stringify(configCurseur, null, 2));
            
            console.log(`🌡️ Curseur étendu: ${this.curseur_etendu.position.toFixed(1)}°C (${this.curseur_etendu.zone_actuelle})`);
            console.log(`🧠 Bonus QI étendu: +${this.curseur_etendu.bonus_qi} points`);
            
        } catch (error) {
            console.log(`❌ Erreur curseur étendu: ${error.message}`);
        }
    }
    
    determinerZoneEtendue(temperature) {
        if (temperature >= 95) return 'zone1_100C';
        else if (temperature >= 85) return 'zone2_90C';
        else if (temperature >= 75) return 'zone3_80C';
        else if (temperature >= 65) return 'zone4_70C';
        else if (temperature >= 55) return 'zone5_60C';
        else if (temperature >= 45) return 'zone6_50C';
        else if (temperature >= 35) return 'zone7_40C';
        else if (temperature >= 25) return 'zone8_30C';
        else if (temperature >= 15) return 'zone9_20C';
        else if (temperature >= 5) return 'zone10_10C';
        else return 'zone11_0C';
    }
    
    calculerBonusQIEtendu(temperature) {
        // Nouveau calcul bonus QI avec zones étendues
        let bonus = 0;
        
        if (temperature >= 95) bonus = 35;      // Zone Transcendance
        else if (temperature >= 85) bonus = 30; // Zone Illumination
        else if (temperature >= 75) bonus = 25; // Zone Inspiration
        else if (temperature >= 65) bonus = 20; // Zone Excellence
        else if (temperature >= 55) bonus = 15; // Zone Maîtrise
        else if (temperature >= 45) bonus = 10; // Zone Équilibre
        else if (temperature >= 35) bonus = 5;  // Zone Apprentissage
        else if (temperature >= 25) bonus = 2;  // Zone Réflexion
        else if (temperature >= 15) bonus = 1;  // Zone Méditation
        else bonus = 0;                         // Zones inférieures
        
        return bonus;
    }
    
    creerMappingsIntelligents() {
        console.log('🔗 Création mappings intelligents...');
        
        const mappings = {
            performance_zones: {
                'transcendance': { zone: 'zone1_100C', qi_min: 180, description: 'Conscience supérieure' },
                'illumination': { zone: 'zone2_90C', qi_min: 170, description: 'Révélations profondes' },
                'inspiration': { zone: 'zone3_80C', qi_min: 160, description: 'Créativité maximale' },
                'excellence': { zone: 'zone4_70C', qi_min: 150, description: 'Performance optimale' },
                'maitrise': { zone: 'zone5_60C', qi_min: 140, description: 'Compétence avancée' },
                'equilibre': { zone: 'zone6_50C', qi_min: 120, description: 'Fonctionnement normal' }
            },
            
            triggers_evolution: {
                'formation_complete': { bonus_temp: 15, description: 'Formation maçonnique terminée' },
                'test_parfait': { bonus_temp: 10, description: 'Test QI parfait' },
                'innovation': { bonus_temp: 8, description: 'Réponse innovante' },
                'connexion_profonde': { bonus_temp: 5, description: 'Connexion conceptuelle' }
            },
            
            seuils_critiques: {
                'zone_transcendance': 95,
                'zone_illumination': 85,
                'zone_inspiration': 75,
                'seuil_genie': 65,
                'seuil_superieur': 55
            }
        };
        
        const cheminMappings = path.join(this.config.zones_etendues, 'mappings_intelligents.json');
        fs.writeFileSync(cheminMappings, JSON.stringify(mappings, null, 2));
        
        console.log('✅ Mappings intelligents créés');
    }
    
    testerSystemeEtendu() {
        console.log('\n🧪 TEST SYSTÈME ÉTENDU');
        console.log('======================');
        
        // Test progression curseur
        const positions_test = [30, 50, 70, 85, 95];
        
        positions_test.forEach(temp => {
            const zone = this.determinerZoneEtendue(temp);
            const bonus = this.calculerBonusQIEtendu(temp);
            const qi_estime = 85 + bonus + 25; // Base + bonus curseur + bonus mémoire
            
            console.log(`🌡️ ${temp}°C → ${zone} → QI ${qi_estime} (+${bonus} curseur)`);
        });
        
        // Test capacités maximales
        console.log('\n🎯 CAPACITÉS MAXIMALES:');
        console.log(`├── QI Base: 85`);
        console.log(`├── Bonus Mémoire: +30`);
        console.log(`├── Bonus Curseur Max: +35`);
        console.log(`├── Bonus Formation: +30`);
        console.log(`├── Bonus Évolution: +25`);
        console.log(`└── 🏆 QI MAXIMUM: 205`);
        
        // Créer souvenir test dans zone haute
        this.creerSouvenirTest();
    }
    
    creerSouvenirTest() {
        try {
            const souvenirTest = {
                id: `test_zone_etendue_${Date.now()}`,
                type: 'test_systeme_etendu',
                contenu: 'Test du système de zones étendues 0°C-100°C avec curseur adaptatif',
                zone_etendue: 'zone1_100C',
                temperature: 100,
                bonus_qi: 35,
                timestamp: Date.now(),
                date: new Date().toISOString(),
                metadata: {
                    systeme: 'zones_etendues_100c',
                    version: '1.0',
                    test: true
                }
            };
            
            const cheminTest = path.join(this.config.zones_etendues, 'zone1_100C', `${souvenirTest.id}.json`);
            fs.writeFileSync(cheminTest, JSON.stringify(souvenirTest, null, 2));
            
            console.log('✅ Souvenir test créé en zone Transcendance (100°C)');
            
        } catch (error) {
            console.log(`⚠️ Erreur création souvenir test: ${error.message}`);
        }
    }
    
    afficherStatistiquesEtendues() {
        console.log('\n📊 STATISTIQUES ZONES ÉTENDUES');
        console.log('==============================');
        
        let totalSouvenirs = 0;
        
        Object.keys(this.zones_etendues).forEach(zone => {
            const cheminZone = path.join(this.config.zones_etendues, zone);
            
            if (fs.existsSync(cheminZone)) {
                const fichiers = fs.readdirSync(cheminZone).filter(f => f.endsWith('.json') && f !== 'config.json');
                totalSouvenirs += fichiers.length;
                
                const temp = parseInt(zone.split('_')[1].replace('C', ''));
                const nom = this.zones_etendues[zone].nom;
                
                console.log(`🌡️ ${temp}°C ${nom}: ${fichiers.length} souvenirs`);
            }
        });
        
        console.log(`\n📊 Total: ${totalSouvenirs} souvenirs dans ${Object.keys(this.zones_etendues).length} zones`);
        console.log(`🎯 Curseur actuel: ${this.curseur_etendu.position.toFixed(1)}°C`);
        console.log(`🧠 Bonus QI actuel: +${this.curseur_etendu.bonus_qi} points`);
        console.log(`🚀 QI estimé: ${85 + this.curseur_etendu.bonus_qi + 30} points`);
    }
}

// Export
module.exports = ZonesEtendues100C;

// Test automatique si exécuté directement
if (require.main === module) {
    console.log('🚀 LANCEMENT ZONES ÉTENDUES 100°C');
    console.log('==================================');
    
    const zones = new ZonesEtendues100C();
    
    setTimeout(() => {
        zones.afficherStatistiquesEtendues();
    }, 2000);
}
