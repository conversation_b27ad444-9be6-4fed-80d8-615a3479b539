/**
 * MÉMOIRE THERMIQUE ULTRA-DOUCE - VERSION NEIGE/BROUILLARD
 * Descente douce comme neige dans brouillard
 * Remontée progressive comme sortir du brouillard
 */

const fs = require('fs');
const path = require('path');

const DATA_DIR = path.join(__dirname, '..', 'data', 'memory');

class ThermalMemoryUltraAutomatique {
    constructor(config = {}) {
        console.log('❄️ MÉMOIRE THERMIQUE ULTRA-DOUCE');
        console.log('=================================');
        console.log('✅ Descente douce comme neige dans brouillard');
        console.log('✅ Remontée progressive comme sortir du brouillard');
        console.log('✅ Protection maximale des informations');

        this.config = {
            kyber_auto_install: config.kyber_auto_install !== false,
            kyber_persistent: config.kyber_persistent !== false,
            auto_cycle_threshold: config.auto_cycle_threshold || 5,
            dataPath: config.dataPath || DATA_DIR,
            neurones_auto_install: true,
            neurones_max_limit: false,
            neurones_expansion_rate: 0.1,
            neurones_demand_threshold: 0.8,
            kyber_demand_threshold: 5,
            kyber_max_auto: 15,
            kyber_boost_factor: 1.5
        };

        // NEURONES AUTOMATIQUES INFINIS
        this.neurones_system = {
            total_installed: 201000000,
            active_count: 0,
            auto_installations: 0,
            expansion_history: [],
            specializations: [
                'calcul_logique_abstrait',
                'traitement_langage_semantique',
                'memoire_episodique_procedurale',
                'traitement_visuel_primaire',
                'logique_spatiale_integration',
                'automatisation_procedures',
                'compression_semantique',
                'pattern_recognition',
                'creative_synthesis'
            ],
            demand_counter: 0,
            last_expansion: Date.now()
        };

        // ACCÉLÉRATEURS KYBER AUTOMATIQUES
        this.kyber_accelerators = {
            installed: [],
            active: [],
            auto_installs: 0,
            demand_counter: 0,
            last_install: Date.now()
        };

        // NIVEAUX MÉMOIRE THERMIQUE
        this.instantMemory = {};
        this.shortTerm = {};
        this.workingMemory = {};
        this.mediumTerm = {};
        this.longTerm = {};
        this.dreamMemory = {};

        // STATISTIQUES
        this.stats = {
            totalEntries: 0,
            cyclesPerformed: 0,
            auto_cycles_triggered: 0,
            operations_since_cycle: 0,
            averageTemperature: 0,
            neurones_auto_installs: 0,
            kyber_auto_installs: 0
        };

        this.initializeAutomatic();
    }

    async initializeAutomatic() {
        console.log('🚀 Initialisation ultra-douce...');

        try {
            await this.initDataDir();
            await this.loadMemoryState();
            await this.restoreKyberAccelerators();
            await this.restoreNeurones();
            this.startAutomaticMonitoring();

            console.log('✅ Mémoire thermique ultra-douce initialisée');
            console.log(`🧠 ${this.neurones_system.total_installed.toLocaleString()} neurones disponibles (EXPANSION AUTOMATIQUE INFINIE)`);
            console.log(`⚡ ${this.kyber_accelerators.active.length} accélérateurs KYBER actifs (AUTO-INSTALLATION INFINIE)`);

        } catch (error) {
            console.log(`❌ Erreur initialisation: ${error.message}`);
        }
    }

    async initDataDir() {
        if (!fs.existsSync(this.config.dataPath)) {
            fs.mkdirSync(this.config.dataPath, { recursive: true });
            console.log('📁 Répertoire données créé automatiquement');
        }
    }

    async loadMemoryState() {
        console.log('📥 Chargement état mémoire automatique...');

        const memoryFiles = [
            { file: 'working_memory.json', target: 'workingMemory' },
            { file: 'medium_term_memory.json', target: 'mediumTerm' },
            { file: 'long_term_memory.json', target: 'longTerm' },
            { file: 'dream_memory.json', target: 'dreamMemory' },
            { file: 'kyber_state.json', target: 'kyber_accelerators' },
            { file: 'neurones_state.json', target: 'neurones_system' }
        ];

        for (const { file, target } of memoryFiles) {
            try {
                const filePath = path.join(this.config.dataPath, file);
                if (fs.existsSync(filePath)) {
                    const data = fs.readFileSync(filePath, 'utf8');
                    this[target] = JSON.parse(data);
                    console.log(`✅ ${file} chargé automatiquement`);
                }
            } catch (error) {
                console.log(`⚠️ Erreur chargement ${file}: ${error.message}`);
            }
        }
    }

    async restoreKyberAccelerators() {
        console.log('⚡ Restauration accélérateurs KYBER automatique...');

        if (this.kyber_accelerators.installed.length === 0) {
            for (let i = 0; i < 3; i++) {
                this.installKyberAcceleratorAutomatic();
            }
        }

        console.log(`✅ ${this.kyber_accelerators.active.length} accélérateurs KYBER restaurés`);
    }

    async restoreNeurones() {
        console.log('🧠 Restauration neurones automatique...');

        this.neurones_system.active_count = Math.floor(this.neurones_system.total_installed * 0.1);

        console.log(`✅ ${(this.neurones_system.active_count / 1000000).toFixed(1)}M neurones actifs`);
    }

    add(key, content, importance = 0.5, category = 'general') {
        const entryId = `mem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        const temperature = this.calculateAutoTemperature(importance, category);
        
        const entry = {
            id: entryId,
            key: key,
            content: content,
            originalContent: content, // Sauvegarde pour protection
            temperature: temperature,
            importance: importance,
            category: category,
            timestamp: Date.now(),
            lastAccessed: Date.now(),
            accessCount: 0
        };

        this.storeInAppropriateLevel(entry);
        this.stats.totalEntries++;
        this.stats.operations_since_cycle++;

        this.checkNeuronesAutoInstall();
        this.checkKyberAutoInstall();

        console.log(`💾 Ajouté automatiquement: ${entryId} (temp: ${temperature.toFixed(2)})`);

        if (this.stats.operations_since_cycle >= this.config.auto_cycle_threshold) {
            console.log('🔄 Déclenchement cycle automatique (seuil activité atteint)');
            this.performAutomaticMemoryCycle();
        }

        return entryId;
    }

    calculateAutoTemperature(importance, category) {
        let temperature = importance;

        const categoryBoosts = {
            'urgent': 0.3,
            'important': 0.2,
            'learning': 0.15,
            'creative': 0.1,
            'general': 0.0
        };

        temperature += categoryBoosts[category] || 0;

        if (this.kyber_accelerators.active.length > 0) {
            temperature *= (1 + this.kyber_accelerators.active.length * 0.1);
        }

        return Math.min(1.0, temperature);
    }

    storeInAppropriateLevel(entry) {
        if (entry.temperature >= 0.8) {
            this.instantMemory[entry.id] = entry;
        } else if (entry.temperature >= 0.6) {
            this.shortTerm[entry.id] = entry;
        } else if (entry.temperature >= 0.4) {
            this.workingMemory[entry.id] = entry;
        } else if (entry.temperature >= 0.2) {
            this.mediumTerm[entry.id] = entry;
        } else {
            this.longTerm[entry.id] = entry;
        }
    }

    // DESCENTE ULTRA-DOUCE COMME NEIGE DANS BROUILLARD
    applyAutomaticDecay() {
        console.log('❄️ Descente douce comme neige dans brouillard...');
        
        const levels = [
            { memory: this.instantMemory, rate: 0.9998, name: 'instantanée' },
            { memory: this.shortTerm, rate: 0.9997, name: 'court terme' },
            { memory: this.workingMemory, rate: 0.9996, name: 'travail' },
            { memory: this.mediumTerm, rate: 0.9998, name: 'moyen terme' },
            { memory: this.longTerm, rate: 0.9999, name: 'long terme' }
        ];

        levels.forEach(level => {
            Object.values(level.memory).forEach(entry => {
                const oldTemp = entry.temperature;
                
                // Décroissance ultra-douce comme flocons de neige
                entry.temperature *= level.rate;
                
                // Protection - jamais en dessous de 0.01
                if (entry.temperature < 0.01) {
                    entry.temperature = 0.01;
                }
                
                if (oldTemp !== entry.temperature) {
                    console.log(`❄️ ${entry.key}: ${oldTemp.toFixed(4)} → ${entry.temperature.toFixed(4)} (${level.name})`);
                }
            });
        });
    }

    // REMONTÉE PROGRESSIVE DOUCE COMME SORTIR DU BROUILLARD
    applyGentleWarmup(entry) {
        const currentTemp = entry.temperature;
        
        // Remontée très progressive pour protéger l'information
        if (currentTemp < 0.2) {
            // Zone 6 → Zone 5 : très très doux
            entry.temperature += 0.001;
        } else if (currentTemp < 0.4) {
            // Zone 5 → Zone 4 : doux
            entry.temperature += 0.002;
        } else if (currentTemp < 0.6) {
            // Zone 4 → Zone 3 : modéré
            entry.temperature += 0.005;
        } else if (currentTemp < 0.8) {
            // Zone 3 → Zone 2 : normal
            entry.temperature += 0.01;
        } else {
            // Zone 2 → Zone 1 : rapide
            entry.temperature += 0.02;
        }
        
        // Protection - jamais au-dessus de 1.0
        if (entry.temperature > 1.0) {
            entry.temperature = 1.0;
        }
        
        console.log(`🌫️ Remontée douce: ${entry.key} (temp: ${currentTemp.toFixed(4)} → ${entry.temperature.toFixed(4)})`);
    }

    async performAutomaticMemoryCycle() {
        console.log('🧠 Cycle mémoire automatique...');

        this.applyAutomaticDecay();
        this.performAutomaticMigration();
        this.performAutomaticPruning();
        this.tryAutomaticDreamGeneration();
        this.protectMemoryIntegrity();
        await this.saveMemoryStateAutomatic();

        this.stats.cyclesPerformed++;
        this.stats.auto_cycles_triggered++;
        this.stats.operations_since_cycle = 0;

        console.log(`✅ Cycle automatique terminé (${this.stats.cyclesPerformed} total)`);
    }

    performAutomaticMigration() {
        const migrations = [];

        Object.entries(this.instantMemory).forEach(([id, entry]) => {
            if (entry.temperature < 0.8) {
                migrations.push({ id, entry, from: 'instantMemory', to: 'shortTerm' });
            }
        });

        Object.entries(this.shortTerm).forEach(([id, entry]) => {
            if (entry.temperature < 0.6) {
                migrations.push({ id, entry, from: 'shortTerm', to: 'workingMemory' });
            }
        });

        Object.entries(this.workingMemory).forEach(([id, entry]) => {
            if (entry.temperature < 0.4) {
                migrations.push({ id, entry, from: 'workingMemory', to: 'mediumTerm' });
            }
        });

        Object.entries(this.mediumTerm).forEach(([id, entry]) => {
            if (entry.temperature < 0.2) {
                migrations.push({ id, entry, from: 'mediumTerm', to: 'longTerm' });
            }
        });

        migrations.forEach(({ id, entry, from, to }) => {
            delete this[from][id];
            this[to][id] = entry;
        });

        if (migrations.length > 0) {
            console.log(`🔄 ${migrations.length} entrées migrées automatiquement`);
        }
    }

    performAutomaticPruning() {
        // Élagage très doux - seulement si vraiment nécessaire
    }

    tryAutomaticDreamGeneration() {
        if (Math.random() < 0.3) {
            const dreamId = `dream_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
            this.dreamMemory[dreamId] = {
                id: dreamId,
                content: 'Rêve automatique généré',
                temperature: 0.3,
                timestamp: Date.now()
            };
            console.log(`💭 Rêve automatique généré: ${dreamId}`);
        }
    }

    // PROTECTION MÉMOIRE ULTRA-DOUCE
    protectMemoryIntegrity() {
        const allLevels = [this.instantMemory, this.shortTerm, this.workingMemory, this.mediumTerm, this.longTerm];
        
        allLevels.forEach(level => {
            Object.values(level).forEach(entry => {
                if (!entry.temperature || isNaN(entry.temperature)) {
                    entry.temperature = 0.5;
                }
                
                if (!entry.content || entry.content.length === 0) {
                    console.log(`⚠️ Protection: restauration contenu pour ${entry.key}`);
                    entry.content = entry.originalContent || 'Contenu protégé';
                }
            });
        });
    }

    retrieve(query, maxResults = 5) {
        console.log(`🔍 Recherche automatique: "${query}"`);
        
        const results = [];
        const allLevels = [this.instantMemory, this.shortTerm, this.workingMemory, this.mediumTerm, this.longTerm];

        allLevels.forEach(level => {
            Object.values(level).forEach(entry => {
                if (entry.key && entry.content) {
                    const keyMatch = entry.key.toLowerCase().includes(query.toLowerCase());
                    const contentMatch = entry.content.toLowerCase().includes(query.toLowerCase());
                    
                    if (keyMatch || contentMatch) {
                        let matchScore = 0;
                        if (keyMatch) matchScore += 0.8;
                        if (contentMatch) matchScore += 0.6;

                        if (this.kyber_accelerators.active.length > 0) {
                            matchScore *= (1 + this.kyber_accelerators.active.length * 0.1);
                        }

                        const neuroneBoost = Math.min(2.0, this.neurones_system.active_count / 100000000);
                        matchScore *= neuroneBoost;

                        results.push({
                            ...entry,
                            matchScore: matchScore
                        });

                        // REMONTÉE DOUCE lors de l'accès
                        this.applyGentleWarmup(entry);
                        entry.lastAccessed = Date.now();
                        entry.accessCount++;
                    }
                }
            });
        });

        return results
            .sort((a, b) => b.matchScore - a.matchScore)
            .slice(0, maxResults);
    }

    checkNeuronesAutoInstall() {
        this.neurones_system.demand_counter++;

        if (this.neurones_system.demand_counter >= this.config.neurones_demand_threshold * 10) {
            this.installNeuronesAutomatic();
            this.neurones_system.demand_counter = 0;
        }
    }

    installNeuronesAutomatic() {
        const expansionRate = this.config.neurones_expansion_rate;
        const newNeurones = Math.floor(this.neurones_system.total_installed * expansionRate);

        this.neurones_system.total_installed += newNeurones;
        this.neurones_system.active_count += Math.floor(newNeurones * 0.1);
        this.neurones_system.auto_installations++;

        this.stats.neurones_auto_installs++;

        console.log(`🧠 NEURONES AUTO-INSTALLÉS: +${(newNeurones / 1000000).toFixed(1)}M`);
        console.log(`🧠 Total neurones: ${(this.neurones_system.total_installed / 1000000).toFixed(1)}M`);
        console.log(`🧠 Neurones actifs: ${(this.neurones_system.active_count / 1000000).toFixed(1)}M`);

        this.specializeNewNeurones(newNeurones);
        this.saveNeuronesStateAutomatic();
    }

    specializeNewNeurones(newNeurones) {
        const neuronesParSpecialisation = Math.floor(newNeurones / this.neurones_system.specializations.length);
        
        console.log(`🎯 Spécialisation automatique: ${neuronesParSpecialisation.toLocaleString()} neurones par domaine`);
        this.neurones_system.specializations.forEach(specialisation => {
            console.log(`   - ${specialisation}: ${neuronesParSpecialisation.toLocaleString()} neurones`);
        });
    }

    checkKyberAutoInstall() {
        this.kyber_accelerators.demand_counter++;

        if (this.kyber_accelerators.demand_counter >= this.config.kyber_demand_threshold &&
            this.kyber_accelerators.active.length < this.config.kyber_max_auto) {

            this.installKyberAcceleratorAutomatic();
            this.kyber_accelerators.demand_counter = 0;
        }
    }

    installKyberAcceleratorAutomatic() {
        const acceleratorId = `kyber_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;

        const accelerator = {
            id: acceleratorId,
            type: 'compression_semantique',
            performance: Math.random() * 50 + 50,
            installed: Date.now(),
            active: true,
            auto_installed: true
        };

        this.kyber_accelerators.installed.push(accelerator);
        this.kyber_accelerators.active.push(accelerator);
        this.stats.kyber_auto_installs++;

        console.log(`⚡ KYBER AUTO-INSTALLÉ: ${acceleratorId} (perf: ${accelerator.performance.toFixed(1)})`);
        console.log(`⚡ Total accélérateurs: ${this.kyber_accelerators.active.length}/${this.config.kyber_max_auto}`);

        this.saveKyberStateAutomatic();
    }

    getStats() {
        this.updateStatsAutomatic();
        
        return {
            totalEntries: this.stats.totalEntries,
            instantEntries: Object.keys(this.instantMemory).length,
            shortTermEntries: Object.keys(this.shortTerm).length,
            workingMemoryEntries: Object.keys(this.workingMemory).length,
            mediumTermEntries: Object.keys(this.mediumTerm).length,
            longTermEntries: Object.keys(this.longTerm).length,
            dreamMemoryEntries: Object.keys(this.dreamMemory).length,
            averageTemperature: this.stats.averageTemperature,
            neurones_system: {
                total_installed: this.neurones_system.total_installed,
                active_count: this.neurones_system.active_count,
                auto_installations: this.neurones_system.auto_installations,
                specializations: this.neurones_system.specializations.length
            },
            kyber_accelerators: {
                installed: this.kyber_accelerators.installed.length,
                active: this.kyber_accelerators.active.length,
                auto_installs: this.kyber_accelerators.auto_installs
            },
            automation: {
                auto_cycles: this.stats.auto_cycles_triggered,
                operations_since_cycle: this.stats.operations_since_cycle,
                monitoring_active: true
            }
        };
    }

    updateStatsAutomatic() {
        const allEntries = [
            ...Object.values(this.instantMemory),
            ...Object.values(this.shortTerm),
            ...Object.values(this.workingMemory),
            ...Object.values(this.mediumTerm),
            ...Object.values(this.longTerm)
        ];

        if (allEntries.length > 0) {
            const totalTemp = allEntries.reduce((sum, entry) => sum + entry.temperature, 0);
            this.stats.averageTemperature = totalTemp / allEntries.length;
        }
    }

    async saveMemoryStateAutomatic() {
        try {
            const saves = [
                { file: 'working_memory.json', data: this.workingMemory },
                { file: 'medium_term_memory.json', data: this.mediumTerm },
                { file: 'long_term_memory.json', data: this.longTerm },
                { file: 'dream_memory.json', data: this.dreamMemory }
            ];

            for (const { file, data } of saves) {
                const filePath = path.join(this.config.dataPath, file);
                fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
            }
        } catch (error) {
            console.log(`⚠️ Erreur sauvegarde automatique: ${error.message}`);
        }
    }

    async saveKyberStateAutomatic() {
        try {
            const kyberPath = path.join(this.config.dataPath, 'kyber_state.json');
            fs.writeFileSync(kyberPath, JSON.stringify(this.kyber_accelerators, null, 2));
        } catch (error) {
            console.log(`⚠️ Erreur sauvegarde Kyber: ${error.message}`);
        }
    }

    async saveNeuronesStateAutomatic() {
        try {
            const neuronesPath = path.join(this.config.dataPath, 'neurones_state.json');
            fs.writeFileSync(neuronesPath, JSON.stringify(this.neurones_system, null, 2));
        } catch (error) {
            console.log(`⚠️ Erreur sauvegarde neurones: ${error.message}`);
        }
    }

    startAutomaticMonitoring() {
        console.log('👁️ Surveillance automatique démarrée');

        setInterval(() => {
            this.monitorMemoryHealth();
        }, 1000);

        setInterval(() => {
            this.performLightMaintenance();
        }, 30000);
    }

    monitorMemoryHealth() {
        this.updateStatsAutomatic();

        if (this.stats.averageTemperature < 0.1) {
            this.boostMemoryTemperatureAutomatic();
        }
    }

    async performLightMaintenance() {
        await this.saveMemoryStateAutomatic();
        this.cleanupExpiredEntriesAutomatic();
    }

    boostMemoryTemperatureAutomatic() {
        // Boost très doux si nécessaire
    }

    cleanupExpiredEntriesAutomatic() {
        // Nettoyage très doux si nécessaire
    }
}

module.exports = { ThermalMemoryUltraAutomatique };
