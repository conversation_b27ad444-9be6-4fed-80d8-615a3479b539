#!/usr/bin/env node

/**
 * 🧪 TEST DES RÉPONSES DÉTAILLÉES LOUNA-AI
 * ========================================
 * Script pour tester les nouvelles fonctionnalités de détail
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testerReponseDetaillee(question) {
    try {
        console.log(`\n🔍 TEST: "${question}"`);
        console.log('='.repeat(50));
        
        const response = await axios.post(`${BASE_URL}/api/chat`, {
            message: question
        }, {
            timeout: 10000
        });
        
        if (response.data && response.data.reponse) {
            console.log('📝 RÉPONSE:');
            console.log(response.data.reponse);
            
            // Vérifier si la réponse est enrichie
            if (response.data.reponse.includes('Détails supplémentaires') || 
                response.data.reponse.includes('Réponse enrichie')) {
                console.log('✅ RÉPONSE ENRICHIE DÉTECTÉE');
            } else {
                console.log('⚠️ Réponse standard (pas d\'enrichissement détecté)');
            }
        } else {
            console.log('❌ Pas de réponse reçue');
        }
        
    } catch (error) {
        console.error(`❌ Erreur: ${error.message}`);
    }
}

async function executerTests() {
    console.log('🧪 TESTS DES RÉPONSES DÉTAILLÉES LOUNA-AI');
    console.log('==========================================');
    
    // Vérifier que le serveur est accessible
    try {
        const statusResponse = await axios.get(`${BASE_URL}/api/status`);
        console.log('✅ Serveur LOUNA-AI accessible');
        console.log(`📊 QI: ${statusResponse.data.qi_actuel}`);
        console.log(`💾 Mémoires: ${statusResponse.data.memoires}`);
    } catch (error) {
        console.error('❌ Serveur LOUNA-AI non accessible');
        console.error('🔧 Assurez-vous que le serveur est démarré avec: node serveur-interface-complete.js');
        process.exit(1);
    }
    
    // Tests avec demandes de détails explicites
    const testsDetails = [
        "Quelle est la formule du dioxyde de carbone ? Peux-tu détailler ?",
        "Explique-moi en détail la suite de Fibonacci",
        "Quelle est la capitale de la Guyane ? Donne-moi plus d'informations",
        "Dans la suite 1,1,2,3,5,8,13, quel est le nombre suivant ? Développe ta réponse",
        "Parle-moi de LOUNA-AI et précise tes capacités"
    ];
    
    // Tests sans demande de détails (pour comparaison)
    const testsStandard = [
        "Quelle est la formule du dioxyde de carbone ?",
        "Dans la suite 1,1,2,3,5,8,13, quel est le nombre suivant ?",
        "Quelle est la capitale de la Guyane ?"
    ];
    
    console.log('\n🔍 TESTS AVEC DEMANDES DE DÉTAILS');
    console.log('==================================');
    
    for (const test of testsDetails) {
        await testerReponseDetaillee(test);
        await new Promise(resolve => setTimeout(resolve, 2000)); // Pause entre tests
    }
    
    console.log('\n📝 TESTS STANDARD (POUR COMPARAISON)');
    console.log('====================================');
    
    for (const test of testsStandard) {
        await testerReponseDetaillee(test);
        await new Promise(resolve => setTimeout(resolve, 2000)); // Pause entre tests
    }
    
    console.log('\n🎉 TESTS TERMINÉS');
    console.log('=================');
    console.log('✅ Vérifiez que les réponses avec demandes de détails sont plus complètes');
    console.log('📖 Consultez la page de présentation: http://localhost:3000/presentation-louna-ai.html');
}

// Exécuter les tests
if (require.main === module) {
    executerTests().catch(console.error);
}

module.exports = { testerReponseDetaillee, executerTests };
