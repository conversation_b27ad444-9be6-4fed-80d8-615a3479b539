/**
 * Correctif pour la mémoire thermique
 * Ce script ajoute les méthodes manquantes à l'objet thermalMemory
 * pour résoudre les erreurs comme "this.thermalMemory.getEntriesFromZone is not a function"
 */

// Fonction pour appliquer le correctif à l'objet thermalMemory
function applyThermalMemoryFix(thermalMemory) {
  if (!thermalMemory) {
    console.error('Impossible d\'appliquer le correctif: objet thermalMemory non défini');
    return false;
  }

  console.log('Application du correctif pour la mémoire thermique...');

  // Ajouter la méthode getEntriesFromZone si elle n'existe pas
  if (!thermalMemory.getEntriesFromZone) {
    thermalMemory.getEntriesFromZone = function(zoneNumber, limit = 10) {
      try {
        // Vérifier si getConversationsByZone existe
        if (typeof this.getConversationsByZone === 'function') {
          // Utiliser getConversationsByZone et limiter les résultats
          const conversations = this.getConversationsByZone(zoneNumber);
          return conversations.slice(0, limit);
        } 
        // Vérifier si memory.memories existe (structure alternative)
        else if (this.memory && Array.isArray(this.memory.memories)) {
          // Filtrer les conversations par zone
          const zoneConversations = this.memory.memories.filter(conv => conv.zone === zoneNumber);
          return zoneConversations.slice(0, limit);
        }
        // Fallback: retourner un tableau vide
        return [];
      } catch (error) {
        console.error(`Erreur dans getEntriesFromZone pour la zone ${zoneNumber}:`, error);
        return [];
      }
    };
    console.log('Méthode getEntriesFromZone ajoutée');
  }

  // Ajouter la méthode storeInZone si elle n'existe pas
  if (!thermalMemory.storeInZone) {
    thermalMemory.storeInZone = function(zoneNumber, entry) {
      try {
        // Vérifier si addConversation existe
        if (typeof this.addConversation === 'function') {
          // Adapter le format pour addConversation
          const conversationEntry = {
            id: entry.id || `thought_${Date.now()}`,
            title: entry.title || `Pensée - Zone ${zoneNumber}`,
            messages: [{
              role: 'system',
              content: typeof entry === 'string' ? entry : (entry.content || JSON.stringify(entry)),
              timestamp: new Date().toISOString()
            }],
            zone: zoneNumber,
            temperature: this.getZoneTemperature ? this.getZoneTemperature(zoneNumber) : 100 - ((zoneNumber - 1) * 20),
            created: new Date().toISOString()
          };
          
          this.addConversation(conversationEntry);
          return true;
        }
        // Vérifier si memory.memories existe (structure alternative)
        else if (this.memory && Array.isArray(this.memory.memories)) {
          // Créer une nouvelle entrée
          const newEntry = {
            id: entry.id || `thought_${Date.now()}`,
            title: entry.title || `Pensée - Zone ${zoneNumber}`,
            messages: [{
              role: 'system',
              content: typeof entry === 'string' ? entry : (entry.content || JSON.stringify(entry)),
              timestamp: new Date().toISOString()
            }],
            zone: zoneNumber,
            temperature: 100 - ((zoneNumber - 1) * 20),
            created: new Date().toISOString()
          };
          
          this.memory.memories.push(newEntry);
          return true;
        }
        
        return false;
      } catch (error) {
        console.error(`Erreur dans storeInZone pour la zone ${zoneNumber}:`, error);
        return false;
      }
    };
    console.log('Méthode storeInZone ajoutée');
  }

  // Ajouter la méthode getZoneTemperature si elle n'existe pas
  if (!thermalMemory.getZoneTemperature) {
    thermalMemory.getZoneTemperature = function(zoneNumber) {
      try {
        // Vérifier que la configuration et les zones de température sont correctement initialisées
        if (!this.config || !this.config.temperatureZones || !Array.isArray(this.config.temperatureZones)) {
          console.warn('Configuration des zones de température non initialisée, utilisation des valeurs par défaut');
          // Retourner des valeurs par défaut basées sur le numéro de zone
          return 100 - ((zoneNumber - 1) * 20);
        }

        // Vérifier que l'index est valide
        if (zoneNumber >= 1 && zoneNumber <= 6 && this.config.temperatureZones.length >= zoneNumber) {
          const zone = this.config.temperatureZones[zoneNumber - 1];
          if (zone && typeof zone.temp === 'number') {
            return zone.temp;
          }
        }
        
        // Valeur par défaut
        return 100 - ((zoneNumber - 1) * 20);
      } catch (error) {
        console.error(`Erreur dans getZoneTemperature pour la zone ${zoneNumber}:`, error);
        return 100 - ((zoneNumber - 1) * 20);
      }
    };
    console.log('Méthode getZoneTemperature ajoutée');
  }

  // Ajouter la méthode getAcceleratorsStats si elle n'existe pas
  if (!thermalMemory.getAcceleratorsStats) {
    thermalMemory.getAcceleratorsStats = function() {
      try {
        // Vérifier si kyberAccelerators existe
        if (this.kyberAccelerators && typeof this.kyberAccelerators.getStats === 'function') {
          return this.kyberAccelerators.getStats();
        }
        
        // Retourner des statistiques par défaut
        return {
          totalAccelerators: 0,
          activeAccelerators: 0,
          types: {},
          efficiency: 100,
          temperature: 50,
          load: 0
        };
      } catch (error) {
        console.error('Erreur dans getAcceleratorsStats:', error);
        return {
          totalAccelerators: 0,
          activeAccelerators: 0,
          types: {},
          efficiency: 100,
          temperature: 50,
          load: 0
        };
      }
    };
    console.log('Méthode getAcceleratorsStats ajoutée');
  }

  console.log('Correctif pour la mémoire thermique appliqué avec succès');
  return true;
}

module.exports = applyThermalMemoryFix;
