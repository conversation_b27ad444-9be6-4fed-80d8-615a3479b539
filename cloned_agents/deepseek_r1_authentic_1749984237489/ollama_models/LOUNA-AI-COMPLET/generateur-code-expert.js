/**
 * GÉNÉRATEUR DE CODE EXPERT POUR LOUNA-AI
 * Génération de code professionnel avec intégration VS Code et tests
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

class GenerateurCodeExpert {
    constructor() {
        this.projetsCreés = new Map();
        this.dossierProjets = path.join(__dirname, 'projets-code');
        
        // Créer le dossier de projets
        if (!fs.existsSync(this.dossierProjets)) {
            fs.mkdirSync(this.dossierProjets, { recursive: true });
        }
        
        this.templatesAvancés = new Map();
        this.initialiserTemplatesAvancés();
    }

    // CRÉER UN PROJET COMPLET AVEC TESTS
    async creerProjetComplet(specifications) {
        console.log(`🚀 Création projet expert: ${specifications.nom}`);
        
        try {
            const nomProjet = this.normaliserNomProjet(specifications.nom);
            const dossierProjet = path.join(this.dossierProjets, nomProjet);
            
            // Créer la structure du projet
            await this.creerStructureProjet(dossierProjet, specifications);
            
            // Générer le code principal
            const codeGenere = await this.genererCodeProfessionnel(specifications);
            
            // Créer tous les fichiers
            await this.creerFichiersProjet(dossierProjet, codeGenere, specifications);
            
            // Configurer VS Code
            await this.configurerVSCode(dossierProjet, specifications);
            
            // Initialiser Git
            await this.initialiserGit(dossierProjet);
            
            // Installer les dépendances
            await this.installerDependances(dossierProjet, specifications);
            
            // Exécuter les tests
            const resultatsTests = await this.executerTests(dossierProjet);
            
            const projet = {
                nom: nomProjet,
                chemin: dossierProjet,
                specifications: specifications,
                fichiers: codeGenere.fichiers,
                tests_passes: resultatsTests.success,
                date_creation: new Date().toISOString()
            };
            
            this.projetsCreés.set(nomProjet, projet);
            
            return {
                success: true,
                projet: projet,
                tests: resultatsTests,
                message: `Projet "${nomProjet}" créé avec succès et testé !`
            };
            
        } catch (error) {
            console.error(`❌ Erreur création projet: ${error.message}`);
            return {
                success: false,
                error: error.message,
                message: `Erreur lors de la création: ${error.message}`
            };
        }
    }

    // CRÉER LA STRUCTURE DU PROJET
    async creerStructureProjet(dossierProjet, specs) {
        const structure = [
            'src',
            'tests',
            'docs',
            'assets',
            '.vscode'
        ];
        
        // Créer le dossier principal
        if (!fs.existsSync(dossierProjet)) {
            fs.mkdirSync(dossierProjet, { recursive: true });
        }
        
        // Créer les sous-dossiers
        for (const dossier of structure) {
            const cheminDossier = path.join(dossierProjet, dossier);
            if (!fs.existsSync(cheminDossier)) {
                fs.mkdirSync(cheminDossier, { recursive: true });
            }
        }
        
        console.log(`📁 Structure projet créée: ${structure.length} dossiers`);
    }

    // GÉNÉRER DU CODE PROFESSIONNEL
    async genererCodeProfessionnel(specs) {
        const template = this.selectionnerTemplateAvance(specs);
        
        const code = {
            fichiers: [],
            principal: this.genererCodePrincipalAvance(template, specs),
            tests: this.genererTestsComplets(template, specs),
            config: this.genererConfigurationComplete(specs),
            documentation: this.genererDocumentationComplete(specs)
        };
        
        return code;
    }

    selectionnerTemplateAvance(specs) {
        const type = specs.type || this.determinerTypeProjet(specs);
        
        if (this.templatesAvancés.has(type)) {
            return this.templatesAvancés.get(type);
        }
        
        return this.templatesAvancés.get('web_app');
    }

    determinerTypeProjet(specs) {
        const desc = specs.description.toLowerCase();
        
        if (desc.includes('jeu') || desc.includes('game')) return 'jeu';
        if (desc.includes('web') || desc.includes('site')) return 'web_app';
        if (desc.includes('api') || desc.includes('serveur')) return 'api';
        if (desc.includes('mobile') || desc.includes('app')) return 'mobile';
        if (desc.includes('desktop')) return 'desktop';
        
        return 'web_app';
    }

    genererCodePrincipalAvance(template, specs) {
        let code = template.header.replace(/{{NOM_PROJET}}/g, specs.nom);
        code = code.replace(/{{DESCRIPTION}}/g, specs.description);
        code = code.replace(/{{AUTEUR}}/g, 'LOUNA-AI');
        code = code.replace(/{{DATE}}/g, new Date().toISOString());
        
        // Ajouter les imports
        code += template.imports;
        
        // Ajouter la classe principale
        code += template.classe_principale.replace(/{{NOM_CLASSE}}/g, this.convertirEnNomClasse(specs.nom));
        
        // Ajouter les méthodes spécifiques
        code += template.methodes;
        
        // Ajouter l'initialisation
        code += template.initialisation;
        
        return code;
    }

    genererTestsComplets(template, specs) {
        const nomClasse = this.convertirEnNomClasse(specs.nom);
        
        return `/**
 * Tests complets pour ${specs.nom}
 * Générés automatiquement par LOUNA-AI
 */

const { expect } = require('chai');
const ${nomClasse} = require('../src/${specs.nom}');

describe('${nomClasse}', () => {
    let instance;
    
    beforeEach(() => {
        instance = new ${nomClasse}();
    });
    
    describe('Initialisation', () => {
        it('devrait créer une instance valide', () => {
            expect(instance).to.be.instanceOf(${nomClasse});
            expect(instance.version).to.exist;
        });
        
        it('devrait avoir les propriétés requises', () => {
            expect(instance.description).to.equal('${specs.description}');
        });
    });
    
    describe('Fonctionnalités principales', () => {
        ${template.tests_specifiques}
    });
    
    describe('Gestion des erreurs', () => {
        it('devrait gérer les entrées invalides', () => {
            expect(() => instance.traiterDonnees(null)).to.not.throw();
        });
        
        it('devrait retourner des erreurs appropriées', () => {
            const resultat = instance.traiterDonnees('invalid');
            expect(resultat).to.have.property('error');
        });
    });
    
    describe('Performance', () => {
        it('devrait traiter les données rapidement', () => {
            const start = Date.now();
            instance.traiterDonnees(['test']);
            const duration = Date.now() - start;
            expect(duration).to.be.below(100); // moins de 100ms
        });
    });
});
`;
    }

    genererDocumentationComplete(specs) {
        return {
            contenu: `Documentation pour ${specs.nom}`,
            chemin: `${specs.nom}_README.md`
        };
    }
        genererConfigurationComplete(specs) {
        return {
            'package.json': JSON.stringify({
                name: specs.nom.replace(/\s+/g, '-').toLowerCase(),
                version: '1.0.0',
                description: specs.description,
                main: `src/${specs.nom}.js`,
                scripts: {
                    start: `node src/${specs.nom}.js`,
                    test: 'mocha tests/**/*.test.js',
                    'test:watch': 'mocha tests/**/*.test.js --watch',
                    'test:coverage': 'nyc mocha tests/**/*.test.js',
                    dev: 'nodemon src/index.js',
                    build: 'webpack --mode production',
                    lint: 'eslint src/**/*.js',
                    'lint:fix': 'eslint src/**/*.js --fix'
                },
                dependencies: this.genererDependances(specs),
                devDependencies: {
                    'mocha': '^10.0.0',
                    'chai': '^4.3.0',
                    'nyc': '^15.1.0',
                    'nodemon': '^3.0.0',
                    'eslint': '^8.0.0',
                    'webpack': '^5.0.0',
                    'webpack-cli': '^5.0.0'
                },
                keywords: ['louna-ai', 'generated', specs.type || 'application'],
                author: 'LOUNA-AI',
                license: 'MIT'
            }, null, 2),
            
            '.eslintrc.json': JSON.stringify({
                env: {
                    browser: true,
                    es2021: true,
                    node: true,
                    mocha: true
                },
                extends: ['eslint:recommended'],
                parserOptions: {
                    ecmaVersion: 'latest',
                    sourceType: 'module'
                },
                rules: {
                    'no-unused-vars': 'warn',
                    'no-console': 'off',
                    'indent': ['error', 4],
                    'quotes': ['error', 'single'],
                    'semi': ['error', 'always']
                }
            }, null, 2),
            
            '.gitignore': `node_modules/
dist/
build/
.env
.DS_Store
*.log
coverage/
.nyc_output/
`,
            
            'README.md': this.genererReadmeComplet(specs)
        };
    }

    genererDependances(specs) {
        const deps = {};
        
        if (specs.type === 'web_app') {
            deps.express = '^4.18.0';
            deps.cors = '^2.8.5';
        }
        
        if (specs.type === 'jeu') {
            deps.canvas = '^2.11.0';
        }
        
        if (specs.features && specs.features.includes('database')) {
            deps.mongoose = '^7.0.0';
        }
        
        return deps;
    }

    genererReadmeComplet(specs) {
        return `# ${specs.nom}

## Description
${specs.description}

## Installation
\`\`\`bash
npm install
\`\`\`

## Utilisation
\`\`\`bash
npm start
\`\`\`

## Tests
\`\`\`bash
# Exécuter tous les tests
npm test

# Tests en mode watch
npm run test:watch

# Coverage des tests
npm run test:coverage
\`\`\`

## Développement
\`\`\`bash
# Mode développement avec auto-reload
npm run dev

# Linter
npm run lint
npm run lint:fix
\`\`\`

## Structure du projet
\`\`\`
${specs.nom}/
├── src/           # Code source
├── tests/         # Tests unitaires
├── docs/          # Documentation
├── assets/        # Ressources
└── .vscode/       # Configuration VS Code
\`\`\`

## Fonctionnalités
- ✅ Code professionnel généré automatiquement
- ✅ Tests unitaires complets
- ✅ Configuration ESLint
- ✅ Scripts npm optimisés
- ✅ Documentation complète

## Créé par
LOUNA-AI - Assistant de développement intelligent

## License
MIT
`;
    }

    // CONFIGURER VS CODE
    async configurerVSCode(dossierProjet, specs) {
        const configVSCode = {
            'settings.json': JSON.stringify({
                'editor.tabSize': 4,
                'editor.insertSpaces': true,
                'editor.formatOnSave': true,
                'eslint.autoFixOnSave': true,
                'files.autoSave': 'onFocusChange',
                'terminal.integrated.defaultProfile.osx': 'bash',
                'emmet.includeLanguages': {
                    'javascript': 'javascriptreact'
                }
            }, null, 2),
            
            'launch.json': JSON.stringify({
                version: '0.2.0',
                configurations: [
                    {
                        name: 'Lancer application',
                        type: 'node',
                        request: 'launch',
                        program: `\${workspaceFolder}/src/${specs.nom}.js`,
                        console: 'integratedTerminal',
                        skipFiles: ['<node_internals>/**']
                    },
                    {
                        name: 'Déboguer tests',
                        type: 'node',
                        request: 'launch',
                        program: '${workspaceFolder}/node_modules/mocha/bin/_mocha',
                        args: ['tests/**/*.test.js'],
                        console: 'integratedTerminal',
                        skipFiles: ['<node_internals>/**']
                    }
                ]
            }, null, 2),
            
            'tasks.json': JSON.stringify({
                version: '2.0.0',
                tasks: [
                    {
                        label: 'npm: start',
                        type: 'npm',
                        script: 'start',
                        group: {
                            kind: 'build',
                            isDefault: true
                        }
                    },
                    {
                        label: 'npm: test',
                        type: 'npm',
                        script: 'test',
                        group: 'test'
                    }
                ]
            }, null, 2)
        };
        
        const dossierVSCode = path.join(dossierProjet, '.vscode');
        
        for (const [fichier, contenu] of Object.entries(configVSCode)) {
            const cheminFichier = path.join(dossierVSCode, fichier);
            fs.writeFileSync(cheminFichier, contenu);
        }
        
        console.log('⚙️ Configuration VS Code créée');
    }

    // CRÉER TOUS LES FICHIERS
    async creerFichiersProjet(dossierProjet, code, specs) {
        const fichiers = [];
        
        // Fichier principal
        const cheminPrincipal = path.join(dossierProjet, 'src', `${specs.nom}.js`);
        fs.writeFileSync(cheminPrincipal, code.principal);
        fichiers.push({ nom: `${specs.nom}.js`, chemin: cheminPrincipal, type: 'principal' });
        
        // Fichier de tests
        const cheminTests = path.join(dossierProjet, 'tests', `${specs.nom}.test.js`);
        fs.writeFileSync(cheminTests, code.tests);
        fichiers.push({ nom: `${specs.nom}.test.js`, chemin: cheminTests, type: 'tests' });
        
        // Fichiers de configuration
        for (const [nom, contenu] of Object.entries(code.config)) {
            const cheminConfig = path.join(dossierProjet, nom);
            fs.writeFileSync(cheminConfig, contenu);
            fichiers.push({ nom: nom, chemin: cheminConfig, type: 'configuration' });
        }
        
        code.fichiers = fichiers;
        console.log(`📁 ${fichiers.length} fichiers créés`);
        
        return fichiers;
    }

    // INITIALISER GIT
    async initialiserGit(dossierProjet) {
        try {
            await this.executerCommande('git init', dossierProjet);
            await this.executerCommande('git add .', dossierProjet);
            await this.executerCommande('git commit -m "Initial commit by LOUNA-AI"', dossierProjet);
            console.log('📦 Git initialisé');
        } catch (error) {
            console.log('⚠️ Git non disponible');
        }
    }

    // INSTALLER LES DÉPENDANCES
    async installerDependances(dossierProjet, specs) {
        try {
            console.log('📦 Installation des dépendances...');
            await this.executerCommande('npm install', dossierProjet);
            console.log('✅ Dépendances installées');
        } catch (error) {
            console.log('⚠️ Erreur installation dépendances:', error.message);
        }
    }

    // EXÉCUTER LES TESTS
    async executerTests(dossierProjet) {
        try {
            console.log('🧪 Exécution des tests...');
            const resultat = await this.executerCommande('npm test', dossierProjet);
            
            return {
                success: true,
                output: resultat.sortie,
                message: 'Tests exécutés avec succès'
            };
        } catch (error) {
            return {
                success: false,
                error: error.message,
                message: 'Erreur lors des tests'
            };
        }
    }

    // OUVRIR DANS VS CODE
    async ouvrirDansVSCode(cheminProjet) {
        try {
            await this.executerCommande(`code "${cheminProjet}"`, process.cwd());
            return {
                success: true,
                message: 'Projet ouvert dans VS Code'
            };
        } catch (error) {
            return {
                success: false,
                error: error.message,
                message: 'Impossible d\'ouvrir VS Code'
            };
        }
    }

    // UTILITAIRES
    normaliserNomProjet(nom) {
        return nom.toLowerCase()
            .replace(/\s+/g, '-')
            .replace(/[^a-z0-9-]/g, '')
            .substring(0, 50);
    }

    convertirEnNomClasse(nom) {
        return nom.split(/[-_\s]+/)
            .map(mot => mot.charAt(0).toUpperCase() + mot.slice(1))
            .join('');
    }

    async executerCommande(commande, cwd = process.cwd()) {
        return new Promise((resolve, reject) => {
            exec(commande, { cwd, timeout: 30000 }, (error, stdout, stderr) => {
                if (error) {
                    reject(error);
                } else {
                    resolve({
                        success: true,
                        sortie: stdout,
                        erreur: stderr
                    });
                }
            });
        });
    }

    // INITIALISER LES TEMPLATES AVANCÉS
    initialiserTemplatesAvancés() {
        // Template pour jeu (Morpion)
        this.templatesAvancés.set('jeu', {
            header: `/**
 * {{NOM_PROJET}}
 * {{DESCRIPTION}}
 * Créé par {{AUTEUR}} le {{DATE}}
 */
`,
            imports: `
// Imports pour le jeu
const readline = require('readline');
const EventEmitter = require('events');

`,
            classe_principale: `class {{NOM_CLASSE}} extends EventEmitter {
    constructor() {
        super();
        this.version = '1.0.0';
        this.description = '{{DESCRIPTION}}';
        this.plateau = this.initialiserPlateau();
        this.joueurActuel = 'X';
        this.partieTerminee = false;
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        console.log('🎮 Jeu initialisé !');
    }
`,
            methodes: `
    initialiserPlateau() {
        return [
            [' ', ' ', ' '],
            [' ', ' ', ' '],
            [' ', ' ', ' ']
        ];
    }
    
    afficherPlateau() {
        console.clear();
        console.log('\\n🎯 Morpion - LOUNA-AI\\n');
        console.log('   0   1   2');
        for (let i = 0; i < 3; i++) {
            console.log(\`\${i}  \${this.plateau[i][0]} | \${this.plateau[i][1]} | \${this.plateau[i][2]}\`);
            if (i < 2) console.log('  ---|---|---');
        }
        console.log(\`\\nJoueur actuel: \${this.joueurActuel}\`);
    }
    
    jouerCoup(ligne, colonne) {
        if (this.partieTerminee) {
            return { success: false, message: 'Partie terminée' };
        }
        
        if (ligne < 0 || ligne > 2 || colonne < 0 || colonne > 2) {
            return { success: false, message: 'Position invalide' };
        }
        
        if (this.plateau[ligne][colonne] !== ' ') {
            return { success: false, message: 'Case déjà occupée' };
        }
        
        this.plateau[ligne][colonne] = this.joueurActuel;
        
        const gagnant = this.verifierGagnant();
        if (gagnant) {
            this.partieTerminee = true;
            this.emit('partie-terminee', { gagnant, type: 'victoire' });
            return { success: true, message: \`Joueur \${gagnant} gagne !\`, gagnant };
        }
        
        if (this.plateauPlein()) {
            this.partieTerminee = true;
            this.emit('partie-terminee', { gagnant: null, type: 'egalite' });
            return { success: true, message: 'Match nul !', gagnant: null };
        }
        
        this.changerJoueur();
        this.emit('coup-joue', { ligne, colonne, joueur: this.joueurActuel });
        
        return { success: true, message: 'Coup joué' };
    }
    
    verifierGagnant() {
        // Vérifier lignes
        for (let i = 0; i < 3; i++) {
            if (this.plateau[i][0] !== ' ' && 
                this.plateau[i][0] === this.plateau[i][1] && 
                this.plateau[i][1] === this.plateau[i][2]) {
                return this.plateau[i][0];
            }
        }
        
        // Vérifier colonnes
        for (let j = 0; j < 3; j++) {
            if (this.plateau[0][j] !== ' ' && 
                this.plateau[0][j] === this.plateau[1][j] && 
                this.plateau[1][j] === this.plateau[2][j]) {
                return this.plateau[0][j];
            }
        }
        
        // Vérifier diagonales
        if (this.plateau[0][0] !== ' ' && 
            this.plateau[0][0] === this.plateau[1][1] && 
            this.plateau[1][1] === this.plateau[2][2]) {
            return this.plateau[0][0];
        }
        
        if (this.plateau[0][2] !== ' ' && 
            this.plateau[0][2] === this.plateau[1][1] && 
            this.plateau[1][1] === this.plateau[2][0]) {
            return this.plateau[0][2];
        }
        
        return null;
    }
    
    plateauPlein() {
        for (let i = 0; i < 3; i++) {
            for (let j = 0; j < 3; j++) {
                if (this.plateau[i][j] === ' ') {
                    return false;
                }
            }
        }
        return true;
    }
    
    changerJoueur() {
        this.joueurActuel = this.joueurActuel === 'X' ? 'O' : 'X';
    }
    
    async jouer() {
        console.log('🎮 Démarrage du jeu !');
        
        while (!this.partieTerminee) {
            this.afficherPlateau();
            
            try {
                const coup = await this.demanderCoup();
                const resultat = this.jouerCoup(coup.ligne, coup.colonne);
                
                if (!resultat.success) {
                    console.log('❌', resultat.message);
                    continue;
                }
                
                if (resultat.gagnant !== undefined) {
                    this.afficherPlateau();
                    console.log('🎉', resultat.message);
                    break;
                }
                
            } catch (error) {
                console.log('❌ Erreur:', error.message);
            }
        }
        
        this.rl.close();
    }
    
    demanderCoup() {
        return new Promise((resolve, reject) => {
            this.rl.question(\`Joueur \${this.joueurActuel}, entrez votre coup (ligne,colonne): \`, (reponse) => {
                try {
                    const [ligne, colonne] = reponse.split(',').map(x => parseInt(x.trim()));
                    if (isNaN(ligne) || isNaN(colonne)) {
                        reject(new Error('Format invalide. Utilisez: ligne,colonne (ex: 1,2)'));
                    } else {
                        resolve({ ligne, colonne });
                    }
                } catch (error) {
                    reject(new Error('Format invalide'));
                }
            });
        });
    }
    
    reinitialiser() {
        this.plateau = this.initialiserPlateau();
        this.joueurActuel = 'X';
        this.partieTerminee = false;
        this.emit('jeu-reinitialise');
    }
`,
            initialisation: `
}

// Export de la classe
module.exports = {{NOM_CLASSE}};

// Démarrage automatique si exécuté directement
if (require.main === module) {
    const jeu = new {{NOM_CLASSE}}();
    jeu.jouer().catch(console.error);
}
`,
            tests_specifiques: `
        it('devrait initialiser un plateau vide', () => {
            expect(instance.plateau).to.deep.equal([
                [' ', ' ', ' '],
                [' ', ' ', ' '],
                [' ', ' ', ' ']
            ]);
        });
        
        it('devrait permettre de jouer un coup valide', () => {
            const resultat = instance.jouerCoup(0, 0);
            expect(resultat.success).to.be.true;
            expect(instance.plateau[0][0]).to.equal('X');
        });
        
        it('devrait détecter une victoire', () => {
            instance.plateau = [
                ['X', 'X', 'X'],
                [' ', ' ', ' '],
                [' ', ' ', ' ']
            ];
            const gagnant = instance.verifierGagnant();
            expect(gagnant).to.equal('X');
        });
        
        it('devrait détecter un plateau plein', () => {
            instance.plateau = [
                ['X', 'O', 'X'],
                ['O', 'X', 'O'],
                ['O', 'X', 'O']
            ];
            expect(instance.plateauPlein()).to.be.true;
        });
`
        });

        console.log(`📋 ${this.templatesAvancés.size} templates avancés initialisés`);
    }

    // STATISTIQUES
    obtenirStatistiques() {
        return {
            projets_crees: this.projetsCreés.size,
            templates_avances: this.templatesAvancés.size,
            dossier_projets: this.dossierProjets
        };
    }
}

module.exports = GenerateurCodeExpert;
