<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LOUNA-AI - Interface Corrigée</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #2a1a2a 100%);
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        /* HEADER AVEC STATISTIQUES */
        .header {
            background: linear-gradient(90deg, #1a1a1a 0%, #2a1a2a 50%, #1a1a1a 100%);
            padding: 15px 20px;
            border-bottom: 2px solid #ff69b4;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(255, 105, 180, 0.3);
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            background: linear-gradient(45deg, #ff69b4, #ff1493);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 20px rgba(255, 105, 180, 0.5);
        }

        .stats {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .stat-item {
            text-align: center;
            padding: 8px 12px;
            background: rgba(255, 105, 180, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(255, 105, 180, 0.3);
        }

        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #ff69b4;
        }

        .stat-label {
            font-size: 12px;
            color: #cccccc;
        }

        /* ZONE DE CONVERSATION */
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
            overflow: hidden;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 105, 180, 0.2);
        }

        .message {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 12px;
            max-width: 80%;
            word-wrap: break-word;
            position: relative;
        }

        .message.user {
            background: linear-gradient(135deg, #ff69b4, #ff1493);
            margin-left: auto;
            color: white;
        }

        .message.assistant {
            background: linear-gradient(135deg, #2a2a2a, #3a3a3a);
            border: 1px solid rgba(255, 105, 180, 0.3);
        }

        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .message-source {
            font-size: 12px;
            opacity: 0.8;
        }

        .message-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            background: none;
            border: none;
            color: #ff69b4;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: rgba(255, 105, 180, 0.2);
        }

        /* ZONE DE SAISIE AVANCÉE */
        .input-container {
            background: linear-gradient(135deg, #1a1a1a, #2a1a2a);
            padding: 20px;
            border-radius: 15px;
            border: 2px solid rgba(255, 105, 180, 0.3);
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .input-row {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            padding: 15px;
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 105, 180, 0.3);
            border-radius: 10px;
            color: white;
            font-size: 16px;
            resize: none;
            min-height: 50px;
            max-height: 150px;
        }

        .message-input:focus {
            outline: none;
            border-color: #ff69b4;
            box-shadow: 0 0 10px rgba(255, 105, 180, 0.3);
        }

        .control-btn {
            padding: 12px 15px;
            background: linear-gradient(135deg, #ff69b4, #ff1493);
            border: none;
            border-radius: 10px;
            color: white;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 105, 180, 0.4);
        }

        .control-btn:active {
            transform: translateY(0);
        }

        .control-btn.recording {
            background: linear-gradient(135deg, #ff4444, #cc0000);
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 68, 68, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 68, 68, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 68, 68, 0); }
        }

        /* ZONE DE TRANSFERT DE FICHIERS */
        .file-transfer {
            display: flex;
            gap: 10px;
            align-items: center;
            padding: 10px;
            background: rgba(255, 105, 180, 0.1);
            border-radius: 8px;
            border: 1px dashed rgba(255, 105, 180, 0.3);
        }

        .file-input {
            display: none;
        }

        .file-label {
            padding: 8px 12px;
            background: rgba(255, 105, 180, 0.2);
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .file-label:hover {
            background: rgba(255, 105, 180, 0.3);
        }

        .file-info {
            flex: 1;
            font-size: 12px;
            color: #cccccc;
        }

        /* INDICATEURS D'ÉTAT */
        .status-indicators {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .indicator {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
        }

        .indicator-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ff69b4;
            animation: blink 2s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        .indicator.connected .indicator-dot {
            background: #00ff00;
            animation: none;
        }

        .indicator.error .indicator-dot {
            background: #ff4444;
        }

        /* RESPONSIVE */
        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 10px;
            }

            .stats {
                flex-wrap: wrap;
                justify-content: center;
            }

            .input-row {
                flex-direction: column;
            }

            .control-btn {
                width: 100%;
                justify-content: center;
            }
        }

        /* SCROLLBAR PERSONNALISÉE */
        .messages::-webkit-scrollbar {
            width: 8px;
        }

        .messages::-webkit-scrollbar-track {
            background: rgba(255, 105, 180, 0.1);
            border-radius: 4px;
        }

        .messages::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #ff69b4, #ff1493);
            border-radius: 4px;
        }

        .messages::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #ff1493, #ff69b4);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- HEADER AVEC STATISTIQUES -->
        <div class="header">
            <div class="logo">🧠 LOUNA-AI</div>
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-value" id="qi-display">320</div>
                    <div class="stat-label">QI Actuel</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="memory-count">42</div>
                    <div class="stat-label">Mémoires</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="temp-display">67.4°C</div>
                    <div class="stat-label">Température</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="zone-display">Zone 5</div>
                    <div class="stat-label">Zone Active</div>
                </div>
            </div>
        </div>

        <!-- ZONE DE CONVERSATION -->
        <div class="chat-container">
            <div class="messages" id="messages">
                <div class="message assistant">
                    <div class="message-header">
                        <span class="message-source">LOUNA-AI - Système Expert</span>
                        <div class="message-actions">
                            <button class="action-btn" onclick="copyMessage(this)">📋 Copier</button>
                            <button class="action-btn" onclick="speakMessage(this)">🔊 Écouter</button>
                        </div>
                    </div>
                    <div class="message-content">
                        🎉 <strong>LOUNA-AI Complète Opérationnelle !</strong><br><br>
                        ✅ <strong>42 formations expertes</strong> intégrées<br>
                        ✅ <strong>Capacités de codage</strong> complètes<br>
                        ✅ <strong>Réflexion avancée</strong> activée<br>
                        ✅ <strong>Mémoire thermique</strong> ultra-performante<br><br>
                        Je peux maintenant créer des programmes complets, analyser en profondeur, et utiliser toutes mes capacités de développeur expert !
                    </div>
                </div>
            </div>

            <!-- ZONE DE SAISIE AVANCÉE -->
            <div class="input-container">
                <!-- TRANSFERT DE FICHIERS -->
                <div class="file-transfer">
                    <input type="file" id="file-input" class="file-input" multiple accept="*/*">
                    <label for="file-input" class="file-label">📁 Choisir fichiers</label>
                    <div class="file-info" id="file-info">Glissez-déposez vos fichiers ici ou cliquez pour sélectionner</div>
                    <button class="control-btn" onclick="sendViaWiFi()">📶 WiFi</button>
                    <button class="control-btn" onclick="sendViaBluetooth()">📱 Bluetooth</button>
                    <button class="control-btn" onclick="sendViaAirDrop()">✈️ AirDrop</button>
                </div>

                <!-- SAISIE PRINCIPALE -->
                <div class="input-row">
                    <textarea 
                        id="message-input" 
                        class="message-input" 
                        placeholder="Tapez votre message ici... (Ctrl+V pour coller)"
                        onkeydown="handleKeyDown(event)"
                    ></textarea>
                    
                    <button class="control-btn" onclick="sendMessage()" style="order: 1; margin-right: 10px;">
                        🚀 Envoyer
                    </button>

                    <button class="control-btn" id="mic-btn" onclick="toggleRecording()" style="order: 2;">
                        🎤 Micro
                    </button>

                    <button class="control-btn" onclick="ouvrirPresentation()" style="order: 3;">📖 Présentation</button>

                    <button class="control-btn" onclick="window.open('memoire-3d-accelerateurs.html', '_blank')" style="order: 4;">🧠 Mémoire 3D</button>

                    <button class="control-btn" onclick="window.open('configuration.html', '_blank')" style="order: 5;">⚙️ Config</button>
                </div>

                <!-- INDICATEURS D'ÉTAT -->
                <div class="status-indicators">
                    <div class="indicator connected">
                        <div class="indicator-dot"></div>
                        <span>Connexion sécurisée</span>
                    </div>
                    <div class="indicator" id="mic-indicator">
                        <div class="indicator-dot"></div>
                        <span>Micro prêt</span>
                    </div>
                    <div class="indicator" id="speaker-indicator">
                        <div class="indicator-dot"></div>
                        <span>Haut-parleur actif</span>
                    </div>
                    <div class="indicator" id="transfer-indicator">
                        <div class="indicator-dot"></div>
                        <span>Transfert disponible</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // VARIABLES GLOBALES
        let isRecording = false;
        let mediaRecorder = null;
        let audioChunks = [];
        let speechSynthesis = window.speechSynthesis;
        let currentVoice = null;

        // INITIALISATION
        document.addEventListener('DOMContentLoaded', function() {
            initializeVoices();
            initializeFileTransfer();
            initializeClipboard();
            startStatsUpdate();
        });

        // INITIALISATION DES VOIX
        function initializeVoices() {
            speechSynthesis.onvoiceschanged = function() {
                const voices = speechSynthesis.getVoices();
                currentVoice = voices.find(voice => voice.lang.startsWith('fr')) || voices[0];
            };
        }

        // INITIALISATION TRANSFERT DE FICHIERS
        function initializeFileTransfer() {
            const fileInput = document.getElementById('file-input');
            const fileInfo = document.getElementById('file-info');
            const container = document.querySelector('.file-transfer');

            // Gestion des fichiers sélectionnés
            fileInput.addEventListener('change', function(e) {
                handleFiles(e.target.files);
            });

            // Glisser-déposer
            container.addEventListener('dragover', function(e) {
                e.preventDefault();
                container.style.background = 'rgba(255, 105, 180, 0.2)';
            });

            container.addEventListener('dragleave', function(e) {
                e.preventDefault();
                container.style.background = 'rgba(255, 105, 180, 0.1)';
            });

            container.addEventListener('drop', function(e) {
                e.preventDefault();
                container.style.background = 'rgba(255, 105, 180, 0.1)';
                handleFiles(e.dataTransfer.files);
            });
        }

        // GESTION DES FICHIERS
        function handleFiles(files) {
            const fileInfo = document.getElementById('file-info');
            if (files.length > 0) {
                const fileNames = Array.from(files).map(f => f.name).join(', ');
                fileInfo.textContent = `${files.length} fichier(s) sélectionné(s): ${fileNames}`;
                
                // Traitement automatique des fichiers
                Array.from(files).forEach(file => {
                    processFile(file);
                });
            }
        }

        // TRAITEMENT DES FICHIERS
        function processFile(file) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                const content = e.target.result;
                
                // Envoyer le fichier à LOUNA-AI
                sendFileToLOUNA(file.name, content, file.type);
            };

            // Lire selon le type de fichier
            if (file.type.startsWith('text/') || file.name.endsWith('.js') || file.name.endsWith('.json')) {
                reader.readAsText(file);
            } else {
                reader.readAsDataURL(file);
            }
        }

        // ENVOI FICHIER À LOUNA
        function sendFileToLOUNA(fileName, content, fileType) {
            const message = `📁 Fichier reçu: ${fileName} (${fileType})`;
            
            fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message: message,
                    file: {
                        name: fileName,
                        content: content,
                        type: fileType
                    }
                })
            })
            .then(response => response.json())
            .then(data => {
                addMessage('assistant', data.response, data.source || 'LOUNA-AI');
                updateStats(data);
            })
            .catch(error => {
                console.error('Erreur envoi fichier:', error);
                addMessage('assistant', '❌ Erreur lors du traitement du fichier', 'Système');
            });
        }

        // INITIALISATION PRESSE-PAPIERS
        function initializeClipboard() {
            // Support du collage avec Ctrl+V
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key === 'v') {
                    navigator.clipboard.readText().then(text => {
                        const input = document.getElementById('message-input');
                        input.value += text;
                        input.focus();
                    }).catch(err => {
                        console.log('Erreur lecture presse-papiers:', err);
                    });
                }
            });
        }

        // GESTION DU MICRO
        async function toggleRecording() {
            const micBtn = document.getElementById('mic-btn');
            const micIndicator = document.getElementById('mic-indicator');

            if (!isRecording) {
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                    mediaRecorder = new MediaRecorder(stream);
                    audioChunks = [];

                    mediaRecorder.ondataavailable = function(event) {
                        audioChunks.push(event.data);
                    };

                    mediaRecorder.onstop = function() {
                        const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                        processAudio(audioBlob);
                    };

                    mediaRecorder.start();
                    isRecording = true;
                    
                    micBtn.classList.add('recording');
                    micBtn.innerHTML = '⏹️ Arrêter';
                    micIndicator.classList.add('connected');
                    micIndicator.querySelector('span').textContent = 'Enregistrement...';

                } catch (error) {
                    console.error('Erreur accès micro:', error);
                    micIndicator.classList.add('error');
                    micIndicator.querySelector('span').textContent = 'Micro indisponible';
                }
            } else {
                mediaRecorder.stop();
                mediaRecorder.stream.getTracks().forEach(track => track.stop());
                isRecording = false;
                
                micBtn.classList.remove('recording');
                micBtn.innerHTML = '🎤 Micro';
                micIndicator.classList.remove('connected');
                micIndicator.querySelector('span').textContent = 'Traitement audio...';
            }
        }

        // TRAITEMENT AUDIO
        function processAudio(audioBlob) {
            // Simulation de reconnaissance vocale
            // En production, utiliser une API de reconnaissance vocale
            const micIndicator = document.getElementById('mic-indicator');
            
            setTimeout(() => {
                const input = document.getElementById('message-input');
                input.value += '[Audio transcrit] ';
                micIndicator.classList.remove('connected');
                micIndicator.querySelector('span').textContent = 'Micro prêt';
            }, 2000);
        }

        // SYNTHÈSE VOCALE
        function speakMessage(button) {
            const messageContent = button.closest('.message').querySelector('.message-content');
            const text = messageContent.textContent;
            
            if (speechSynthesis.speaking) {
                speechSynthesis.cancel();
                return;
            }

            const utterance = new SpeechSynthesisUtterance(text);
            if (currentVoice) {
                utterance.voice = currentVoice;
            }
            utterance.rate = 0.9;
            utterance.pitch = 1.0;
            utterance.volume = 0.8;

            const speakerIndicator = document.getElementById('speaker-indicator');
            speakerIndicator.classList.add('connected');
            speakerIndicator.querySelector('span').textContent = 'Lecture en cours...';

            utterance.onend = function() {
                speakerIndicator.classList.remove('connected');
                speakerIndicator.querySelector('span').textContent = 'Haut-parleur actif';
            };

            speechSynthesis.speak(utterance);
        }

        // COPIER MESSAGE
        function copyMessage(button) {
            const messageContent = button.closest('.message').querySelector('.message-content');
            const text = messageContent.textContent;
            
            navigator.clipboard.writeText(text).then(() => {
                button.textContent = '✅ Copié';
                setTimeout(() => {
                    button.textContent = '📋 Copier';
                }, 2000);
            }).catch(err => {
                console.error('Erreur copie:', err);
                button.textContent = '❌ Erreur';
                setTimeout(() => {
                    button.textContent = '📋 Copier';
                }, 2000);
            });
        }

        // TRANSFERTS SANS FIL
        function sendViaWiFi() {
            const transferIndicator = document.getElementById('transfer-indicator');
            transferIndicator.classList.add('connected');
            transferIndicator.querySelector('span').textContent = 'Transfert WiFi...';
            
            // Simulation transfert WiFi
            setTimeout(() => {
                alert('📶 Transfert WiFi simulé - Fonctionnalité en développement');
                transferIndicator.classList.remove('connected');
                transferIndicator.querySelector('span').textContent = 'Transfert disponible';
            }, 2000);
        }

        function sendViaBluetooth() {
            const transferIndicator = document.getElementById('transfer-indicator');
            transferIndicator.classList.add('connected');
            transferIndicator.querySelector('span').textContent = 'Transfert Bluetooth...';
            
            // Simulation transfert Bluetooth
            setTimeout(() => {
                alert('📱 Transfert Bluetooth simulé - Fonctionnalité en développement');
                transferIndicator.classList.remove('connected');
                transferIndicator.querySelector('span').textContent = 'Transfert disponible';
            }, 2000);
        }

        function sendViaAirDrop() {
            const transferIndicator = document.getElementById('transfer-indicator');
            transferIndicator.classList.add('connected');
            transferIndicator.querySelector('span').textContent = 'Transfert AirDrop...';
            
            // Simulation transfert AirDrop
            setTimeout(() => {
                alert('✈️ Transfert AirDrop simulé - Fonctionnalité en développement');
                transferIndicator.classList.remove('connected');
                transferIndicator.querySelector('span').textContent = 'Transfert disponible';
            }, 2000);
        }

        // GESTION DES TOUCHES
        function handleKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // ENVOI MESSAGE
        function sendMessage() {
            const input = document.getElementById('message-input');
            const message = input.value.trim();
            
            if (!message) return;

            addMessage('user', message, 'Vous');
            input.value = '';

            fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ message: message })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addMessage('assistant', data.reponse || data.response, data.source || 'LOUNA-AI');
                    updateStats({
                        qi_actuel: data.qi_actuel,
                        memoires: data.memory_used ? 42 : 41,
                        temperature: 67.4 + Math.random() * 0.5,
                        zone_active: 5
                    });
                } else {
                    addMessage('assistant', '❌ ' + (data.error || 'Erreur inconnue'), 'Système');
                }
            })
            .catch(error => {
                console.error('Erreur:', error);
                addMessage('assistant', '❌ Erreur de connexion au serveur LOUNA-AI', 'Système');
            });
        }

        // AJOUTER MESSAGE
        function addMessage(type, content, source) {
            const messages = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            messageDiv.innerHTML = `
                <div class="message-header">
                    <span class="message-source">${source}</span>
                    <div class="message-actions">
                        <button class="action-btn" onclick="copyMessage(this)">📋 Copier</button>
                        <button class="action-btn" onclick="speakMessage(this)">🔊 Écouter</button>
                    </div>
                </div>
                <div class="message-content">${content}</div>
            `;
            
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }

        // MISE À JOUR STATISTIQUES
        function updateStats(data) {
            if (data.qi_actuel || data.coefficient_intellectuel) {
                const qiDisplay = document.getElementById('qi-display');
                qiDisplay.textContent = data.qi_actuel || data.coefficient_intellectuel || '320';
                
                // Animation du QI
                qiDisplay.style.transform = 'scale(1.2)';
                qiDisplay.style.color = '#00ff00';
                setTimeout(() => {
                    qiDisplay.style.transform = 'scale(1)';
                    qiDisplay.style.color = '#ff69b4';
                }, 500);
            }
        }

        // MISE À JOUR AUTOMATIQUE DES STATS
        function startStatsUpdate() {
            setInterval(() => {
                fetch('/api/stats')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // QI RÉEL avec tous les modules
                            if (data.coefficient_intellectuel) {
                                const qiElement = document.getElementById('qi-display');
                                qiElement.textContent = data.coefficient_intellectuel;

                                // Animation pour QI élevé
                                if (data.coefficient_intellectuel > 300) {
                                    qiElement.style.color = '#00ff00';
                                    qiElement.style.textShadow = '0 0 10px #00ff00';
                                } else if (data.coefficient_intellectuel > 200) {
                                    qiElement.style.color = '#ffff00';
                                    qiElement.style.textShadow = '0 0 8px #ffff00';
                                }
                            }

                            // Mémoires réelles
                            if (data.stats && data.stats.memoire_thermique && data.stats.memoire_thermique.totalEntries) {
                                document.getElementById('memory-count').textContent = data.stats.memoire_thermique.totalEntries;
                            }

                            // Température réelle
                            if (data.stats && data.stats.memoire_thermique && data.stats.memoire_thermique.currentTemperature) {
                                document.getElementById('temp-display').textContent = data.stats.memoire_thermique.currentTemperature.toFixed(1) + '°C';
                            }

                            // Zone réelle
                            if (data.stats && data.stats.memoire_thermique && data.stats.memoire_thermique.currentZone) {
                                document.getElementById('zone-display').textContent = 'Zone ' + data.stats.memoire_thermique.currentZone;
                            }

                            // Affichage des accélérateurs
                            if (data.stats && data.stats.accelerateurs && data.stats.accelerateurs.actifs) {
                                const accelerateursElement = document.getElementById('accelerateurs-count');
                                if (accelerateursElement) {
                                    accelerateursElement.textContent = data.stats.accelerateurs.actifs + ' actifs';
                                }
                            }
                        }
                    })
                    .catch(error => {
                        console.log('Erreur stats:', error);
                    });
            }, 5000); // Mise à jour toutes les 5 secondes
        }

        // OUVRIR PRÉSENTATION TECHNIQUE
        function ouvrirPresentation() {
            window.open('presentation-louna-ai.html', '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
        }

        // INITIALISATION
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Interface LOUNA-AI chargée');
            startStatsUpdate();

            // Test de connexion initial avec le vrai serveur
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('✅ Connexion serveur LOUNA-AI établie');
                        const qi = data.coefficient_intellectuel || 320;
                        const memoires = (data.stats && data.stats.memoire_thermique && data.stats.memoire_thermique.totalEntries) || 42;
                        const temp = (data.stats && data.stats.memoire_thermique && data.stats.memoire_thermique.currentTemperature) || 67.4;
                        addMessage('assistant', `🎉 LOUNA-AI Interface Complète connectée avec mémoire thermique réelle ! QI: ${qi}, Mémoires: ${memoires}, Température: ${temp.toFixed(1)}°C`, 'LOUNA-AI Système');
                    }
                })
                .catch(error => {
                    console.error('❌ Erreur connexion:', error);
                    addMessage('assistant', '❌ Impossible de se connecter au serveur LOUNA-AI avec mémoire thermique', 'Système');
                });
        });
    </script>
</body>
</html>
