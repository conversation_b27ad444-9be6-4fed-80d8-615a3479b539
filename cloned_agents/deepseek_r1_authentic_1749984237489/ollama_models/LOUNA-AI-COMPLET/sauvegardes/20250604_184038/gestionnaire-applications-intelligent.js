/**
 * GESTIONNAIRE D'APPLICATIONS INTELLIGENT POUR LOUNA-AI
 * Système modulaire pour ouvrir applications, rechercher infos, créer fiches techniques
 */

const { exec, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

class GestionnaireApplicationsIntelligent {
    constructor() {
        // Base de données des applications connues
        this.applicationsConnues = new Map([
            // Applications de codage
            ['vscode', { nom: 'Visual Studio Code', commande: 'code', type: 'codage', description: 'Éditeur de code Microsoft' }],
            ['xcode', { nom: 'Xcode', commande: 'open -a Xcode', type: 'codage', description: 'IDE Apple pour iOS/macOS' }],
            ['sublime', { nom: 'Sublime Text', commande: 'subl', type: 'codage', description: 'Éditeur de texte avancé' }],
            ['atom', { nom: 'Atom', commande: 'atom', type: 'codage', description: '<PERSON><PERSON><PERSON>it<PERSON> (obsolète)' }],
            ['webstorm', { nom: 'WebStorm', commande: 'open -a WebStorm', type: 'codage', description: 'IDE JetBrains pour web' }],
            ['pycharm', { nom: 'PyCharm', commande: 'open -a PyCharm', type: 'codage', description: 'IDE Python JetBrains' }],
            
            // Applications système
            ['terminal', { nom: 'Terminal', commande: 'open -a Terminal', type: 'système', description: 'Terminal macOS' }],
            ['finder', { nom: 'Finder', commande: 'open -a Finder', type: 'système', description: 'Gestionnaire de fichiers' }],
            ['safari', { nom: 'Safari', commande: 'open -a Safari', type: 'navigateur', description: 'Navigateur Apple' }],
            ['chrome', { nom: 'Google Chrome', commande: 'open -a "Google Chrome"', type: 'navigateur', description: 'Navigateur Google' }],
            ['firefox', { nom: 'Firefox', commande: 'open -a Firefox', type: 'navigateur', description: 'Navigateur Mozilla' }],
            
            // Applications créatives
            ['photoshop', { nom: 'Adobe Photoshop', commande: 'open -a "Adobe Photoshop 2024"', type: 'créatif', description: 'Éditeur d\'images Adobe' }],
            ['illustrator', { nom: 'Adobe Illustrator', commande: 'open -a "Adobe Illustrator 2024"', type: 'créatif', description: 'Éditeur vectoriel Adobe' }],
            ['figma', { nom: 'Figma', commande: 'open -a Figma', type: 'créatif', description: 'Design UI/UX' }],
            
            // Applications bureautique
            ['word', { nom: 'Microsoft Word', commande: 'open -a "Microsoft Word"', type: 'bureautique', description: 'Traitement de texte' }],
            ['excel', { nom: 'Microsoft Excel', commande: 'open -a "Microsoft Excel"', type: 'bureautique', description: 'Tableur Microsoft' }],
            ['powerpoint', { nom: 'Microsoft PowerPoint', commande: 'open -a "Microsoft PowerPoint"', type: 'bureautique', description: 'Présentations Microsoft' }],
            
            // Applications spécialisées
            ['docker', { nom: 'Docker Desktop', commande: 'open -a "Docker Desktop"', type: 'développement', description: 'Conteneurisation' }],
            ['postman', { nom: 'Postman', commande: 'open -a Postman', type: 'développement', description: 'Test API' }],
            ['github', { nom: 'GitHub Desktop', commande: 'open -a "GitHub Desktop"', type: 'développement', description: 'Client Git GitHub' }]
        ]);
        
        this.historique = [];
        this.fichesCreees = new Map();
        this.recherchesEffectuees = new Map();
    }

    // OUVRIR UNE APPLICATION
    async ouvrirApplication(nomApp, parametres = null) {
        try {
            const appKey = nomApp.toLowerCase().replace(/\s+/g, '');
            console.log(`🚀 Tentative ouverture: ${nomApp} (clé: ${appKey})`);
            
            let application = this.applicationsConnues.get(appKey);
            
            // Si l'application n'est pas connue, essayer de la trouver
            if (!application) {
                console.log(`🔍 Application inconnue, recherche en cours...`);
                application = await this.rechercherApplication(nomApp);
            }
            
            if (!application) {
                return {
                    success: false,
                    message: `Application "${nomApp}" non trouvée. Voulez-vous que je recherche des informations à son sujet ?`,
                    suggestions: await this.suggererApplicationsSimilaires(nomApp)
                };
            }
            
            // Construire la commande
            let commande = application.commande;
            if (parametres) {
                commande += ` ${parametres}`;
            }
            
            console.log(`⚡ Exécution: ${commande}`);
            
            // Exécuter la commande
            const resultat = await this.executerCommande(commande);
            
            // Enregistrer dans l'historique
            this.historique.push({
                action: 'ouverture_application',
                application: application.nom,
                commande: commande,
                succes: resultat.success,
                timestamp: Date.now()
            });
            
            if (resultat.success) {
                return {
                    success: true,
                    application: application.nom,
                    type: application.type,
                    description: application.description,
                    message: `✅ ${application.nom} ouvert avec succès !`,
                    commande_executee: commande
                };
            } else {
                return {
                    success: false,
                    message: `❌ Erreur lors de l'ouverture de ${application.nom}: ${resultat.error}`,
                    suggestion: "Vérifiez que l'application est installée sur votre système."
                };
            }
            
        } catch (error) {
            console.error(`❌ Erreur ouverture application: ${error.message}`);
            return {
                success: false,
                error: error.message,
                message: `Erreur lors de l'ouverture de ${nomApp}: ${error.message}`
            };
        }
    }

    // RECHERCHER UNE APPLICATION INCONNUE
    async rechercherApplication(nomApp) {
        try {
            console.log(`🔍 Recherche application: ${nomApp}`);
            
            // Essayer de trouver l'application dans /Applications
            const commandeRecherche = `find /Applications -name "*${nomApp}*" -type d -maxdepth 2 2>/dev/null | head -5`;
            const resultatRecherche = await this.executerCommande(commandeRecherche);
            
            if (resultatRecherche.success && resultatRecherche.sortie.trim()) {
                const applications = resultatRecherche.sortie.split('\n').filter(ligne => ligne.trim());
                
                if (applications.length > 0) {
                    const premierApp = applications[0];
                    const nomApplication = path.basename(premierApp, '.app');
                    
                    const nouvelleApp = {
                        nom: nomApplication,
                        commande: `open -a "${nomApplication}"`,
                        type: 'découvert',
                        description: `Application découverte: ${nomApplication}`
                    };
                    
                    // Ajouter à la base de données
                    this.applicationsConnues.set(nomApp.toLowerCase(), nouvelleApp);
                    
                    console.log(`✅ Application trouvée: ${nomApplication}`);
                    return nouvelleApp;
                }
            }
            
            // Si pas trouvé, proposer une recherche internet
            console.log(`❌ Application non trouvée localement`);
            return null;
            
        } catch (error) {
            console.error(`❌ Erreur recherche application: ${error.message}`);
            return null;
        }
    }

    // SUGGÉRER DES APPLICATIONS SIMILAIRES
    async suggererApplicationsSimilaires(nomApp) {
        const suggestions = [];
        const nomLower = nomApp.toLowerCase();
        
        // Recherche par mots-clés
        const motsClés = {
            'code': ['vscode', 'sublime', 'atom'],
            'edit': ['vscode', 'sublime', 'atom'],
            'photo': ['photoshop'],
            'design': ['figma', 'photoshop', 'illustrator'],
            'web': ['chrome', 'safari', 'firefox'],
            'terminal': ['terminal'],
            'git': ['github'],
            'docker': ['docker']
        };
        
        for (const [motClé, apps] of Object.entries(motsClés)) {
            if (nomLower.includes(motClé)) {
                apps.forEach(app => {
                    const appInfo = this.applicationsConnues.get(app);
                    if (appInfo) {
                        suggestions.push(appInfo);
                    }
                });
            }
        }
        
        return suggestions.slice(0, 3); // Limiter à 3 suggestions
    }

    // RECHERCHER DES INFORMATIONS SUR INTERNET
    async rechercherInformationsApplication(nomApp) {
        try {
            console.log(`🌐 Recherche internet pour: ${nomApp}`);
            
            // Simuler une recherche (dans un vrai système, on utiliserait une API)
            const informations = {
                nom: nomApp,
                description: `Informations recherchées pour ${nomApp}`,
                installation: `Pour installer ${nomApp}, visitez le site officiel ou utilisez Homebrew`,
                utilisation: `${nomApp} est utilisé pour diverses tâches selon son type`,
                alternatives: [],
                liens: [
                    `https://www.google.com/search?q=${encodeURIComponent(nomApp + ' application mac')}`,
                    `https://www.youtube.com/results?search_query=${encodeURIComponent(nomApp + ' tutorial')}`
                ]
            };
            
            // Enregistrer la recherche
            this.recherchesEffectuees.set(nomApp, {
                informations: informations,
                timestamp: Date.now()
            });
            
            return {
                success: true,
                informations: informations,
                message: `Informations trouvées pour ${nomApp}`
            };
            
        } catch (error) {
            return {
                success: false,
                error: error.message,
                message: `Erreur lors de la recherche d'informations pour ${nomApp}`
            };
        }
    }

    // CRÉER UNE FICHE TECHNIQUE
    async creerFicheTechnique(nomApp, informations = null) {
        try {
            console.log(`📋 Création fiche technique: ${nomApp}`);
            
            let infos = informations;
            if (!infos) {
                // Rechercher les informations si pas fournies
                const recherche = await this.rechercherInformationsApplication(nomApp);
                if (recherche.success) {
                    infos = recherche.informations;
                } else {
                    infos = { nom: nomApp, description: 'Informations limitées' };
                }
            }
            
            const fiche = {
                nom: infos.nom || nomApp,
                description: infos.description || 'Description non disponible',
                type: infos.type || 'Non spécifié',
                installation: infos.installation || 'Instructions d\'installation non disponibles',
                utilisation: infos.utilisation || 'Instructions d\'utilisation non disponibles',
                alternatives: infos.alternatives || [],
                liens: infos.liens || [],
                dateCreation: new Date().toISOString(),
                creePar: 'LOUNA-AI'
            };
            
            // Enregistrer la fiche
            this.fichesCreees.set(nomApp, fiche);
            
            // Optionnel: sauvegarder sur disque
            const cheminFiche = path.join(__dirname, 'fiches-techniques', `${nomApp.replace(/\s+/g, '_')}.json`);
            try {
                const dossierFiches = path.dirname(cheminFiche);
                if (!fs.existsSync(dossierFiches)) {
                    fs.mkdirSync(dossierFiches, { recursive: true });
                }
                fs.writeFileSync(cheminFiche, JSON.stringify(fiche, null, 2));
                console.log(`💾 Fiche sauvegardée: ${cheminFiche}`);
            } catch (e) {
                console.log(`⚠️ Impossible de sauvegarder la fiche: ${e.message}`);
            }
            
            return {
                success: true,
                fiche: fiche,
                message: `📋 Fiche technique créée pour ${nomApp}`,
                chemin: cheminFiche
            };
            
        } catch (error) {
            return {
                success: false,
                error: error.message,
                message: `Erreur lors de la création de la fiche technique pour ${nomApp}`
            };
        }
    }

    // TRAITEMENT INTELLIGENT DES DEMANDES
    async traiterDemande(demande) {
        const demandeLower = demande.toLowerCase();
        
        try {
            // Détection du type de demande
            if (demandeLower.includes('ouvre') || demandeLower.includes('lance') || demandeLower.includes('démarre')) {
                return await this.traiterDemandeOuverture(demande);
            }
            
            if (demandeLower.includes('fiche') || demandeLower.includes('documentation') || demandeLower.includes('info')) {
                return await this.traiterDemandeFiche(demande);
            }
            
            if (demandeLower.includes('recherche') || demandeLower.includes('trouve') || demandeLower.includes('cherche')) {
                return await this.traiterDemandeRecherche(demande);
            }
            
            if (demandeLower.includes('applications') || demandeLower.includes('liste')) {
                return await this.listerApplications();
            }
            
            // Par défaut, essayer d'ouvrir l'application mentionnée
            return await this.traiterDemandeOuverture(demande);
            
        } catch (error) {
            return {
                success: false,
                error: error.message,
                message: `Erreur lors du traitement de la demande: ${error.message}`
            };
        }
    }

    // TRAITER DEMANDE D'OUVERTURE
    async traiterDemandeOuverture(demande) {
        // Extraire le nom de l'application
        const mots = demande.split(/\s+/);
        let nomApp = '';
        
        // Chercher après les mots-clés d'action
        const motsAction = ['ouvre', 'lance', 'démarre', 'open', 'start'];
        let indexAction = -1;
        
        for (let i = 0; i < mots.length; i++) {
            if (motsAction.some(action => mots[i].toLowerCase().includes(action))) {
                indexAction = i;
                break;
            }
        }
        
        if (indexAction >= 0 && indexAction < mots.length - 1) {
            nomApp = mots.slice(indexAction + 1).join(' ').replace(/['"]/g, '');
        } else {
            // Si pas de mot d'action trouvé, prendre le dernier mot
            nomApp = mots[mots.length - 1].replace(/['"]/g, '');
        }
        
        if (!nomApp) {
            return {
                success: false,
                message: "Je n'ai pas pu identifier l'application à ouvrir. Pouvez-vous préciser ?",
                exemple: "Exemple: 'Ouvre VS Code' ou 'Lance Terminal'"
            };
        }
        
        return await this.ouvrirApplication(nomApp);
    }

    // TRAITER DEMANDE DE FICHE
    async traiterDemandeFiche(demande) {
        const nomApp = this.extraireNomApplication(demande);
        if (!nomApp) {
            return {
                success: false,
                message: "Pour quelle application voulez-vous une fiche technique ?",
                exemple: "Exemple: 'Crée une fiche pour VS Code'"
            };
        }
        
        return await this.creerFicheTechnique(nomApp);
    }

    // TRAITER DEMANDE DE RECHERCHE
    async traiterDemandeRecherche(demande) {
        const nomApp = this.extraireNomApplication(demande);
        if (!nomApp) {
            return {
                success: false,
                message: "Que voulez-vous rechercher ?",
                exemple: "Exemple: 'Recherche des infos sur Figma'"
            };
        }
        
        return await this.rechercherInformationsApplication(nomApp);
    }

    // LISTER LES APPLICATIONS CONNUES
    async listerApplications() {
        const applications = Array.from(this.applicationsConnues.values());
        const parType = {};
        
        applications.forEach(app => {
            if (!parType[app.type]) {
                parType[app.type] = [];
            }
            parType[app.type].push(app);
        });
        
        return {
            success: true,
            applications: applications,
            parType: parType,
            total: applications.length,
            message: `${applications.length} applications disponibles`
        };
    }

    // UTILITAIRES
    extraireNomApplication(demande) {
        // Logique simple pour extraire le nom d'application
        const mots = demande.split(/\s+/);
        return mots[mots.length - 1].replace(/['"]/g, '');
    }

    async executerCommande(commande) {
        return new Promise((resolve) => {
            exec(commande, { timeout: 10000 }, (error, stdout, stderr) => {
                if (error) {
                    resolve({
                        success: false,
                        error: error.message,
                        sortie: stdout,
                        erreur: stderr
                    });
                } else {
                    resolve({
                        success: true,
                        sortie: stdout,
                        erreur: stderr
                    });
                }
            });
        });
    }

    // STATISTIQUES
    obtenirStatistiques() {
        const stats = {
            applications_connues: this.applicationsConnues.size,
            historique_actions: this.historique.length,
            fiches_creees: this.fichesCreees.size,
            recherches_effectuees: this.recherchesEffectuees.size,
            types_applications: {}
        };
        
        // Compter par type
        Array.from(this.applicationsConnues.values()).forEach(app => {
            stats.types_applications[app.type] = (stats.types_applications[app.type] || 0) + 1;
        });
        
        return stats;
    }
}

module.exports = GestionnaireApplicationsIntelligent;
