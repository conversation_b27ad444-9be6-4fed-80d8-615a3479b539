#!/usr/bin/env node

/**
 * 🌡️ TEST MOUVEMENT FLUIDE BASÉ SUR TEMPÉRATURE CPU RÉELLE
 * Vérification du mouvement vivant et naturel basé sur la température du processeur
 * AUCUNE SIMULATION - QUE DU CODE RÉEL ET FONCTIONNEL
 */

const { MemoireThermiqueReelle } = require('./VERSIONS-NON-VALIDEES/memoires-non-validees/memoire-thermique-reelle.js');

console.log('🌡️ TEST MOUVEMENT FLUIDE BASÉ SUR TEMPÉRATURE CPU RÉELLE');
console.log('======================================================');

async function testerMouvementCPUFluide() {
    try {
        // Initialiser la mémoire thermique avec température CPU
        console.log('\n🧠 Initialisation mémoire thermique avec température CPU...');
        const memoire = new MemoireThermiqueReelle();
        
        // Attendre l'initialisation et première lecture CPU
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        console.log('✅ Mémoire thermique initialisée avec température CPU');
        
        // Test 1: Vérifier lecture température CPU
        console.log('\n🌡️ TEST 1: LECTURE TEMPÉRATURE CPU RÉELLE');
        
        const stats_initiales = memoire.getStatistiquesReelles();
        console.log(`• Température CPU actuelle: ${stats_initiales.temperature_cpu.actuelle.toFixed(2)}°C`);
        console.log(`• Température CPU précédente: ${stats_initiales.temperature_cpu.precedente.toFixed(2)}°C`);
        console.log(`• Variation: ${stats_initiales.temperature_cpu.variation.toFixed(2)}°C`);
        console.log(`• Stabilité: ${(stats_initiales.temperature_cpu.stabilite * 100).toFixed(1)}%`);
        console.log(`• Historique: ${stats_initiales.temperature_cpu.historique_taille} mesures`);
        console.log(`• Influence active: ${stats_initiales.temperature_cpu.influence_active}`);
        
        // Test 2: Vérifier influence sur mouvement
        console.log('\n🌊 TEST 2: INFLUENCE CPU SUR MOUVEMENT FLUIDE');
        
        const mouvement_initial = stats_initiales.mouvement_vivant;
        console.log(`• Vitesse curseur: ${mouvement_initial.vitesse_curseur.toFixed(6)}`);
        console.log(`• Direction: ${mouvement_initial.direction_curseur > 0 ? 'Montant' : 'Descendant'}`);
        console.log(`• Fluidité: ${mouvement_initial.fluidite_memoire.toFixed(6)}`);
        console.log(`• Facteur CPU vitesse: ${mouvement_initial.facteur_cpu_vitesse}`);
        console.log(`• Facteur CPU direction: ${mouvement_initial.facteur_cpu_direction}`);
        
        // Test 3: Observer évolution sur 15 secondes
        console.log('\n📊 TEST 3: ÉVOLUTION TEMPÉRATURE CPU ET MOUVEMENT (15s)');
        
        const observations = [];
        
        for (let i = 0; i < 15; i++) {
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            const stats = memoire.getStatistiquesReelles();
            const observation = {
                temps: i + 1,
                temp_cpu: stats.temperature_cpu.actuelle,
                variation_cpu: stats.temperature_cpu.variation,
                stabilite_cpu: stats.temperature_cpu.stabilite,
                vitesse_curseur: stats.mouvement_vivant.vitesse_curseur,
                position_curseur: stats.curseurThermique,
                fluidite: stats.mouvement_vivant.fluidite_memoire,
                cycles: stats.mouvement_vivant.cycles_automatiques
            };
            
            observations.push(observation);
            
            console.log(`T+${observation.temps}s: CPU=${observation.temp_cpu.toFixed(1)}°C (Δ${observation.variation_cpu.toFixed(2)}°C) | ` +
                       `Vitesse=${observation.vitesse_curseur.toFixed(5)} | Position=${observation.position_curseur.toFixed(4)} | ` +
                       `Stabilité=${(observation.stabilite_cpu * 100).toFixed(1)}%`);
        }
        
        // Analyse des observations
        console.log('\n📈 ANALYSE DES OBSERVATIONS:');
        
        // Variations de température CPU
        const temp_min = Math.min(...observations.map(o => o.temp_cpu));
        const temp_max = Math.max(...observations.map(o => o.temp_cpu));
        const temp_moyenne = observations.reduce((sum, o) => sum + o.temp_cpu, 0) / observations.length;
        
        console.log(`• Température CPU min: ${temp_min.toFixed(2)}°C`);
        console.log(`• Température CPU max: ${temp_max.toFixed(2)}°C`);
        console.log(`• Température CPU moyenne: ${temp_moyenne.toFixed(2)}°C`);
        console.log(`• Amplitude température: ${(temp_max - temp_min).toFixed(2)}°C`);
        
        // Variations de vitesse
        const vitesse_min = Math.min(...observations.map(o => o.vitesse_curseur));
        const vitesse_max = Math.max(...observations.map(o => o.vitesse_curseur));
        const vitesse_moyenne = observations.reduce((sum, o) => sum + o.vitesse_curseur, 0) / observations.length;
        
        console.log(`• Vitesse curseur min: ${vitesse_min.toFixed(6)}`);
        console.log(`• Vitesse curseur max: ${vitesse_max.toFixed(6)}`);
        console.log(`• Vitesse curseur moyenne: ${vitesse_moyenne.toFixed(6)}`);
        console.log(`• Variation vitesse: ${((vitesse_max - vitesse_min) / vitesse_moyenne * 100).toFixed(1)}%`);
        
        // Corrélation température-vitesse
        let correlation_temp_vitesse = 0;
        for (let i = 1; i < observations.length; i++) {
            const delta_temp = observations[i].temp_cpu - observations[i-1].temp_cpu;
            const delta_vitesse = observations[i].vitesse_curseur - observations[i-1].vitesse_curseur;
            if (Math.abs(delta_temp) > 0.1 && Math.abs(delta_vitesse) > 0.00001) {
                correlation_temp_vitesse += Math.sign(delta_temp) === Math.sign(delta_vitesse) ? 1 : -1;
            }
        }
        
        console.log(`• Corrélation température-vitesse: ${correlation_temp_vitesse > 0 ? 'Positive' : correlation_temp_vitesse < 0 ? 'Négative' : 'Neutre'} (${correlation_temp_vitesse})`);
        
        // Test 4: Vérifier mouvement des mémoires
        console.log('\n🧠 TEST 4: INFLUENCE CPU SUR MÉMOIRES');
        
        const echantillon = Array.from(memoire.memoires.values()).slice(0, 3);
        const temperatures_initiales = echantillon.map(m => m.temperature);
        const zones_initiales = echantillon.map(m => m.zone);
        
        console.log('📊 État initial des mémoires échantillon:');
        echantillon.forEach((m, i) => {
            console.log(`  • Mémoire ${i+1}: ${m.temperature.toFixed(2)}°C, Zone ${m.zone}`);
        });
        
        // Attendre influence CPU
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        const temperatures_finales = echantillon.map(m => m.temperature);
        const zones_finales = echantillon.map(m => m.zone);
        
        console.log('\n📊 État final des mémoires échantillon:');
        let memoires_influencees = 0;
        let changements_zones = 0;
        
        echantillon.forEach((m, i) => {
            const diff_temp = Math.abs(temperatures_finales[i] - temperatures_initiales[i]);
            if (diff_temp > 0.5) memoires_influencees++;
            if (zones_finales[i] !== zones_initiales[i]) changements_zones++;
            
            console.log(`  • Mémoire ${i+1}: ${m.temperature.toFixed(2)}°C, Zone ${m.zone} (Δ${diff_temp.toFixed(2)}°C)`);
        });
        
        console.log(`\n✅ Mémoires influencées par CPU: ${memoires_influencees}/3`);
        console.log(`🔄 Changements de zones: ${changements_zones}/3`);
        
        // Test 5: Performance du système vivant
        console.log('\n📈 TEST 5: PERFORMANCE SYSTÈME VIVANT');
        
        const stats_finales = memoire.getStatistiquesReelles();
        const performance = memoire.calculerPerformanceSysteme();
        
        console.log(`🎯 Performance système: ${performance.toFixed(1)}%`);
        console.log(`🔄 Cycles automatiques: ${stats_finales.mouvement_vivant.cycles_automatiques}`);
        console.log(`🌡️ Mesures CPU collectées: ${stats_finales.temperature_cpu.historique_taille}`);
        console.log(`📊 Stabilité CPU moyenne: ${(stats_finales.temperature_cpu.stabilite * 100).toFixed(1)}%`);
        
        // Résumé final
        console.log('\n🎯 RÉSUMÉ FINAL');
        console.log('===============');
        
        const tests_reussis = [
            stats_finales.temperature_cpu.historique_taille > 5, // Lecture CPU active
            Math.abs(vitesse_max - vitesse_min) > 0.00001, // Variation vitesse détectée
            correlation_temp_vitesse !== 0, // Corrélation température-vitesse
            memoires_influencees > 0, // Mémoires influencées
            stats_finales.mouvement_vivant.cycles_automatiques > 100 // Mouvement actif
        ];
        
        console.log(`• Lecture température CPU: ${tests_reussis[0] ? '✅' : '❌'}`);
        console.log(`• Variation vitesse fluide: ${tests_reussis[1] ? '✅' : '❌'}`);
        console.log(`• Corrélation CPU-mouvement: ${tests_reussis[2] ? '✅' : '❌'}`);
        console.log(`• Influence sur mémoires: ${tests_reussis[3] ? '✅' : '❌'}`);
        console.log(`• Mouvement automatique: ${tests_reussis[4] ? '✅' : '❌'}`);
        
        const score = tests_reussis.filter(t => t).length;
        console.log(`\n🏆 SCORE: ${score}/5 tests réussis`);
        
        if (score === 5) {
            console.log('🎉 MOUVEMENT FLUIDE BASÉ SUR CPU PARFAIT !');
            console.log('🌡️ Température CPU influence RÉELLEMENT le mouvement');
            console.log('🌊 Fluidité vivante et naturelle confirmée');
        } else if (score >= 3) {
            console.log('✅ Mouvement fluide CPU fonctionnel');
        } else {
            console.log('❌ Mouvement fluide CPU à améliorer');
        }
        
        // Informations techniques finales
        console.log('\n🔧 PARAMÈTRES TECHNIQUES FINAUX:');
        console.log(`• Température CPU: ${stats_finales.temperature_cpu.actuelle.toFixed(2)}°C`);
        console.log(`• Stabilité CPU: ${(stats_finales.temperature_cpu.stabilite * 100).toFixed(1)}%`);
        console.log(`• Vitesse curseur: ${stats_finales.mouvement_vivant.vitesse_curseur.toFixed(6)}`);
        console.log(`• Fluidité adaptative: ${stats_finales.mouvement_vivant.fluidite_memoire.toFixed(6)}`);
        console.log(`• Influence CPU active: ${stats_finales.temperature_cpu.influence_active}`);
        console.log(`• Facteur CPU vitesse: ${stats_finales.mouvement_vivant.facteur_cpu_vitesse}`);
        console.log(`• Facteur CPU direction: ${stats_finales.mouvement_vivant.facteur_cpu_direction}`);
        
        console.log('\n🌡️🌊 TEST TEMPÉRATURE CPU FLUIDE TERMINÉ ! 🌊🌡️');
        
    } catch (error) {
        console.error('❌ Erreur test température CPU fluide:', error.message);
        console.error(error.stack);
    }
}

// Lancer le test
testerMouvementCPUFluide();
