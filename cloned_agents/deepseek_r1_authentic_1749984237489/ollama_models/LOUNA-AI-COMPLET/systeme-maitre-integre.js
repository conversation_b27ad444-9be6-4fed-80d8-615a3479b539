/**
 * SYSTÈME MAÎTRE INTÉGRÉ
 * Orchestrateur principal de tous les composants
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Import des modules
const SystemeFinalFonctionnel = require('./systeme-final-fonctionnel.js');
const MonitoringTempsReel = require('./monitoring-temps-reel.js');
const AutoEvolution = require('./auto-evolution.js');
const CompressionAutomatique = require('./compression-automatique.js');
const InterfaceMonitoring = require('./interface-monitoring.js');
const RechercheEtendue = require('./recherche-etendue.js');

class SystemeMaitreIntegre {
    constructor() {
        console.log('👑 SYSTÈME MAÎTRE INTÉGRÉ');
        console.log('=========================');

        this.config = {
            memoire: '/Volumes/LounaAI_V3/MEMOIRE-REELLE',
            maitre: '/Volumes/LounaAI_V3/MEMOIRE-REELLE/systeme-maitre'
        };

        this.composants = {
            systeme_principal: null,
            monitoring: null,
            auto_evolution: null,
            compression: null,
            interface_web: null,
            recherche_etendue: null
        };

        this.etat_maitre = {
            initialise: false,
            tous_composants_actifs: false,
            mode_autonome: false,
            derniere_synchronisation: null
        };

        this.metriques_globales = {
            temps_demarrage: Date.now(),
            interactions_totales: 0,
            evolutions_detectees: 0,
            problemes_resolus: 0,
            qi_moyen: 85,
            uptime_total: 0
        };

        this.initialiserSystemeMaitre();
    }

    async initialiserSystemeMaitre() {
        console.log('🔧 Initialisation système maître...');

        try {
            // Créer structure maître
            this.creerStructureMaitre();

            // Initialiser composants
            await this.initialiserComposants();

            // Synchroniser composants
            this.synchroniserComposants();

            // Démarrer orchestration
            this.demarrerOrchestration();

            this.etat_maitre.initialise = true;
            console.log('✅ Système maître intégré initialisé');

        } catch (error) {
            console.log(`❌ Erreur initialisation maître: ${error.message}`);
        }
    }

    creerStructureMaitre() {
        console.log('\n📁 CRÉATION STRUCTURE MAÎTRE');
        console.log('============================');

        const dossiers = [
            this.config.maitre,
            path.join(this.config.maitre, 'orchestration'),
            path.join(this.config.maitre, 'synchronisation'),
            path.join(this.config.maitre, 'logs-maitre'),
            path.join(this.config.maitre, 'backup-systeme')
        ];

        dossiers.forEach(dossier => {
            if (!fs.existsSync(dossier)) {
                fs.mkdirSync(dossier, { recursive: true });
                console.log(`✅ Créé: ${path.basename(dossier)}`);
            }
        });

        console.log('✅ Structure maître créée');
    }

    async initialiserComposants() {
        console.log('\n🔧 INITIALISATION COMPOSANTS');
        console.log('============================');

        try {
            // 1. Système principal
            console.log('🎯 Initialisation système principal...');
            this.composants.systeme_principal = new SystemeFinalFonctionnel();
            await new Promise(resolve => setTimeout(resolve, 2000)); // Attendre initialisation
            console.log('✅ Système principal initialisé');

            // 2. Monitoring
            console.log('👁️ Initialisation monitoring...');
            this.composants.monitoring = new MonitoringTempsReel();
            await new Promise(resolve => setTimeout(resolve, 1000));
            console.log('✅ Monitoring initialisé');

            // 3. Auto-évolution
            console.log('🧬 Initialisation auto-évolution...');
            this.composants.auto_evolution = new AutoEvolution();
            await new Promise(resolve => setTimeout(resolve, 1000));
            console.log('✅ Auto-évolution initialisée');

            // 4. Compression automatique
            console.log('🗜️ Initialisation compression...');
            this.composants.compression = new CompressionAutomatique();
            await new Promise(resolve => setTimeout(resolve, 1000));
            console.log('✅ Compression initialisée');

            // 5. Recherche étendue
            console.log('🔍 Initialisation recherche étendue...');
            this.composants.recherche_etendue = new RechercheEtendue();
            await new Promise(resolve => setTimeout(resolve, 1000));
            console.log('✅ Recherche étendue initialisée');

            // 6. Interface web
            console.log('🖥️ Initialisation interface web...');
            this.composants.interface_web = new InterfaceMonitoring();
            await new Promise(resolve => setTimeout(resolve, 2000));
            console.log('✅ Interface web initialisée');

            this.etat_maitre.tous_composants_actifs = true;

        } catch (error) {
            console.log(`❌ Erreur initialisation composants: ${error.message}`);
        }
    }

    synchroniserComposants() {
        console.log('\n🔄 SYNCHRONISATION COMPOSANTS');
        console.log('=============================');

        try {
            // Synchroniser métriques
            if (this.composants.systeme_principal && this.composants.auto_evolution) {
                this.metriques_globales.qi_moyen = this.composants.auto_evolution.metriques_evolution.niveau_intelligence;
            }

            // Synchroniser états
            this.synchroniserEtats();

            // Créer liens entre composants
            this.creerLiensComposants();

            this.etat_maitre.derniere_synchronisation = Date.now();
            console.log('✅ Composants synchronisés');

        } catch (error) {
            console.log(`❌ Erreur synchronisation: ${error.message}`);
        }
    }

    synchroniserEtats() {
        const etatGlobal = {
            timestamp: Date.now(),
            systeme_principal: this.composants.systeme_principal ? {
                systeme_pret: this.composants.systeme_principal.etat.systeme_pret,
                curseur_position: this.composants.systeme_principal.curseur.position,
                interactions: this.composants.systeme_principal.stats.interactions_totales
            } : null,
            monitoring: this.composants.monitoring ? {
                score_sante: this.composants.monitoring.calculerScoreSante(),
                verifications: this.composants.monitoring.metriques.verifications_totales
            } : null,
            auto_evolution: this.composants.auto_evolution ? {
                qi_actuel: this.composants.auto_evolution.metriques_evolution.niveau_intelligence,
                patterns: this.composants.auto_evolution.metriques_evolution.patterns_detectes
            } : null
        };

        // Sauvegarder état global
        const cheminEtat = path.join(this.config.maitre, 'synchronisation', 'etat_global.json');
        fs.writeFileSync(cheminEtat, JSON.stringify(etatGlobal, null, 2));
    }

    creerLiensComposants() {
        // Créer fichiers de liaison entre composants
        const liens = {
            systeme_vers_monitoring: {
                source: 'systeme_principal',
                destination: 'monitoring',
                type: 'metriques_systeme',
                actif: true
            },
            monitoring_vers_evolution: {
                source: 'monitoring',
                destination: 'auto_evolution',
                type: 'donnees_performance',
                actif: true
            },
            evolution_vers_systeme: {
                source: 'auto_evolution',
                destination: 'systeme_principal',
                type: 'optimisations_qi',
                actif: true
            }
        };

        const cheminLiens = path.join(this.config.maitre, 'orchestration', 'liens_composants.json');
        fs.writeFileSync(cheminLiens, JSON.stringify(liens, null, 2));
    }

    demarrerOrchestration() {
        console.log('\n🎼 DÉMARRAGE ORCHESTRATION');
        console.log('==========================');

        // Orchestration toutes les 20 secondes
        this.intervalOrchestration = setInterval(() => {
            this.executerCycleOrchestration();
        }, 20000);

        // Mode autonome après 1 minute
        setTimeout(() => {
            this.activerModeAutonome();
        }, 60000);

        console.log('✅ Orchestration démarrée (20s)');
    }

    executerCycleOrchestration() {
        console.log('\n🎼 CYCLE ORCHESTRATION');
        console.log('======================');

        try {
            // 1. Vérifier état composants
            this.verifierEtatComposants();

            // 2. Synchroniser données
            this.synchroniserDonnees();

            // 3. Optimiser performance
            this.optimiserPerformance();

            // 4. Mettre à jour métriques
            this.mettreAJourMetriquesGlobales();

            // 5. Sauvegarder état maître
            this.sauvegarderEtatMaitre();

        } catch (error) {
            console.log(`❌ Erreur cycle orchestration: ${error.message}`);
        }
    }

    verifierEtatComposants() {
        let composantsActifs = 0;

        if (this.composants.systeme_principal && this.composants.systeme_principal.etat.systeme_pret) {
            composantsActifs++;
            console.log('✅ Système principal actif');
        } else {
            console.log('⚠️ Système principal inactif');
        }

        if (this.composants.monitoring && this.composants.monitoring.etat_systeme.memoire_active) {
            composantsActifs++;
            console.log('✅ Monitoring actif');
        } else {
            console.log('⚠️ Monitoring inactif');
        }

        if (this.composants.auto_evolution && this.composants.auto_evolution.metriques_evolution.niveau_intelligence > 0) {
            composantsActifs++;
            console.log('✅ Auto-évolution active');
        } else {
            console.log('⚠️ Auto-évolution inactive');
        }

        this.etat_maitre.tous_composants_actifs = (composantsActifs === 3);
        console.log(`🎯 Composants actifs: ${composantsActifs}/3`);
    }

    synchroniserDonnees() {
        try {
            // Synchroniser QI entre évolution et système principal
            if (this.composants.auto_evolution && this.composants.systeme_principal) {
                const qiEvolution = this.composants.auto_evolution.metriques_evolution.niveau_intelligence;

                // Mettre à jour QI dans système principal (simulation)
                console.log(`🧠 Synchronisation QI: ${qiEvolution.toFixed(1)}`);
            }

            // Synchroniser métriques monitoring
            if (this.composants.monitoring && this.composants.systeme_principal) {
                const interactions = this.composants.systeme_principal.stats.interactions_totales;
                console.log(`📊 Synchronisation interactions: ${interactions}`);
            }

        } catch (error) {
            console.log(`⚠️ Erreur synchronisation données: ${error.message}`);
        }
    }

    optimiserPerformance() {
        try {
            let optimisations = 0;

            // Optimisation basée sur monitoring
            if (this.composants.monitoring) {
                const scoreSante = parseFloat(this.composants.monitoring.calculerScoreSante());

                if (scoreSante < 80) {
                    console.log('🔧 Optimisation performance nécessaire');
                    this.appliquerOptimisations();
                    optimisations++;
                }
            }

            // Optimisation basée sur évolution
            if (this.composants.auto_evolution) {
                const patterns = this.composants.auto_evolution.metriques_evolution.patterns_detectes;

                if (patterns > 10) {
                    console.log('🧬 Application patterns évolution');
                    optimisations++;
                }
            }

            if (optimisations > 0) {
                this.metriques_globales.problemes_resolus += optimisations;
                console.log(`✅ ${optimisations} optimisations appliquées`);
            }

        } catch (error) {
            console.log(`⚠️ Erreur optimisation: ${error.message}`);
        }
    }

    appliquerOptimisations() {
        // Simuler optimisations système
        console.log('🔧 Application optimisations système...');

        // Optimisation mémoire
        if (this.composants.systeme_principal) {
            console.log('   💾 Optimisation mémoire thermique');
        }

        // Optimisation curseur
        console.log('   🌡️ Optimisation curseur thermique');

        // Optimisation connexions
        console.log('   🔗 Optimisation connexions');
    }

    mettreAJourMetriquesGlobales() {
        this.metriques_globales.uptime_total = Date.now() - this.metriques_globales.temps_demarrage;

        // Agréger métriques composants
        if (this.composants.systeme_principal) {
            this.metriques_globales.interactions_totales = this.composants.systeme_principal.stats.interactions_totales;
        }

        if (this.composants.auto_evolution) {
            this.metriques_globales.qi_moyen = this.composants.auto_evolution.metriques_evolution.niveau_intelligence;
            this.metriques_globales.evolutions_detectees = this.composants.auto_evolution.metriques_evolution.patterns_detectes;
        }
    }

    sauvegarderEtatMaitre() {
        try {
            const etatMaitre = {
                timestamp: Date.now(),
                date: new Date().toISOString(),
                etat_maitre: this.etat_maitre,
                metriques_globales: this.metriques_globales,
                composants_status: {
                    systeme_principal: this.composants.systeme_principal ? 'ACTIF' : 'INACTIF',
                    monitoring: this.composants.monitoring ? 'ACTIF' : 'INACTIF',
                    auto_evolution: this.composants.auto_evolution ? 'ACTIF' : 'INACTIF'
                }
            };

            const cheminEtat = path.join(this.config.maitre, 'logs-maitre', 'etat_maitre.json');
            fs.writeFileSync(cheminEtat, JSON.stringify(etatMaitre, null, 2));

        } catch (error) {
            console.log(`⚠️ Erreur sauvegarde état maître: ${error.message}`);
        }
    }

    activerModeAutonome() {
        console.log('\n🤖 ACTIVATION MODE AUTONOME');
        console.log('===========================');

        if (this.etat_maitre.tous_composants_actifs) {
            this.etat_maitre.mode_autonome = true;
            console.log('✅ Mode autonome activé');
            console.log('🎯 Système fonctionne de manière autonome');

            // Démarrer interactions automatiques
            this.demarrerInteractionsAutomatiques();
        } else {
            console.log('⚠️ Mode autonome non disponible - composants manquants');
        }
    }

    demarrerInteractionsAutomatiques() {
        console.log('🔄 Démarrage interactions automatiques...');

        const questionsAuto = [
            "Comment puis-je améliorer ma mémoire?",
            "Quel est mon niveau d'intelligence actuel?",
            "Quels patterns ai-je détectés récemment?",
            "Comment optimiser mes performances?"
        ];

        // Interaction automatique toutes les 45 secondes
        this.intervalInteractionsAuto = setInterval(async () => {
            if (this.etat_maitre.mode_autonome && this.composants.systeme_principal) {
                const question = questionsAuto[Math.floor(Math.random() * questionsAuto.length)];

                console.log(`\n🤖 INTERACTION AUTONOME: "${question}"`);

                try {
                    const resultat = await this.composants.systeme_principal.interactionComplete(question);

                    if (resultat.succes) {
                        console.log('✅ Interaction autonome réussie');
                    }
                } catch (error) {
                    console.log(`⚠️ Erreur interaction autonome: ${error.message}`);
                }
            }
        }, 45000);

        console.log('✅ Interactions automatiques démarrées (45s)');
    }

    // INTERACTION MANUELLE AVEC SYSTÈME MAÎTRE
    async interactionMaitre(question) {
        console.log('\n👑 INTERACTION SYSTÈME MAÎTRE');
        console.log('=============================');
        console.log(`❓ Question: "${question}"`);

        if (!this.etat_maitre.initialise) {
            return {
                reponse: "Système maître non initialisé",
                succes: false
            };
        }

        try {
            // Utiliser système principal pour l'interaction
            const resultat = await this.composants.systeme_principal.interactionComplete(question);

            // Enrichir avec données maître
            if (resultat.succes) {
                resultat.qi_systeme = this.metriques_globales.qi_moyen;
                resultat.uptime_maitre = this.metriques_globales.uptime_total;
                resultat.mode_autonome = this.etat_maitre.mode_autonome;
            }

            return resultat;

        } catch (error) {
            console.log(`❌ Erreur interaction maître: ${error.message}`);
            return {
                reponse: "Erreur système maître",
                succes: false,
                erreur: error.message
            };
        }
    }

    afficherEtatMaitre() {
        console.log('\n👑 ÉTAT SYSTÈME MAÎTRE');
        console.log('======================');

        console.log(`🎯 Système initialisé: ${this.etat_maitre.initialise ? '✅' : '❌'}`);
        console.log(`🔧 Tous composants actifs: ${this.etat_maitre.tous_composants_actifs ? '✅' : '❌'}`);
        console.log(`🤖 Mode autonome: ${this.etat_maitre.mode_autonome ? '✅' : '❌'}`);

        console.log(`\n📊 MÉTRIQUES GLOBALES:`);
        console.log(`   Uptime: ${(this.metriques_globales.uptime_total / 1000 / 60).toFixed(1)} min`);
        console.log(`   Interactions totales: ${this.metriques_globales.interactions_totales}`);
        console.log(`   QI moyen: ${this.metriques_globales.qi_moyen.toFixed(1)}`);
        console.log(`   Évolutions détectées: ${this.metriques_globales.evolutions_detectees}`);
        console.log(`   Problèmes résolus: ${this.metriques_globales.problemes_resolus}`);

        console.log(`\n🔧 COMPOSANTS:`);
        console.log(`   Système principal: ${this.composants.systeme_principal ? '✅ ACTIF' : '❌ INACTIF'}`);
        console.log(`   Monitoring: ${this.composants.monitoring ? '✅ ACTIF' : '❌ INACTIF'}`);
        console.log(`   Auto-évolution: ${this.composants.auto_evolution ? '✅ ACTIF' : '❌ INACTIF'}`);

        if (this.etat_maitre.mode_autonome) {
            console.log('\n🎉 SYSTÈME MAÎTRE PLEINEMENT OPÉRATIONNEL');
        } else {
            console.log('\n⚠️ Système maître en cours d\'initialisation');
        }
    }

    arreterSystemeMaitre() {
        console.log('\n⏹️ Arrêt système maître...');

        // Arrêter orchestration
        if (this.intervalOrchestration) {
            clearInterval(this.intervalOrchestration);
            console.log('🎼 Orchestration arrêtée');
        }

        // Arrêter interactions automatiques
        if (this.intervalInteractionsAuto) {
            clearInterval(this.intervalInteractionsAuto);
            console.log('🤖 Interactions automatiques arrêtées');
        }

        // Arrêter composants
        if (this.composants.monitoring && this.composants.monitoring.arreterMonitoring) {
            this.composants.monitoring.arreterMonitoring();
        }

        if (this.composants.auto_evolution && this.composants.auto_evolution.arreterEvolution) {
            this.composants.auto_evolution.arreterEvolution();
        }

        // Sauvegarde finale
        this.sauvegarderEtatMaitre();

        console.log('✅ Système maître arrêté proprement');
    }
}

// Export
module.exports = SystemeMaitreIntegre;

// Test automatique si exécuté directement
if (require.main === module) {
    console.log('🚀 LANCEMENT SYSTÈME MAÎTRE INTÉGRÉ');
    console.log('===================================');

    const systemeMaitre = new SystemeMaitreIntegre();

    // Afficher état toutes les 30 secondes
    const intervalAffichage = setInterval(() => {
        systemeMaitre.afficherEtatMaitre();
    }, 30000);

    // Test interaction après 2 minutes
    setTimeout(async () => {
        console.log('\n🧪 TEST INTERACTION MAÎTRE');
        const resultat = await systemeMaitre.interactionMaitre("Quel est l'état de mon système unifié?");
        console.log(`📝 Résultat: ${resultat.succes ? 'SUCCÈS' : 'ÉCHEC'}`);
    }, 120000);

    // Arrêt automatique après 5 minutes
    setTimeout(() => {
        clearInterval(intervalAffichage);
        systemeMaitre.arreterSystemeMaitre();
        process.exit(0);
    }, 300000);

    // Gestion arrêt manuel
    process.on('SIGINT', () => {
        console.log('\n🛑 Arrêt manuel demandé...');
        clearInterval(intervalAffichage);
        systemeMaitre.arreterSystemeMaitre();
        process.exit(0);
    });
}
