/**
 * SYSTÈME COGNITIF AVANCÉ POUR LOUNA-AI
 * Filtrage intelligent, validation de pertinence, système de confiance
 */

class SystemeCognitifAvance {
    constructor() {
        this.seuilPertinence = 0.4; // Seuil minimum de pertinence
        this.seuilConfiance = 0.6; // Seuil minimum de confiance
        this.motsAbsurdes = new Set([
            'licorne', 'dragon', 'fée', 'magie', 'sorcier', 'fantôme',
            'vampire', 'zombie', 'alien', 'extraterrestre', 'martien',
            'superman', 'batman', 'spiderman', 'hulk', 'thor'
        ]);
        this.conceptsImpossibles = new Set([
            'voler dans l\'espace', 'manger des nuages', 'boire la lune',
            'compter l\'infini', 'diviser par zéro', 'voyager dans le temps'
        ]);
        this.historiqueFiltrage = [];
    }

    // FILTRAGE PRINCIPAL DE PERTINENCE
    filtrerQuestion(question) {
        const analyse = this.analyserQuestion(question);
        
        this.historiqueFiltrage.push({
            question: question,
            analyse: analyse,
            timestamp: Date.now()
        });

        console.log(`🧠 Analyse cognitive: ${JSON.stringify(analyse)}`);

        // Décision de filtrage
        if (analyse.estAbsurde) {
            return {
                valide: false,
                raison: 'Question absurde détectée',
                confiance: 0,
                reponseDefaut: "Je ne trouve pas d'information pertinente pour répondre à cette question."
            };
        }

        if (analyse.pertinence < this.seuilPertinence) {
            return {
                valide: false,
                raison: 'Pertinence insuffisante',
                confiance: analyse.pertinence,
                reponseDefaut: "Cette question ne semble pas avoir de réponse factuelle."
            };
        }

        return {
            valide: true,
            pertinence: analyse.pertinence,
            confiance: analyse.confiance,
            domaine: analyse.domaine,
            complexite: analyse.complexite
        };
    }

    // ANALYSE COMPLÈTE DE LA QUESTION
    analyserQuestion(question) {
        const questionNormalisee = question.toLowerCase().trim();
        
        return {
            estAbsurde: this.detecterAbsurdite(questionNormalisee),
            pertinence: this.calculerPertinence(questionNormalisee),
            confiance: this.calculerConfiance(questionNormalisee),
            domaine: this.identifierDomaine(questionNormalisee),
            complexite: this.evaluerComplexite(questionNormalisee),
            typeQuestion: this.classifierTypeQuestion(questionNormalisee)
        };
    }

    // DÉTECTION D'ABSURDITÉ
    detecterAbsurdite(question) {
        // Vérifier mots absurdes
        for (const mot of this.motsAbsurdes) {
            if (question.includes(mot)) {
                console.log(`🚫 Mot absurde détecté: ${mot}`);
                return true;
            }
        }

        // Vérifier concepts impossibles
        for (const concept of this.conceptsImpossibles) {
            if (question.includes(concept)) {
                console.log(`🚫 Concept impossible détecté: ${concept}`);
                return true;
            }
        }

        // Vérifier patterns absurdes
        const patternsAbsurdes = [
            /combien de .* mangent .*/,
            /pourquoi les .* volent dans .*/,
            /comment .* transforment .* en .*/,
            /où vivent les .* magiques/,
            /quand les .* parlent avec .*/
        ];

        for (const pattern of patternsAbsurdes) {
            if (pattern.test(question)) {
                console.log(`🚫 Pattern absurde détecté: ${pattern}`);
                return true;
            }
        }

        return false;
    }

    // CALCUL DE PERTINENCE
    calculerPertinence(question) {
        let score = 0.5; // Base

        // Mots-clés scientifiques/techniques (+)
        const motsClesPositifs = [
            'calcul', 'formule', 'équation', 'définition', 'explication',
            'comment', 'pourquoi', 'qu\'est-ce', 'diagnostic', 'symptôme',
            'traitement', 'physique', 'chimie', 'mathématiques', 'médecine'
        ];

        for (const mot of motsClesPositifs) {
            if (question.includes(mot)) {
                score += 0.1;
            }
        }

        // Questions d'identité (+)
        if (question.includes('qui es-tu') || question.includes('ton nom')) {
            score += 0.3;
        }

        // Longueur appropriée
        if (question.length > 10 && question.length < 200) {
            score += 0.1;
        }

        // Présence de point d'interrogation
        if (question.includes('?')) {
            score += 0.1;
        }

        return Math.min(1.0, score);
    }

    // CALCUL DE CONFIANCE
    calculerConfiance(question) {
        let confiance = 0.5;

        // Question claire et structurée
        if (question.includes('?') && question.length > 5) {
            confiance += 0.2;
        }

        // Domaine identifiable
        const domaine = this.identifierDomaine(question);
        if (domaine !== 'inconnu') {
            confiance += 0.2;
        }

        // Pas de fautes évidentes
        if (!question.includes('???') && !question.includes('!!!')) {
            confiance += 0.1;
        }

        return Math.min(1.0, confiance);
    }

    // IDENTIFICATION DU DOMAINE
    identifierDomaine(question) {
        const domaines = {
            'mathematiques': ['calcul', 'équation', 'formule', 'nombre', '+', '-', '*', '×', '÷', '/', 'intégrale'],
            'medecine': ['symptôme', 'diagnostic', 'traitement', 'maladie', 'patient', 'dyspnée', 'œdème'],
            'physique': ['énergie', 'force', 'vitesse', 'température', 'pression', 'cinétique'],
            'chimie': ['réaction', 'équilibre', 'molécule', 'atome', 'CH4', 'CO2', 'H2O'],
            'identite': ['qui es-tu', 'ton nom', 'créateur', 'qui t\'a créé'],
            'general': ['comment', 'pourquoi', 'qu\'est-ce', 'définition', 'explication']
        };

        for (const [domaine, motsCles] of Object.entries(domaines)) {
            for (const mot of motsCles) {
                if (question.includes(mot)) {
                    return domaine;
                }
            }
        }

        return 'inconnu';
    }

    // ÉVALUATION DE LA COMPLEXITÉ
    evaluerComplexite(question) {
        let complexite = 1;

        // Longueur
        if (question.length > 50) complexite++;
        if (question.length > 100) complexite++;

        // Mots complexes
        const motsComplexes = ['physiopathologie', 'différentiel', 'intégrale', 'thermodynamique'];
        for (const mot of motsComplexes) {
            if (question.includes(mot)) complexite++;
        }

        // Questions multiples
        if ((question.match(/\?/g) || []).length > 1) complexite++;

        return Math.min(5, complexite);
    }

    // CLASSIFICATION TYPE DE QUESTION
    classifierTypeQuestion(question) {
        if (question.includes('calcul') || /\d+.*[+\-*/×÷].*\d+/.test(question)) {
            return 'calcul';
        }
        if (question.includes('qui es-tu') || question.includes('ton nom')) {
            return 'identite';
        }
        if (question.includes('symptôme') || question.includes('diagnostic')) {
            return 'medical';
        }
        if (question.includes('comment') || question.includes('explication')) {
            return 'explication';
        }
        if (question.includes('définition') || question.includes('qu\'est-ce')) {
            return 'definition';
        }
        return 'general';
    }

    // VALIDATION FINALE AVANT RÉPONSE
    validerReponse(question, reponse, source) {
        const coherence = this.evaluerCoherence(question, reponse);
        
        if (coherence < 0.3) {
            return {
                valide: false,
                raison: 'Réponse incohérente',
                reponseCorrigee: "Je ne peux pas fournir une réponse fiable à cette question."
            };
        }

        return {
            valide: true,
            coherence: coherence,
            source: source
        };
    }

    // ÉVALUATION COHÉRENCE QUESTION-RÉPONSE
    evaluerCoherence(question, reponse) {
        const motsQuestion = new Set(question.toLowerCase().split(/\s+/));
        const motsReponse = new Set(reponse.toLowerCase().split(/\s+/));
        
        // Intersection des mots significatifs
        const motsCommuns = [...motsQuestion].filter(mot => 
            motsReponse.has(mot) && mot.length > 3
        );

        const coherence = motsCommuns.length / Math.max(motsQuestion.size, 1);
        
        // Bonus pour réponses appropriées
        if (reponse.includes("Je ne trouve pas") && this.detecterAbsurdite(question)) {
            return 0.9; // Bonne réponse pour question absurde
        }

        return coherence;
    }

    // STATISTIQUES DU SYSTÈME COGNITIF
    obtenirStatistiques() {
        const total = this.historiqueFiltrage.length;
        const valides = this.historiqueFiltrage.filter(h => !h.analyse.estAbsurde).length;
        const absurdes = total - valides;

        return {
            total_questions: total,
            questions_valides: valides,
            questions_absurdes: absurdes,
            taux_filtrage: total > 0 ? (absurdes / total * 100).toFixed(1) + '%' : '0%',
            seuil_pertinence: this.seuilPertinence,
            seuil_confiance: this.seuilConfiance
        };
    }

    // AJUSTEMENT DYNAMIQUE DES SEUILS
    ajusterSeuils(performance) {
        if (performance.precision > 0.9) {
            this.seuilPertinence = Math.max(0.3, this.seuilPertinence - 0.05);
        } else if (performance.precision < 0.7) {
            this.seuilPertinence = Math.min(0.6, this.seuilPertinence + 0.05);
        }

        console.log(`🎯 Seuils ajustés: pertinence=${this.seuilPertinence}, confiance=${this.seuilConfiance}`);
    }
}

module.exports = SystemeCognitifAvance;
