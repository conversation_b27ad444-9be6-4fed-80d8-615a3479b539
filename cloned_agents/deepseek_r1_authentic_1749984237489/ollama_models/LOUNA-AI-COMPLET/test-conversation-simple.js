const http = require('http');

// Test simple de conversation
const testData = JSON.stringify({
    message: "<PERSON><PERSON><PERSON>, comment allez-vous ?"
});

const options = {
    hostname: 'localhost',
    port: 3001,
    path: '/conversation',
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(testData)
    }
};

console.log('🧪 TEST CONVERSATION RÉEL AVEC LOUNA-AI');
console.log('========================================');
console.log('📤 Envoi: "Bonjour, comment allez-vous ?"');

const req = http.request(options, (res) => {
    console.log(`📊 Status: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
        data += chunk;
    });
    
    res.on('end', () => {
        try {
            const response = JSON.parse(data);
            console.log('📥 RÉPONSE REÇUE:');
            console.log('================');
            console.log(`✅ Success: ${response.success}`);
            console.log(`💬 Réponse: ${response.response || response.message}`);
            console.log(`🧠 QI: ${response.etat?.qi_actuel || 'N/A'}`);
            console.log(`🌡️ Température: ${response.etat?.temperature || 'N/A'}°C`);
            console.log(`💾 Mémoires: ${response.etat?.memoires || 'N/A'}`);
            
            if (response.success && response.response) {
                console.log('\n🎉 TEST RÉUSSI - L\'AGENT RÉPOND CORRECTEMENT !');
            } else {
                console.log('\n❌ TEST ÉCHOUÉ - Pas de réponse valide');
            }
        } catch (error) {
            console.log('❌ Erreur parsing JSON:', error.message);
            console.log('📄 Données brutes:', data);
        }
    });
});

req.on('error', (error) => {
    console.log('❌ Erreur requête:', error.message);
});

req.write(testData);
req.end();
