// CORRECTION RAPIDE QI INTERFACE
// Ajouter au JavaScript de l'interface

// Fonction pour mettre à jour QI depuis serveur
async function updateQIFromServer() {
    try {
        const response = await fetch("/stats");
        const data = await response.json();
        
        if (data.success && data.coefficient_intellectuel) {
            const currentQI = data.coefficient_intellectuel;
            updateDisplayWithAnimation("coefficientQI", currentQI);
            console.log(`🧠 QI mis à jour: ${currentQI}`);
        }
    } catch (error) {
        console.error("Erreur mise à jour QI:", error);
    }
}

// Appeler au démarrage et toutes les 5 secondes
updateQIFromServer();
setInterval(updateQIFromServer, 5000);
