/**
 * GESTIONNAIRE COMPLET DU BUREAU ET APPLICATIONS POUR LOUNA-AI
 * Contrôle total du bureau, applications et automatisation des tâches
 */

const { exec, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

class GestionnaireBureauComplet {
    constructor() {
        // Base de données des applications connues (étendue)
        this.applicationsConnues = new Map([
            // Applications de codage
            ['vscode', { nom: 'Visual Studio Code', commande: 'code', type: 'codage', raccourci: 'cmd+space code' }],
            ['xcode', { nom: 'Xcode', commande: 'open -a Xcode', type: 'codage', raccourci: 'cmd+space xcode' }],
            ['sublime', { nom: 'Sublime Text', commande: 'subl', type: 'codage', raccourci: 'cmd+space sublime' }],
            ['webstorm', { nom: 'WebStorm', commande: 'open -a WebStorm', type: 'codage', raccourci: 'cmd+space webstorm' }],
            ['pycharm', { nom: 'PyCharm', commande: 'open -a PyCharm', type: 'codage', raccourci: 'cmd+space pycharm' }],
            
            // Applications système
            ['terminal', { nom: 'Terminal', commande: 'open -a Terminal', type: 'système', raccourci: 'cmd+space terminal' }],
            ['finder', { nom: 'Finder', commande: 'open -a Finder', type: 'système', raccourci: 'cmd+space finder' }],
            ['safari', { nom: 'Safari', commande: 'open -a Safari', type: 'navigateur', raccourci: 'cmd+space safari' }],
            ['chrome', { nom: 'Google Chrome', commande: 'open -a "Google Chrome"', type: 'navigateur', raccourci: 'cmd+space chrome' }],
            ['firefox', { nom: 'Firefox', commande: 'open -a Firefox', type: 'navigateur', raccourci: 'cmd+space firefox' }],
            
            // Applications créatives
            ['photoshop', { nom: 'Adobe Photoshop', commande: 'open -a "Adobe Photoshop 2024"', type: 'créatif', raccourci: 'cmd+space photoshop' }],
            ['illustrator', { nom: 'Adobe Illustrator', commande: 'open -a "Adobe Illustrator 2024"', type: 'créatif', raccourci: 'cmd+space illustrator' }],
            ['figma', { nom: 'Figma', commande: 'open -a Figma', type: 'créatif', raccourci: 'cmd+space figma' }],
            
            // Applications bureautique
            ['word', { nom: 'Microsoft Word', commande: 'open -a "Microsoft Word"', type: 'bureautique', raccourci: 'cmd+space word' }],
            ['excel', { nom: 'Microsoft Excel', commande: 'open -a "Microsoft Excel"', type: 'bureautique', raccourci: 'cmd+space excel' }],
            ['powerpoint', { nom: 'Microsoft PowerPoint', commande: 'open -a "Microsoft PowerPoint"', type: 'bureautique', raccourci: 'cmd+space powerpoint' }],
            
            // Applications spécialisées
            ['docker', { nom: 'Docker Desktop', commande: 'open -a "Docker Desktop"', type: 'développement', raccourci: 'cmd+space docker' }],
            ['postman', { nom: 'Postman', commande: 'open -a Postman', type: 'développement', raccourci: 'cmd+space postman' }],
            ['github', { nom: 'GitHub Desktop', commande: 'open -a "GitHub Desktop"', type: 'développement', raccourci: 'cmd+space github' }]
        ]);
        
        this.historique = [];
        this.processusLances = new Map();
        this.raccourcisPersonnalises = new Map();
        this.automationsActives = new Map();
    }

    // OUVRIR UNE APPLICATION AVEC CONTRÔLE AVANCÉ
    async ouvrirApplication(nomApp, parametres = null, options = {}) {
        try {
            const appKey = nomApp.toLowerCase().replace(/\s+/g, '');
            console.log(`🚀 Ouverture avancée: ${nomApp}`);
            
            let application = this.applicationsConnues.get(appKey);
            
            // Si l'application n'est pas connue, essayer de la découvrir
            if (!application) {
                application = await this.decouvririApplication(nomApp);
            }
            
            if (!application) {
                return {
                    success: false,
                    message: `Application "${nomApp}" non trouvée`,
                    suggestions: await this.suggererAlternatives(nomApp)
                };
            }
            
            // Construire la commande avec options avancées
            let commande = application.commande;
            
            // Ajouter paramètres si fournis
            if (parametres) {
                commande += ` ${parametres}`;
            }
            
            // Options avancées
            if (options.nouveauProcessus) {
                commande = `nohup ${commande} > /dev/null 2>&1 &`;
            }
            
            if (options.priorite) {
                commande = `nice -n ${options.priorite} ${commande}`;
            }
            
            console.log(`⚡ Exécution: ${commande}`);
            
            // Exécuter avec surveillance du processus
            const resultat = await this.executerAvecSurveillance(commande, application);
            
            // Enregistrer dans l'historique
            this.historique.push({
                action: 'ouverture_application',
                application: application.nom,
                commande: commande,
                options: options,
                succes: resultat.success,
                timestamp: Date.now(),
                pid: resultat.pid
            });
            
            if (resultat.success) {
                // Enregistrer le processus lancé
                if (resultat.pid) {
                    this.processusLances.set(application.nom, {
                        pid: resultat.pid,
                        commande: commande,
                        timestamp: Date.now()
                    });
                }
                
                return {
                    success: true,
                    application: application.nom,
                    type: application.type,
                    description: application.description,
                    message: `✅ ${application.nom} ouvert avec succès !`,
                    pid: resultat.pid,
                    commande_executee: commande
                };
            } else {
                return {
                    success: false,
                    message: `❌ Erreur lors de l'ouverture de ${application.nom}: ${resultat.error}`,
                    suggestion: "Vérifiez que l'application est installée."
                };
            }
            
        } catch (error) {
            console.error(`❌ Erreur ouverture application: ${error.message}`);
            return {
                success: false,
                error: error.message,
                message: `Erreur lors de l'ouverture de ${nomApp}: ${error.message}`
            };
        }
    }

    // FERMER UNE APPLICATION
    async fermerApplication(nomApp, force = false) {
        try {
            console.log(`🔴 Fermeture: ${nomApp}`);
            
            const processus = this.processusLances.get(nomApp);
            
            if (processus) {
                // Fermer par PID
                const signal = force ? 'SIGKILL' : 'SIGTERM';
                const commande = `kill -${signal} ${processus.pid}`;
                
                const resultat = await this.executerCommande(commande);
                
                if (resultat.success) {
                    this.processusLances.delete(nomApp);
                    console.log(`✅ ${nomApp} fermé (PID: ${processus.pid})`);
                    
                    return {
                        success: true,
                        message: `${nomApp} fermé avec succès`,
                        pid: processus.pid
                    };
                }
            }
            
            // Fermer par nom d'application (macOS)
            const commande = force ? 
                `pkill -f "${nomApp}"` : 
                `osascript -e 'quit app "${nomApp}"'`;
            
            const resultat = await this.executerCommande(commande);
            
            return {
                success: resultat.success,
                message: resultat.success ? 
                    `${nomApp} fermé avec succès` : 
                    `Erreur lors de la fermeture de ${nomApp}`,
                methode: force ? 'force' : 'normal'
            };
            
        } catch (error) {
            return {
                success: false,
                error: error.message,
                message: `Erreur lors de la fermeture de ${nomApp}: ${error.message}`
            };
        }
    }

    // NAVIGUER DANS LES MENUS (macOS)
    async naviguerMenu(application, chemin_menu) {
        try {
            console.log(`📋 Navigation menu: ${application} -> ${chemin_menu}`);
            
            // Utiliser AppleScript pour naviguer dans les menus
            const script = `
                tell application "${application}"
                    activate
                    tell application "System Events"
                        tell process "${application}"
                            ${this.genererScriptMenu(chemin_menu)}
                        end tell
                    end tell
                end tell
            `;
            
            const commande = `osascript -e '${script}'`;
            const resultat = await this.executerCommande(commande);
            
            return {
                success: resultat.success,
                message: resultat.success ? 
                    `Menu navigué: ${chemin_menu}` : 
                    `Erreur navigation menu: ${resultat.error}`,
                application: application,
                chemin: chemin_menu
            };
            
        } catch (error) {
            return {
                success: false,
                error: error.message,
                message: `Erreur navigation menu: ${error.message}`
            };
        }
    }

    // UTILISER RACCOURCIS CLAVIER
    async utiliserRaccourci(raccourci, application = null) {
        try {
            console.log(`⌨️ Raccourci: ${raccourci}`);
            
            let script = '';
            
            if (application) {
                script = `
                    tell application "${application}"
                        activate
                        tell application "System Events"
                            keystroke "${this.convertirRaccourci(raccourci)}"
                        end tell
                    end tell
                `;
            } else {
                script = `
                    tell application "System Events"
                        keystroke "${this.convertirRaccourci(raccourci)}"
                    end tell
                `;
            }
            
            const commande = `osascript -e '${script}'`;
            const resultat = await this.executerCommande(commande);
            
            return {
                success: resultat.success,
                message: resultat.success ? 
                    `Raccourci exécuté: ${raccourci}` : 
                    `Erreur raccourci: ${resultat.error}`,
                raccourci: raccourci,
                application: application
            };
            
        } catch (error) {
            return {
                success: false,
                error: error.message,
                message: `Erreur raccourci: ${error.message}`
            };
        }
    }

    // AUTOMATISER UNE TÂCHE
    async automatiserTache(nom_tache, etapes, options = {}) {
        try {
            console.log(`🤖 Automatisation: ${nom_tache}`);
            
            const automation = {
                nom: nom_tache,
                etapes: etapes,
                options: options,
                timestamp: Date.now(),
                executions: 0,
                derniere_execution: null
            };
            
            // Enregistrer l'automation
            this.automationsActives.set(nom_tache, automation);
            
            // Exécuter si demandé
            if (options.executer_maintenant) {
                return await this.executerAutomation(nom_tache);
            }
            
            // Programmer si récurrent
            if (options.recurrent && options.intervalle) {
                this.programmerAutomation(nom_tache, options.intervalle);
            }
            
            return {
                success: true,
                message: `Automation "${nom_tache}" créée avec succès`,
                automation: automation
            };
            
        } catch (error) {
            return {
                success: false,
                error: error.message,
                message: `Erreur création automation: ${error.message}`
            };
        }
    }

    // EXÉCUTER UNE AUTOMATION
    async executerAutomation(nom_tache) {
        try {
            const automation = this.automationsActives.get(nom_tache);
            
            if (!automation) {
                return {
                    success: false,
                    message: `Automation "${nom_tache}" non trouvée`
                };
            }
            
            console.log(`🤖 Exécution automation: ${nom_tache}`);
            
            const resultats = [];
            
            for (const etape of automation.etapes) {
                const resultat = await this.executerEtapeAutomation(etape);
                resultats.push(resultat);
                
                if (!resultat.success && automation.options.arreter_si_erreur) {
                    break;
                }
                
                // Pause entre étapes si spécifiée
                if (automation.options.pause_entre_etapes) {
                    await this.attendre(automation.options.pause_entre_etapes);
                }
            }
            
            // Mettre à jour les statistiques
            automation.executions++;
            automation.derniere_execution = Date.now();
            
            const succes = resultats.every(r => r.success);
            
            return {
                success: succes,
                message: succes ? 
                    `Automation "${nom_tache}" exécutée avec succès` : 
                    `Automation "${nom_tache}" exécutée avec erreurs`,
                resultats: resultats,
                executions_totales: automation.executions
            };
            
        } catch (error) {
            return {
                success: false,
                error: error.message,
                message: `Erreur exécution automation: ${error.message}`
            };
        }
    }

    // UTILITAIRES
    async decouvririApplication(nomApp) {
        // Rechercher l'application dans le système
        const commande = `find /Applications -name "*${nomApp}*" -type d -maxdepth 2 2>/dev/null | head -1`;
        const resultat = await this.executerCommande(commande);
        
        if (resultat.success && resultat.sortie.trim()) {
            const cheminApp = resultat.sortie.trim();
            const nomApplication = path.basename(cheminApp, '.app');
            
            const nouvelleApp = {
                nom: nomApplication,
                commande: `open -a "${nomApplication}"`,
                type: 'découvert',
                description: `Application découverte: ${nomApplication}`,
                raccourci: `cmd+space ${nomApplication.toLowerCase()}`
            };
            
            // Ajouter à la base de données
            this.applicationsConnues.set(nomApp.toLowerCase(), nouvelleApp);
            
            return nouvelleApp;
        }
        
        return null;
    }

    async executerAvecSurveillance(commande, application) {
        return new Promise((resolve) => {
            const processus = spawn('sh', ['-c', commande], {
                detached: true,
                stdio: 'ignore'
            });
            
            processus.unref();
            
            // Attendre un peu pour vérifier si le processus démarre
            setTimeout(() => {
                resolve({
                    success: true,
                    pid: processus.pid,
                    application: application.nom
                });
            }, 1000);
            
            processus.on('error', (error) => {
                resolve({
                    success: false,
                    error: error.message
                });
            });
        });
    }

    genererScriptMenu(chemin_menu) {
        const elements = chemin_menu.split(' -> ');
        let script = '';
        
        for (let i = 0; i < elements.length; i++) {
            if (i === 0) {
                script += `click menu bar item "${elements[i]}" of menu bar 1\n`;
            } else {
                script += `click menu item "${elements[i]}" of menu "${elements[i-1]}" of menu bar item "${elements[0]}" of menu bar 1\n`;
            }
        }
        
        return script;
    }

    convertirRaccourci(raccourci) {
        // Convertir les raccourcis en format AppleScript
        return raccourci
            .replace(/cmd\+/g, 'command down ')
            .replace(/ctrl\+/g, 'control down ')
            .replace(/alt\+/g, 'option down ')
            .replace(/shift\+/g, 'shift down ');
    }

    async executerEtapeAutomation(etape) {
        switch (etape.type) {
            case 'ouvrir_app':
                return await this.ouvrirApplication(etape.application, etape.parametres);
            case 'fermer_app':
                return await this.fermerApplication(etape.application, etape.force);
            case 'raccourci':
                return await this.utiliserRaccourci(etape.raccourci, etape.application);
            case 'menu':
                return await this.naviguerMenu(etape.application, etape.chemin);
            case 'attendre':
                await this.attendre(etape.duree);
                return { success: true, message: `Attente de ${etape.duree}ms` };
            default:
                return { success: false, message: `Type d'étape inconnu: ${etape.type}` };
        }
    }

    async attendre(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async executerCommande(commande) {
        return new Promise((resolve) => {
            exec(commande, { timeout: 10000 }, (error, stdout, stderr) => {
                resolve({
                    success: !error,
                    error: error?.message,
                    sortie: stdout,
                    erreur: stderr
                });
            });
        });
    }

    // STATISTIQUES
    obtenirStatistiques() {
        return {
            applications_connues: this.applicationsConnues.size,
            processus_lances: this.processusLances.size,
            automations_actives: this.automationsActives.size,
            actions_historique: this.historique.length,
            raccourcis_personnalises: this.raccourcisPersonnalises.size
        };
    }
}

module.exports = GestionnaireBureauComplet;
