# 🎬 GUIDE D'INSTALLATION FFMPEG POUR LOUNA-AI

## 📋 **POURQUOI FFMPEG ?**

FFmpeg est nécessaire pour que LOUNA-AI puisse :
- ✅ **Générer de vraies vidéos** au lieu de simulations
- ✅ **Créer des vidéos de démonstration** avec effets visuels
- ✅ **Convertir et traiter** les fichiers vidéo
- ✅ **Optimiser les performances** du générateur vidéo LTX

---

## 🖥️ **INSTALLATION SUR macOS**

### **Méthode 1 : Homebrew (Recommandée)**

```bash
# 1. Installer Homebrew si pas déjà fait
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 2. Installer FFmpeg
brew install ffmpeg

# 3. Vérifier l'installation
ffmpeg -version
```

### **Méthode 2 : MacPorts**

```bash
# 1. Installer MacPorts depuis https://www.macports.org/install.php
# 2. Installer FFmpeg
sudo port install ffmpeg

# 3. Vérifier l'installation
ffmpeg -version
```

### **Méthode 3 : Téléchargement direct**

1. Aller sur https://ffmpeg.org/download.html
2. Télécharger la version macOS
3. Extraire dans `/usr/local/bin/`
4. Ajouter au PATH dans `~/.zshrc` ou `~/.bash_profile`

---

## 🐧 **INSTALLATION SUR LINUX**

### **Ubuntu/Debian :**
```bash
sudo apt update
sudo apt install ffmpeg
```

### **CentOS/RHEL/Fedora :**
```bash
# Fedora
sudo dnf install ffmpeg

# CentOS/RHEL (avec EPEL)
sudo yum install epel-release
sudo yum install ffmpeg
```

### **Arch Linux :**
```bash
sudo pacman -S ffmpeg
```

---

## 🪟 **INSTALLATION SUR WINDOWS**

### **Méthode 1 : Chocolatey**
```powershell
# Installer Chocolatey puis FFmpeg
choco install ffmpeg
```

### **Méthode 2 : Téléchargement direct**
1. Aller sur https://ffmpeg.org/download.html#build-windows
2. Télécharger la version Windows
3. Extraire dans `C:\ffmpeg\`
4. Ajouter `C:\ffmpeg\bin\` au PATH système

---

## ✅ **VÉRIFICATION DE L'INSTALLATION**

Après installation, testez avec :

```bash
# Vérifier la version
ffmpeg -version

# Tester une conversion simple
ffmpeg -f lavfi -i testsrc=duration=1:size=320x240:rate=1 test.mp4
```

Si ces commandes fonctionnent, FFmpeg est correctement installé !

---

## 🔧 **CONFIGURATION POUR LOUNA-AI**

Une fois FFmpeg installé :

1. **Redémarrer le serveur LOUNA-AI**
2. **Vérifier les logs** : vous devriez voir "✅ FFmpeg détecté" au lieu de "⚠️ LTX-Video non installé"
3. **Tester la génération vidéo** depuis l'interface

---

## 🎯 **FONCTIONNALITÉS ACTIVÉES AVEC FFMPEG**

### **Sans FFmpeg (Mode simulation) :**
- ⚠️ Génération de fichiers de simulation
- ⚠️ Pas de vraies vidéos
- ⚠️ Fonctionnalités limitées

### **Avec FFmpeg (Mode complet) :**
- ✅ **Génération de vraies vidéos MP4**
- ✅ **Effets visuels et transitions**
- ✅ **Conversion de formats**
- ✅ **Optimisation automatique**
- ✅ **Prévisualisation en temps réel**
- ✅ **Export haute qualité**

---

## 🚨 **DÉPANNAGE**

### **Problème : "ffmpeg: command not found"**
**Solution :** FFmpeg n'est pas dans le PATH
```bash
# Trouver FFmpeg
which ffmpeg
whereis ffmpeg

# Ajouter au PATH (exemple pour macOS/Linux)
echo 'export PATH="/usr/local/bin:$PATH"' >> ~/.zshrc
source ~/.zshrc
```

### **Problème : "Permission denied"**
**Solution :** Problème de permissions
```bash
# Donner les permissions d'exécution
chmod +x /usr/local/bin/ffmpeg

# Ou installer avec sudo
sudo brew install ffmpeg
```

### **Problème : "No such file or directory"**
**Solution :** Installation incomplète
```bash
# Réinstaller FFmpeg
brew uninstall ffmpeg
brew install ffmpeg
```

---

## 📊 **OPTIMISATIONS RECOMMANDÉES**

### **Pour de meilleures performances :**

```bash
# Installer avec support GPU (si disponible)
brew install ffmpeg --with-libvpx --with-libx264 --with-libx265

# Vérifier les codecs disponibles
ffmpeg -codecs | grep -E "(h264|h265|vp9)"
```

### **Configuration avancée :**
- **CPU multi-core** : FFmpeg utilisera automatiquement tous les cœurs
- **GPU acceleration** : Support NVIDIA NVENC et AMD VCE
- **Formats optimisés** : H.264, H.265, VP9 pour la compression

---

## 🎬 **APRÈS INSTALLATION**

1. **Redémarrer LOUNA-AI**
2. **Aller dans l'interface générateur vidéo**
3. **Tester une génération simple**
4. **Vérifier que les vidéos sont créées dans le dossier `videos-ltx/`**

---

## 📞 **SUPPORT**

Si vous rencontrez des problèmes :

1. **Vérifiez les logs** du serveur LOUNA-AI
2. **Testez FFmpeg** en ligne de commande
3. **Consultez la documentation** FFmpeg officielle
4. **Redémarrez** le système si nécessaire

**FFmpeg est maintenant prêt pour LOUNA-AI ! 🚀**
