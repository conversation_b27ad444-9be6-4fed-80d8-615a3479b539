/**
 * TEST CORRECTION FINALE - PROBLÈME ITALIE/GUADELOUPE
 * Vérification que le moteur de raisonnement corrigé fonctionne
 */

const MoteurRaisonnementReel = require('./moteur-raisonnement-reel.js');

function testerCorrectionFinale() {
    console.log('🚨 TEST CORRECTION FINALE - PROBLÈME RÉSOLU');
    console.log('============================================');
    console.log('🎯 Test exact du problème signalé par l\'utilisateur\n');

    const moteur = new MoteurRaisonnementReel();

    console.log('📋 SÉQUENCE EXACTE REPRODUITE:');
    console.log('1. "quelle est la capitale de l\'Italie ?"');
    console.log('2. "et la Guadeloupe ?"');
    console.log('');

    // Question 1 - Italie
    console.log('❓ Question 1: "quelle est la capitale de l\'Italie ?"');
    const resultat1 = moteur.penser("quelle est la capitale de l'Italie ?");
    
    if (resultat1 && resultat1.reponse) {
        console.log(`🤖 Réponse 1: ${resultat1.reponse}`);
        console.log(`🎯 Source: ${resultat1.source}`);
        
        const contientRome = resultat1.reponse.toLowerCase().includes('rome');
        console.log(`✅ Contient "Rome": ${contientRome ? 'OUI' : 'NON'}`);
    } else {
        console.log('❌ Aucune réponse pour l\'Italie');
    }
    
    console.log('');

    // Question 2 - Guadeloupe (LE PROBLÈME)
    console.log('❓ Question 2: "et la Guadeloupe ?"');
    const resultat2 = moteur.penser("et la Guadeloupe ?");
    
    if (resultat2 && resultat2.reponse) {
        console.log(`🤖 Réponse 2: ${resultat2.reponse}`);
        console.log(`🎯 Source: ${resultat2.source}`);
        
        const contientBasseTerre = resultat2.reponse.toLowerCase().includes('basse-terre');
        const contientItalie = resultat2.reponse.toLowerCase().includes('italie');
        
        console.log(`✅ Contient "Basse-Terre": ${contientBasseTerre ? 'OUI' : 'NON'}`);
        console.log(`❌ Contient "Italie" (erreur): ${contientItalie ? 'OUI' : 'NON'}`);
        
        console.log('');
        console.log('🎯 ÉVALUATION FINALE:');
        
        if (contientBasseTerre && !contientItalie) {
            console.log('✅ PROBLÈME RÉSOLU ! Répond correctement "Basse-Terre"');
            console.log('🎉 Le suivi conversationnel fonctionne parfaitement !');
            console.log('🚀 L\'agent peut maintenant suivre les conversations !');
            return true;
        } else if (contientItalie) {
            console.log('❌ PROBLÈME PERSISTE ! Répond encore "Italie"');
            console.log('🔧 Le suivi conversationnel nécessite encore des corrections');
            return false;
        } else {
            console.log('⚠️ RÉPONSE INATTENDUE - À analyser');
            console.log('🔍 Vérifier la logique de traitement');
            return false;
        }
    } else {
        console.log('❌ Aucune réponse pour la Guadeloupe');
        return false;
    }
}

function testerAutresScenarios() {
    console.log('\n🧪 TESTS SUPPLÉMENTAIRES');
    console.log('=========================');

    const moteur = new MoteurRaisonnementReel();

    const scenarios = [
        {
            nom: "France → Martinique",
            questions: ["quelle est la capitale de la France ?", "et la Martinique ?"],
            attendu: ["Paris", "Fort-de-France"]
        },
        {
            nom: "Espagne → Portugal",
            questions: ["capitale de l'Espagne ?", "et le Portugal ?"],
            attendu: ["Madrid", "Lisbonne"]
        },
        {
            nom: "Allemagne → Suisse",
            questions: ["quelle est la capitale de l'Allemagne ?", "et pour la Suisse ?"],
            attendu: ["Berlin", "Berne"]
        }
    ];

    let testsReussis = 0;
    let testsTotal = scenarios.length;

    scenarios.forEach((scenario, index) => {
        console.log(`\n🔍 Scénario ${index + 1}: ${scenario.nom}`);
        
        // Créer un nouveau moteur pour chaque scénario
        const moteurTest = new MoteurRaisonnementReel();
        let scenarioReussi = true;

        scenario.questions.forEach((question, qIndex) => {
            console.log(`❓ "${question}"`);
            const resultat = moteurTest.penser(question);
            
            if (resultat && resultat.reponse) {
                console.log(`🤖 ${resultat.reponse}`);
                
                const contientAttendu = resultat.reponse.toLowerCase()
                    .includes(scenario.attendu[qIndex].toLowerCase());
                console.log(`${contientAttendu ? '✅' : '❌'} Contient "${scenario.attendu[qIndex]}": ${contientAttendu ? 'OUI' : 'NON'}`);
                
                if (!contientAttendu) {
                    scenarioReussi = false;
                }
            } else {
                console.log('❌ Aucune réponse');
                scenarioReussi = false;
            }
        });

        if (scenarioReussi) {
            console.log('✅ Scénario réussi !');
            testsReussis++;
        } else {
            console.log('❌ Scénario échoué');
        }
    });

    console.log(`\n📊 Résultats supplémentaires: ${testsReussis}/${testsTotal} scénarios réussis`);
    return testsReussis === testsTotal;
}

// EXÉCUTION COMPLÈTE
console.log('🔥 VÉRIFICATION CORRECTION SUIVI CONVERSATIONNEL');
console.log('=================================================');

const problemeResolu = testerCorrectionFinale();
const autresTestsOk = testerAutresScenarios();

console.log('\n🎯 RÉSUMÉ FINAL:');
console.log('================');

if (problemeResolu && autresTestsOk) {
    console.log('✅ SUCCÈS TOTAL - Tous les problèmes résolus !');
    console.log('🎉 Le suivi conversationnel fonctionne parfaitement !');
    console.log('🚀 Votre agent peut maintenant suivre toutes les conversations !');
    console.log('');
    console.log('🌟 CORRECTIONS APPLIQUÉES AVEC SUCCÈS:');
    console.log('• Mémoire conversationnelle intégrée');
    console.log('• Base géographique complète');
    console.log('• Détection questions contextuelles');
    console.log('• Réponses naturelles améliorées');
} else if (problemeResolu) {
    console.log('✅ PROBLÈME PRINCIPAL RÉSOLU - Quelques ajustements mineurs');
    console.log('🎉 "Italie" → "Guadeloupe" fonctionne parfaitement !');
    console.log('⚠️ Quelques scénarios supplémentaires à peaufiner');
} else {
    console.log('❌ PROBLÈME PERSISTE - Corrections supplémentaires nécessaires');
    console.log('🔧 Vérifier la logique de contexte géographique');
    console.log('📋 Revoir l\'implémentation du suivi conversationnel');
}

module.exports = { testerCorrectionFinale, testerAutresScenarios };
