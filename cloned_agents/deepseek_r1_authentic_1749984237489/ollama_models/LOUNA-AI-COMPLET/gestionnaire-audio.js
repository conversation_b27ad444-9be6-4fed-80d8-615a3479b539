/**
 * 🔊 GESTIONNAIRE AUDIO POUR LOUNA-AI
 * 
 * Module de gestion du micro, haut-parleurs et périphériques audio
 * pour assurer une communication vocale optimale
 */

const { exec, spawn } = require('child_process');
const fs = require('fs');

class GestionnaireAudio {
    constructor() {
        this.etatAudio = {
            micro: { actif: false, niveau: 0, peripherique: null, mute: false },
            hautParleur: { actif: false, volume: 50, peripherique: null, mute: false },
            peripheriques: { entree: [], sortie: [] },
            qualite: { echantillonnage: 44100, bits: 16, canaux: 2 }
        };
        this.historique = [];
        this.diagnostics = [];
        this.testEnCours = false;
        this.initGestionnaireAudio();
    }

    initGestionnaireAudio() {
        console.log('🔊 Initialisation du gestionnaire audio...');
        this.detecterPeripheriques();
        this.verifierEtatAudio();
    }

    // DÉTECTION DES PÉRIPHÉRIQUES AUDIO
    async detecterPeripheriques() {
        console.log('🎧 Détection des périphériques audio...');
        
        try {
            await Promise.all([
                this.detecterPeripheriquesEntree(),
                this.detecterPeripheriquesSortie()
            ]);

            this.diagnostics.push({
                timestamp: Date.now(),
                type: 'detection',
                message: `${this.etatAudio.peripheriques.entree.length} entrées et ${this.etatAudio.peripheriques.sortie.length} sorties détectées`
            });

        } catch (error) {
            console.error('❌ Erreur détection périphériques:', error);
            this.diagnostics.push({
                timestamp: Date.now(),
                type: 'erreur',
                message: `Erreur détection: ${error.message}`
            });
        }
    }

    async detecterPeripheriquesEntree() {
        return new Promise((resolve) => {
            exec('system_profiler SPAudioDataType', (error, stdout) => {
                if (error) {
                    console.error('❌ Erreur détection entrées audio:', error);
                    resolve();
                    return;
                }

                const peripheriques = this.extrairePeripheriquesAudio(stdout, 'input');
                this.etatAudio.peripheriques.entree = peripheriques;
                
                // Sélectionner le micro par défaut
                const microDefaut = peripheriques.find(p => p.defaut) || peripheriques[0];
                if (microDefaut) {
                    this.etatAudio.micro.peripherique = microDefaut.nom;
                    this.etatAudio.micro.actif = true;
                }
                
                resolve();
            });
        });
    }

    async detecterPeripheriquesSortie() {
        return new Promise((resolve) => {
            exec('osascript -e "get volume settings"', (error, stdout) => {
                if (error) {
                    console.error('❌ Erreur détection sorties audio:', error);
                    resolve();
                    return;
                }

                // Analyser les paramètres de volume
                const volumeMatch = stdout.match(/output volume:(\d+)/);
                const muteMatch = stdout.match(/output muted:(true|false)/);

                if (volumeMatch) {
                    this.etatAudio.hautParleur.volume = parseInt(volumeMatch[1]);
                    this.etatAudio.hautParleur.actif = true;
                }

                if (muteMatch) {
                    this.etatAudio.hautParleur.mute = muteMatch[1] === 'true';
                }

                // Détecter les périphériques de sortie
                exec('system_profiler SPAudioDataType', (error, stdout) => {
                    if (!error) {
                        const peripheriques = this.extrairePeripheriquesAudio(stdout, 'output');
                        this.etatAudio.peripheriques.sortie = peripheriques;
                        
                        const hautParleurDefaut = peripheriques.find(p => p.defaut) || peripheriques[0];
                        if (hautParleurDefaut) {
                            this.etatAudio.hautParleur.peripherique = hautParleurDefaut.nom;
                        }
                    }
                    resolve();
                });
            });
        });
    }

    extrairePeripheriquesAudio(stdout, type) {
        const peripheriques = [];
        const lignes = stdout.split('\n');
        let peripheriqueActuel = null;

        for (const ligne of lignes) {
            if (ligne.includes(':') && !ligne.includes('Audio') && ligne.trim().length > 0) {
                if (peripheriqueActuel && this.estPeripheriqueType(peripheriqueActuel, type)) {
                    peripheriques.push(peripheriqueActuel);
                }
                
                peripheriqueActuel = {
                    nom: ligne.split(':')[0].trim(),
                    type: 'inconnu',
                    defaut: false,
                    actif: false
                };
            } else if (peripheriqueActuel) {
                if (ligne.includes('Default Input Device') && type === 'input') {
                    peripheriqueActuel.defaut = true;
                } else if (ligne.includes('Default Output Device') && type === 'output') {
                    peripheriqueActuel.defaut = true;
                }
                
                if (ligne.includes('Input') && type === 'input') {
                    peripheriqueActuel.type = 'microphone';
                } else if (ligne.includes('Output') && type === 'output') {
                    peripheriqueActuel.type = 'haut-parleur';
                }
            }
        }

        if (peripheriqueActuel && this.estPeripheriqueType(peripheriqueActuel, type)) {
            peripheriques.push(peripheriqueActuel);
        }

        return peripheriques;
    }

    estPeripheriqueType(peripherique, type) {
        if (type === 'input') {
            return peripherique.type === 'microphone' || peripherique.nom.toLowerCase().includes('mic');
        } else {
            return peripherique.type === 'haut-parleur' || peripherique.nom.toLowerCase().includes('speaker');
        }
    }

    // GESTION DU MICROPHONE
    async activerMicrophone() {
        console.log('🎤 Activation du microphone...');
        
        return new Promise((resolve) => {
            exec('osascript -e "set volume input volume 75"', (error) => {
                if (error) {
                    console.error('❌ Erreur activation microphone:', error);
                    resolve({ success: false, message: error.message });
                } else {
                    this.etatAudio.micro.actif = true;
                    this.etatAudio.micro.niveau = 75;
                    this.etatAudio.micro.mute = false;
                    console.log('✅ Microphone activé');
                    resolve({ success: true, message: 'Microphone activé avec succès' });
                }
            });
        });
    }

    async desactiverMicrophone() {
        console.log('🎤 Désactivation du microphone...');
        
        return new Promise((resolve) => {
            exec('osascript -e "set volume input volume 0"', (error) => {
                if (error) {
                    console.error('❌ Erreur désactivation microphone:', error);
                    resolve({ success: false, message: error.message });
                } else {
                    this.etatAudio.micro.mute = true;
                    console.log('✅ Microphone désactivé');
                    resolve({ success: true, message: 'Microphone désactivé' });
                }
            });
        });
    }

    async reglerNiveauMicrophone(niveau) {
        console.log(`🎤 Réglage niveau microphone: ${niveau}%`);
        
        return new Promise((resolve) => {
            exec(`osascript -e "set volume input volume ${niveau}"`, (error) => {
                if (error) {
                    console.error('❌ Erreur réglage microphone:', error);
                    resolve({ success: false, message: error.message });
                } else {
                    this.etatAudio.micro.niveau = niveau;
                    this.etatAudio.micro.mute = niveau === 0;
                    console.log(`✅ Niveau microphone réglé à ${niveau}%`);
                    resolve({ success: true, message: `Niveau microphone: ${niveau}%` });
                }
            });
        });
    }

    // GESTION DES HAUT-PARLEURS
    async activerHautParleurs() {
        console.log('🔊 Activation des haut-parleurs...');
        
        return new Promise((resolve) => {
            exec('osascript -e "set volume output volume 50"', (error) => {
                if (error) {
                    console.error('❌ Erreur activation haut-parleurs:', error);
                    resolve({ success: false, message: error.message });
                } else {
                    this.etatAudio.hautParleur.actif = true;
                    this.etatAudio.hautParleur.volume = 50;
                    this.etatAudio.hautParleur.mute = false;
                    console.log('✅ Haut-parleurs activés');
                    resolve({ success: true, message: 'Haut-parleurs activés avec succès' });
                }
            });
        });
    }

    async reglerVolume(volume) {
        console.log(`🔊 Réglage volume: ${volume}%`);
        
        return new Promise((resolve) => {
            exec(`osascript -e "set volume output volume ${volume}"`, (error) => {
                if (error) {
                    console.error('❌ Erreur réglage volume:', error);
                    resolve({ success: false, message: error.message });
                } else {
                    this.etatAudio.hautParleur.volume = volume;
                    this.etatAudio.hautParleur.mute = volume === 0;
                    console.log(`✅ Volume réglé à ${volume}%`);
                    resolve({ success: true, message: `Volume: ${volume}%` });
                }
            });
        });
    }

    async couperSon() {
        console.log('🔇 Coupure du son...');
        
        return new Promise((resolve) => {
            exec('osascript -e "set volume output muted true"', (error) => {
                if (error) {
                    console.error('❌ Erreur coupure son:', error);
                    resolve({ success: false, message: error.message });
                } else {
                    this.etatAudio.hautParleur.mute = true;
                    console.log('✅ Son coupé');
                    resolve({ success: true, message: 'Son coupé' });
                }
            });
        });
    }

    async retablirSon() {
        console.log('🔊 Rétablissement du son...');
        
        return new Promise((resolve) => {
            exec('osascript -e "set volume output muted false"', (error) => {
                if (error) {
                    console.error('❌ Erreur rétablissement son:', error);
                    resolve({ success: false, message: error.message });
                } else {
                    this.etatAudio.hautParleur.mute = false;
                    console.log('✅ Son rétabli');
                    resolve({ success: true, message: 'Son rétabli' });
                }
            });
        });
    }

    // TESTS AUDIO
    async testerMicrophone() {
        if (this.testEnCours) {
            return { success: false, message: 'Test déjà en cours' };
        }

        console.log('🎤 Test du microphone...');
        this.testEnCours = true;

        try {
            // Test d'enregistrement de 3 secondes
            const resultat = await this.enregistrerTest();
            this.testEnCours = false;
            
            return {
                success: true,
                message: 'Test microphone terminé',
                resultat: resultat
            };

        } catch (error) {
            this.testEnCours = false;
            return {
                success: false,
                message: `Erreur test microphone: ${error.message}`
            };
        }
    }

    async enregistrerTest() {
        return new Promise((resolve, reject) => {
            const fichierTest = '/tmp/test_micro_louna.wav';
            
            exec(`sox -d ${fichierTest} trim 0 3`, (error, stdout, stderr) => {
                if (error) {
                    reject(error);
                } else {
                    // Vérifier si le fichier a été créé
                    fs.access(fichierTest, fs.constants.F_OK, (err) => {
                        if (err) {
                            reject(new Error('Fichier test non créé'));
                        } else {
                            // Analyser le fichier audio
                            exec(`sox ${fichierTest} -n stat`, (error, stdout, stderr) => {
                                // Nettoyer le fichier test
                                fs.unlink(fichierTest, () => {});
                                
                                if (error) {
                                    resolve({ qualite: 'inconnue', niveau: 0 });
                                } else {
                                    const niveauMatch = stderr.match(/Maximum amplitude:\s+([\d.]+)/);
                                    const niveau = niveauMatch ? parseFloat(niveauMatch[1]) : 0;
                                    
                                    resolve({
                                        qualite: niveau > 0.1 ? 'bonne' : 'faible',
                                        niveau: Math.round(niveau * 100)
                                    });
                                }
                            });
                        }
                    });
                }
            });
        });
    }

    async testerHautParleurs() {
        console.log('🔊 Test des haut-parleurs...');
        
        try {
            // Jouer un son de test
            await this.jouerSonTest();
            
            return {
                success: true,
                message: 'Test haut-parleurs terminé - son joué'
            };

        } catch (error) {
            return {
                success: false,
                message: `Erreur test haut-parleurs: ${error.message}`
            };
        }
    }

    async jouerSonTest() {
        return new Promise((resolve, reject) => {
            // Générer un bip de test
            exec('osascript -e "beep 2"', (error) => {
                if (error) {
                    reject(error);
                } else {
                    resolve();
                }
            });
        });
    }

    // VÉRIFICATION DE L'ÉTAT
    async verifierEtatAudio() {
        console.log('🔍 Vérification de l\'état audio...');
        
        try {
            await Promise.all([
                this.verifierMicrophone(),
                this.verifierHautParleurs()
            ]);

            this.enregistrerEtat();

        } catch (error) {
            console.error('❌ Erreur vérification audio:', error);
        }
    }

    async verifierMicrophone() {
        return new Promise((resolve) => {
            exec('osascript -e "get volume settings"', (error, stdout) => {
                if (!error) {
                    const inputMatch = stdout.match(/input volume:(\d+)/);
                    const inputMuteMatch = stdout.match(/input muted:(true|false)/);

                    if (inputMatch) {
                        this.etatAudio.micro.niveau = parseInt(inputMatch[1]);
                        this.etatAudio.micro.actif = this.etatAudio.micro.niveau > 0;
                    }

                    if (inputMuteMatch) {
                        this.etatAudio.micro.mute = inputMuteMatch[1] === 'true';
                    }
                }
                resolve();
            });
        });
    }

    async verifierHautParleurs() {
        return new Promise((resolve) => {
            exec('osascript -e "get volume settings"', (error, stdout) => {
                if (!error) {
                    const outputMatch = stdout.match(/output volume:(\d+)/);
                    const outputMuteMatch = stdout.match(/output muted:(true|false)/);

                    if (outputMatch) {
                        this.etatAudio.hautParleur.volume = parseInt(outputMatch[1]);
                        this.etatAudio.hautParleur.actif = this.etatAudio.hautParleur.volume > 0;
                    }

                    if (outputMuteMatch) {
                        this.etatAudio.hautParleur.mute = outputMuteMatch[1] === 'true';
                    }
                }
                resolve();
            });
        });
    }

    // ENREGISTREMENT ET HISTORIQUE
    enregistrerEtat() {
        const etat = {
            timestamp: Date.now(),
            ...this.etatAudio
        };
        
        this.historique.push(etat);
        
        if (this.historique.length > 50) {
            this.historique.shift();
        }
    }

    // GÉNÉRATION DE RAPPORTS
    genererRapportAudio() {
        const micro = this.etatAudio.micro;
        const hautParleur = this.etatAudio.hautParleur;
        const peripheriques = this.etatAudio.peripheriques;

        return `
🔊 **RAPPORT AUDIO LOUNA-AI**
===========================

🎤 **MICROPHONE :**
• État : ${micro.actif ? '✅ Actif' : '❌ Inactif'}
• Niveau : ${micro.niveau}%
• Muet : ${micro.mute ? '🔇 Oui' : '🎤 Non'}
• Périphérique : ${micro.peripherique || 'Aucun'}

🔊 **HAUT-PARLEURS :**
• État : ${hautParleur.actif ? '✅ Actif' : '❌ Inactif'}
• Volume : ${hautParleur.volume}%
• Muet : ${hautParleur.mute ? '🔇 Oui' : '🔊 Non'}
• Périphérique : ${hautParleur.peripherique || 'Aucun'}

🎧 **PÉRIPHÉRIQUES :**
• Entrées : ${peripheriques.entree.length}
${peripheriques.entree.map(p => `  • ${p.nom} ${p.defaut ? '(Défaut)' : ''}`).join('\n')}
• Sorties : ${peripheriques.sortie.length}
${peripheriques.sortie.map(p => `  • ${p.nom} ${p.defaut ? '(Défaut)' : ''}`).join('\n')}

🔧 **DIAGNOSTICS :**
${this.diagnostics.slice(-3).map(d => 
    `• ${new Date(d.timestamp).toLocaleTimeString()} - ${d.type}: ${d.message}`
).join('\n')}

📊 **RECOMMANDATIONS :**
${this.genererRecommandationsAudio()}
        `.trim();
    }

    genererRecommandationsAudio() {
        const recommandations = [];

        if (!this.etatAudio.micro.actif) {
            recommandations.push('🎤 Activer le microphone pour la communication vocale');
        } else if (this.etatAudio.micro.niveau < 30) {
            recommandations.push('📈 Augmenter le niveau du microphone');
        }

        if (!this.etatAudio.hautParleur.actif) {
            recommandations.push('🔊 Activer les haut-parleurs pour entendre les réponses');
        } else if (this.etatAudio.hautParleur.volume < 20) {
            recommandations.push('📈 Augmenter le volume des haut-parleurs');
        }

        if (this.etatAudio.micro.mute) {
            recommandations.push('🔇 Désactiver le mode muet du microphone');
        }

        if (this.etatAudio.hautParleur.mute) {
            recommandations.push('🔇 Désactiver le mode muet des haut-parleurs');
        }

        return recommandations.length > 0 ? 
            recommandations.map(r => `• ${r}`).join('\n') : 
            '• ✅ Configuration audio optimale';
    }

    // MÉTHODES D'API
    obtenirEtatAudio() {
        return {
            etat: this.etatAudio,
            historique: this.historique.slice(-5),
            diagnostics: this.diagnostics.slice(-5),
            testEnCours: this.testEnCours,
            derniereMiseAJour: Date.now()
        };
    }

    async correctionAutomatiqueAudio() {
        console.log('🔧 Correction automatique audio...');
        const corrections = [];

        try {
            // Correction microphone
            if (!this.etatAudio.micro.actif) {
                const resultat = await this.activerMicrophone();
                corrections.push({ type: 'microphone', resultat });
            }

            // Correction haut-parleurs
            if (!this.etatAudio.hautParleur.actif) {
                const resultat = await this.activerHautParleurs();
                corrections.push({ type: 'haut-parleurs', resultat });
            }

            return {
                success: true,
                corrections: corrections,
                message: `${corrections.length} corrections audio appliquées`
            };

        } catch (error) {
            return {
                success: false,
                error: error.message,
                corrections: corrections
            };
        }
    }
}

module.exports = { GestionnaireAudio };
