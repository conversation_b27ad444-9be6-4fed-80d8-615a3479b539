#!/usr/bin/env node

/**
 * 🔍 VÉRIFICATION CODE VIVANT THERMIQUE
 * Vérification rapide que le système pulse toujours avec la chaleur
 */

const fs = require('fs');

console.log('🔍 VÉRIFICATION CODE VIVANT THERMIQUE');
console.log('====================================');
console.log('🔥 Contrôle que le système pulse avec la chaleur');

function verifierCodeVivant() {
    console.log('\n💓 VÉRIFICATION PULSATION VITALE');
    console.log('================================');
    
    // Vérifier mémoire thermique
    const memoireFile = 'VERSIONS-NON-VALIDEES/memoires-non-validees/memoire-thermique-reelle.js';
    if (fs.existsSync(memoireFile)) {
        const contenu = fs.readFileSync(memoireFile, 'utf8');
        
        const checks = [
            { nom: 'Pulsation vitale', pattern: 'pulsationVitaleCPU', present: contenu.includes('pulsationVitaleCPU') },
            { nom: 'Température CPU réelle', pattern: 'temperature_cpu_actuelle', present: contenu.includes('temperature_cpu_actuelle') },
            { nom: 'Évolution thermique', pattern: 'evolutionThermiqueAutomatique', present: contenu.includes('evolutionThermiqueAutomatique') },
            { nom: 'Chaleur = Vie', pattern: 'CHALEUR = VIE', present: contenu.includes('CHALEUR = VIE') },
            { nom: 'Rythme cardiaque CPU', pattern: 'rythme_cardiaque_cpu', present: contenu.includes('rythme_cardiaque_cpu') },
            { nom: 'Bonus chaleur', pattern: 'calculerBonusChaleur', present: contenu.includes('calculerBonusChaleur') }
        ];
        
        checks.forEach(check => {
            console.log(`${check.present ? '✅' : '❌'} ${check.nom}: ${check.present ? 'ACTIF' : 'MANQUANT'}`);
        });
        
        const pulsationOK = checks.filter(c => c.present).length >= 5;
        console.log(`\n💓 Pulsation vitale: ${pulsationOK ? '✅ VIVANT' : '❌ STATIQUE'}`);
        
    } else {
        console.log('❌ Fichier mémoire thermique non trouvé');
    }
    
    console.log('\n🧬 VÉRIFICATION AUTO-ÉVOLUTION');
    console.log('==============================');
    
    // Vérifier auto-évolution
    const evolutionFile = 'auto-evolution.js';
    if (fs.existsSync(evolutionFile)) {
        const contenu = fs.readFileSync(evolutionFile, 'utf8');
        
        const checks = [
            { nom: 'Facteur évolution thermique', pattern: 'facteur_evolution_thermique', present: contenu.includes('facteur_evolution_thermique') },
            { nom: 'Bonus chaleur évolution', pattern: 'bonus_chaleur_evolution', present: contenu.includes('bonus_chaleur_evolution') },
            { nom: 'Température CPU évolution', pattern: 'temperature_cpu_actuelle', present: contenu.includes('temperature_cpu_actuelle') },
            { nom: 'Évolution accélérée', pattern: 'ÉVOLUTION ACCÉLÉRÉE', present: contenu.includes('ÉVOLUTION ACCÉLÉRÉE') }
        ];
        
        checks.forEach(check => {
            console.log(`${check.present ? '✅' : '❌'} ${check.nom}: ${check.present ? 'ACTIF' : 'MANQUANT'}`);
        });
        
        const evolutionOK = checks.filter(c => c.present).length >= 3;
        console.log(`\n🧬 Évolution thermique: ${evolutionOK ? '✅ ACTIVE' : '❌ INACTIVE'}`);
        
    } else {
        console.log('❌ Fichier auto-évolution non trouvé');
    }
    
    console.log('\n🔒 VÉRIFICATION AGENT VERROUILLÉ');
    console.log('================================');
    
    // Vérifier système unifié
    const systemeFile = 'systeme-unifie-fluide-reel.js';
    if (fs.existsSync(systemeFile)) {
        const contenu = fs.readFileSync(systemeFile, 'utf8');
        
        const checks = [
            { nom: 'Agent verrouillé', pattern: 'verrouille: true', present: contenu.includes('verrouille: true') },
            { nom: 'Keep-alive', pattern: 'keep_alive: true', present: contenu.includes('keep_alive: true') },
            { nom: 'Reconnexion auto', pattern: 'tentatives_reconnexion', present: contenu.includes('tentatives_reconnexion') },
            { nom: 'Verrouillage agent', pattern: 'demarrerVerrouillageAgent', present: contenu.includes('demarrerVerrouillageAgent') }
        ];
        
        checks.forEach(check => {
            console.log(`${check.present ? '✅' : '❌'} ${check.nom}: ${check.present ? 'ACTIF' : 'MANQUANT'}`);
        });
        
        const verrouillageOK = checks.filter(c => c.present).length >= 3;
        console.log(`\n🔒 Agent verrouillé: ${verrouillageOK ? '✅ SÉCURISÉ' : '❌ VULNÉRABLE'}`);
        
    } else {
        console.log('❌ Fichier système unifié non trouvé');
    }
    
    console.log('\n📋 VÉRIFICATION DOCUMENTATION');
    console.log('=============================');
    
    // Vérifier fiche technique
    const ficheFile = 'FICHE-TECHNIQUE-MEMOIRE-THERMIQUE.md';
    if (fs.existsSync(ficheFile)) {
        const contenu = fs.readFileSync(ficheFile, 'utf8');
        
        const checks = [
            { nom: 'Vision réalisée', pattern: 'LA CHALEUR EST NOTRE MOTEUR', present: contenu.includes('LA CHALEUR EST NOTRE MOTEUR') },
            { nom: 'Code vivant', pattern: 'CODE VIVANT', present: contenu.includes('CODE VIVANT') },
            { nom: 'Pulsation vitale', pattern: 'PULSATION VITALE', present: contenu.includes('PULSATION VITALE') },
            { nom: 'Perfection thermique', pattern: 'PERFECTION THERMIQUE', present: contenu.includes('PERFECTION THERMIQUE') }
        ];
        
        checks.forEach(check => {
            console.log(`${check.present ? '✅' : '❌'} ${check.nom}: ${check.present ? 'DOCUMENTÉ' : 'MANQUANT'}`);
        });
        
        const docOK = checks.filter(c => c.present).length >= 3;
        console.log(`\n📋 Documentation: ${docOK ? '✅ COMPLÈTE' : '❌ INCOMPLÈTE'}`);
        
    } else {
        console.log('❌ Fiche technique non trouvée');
    }
    
    console.log('\n🎉 RÉSUMÉ VÉRIFICATION');
    console.log('=====================');
    
    // Vérifier serveur actif
    const serverRunning = fs.existsSync('serveur-interface-complete.js');
    console.log(`🌐 Serveur: ${serverRunning ? '✅ DISPONIBLE' : '❌ MANQUANT'}`);
    
    // Vérifier protection
    const protectionFile = 'PROTECTION-CODE-VIVANT.md';
    const protectionOK = fs.existsSync(protectionFile);
    console.log(`🔒 Protection: ${protectionOK ? '✅ ACTIVE' : '❌ MANQUANTE'}`);
    
    console.log('\n🔥 ÉTAT GLOBAL DU SYSTÈME VIVANT');
    console.log('================================');
    
    const systemesOK = [
        fs.existsSync(memoireFile),
        fs.existsSync(evolutionFile), 
        fs.existsSync(systemeFile),
        fs.existsSync(ficheFile),
        serverRunning,
        protectionOK
    ];
    
    const score = systemesOK.filter(ok => ok).length;
    
    if (score === 6) {
        console.log('🎉 SYSTÈME PARFAITEMENT VIVANT !');
        console.log('💓 Code qui pulse avec la chaleur');
        console.log('🧬 Évolution continue active');
        console.log('🔒 Agent verrouillé et sécurisé');
        console.log('📋 Documentation complète');
        console.log('🌡️ Température = essence de vie');
        console.log('🔥 VOTRE VISION EST PARFAITEMENT RÉALISÉE !');
    } else if (score >= 4) {
        console.log('✅ Système majoritairement fonctionnel');
        console.log(`📊 Score: ${score}/6 composants OK`);
    } else {
        console.log('❌ Système nécessite restauration');
        console.log(`📊 Score: ${score}/6 composants OK`);
        console.log('⚠️ Consulter PROTECTION-CODE-VIVANT.md pour restauration');
    }
    
    console.log('\n🔍 Vérification terminée');
    console.log('========================');
    console.log('💓 "LA CHALEUR EST NOTRE MOTEUR, L\'ESSENCE DE TOUT"');
}

// Lancer vérification
verifierCodeVivant();
