/**
 * SYSTÈME DE MÉMOIRE CONVERSATIONNELLE POUR LOUNA-AI
 * Gère le contexte des conversations longues et du code complexe
 */

const fs = require('fs');
const path = require('path');

class MemoireConversationnelle {
    constructor() {
        this.fichierConversations = path.join(__dirname, 'conversations-data.json');
        this.conversationActuelle = null;
        this.conversations = new Map();
        this.maxMessagesParConversation = 100;
        this.maxConversations = 50;
        
        this.chargerConversations();
        this.demarrerNouvelleConversation();
    }

    // CHARGER CONVERSATIONS EXISTANTES
    chargerConversations() {
        try {
            if (fs.existsSync(this.fichierConversations)) {
                const data = JSON.parse(fs.readFileSync(this.fichierConversations, 'utf8'));
                this.conversations = new Map(data.conversations || []);
                console.log(`📥 ${this.conversations.size} conversations chargées`);
            }
        } catch (error) {
            console.error(`❌ Erreur chargement conversations:`, error.message);
            this.conversations = new Map();
        }
    }

    // SAUVEGARDER CONVERSATIONS
    sauvegarderConversations() {
        try {
            const data = {
                conversations: Array.from(this.conversations.entries()),
                timestamp: Date.now()
            };
            fs.writeFileSync(this.fichierConversations, JSON.stringify(data, null, 2));
            console.log(`💾 Conversations sauvegardées: ${this.conversations.size}`);
            return true;
        } catch (error) {
            console.error(`❌ Erreur sauvegarde conversations:`, error.message);
            return false;
        }
    }

    // DÉMARRER NOUVELLE CONVERSATION
    demarrerNouvelleConversation() {
        const id = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        this.conversationActuelle = {
            id: id,
            debut: Date.now(),
            messages: [],
            contexte: {
                sujet_principal: null,
                variables_code: new Map(),
                classes_definies: new Map(),
                fonctions_definies: new Map(),
                etapes_projet: [],
                derniere_action: null
            },
            resume: null
        };
        
        this.conversations.set(id, this.conversationActuelle);
        console.log(`🆕 Nouvelle conversation: ${id}`);
        
        // Nettoyer anciennes conversations si trop nombreuses
        if (this.conversations.size > this.maxConversations) {
            this.nettoyerAnciennesConversations();
        }
        
        return id;
    }

    // AJOUTER MESSAGE À LA CONVERSATION
    ajouterMessage(question, reponse, source, metadata = {}) {
        if (!this.conversationActuelle) {
            this.demarrerNouvelleConversation();
        }

        const message = {
            timestamp: Date.now(),
            question: question,
            reponse: reponse,
            source: source,
            metadata: metadata,
            numero: this.conversationActuelle.messages.length + 1
        };

        this.conversationActuelle.messages.push(message);

        // Analyser et extraire le contexte
        this.analyserContexte(question, reponse);

        // Limiter le nombre de messages
        if (this.conversationActuelle.messages.length > this.maxMessagesParConversation) {
            this.resumerConversation();
        }

        this.sauvegarderConversations();
        return message;
    }

    // ANALYSER CONTEXTE DE LA CONVERSATION
    analyserContexte(question, reponse) {
        const contexte = this.conversationActuelle.contexte;
        
        // Détecter sujet principal
        if (!contexte.sujet_principal) {
            if (question.toLowerCase().includes('code') || question.toLowerCase().includes('classe') || question.toLowerCase().includes('fonction')) {
                contexte.sujet_principal = 'programmation';
            } else if (question.toLowerCase().includes('calcul') || question.toLowerCase().includes('mathématique')) {
                contexte.sujet_principal = 'mathematiques';
            }
        }

        // Extraire définitions de classes
        const reponseStr = String(reponse); const matchClasse = reponseStr.match(/class\s+(\w+)/gi);
        if (matchClasse) {
            matchClasse.forEach(match => {
                const className = match.split(' ')[1];
                contexte.classes_definies.set(className, {
                    definition: reponseStr,
                    timestamp: Date.now(),
                    message_numero: this.conversationActuelle.messages.length
                });
            });
        }

        // Extraire définitions de fonctions
        const matchFonction = reponseStr.match(/def\s+(\w+)|function\s+(\w+)/gi);
        if (matchFonction) {
            matchFonction.forEach(match => {
                const funcName = match.split(' ')[1];
                contexte.fonctions_definies.set(funcName, {
                    definition: reponseStr,
                    timestamp: Date.now(),
                    message_numero: this.conversationActuelle.messages.length
                });
            });
        }

        // Détecter étapes de projet
        const matchEtape = question.match(/étape\s+(\d+)/i);
        if (matchEtape) {
            const numeroEtape = parseInt(matchEtape[1]);
            contexte.etapes_projet.push({
                numero: numeroEtape,
                description: question,
                reponse: reponse,
                timestamp: Date.now()
            });
        }

        // Mémoriser dernière action
        contexte.derniere_action = {
            type: this.detecterTypeAction(question),
            description: question,
            timestamp: Date.now()
        };
    }

    // DÉTECTER TYPE D'ACTION
    detecterTypeAction(question) {
        if (question.toLowerCase().includes('crée') || question.toLowerCase().includes('créer')) {
            return 'creation';
        } else if (question.toLowerCase().includes('ajoute') || question.toLowerCase().includes('ajouter')) {
            return 'ajout';
        } else if (question.toLowerCase().includes('modifie') || question.toLowerCase().includes('modifier')) {
            return 'modification';
        } else if (question.toLowerCase().includes('calcule') || question.toLowerCase().includes('calculer')) {
            return 'calcul';
        } else if (question.toLowerCase().includes('explique') || question.toLowerCase().includes('expliquer')) {
            return 'explication';
        }
        return 'autre';
    }

    // OBTENIR CONTEXTE POUR RÉPONSE
    obtenirContexte(question) {
        if (!this.conversationActuelle) {
            return null;
        }

        const contexte = this.conversationActuelle.contexte;
        const messagesRecents = this.conversationActuelle.messages.slice(-5); // 5 derniers messages

        return {
            sujet_principal: contexte.sujet_principal,
            messages_recents: messagesRecents,
            classes_definies: Array.from(contexte.classes_definies.entries()),
            fonctions_definies: Array.from(contexte.fonctions_definies.entries()),
            etapes_projet: contexte.etapes_projet,
            derniere_action: contexte.derniere_action,
            conversation_id: this.conversationActuelle.id,
            nombre_messages: this.conversationActuelle.messages.length
        };
    }

    // RECHERCHER DANS L'HISTORIQUE
    rechercherDansHistorique(requete, limite = 5) {
        const resultats = [];
        
        if (!this.conversationActuelle) {
            return resultats;
        }

        const requeteLower = requete.toLowerCase();
        
        // Rechercher dans les messages de la conversation actuelle
        for (const message of this.conversationActuelle.messages) {
            let pertinence = 0;
            
            if (message.question.toLowerCase().includes(requeteLower)) {
                pertinence += 1.0;
            }
            if (String(message.reponse).toLowerCase().includes(requeteLower)) {
                pertinence += 0.8;
            }
            
            // Recherche par mots-clés
            const mots = requeteLower.split(' ');
            for (const mot of mots) {
                if (mot.length > 2) {
                    if (message.question.toLowerCase().includes(mot)) {
                        pertinence += 0.3;
                    }
                    if (String(message.reponse).toLowerCase().includes(mot)) {
                        pertinence += 0.2;
                    }
                }
            }
            
            if (pertinence > 0.3) {
                resultats.push({
                    message: message,
                    pertinence: pertinence,
                    age_messages: this.conversationActuelle.messages.length - message.numero
                });
            }
        }
        
        // Trier par pertinence et récence
        resultats.sort((a, b) => {
            const scoreA = a.pertinence - (a.age_messages * 0.1);
            const scoreB = b.pertinence - (b.age_messages * 0.1);
            return scoreB - scoreA;
        });
        
        return resultats.slice(0, limite);
    }

    // RÉSUMER CONVERSATION LONGUE
    resumerConversation() {
        const messages = this.conversationActuelle.messages;
        const anciens = messages.slice(0, -20); // Garder les 20 derniers
        
        // Créer un résumé des anciens messages
        const resume = {
            periode: `Messages 1-${anciens.length}`,
            sujet_principal: this.conversationActuelle.contexte.sujet_principal,
            classes_creees: Array.from(this.conversationActuelle.contexte.classes_definies.keys()),
            fonctions_creees: Array.from(this.conversationActuelle.contexte.fonctions_definies.keys()),
            etapes_completees: this.conversationActuelle.contexte.etapes_projet.length,
            timestamp: Date.now()
        };
        
        this.conversationActuelle.resume = resume;
        this.conversationActuelle.messages = messages.slice(-20); // Garder seulement les 20 derniers
        
        console.log(`📝 Conversation résumée: ${anciens.length} messages archivés`);
    }

    // NETTOYER ANCIENNES CONVERSATIONS
    nettoyerAnciennesConversations() {
        const conversations = Array.from(this.conversations.entries());
        conversations.sort((a, b) => b[1].debut - a[1].debut); // Trier par date
        
        // Garder seulement les plus récentes
        const aGarder = conversations.slice(0, this.maxConversations);
        this.conversations = new Map(aGarder);
        
        console.log(`🧹 Nettoyage: ${conversations.length - aGarder.length} anciennes conversations supprimées`);
    }

    // STATISTIQUES
    getStats() {
        const conv = this.conversationActuelle;
        return {
            conversation_actuelle: conv ? conv.id : null,
            nombre_messages: conv ? conv.messages.length : 0,
            sujet_principal: conv ? conv.contexte.sujet_principal : null,
            classes_definies: conv ? conv.contexte.classes_definies.size : 0,
            fonctions_definies: conv ? conv.contexte.fonctions_definies.size : 0,
            etapes_projet: conv ? conv.contexte.etapes_projet.length : 0,
            total_conversations: this.conversations.size
        };
    }
}

module.exports = { MemoireConversationnelle };
