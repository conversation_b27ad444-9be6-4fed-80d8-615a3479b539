const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 8080;

const server = http.createServer((req, res) => {
    console.log(`📡 Requête: ${req.url}`);

    // Headers CORS
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    let filePath = path.join(__dirname, 'interface-louna-complete.html');

    // Vérifier si le fichier existe
    if (!fs.existsSync(filePath)) {
        console.log(`❌ Fichier non trouvé: ${filePath}`);
        res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(`
            <h1>Fichier non trouvé</h1>
            <p>Le fichier ${filePath} n'existe pas.</p>
            <p>Répertoire actuel: ${__dirname}</p>
        `);
        return;
    }

    fs.readFile(filePath, 'utf8', (err, content) => {
        if (err) {
            console.log(`❌ Erreur lecture: ${err.message}`);
            res.writeHead(500, { 'Content-Type': 'text/html; charset=utf-8' });
            res.end(`<h1>Erreur serveur</h1><p>${err.message}</p>`);
        } else {
            console.log(`✅ Fichier servi avec succès`);
            res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
            res.end(content);
        }
    });
});

server.listen(PORT, () => {
    console.log('🌟 ================================================');
    console.log('🚀 SERVEUR LOUNA-AI DÉMARRÉ AVEC SUCCÈS');
    console.log('🌟 ================================================');
    console.log(`🌐 URL: http://localhost:${PORT}`);
    console.log(`📁 Fichier: ${path.join(__dirname, 'interface-louna-complete.html')}`);
    console.log('✅ Interface corrigée avec barre latérale active !');
    console.log('🌟 ================================================');
});

server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.log(`❌ Port ${PORT} occupé. Essayons le port ${PORT + 1}...`);
        server.listen(PORT + 1);
    } else {
        console.log('❌ Erreur serveur:', err.message);
    }
});
