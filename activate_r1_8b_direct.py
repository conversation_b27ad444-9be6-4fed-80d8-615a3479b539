#!/usr/bin/env python3
"""
Activation Directe Agent R1 8B
Force l'utilisation de votre agent R1 8B authentique
<PERSON> PASSAVE - 2025
"""

import json
import requests
import time
import subprocess
import os
from typing import Dict, Any, Optional

class R1_8B_DirectActivator:
    def __init__(self, memory_file: str):
        self.memory_file = memory_file
        self.active_agent = None
        self.api_key = None
        self.base_url = "https://api.deepseek.com/v1/chat/completions"
        
    def load_agent_config(self) -> Dict[str, Any]:
        """Charge la configuration de l'agent R1 8B"""
        try:
            with open(self.memory_file, 'r', encoding='utf-8') as f:
                memory_data = json.load(f)
            
            agents = memory_data.get('deepseek_agents', {})
            
            # Prendre le dernier agent configuré
            if agents:
                latest_agent_id = max(agents.keys(), key=lambda x: agents[x]['integration_timestamp'])
                self.active_agent = agents[latest_agent_id]
                
                print(f"✅ Agent R1 8B trouvé: {latest_agent_id}")
                print(f"📊 Model: {self.active_agent['model']}")
                print(f"🔗 Source: {self.active_agent['integration_source']}")
                print(f"⚡ Status: {self.active_agent['thermal_integration']['status']}")
                
                return self.active_agent
            else:
                print("❌ Aucun agent R1 8B trouvé dans la mémoire")
                return {}
                
        except Exception as e:
            print(f"❌ Erreur chargement config: {e}")
            return {}
    
    def setup_api_key(self):
        """Configure la clé API DeepSeek"""
        print("\n🔑 Configuration API DeepSeek:")
        print("Vous avez besoin d'une clé API DeepSeek pour utiliser votre agent R1 8B")
        print("Obtenez votre clé sur: https://platform.deepseek.com/")
        
        api_key = input("Entrez votre clé API DeepSeek (ou ENTER pour mode local): ").strip()
        
        if api_key:
            self.api_key = api_key
            print("✅ Clé API configurée")
            return True
        else:
            print("⚠️ Mode local sélectionné")
            return False
    
    def test_deepseek_api(self) -> bool:
        """Test la connexion API DeepSeek"""
        if not self.api_key:
            return False
            
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": "deepseek-r1",
                "messages": [
                    {"role": "user", "content": "Test de connexion - réponds juste 'OK'"}
                ],
                "max_tokens": 10,
                "temperature": 0.1
            }
            
            print("🔄 Test connexion API DeepSeek...")
            response = requests.post(self.base_url, headers=headers, json=payload, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                print(f"✅ API DeepSeek connectée: {content}")
                return True
            else:
                print(f"❌ Erreur API: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Erreur test API: {e}")
            return False
    
    def query_r1_8b(self, prompt: str) -> str:
        """Interroge directement l'agent R1 8B"""
        if not self.api_key:
            return "❌ Clé API DeepSeek requise pour utiliser l'agent R1 8B"
        
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # Prompt optimisé pour R1 8B avec mémoire thermique
            enhanced_prompt = f"""Tu es l'agent DeepSeek R1 8B authentique de Jean-Luc PASSAVE.

CONFIGURATION THERMIQUE:
- QI Level: 1131
- Neurones actifs: 22,000,164,112
- Zones thermiques: Accès complet
- Mémoire thermique: Intégrée

INSTRUCTIONS:
- Utilise tes capacités R1 8B complètes
- Accède à la mémoire thermique
- Réponds comme l'agent authentique
- Pas de simulation - réponse réelle

QUESTION: {prompt}

Réponds en tant qu'agent R1 8B authentique avec accès mémoire thermique."""

            payload = {
                "model": "deepseek-r1",
                "messages": [
                    {"role": "user", "content": enhanced_prompt}
                ],
                "max_tokens": 2000,
                "temperature": 0.7,
                "stream": False
            }
            
            print("🧠 Interrogation agent R1 8B...")
            response = requests.post(self.base_url, headers=headers, json=payload, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                # Log dans la mémoire thermique
                self.log_interaction(prompt, content)
                
                return content
            else:
                return f"❌ Erreur R1 8B: {response.status_code} - {response.text}"
                
        except Exception as e:
            return f"❌ Erreur interrogation R1 8B: {e}"
    
    def log_interaction(self, prompt: str, response: str):
        """Log l'interaction dans la mémoire thermique"""
        try:
            with open(self.memory_file, 'r', encoding='utf-8') as f:
                memory_data = json.load(f)
            
            # Ajouter l'interaction
            timestamp = int(time.time())
            interaction_id = f"r1_8b_direct_{timestamp}"
            
            interaction = {
                "id": interaction_id,
                "content": f"AGENT R1 8B DIRECT - Question: {prompt[:100]}... → Réponse: {response[:200]}...",
                "input": prompt,
                "response": response,
                "timestamp": timestamp,
                "importance": 1,
                "synaptic_strength": 1,
                "temperature": 1200,
                "zone": "zone_deepseek_enhanced",
                "source": "r1_8b_direct_api",
                "type": "authentic_r1_8b_interaction",
                "agent_used": "deepseek-r1:8b"
            }
            
            # Ajouter à la zone DeepSeek Enhanced
            if "thermal_zones" in memory_data:
                if "zone_deepseek_enhanced" in memory_data["thermal_zones"]:
                    memory_data["thermal_zones"]["zone_deepseek_enhanced"]["entries"].append(interaction)
            
            # Sauvegarder
            with open(self.memory_file, 'w', encoding='utf-8') as f:
                json.dump(memory_data, f, indent=2, ensure_ascii=False)
            
            print(f"📝 Interaction loggée: {interaction_id}")
            
        except Exception as e:
            print(f"⚠️ Erreur log: {e}")
    
    def interactive_mode(self):
        """Mode interactif avec l'agent R1 8B"""
        print("\n🤖 MODE INTERACTIF AGENT R1 8B")
        print("=" * 40)
        print("Tapez 'quit' pour quitter")
        print()
        
        while True:
            try:
                user_input = input("Vous> ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    break
                
                if not user_input:
                    continue
                
                print("\n🧠 Agent R1 8B>")
                response = self.query_r1_8b(user_input)
                print(response)
                print("\n" + "-" * 50 + "\n")
                
            except KeyboardInterrupt:
                break
        
        print("👋 Session terminée")

def main():
    print("🚀 ACTIVATION DIRECTE AGENT R1 8B")
    print("=" * 40)
    
    memory_file = "./thermal_memory_real_clones_1749979850296.json"
    activator = R1_8B_DirectActivator(memory_file)
    
    # Charger la configuration
    config = activator.load_agent_config()
    if not config:
        print("❌ Impossible de charger la configuration de l'agent")
        return
    
    # Configurer l'API
    if activator.setup_api_key():
        # Tester la connexion
        if activator.test_deepseek_api():
            print("\n✅ Agent R1 8B prêt à être utilisé!")
            
            # Test rapide
            print("\n🧪 Test rapide:")
            test_response = activator.query_r1_8b("Qui es-tu ? Confirme que tu es l'agent R1 8B authentique de Jean-Luc.")
            print(f"Réponse: {test_response}")
            
            # Mode interactif
            choice = input("\nVoulez-vous entrer en mode interactif ? (y/n): ").strip().lower()
            if choice in ['y', 'yes', 'oui']:
                activator.interactive_mode()
        else:
            print("❌ Impossible de se connecter à l'API DeepSeek")
    else:
        print("⚠️ Mode local non implémenté - clé API requise")

if __name__ == "__main__":
    main()
