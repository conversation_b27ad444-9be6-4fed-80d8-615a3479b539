#!/usr/bin/env python3
"""
TESTS AVANCÉS MISTRAL + MÉMOIRE THERMIQUE
Tests approfondis pour Jean-Luc PASSAVE
"""

import time
import json
import sqlite3
from mistral_thermal_integration import MistralThermalMemory

class AdvancedMistralTester:
    """Tests avancés du système Mistral + Mémoire Thermique"""
    
    def __init__(self):
        self.mistral_thermal = MistralThermalMemory()
        self.test_results = []
        
    def run_all_tests(self):
        """Lance tous les tests avancés"""
        print("🧪 TESTS AVANCÉS MISTRAL + MÉMOIRE THERMIQUE")
        print("=" * 60)
        
        tests = [
            self.test_memory_persistence,
            self.test_learning_evolution,
            self.test_context_awareness,
            self.test_injection_system,
            self.test_concept_extraction,
            self.test_database_integrity,
            self.test_performance_metrics,
            self.test_complex_scenarios
        ]
        
        for test in tests:
            try:
                print(f"\n🔬 {test.__name__.replace('_', ' ').upper()}")
                print("-" * 40)
                result = test()
                self.test_results.append({
                    "test": test.__name__,
                    "status": "PASS" if result else "FAIL",
                    "timestamp": time.time()
                })
                print(f"✅ RÉSULTAT: {'PASS' if result else 'FAIL'}")
            except Exception as e:
                print(f"❌ ERREUR: {e}")
                self.test_results.append({
                    "test": test.__name__,
                    "status": "ERROR",
                    "error": str(e),
                    "timestamp": time.time()
                })
        
        self.generate_report()
    
    def test_memory_persistence(self):
        """Test de persistance de la mémoire"""
        # Injecter une donnée de test
        test_key = f"test_persistence_{int(time.time())}"
        test_value = "Données de test persistance"
        
        self.mistral_thermal.inject_thermal_memory(test_key, test_value, 8)
        
        # Vérifier la récupération
        retrieved = self.mistral_thermal.get_thermal_memory(test_key)
        
        if retrieved == test_value:
            print("✅ Mémoire persistante fonctionnelle")
            return True
        else:
            print(f"❌ Échec persistance: {retrieved} != {test_value}")
            return False
    
    def test_learning_evolution(self):
        """Test d'évolution par apprentissage"""
        initial_count = self.mistral_thermal.injection_count
        
        # Simuler plusieurs interactions
        test_inputs = [
            "Test apprentissage IA",
            "Cybersécurité avancée",
            "Mémoire thermique évolution"
        ]
        
        for input_text in test_inputs:
            self.mistral_thermal.learn_from_interaction(input_text, "Réponse test")
        
        final_count = self.mistral_thermal.injection_count
        
        if final_count > initial_count:
            print(f"✅ Apprentissage actif: {initial_count} → {final_count}")
            return True
        else:
            print(f"❌ Pas d'apprentissage détecté")
            return False
    
    def test_context_awareness(self):
        """Test de conscience contextuelle"""
        # Test avec question sur l'identité
        result = self.mistral_thermal.process_with_thermal_memory("Qui êtes-vous ?")
        
        response = result['mistral_response'].lower()
        
        # Vérifier que la réponse contient des éléments de contexte
        context_elements = ['mistral', 'jean-luc', 'mémoire', 'thermique']
        found_elements = [elem for elem in context_elements if elem in response]
        
        if len(found_elements) >= 2:
            print(f"✅ Contexte utilisé: {found_elements}")
            return True
        else:
            print(f"❌ Contexte insuffisant: {found_elements}")
            return False
    
    def test_injection_system(self):
        """Test du système d'injection"""
        # Test injection multiple
        injections = [
            ("test_skill_1", "Compétence en Python", 7),
            ("test_skill_2", "Expertise en sécurité", 9),
            ("test_memory_3", "Capacité d'adaptation", 6)
        ]
        
        success_count = 0
        for key, value, importance in injections:
            try:
                self.mistral_thermal.inject_thermal_memory(key, value, importance)
                retrieved = self.mistral_thermal.get_thermal_memory(key)
                if retrieved == value:
                    success_count += 1
            except Exception as e:
                print(f"❌ Erreur injection {key}: {e}")
        
        if success_count == len(injections):
            print(f"✅ Toutes les injections réussies: {success_count}/{len(injections)}")
            return True
        else:
            print(f"❌ Injections partielles: {success_count}/{len(injections)}")
            return False
    
    def test_concept_extraction(self):
        """Test d'extraction de concepts"""
        test_text = "Intelligence artificielle et cybersécurité avec apprentissage automatique"
        concepts = self.mistral_thermal.extract_concepts(test_text)
        
        expected_concepts = ['intelligence', 'ai', 'cybersécurité', 'apprentissage']
        found_concepts = [c for c in expected_concepts if c in concepts]
        
        if len(found_concepts) >= 2:
            print(f"✅ Concepts extraits: {concepts}")
            return True
        else:
            print(f"❌ Extraction insuffisante: {concepts}")
            return False
    
    def test_database_integrity(self):
        """Test d'intégrité de la base de données"""
        try:
            conn = sqlite3.connect(self.mistral_thermal.db_path)
            cursor = conn.cursor()
            
            # Vérifier les tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            expected_tables = ['thermal_memory', 'conversation_context', 'mistral_evolution']
            
            table_names = [table[0] for table in tables]
            missing_tables = [t for t in expected_tables if t not in table_names]
            
            if not missing_tables:
                print(f"✅ Toutes les tables présentes: {table_names}")
                
                # Vérifier l'intégrité des données
                cursor.execute("SELECT COUNT(*) FROM thermal_memory")
                memory_count = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM conversation_context")
                conversation_count = cursor.fetchone()[0]
                
                print(f"✅ Données: {memory_count} mémoires, {conversation_count} conversations")
                conn.close()
                return True
            else:
                print(f"❌ Tables manquantes: {missing_tables}")
                conn.close()
                return False
                
        except Exception as e:
            print(f"❌ Erreur base de données: {e}")
            return False
    
    def test_performance_metrics(self):
        """Test de métriques de performance"""
        start_time = time.time()
        
        # Test de performance sur plusieurs requêtes
        test_queries = [
            "Quel est votre QI ?",
            "Quelle est votre mission ?",
            "Parlez-moi de vos capacités"
        ]
        
        response_times = []
        for query in test_queries:
            query_start = time.time()
            result = self.mistral_thermal.process_with_thermal_memory(query)
            query_time = time.time() - query_start
            response_times.append(query_time)
        
        avg_response_time = sum(response_times) / len(response_times)
        total_time = time.time() - start_time
        
        print(f"✅ Temps moyen de réponse: {avg_response_time:.2f}s")
        print(f"✅ Temps total: {total_time:.2f}s")
        print(f"✅ Temps individuels: {[f'{t:.2f}s' for t in response_times]}")
        
        # Performance acceptable si < 30s par requête en moyenne
        return avg_response_time < 30.0
    
    def test_complex_scenarios(self):
        """Test de scénarios complexes"""
        # Scénario 1: Injection puis récupération contextuelle
        self.mistral_thermal.inject_thermal_memory(
            "scenario_test", 
            "Jean-Luc travaille sur des projets d'IA révolutionnaires", 
            9
        )
        
        # Scénario 2: Question qui devrait utiliser le contexte injecté
        result = self.mistral_thermal.process_with_thermal_memory(
            "Que savez-vous des projets de Jean-Luc ?"
        )
        
        response = result['mistral_response'].lower()
        
        # Vérifier que la réponse utilise le contexte
        context_used = any(word in response for word in ['projet', 'révolutionnaire', 'ia', 'jean-luc'])
        
        if context_used:
            print(f"✅ Scénario complexe réussi")
            print(f"✅ Réponse contextuelle: {result['mistral_response'][:100]}...")
            return True
        else:
            print(f"❌ Contexte non utilisé dans scénario complexe")
            return False
    
    def generate_report(self):
        """Génère un rapport de test"""
        print("\n" + "=" * 60)
        print("📊 RAPPORT DE TESTS AVANCÉS")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAIL'])
        error_tests = len([r for r in self.test_results if r['status'] == 'ERROR'])
        
        print(f"📈 RÉSULTATS GLOBAUX:")
        print(f"  ✅ Tests réussis: {passed_tests}/{total_tests}")
        print(f"  ❌ Tests échoués: {failed_tests}/{total_tests}")
        print(f"  🔥 Erreurs: {error_tests}/{total_tests}")
        print(f"  📊 Taux de réussite: {(passed_tests/total_tests)*100:.1f}%")
        
        print(f"\n📋 DÉTAIL DES TESTS:")
        for result in self.test_results:
            status_icon = "✅" if result['status'] == 'PASS' else "❌" if result['status'] == 'FAIL' else "🔥"
            print(f"  {status_icon} {result['test']}: {result['status']}")
        
        # Statut final du système
        status = self.mistral_thermal.get_thermal_status()
        print(f"\n🎯 STATUT SYSTÈME FINAL:")
        print(f"  🧠 Entrées mémoire: {status['memory_entries']}")
        print(f"  💬 Conversations: {status['conversations_stored']}")
        print(f"  💉 Injections: {status['injections_performed']}")
        print(f"  🔥 Mémoire active: {status['thermal_memory_active']}")
        
        if passed_tests == total_tests:
            print(f"\n🎉 TOUS LES TESTS RÉUSSIS ! SYSTÈME PARFAITEMENT OPÉRATIONNEL !")
        elif passed_tests >= total_tests * 0.8:
            print(f"\n✅ SYSTÈME LARGEMENT FONCTIONNEL ({(passed_tests/total_tests)*100:.1f}%)")
        else:
            print(f"\n⚠️ SYSTÈME NÉCESSITE DES AMÉLIORATIONS")

def main():
    """Lance les tests avancés"""
    tester = AdvancedMistralTester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()
