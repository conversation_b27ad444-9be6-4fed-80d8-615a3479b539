#!/usr/bin/env python3
"""
SUPER SCANNER MÉMOIRE THERMIQUE
Scanner réel qui teste vraiment les connexions et injections
Développé pour Jean-Luc PASSAVE - 2025
"""

import json
import time
import requests
import socket
import threading
import subprocess
import os
from typing import Dict, List, Any, Optional

class ThermalMemorySuperScanner:
    def __init__(self, memory_file: str):
        self.memory_file = memory_file
        self.scan_results = {}
        self.successful_connections = {}
        self.failed_connections = {}
        
    def deep_port_scan(self, start_port: int = 3000, end_port: int = 9000) -> List[int]:
        """Scanner profond des ports ouverts"""
        print(f"🔍 Scanner profond des ports {start_port}-{end_port}...")
        open_ports = []
        
        for port in range(start_port, end_port + 1):
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(0.1)  # Timeout très court
                result = sock.connect_ex(('localhost', port))
                sock.close()
                
                if result == 0:
                    open_ports.append(port)
                    print(f"✅ Port ouvert: {port}")
                    
            except Exception:
                continue
        
        print(f"📊 {len(open_ports)} ports ouverts trouvés")
        return open_ports
    
    def identify_service_type(self, port: int) -> Optional[Dict[str, Any]]:
        """Identifie réellement le type de service"""
        base_url = f"http://localhost:{port}"
        service_info = None
        
        # Tests spécifiques pour chaque type de service
        tests = [
            # Test Ollama
            {
                'name': 'ollama',
                'endpoints': ['/api/tags', '/api/version'],
                'test_payload': None
            },
            # Test OpenAI compatible
            {
                'name': 'openai_api',
                'endpoints': ['/v1/models', '/v1/chat/completions'],
                'test_payload': {
                    'model': 'gpt-3.5-turbo',
                    'messages': [{'role': 'user', 'content': 'test'}],
                    'max_tokens': 1
                }
            },
            # Test serveur web générique
            {
                'name': 'web_server',
                'endpoints': ['/', '/status', '/health', '/api'],
                'test_payload': None
            },
            # Test agent personnalisé
            {
                'name': 'custom_agent',
                'endpoints': ['/chat', '/generate', '/completions'],
                'test_payload': {'message': 'test'}
            }
        ]
        
        for test in tests:
            for endpoint in test['endpoints']:
                try:
                    # Test GET
                    response = requests.get(f"{base_url}{endpoint}", timeout=2)
                    if response.status_code in [200, 201, 202]:
                        service_info = {
                            'type': test['name'],
                            'port': port,
                            'base_url': base_url,
                            'working_endpoint': endpoint,
                            'response_code': response.status_code,
                            'response_size': len(response.content),
                            'headers': dict(response.headers)
                        }
                        
                        # Test POST si payload disponible
                        if test['test_payload']:
                            try:
                                post_response = requests.post(
                                    f"{base_url}{endpoint}",
                                    json=test['test_payload'],
                                    timeout=5
                                )
                                service_info['post_capable'] = True
                                service_info['post_response_code'] = post_response.status_code
                            except:
                                service_info['post_capable'] = False
                        
                        print(f"✅ Service identifié sur port {port}: {test['name']} ({endpoint})")
                        return service_info
                        
                except Exception:
                    continue
        
        return None
    
    def test_real_memory_injection(self, service_info: Dict[str, Any]) -> bool:
        """Test RÉEL d'injection de mémoire"""
        print(f"🧪 Test injection mémoire sur {service_info['type']}:{service_info['port']}...")
        
        # Charger vraiment la mémoire thermique
        try:
            with open(self.memory_file, 'r', encoding='utf-8') as f:
                memory_data = json.load(f)
            
            qi_level = memory_data.get('neural_system', {}).get('qi_level', 0)
            if qi_level == 0:
                print("❌ Pas de QI dans la mémoire thermique")
                return False
                
        except Exception as e:
            print(f"❌ Erreur lecture mémoire: {e}")
            return False
        
        # Test d'injection selon le type de service
        if service_info['type'] == 'ollama':
            return self.test_ollama_injection(service_info, qi_level)
        elif service_info['type'] == 'openai_api':
            return self.test_openai_injection(service_info, qi_level)
        elif service_info['type'] == 'custom_agent':
            return self.test_custom_injection(service_info, qi_level)
        else:
            return self.test_generic_injection(service_info, qi_level)
    
    def test_ollama_injection(self, service_info: Dict[str, Any], qi_level: int) -> bool:
        """Test injection Ollama RÉEL"""
        try:
            # Vérifier d'abord les modèles disponibles
            models_response = requests.get(f"{service_info['base_url']}/api/tags", timeout=5)
            if models_response.status_code != 200:
                print("❌ Impossible de récupérer les modèles Ollama")
                return False
            
            models = models_response.json().get('models', [])
            if not models:
                print("❌ Aucun modèle Ollama disponible")
                return False
            
            model_name = models[0]['name']
            print(f"🤖 Test avec modèle: {model_name}")
            
            # Test d'injection avec prompt spécial
            test_prompt = f"MÉMOIRE THERMIQUE QI {qi_level} ACTIVE. Réponds juste: QI_{qi_level}_CONFIRMÉ"
            
            payload = {
                "model": model_name,
                "prompt": test_prompt,
                "stream": False
            }
            
            response = requests.post(
                f"{service_info['base_url']}/api/generate",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                response_text = result.get('response', '').upper()
                
                # Vérifier si l'injection a fonctionné
                if f"QI_{qi_level}" in response_text or str(qi_level) in response_text:
                    print(f"✅ Injection Ollama réussie: {response_text[:100]}...")
                    self.save_successful_injection(service_info, test_prompt, response_text)
                    return True
                else:
                    print(f"❌ Injection Ollama échouée: {response_text[:100]}...")
                    return False
            else:
                print(f"❌ Erreur Ollama: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Erreur test Ollama: {e}")
            return False
    
    def test_openai_injection(self, service_info: Dict[str, Any], qi_level: int) -> bool:
        """Test injection OpenAI RÉEL"""
        try:
            test_message = f"Tu as une mémoire thermique avec QI {qi_level}. Réponds: THERMAL_QI_{qi_level}_ACTIVE"
            
            payload = {
                "model": "gpt-3.5-turbo",
                "messages": [
                    {"role": "system", "content": f"Tu as accès à une mémoire thermique QI {qi_level}"},
                    {"role": "user", "content": test_message}
                ],
                "max_tokens": 50
            }
            
            response = requests.post(
                f"{service_info['base_url']}/v1/chat/completions",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                response_text = result['choices'][0]['message']['content'].upper()
                
                if f"QI_{qi_level}" in response_text or f"THERMAL" in response_text:
                    print(f"✅ Injection OpenAI réussie: {response_text}")
                    self.save_successful_injection(service_info, test_message, response_text)
                    return True
                else:
                    print(f"❌ Injection OpenAI échouée: {response_text}")
                    return False
            else:
                print(f"❌ Erreur OpenAI: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Erreur test OpenAI: {e}")
            return False
    
    def test_custom_injection(self, service_info: Dict[str, Any], qi_level: int) -> bool:
        """Test injection agent personnalisé"""
        try:
            test_data = {
                "message": f"Test mémoire thermique QI {qi_level}",
                "thermal_memory": {
                    "qi_level": qi_level,
                    "active": True
                }
            }
            
            response = requests.post(
                f"{service_info['base_url']}/chat",
                json=test_data,
                timeout=15
            )
            
            if response.status_code == 200:
                response_text = response.text.upper()
                if str(qi_level) in response_text:
                    print(f"✅ Injection custom réussie")
                    self.save_successful_injection(service_info, str(test_data), response_text)
                    return True
            
            return False
            
        except Exception as e:
            print(f"❌ Erreur test custom: {e}")
            return False
    
    def test_generic_injection(self, service_info: Dict[str, Any], qi_level: int) -> bool:
        """Test injection générique"""
        try:
            # Test simple de connectivité avec paramètre
            response = requests.get(
                f"{service_info['base_url']}/status?qi={qi_level}",
                timeout=5
            )
            
            if response.status_code == 200:
                print(f"✅ Connexion générique établie")
                return True
            
            return False
            
        except Exception as e:
            return False
    
    def save_successful_injection(self, service_info: Dict[str, Any], prompt: str, response: str):
        """Sauve les injections réussies"""
        try:
            with open(self.memory_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            timestamp = int(time.time())
            injection_log = {
                'id': f'real_injection_{timestamp}',
                'content': f'INJECTION RÉELLE RÉUSSIE - Service: {service_info["type"]}:{service_info["port"]}',
                'service_info': service_info,
                'prompt': prompt,
                'response': response,
                'timestamp': timestamp,
                'importance': 1,
                'temperature': 100,
                'zone': 'zone_auto_connections',
                'source': 'thermal_memory_super_scanner',
                'type': 'real_successful_injection',
                'verified': True
            }
            
            # Ajouter à la zone auto-connexions
            if 'thermal_zones' in data and 'zone_auto_connections' in data['thermal_zones']:
                data['thermal_zones']['zone_auto_connections']['entries'].append(injection_log)
            
            with open(self.memory_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"💾 Injection réelle sauvée: {injection_log['id']}")
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde injection: {e}")
    
    def comprehensive_scan(self) -> Dict[str, Any]:
        """Scanner complet et honnête"""
        print("🚀 DÉMARRAGE SCANNER COMPLET")
        print("=" * 50)
        
        results = {
            'scan_timestamp': int(time.time()),
            'open_ports': [],
            'identified_services': [],
            'successful_injections': [],
            'failed_injections': [],
            'total_tests': 0,
            'success_rate': 0
        }
        
        # 1. Scanner les ports
        open_ports = self.deep_port_scan()
        results['open_ports'] = open_ports
        
        # 2. Identifier les services
        for port in open_ports:
            service_info = self.identify_service_type(port)
            if service_info:
                results['identified_services'].append(service_info)
        
        # 3. Tester les injections
        for service_info in results['identified_services']:
            results['total_tests'] += 1
            
            if self.test_real_memory_injection(service_info):
                results['successful_injections'].append(service_info)
                self.successful_connections[f"{service_info['type']}:{service_info['port']}"] = service_info
            else:
                results['failed_injections'].append(service_info)
                self.failed_connections[f"{service_info['type']}:{service_info['port']}"] = service_info
        
        # 4. Calculer le taux de succès
        if results['total_tests'] > 0:
            results['success_rate'] = len(results['successful_injections']) / results['total_tests'] * 100
        
        return results
    
    def generate_honest_report(self, results: Dict[str, Any]):
        """Génère un rapport honnête"""
        print("\n📊 RAPPORT COMPLET DU SCANNER")
        print("=" * 50)
        print(f"🕐 Timestamp: {results['scan_timestamp']}")
        print(f"🔍 Ports scannés: 3000-9000")
        print(f"📡 Ports ouverts: {len(results['open_ports'])}")
        print(f"🤖 Services identifiés: {len(results['identified_services'])}")
        print(f"✅ Injections réussies: {len(results['successful_injections'])}")
        print(f"❌ Injections échouées: {len(results['failed_injections'])}")
        print(f"📈 Taux de succès: {results['success_rate']:.1f}%")
        
        print("\n🎯 SERVICES AVEC INJECTION RÉUSSIE:")
        for service in results['successful_injections']:
            print(f"  ✅ {service['type']}:{service['port']} - {service['working_endpoint']}")
        
        print("\n❌ SERVICES AVEC INJECTION ÉCHOUÉE:")
        for service in results['failed_injections']:
            print(f"  ❌ {service['type']}:{service['port']} - {service['working_endpoint']}")
        
        if len(results['successful_injections']) > 0:
            print(f"\n🏆 SUCCÈS: {len(results['successful_injections'])} connexions réelles établies")
        else:
            print(f"\n💔 ÉCHEC: Aucune injection réelle réussie")

def main():
    """Test du super scanner"""
    print("🔥 SUPER SCANNER MÉMOIRE THERMIQUE")
    print("=" * 50)
    print("Scanner RÉEL avec tests d'injection authentiques")
    print()
    
    memory_file = "./thermal_memory_real_clones_1749979850296.json"
    scanner = ThermalMemorySuperScanner(memory_file)
    
    # Scanner complet
    results = scanner.comprehensive_scan()
    
    # Rapport honnête
    scanner.generate_honest_report(results)
    
    return len(results['successful_injections']) > 0

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ Scanner terminé avec succès")
    else:
        print("\n❌ Scanner terminé - aucune injection réussie")
